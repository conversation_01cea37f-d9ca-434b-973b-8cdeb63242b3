# .env.development# VITE_BASE_URL 要带着项目名称
VITE_BASE_URL = 'https://smp.dwq360.com/gbac-tip-frontend/api'
VITE_OSS_PATH = 'https://gbac-dev.oss-cn-shenzhen.aliyuncs.com'
# iframe 模式下，需要配置这个
VITE_IFRAME_HOST = 'https://operate-smp.dwq360.com'
# 这里是配置 二级 文件夹 生产就去掉
VITE_BASE_PATH = '/gbac-bidmgt-admfrontend/'
#会务域名地址
VITE_EVENT_URL = 'https://smp.dwq360.com/gbac-event-admfrontend'

# 配置企业微信登录数据
VITE_WX_APPID='wwc978f633fcc41586'
VITE_WX_AGENTID='1000043'
VITE_WX_REDIRECT_URL='/auth/user/oauth2/ww/login'
