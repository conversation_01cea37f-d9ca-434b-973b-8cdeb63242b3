{"printWidth": 150, "tabWidth": 4, "useTabs": true, "semi": true, "singleQuote": true, "quoteProps": "as-needed", "jsxSingleQuote": false, "trailingComma": "es5", "bracketSpacing": true, "jsxBracketSameLine": false, "bracketSameLine": false, "arrowParens": "always", "rangeStart": 0, "requirePragma": false, "insertPragma": false, "proseWrap": "preserve", "htmlWhitespaceSensitivity": "css", "vueIndentScriptAndStyle": false, "endOfLine": "lf"}