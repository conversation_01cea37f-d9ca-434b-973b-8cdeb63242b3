# React + Vite

Currently, two official plugins are available:

- [@vitejs/plugin-react](https://github.com/vitejs/vite-plugin-react/blob/main/packages/plugin-react/README.md) uses [Babel](https://babeljs.io/) for Fast Refresh
- [@vitejs/plugin-react-swc](https://github.com/vitejs/vite-plugin-react-swc) uses [SWC](https://swc.rs/) for Fast Refresh

# 文档地址
1. react 官网地址 https://react.docschina.org/  https://react.docschina.org/reference/react-dom/components/common#dangerously-setting-the-inner-html
2. antd 5.x.x 官网地址 https://ant.design/components/overview-cn/ 
3. vite 4.x.x https://vitejs.dev/
4. react-router6 https://baimingxuan.github.io/react-router6-doc/hooks/use-outlet-context.html
5. redux https://cn.redux.js.org/tutorials/
6. r-scale-screen https://github.com/Alfred-Skyblue/r-scale-screen
6. charts.ant.design 图形 https://charts.ant.design/manual/getting-started
6. charts.ant.design 地图绘制 https://l7draw.antv.vision/