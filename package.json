{"name": "pc-react-antd-redux", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "cross-env NODE_ENV=development vite", "build": "cross-env NODE_ENV=production vite build --mode production", "build-admin": "cross-env NODE_ENV=production VITE_BASE_PATH=/admin/ vite build --mode production", "build-erp": "cross-env NODE_ENV=production VITE_BASE_PATH=/erp/ vite build --mode production", "build:dev": "cross-env NODE_ENV=development vite build --mode development", "build:test": "cross-env NODE_ENV=test vite build --mode test", "lint": "eslint . --ext js,jsx --report-unused-disable-directives --max-warnings 0", "preview": "vite preview"}, "dependencies": {"@ant-design/charts": "^1.4.2", "@antv/g2": "^5.1.15", "@reduxjs/toolkit": "^1.9.5", "@wangeditor/editor": "^5.1.23", "@wangeditor/editor-for-react": "^1.0.6", "@wecom/jssdk": "^2.3.1", "ali-oss": "^6.20.0", "antd": "^5.19.3", "axios": "^1.4.0", "cross-env": "^7.0.3", "crypto-js": "^4.2.0", "dayjs": "^1.11.13", "echarts": "^5.5.1", "konva": "^9.3.0", "pinyin-pro": "^3.26.0", "r-scale-screen": "^0.0.2", "react": "^18.2.0", "react-beautiful-dnd": "^13.1.1", "react-cropper": "^2.3.3", "react-dom": "^18.2.0", "react-draggable": "^4.4.6", "react-konva": "^18.2.10", "react-konva-utils": "^1.0.5", "react-quill": "^2.0.0", "react-redux": "^8.1.2", "react-router-dom": "^6.14.2", "react-ueditor-wrap": "^1.0.8", "reset-css": "^5.0.2"}, "devDependencies": {"@types/react": "^18.2.15", "@types/react-dom": "^18.2.7", "@vitejs/plugin-react-swc": "^3.3.2", "eslint": "^8.45.0", "eslint-plugin-react": "^7.32.2", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.3", "sass": "^1.64.1", "vite": "^4.4.5"}, "resolutions": {"dayjs": "^1.11.13"}, "volta": {"node": "18.20.0"}}