/*! UEditorPlus v2.0.0*/
!function(){function a(a){"use strict";var b={omitExtraWLInCodeBlocks:{defaultValue:!1,describe:"Omit the default extra whiteline added to code blocks",type:"boolean"},noHeaderId:{defaultValue:!1,describe:"Turn on/off generated header id",type:"boolean"},prefixHeaderId:{defaultValue:!1,describe:"Add a prefix to the generated header ids. Passing a string will prefix that string to the header id. Setting to true will add a generic 'section-' prefix",type:"string"},rawPrefixHeaderId:{defaultValue:!1,describe:'Setting this option to true will prevent showdown from modifying the prefix. This might result in malformed IDs (if, for instance, the " char is used in the prefix)',type:"boolean"},ghCompatibleHeaderId:{defaultValue:!1,describe:"Generate header ids compatible with github style (spaces are replaced with dashes, a bunch of non alphanumeric chars are removed)",type:"boolean"},rawHeaderId:{defaultValue:!1,describe:"Remove only spaces, ' and \" from generated header ids (including prefixes), replacing them with dashes (-). WARNING: This might result in malformed ids",type:"boolean"},headerLevelStart:{defaultValue:!1,describe:"The header blocks level start",type:"integer"},parseImgDimensions:{defaultValue:!1,describe:"Turn on/off image dimension parsing",type:"boolean"},simplifiedAutoLink:{defaultValue:!1,describe:"Turn on/off GFM autolink style",type:"boolean"},excludeTrailingPunctuationFromURLs:{defaultValue:!1,describe:"Excludes trailing punctuation from links generated with autoLinking",type:"boolean"},literalMidWordUnderscores:{defaultValue:!1,describe:"Parse midword underscores as literal underscores",type:"boolean"},literalMidWordAsterisks:{defaultValue:!1,describe:"Parse midword asterisks as literal asterisks",type:"boolean"},strikethrough:{defaultValue:!1,describe:"Turn on/off strikethrough support",type:"boolean"},tables:{defaultValue:!1,describe:"Turn on/off tables support",type:"boolean"},tablesHeaderId:{defaultValue:!1,describe:"Add an id to table headers",type:"boolean"},ghCodeBlocks:{defaultValue:!0,describe:"Turn on/off GFM fenced code blocks support",type:"boolean"},tasklists:{defaultValue:!1,describe:"Turn on/off GFM tasklist support",type:"boolean"},smoothLivePreview:{defaultValue:!1,describe:"Prevents weird effects in live previews due to incomplete input",type:"boolean"},smartIndentationFix:{defaultValue:!1,describe:"Tries to smartly fix indentation in es6 strings",type:"boolean"},disableForced4SpacesIndentedSublists:{defaultValue:!1,describe:"Disables the requirement of indenting nested sublists by 4 spaces",type:"boolean"},simpleLineBreaks:{defaultValue:!1,describe:"Parses simple line breaks as <br> (GFM Style)",type:"boolean"},requireSpaceBeforeHeadingText:{defaultValue:!1,describe:"Makes adding a space between `#` and the header text mandatory (GFM Style)",type:"boolean"},ghMentions:{defaultValue:!1,describe:"Enables github @mentions",type:"boolean"},ghMentionsLink:{defaultValue:"https://github.com/{u}",describe:"Changes the link generated by @mentions. Only applies if ghMentions option is enabled.",type:"string"},encodeEmails:{defaultValue:!0,describe:"Encode e-mail addresses through the use of Character Entities, transforming ASCII e-mail addresses into its equivalent decimal entities",type:"boolean"},openLinksInNewWindow:{defaultValue:!1,describe:"Open all links in new windows",type:"boolean"},backslashEscapesHTMLTags:{defaultValue:!1,describe:"Support for HTML Tag escaping. ex: <div>foo</div>",type:"boolean"},emoji:{defaultValue:!1,describe:"Enable emoji support. Ex: `this is a :smile: emoji`",type:"boolean"},underline:{defaultValue:!1,describe:"Enable support for underline. Syntax is double or triple underscores: `__underline word__`. With this option enabled, underscores no longer parses into `<em>` and `<strong>`",type:"boolean"},ellipsis:{defaultValue:!0,describe:"Replaces three dots with the ellipsis unicode character",type:"boolean"},completeHTMLDocument:{defaultValue:!1,describe:"Outputs a complete html document, including `<html>`, `<head>` and `<body>` tags",type:"boolean"},metadata:{defaultValue:!1,describe:"Enable support for document metadata (defined at the top of the document between `«««` and `»»»` or between `---` and `---`).",type:"boolean"},splitAdjacentBlockquotes:{defaultValue:!1,describe:"Split adjacent blockquote blocks",type:"boolean"}};if(!1===a)return JSON.parse(JSON.stringify(b));var c,d={};for(c in b)b.hasOwnProperty(c)&&(d[c]=b[c].defaultValue);return d}function b(a,b){"use strict";var c=b?"Error in "+b+" extension->":"Error in unnamed extension",d={valid:!0,error:""};g.helper.isArray(a)||(a=[a]);for(var e=0;e<a.length;++e){var f=c+" sub-extension "+e+": ",h=a[e];if("object"!=typeof h)return d.valid=!1,d.error=f+"must be an object, but "+typeof h+" given",d;if(!g.helper.isString(h.type))return d.valid=!1,d.error=f+'property "type" must be a string, but '+typeof h.type+" given",d;var i=h.type=h.type.toLowerCase();if("lang"!==(i="html"===(i="language"===i?h.type="lang":i)?h.type="output":i)&&"output"!==i&&"listener"!==i)return d.valid=!1,d.error=f+"type "+i+' is not recognized. Valid values: "lang/language", "output/html" or "listener"',d;if("listener"===i){if(g.helper.isUndefined(h.listeners))return d.valid=!1,d.error=f+'. Extensions of type "listener" must have a property called "listeners"',d}else if(g.helper.isUndefined(h.filter)&&g.helper.isUndefined(h.regex))return d.valid=!1,d.error=f+i+' extensions must define either a "regex" property or a "filter" method',d;if(h.listeners){if("object"!=typeof h.listeners)return d.valid=!1,d.error=f+'"listeners" property must be an object but '+typeof h.listeners+" given",d;for(var j in h.listeners)if(h.listeners.hasOwnProperty(j)&&"function"!=typeof h.listeners[j])return d.valid=!1,d.error=f+'"listeners" property must be an hash of [event name]: [callback]. listeners.'+j+" must be a function but "+typeof h.listeners[j]+" given",d}if(h.filter){if("function"!=typeof h.filter)return d.valid=!1,d.error=f+'"filter" must be a function, but '+typeof h.filter+" given",d}else if(h.regex){if(g.helper.isString(h.regex)&&(h.regex=new RegExp(h.regex,"g")),!(h.regex instanceof RegExp))return d.valid=!1,d.error=f+'"regex" property must either be a string or a RegExp object, but '+typeof h.regex+" given",d;if(g.helper.isUndefined(h.replace))return d.valid=!1,d.error=f+'"regex" extensions must implement a replace string or function',d}}return d}function c(a,b){"use strict";return"¨E"+b.charCodeAt(0)+"E"}function d(a,b,c,d){"use strict";var e,f,g,h=-1<(d=d||"").indexOf("g"),i=new RegExp(b+"|"+c,"g"+d.replace(/g/g,"")),j=new RegExp(b,d.replace(/g/g,"")),k=[];do for(e=0;m=i.exec(a);)if(j.test(m[0]))e++||(g=(f=i.lastIndex)-m[0].length);else if(e&&!--e){var l=m.index+m[0].length,m={left:{start:g,end:f},match:{start:f,end:m.index},right:{start:m.index,end:l},wholeMatch:{start:g,end:l}};if(k.push(m),!h)return k}while(e&&(i.lastIndex=f));return k}function e(a){"use strict";return function(b,c,d,e,f,h,i){var j=d=d.replace(g.helper.regexes.asteriskDashAndColon,g.helper.escapeCharactersCallback),k="",l="",c=c||"",i=i||"";return/^www\./i.test(d)&&(d=d.replace(/^www\./i,"http://www.")),a.excludeTrailingPunctuationFromURLs&&h&&(k=h),c+'<a href="'+d+'"'+(l=a.openLinksInNewWindow?' rel="noopener noreferrer" target="¨E95Eblank"':l)+">"+j+"</a>"+k+i}}function f(a,b){"use strict";return function(c,d,e){var f="mailto:";return d=d||"",e=g.subParser("unescapeSpecialChars")(e,a,b),a.encodeEmails?(f=g.helper.encodeEmailAddress(f+e),e=g.helper.encodeEmailAddress(e)):f+=e,d+'<a href="'+f+'">'+e+"</a>"}}var g={},h={},i={},j=a(!0),k="vanilla",l={github:{omitExtraWLInCodeBlocks:!0,simplifiedAutoLink:!0,excludeTrailingPunctuationFromURLs:!0,literalMidWordUnderscores:!0,strikethrough:!0,tables:!0,tablesHeaderId:!0,ghCodeBlocks:!0,tasklists:!0,disableForced4SpacesIndentedSublists:!0,simpleLineBreaks:!0,requireSpaceBeforeHeadingText:!0,ghCompatibleHeaderId:!0,ghMentions:!0,backslashEscapesHTMLTags:!0,emoji:!0,splitAdjacentBlockquotes:!0},original:{noHeaderId:!0,ghCodeBlocks:!1},ghost:{omitExtraWLInCodeBlocks:!0,parseImgDimensions:!0,simplifiedAutoLink:!0,excludeTrailingPunctuationFromURLs:!0,literalMidWordUnderscores:!0,strikethrough:!0,tables:!0,tablesHeaderId:!0,ghCodeBlocks:!0,tasklists:!0,smoothLivePreview:!0,simpleLineBreaks:!0,requireSpaceBeforeHeadingText:!0,ghMentions:!1,encodeEmails:!0},vanilla:a(!0),allOn:function(){"use strict";var b,c=a(!0),d={};for(b in c)c.hasOwnProperty(b)&&(d[b]=!0);return d}()};g.helper={},g.extensions={},g.setOption=function(a,b){"use strict";return j[a]=b,this},g.getOption=function(a){"use strict";return j[a]},g.getOptions=function(){"use strict";return j},g.resetOptions=function(){"use strict";j=a(!0)},g.setFlavor=function(a){"use strict";if(!l.hasOwnProperty(a))throw Error(a+" flavor was not found");g.resetOptions();var b,c=l[a];for(b in k=a,c)c.hasOwnProperty(b)&&(j[b]=c[b])},g.getFlavor=function(){"use strict";return k},g.getFlavorOptions=function(a){"use strict";if(l.hasOwnProperty(a))return l[a]},g.getDefaultOptions=a,g.subParser=function(a,b){"use strict";if(g.helper.isString(a)){if(void 0===b){if(h.hasOwnProperty(a))return h[a];throw Error("SubParser named "+a+" not registered!")}h[a]=b}},g.extension=function(a,c){"use strict";if(!g.helper.isString(a))throw Error("Extension 'name' must be a string");if(a=g.helper.stdExtName(a),g.helper.isUndefined(c)){if(i.hasOwnProperty(a))return i[a];throw Error("Extension named "+a+" is not registered!")}"function"==typeof c&&(c=c());var d=b(c=g.helper.isArray(c)?c:[c],a);if(!d.valid)throw Error(d.error);i[a]=c},g.getAllExtensions=function(){"use strict";return i},g.removeExtension=function(a){"use strict";delete i[a]},g.resetExtensions=function(){"use strict";i={}},g.validateExtension=function(a){"use strict";return a=b(a,null),!!a.valid||(console.warn(a.error),!1)},g.hasOwnProperty("helper")||(g.helper={}),g.helper.isString=function(a){"use strict";return"string"==typeof a||a instanceof String},g.helper.isFunction=function(a){"use strict";return a&&"[object Function]"==={}.toString.call(a)},g.helper.isArray=function(a){"use strict";return Array.isArray(a)},g.helper.isUndefined=function(a){"use strict";return void 0===a},g.helper.forEach=function(a,b){"use strict";if(g.helper.isUndefined(a))throw new Error("obj param is required");if(g.helper.isUndefined(b))throw new Error("callback param is required");if(!g.helper.isFunction(b))throw new Error("callback param must be a function/closure");if("function"==typeof a.forEach)a.forEach(b);else if(g.helper.isArray(a))for(var c=0;c<a.length;c++)b(a[c],c,a);else{if("object"!=typeof a)throw new Error("obj does not seem to be an array or an iterable object");for(var d in a)a.hasOwnProperty(d)&&b(a[d],d,a)}},g.helper.stdExtName=function(a){"use strict";return a.replace(/[_?*+\/\\.^-]/g,"").replace(/\s/g,"").toLowerCase()},g.helper.escapeCharactersCallback=c,g.helper.escapeCharacters=function(a,b,d){"use strict";return b="(["+b.replace(/([\[\]\\])/g,"\\$1")+"])",d&&(b="\\\\"+b),d=new RegExp(b,"g"),a=a.replace(d,c)},g.helper.unescapeHTMLEntities=function(a){"use strict";return a.replace(/&quot;/g,'"').replace(/&lt;/g,"<").replace(/&gt;/g,">").replace(/&amp;/g,"&")},g.helper.matchRecursiveRegExp=function(a,b,c,e){"use strict";for(var f=d(a,b,c,e),g=[],h=0;h<f.length;++h)g.push([a.slice(f[h].wholeMatch.start,f[h].wholeMatch.end),a.slice(f[h].match.start,f[h].match.end),a.slice(f[h].left.start,f[h].left.end),a.slice(f[h].right.start,f[h].right.end)]);return g},g.helper.replaceRecursiveRegExp=function(a,b,c,e,f){"use strict";g.helper.isFunction(b)||(h=b,b=function(){return h});var h,i=d(a,c,e,f),c=a,j=i.length;if(0<j){var k=[];0!==i[0].wholeMatch.start&&k.push(a.slice(0,i[0].wholeMatch.start));for(var l=0;l<j;++l)k.push(b(a.slice(i[l].wholeMatch.start,i[l].wholeMatch.end),a.slice(i[l].match.start,i[l].match.end),a.slice(i[l].left.start,i[l].left.end),a.slice(i[l].right.start,i[l].right.end))),l<j-1&&k.push(a.slice(i[l].wholeMatch.end,i[l+1].wholeMatch.start));i[j-1].wholeMatch.end<a.length&&k.push(a.slice(i[j-1].wholeMatch.end)),c=k.join("")}return c},g.helper.regexIndexOf=function(a,b,c){"use strict";if(!g.helper.isString(a))throw"InvalidArgumentError: first parameter of showdown.helper.regexIndexOf function must be a string";if(b instanceof RegExp==0)throw"InvalidArgumentError: second parameter of showdown.helper.regexIndexOf function must be an instance of RegExp";return a=a.substring(c||0).search(b),0<=a?a+(c||0):a},g.helper.splitAtIndex=function(a,b){"use strict";if(g.helper.isString(a))return[a.substring(0,b),a.substring(b)];throw"InvalidArgumentError: first parameter of showdown.helper.regexIndexOf function must be a string"},g.helper.encodeEmailAddress=function(a){"use strict";var b=[function(a){return"&#"+a.charCodeAt(0)+";"},function(a){return"&#x"+a.charCodeAt(0).toString(16)+";"},function(a){return a}];return a=a.replace(/./g,function(a){var c;return a="@"===a?b[Math.floor(2*Math.random())](a):.9<(c=Math.random())?b[2](a):.45<c?b[1](a):b[0](a)})},g.helper.padEnd=function(a,b,c){"use strict";return b>>=0,c=String(c||" "),a.length>b?String(a):((b-=a.length)>c.length&&(c+=c.repeat(b/c.length)),String(a)+c.slice(0,b))},"undefined"==typeof console&&(console={warn:function(a){"use strict";alert(a)},log:function(a){"use strict";alert(a)},error:function(a){"use strict";throw a}}),g.helper.regexes={asteriskDashAndColon:/([*_:~])/g},g.helper.emojis={"+1":"👍","-1":"👎",100:"💯",1234:"🔢","1st_place_medal":"🥇","2nd_place_medal":"🥈","3rd_place_medal":"🥉","8ball":"🎱",a:"🅰️",ab:"🆎",abc:"🔤",abcd:"🔡",accept:"🉑",aerial_tramway:"🚡",airplane:"✈️",alarm_clock:"⏰",alembic:"⚗️",alien:"👽",ambulance:"🚑",amphora:"🏺",anchor:"⚓️",angel:"👼",anger:"💢",angry:"😠",anguished:"😧",ant:"🐜",apple:"🍎",aquarius:"♒️",aries:"♈️",arrow_backward:"◀️",arrow_double_down:"⏬",arrow_double_up:"⏫",arrow_down:"⬇️",arrow_down_small:"🔽",arrow_forward:"▶️",arrow_heading_down:"⤵️",arrow_heading_up:"⤴️",arrow_left:"⬅️",arrow_lower_left:"↙️",arrow_lower_right:"↘️",arrow_right:"➡️",arrow_right_hook:"↪️",arrow_up:"⬆️",arrow_up_down:"↕️",arrow_up_small:"🔼",arrow_upper_left:"↖️",arrow_upper_right:"↗️",arrows_clockwise:"🔃",arrows_counterclockwise:"🔄",art:"🎨",articulated_lorry:"🚛",artificial_satellite:"🛰",astonished:"😲",athletic_shoe:"👟",atm:"🏧",atom_symbol:"⚛️",avocado:"🥑",b:"🅱️",baby:"👶",baby_bottle:"🍼",baby_chick:"🐤",baby_symbol:"🚼",back:"🔙",bacon:"🥓",badminton:"🏸",baggage_claim:"🛄",baguette_bread:"🥖",balance_scale:"⚖️",balloon:"🎈",ballot_box:"🗳",ballot_box_with_check:"☑️",bamboo:"🎍",banana:"🍌",bangbang:"‼️",bank:"🏦",bar_chart:"📊",barber:"💈",baseball:"⚾️",basketball:"🏀",basketball_man:"⛹️",basketball_woman:"⛹️&zwj;♀️",bat:"🦇",bath:"🛀",bathtub:"🛁",battery:"🔋",beach_umbrella:"🏖",bear:"🐻",bed:"🛏",bee:"🐝",beer:"🍺",beers:"🍻",beetle:"🐞",beginner:"🔰",bell:"🔔",bellhop_bell:"🛎",bento:"🍱",biking_man:"🚴",bike:"🚲",biking_woman:"🚴&zwj;♀️",bikini:"👙",biohazard:"☣️",bird:"🐦",birthday:"🎂",black_circle:"⚫️",black_flag:"🏴",black_heart:"🖤",black_joker:"🃏",black_large_square:"⬛️",black_medium_small_square:"◾️",black_medium_square:"◼️",black_nib:"✒️",black_small_square:"▪️",black_square_button:"🔲",blonde_man:"👱",blonde_woman:"👱&zwj;♀️",blossom:"🌼",blowfish:"🐡",blue_book:"📘",blue_car:"🚙",blue_heart:"💙",blush:"😊",boar:"🐗",boat:"⛵️",bomb:"💣",book:"📖",bookmark:"🔖",bookmark_tabs:"📑",books:"📚",boom:"💥",boot:"👢",bouquet:"💐",bowing_man:"🙇",bow_and_arrow:"🏹",bowing_woman:"🙇&zwj;♀️",bowling:"🎳",boxing_glove:"🥊",boy:"👦",bread:"🍞",bride_with_veil:"👰",bridge_at_night:"🌉",briefcase:"💼",broken_heart:"💔",bug:"🐛",building_construction:"🏗",bulb:"💡",bullettrain_front:"🚅",bullettrain_side:"🚄",burrito:"🌯",bus:"🚌",business_suit_levitating:"🕴",busstop:"🚏",bust_in_silhouette:"👤",busts_in_silhouette:"👥",butterfly:"🦋",cactus:"🌵",cake:"🍰",calendar:"📆",call_me_hand:"🤙",calling:"📲",camel:"🐫",camera:"📷",camera_flash:"📸",camping:"🏕",cancer:"♋️",candle:"🕯",candy:"🍬",canoe:"🛶",capital_abcd:"🔠",capricorn:"♑️",car:"🚗",card_file_box:"🗃",card_index:"📇",card_index_dividers:"🗂",carousel_horse:"🎠",carrot:"🥕",cat:"🐱",cat2:"🐈",cd:"💿",chains:"⛓",champagne:"🍾",chart:"💹",chart_with_downwards_trend:"📉",chart_with_upwards_trend:"📈",checkered_flag:"🏁",cheese:"🧀",cherries:"🍒",cherry_blossom:"🌸",chestnut:"🌰",chicken:"🐔",children_crossing:"🚸",chipmunk:"🐿",chocolate_bar:"🍫",christmas_tree:"🎄",church:"⛪️",cinema:"🎦",circus_tent:"🎪",city_sunrise:"🌇",city_sunset:"🌆",cityscape:"🏙",cl:"🆑",clamp:"🗜",clap:"👏",clapper:"🎬",classical_building:"🏛",clinking_glasses:"🥂",clipboard:"📋",clock1:"🕐",clock10:"🕙",clock1030:"🕥",clock11:"🕚",clock1130:"🕦",clock12:"🕛",clock1230:"🕧",clock130:"🕜",clock2:"🕑",clock230:"🕝",clock3:"🕒",clock330:"🕞",clock4:"🕓",clock430:"🕟",clock5:"🕔",clock530:"🕠",clock6:"🕕",clock630:"🕡",clock7:"🕖",clock730:"🕢",clock8:"🕗",clock830:"🕣",clock9:"🕘",clock930:"🕤",closed_book:"📕",closed_lock_with_key:"🔐",closed_umbrella:"🌂",cloud:"☁️",cloud_with_lightning:"🌩",cloud_with_lightning_and_rain:"⛈",cloud_with_rain:"🌧",cloud_with_snow:"🌨",clown_face:"🤡",clubs:"♣️",cocktail:"🍸",coffee:"☕️",coffin:"⚰️",cold_sweat:"😰",comet:"☄️",computer:"💻",computer_mouse:"🖱",confetti_ball:"🎊",confounded:"😖",confused:"😕",congratulations:"㊗️",construction:"🚧",construction_worker_man:"👷",construction_worker_woman:"👷&zwj;♀️",control_knobs:"🎛",convenience_store:"🏪",cookie:"🍪",cool:"🆒",policeman:"👮",copyright:"©️",corn:"🌽",couch_and_lamp:"🛋",couple:"👫",couple_with_heart_woman_man:"💑",couple_with_heart_man_man:"👨&zwj;❤️&zwj;👨",couple_with_heart_woman_woman:"👩&zwj;❤️&zwj;👩",couplekiss_man_man:"👨&zwj;❤️&zwj;💋&zwj;👨",couplekiss_man_woman:"💏",couplekiss_woman_woman:"👩&zwj;❤️&zwj;💋&zwj;👩",cow:"🐮",cow2:"🐄",cowboy_hat_face:"🤠",crab:"🦀",crayon:"🖍",credit_card:"💳",crescent_moon:"🌙",cricket:"🏏",crocodile:"🐊",croissant:"🥐",crossed_fingers:"🤞",crossed_flags:"🎌",crossed_swords:"⚔️",crown:"👑",cry:"😢",crying_cat_face:"😿",crystal_ball:"🔮",cucumber:"🥒",cupid:"💘",curly_loop:"➰",currency_exchange:"💱",curry:"🍛",custard:"🍮",customs:"🛃",cyclone:"🌀",dagger:"🗡",dancer:"💃",dancing_women:"👯",dancing_men:"👯&zwj;♂️",dango:"🍡",dark_sunglasses:"🕶",dart:"🎯",dash:"💨",date:"📅",deciduous_tree:"🌳",deer:"🦌",department_store:"🏬",derelict_house:"🏚",desert:"🏜",desert_island:"🏝",desktop_computer:"🖥",male_detective:"🕵️",diamond_shape_with_a_dot_inside:"💠",diamonds:"♦️",disappointed:"😞",disappointed_relieved:"😥",dizzy:"💫",dizzy_face:"😵",do_not_litter:"🚯",dog:"🐶",dog2:"🐕",dollar:"💵",dolls:"🎎",dolphin:"🐬",door:"🚪",doughnut:"🍩",dove:"🕊",dragon:"🐉",dragon_face:"🐲",dress:"👗",dromedary_camel:"🐪",drooling_face:"🤤",droplet:"💧",drum:"🥁",duck:"🦆",dvd:"📀","e-mail":"📧",eagle:"🦅",ear:"👂",ear_of_rice:"🌾",earth_africa:"🌍",earth_americas:"🌎",earth_asia:"🌏",egg:"🥚",eggplant:"🍆",eight_pointed_black_star:"✴️",eight_spoked_asterisk:"✳️",electric_plug:"🔌",elephant:"🐘",email:"✉️",end:"🔚",envelope_with_arrow:"📩",euro:"💶",european_castle:"🏰",european_post_office:"🏤",evergreen_tree:"🌲",exclamation:"❗️",expressionless:"😑",eye:"👁",eye_speech_bubble:"👁&zwj;🗨",eyeglasses:"👓",eyes:"👀",face_with_head_bandage:"🤕",face_with_thermometer:"🤒",fist_oncoming:"👊",factory:"🏭",fallen_leaf:"🍂",family_man_woman_boy:"👪",family_man_boy:"👨&zwj;👦",family_man_boy_boy:"👨&zwj;👦&zwj;👦",family_man_girl:"👨&zwj;👧",family_man_girl_boy:"👨&zwj;👧&zwj;👦",family_man_girl_girl:"👨&zwj;👧&zwj;👧",family_man_man_boy:"👨&zwj;👨&zwj;👦",family_man_man_boy_boy:"👨&zwj;👨&zwj;👦&zwj;👦",family_man_man_girl:"👨&zwj;👨&zwj;👧",family_man_man_girl_boy:"👨&zwj;👨&zwj;👧&zwj;👦",family_man_man_girl_girl:"👨&zwj;👨&zwj;👧&zwj;👧",family_man_woman_boy_boy:"👨&zwj;👩&zwj;👦&zwj;👦",family_man_woman_girl:"👨&zwj;👩&zwj;👧",family_man_woman_girl_boy:"👨&zwj;👩&zwj;👧&zwj;👦",family_man_woman_girl_girl:"👨&zwj;👩&zwj;👧&zwj;👧",family_woman_boy:"👩&zwj;👦",family_woman_boy_boy:"👩&zwj;👦&zwj;👦",family_woman_girl:"👩&zwj;👧",family_woman_girl_boy:"👩&zwj;👧&zwj;👦",family_woman_girl_girl:"👩&zwj;👧&zwj;👧",family_woman_woman_boy:"👩&zwj;👩&zwj;👦",family_woman_woman_boy_boy:"👩&zwj;👩&zwj;👦&zwj;👦",family_woman_woman_girl:"👩&zwj;👩&zwj;👧",family_woman_woman_girl_boy:"👩&zwj;👩&zwj;👧&zwj;👦",family_woman_woman_girl_girl:"👩&zwj;👩&zwj;👧&zwj;👧",fast_forward:"⏩",fax:"📠",fearful:"😨",feet:"🐾",female_detective:"🕵️&zwj;♀️",ferris_wheel:"🎡",ferry:"⛴",field_hockey:"🏑",file_cabinet:"🗄",file_folder:"📁",film_projector:"📽",film_strip:"🎞",fire:"🔥",fire_engine:"🚒",fireworks:"🎆",first_quarter_moon:"🌓",first_quarter_moon_with_face:"🌛",fish:"🐟",fish_cake:"🍥",fishing_pole_and_fish:"🎣",fist_raised:"✊",fist_left:"🤛",fist_right:"🤜",flags:"🎏",flashlight:"🔦",fleur_de_lis:"⚜️",flight_arrival:"🛬",flight_departure:"🛫",floppy_disk:"💾",flower_playing_cards:"🎴",flushed:"😳",fog:"🌫",foggy:"🌁",football:"🏈",footprints:"👣",fork_and_knife:"🍴",fountain:"⛲️",fountain_pen:"🖋",four_leaf_clover:"🍀",fox_face:"🦊",framed_picture:"🖼",free:"🆓",fried_egg:"🍳",fried_shrimp:"🍤",fries:"🍟",frog:"🐸",frowning:"😦",frowning_face:"☹️",frowning_man:"🙍&zwj;♂️",frowning_woman:"🙍",middle_finger:"🖕",fuelpump:"⛽️",full_moon:"🌕",full_moon_with_face:"🌝",funeral_urn:"⚱️",game_die:"🎲",gear:"⚙️",gem:"💎",gemini:"♊️",ghost:"👻",gift:"🎁",gift_heart:"💝",girl:"👧",globe_with_meridians:"🌐",goal_net:"🥅",goat:"🐐",golf:"⛳️",golfing_man:"🏌️",golfing_woman:"🏌️&zwj;♀️",gorilla:"🦍",grapes:"🍇",green_apple:"🍏",green_book:"📗",green_heart:"💚",green_salad:"🥗",grey_exclamation:"❕",grey_question:"❔",grimacing:"😬",grin:"😁",grinning:"😀",guardsman:"💂",guardswoman:"💂&zwj;♀️",guitar:"🎸",gun:"🔫",haircut_woman:"💇",haircut_man:"💇&zwj;♂️",hamburger:"🍔",hammer:"🔨",hammer_and_pick:"⚒",hammer_and_wrench:"🛠",hamster:"🐹",hand:"✋",handbag:"👜",handshake:"🤝",hankey:"💩",hatched_chick:"🐥",hatching_chick:"🐣",headphones:"🎧",hear_no_evil:"🙉",heart:"❤️",heart_decoration:"💟",heart_eyes:"😍",heart_eyes_cat:"😻",heartbeat:"💓",heartpulse:"💗",hearts:"♥️",heavy_check_mark:"✔️",heavy_division_sign:"➗",heavy_dollar_sign:"💲",heavy_heart_exclamation:"❣️",heavy_minus_sign:"➖",heavy_multiplication_x:"✖️",heavy_plus_sign:"➕",helicopter:"🚁",herb:"🌿",hibiscus:"🌺",high_brightness:"🔆",high_heel:"👠",hocho:"🔪",hole:"🕳",honey_pot:"🍯",horse:"🐴",horse_racing:"🏇",hospital:"🏥",hot_pepper:"🌶",hotdog:"🌭",hotel:"🏨",hotsprings:"♨️",hourglass:"⌛️",hourglass_flowing_sand:"⏳",house:"🏠",house_with_garden:"🏡",houses:"🏘",hugs:"🤗",hushed:"😯",ice_cream:"🍨",ice_hockey:"🏒",ice_skate:"⛸",icecream:"🍦",id:"🆔",ideograph_advantage:"🉐",imp:"👿",inbox_tray:"📥",incoming_envelope:"📨",tipping_hand_woman:"💁",information_source:"ℹ️",innocent:"😇",interrobang:"⁉️",iphone:"📱",izakaya_lantern:"🏮",jack_o_lantern:"🎃",japan:"🗾",japanese_castle:"🏯",japanese_goblin:"👺",japanese_ogre:"👹",jeans:"👖",joy:"😂",joy_cat:"😹",joystick:"🕹",kaaba:"🕋",key:"🔑",keyboard:"⌨️",keycap_ten:"🔟",kick_scooter:"🛴",kimono:"👘",kiss:"💋",kissing:"😗",kissing_cat:"😽",kissing_closed_eyes:"😚",kissing_heart:"😘",kissing_smiling_eyes:"😙",kiwi_fruit:"🥝",koala:"🐨",koko:"🈁",label:"🏷",large_blue_circle:"🔵",large_blue_diamond:"🔷",large_orange_diamond:"🔶",last_quarter_moon:"🌗",last_quarter_moon_with_face:"🌜",latin_cross:"✝️",laughing:"😆",leaves:"🍃",ledger:"📒",left_luggage:"🛅",left_right_arrow:"↔️",leftwards_arrow_with_hook:"↩️",lemon:"🍋",leo:"♌️",leopard:"🐆",level_slider:"🎚",libra:"♎️",light_rail:"🚈",link:"🔗",lion:"🦁",lips:"👄",lipstick:"💄",lizard:"🦎",lock:"🔒",lock_with_ink_pen:"🔏",lollipop:"🍭",loop:"➿",loud_sound:"🔊",loudspeaker:"📢",love_hotel:"🏩",love_letter:"💌",low_brightness:"🔅",lying_face:"🤥",m:"Ⓜ️",mag:"🔍",mag_right:"🔎",mahjong:"🀄️",mailbox:"📫",mailbox_closed:"📪",mailbox_with_mail:"📬",mailbox_with_no_mail:"📭",man:"👨",man_artist:"👨&zwj;🎨",man_astronaut:"👨&zwj;🚀",man_cartwheeling:"🤸&zwj;♂️",man_cook:"👨&zwj;🍳",man_dancing:"🕺",man_facepalming:"🤦&zwj;♂️",man_factory_worker:"👨&zwj;🏭",man_farmer:"👨&zwj;🌾",man_firefighter:"👨&zwj;🚒",man_health_worker:"👨&zwj;⚕️",man_in_tuxedo:"🤵",man_judge:"👨&zwj;⚖️",man_juggling:"🤹&zwj;♂️",man_mechanic:"👨&zwj;🔧",man_office_worker:"👨&zwj;💼",man_pilot:"👨&zwj;✈️",man_playing_handball:"🤾&zwj;♂️",man_playing_water_polo:"🤽&zwj;♂️",man_scientist:"👨&zwj;🔬",man_shrugging:"🤷&zwj;♂️",man_singer:"👨&zwj;🎤",man_student:"👨&zwj;🎓",man_teacher:"👨&zwj;🏫",man_technologist:"👨&zwj;💻",man_with_gua_pi_mao:"👲",man_with_turban:"👳",tangerine:"🍊",mans_shoe:"👞",mantelpiece_clock:"🕰",maple_leaf:"🍁",martial_arts_uniform:"🥋",mask:"😷",massage_woman:"💆",massage_man:"💆&zwj;♂️",meat_on_bone:"🍖",medal_military:"🎖",medal_sports:"🏅",mega:"📣",melon:"🍈",memo:"📝",men_wrestling:"🤼&zwj;♂️",menorah:"🕎",mens:"🚹",metal:"🤘",metro:"🚇",microphone:"🎤",microscope:"🔬",milk_glass:"🥛",milky_way:"🌌",minibus:"🚐",minidisc:"💽",mobile_phone_off:"📴",money_mouth_face:"🤑",money_with_wings:"💸",moneybag:"💰",monkey:"🐒",monkey_face:"🐵",monorail:"🚝",moon:"🌔",mortar_board:"🎓",mosque:"🕌",motor_boat:"🛥",motor_scooter:"🛵",motorcycle:"🏍",motorway:"🛣",mount_fuji:"🗻",mountain:"⛰",mountain_biking_man:"🚵",mountain_biking_woman:"🚵&zwj;♀️",mountain_cableway:"🚠",mountain_railway:"🚞",mountain_snow:"🏔",mouse:"🐭",mouse2:"🐁",movie_camera:"🎥",moyai:"🗿",mrs_claus:"🤶",muscle:"💪",mushroom:"🍄",musical_keyboard:"🎹",musical_note:"🎵",musical_score:"🎼",mute:"🔇",nail_care:"💅",name_badge:"📛",national_park:"🏞",nauseated_face:"🤢",necktie:"👔",negative_squared_cross_mark:"❎",nerd_face:"🤓",neutral_face:"😐","new":"🆕",new_moon:"🌑",new_moon_with_face:"🌚",newspaper:"📰",newspaper_roll:"🗞",next_track_button:"⏭",ng:"🆖",no_good_man:"🙅&zwj;♂️",no_good_woman:"🙅",night_with_stars:"🌃",no_bell:"🔕",no_bicycles:"🚳",no_entry:"⛔️",no_entry_sign:"🚫",no_mobile_phones:"📵",no_mouth:"😶",no_pedestrians:"🚷",no_smoking:"🚭","non-potable_water":"🚱",nose:"👃",notebook:"📓",notebook_with_decorative_cover:"📔",notes:"🎶",nut_and_bolt:"🔩",o:"⭕️",o2:"🅾️",ocean:"🌊",octopus:"🐙",oden:"🍢",office:"🏢",oil_drum:"🛢",ok:"🆗",ok_hand:"👌",ok_man:"🙆&zwj;♂️",ok_woman:"🙆",old_key:"🗝",older_man:"👴",older_woman:"👵",om:"🕉",on:"🔛",oncoming_automobile:"🚘",oncoming_bus:"🚍",oncoming_police_car:"🚔",oncoming_taxi:"🚖",open_file_folder:"📂",open_hands:"👐",open_mouth:"😮",open_umbrella:"☂️",ophiuchus:"⛎",orange_book:"📙",orthodox_cross:"☦️",outbox_tray:"📤",owl:"🦉",ox:"🐂","package":"📦",page_facing_up:"📄",page_with_curl:"📃",pager:"📟",paintbrush:"🖌",palm_tree:"🌴",pancakes:"🥞",panda_face:"🐼",paperclip:"📎",paperclips:"🖇",parasol_on_ground:"⛱",parking:"🅿️",part_alternation_mark:"〽️",partly_sunny:"⛅️",passenger_ship:"🛳",passport_control:"🛂",pause_button:"⏸",peace_symbol:"☮️",peach:"🍑",peanuts:"🥜",pear:"🍐",pen:"🖊",pencil2:"✏️",penguin:"🐧",pensive:"😔",performing_arts:"🎭",persevere:"😣",person_fencing:"🤺",pouting_woman:"🙎",phone:"☎️",pick:"⛏",pig:"🐷",pig2:"🐖",pig_nose:"🐽",pill:"💊",pineapple:"🍍",ping_pong:"🏓",pisces:"♓️",pizza:"🍕",place_of_worship:"🛐",plate_with_cutlery:"🍽",play_or_pause_button:"⏯",point_down:"👇",point_left:"👈",point_right:"👉",point_up:"☝️",point_up_2:"👆",police_car:"🚓",policewoman:"👮&zwj;♀️",poodle:"🐩",popcorn:"🍿",post_office:"🏣",postal_horn:"📯",postbox:"📮",potable_water:"🚰",potato:"🥔",pouch:"👝",poultry_leg:"🍗",pound:"💷",rage:"😡",pouting_cat:"😾",pouting_man:"🙎&zwj;♂️",pray:"🙏",prayer_beads:"📿",pregnant_woman:"🤰",previous_track_button:"⏮",prince:"🤴",princess:"👸",printer:"🖨",purple_heart:"💜",purse:"👛",pushpin:"📌",put_litter_in_its_place:"🚮",question:"❓",rabbit:"🐰",rabbit2:"🐇",racehorse:"🐎",racing_car:"🏎",radio:"📻",radio_button:"🔘",radioactive:"☢️",railway_car:"🚃",railway_track:"🛤",rainbow:"🌈",rainbow_flag:"🏳️&zwj;🌈",raised_back_of_hand:"🤚",raised_hand_with_fingers_splayed:"🖐",raised_hands:"🙌",raising_hand_woman:"🙋",raising_hand_man:"🙋&zwj;♂️",ram:"🐏",ramen:"🍜",rat:"🐀",record_button:"⏺",recycle:"♻️",red_circle:"🔴",registered:"®️",relaxed:"☺️",relieved:"😌",reminder_ribbon:"🎗",repeat:"🔁",repeat_one:"🔂",rescue_worker_helmet:"⛑",restroom:"🚻",revolving_hearts:"💞",rewind:"⏪",rhinoceros:"🦏",ribbon:"🎀",rice:"🍚",rice_ball:"🍙",rice_cracker:"🍘",rice_scene:"🎑",right_anger_bubble:"🗯",ring:"💍",robot:"🤖",rocket:"🚀",rofl:"🤣",roll_eyes:"🙄",roller_coaster:"🎢",rooster:"🐓",rose:"🌹",rosette:"🏵",rotating_light:"🚨",round_pushpin:"📍",rowing_man:"🚣",rowing_woman:"🚣&zwj;♀️",rugby_football:"🏉",running_man:"🏃",running_shirt_with_sash:"🎽",running_woman:"🏃&zwj;♀️",sa:"🈂️",sagittarius:"♐️",sake:"🍶",sandal:"👡",santa:"🎅",satellite:"📡",saxophone:"🎷",school:"🏫",school_satchel:"🎒",scissors:"✂️",scorpion:"🦂",scorpius:"♏️",scream:"😱",scream_cat:"🙀",scroll:"📜",seat:"💺",secret:"㊙️",see_no_evil:"🙈",seedling:"🌱",selfie:"🤳",shallow_pan_of_food:"🥘",shamrock:"☘️",shark:"🦈",shaved_ice:"🍧",sheep:"🐑",shell:"🐚",shield:"🛡",shinto_shrine:"⛩",ship:"🚢",shirt:"👕",shopping:"🛍",shopping_cart:"🛒",shower:"🚿",shrimp:"🦐",signal_strength:"📶",six_pointed_star:"🔯",ski:"🎿",skier:"⛷",skull:"💀",skull_and_crossbones:"☠️",sleeping:"😴",sleeping_bed:"🛌",sleepy:"😪",slightly_frowning_face:"🙁",slightly_smiling_face:"🙂",slot_machine:"🎰",small_airplane:"🛩",small_blue_diamond:"🔹",small_orange_diamond:"🔸",small_red_triangle:"🔺",small_red_triangle_down:"🔻",smile:"😄",smile_cat:"😸",smiley:"😃",smiley_cat:"😺",smiling_imp:"😈",smirk:"😏",smirk_cat:"😼",smoking:"🚬",snail:"🐌",snake:"🐍",sneezing_face:"🤧",snowboarder:"🏂",snowflake:"❄️",snowman:"⛄️",snowman_with_snow:"☃️",sob:"😭",soccer:"⚽️",soon:"🔜",sos:"🆘",sound:"🔉",space_invader:"👾",spades:"♠️",spaghetti:"🍝",sparkle:"❇️",sparkler:"🎇",sparkles:"✨",sparkling_heart:"💖",speak_no_evil:"🙊",speaker:"🔈",speaking_head:"🗣",speech_balloon:"💬",speedboat:"🚤",spider:"🕷",spider_web:"🕸",spiral_calendar:"🗓",spiral_notepad:"🗒",spoon:"🥄",squid:"🦑",stadium:"🏟",star:"⭐️",star2:"🌟",star_and_crescent:"☪️",star_of_david:"✡️",stars:"🌠",station:"🚉",statue_of_liberty:"🗽",steam_locomotive:"🚂",stew:"🍲",stop_button:"⏹",stop_sign:"🛑",stopwatch:"⏱",straight_ruler:"📏",strawberry:"🍓",stuck_out_tongue:"😛",stuck_out_tongue_closed_eyes:"😝",stuck_out_tongue_winking_eye:"😜",studio_microphone:"🎙",stuffed_flatbread:"🥙",sun_behind_large_cloud:"🌥",sun_behind_rain_cloud:"🌦",sun_behind_small_cloud:"🌤",sun_with_face:"🌞",sunflower:"🌻",sunglasses:"😎",sunny:"☀️",sunrise:"🌅",sunrise_over_mountains:"🌄",surfing_man:"🏄",surfing_woman:"🏄&zwj;♀️",sushi:"🍣",suspension_railway:"🚟",sweat:"😓",sweat_drops:"💦",sweat_smile:"😅",sweet_potato:"🍠",swimming_man:"🏊",swimming_woman:"🏊&zwj;♀️",symbols:"🔣",synagogue:"🕍",syringe:"💉",taco:"🌮",tada:"🎉",tanabata_tree:"🎋",taurus:"♉️",taxi:"🚕",tea:"🍵",telephone_receiver:"📞",telescope:"🔭",tennis:"🎾",tent:"⛺️",thermometer:"🌡",thinking:"🤔",thought_balloon:"💭",ticket:"🎫",tickets:"🎟",tiger:"🐯",tiger2:"🐅",timer_clock:"⏲",tipping_hand_man:"💁&zwj;♂️",tired_face:"😫",tm:"™️",toilet:"🚽",tokyo_tower:"🗼",tomato:"🍅",tongue:"👅",top:"🔝",tophat:"🎩",tornado:"🌪",trackball:"🖲",tractor:"🚜",traffic_light:"🚥",train:"🚋",train2:"🚆",tram:"🚊",triangular_flag_on_post:"🚩",triangular_ruler:"📐",trident:"🔱",triumph:"😤",trolleybus:"🚎",trophy:"🏆",tropical_drink:"🍹",tropical_fish:"🐠",truck:"🚚",trumpet:"🎺",tulip:"🌷",tumbler_glass:"🥃",turkey:"🦃",turtle:"🐢",tv:"📺",twisted_rightwards_arrows:"🔀",two_hearts:"💕",two_men_holding_hands:"👬",two_women_holding_hands:"👭",u5272:"🈹",u5408:"🈴",u55b6:"🈺",u6307:"🈯️",u6708:"🈷️",u6709:"🈶",u6e80:"🈵",u7121:"🈚️",u7533:"🈸",u7981:"🈲",u7a7a:"🈳",umbrella:"☔️",unamused:"😒",underage:"🔞",unicorn:"🦄",unlock:"🔓",up:"🆙",upside_down_face:"🙃",v:"✌️",vertical_traffic_light:"🚦",vhs:"📼",vibration_mode:"📳",video_camera:"📹",video_game:"🎮",violin:"🎻",virgo:"♍️",volcano:"🌋",volleyball:"🏐",
vs:"🆚",vulcan_salute:"🖖",walking_man:"🚶",walking_woman:"🚶&zwj;♀️",waning_crescent_moon:"🌘",waning_gibbous_moon:"🌖",warning:"⚠️",wastebasket:"🗑",watch:"⌚️",water_buffalo:"🐃",watermelon:"🍉",wave:"👋",wavy_dash:"〰️",waxing_crescent_moon:"🌒",wc:"🚾",weary:"😩",wedding:"💒",weight_lifting_man:"🏋️",weight_lifting_woman:"🏋️&zwj;♀️",whale:"🐳",whale2:"🐋",wheel_of_dharma:"☸️",wheelchair:"♿️",white_check_mark:"✅",white_circle:"⚪️",white_flag:"🏳️",white_flower:"💮",white_large_square:"⬜️",white_medium_small_square:"◽️",white_medium_square:"◻️",white_small_square:"▫️",white_square_button:"🔳",wilted_flower:"🥀",wind_chime:"🎐",wind_face:"🌬",wine_glass:"🍷",wink:"😉",wolf:"🐺",woman:"👩",woman_artist:"👩&zwj;🎨",woman_astronaut:"👩&zwj;🚀",woman_cartwheeling:"🤸&zwj;♀️",woman_cook:"👩&zwj;🍳",woman_facepalming:"🤦&zwj;♀️",woman_factory_worker:"👩&zwj;🏭",woman_farmer:"👩&zwj;🌾",woman_firefighter:"👩&zwj;🚒",woman_health_worker:"👩&zwj;⚕️",woman_judge:"👩&zwj;⚖️",woman_juggling:"🤹&zwj;♀️",woman_mechanic:"👩&zwj;🔧",woman_office_worker:"👩&zwj;💼",woman_pilot:"👩&zwj;✈️",woman_playing_handball:"🤾&zwj;♀️",woman_playing_water_polo:"🤽&zwj;♀️",woman_scientist:"👩&zwj;🔬",woman_shrugging:"🤷&zwj;♀️",woman_singer:"👩&zwj;🎤",woman_student:"👩&zwj;🎓",woman_teacher:"👩&zwj;🏫",woman_technologist:"👩&zwj;💻",woman_with_turban:"👳&zwj;♀️",womans_clothes:"👚",womans_hat:"👒",women_wrestling:"🤼&zwj;♀️",womens:"🚺",world_map:"🗺",worried:"😟",wrench:"🔧",writing_hand:"✍️",x:"❌",yellow_heart:"💛",yen:"💴",yin_yang:"☯️",yum:"😋",zap:"⚡️",zipper_mouth_face:"🤐",zzz:"💤",octocat:'<img alt=":octocat:" height="20" width="20" align="absmiddle" src="https://assets-cdn.github.com/images/icons/emoji/octocat.png">',showdown:"<span style=\"font-family: 'Anonymous Pro', monospace; text-decoration: underline; text-decoration-style: dashed; text-decoration-color: #3e8b8a;text-underline-position: under;\">S</span>"},g.Converter=function(a){"use strict";function c(a,c){if(c=c||null,g.helper.isString(a)){if(c=a=g.helper.stdExtName(a),g.extensions[a]){console.warn("DEPRECATION WARNING: "+a+" is an old extension that uses a deprecated loading method.Please inform the developer that the extension should be updated!");var e=g.extensions[a],f=a;if("function"==typeof e&&(e=e(new g.Converter)),g.helper.isArray(e)||(e=[e]),!(f=b(e,f)).valid)throw Error(f.error);for(var h=0;h<e.length;++h)switch(e[h].type){case"lang":m.push(e[h]);break;case"output":n.push(e[h]);break;default:throw Error("Extension loader error: Type unrecognized!!!")}return}if(g.helper.isUndefined(i[a]))throw Error('Extension "'+a+'" could not be loaded. It was either not found or is not a valid extension.');a=i[a]}if("function"==typeof a&&(a=a()),f=b(a=g.helper.isArray(a)?a:[a],c),!f.valid)throw Error(f.error);for(var j=0;j<a.length;++j){switch(a[j].type){case"lang":m.push(a[j]);break;case"output":n.push(a[j])}if(a[j].hasOwnProperty("listeners"))for(var k in a[j].listeners)a[j].listeners.hasOwnProperty(k)&&d(k,a[j].listeners[k])}}function d(a,b){if(!g.helper.isString(a))throw Error("Invalid argument in converter.listen() method: name must be a string, but "+typeof a+" given");if("function"!=typeof b)throw Error("Invalid argument in converter.listen() method: callback must be a function, but "+typeof b+" given");o.hasOwnProperty(a)||(o[a]=[]),o[a].push(b)}var e,f,h={},m=[],n=[],o={},p=k,q={parsed:{},raw:"",format:""};for(e in a=a||{},j)j.hasOwnProperty(e)&&(h[e]=j[e]);if("object"!=typeof a)throw Error("Converter expects the passed parameter to be an object, but "+typeof a+" was passed instead.");for(f in a)a.hasOwnProperty(f)&&(h[f]=a[f]);h.extensions&&g.helper.forEach(h.extensions,c),this._dispatch=function(a,b,c,d){if(o.hasOwnProperty(a))for(var e=0;e<o[a].length;++e){var f=o[a][e](a,b,this,c,d);f&&void 0!==f&&(b=f)}return b},this.listen=function(a,b){return d(a,b),this},this.makeHtml=function(a){if(!a)return a;var b,c,d={gHtmlBlocks:[],gHtmlMdBlocks:[],gHtmlSpans:[],gUrls:{},gTitles:{},gDimensions:{},gListLevel:0,hashLinkCounts:{},langExtensions:m,outputModifiers:n,converter:this,ghCodeBlocks:[],metadata:{parsed:{},raw:"",format:""}};return a=(a=(a=(a=(a=a.replace(/¨/g,"¨T")).replace(/\$/g,"¨D")).replace(/\r\n/g,"\n")).replace(/\r/g,"\n")).replace(/\u00A0/g,"&nbsp;"),h.smartIndentationFix&&(c=(b=a).match(/^\s*/)[0].length,c=new RegExp("^\\s{0,"+c+"}","gm"),a=b.replace(c,"")),a="\n\n"+a+"\n\n",a=(a=g.subParser("detab")(a,h,d)).replace(/^[ \t]+$/gm,""),g.helper.forEach(m,function(b){a=g.subParser("runExtension")(b,a,h,d)}),a=g.subParser("metadata")(a,h,d),a=g.subParser("hashPreCodeTags")(a,h,d),a=g.subParser("githubCodeBlocks")(a,h,d),a=g.subParser("hashHTMLBlocks")(a,h,d),a=g.subParser("hashCodeTags")(a,h,d),a=g.subParser("stripLinkDefinitions")(a,h,d),a=g.subParser("blockGamut")(a,h,d),a=g.subParser("unhashHTMLSpans")(a,h,d),a=(a=(a=g.subParser("unescapeSpecialChars")(a,h,d)).replace(/¨D/g,"$$")).replace(/¨T/g,"¨"),a=g.subParser("completeHTMLDocument")(a,h,d),g.helper.forEach(n,function(b){a=g.subParser("runExtension")(b,a,h,d)}),q=d.metadata,a},this.makeMarkdown=this.makeMd=function(a,b){if(a=(a=(a=a.replace(/\r\n/g,"\n")).replace(/\r/g,"\n")).replace(/>[ \t]+</,">¨NBSP;<"),!b){if(!window||!window.document)throw new Error("HTMLParser is undefined. If in a webworker or nodejs environment, you need to provide a WHATWG DOM and HTML such as JSDOM");b=window.document}for(var b=b.createElement("div"),c=(b.innerHTML=a,{preList:function(a){for(var b=a.querySelectorAll("pre"),c=[],d=0;d<b.length;++d)if(1===b[d].childElementCount&&"code"===b[d].firstChild.tagName.toLowerCase()){var e=b[d].firstChild.innerHTML.trim(),f=b[d].firstChild.getAttribute("data-language")||"";if(""===f)for(var h=b[d].firstChild.className.split(" "),i=0;i<h.length;++i){var j=h[i].match(/^language-(.+)$/);if(null!==j){f=j[1];break}}e=g.helper.unescapeHTMLEntities(e),c.push(e),b[d].outerHTML='<precode language="'+f+'" precodenum="'+d.toString()+'"></precode>'}else c.push(b[d].innerHTML),b[d].innerHTML="",b[d].setAttribute("prenum",d.toString());return c}(b)}),d=(!function h(a){for(var b=0;b<a.childNodes.length;++b){var c=a.childNodes[b];3===c.nodeType?/\S/.test(c.nodeValue)||/^[ ]+$/.test(c.nodeValue)?(c.nodeValue=c.nodeValue.split("\n").join(" "),c.nodeValue=c.nodeValue.replace(/(\s)+/g,"$1")):(a.removeChild(c),--b):1===c.nodeType&&h(c)}}(b),b.childNodes),e="",f=0;f<d.length;f++)e+=g.subParser("makeMarkdown.node")(d[f],c);return e},this.setOption=function(a,b){h[a]=b},this.getOption=function(a){return h[a]},this.getOptions=function(){return h},this.addExtension=function(a,b){c(a,b=b||null)},this.useExtension=function(a){c(a)},this.setFlavor=function(a){if(!l.hasOwnProperty(a))throw Error(a+" flavor was not found");var b,c=l[a];for(b in p=a,c)c.hasOwnProperty(b)&&(h[b]=c[b])},this.getFlavor=function(){return p},this.removeExtension=function(a){g.helper.isArray(a)||(a=[a]);for(var b=0;b<a.length;++b){for(var c=a[b],d=0;d<m.length;++d)m[d]===c&&m.splice(d,1);for(var e=0;e<n.length;++e)n[e]===c&&n.splice(e,1)}},this.getAllExtensions=function(){return{language:m,output:n}},this.getMetadata=function(a){return a?q.raw:q.parsed},this.getMetadataFormat=function(){return q.format},this._setMetadataPair=function(a,b){q.parsed[a]=b},this._setMetadataFormat=function(a){q.format=a},this._setMetadataRaw=function(a){q.raw=a}},g.subParser("anchors",function(a,b,c){"use strict";function d(a,d,e,f,h,i,j){if(g.helper.isUndefined(j)&&(j=""),e=e.toLowerCase(),-1<a.search(/\(<?\s*>? ?(['"].*['"])?\)$/m))f="";else if(!f){if(f="#"+(e=e||d.toLowerCase().replace(/ ?\n/g," ")),g.helper.isUndefined(c.gUrls[e]))return a;f=c.gUrls[e],g.helper.isUndefined(c.gTitles[e])||(j=c.gTitles[e])}return a='<a href="'+(f=f.replace(g.helper.regexes.asteriskDashAndColon,g.helper.escapeCharactersCallback))+'"',""!==j&&null!==j&&(a+=' title="'+(j=(j=j.replace(/"/g,"&quot;")).replace(g.helper.regexes.asteriskDashAndColon,g.helper.escapeCharactersCallback))+'"'),b.openLinksInNewWindow&&!/^#/.test(f)&&(a+=' rel="noopener noreferrer" target="¨E95Eblank"'),a+=">"+d+"</a>"}return a=(a=(a=(a=(a=c.converter._dispatch("anchors.before",a,b,c)).replace(/\[((?:\[[^\]]*]|[^\[\]])*)] ?(?:\n *)?\[(.*?)]()()()()/g,d)).replace(/\[((?:\[[^\]]*]|[^\[\]])*)]()[ \t]*\([ \t]?<([^>]*)>(?:[ \t]*((["'])([^"]*?)\5))?[ \t]?\)/g,d)).replace(/\[((?:\[[^\]]*]|[^\[\]])*)]()[ \t]*\([ \t]?<?([\S]+?(?:\([\S]*?\)[\S]*?)?)>?(?:[ \t]*((["'])([^"]*?)\5))?[ \t]?\)/g,d)).replace(/\[([^\[\]]+)]()()()()()/g,d),b.ghMentions&&(a=a.replace(/(^|\s)(\\)?(@([a-z\d]+(?:[a-z\d.-]+?[a-z\d]+)*))/gim,function(a,c,d,e,f){if("\\"===d)return c+e;if(!g.helper.isString(b.ghMentionsLink))throw new Error("ghMentionsLink option must be a string");return d="",c+'<a href="'+b.ghMentionsLink.replace(/\{u}/g,f)+'"'+(d=b.openLinksInNewWindow?' rel="noopener noreferrer" target="¨E95Eblank"':d)+">"+e+"</a>"})),a=c.converter._dispatch("anchors.after",a,b,c)});var m=/([*~_]+|\b)(((https?|ftp|dict):\/\/|www\.)[^'">\s]+?\.[^'">\s]+?)()(\1)?(?=\s|$)(?!["<>])/gi,n=/([*~_]+|\b)(((https?|ftp|dict):\/\/|www\.)[^'">\s]+\.[^'">\s]+?)([.!?,()\[\]])?(\1)?(?=\s|$)(?!["<>])/gi,o=/()<(((https?|ftp|dict):\/\/|www\.)[^'">\s]+)()>()/gi,p=/(^|\s)(?:mailto:)?([A-Za-z0-9!#$%&'*+-/=?^_`{|}~.]+@[-a-z0-9]+(\.[-a-z0-9]+)*\.[a-z]+)(?=$|\s)/gim,q=/<()(?:mailto:)?([-.\w]+@[-a-z0-9]+(\.[-a-z0-9]+)*\.[a-z]+)>/gi;g.subParser("autoLinks",function(a,b,c){"use strict";return a=(a=(a=c.converter._dispatch("autoLinks.before",a,b,c)).replace(o,e(b))).replace(q,f(b,c)),a=c.converter._dispatch("autoLinks.after",a,b,c)}),g.subParser("simplifiedAutoLinks",function(a,b,c){"use strict";return b.simplifiedAutoLink?(a=c.converter._dispatch("simplifiedAutoLinks.before",a,b,c),a=(a=b.excludeTrailingPunctuationFromURLs?a.replace(n,e(b)):a.replace(m,e(b))).replace(p,f(b,c)),c.converter._dispatch("simplifiedAutoLinks.after",a,b,c)):a}),g.subParser("blockGamut",function(a,b,c){"use strict";return a=c.converter._dispatch("blockGamut.before",a,b,c),a=g.subParser("blockQuotes")(a,b,c),a=g.subParser("headers")(a,b,c),a=g.subParser("horizontalRule")(a,b,c),a=g.subParser("lists")(a,b,c),a=g.subParser("codeBlocks")(a,b,c),a=g.subParser("tables")(a,b,c),a=g.subParser("hashHTMLBlocks")(a,b,c),a=g.subParser("paragraphs")(a,b,c),a=c.converter._dispatch("blockGamut.after",a,b,c)}),g.subParser("blockQuotes",function(a,b,c){"use strict";a=c.converter._dispatch("blockQuotes.before",a,b,c);var d=/(^ {0,3}>[ \t]?.+\n(.+\n)*\n*)+/gm;return b.splitAdjacentBlockquotes&&(d=/^ {0,3}>[\s\S]*?(?:\n\n)/gm),a=(a+="\n\n").replace(d,function(a){return a=(a=(a=a.replace(/^[ \t]*>[ \t]?/gm,"")).replace(/¨0/g,"")).replace(/^[ \t]+$/gm,""),a=g.subParser("githubCodeBlocks")(a,b,c),a=(a=(a=g.subParser("blockGamut")(a,b,c)).replace(/(^|\n)/g,"$1  ")).replace(/(\s*<pre>[^\r]+?<\/pre>)/gm,function(a,b){return b.replace(/^  /gm,"¨0").replace(/¨0/g,"")}),g.subParser("hashBlock")("<blockquote>\n"+a+"\n</blockquote>",b,c)}),a=c.converter._dispatch("blockQuotes.after",a,b,c)}),g.subParser("codeBlocks",function(a,b,c){"use strict";return a=c.converter._dispatch("codeBlocks.before",a,b,c),a=(a=(a+="¨0").replace(/(?:\n\n|^)((?:(?:[ ]{4}|\t).*\n+)+)(\n*[ ]{0,3}[^ \t\n]|(?=¨0))/g,function(a,d,e){var f="\n",d=g.subParser("outdent")(d,b,c);return d=g.subParser("encodeCode")(d,b,c),d="<pre><code>"+(d=(d=(d=g.subParser("detab")(d,b,c)).replace(/^\n+/g,"")).replace(/\n+$/g,""))+(f=b.omitExtraWLInCodeBlocks?"":f)+"</code></pre>",g.subParser("hashBlock")(d,b,c)+e})).replace(/¨0/,""),a=c.converter._dispatch("codeBlocks.after",a,b,c)}),g.subParser("codeSpans",function(a,b,c){"use strict";return a=(a=void 0===(a=c.converter._dispatch("codeSpans.before",a,b,c))?"":a).replace(/(^|[^\\])(`+)([^\r]*?[^`])\2(?!`)/gm,function(a,d,e,f){return f=(f=f.replace(/^([ \t]*)/g,"")).replace(/[ \t]*$/g,""),f=d+"<code>"+(f=g.subParser("encodeCode")(f,b,c))+"</code>",f=g.subParser("hashHTMLSpans")(f,b,c)}),a=c.converter._dispatch("codeSpans.after",a,b,c)}),g.subParser("completeHTMLDocument",function(a,b,c){"use strict";if(!b.completeHTMLDocument)return a;a=c.converter._dispatch("completeHTMLDocument.before",a,b,c);var d,e="html",f="<!DOCTYPE HTML>\n",g="",h='<meta charset="utf-8">\n',i="",j="";for(d in void 0!==c.metadata.parsed.doctype&&(f="<!DOCTYPE "+c.metadata.parsed.doctype+">\n","html"!==(e=c.metadata.parsed.doctype.toString().toLowerCase())&&"html5"!==e||(h='<meta charset="utf-8">')),c.metadata.parsed)if(c.metadata.parsed.hasOwnProperty(d))switch(d.toLowerCase()){case"doctype":break;case"title":g="<title>"+c.metadata.parsed.title+"</title>\n";break;case"charset":h="html"===e||"html5"===e?'<meta charset="'+c.metadata.parsed.charset+'">\n':'<meta name="charset" content="'+c.metadata.parsed.charset+'">\n';break;case"language":case"lang":i=' lang="'+c.metadata.parsed[d]+'"',j+='<meta name="'+d+'" content="'+c.metadata.parsed[d]+'">\n';break;default:j+='<meta name="'+d+'" content="'+c.metadata.parsed[d]+'">\n'}return a=f+"<html"+i+">\n<head>\n"+g+h+j+"</head>\n<body>\n"+a.trim()+"\n</body>\n</html>",a=c.converter._dispatch("completeHTMLDocument.after",a,b,c)}),g.subParser("detab",function(a,b,c){"use strict";return a=(a=(a=(a=(a=(a=c.converter._dispatch("detab.before",a,b,c)).replace(/\t(?=\t)/g,"    ")).replace(/\t/g,"¨A¨B")).replace(/¨B(.+?)¨A/g,function(a,b){for(var c=b,d=4-c.length%4,e=0;e<d;e++)c+=" ";return c})).replace(/¨A/g,"    ")).replace(/¨B/g,""),a=c.converter._dispatch("detab.after",a,b,c)}),g.subParser("ellipsis",function(a,b,c){"use strict";return b.ellipsis?(a=(a=c.converter._dispatch("ellipsis.before",a,b,c)).replace(/\.\.\./g,"…"),c.converter._dispatch("ellipsis.after",a,b,c)):a}),g.subParser("emoji",function(a,b,c){"use strict";return b.emoji?(a=(a=c.converter._dispatch("emoji.before",a,b,c)).replace(/:([\S]+?):/g,function(a,b){return g.helper.emojis.hasOwnProperty(b)?g.helper.emojis[b]:a}),a=c.converter._dispatch("emoji.after",a,b,c)):a}),g.subParser("encodeAmpsAndAngles",function(a,b,c){"use strict";return a=(a=(a=(a=(a=c.converter._dispatch("encodeAmpsAndAngles.before",a,b,c)).replace(/&(?!#?[xX]?(?:[0-9a-fA-F]+|\w+);)/g,"&amp;")).replace(/<(?![a-z\/?$!])/gi,"&lt;")).replace(/</g,"&lt;")).replace(/>/g,"&gt;"),a=c.converter._dispatch("encodeAmpsAndAngles.after",a,b,c)}),g.subParser("encodeBackslashEscapes",function(a,b,c){"use strict";return a=(a=(a=c.converter._dispatch("encodeBackslashEscapes.before",a,b,c)).replace(/\\(\\)/g,g.helper.escapeCharactersCallback)).replace(/\\([`*_{}\[\]()>#+.!~=|:-])/g,g.helper.escapeCharactersCallback),a=c.converter._dispatch("encodeBackslashEscapes.after",a,b,c)}),g.subParser("encodeCode",function(a,b,c){"use strict";return a=(a=c.converter._dispatch("encodeCode.before",a,b,c)).replace(/&/g,"&amp;").replace(/</g,"&lt;").replace(/>/g,"&gt;").replace(/([*_{}\[\]\\=~-])/g,g.helper.escapeCharactersCallback),a=c.converter._dispatch("encodeCode.after",a,b,c)}),g.subParser("escapeSpecialCharsWithinTagAttributes",function(a,b,c){"use strict";return a=(a=(a=c.converter._dispatch("escapeSpecialCharsWithinTagAttributes.before",a,b,c)).replace(/<\/?[a-z\d_:-]+(?:[\s]+[\s\S]+?)?>/gi,function(a){return a.replace(/(.)<\/?code>(?=.)/g,"$1`").replace(/([\\`*_~=|])/g,g.helper.escapeCharactersCallback)})).replace(/<!(--(?:(?:[^>-]|-[^>])(?:[^-]|-[^-])*)--)>/gi,function(a){return a.replace(/([\\`*_~=|])/g,g.helper.escapeCharactersCallback)}),a=c.converter._dispatch("escapeSpecialCharsWithinTagAttributes.after",a,b,c)}),g.subParser("githubCodeBlocks",function(a,b,c){"use strict";return b.ghCodeBlocks?(a=c.converter._dispatch("githubCodeBlocks.before",a,b,c),a=(a=(a+="¨0").replace(/(?:^|\n)(?: {0,3})(```+|~~~+)(?: *)([^\s`~]*)\n([\s\S]*?)\n(?: {0,3})\1/g,function(a,d,e,f){var h=b.omitExtraWLInCodeBlocks?"":"\n";return f=g.subParser("encodeCode")(f,b,c),f="<pre><code"+(e?' class="'+e+" language-"+e+'"':"")+">"+(f=(f=(f=g.subParser("detab")(f,b,c)).replace(/^\n+/g,"")).replace(/\n+$/g,""))+h+"</code></pre>",f=g.subParser("hashBlock")(f,b,c),"\n\n¨G"+(c.ghCodeBlocks.push({text:a,codeblock:f})-1)+"G\n\n"})).replace(/¨0/,""),c.converter._dispatch("githubCodeBlocks.after",a,b,c)):a}),g.subParser("hashBlock",function(a,b,c){"use strict";return a=(a=c.converter._dispatch("hashBlock.before",a,b,c)).replace(/(^\n+|\n+$)/g,""),a="\n\n¨K"+(c.gHtmlBlocks.push(a)-1)+"K\n\n",a=c.converter._dispatch("hashBlock.after",a,b,c)}),g.subParser("hashCodeTags",function(a,b,c){"use strict";return a=c.converter._dispatch("hashCodeTags.before",a,b,c),a=g.helper.replaceRecursiveRegExp(a,function(a,d,e,f){return e=e+g.subParser("encodeCode")(d,b,c)+f,"¨C"+(c.gHtmlSpans.push(e)-1)+"C"},"<code\\b[^>]*>","</code>","gim"),a=c.converter._dispatch("hashCodeTags.after",a,b,c)}),g.subParser("hashElement",function(a,b,c){"use strict";return function(a,b){return b=(b=(b=b.replace(/\n\n/g,"\n")).replace(/^\n/,"")).replace(/\n+$/g,""),b="\n\n¨K"+(c.gHtmlBlocks.push(b)-1)+"K\n\n"}}),g.subParser("hashHTMLBlocks",function(a,b,c){"use strict";function d(a,b,d,e){return-1!==d.search(/\bmarkdown\b/)&&(a=d+c.converter.makeHtml(b)+e),"\n\n¨K"+(c.gHtmlBlocks.push(a)-1)+"K\n\n"}a=c.converter._dispatch("hashHTMLBlocks.before",a,b,c);var e=["pre","div","h1","h2","h3","h4","h5","h6","blockquote","table","dl","ol","ul","script","noscript","form","fieldset","iframe","math","style","section","header","footer","nav","article","aside","address","audio","canvas","figure","hgroup","output","video","p"];b.backslashEscapesHTMLTags&&(a=a.replace(/\\<(\/?[^>]+?)>/g,function(a,b){return"&lt;"+b+"&gt;"}));for(var f=0;f<e.length;++f)for(var h=new RegExp("^ {0,3}(<"+e[f]+"\\b[^>]*>)","im"),i="<"+e[f]+"\\b[^>]*>",j="</"+e[f]+">";-1!==(k=g.helper.regexIndexOf(a,h));){var k=g.helper.splitAtIndex(a,k),l=g.helper.replaceRecursiveRegExp(k[1],d,i,j,"im");if(l===k[1])break;a=k[0].concat(l)}return a=a.replace(/(\n {0,3}(<(hr)\b([^<>])*?\/?>)[ \t]*(?=\n{2,}))/g,g.subParser("hashElement")(a,b,c)),a=(a=g.helper.replaceRecursiveRegExp(a,function(a){return"\n\n¨K"+(c.gHtmlBlocks.push(a)-1)+"K\n\n"},"^ {0,3}<!--","-->","gm")).replace(/(?:\n\n)( {0,3}(?:<([?%])[^\r]*?\2>)[ \t]*(?=\n{2,}))/g,g.subParser("hashElement")(a,b,c)),a=c.converter._dispatch("hashHTMLBlocks.after",a,b,c)}),g.subParser("hashHTMLSpans",function(a,b,c){"use strict";function d(a){return"¨C"+(c.gHtmlSpans.push(a)-1)+"C"}return a=(a=(a=(a=(a=c.converter._dispatch("hashHTMLSpans.before",a,b,c)).replace(/<[^>]+?\/>/gi,d)).replace(/<([^>]+?)>[\s\S]*?<\/\1>/g,d)).replace(/<([^>]+?)\s[^>]+?>[\s\S]*?<\/\1>/g,d)).replace(/<[^>]+?>/gi,d),a=c.converter._dispatch("hashHTMLSpans.after",a,b,c)}),g.subParser("unhashHTMLSpans",function(a,b,c){"use strict";a=c.converter._dispatch("unhashHTMLSpans.before",a,b,c);for(var d=0;d<c.gHtmlSpans.length;++d){for(var e=c.gHtmlSpans[d],f=0;/¨C(\d+)C/.test(e);){var g=RegExp.$1,e=e.replace("¨C"+g+"C",c.gHtmlSpans[g]);if(10===f){console.error("maximum nesting of 10 spans reached!!!");break}++f}a=a.replace("¨C"+d+"C",e)}return a=c.converter._dispatch("unhashHTMLSpans.after",a,b,c)}),g.subParser("hashPreCodeTags",function(a,b,c){"use strict";return a=c.converter._dispatch("hashPreCodeTags.before",a,b,c),a=g.helper.replaceRecursiveRegExp(a,function(a,d,e,f){return e=e+g.subParser("encodeCode")(d,b,c)+f,"\n\n¨G"+(c.ghCodeBlocks.push({text:a,codeblock:e})-1)+"G\n\n"},"^ {0,3}<pre\\b[^>]*>\\s*<code\\b[^>]*>","^ {0,3}</code>\\s*</pre>","gim"),a=c.converter._dispatch("hashPreCodeTags.after",a,b,c)}),g.subParser("headers",function(a,b,c){"use strict";function d(a){var d=a=b.customizedHeaderId&&(d=a.match(/\{([^{]+?)}\s*$/))&&d[1]?d[1]:a,a=g.helper.isString(b.prefixHeaderId)?b.prefixHeaderId:!0===b.prefixHeaderId?"section-":"";return b.rawPrefixHeaderId||(d=a+d),d=(b.ghCompatibleHeaderId?d.replace(/ /g,"-").replace(/&amp;/g,"").replace(/¨T/g,"").replace(/¨D/g,"").replace(/[&+$,\/:;=?@"#{}|^¨~\[\]`\\*)(%.!'<>]/g,""):b.rawHeaderId?d.replace(/ /g,"-").replace(/&amp;/g,"&").replace(/¨T/g,"¨").replace(/¨D/g,"$").replace(/["']/g,"-"):d.replace(/[^\w]/g,"")).toLowerCase(),b.rawPrefixHeaderId&&(d=a+d),c.hashLinkCounts[d]?d=d+"-"+c.hashLinkCounts[d]++:c.hashLinkCounts[d]=1,d}a=c.converter._dispatch("headers.before",a,b,c);var e=isNaN(parseInt(b.headerLevelStart))?1:parseInt(b.headerLevelStart),f=b.smoothLivePreview?/^(.+)[ \t]*\n={2,}[ \t]*\n+/gm:/^(.+)[ \t]*\n=+[ \t]*\n+/gm,h=b.smoothLivePreview?/^(.+)[ \t]*\n-{2,}[ \t]*\n+/gm:/^(.+)[ \t]*\n-+[ \t]*\n+/gm,f=(a=(a=a.replace(f,function(a,f){var h=g.subParser("spanGamut")(f,b,c),f=b.noHeaderId?"":' id="'+d(f)+'"',f="<h"+e+f+">"+h+"</h"+e+">";return g.subParser("hashBlock")(f,b,c)})).replace(h,function(a,f){var h=g.subParser("spanGamut")(f,b,c),f=b.noHeaderId?"":' id="'+d(f)+'"',i=e+1,f="<h"+i+f+">"+h+"</h"+i+">";return g.subParser("hashBlock")(f,b,c)}),b.requireSpaceBeforeHeadingText?/^(#{1,6})[ \t]+(.+?)[ \t]*#*\n+/gm:/^(#{1,6})[ \t]*(.+?)[ \t]*#*\n+/gm);return a=a.replace(f,function(a,f,h){var i=h,i=(b.customizedHeaderId&&(i=h.replace(/\s?\{([^{]+?)}\s*$/,"")),g.subParser("spanGamut")(i,b,c)),h=b.noHeaderId?"":' id="'+d(h)+'"',f=e-1+f.length,h="<h"+f+h+">"+i+"</h"+f+">";return g.subParser("hashBlock")(h,b,c)}),a=c.converter._dispatch("headers.after",a,b,c)}),g.subParser("horizontalRule",function(a,b,c){"use strict";a=c.converter._dispatch("horizontalRule.before",a,b,c);var d=g.subParser("hashBlock")("<hr />",b,c);return a=(a=(a=a.replace(/^ {0,2}( ?-){3,}[ \t]*$/gm,d)).replace(/^ {0,2}( ?\*){3,}[ \t]*$/gm,d)).replace(/^ {0,2}( ?_){3,}[ \t]*$/gm,d),a=c.converter._dispatch("horizontalRule.after",a,b,c)}),g.subParser("images",function(a,b,c){"use strict";function d(a,b,d,e,f,h,i,j){var k=c.gUrls,l=c.gTitles,m=c.gDimensions;if(d=d.toLowerCase(),j=j||"",-1<a.search(/\(<?\s*>? ?(['"].*['"])?\)$/m))e="";else if(""===e||null===e){if(e="#"+(d=""!==d&&null!==d?d:b.toLowerCase().replace(/ ?\n/g," ")),g.helper.isUndefined(k[d]))return a;e=k[d],g.helper.isUndefined(l[d])||(j=l[d]),g.helper.isUndefined(m[d])||(f=m[d].width,h=m[d].height)}return b=b.replace(/"/g,"&quot;").replace(g.helper.regexes.asteriskDashAndColon,g.helper.escapeCharactersCallback),a='<img src="'+(e=e.replace(g.helper.regexes.asteriskDashAndColon,g.helper.escapeCharactersCallback))+'" alt="'+b+'"',j&&g.helper.isString(j)&&(a+=' title="'+(j=j.replace(/"/g,"&quot;").replace(g.helper.regexes.asteriskDashAndColon,g.helper.escapeCharactersCallback))+'"'),f&&h&&(a=a+(' width="'+(f="*"===f?"auto":f))+'" height="'+(h="*"===h?"auto":h)+'"'),a+=" />"}return a=(a=(a=(a=(a=(a=c.converter._dispatch("images.before",a,b,c)).replace(/!\[([^\]]*?)] ?(?:\n *)?\[([\s\S]*?)]()()()()()/g,d)).replace(/!\[([^\]]*?)][ \t]*()\([ \t]?<?(data:.+?\/.+?;base64,[A-Za-z0-9+/=\n]+?)>?(?: =([*\d]+[A-Za-z%]{0,4})x([*\d]+[A-Za-z%]{0,4}))?[ \t]*(?:(["'])([^"]*?)\6)?[ \t]?\)/g,function(a,b,c,e,f,g,h,i){return d(a,b,c,e=e.replace(/\s/g,""),f,g,0,i)})).replace(/!\[([^\]]*?)][ \t]*()\([ \t]?<([^>]*)>(?: =([*\d]+[A-Za-z%]{0,4})x([*\d]+[A-Za-z%]{0,4}))?[ \t]*(?:(?:(["'])([^"]*?)\6))?[ \t]?\)/g,d)).replace(/!\[([^\]]*?)][ \t]*()\([ \t]?<?([\S]+?(?:\([\S]*?\)[\S]*?)?)>?(?: =([*\d]+[A-Za-z%]{0,4})x([*\d]+[A-Za-z%]{0,4}))?[ \t]*(?:(["'])([^"]*?)\6)?[ \t]?\)/g,d)).replace(/!\[([^\[\]]+)]()()()()()/g,d),a=c.converter._dispatch("images.after",a,b,c)}),g.subParser("italicsAndBold",function(a,b,c){"use strict";return a=c.converter._dispatch("italicsAndBold.before",a,b,c),a=b.literalMidWordUnderscores?(a=(a=a.replace(/\b___(\S[\s\S]*?)___\b/g,function(a,b){return"<strong><em>"+b+"</em></strong>"})).replace(/\b__(\S[\s\S]*?)__\b/g,function(a,b){return"<strong>"+b+"</strong>"})).replace(/\b_(\S[\s\S]*?)_\b/g,function(a,b){return"<em>"+b+"</em>"}):(a=(a=a.replace(/___(\S[\s\S]*?)___/g,function(a,b){return/\S$/.test(b)?"<strong><em>"+b+"</em></strong>":a})).replace(/__(\S[\s\S]*?)__/g,function(a,b){return/\S$/.test(b)?"<strong>"+b+"</strong>":a})).replace(/_([^\s_][\s\S]*?)_/g,function(a,b){return/\S$/.test(b)?"<em>"+b+"</em>":a}),a=b.literalMidWordAsterisks?(a=(a=a.replace(/([^*]|^)\B\*\*\*(\S[\s\S]*?)\*\*\*\B(?!\*)/g,function(a,b,c){return b+"<strong><em>"+c+"</em></strong>"})).replace(/([^*]|^)\B\*\*(\S[\s\S]*?)\*\*\B(?!\*)/g,function(a,b,c){return b+"<strong>"+c+"</strong>"})).replace(/([^*]|^)\B\*(\S[\s\S]*?)\*\B(?!\*)/g,function(a,b,c){return b+"<em>"+c+"</em>"}):(a=(a=a.replace(/\*\*\*(\S[\s\S]*?)\*\*\*/g,function(a,b){return/\S$/.test(b)?"<strong><em>"+b+"</em></strong>":a})).replace(/\*\*(\S[\s\S]*?)\*\*/g,function(a,b){return/\S$/.test(b)?"<strong>"+b+"</strong>":a})).replace(/\*([^\s*][\s\S]*?)\*/g,function(a,b){return/\S$/.test(b)?"<em>"+b+"</em>":a}),a=c.converter._dispatch("italicsAndBold.after",a,b,c)}),g.subParser("lists",function(a,b,c){"use strict";function d(a,d){c.gListLevel++,a=a.replace(/\n{2,}$/,"\n");var e=/(\n)?(^ {0,3})([*+-]|\d+[.])[ \t]+((\[(x|X| )?])?[ \t]*[^\r]+?(\n{1,2}))(?=\n*(¨0| {0,3}([*+-]|\d+[.])[ \t]+))/gm,f=/\n[ \t]*\n(?!¨0)/.test(a+="¨0");return b.disableForced4SpacesIndentedSublists&&(e=/(\n)?(^ {0,3})([*+-]|\d+[.])[ \t]+((\[(x|X| )?])?[ \t]*[^\r]+?(\n{1,2}))(?=\n*(¨0|\2([*+-]|\d+[.])[ \t]+))/gm),a=(a=a.replace(e,function(a,d,e,h,i,j,k){k=k&&""!==k.trim();var i=g.subParser("outdent")(i,b,c),l="";return j&&b.tasklists&&(l=' class="task-list-item" style="list-style-type: none;"',i=i.replace(/^[ \t]*\[(x|X| )?]/m,function(){var a='<input type="checkbox" disabled style="margin: 0px 0.35em 0.25em -1.6em; vertical-align: middle;"';return k&&(a+=" checked"),a+=">"})),i=i.replace(/^([-*+]|\d\.)[ \t]+[\S\n ]*/g,function(a){return"¨A"+a}),i="<li"+l+">"+(i=(i=d||-1<i.search(/\n{2,}/)?(i=g.subParser("githubCodeBlocks")(i,b,c),g.subParser("blockGamut")(i,b,c)):(i=(i=g.subParser("lists")(i,b,c)).replace(/\n$/,""),i=(i=g.subParser("hashHTMLBlocks")(i,b,c)).replace(/\n\n+/g,"\n\n"),(f?g.subParser("paragraphs"):g.subParser("spanGamut"))(i,b,c))).replace("¨A",""))+"</li>\n"})).replace(/¨0/g,""),c.gListLevel--,a=d?a.replace(/\s+$/,""):a}function e(a,b){return"ol"===b&&(b=a.match(/^ *(\d+)\./),b&&"1"!==b[1])?' start="'+b[1]+'"':""}function f(a,c,f){var g,h=b.disableForced4SpacesIndentedSublists?/^ ?\d+\.[ \t]/gm:/^ {0,3}\d+\.[ \t]/gm,i=b.disableForced4SpacesIndentedSublists?/^ ?[*+-][ \t]/gm:/^ {0,3}[*+-][ \t]/gm,j="ul"===c?h:i,k="";return-1!==a.search(j)?function l(b){var g=b.search(j),m=e(a,c);-1!==g?(k+="\n\n<"+c+m+">\n"+d(b.slice(0,g),!!f)+"</"+c+">\n",j="ul"==(c="ul"===c?"ol":"ul")?h:i,l(b.slice(g))):k+="\n\n<"+c+m+">\n"+d(b,!!f)+"</"+c+">\n"}(a):(g=e(a,c),k="\n\n<"+c+g+">\n"+d(a,!!f)+"</"+c+">\n"),k}return a=c.converter._dispatch("lists.before",a,b,c),a+="¨0",a=(a=c.gListLevel?a.replace(/^(( {0,3}([*+-]|\d+[.])[ \t]+)[^\r]+?(¨0|\n{2,}(?=\S)(?![ \t]*(?:[*+-]|\d+[.])[ \t]+)))/gm,function(a,b,c){return f(b,-1<c.search(/[*+-]/g)?"ul":"ol",!0)}):a.replace(/(\n\n|^\n?)(( {0,3}([*+-]|\d+[.])[ \t]+)[^\r]+?(¨0|\n{2,}(?=\S)(?![ \t]*(?:[*+-]|\d+[.])[ \t]+)))/gm,function(a,b,c,d){return f(c,-1<d.search(/[*+-]/g)?"ul":"ol",!1)})).replace(/¨0/,""),a=c.converter._dispatch("lists.after",a,b,c)}),g.subParser("metadata",function(a,b,c){"use strict";function d(a){(a=(a=(c.metadata.raw=a).replace(/&/g,"&amp;").replace(/"/g,"&quot;")).replace(/\n {4}/g," ")).replace(/^([\S ]+): +([\s\S]+?)$/gm,function(a,b,d){return c.metadata.parsed[b]=d,""})}return b.metadata?(a=(a=(a=(a=c.converter._dispatch("metadata.before",a,b,c)).replace(/^\s*«««+(\S*?)\n([\s\S]+?)\n»»»+\n/,function(a,b,c){return d(c),"¨M"})).replace(/^\s*---+(\S*?)\n([\s\S]+?)\n---+\n/,function(a,b,e){return b&&(c.metadata.format=b),d(e),"¨M"})).replace(/¨M/g,""),c.converter._dispatch("metadata.after",a,b,c)):a}),g.subParser("outdent",function(a,b,c){"use strict";return a=(a=(a=c.converter._dispatch("outdent.before",a,b,c)).replace(/^(\t|[ ]{1,4})/gm,"¨0")).replace(/¨0/g,""),a=c.converter._dispatch("outdent.after",a,b,c)}),g.subParser("paragraphs",function(a,b,c){"use strict";for(var d=(a=(a=(a=c.converter._dispatch("paragraphs.before",a,b,c)).replace(/^\n+/g,"")).replace(/\n+$/g,"")).split(/\n{2,}/g),e=[],f=d.length,h=0;h<f;h++){var i=d[h];0<=i.search(/¨(K|G)(\d+)\1/g)?e.push(i):0<=i.search(/\S/)&&(i=(i=g.subParser("spanGamut")(i,b,c)).replace(/^([ \t]*)/g,"<p>"),i+="</p>",e.push(i))}for(f=e.length,h=0;h<f;h++){for(var j="",k=e[h],l=!1;/¨(K|G)(\d+)\1/.test(k);){var m=RegExp.$1,n=RegExp.$2;j=(j="K"===m?c.gHtmlBlocks[n]:l?g.subParser("encodeCode")(c.ghCodeBlocks[n].text,b,c):c.ghCodeBlocks[n].codeblock).replace(/\$/g,"$$$$"),k=k.replace(/(\n\n)?¨(K|G)\d+\2(\n\n)?/,j),/^<pre\b[^>]*>\s*<code\b[^>]*>/.test(k)&&(l=!0)}e[h]=k}return a=(a=(a=e.join("\n")).replace(/^\n+/g,"")).replace(/\n+$/g,""),c.converter._dispatch("paragraphs.after",a,b,c)}),g.subParser("runExtension",function(a,b,c,d){"use strict";return a.filter?b=a.filter(b,d.converter,c):a.regex&&((d=a.regex)instanceof RegExp||(d=new RegExp(d,"g")),b=b.replace(d,a.replace)),b}),g.subParser("spanGamut",function(a,b,c){"use strict";return a=c.converter._dispatch("spanGamut.before",a,b,c),a=g.subParser("codeSpans")(a,b,c),a=g.subParser("escapeSpecialCharsWithinTagAttributes")(a,b,c),a=g.subParser("encodeBackslashEscapes")(a,b,c),a=g.subParser("images")(a,b,c),a=g.subParser("anchors")(a,b,c),a=g.subParser("autoLinks")(a,b,c),a=g.subParser("simplifiedAutoLinks")(a,b,c),a=g.subParser("emoji")(a,b,c),a=g.subParser("underline")(a,b,c),a=g.subParser("italicsAndBold")(a,b,c),a=g.subParser("strikethrough")(a,b,c),a=g.subParser("ellipsis")(a,b,c),a=g.subParser("hashHTMLSpans")(a,b,c),a=g.subParser("encodeAmpsAndAngles")(a,b,c),b.simpleLineBreaks?/\n\n¨K/.test(a)||(a=a.replace(/\n+/g,"<br />\n")):a=a.replace(/  +\n/g,"<br />\n"),a=c.converter._dispatch("spanGamut.after",a,b,c)}),g.subParser("strikethrough",function(a,b,c){"use strict";return b.strikethrough&&(a=(a=c.converter._dispatch("strikethrough.before",a,b,c)).replace(/(?:~){2}([\s\S]+?)(?:~){2}/g,function(a,d){return d=d,"<del>"+(d=b.simplifiedAutoLink?g.subParser("simplifiedAutoLinks")(d,b,c):d)+"</del>"}),a=c.converter._dispatch("strikethrough.after",a,b,c)),a}),g.subParser("stripLinkDefinitions",function(a,b,c){"use strict";function d(d,e,f,h,i,j,k){return e=e.toLowerCase(),a.toLowerCase().split(e).length-1<2?d:(f.match(/^data:.+?\/.+?;base64,/)?c.gUrls[e]=f.replace(/\s/g,""):c.gUrls[e]=g.subParser("encodeAmpsAndAngles")(f,b,c),j?j+k:(k&&(c.gTitles[e]=k.replace(/"|'/g,"&quot;")),b.parseImgDimensions&&h&&i&&(c.gDimensions[e]={width:h,height:i}),""))}return a=(a=(a=(a+="¨0").replace(/^ {0,3}\[([^\]]+)]:[ \t]*\n?[ \t]*<?(data:.+?\/.+?;base64,[A-Za-z0-9+/=\n]+?)>?(?: =([*\d]+[A-Za-z%]{0,4})x([*\d]+[A-Za-z%]{0,4}))?[ \t]*\n?[ \t]*(?:(\n*)["|'(](.+?)["|')][ \t]*)?(?:\n\n|(?=¨0)|(?=\n\[))/gm,d)).replace(/^ {0,3}\[([^\]]+)]:[ \t]*\n?[ \t]*<?([^>\s]+)>?(?: =([*\d]+[A-Za-z%]{0,4})x([*\d]+[A-Za-z%]{0,4}))?[ \t]*\n?[ \t]*(?:(\n*)["|'(](.+?)["|')][ \t]*)?(?:\n+|(?=¨0))/gm,d)).replace(/¨0/,"")}),g.subParser("tables",function(a,b,c){"use strict";function d(a){for(var d=a.split("\n"),e=0;e<d.length;++e)/^ {0,3}\|/.test(d[e])&&(d[e]=d[e].replace(/^ {0,3}\|/,"")),/\|[ \t]*$/.test(d[e])&&(d[e]=d[e].replace(/\|[ \t]*$/,"")),d[e]=g.subParser("codeSpans")(d[e],b,c);var f,h,i,j,k,l=d[0].split("|").map(function(a){return a.trim()}),m=d[1].split("|").map(function(a){return a.trim()}),n=[],o=[],p=[],q=[];for(d.shift(),d.shift(),e=0;e<d.length;++e)""!==d[e].trim()&&n.push(d[e].split("|").map(function(a){return a.trim()}));if(l.length<m.length)return a;for(e=0;e<m.length;++e)p.push((f=m[e],/^:[ \t]*--*$/.test(f)?' style="text-align:left;"':/^--*[ \t]*:[ \t]*$/.test(f)?' style="text-align:right;"':/^:[ \t]*--*[ \t]*:$/.test(f)?' style="text-align:center;"':""));for(e=0;e<l.length;++e)g.helper.isUndefined(p[e])&&(p[e]=""),o.push((h=l[e],i=p[e],j="",h=h.trim(),"<th"+(j=b.tablesHeaderId||b.tableHeaderId?' id="'+h.replace(/ /g,"_").toLowerCase()+'"':j)+i+">"+(h=g.subParser("spanGamut")(h,b,c))+"</th>\n"));for(e=0;e<n.length;++e){for(var r=[],s=0;s<o.length;++s)g.helper.isUndefined(n[e][s]),r.push((k=n[e][s],"<td"+p[s]+">"+g.subParser("spanGamut")(k,b,c)+"</td>\n"));q.push(r)}for(var t=o,u=q,v="<table>\n<thead>\n<tr>\n",w=t.length,x=0;x<w;++x)v+=t[x];for(v+="</tr>\n</thead>\n<tbody>\n",x=0;x<u.length;++x){v+="<tr>\n";
for(var y=0;y<w;++y)v+=u[x][y];v+="</tr>\n"}return v+="</tbody>\n</table>\n"}return b.tables?(a=(a=(a=(a=c.converter._dispatch("tables.before",a,b,c)).replace(/\\(\|)/g,g.helper.escapeCharactersCallback)).replace(/^ {0,3}\|?.+\|.+\n {0,3}\|?[ \t]*:?[ \t]*(?:[-=]){2,}[ \t]*:?[ \t]*\|[ \t]*:?[ \t]*(?:[-=]){2,}[\s\S]+?(?:\n\n|¨0)/gm,d)).replace(/^ {0,3}\|.+\|[ \t]*\n {0,3}\|[ \t]*:?[ \t]*(?:[-=]){2,}[ \t]*:?[ \t]*\|[ \t]*\n( {0,3}\|.+\|[ \t]*\n)*(?:\n|¨0)/gm,d),a=c.converter._dispatch("tables.after",a,b,c)):a}),g.subParser("underline",function(a,b,c){"use strict";return b.underline?(a=c.converter._dispatch("underline.before",a,b,c),a=(a=b.literalMidWordUnderscores?(a=a.replace(/\b___(\S[\s\S]*?)___\b/g,function(a,b){return"<u>"+b+"</u>"})).replace(/\b__(\S[\s\S]*?)__\b/g,function(a,b){return"<u>"+b+"</u>"}):(a=a.replace(/___(\S[\s\S]*?)___/g,function(a,b){return/\S$/.test(b)?"<u>"+b+"</u>":a})).replace(/__(\S[\s\S]*?)__/g,function(a,b){return/\S$/.test(b)?"<u>"+b+"</u>":a})).replace(/(_)/g,g.helper.escapeCharactersCallback),c.converter._dispatch("underline.after",a,b,c)):a}),g.subParser("unescapeSpecialChars",function(a,b,c){"use strict";return a=(a=c.converter._dispatch("unescapeSpecialChars.before",a,b,c)).replace(/¨E(\d+)E/g,function(a,b){return b=parseInt(b),String.fromCharCode(b)}),a=c.converter._dispatch("unescapeSpecialChars.after",a,b,c)}),g.subParser("makeMarkdown.blockquote",function(a,b){"use strict";var c="";if(a.hasChildNodes())for(var d=a.childNodes,e=d.length,f=0;f<e;++f){var h=g.subParser("makeMarkdown.node")(d[f],b);""!==h&&(c+=h)}return c="> "+(c=c.trim()).split("\n").join("\n> ")}),g.subParser("makeMarkdown.codeBlock",function(a,b){"use strict";var c=a.getAttribute("language"),a=a.getAttribute("precodenum");return"```"+c+"\n"+b.preList[a]+"\n```"}),g.subParser("makeMarkdown.codeSpan",function(a){"use strict";return"`"+a.innerHTML+"`"}),g.subParser("makeMarkdown.emphasis",function(a,b){"use strict";var c="";if(a.hasChildNodes()){c+="*";for(var d=a.childNodes,e=d.length,f=0;f<e;++f)c+=g.subParser("makeMarkdown.node")(d[f],b);c+="*"}return c}),g.subParser("makeMarkdown.header",function(a,b,c){"use strict";var c=new Array(c+1).join("#"),d="";if(a.hasChildNodes())for(var d=c+" ",e=a.childNodes,f=e.length,h=0;h<f;++h)d+=g.subParser("makeMarkdown.node")(e[h],b);return d}),g.subParser("makeMarkdown.hr",function(){"use strict";return"---"}),g.subParser("makeMarkdown.image",function(a){"use strict";var b="";return a.hasAttribute("src")&&(b=(b+="!["+a.getAttribute("alt")+"](")+"<"+a.getAttribute("src")+">",a.hasAttribute("width")&&a.hasAttribute("height")&&(b+=" ="+a.getAttribute("width")+"x"+a.getAttribute("height")),a.hasAttribute("title")&&(b+=' "'+a.getAttribute("title")+'"'),b+=")"),b}),g.subParser("makeMarkdown.links",function(a,b){"use strict";var c="";if(a.hasChildNodes()&&a.hasAttribute("href")){for(var d=a.childNodes,e=d.length,c="[",f=0;f<e;++f)c+=g.subParser("makeMarkdown.node")(d[f],b);c=(c+="](")+("<"+a.getAttribute("href")+">"),a.hasAttribute("title")&&(c+=' "'+a.getAttribute("title")+'"'),c+=")"}return c}),g.subParser("makeMarkdown.list",function(a,b,c){"use strict";var d="";if(!a.hasChildNodes())return"";for(var e=a.childNodes,f=e.length,h=a.getAttribute("start")||1,i=0;i<f;++i)void 0!==e[i].tagName&&"li"===e[i].tagName.toLowerCase()&&(d+=("ol"===c?h.toString()+". ":"- ")+g.subParser("makeMarkdown.listItem")(e[i],b),++h);return(d+="\n<!-- -->\n").trim()}),g.subParser("makeMarkdown.listItem",function(a,b){"use strict";for(var c="",d=a.childNodes,e=d.length,f=0;f<e;++f)c+=g.subParser("makeMarkdown.node")(d[f],b);return/\n$/.test(c)?c=c.split("\n").join("\n    ").replace(/^ {4}$/gm,"").replace(/\n\n+/g,"\n\n"):c+="\n",c}),g.subParser("makeMarkdown.node",function(a,b,c){"use strict";c=c||!1;var d="";if(3===a.nodeType)return g.subParser("makeMarkdown.txt")(a,b);if(8===a.nodeType)return"<!--"+a.data+"-->\n\n";if(1!==a.nodeType)return"";switch(a.tagName.toLowerCase()){case"h1":c||(d=g.subParser("makeMarkdown.header")(a,b,1)+"\n\n");break;case"h2":c||(d=g.subParser("makeMarkdown.header")(a,b,2)+"\n\n");break;case"h3":c||(d=g.subParser("makeMarkdown.header")(a,b,3)+"\n\n");break;case"h4":c||(d=g.subParser("makeMarkdown.header")(a,b,4)+"\n\n");break;case"h5":c||(d=g.subParser("makeMarkdown.header")(a,b,5)+"\n\n");break;case"h6":c||(d=g.subParser("makeMarkdown.header")(a,b,6)+"\n\n");break;case"p":c||(d=g.subParser("makeMarkdown.paragraph")(a,b)+"\n\n");break;case"blockquote":c||(d=g.subParser("makeMarkdown.blockquote")(a,b)+"\n\n");break;case"hr":c||(d=g.subParser("makeMarkdown.hr")(a,b)+"\n\n");break;case"ol":c||(d=g.subParser("makeMarkdown.list")(a,b,"ol")+"\n\n");break;case"ul":c||(d=g.subParser("makeMarkdown.list")(a,b,"ul")+"\n\n");break;case"precode":c||(d=g.subParser("makeMarkdown.codeBlock")(a,b)+"\n\n");break;case"pre":c||(d=g.subParser("makeMarkdown.pre")(a,b)+"\n\n");break;case"table":c||(d=g.subParser("makeMarkdown.table")(a,b)+"\n\n");break;case"code":d=g.subParser("makeMarkdown.codeSpan")(a,b);break;case"em":case"i":d=g.subParser("makeMarkdown.emphasis")(a,b);break;case"strong":case"b":d=g.subParser("makeMarkdown.strong")(a,b);break;case"del":d=g.subParser("makeMarkdown.strikethrough")(a,b);break;case"a":d=g.subParser("makeMarkdown.links")(a,b);break;case"img":d=g.subParser("makeMarkdown.image")(a,b);break;default:d=a.outerHTML+"\n\n"}return d}),g.subParser("makeMarkdown.paragraph",function(a,b){"use strict";var c="";if(a.hasChildNodes())for(var d=a.childNodes,e=d.length,f=0;f<e;++f)c+=g.subParser("makeMarkdown.node")(d[f],b);return c=c.trim()}),g.subParser("makeMarkdown.pre",function(a,b){"use strict";return a=a.getAttribute("prenum"),"<pre>"+b.preList[a]+"</pre>"}),g.subParser("makeMarkdown.strikethrough",function(a,b){"use strict";var c="";if(a.hasChildNodes()){c+="~~";for(var d=a.childNodes,e=d.length,f=0;f<e;++f)c+=g.subParser("makeMarkdown.node")(d[f],b);c+="~~"}return c}),g.subParser("makeMarkdown.strong",function(a,b){"use strict";var c="";if(a.hasChildNodes()){c+="**";for(var d=a.childNodes,e=d.length,f=0;f<e;++f)c+=g.subParser("makeMarkdown.node")(d[f],b);c+="**"}return c}),g.subParser("makeMarkdown.table",function(a,b){"use strict";for(var c="",d=[[],[]],e=a.querySelectorAll("thead>tr>th"),f=a.querySelectorAll("tbody>tr"),h=0;h<e.length;++h){var i=g.subParser("makeMarkdown.tableCell")(e[h],b),j="---";if(e[h].hasAttribute("style"))switch(e[h].getAttribute("style").toLowerCase().replace(/\s/g,"")){case"text-align:left;":j=":---";break;case"text-align:right;":j="---:";break;case"text-align:center;":j=":---:"}d[0][h]=i.trim(),d[1][h]=j}for(h=0;h<f.length;++h)for(var k=d.push([])-1,l=f[h].getElementsByTagName("td"),m=0;m<e.length;++m){var n=" ";void 0!==l[m]&&(n=g.subParser("makeMarkdown.tableCell")(l[m],b)),d[k].push(n)}var o=3;for(h=0;h<d.length;++h)for(m=0;m<d[h].length;++m){var p=d[h][m].length;o<p&&(o=p)}for(h=0;h<d.length;++h){for(m=0;m<d[h].length;++m)1===h?":"===d[h][m].slice(-1)?d[h][m]=g.helper.padEnd(d[h][m].slice(-1),o-1,"-")+":":d[h][m]=g.helper.padEnd(d[h][m],o,"-"):d[h][m]=g.helper.padEnd(d[h][m],o);c+="| "+d[h].join(" | ")+" |\n"}return c.trim()}),g.subParser("makeMarkdown.tableCell",function(a,b){"use strict";var c="";if(!a.hasChildNodes())return"";for(var d=a.childNodes,e=d.length,f=0;f<e;++f)c+=g.subParser("makeMarkdown.node")(d[f],b,!0);return c.trim()}),g.subParser("makeMarkdown.txt",function(a){"use strict";return a=a.nodeValue,a=(a=a.replace(/ +/g," ")).replace(/¨NBSP;/g," "),a=(a=(a=(a=(a=(a=(a=(a=(a=g.helper.unescapeHTMLEntities(a)).replace(/([*_~|`])/g,"\\$1")).replace(/^(\s*)>/g,"\\$1>")).replace(/^#/gm,"\\#")).replace(/^(\s*)([-=]{3,})(\s*)$/,"$1\\$2$3")).replace(/^( {0,3}\d+)\./gm,"$1\\.")).replace(/^( {0,3})([+-])/gm,"$1\\$2")).replace(/]([\s]*)\(/g,"\\]$1\\(")).replace(/^ {0,3}\[([\S \t]*?)]:/gm,"\\[$1]:")}),"function"==typeof define&&define.amd?define(function(){"use strict";return g}):"undefined"!=typeof module&&module.exports?module.exports=g:this.showdown=g}.call(this);