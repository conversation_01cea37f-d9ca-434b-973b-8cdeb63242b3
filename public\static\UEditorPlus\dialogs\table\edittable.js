/*! UEditorPlus v2.0.0*/
!function(){var a,b=$G("J_title"),c=$G("J_titleCol"),d=$G("J_caption"),e=$G("J_sorttable"),f=$G("J_autoSizeContent"),g=$G("J_autoSizePage"),h=$G("J_tone"),i=$G("J_preview"),j=function(){a=this,a.init()};j.prototype={init:function(){var i=new UE.ui.ColorPicker({editor:editor}),j=new UE.ui.Popup({editor:editor,content:i});b.checked=editor.queryCommandState("inserttitle")==-1,c.checked=editor.queryCommandState("inserttitlecol")==-1,d.checked=editor.queryCommandState("insertcaption")==-1,e.checked=1==editor.queryCommandState("enablesort");var k=editor.queryCommandState("enablesort"),l=editor.queryCommandState("disablesort");e.checked=!!(k<0&&l>=0),e.disabled=!!(k<0&&l<0),e.title=k<0&&l<0?lang.errorMsg:"",a.createTable(b.checked,c.checked,d.checked),a.setAutoSize(),a.setColor(a.getColor()),domUtils.on(b,"click",a.titleHanler),domUtils.on(c,"click",a.titleColHanler),domUtils.on(d,"click",a.captionHanler),domUtils.on(e,"click",a.sorttableHanler),domUtils.on(f,"click",a.autoSizeContentHanler),domUtils.on(g,"click",a.autoSizePageHanler),domUtils.on(h,"click",function(){j.showAnchor(h)}),domUtils.on(document,"mousedown",function(){j.hide()}),i.addListener("pickcolor",function(){a.setColor(arguments[1]),j.hide()}),i.addListener("picknocolor",function(){a.setColor(""),j.hide()})},createTable:function(a,b,c){var d=[];if(d.push("<table id='J_example'>"),c&&d.push("<caption>"+lang.captionName+"</caption>"),a){d.push("<tr>"),b&&d.push("<th>"+lang.titleName+"</th>");for(var e=0;e<5;e++)d.push("<th>"+lang.titleName+"</th>");d.push("</tr>")}for(var f=0;f<6;f++){d.push("<tr>"),b&&d.push("<th>"+lang.titleName+"</th>");for(var g=0;g<5;g++)d.push("<td>"+lang.cellsName+"</td>");d.push("</tr>")}d.push("</table>"),i.innerHTML=d.join(""),this.updateSortSpan()},titleHanler:function(){var c=$G("J_example"),d=document.createDocumentFragment(),e=domUtils.getComputedStyle(domUtils.getElementsByTagName(c,"td")[0],"border-color"),f=c.rows[0].children.length;if(b.checked){c.insertRow(0);for(var g,h=0;h<f;h++)g=document.createElement("th"),g.innerHTML=lang.titleName,d.appendChild(g);c.rows[0].appendChild(d)}else domUtils.remove(c.rows[0]);a.setColor(e),a.updateSortSpan()},titleColHanler:function(){var b=$G("J_example"),d=domUtils.getComputedStyle(domUtils.getElementsByTagName(b,"td")[0],"border-color"),e=b.rows,f=e.length;if(c.checked)for(var g,h=0;h<f;h++)g=document.createElement("th"),g.innerHTML=lang.titleName,e[h].insertBefore(g,e[h].children[0]);else for(var h=0;h<f;h++)domUtils.remove(e[h].children[0]);a.setColor(d),a.updateSortSpan()},captionHanler:function(){var a=$G("J_example");if(d.checked){var b=document.createElement("caption");b.innerHTML=lang.captionName,a.insertBefore(b,a.firstChild)}else domUtils.remove(domUtils.getElementsByTagName(a,"caption")[0])},sorttableHanler:function(){a.updateSortSpan()},autoSizeContentHanler:function(){var a=$G("J_example");a.removeAttribute("width")},autoSizePageHanler:function(){var a=$G("J_example"),b=a.getElementsByTagName(a,"td");utils.each(b,function(a){a.removeAttribute("width")}),a.setAttribute("width","100%")},updateSortSpan:function(){var a=$G("J_example"),b=a.rows[0],c=domUtils.getElementsByTagName(a,"span");utils.each(c,function(a){a.parentNode.removeChild(a)}),e.checked&&utils.each(b.cells,function(a,b){var c=document.createElement("span");c.innerHTML="^",a.appendChild(c)})},getColor:function(){var a,b=editor.selection.getStart(),c=domUtils.findParentByTagName(b,["td","th","caption"],!0);return a=c&&domUtils.getComputedStyle(c,"border-color"),a||(a="#DDDDDD"),a},setColor:function(a){var b=$G("J_example"),c=domUtils.getElementsByTagName(b,"td").concat(domUtils.getElementsByTagName(b,"th"),domUtils.getElementsByTagName(b,"caption"));h.value=a,utils.each(c,function(b){b.style.borderColor=a})},setAutoSize:function(){var a=this;g.checked=!0,a.autoSizePageHanler()}},new j,dialog.onok=function(){editor.__hasEnterExecCommand=!0;var a={title:"inserttitle deletetitle",titleCol:"inserttitlecol deletetitlecol",caption:"insertcaption deletecaption",sorttable:"enablesort disablesort"};editor.fireEvent("saveScene");for(var b in a){var c=a[b].split(" "),d=$G("J_"+b);d.checked?editor.queryCommandState(c[0])!=-1&&editor.execCommand(c[0]):editor.queryCommandState(c[1])!=-1&&editor.execCommand(c[1])}editor.execCommand("edittable",h.value),f.checked?editor.execCommand("adaptbytext"):"",g.checked?editor.execCommand("adaptbywindow"):"",editor.fireEvent("saveScene"),editor.__hasEnterExecCommand=!1}}();