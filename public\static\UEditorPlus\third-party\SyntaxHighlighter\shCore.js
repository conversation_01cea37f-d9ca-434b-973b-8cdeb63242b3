/*! UEditorPlus v2.0.0*/
var XRegExp;if(XRegExp)throw Error("can't load XRegExp twice in the same frame");if(function(a){function b(a,b){if(!XRegExp.isRegExp(a))throw TypeError("type RegExp expected");var d=a._xregexp;return a=XRegExp(a.source,c(a)+(b||"")),d&&(a._xregexp={source:d.source,captureNames:d.captureNames?d.captureNames.slice(0):null}),a}function c(a){return(a.global?"g":"")+(a.ignoreCase?"i":"")+(a.multiline?"m":"")+(a.extended?"x":"")+(a.sticky?"y":"")}function d(a,b,c,d){var e,f,g,h=j.length;i=!0;try{for(;h--;)if(g=j[h],c&g.scope&&(!g.trigger||g.trigger.call(d))&&(g.pattern.lastIndex=b,f=g.pattern.exec(a),f&&f.index===b)){e={output:g.handler.call(d,f,c),match:f};break}}catch(k){throw k}finally{i=!1}return e}function e(a,b,c){if(Array.prototype.indexOf)return a.indexOf(b,c);for(var d=c||0;d<a.length;d++)if(a[d]===b)return d;return-1}XRegExp=function(c,e){var f,h,j,l,m,n=[],p=XRegExp.OUTSIDE_CLASS,q=0;if(XRegExp.isRegExp(c)){if(e!==a)throw TypeError("can't supply flags when constructing one RegExp from another");return b(c)}if(i)throw Error("can't call the XRegExp constructor within token definition functions");for(e=e||"",f={hasNamedCapture:!1,captureNames:[],hasFlag:function(a){return e.indexOf(a)>-1},setFlag:function(a){e+=a}};q<c.length;)h=d(c,q,p,f),h?(n.push(h.output),q+=h.match[0].length||1):(j=k.exec.call(o[p],c.slice(q)))?(n.push(j[0]),q+=j[0].length):(l=c.charAt(q),"["===l?p=XRegExp.INSIDE_CLASS:"]"===l&&(p=XRegExp.OUTSIDE_CLASS),n.push(l),q++);return m=RegExp(n.join(""),k.replace.call(e,g,"")),m._xregexp={source:c,captureNames:f.hasNamedCapture?f.captureNames:null},m},XRegExp.version="1.5.1",XRegExp.INSIDE_CLASS=1,XRegExp.OUTSIDE_CLASS=2;var f=/\$(?:(\d\d?|[$&`'])|{([$\w]+)})/g,g=/[^gimy]+|([\s\S])(?=[\s\S]*\1)/g,h=/^(?:[?*+]|{\d+(?:,\d*)?})\??/,i=!1,j=[],k={exec:RegExp.prototype.exec,test:RegExp.prototype.test,match:String.prototype.match,replace:String.prototype.replace,split:String.prototype.split},l=k.exec.call(/()??/,"")[1]===a,m=function(){var a=/^/g;return k.test.call(a,""),!a.lastIndex}(),n=RegExp.prototype.sticky!==a,o={};o[XRegExp.INSIDE_CLASS]=/^(?:\\(?:[0-3][0-7]{0,2}|[4-7][0-7]?|x[\dA-Fa-f]{2}|u[\dA-Fa-f]{4}|c[A-Za-z]|[\s\S]))/,o[XRegExp.OUTSIDE_CLASS]=/^(?:\\(?:0(?:[0-3][0-7]{0,2}|[4-7][0-7]?)?|[1-9]\d*|x[\dA-Fa-f]{2}|u[\dA-Fa-f]{4}|c[A-Za-z]|[\s\S])|\(\?[:=!]|[?*+]\?|{\d+(?:,\d*)?}\??)/,XRegExp.addToken=function(a,c,d,e){j.push({pattern:b(a,"g"+(n?"y":"")),handler:c,scope:d||XRegExp.OUTSIDE_CLASS,trigger:e||null})},XRegExp.cache=function(a,b){var c=a+"/"+(b||"");return XRegExp.cache[c]||(XRegExp.cache[c]=XRegExp(a,b))},XRegExp.copyAsGlobal=function(a){return b(a,"g")},XRegExp.escape=function(a){return a.replace(/[-[\]{}()*+?.,\\^$|#\s]/g,"\\$&")},XRegExp.execAt=function(a,c,d,e){var f,g=b(c,"g"+(e&&n?"y":""));return g.lastIndex=d=d||0,f=g.exec(a),e&&f&&f.index!==d&&(f=null),c.global&&(c.lastIndex=f?g.lastIndex:0),f},XRegExp.freezeTokens=function(){XRegExp.addToken=function(){throw Error("can't run addToken after freezeTokens")}},XRegExp.isRegExp=function(a){return"[object RegExp]"===Object.prototype.toString.call(a)},XRegExp.iterate=function(a,c,d,e){for(var f,g=b(c,"g"),h=-1;f=g.exec(a);)c.global&&(c.lastIndex=g.lastIndex),d.call(e,f,++h,a,c),g.lastIndex===f.index&&g.lastIndex++;c.global&&(c.lastIndex=0)},XRegExp.matchChain=function(a,c){return function d(a,e){var f,g=c[e].regex?c[e]:{regex:c[e]},h=b(g.regex,"g"),i=[];for(f=0;f<a.length;f++)XRegExp.iterate(a[f],h,function(a){i.push(g.backref?a[g.backref]||"":a[0])});return e!==c.length-1&&i.length?d(i,e+1):i}([a],0)},RegExp.prototype.apply=function(a,b){return this.exec(b[0])},RegExp.prototype.call=function(a,b){return this.exec(b)},RegExp.prototype.exec=function(b){var d,f,g,h;if(this.global||(h=this.lastIndex),d=k.exec.apply(this,arguments)){if(!l&&d.length>1&&e(d,"")>-1&&(g=RegExp(this.source,k.replace.call(c(this),"g","")),k.replace.call((b+"").slice(d.index),g,function(){for(var b=1;b<arguments.length-2;b++)arguments[b]===a&&(d[b]=a)})),this._xregexp&&this._xregexp.captureNames)for(var i=1;i<d.length;i++)f=this._xregexp.captureNames[i-1],f&&(d[f]=d[i]);!m&&this.global&&!d[0].length&&this.lastIndex>d.index&&this.lastIndex--}return this.global||(this.lastIndex=h),d},RegExp.prototype.test=function(a){var b,c;return this.global||(c=this.lastIndex),b=k.exec.call(this,a),b&&!m&&this.global&&!b[0].length&&this.lastIndex>b.index&&this.lastIndex--,this.global||(this.lastIndex=c),!!b},String.prototype.match=function(a){if(XRegExp.isRegExp(a)||(a=RegExp(a)),a.global){var b=k.match.apply(this,arguments);return a.lastIndex=0,b}return a.exec(this)},String.prototype.replace=function(a,b){var c,d,g,h,i=XRegExp.isRegExp(a);return i?(a._xregexp&&(c=a._xregexp.captureNames),a.global||(h=a.lastIndex)):a+="","[object Function]"===Object.prototype.toString.call(b)?d=k.replace.call(this+"",a,function(){if(c){arguments[0]=new String(arguments[0]);for(var d=0;d<c.length;d++)c[d]&&(arguments[0][c[d]]=arguments[d+1])}return i&&a.global&&(a.lastIndex=arguments[arguments.length-2]+arguments[0].length),b.apply(null,arguments)}):(g=this+"",d=k.replace.call(g,a,function(){var a=arguments;return k.replace.call(b+"",f,function(b,d,f){if(!d){var g=+f;return g<=a.length-3?a[g]:(g=c?e(c,f):-1,g>-1?a[g+1]:b)}switch(d){case"$":return"$";case"&":return a[0];case"`":return a[a.length-1].slice(0,a[a.length-2]);case"'":return a[a.length-1].slice(a[a.length-2]+a[0].length);default:var h="";if(d=+d,!d)return b;for(;d>a.length-3;)h=String.prototype.slice.call(d,-1)+h,d=Math.floor(d/10);return(d?a[d]||"":"$")+h}})})),i&&(a.global?a.lastIndex=0:a.lastIndex=h),d},String.prototype.split=function(b,c){if(!XRegExp.isRegExp(b))return k.split.apply(this,arguments);var d,e,f=this+"",g=[],h=0;if(c===a||+c<0)c=1/0;else if(c=Math.floor(+c),!c)return[];for(b=XRegExp.copyAsGlobal(b);(d=b.exec(f))&&!(b.lastIndex>h&&(g.push(f.slice(h,d.index)),d.length>1&&d.index<f.length&&Array.prototype.push.apply(g,d.slice(1)),e=d[0].length,h=b.lastIndex,g.length>=c));)b.lastIndex===d.index&&b.lastIndex++;return h===f.length?k.test.call(b,"")&&!e||g.push(""):g.push(f.slice(h)),g.length>c?g.slice(0,c):g},XRegExp.addToken(/\(\?#[^)]*\)/,function(a){return k.test.call(h,a.input.slice(a.index+a[0].length))?"":"(?:)"}),XRegExp.addToken(/\((?!\?)/,function(){return this.captureNames.push(null),"("}),XRegExp.addToken(/\(\?<([$\w]+)>/,function(a){return this.captureNames.push(a[1]),this.hasNamedCapture=!0,"("}),XRegExp.addToken(/\\k<([\w$]+)>/,function(a){var b=e(this.captureNames,a[1]);return b>-1?"\\"+(b+1)+(isNaN(a.input.charAt(a.index+a[0].length))?"":"(?:)"):a[0]}),XRegExp.addToken(/\[\^?]/,function(a){return"[]"===a[0]?"\\b\\B":"[\\s\\S]"}),XRegExp.addToken(/^\(\?([imsx]+)\)/,function(a){return this.setFlag(a[1]),""}),XRegExp.addToken(/(?:\s+|#.*)+/,function(a){return k.test.call(h,a.input.slice(a.index+a[0].length))?"":"(?:)"},XRegExp.OUTSIDE_CLASS,function(){return this.hasFlag("x")}),XRegExp.addToken(/\./,function(){return"[\\s\\S]"},XRegExp.OUTSIDE_CLASS,function(){return this.hasFlag("s")})}(),"undefined"==typeof SyntaxHighlighter)var SyntaxHighlighter=function(){function a(a,b){return a.className.indexOf(b)!=-1}function b(b,c){a(b,c)||(b.className+=" "+c)}function c(a,b){a.className=a.className.replace(b,"")}function d(a){for(var b=[],c=0;c<a.length;c++)b.push(a[c]);return b}function e(a){return a.split(/\r?\n/)}function f(a){var b="highlighter_";return 0==a.indexOf(b)?a:b+a}function g(a){return J.vars.highlighters[f(a)]}function h(a){return document.getElementById(f(a))}function i(a){J.vars.highlighters[f(a.id)]=a}function j(a,b,c){if(null==a)return null;var d,e,f=1!=c?a.childNodes:[a.parentNode],g={"#":"id",".":"className"}[b.substr(0,1)]||"nodeName";if(d="nodeName"!=g?b.substr(1):b.toUpperCase(),(a[g]||"").indexOf(d)!=-1)return a;for(var h=0;f&&h<f.length&&null==e;h++)e=j(f[h],b,c);return e}function k(a,b){return j(a,b,!0)}function l(a,b,c){c=Math.max(c||0,0);for(var d=c;d<a.length;d++)if(a[d]==b)return d;return-1}function m(a){return(a||"")+Math.round(1e6*Math.random()).toString()}function n(a,b){var c,d={};for(c in a)d[c]=a[c];for(c in b)d[c]=b[c];return d}function o(a){var b={"true":!0,"false":!1}[a];return null==b?a:b}function p(a,b,c,d,e){var f=(screen.width-c)/2,g=(screen.height-d)/2;e+=", left="+f+", top="+g+", width="+c+", height="+d,e=e.replace(/^,/,"");var h=window.open(a,b,e);return h.focus(),h}function q(a,b,c,d){function e(a){a=a||window.event,a.target||(a.target=a.srcElement,a.preventDefault=function(){this.returnValue=!1}),c.call(d||window,a)}a.attachEvent?a.attachEvent("on"+b,e):a.addEventListener(b,e,!1)}function r(a){window.alert(J.config.strings.alert+a)}function s(a,b){var c=J.vars.discoveredBrushes,d=null;if(null==c){c={};for(var e in J.brushes){var f=J.brushes[e],g=f.aliases;if(null!=g){f.brushName=e.toLowerCase();for(var h=0;h<g.length;h++)c[g[h]]=e}}J.vars.discoveredBrushes=c}return d=J.brushes[c[a]],null==d&&b&&r(J.config.strings.noBrush+a),d}function t(a,b){for(var c=e(a),d=0;d<c.length;d++)c[d]=b(c[d],d);return c.join("\r\n")}function u(a){return a.replace(/^[ ]*[\n]+|[\n]*[ ]*$/g,"")}function v(a){for(var b,c={},d=new XRegExp("^\\[(?<values>(.*?))\\]$"),e=new XRegExp("(?<name>[\\w-]+)\\s*:\\s*(?<value>[\\w-%#]+|\\[.*?\\]|\".*?\"|'.*?')\\s*;?","g");null!=(b=e.exec(a));){var f=b.value.replace(/^['"]|['"]$/g,"");if(null!=f&&d.test(f)){var g=d.exec(f);f=g.values.length>0?g.values.split(/\s*,\s*/):[]}c[b.name]=f}return c}function w(a,b){return null==a||0==a.length||"\n"==a?a:(a=a.replace(/</g,"&lt;"),a=a.replace(/ {2,}/g,function(a){for(var b="",c=0;c<a.length-1;c++)b+=J.config.space;return b+" "}),null!=b&&(a=t(a,function(a){if(0==a.length)return"";var c="";return a=a.replace(/^(&nbsp;| )+/,function(a){return c=a,""}),0==a.length?c:c+'<code class="'+b+'">'+a+"</code>"})),a)}function x(a,b){for(var c=a.toString();c.length<b;)c="0"+c;return c}function y(a,b){for(var c="",d=0;d<b;d++)c+=" ";return a.replace(/\t/g,c)}function z(a,b){function c(a,b,c){return a.substr(0,b)+f.substr(0,c)+a.substr(b+1,a.length)}for(var d=(e(a),"\t"),f="",g=0;g<50;g++)f+="                    ";return a=t(a,function(a){if(a.indexOf(d)==-1)return a;for(var e=0;(e=a.indexOf(d))!=-1;){var f=b-e%b;a=c(a,e,f)}return a})}function A(a){var b=/<br\s*\/?>|&lt;br\s*\/?&gt;/gi;return 1==J.config.bloggerMode&&(a=a.replace(b,"\n")),1==J.config.stripBrs&&(a=a.replace(b,"")),a}function B(a){return a.replace(/^\s+|\s+$/g,"")}function C(a){for(var b=e(A(a)),c=(new Array,/^\s*/),d=1e3,f=0;f<b.length&&d>0;f++){var g=b[f];if(0!=B(g).length){var h=c.exec(g);if(null==h)return a;d=Math.min(h[0].length,d)}}if(d>0)for(var f=0;f<b.length;f++)b[f]=b[f].substr(d);return b.join("\n")}function D(a,b){return a.index<b.index?-1:a.index>b.index?1:a.length<b.length?-1:a.length>b.length?1:0}function E(a,b){function c(a,b){return a[0]}for(var d=null,e=[],f=b.func?b.func:c;null!=(d=b.regex.exec(a));){var g=f(d,b);"string"==typeof g&&(g=[new J.Match(g,d.index,b.css)]),e=e.concat(g)}return e}function F(a){var b=/(.*)((&gt;|&lt;).*)/;return a.replace(J.regexLib.url,function(a){var c="",d=null;return(d=b.exec(a))&&(a=d[1],c=d[2]),'<a href="'+a+'">'+a+"</a>"+c})}function G(){for(var a=document.getElementsByTagName("script"),b=[],c=0;c<a.length;c++)"syntaxhighlighter"==a[c].type&&b.push(a[c]);return b}function H(a){var b="<![CDATA[",c="]]>",d=B(a),e=!1,f=b.length,g=c.length;0==d.indexOf(b)&&(d=d.substring(f),e=!0);var h=d.length;return d.indexOf(c)==h-g&&(d=d.substring(0,h-g),e=!0),e?d:a}function I(a){var d,e=a.target,f=k(e,".syntaxhighlighter"),h=k(e,".container"),i=document.createElement("textarea");if(h&&f&&!j(h,"textarea")){d=g(f.id),b(f,"source");for(var l=h.childNodes,m=[],n=0;n<l.length;n++)m.push(l[n].innerText||l[n].textContent);m=m.join("\r"),m=m.replace(/\u00a0/g," "),i.appendChild(document.createTextNode(m)),h.appendChild(i),i.focus(),i.select(),q(i,"blur",function(a){i.parentNode.removeChild(i),c(f,"source")})}}"undefined"!=typeof require&&"undefined"==typeof XRegExp&&(XRegExp=require("XRegExp").XRegExp);var J={defaults:{"class-name":"","first-line":1,"pad-line-numbers":!1,highlight:!1,title:null,"smart-tabs":!0,"tab-size":4,gutter:!0,toolbar:!0,"quick-code":!0,collapse:!1,"auto-links":!1,light:!1,unindent:!0,"html-script":!1},config:{space:"&nbsp;",useScriptTags:!0,bloggerMode:!1,stripBrs:!1,tagName:"pre",strings:{expandSource:"expand source",help:"?",alert:"SyntaxHighlighter\n\n",noBrush:"Can't find brush for: ",brushNotHtmlScript:"Brush wasn't configured for html-script option: ",aboutDialog:"@ABOUT@"}},vars:{discoveredBrushes:null,highlighters:{}},brushes:{},regexLib:{multiLineCComments:/\/\*[\s\S]*?\*\//gm,singleLineCComments:/\/\/.*$/gm,singleLinePerlComments:/#.*$/gm,doubleQuotedString:/"([^\\"\n]|\\.)*"/g,singleQuotedString:/'([^\\'\n]|\\.)*'/g,multiLineDoubleQuotedString:new XRegExp('"([^\\\\"]|\\\\.)*"',"gs"),multiLineSingleQuotedString:new XRegExp("'([^\\\\']|\\\\.)*'","gs"),xmlComments:/(&lt;|<)!--[\s\S]*?--(&gt;|>)/gm,url:/\w+:\/\/[\w-.\/?%&=:@;#]*/g,phpScriptTags:{left:/(&lt;|<)\?(?:=|php)?/g,right:/\?(&gt;|>)/g,eof:!0},aspScriptTags:{left:/(&lt;|<)%=?/g,right:/%(&gt;|>)/g},scriptScriptTags:{left:/(&lt;|<)\s*script.*?(&gt;|>)/gi,right:/(&lt;|<)\/\s*script\s*(&gt;|>)/gi}},toolbar:{getHtml:function(a){function b(a,b){return J.toolbar.getButtonHtml(a,b,J.config.strings[b])}for(var c='<div class="toolbar">',d=J.toolbar.items,e=d.list,f=0;f<e.length;f++)c+=(d[e[f]].getHtml||b)(a,e[f]);return c+="</div>"},getButtonHtml:function(a,b,c){return'<span><a href="#" class="toolbar_item command_'+b+" "+b+'">'+c+"</a></span>"},handler:function(a){function b(a){var b=new RegExp(a+"_(\\w+)"),c=b.exec(d);return c?c[1]:null}var c=a.target,d=c.className||"",e=g(k(c,".syntaxhighlighter").id),f=b("command");e&&f&&J.toolbar.items[f].execute(e),a.preventDefault()},items:{list:["expandSource","help"],expandSource:{getHtml:function(a){if(1!=a.getParam("collapse"))return"";var b=a.getParam("title");return J.toolbar.getButtonHtml(a,"expandSource",b?b:J.config.strings.expandSource)},execute:function(a){var b=h(a.id);c(b,"collapsed")}},help:{execute:function(a){var b=p("","_blank",500,250,"scrollbars=0"),c=b.document;c.write(J.config.strings.aboutDialog),c.close(),b.focus()}}}},findElements:function(a,b){var c=b?[b]:d(document.getElementsByTagName(J.config.tagName)),e=J.config,f=[];if(e.useScriptTags&&(c=c.concat(G())),0===c.length)return f;for(var g=0;g<c.length;g++){var h={target:c[g],params:n(a,v(c[g].className))};null!=h.params.brush&&f.push(h)}return f},highlight:function(a,b){var c=this.findElements(a,b),d="innerHTML",e=null,f=J.config;if(0!==c.length)for(var g=0;g<c.length;g++){var h,b=c[g],i=b.target,j=b.params,k=j.brush;if(null!=k){if("true"==j["html-script"]||1==J.defaults["html-script"])e=new J.HtmlScript(k),k="htmlscript";else{var l=s(k);if(!l)continue;e=new l}h=i[d],f.useScriptTags&&(h=H(h)),""!=(i.title||"")&&(j.title=i.title),j.brush=k,e.init(j),b=e.getDiv(h),""!=(i.id||"")&&(b.id=i.id);var m=b.firstChild.firstChild;m.className=b.firstChild.className,i.parentNode.replaceChild(m,i)}}},all:function(a){q(window,"load",function(){J.highlight(a)})}};return J.Match=function(a,b,c){this.value=a,this.index=b,this.length=a.length,this.css=c,this.brushName=null},J.Match.prototype.toString=function(){return this.value},J.HtmlScript=function(a){function b(a,b){for(var c=0;c<a.length;c++)a[c].index+=b}function c(a,c){for(var f,g=a.code,h=[],i=d.regexList,j=a.index+a.left.length,k=d.htmlScript,l=0;l<i.length;l++)f=E(g,i[l]),b(f,j),h=h.concat(f);null!=k.left&&null!=a.left&&(f=E(a.left,k.left),b(f,a.index),h=h.concat(f)),null!=k.right&&null!=a.right&&(f=E(a.right,k.right),b(f,a.index+a[0].lastIndexOf(a.right)),h=h.concat(f));for(var m=0;m<h.length;m++)h[m].brushName=e.brushName;return h}var d,e=s(a),f=new J.brushes.Xml,g=this,h="getDiv getHtml init".split(" ");if(null!=e){d=new e;for(var i=0;i<h.length;i++)(function(){var a=h[i];g[a]=function(){return f[a].apply(f,arguments)}})();return null==d.htmlScript?void r(J.config.strings.brushNotHtmlScript+a):void f.regexList.push({regex:d.htmlScript.code,func:c})}},J.Highlighter=function(){},J.Highlighter.prototype={getParam:function(a,b){var c=this.params[a];return o(null==c?b:c)},create:function(a){return document.createElement(a)},findMatches:function(a,b){var c=[];if(null!=a)for(var d=0;d<a.length;d++)"object"==typeof a[d]&&(c=c.concat(E(b,a[d])));return this.removeNestedMatches(c.sort(D))},removeNestedMatches:function(a){for(var b=0;b<a.length;b++)if(null!==a[b])for(var c=a[b],d=c.index+c.length,e=b+1;e<a.length&&null!==a[b];e++){var f=a[e];if(null!==f){if(f.index>d)break;f.index==c.index&&f.length>c.length?a[b]=null:f.index>=c.index&&f.index<d&&(a[e]=null)}}return a},figureOutLineNumbers:function(a){var b=[],c=parseInt(this.getParam("first-line"));return t(a,function(a,d){b.push(d+c)}),b},isLineHighlighted:function(a){var b=this.getParam("highlight",[]);return"object"!=typeof b&&null==b.push&&(b=[b]),l(b,a.toString())!=-1},getLineHtml:function(a,b,c){var d=["line","number"+b,"index"+a,"alt"+(b%2==0?1:2).toString()];return this.isLineHighlighted(b)&&d.push("highlighted"),0==b&&d.push("break"),'<div class="'+d.join(" ")+'">'+c+"</div>"},getLineNumbersHtml:function(a,b){var c="",d=e(a).length,f=parseInt(this.getParam("first-line")),g=this.getParam("pad-line-numbers");1==g?g=(f+d-1).toString().length:1==isNaN(g)&&(g=0);for(var h=0;h<d;h++){var i=b?b[h]:f+h,a=0==i?J.config.space:x(i,g);c+=this.getLineHtml(h,i,a)}return c},getCodeLinesHtml:function(a,b){a=B(a);for(var c=e(a),d=(this.getParam("pad-line-numbers"),parseInt(this.getParam("first-line"))),a="",f=this.getParam("brush"),g=0;g<c.length;g++){var h=c[g],i=/^(&nbsp;|\s)+/.exec(h),j=null,k=b?b[g]:d+g;null!=i&&(j=i[0].toString(),h=h.substr(j.length),j=j.replace(" ",J.config.space)),h=B(h),0==h.length&&(h=J.config.space),a+=this.getLineHtml(g,k,(null!=j?'<code class="'+f+' spaces">'+j+"</code>":"")+h)}return a},getTitleHtml:function(a){return a?"<caption>"+a+"</caption>":""},getMatchesHtml:function(a,b){function c(a){var b=a?a.brushName||f:f;return b?b+" ":""}for(var d=0,e="",f=this.getParam("brush",""),g=0;g<b.length;g++){var h,i=b[g];null!==i&&0!==i.length&&(h=c(i),e+=w(a.substr(d,i.index-d),h+"plain")+w(i.value,h+i.css),d=i.index+i.length+(i.offset||0))}return e+=w(a.substr(d),c()+"plain")},getHtml:function(a){var b,c,d,e="",g=["syntaxhighlighter"];return 1==this.getParam("light")&&(this.params.toolbar=this.params.gutter=!1),className="syntaxhighlighter",1==this.getParam("collapse")&&g.push("collapsed"),0==(gutter=this.getParam("gutter"))&&g.push("nogutter"),g.push(this.getParam("class-name")),g.push(this.getParam("brush")),a=u(a).replace(/\r/g," "),b=this.getParam("tab-size"),a=1==this.getParam("smart-tabs")?z(a,b):y(a,b),this.getParam("unindent")&&(a=C(a)),gutter&&(d=this.figureOutLineNumbers(a)),c=this.findMatches(this.regexList,a),e=this.getMatchesHtml(a,c),e=this.getCodeLinesHtml(e,d),this.getParam("auto-links")&&(e=F(e)),"undefined"!=typeof navigator&&navigator.userAgent&&navigator.userAgent.match(/MSIE/)&&g.push("ie"),e='<div id="'+f(this.id)+'" class="'+g.join(" ")+'">'+(this.getParam("toolbar")?J.toolbar.getHtml(this):"")+'<table border="0" cellpadding="0" cellspacing="0">'+this.getTitleHtml(this.getParam("title"))+"<tbody><tr>"+(gutter?'<td class="gutter">'+this.getLineNumbersHtml(a)+"</td>":"")+'<td class="code"><div class="container">'+e+"</div></td></tr></tbody></table></div>"},getDiv:function(a){null===a&&(a=""),this.code=a;var b=this.create("div");return b.innerHTML=this.getHtml(a),this.getParam("toolbar")&&q(j(b,".toolbar"),"click",J.toolbar.handler),this.getParam("quick-code")&&q(j(b,".code"),"dblclick",I),b},init:function(a){this.id=m(),i(this),this.params=n(J.defaults,a||{}),1==this.getParam("light")&&(this.params.toolbar=this.params.gutter=!1)},getKeywords:function(a){return a=a.replace(/^\s+|\s+$/g,"").replace(/\s+/g,"|"),"\\b(?:"+a+")\\b"},forHtmlScript:function(a){var b={end:a.right.source};a.eof&&(b.end="(?:(?:"+b.end+")|$)"),this.htmlScript={left:{regex:a.left,css:"script"},right:{regex:a.right,css:"script"},code:new XRegExp("(?<left>"+a.left.source+")(?<code>.*?)(?<right>"+b.end+")","sgi")}}},J}();"undefined"!=typeof exports?exports.SyntaxHighlighter=SyntaxHighlighter:null,function(){function a(){var a="class interface function package",b="-Infinity ...rest Array as AS3 Boolean break case catch const continue Date decodeURI decodeURIComponent default delete do dynamic each else encodeURI encodeURIComponent escape extends false final finally flash_proxy for get if implements import in include Infinity instanceof int internal is isFinite isNaN isXMLName label namespace NaN native new null Null Number Object object_proxy override parseFloat parseInt private protected public return set static String super switch this throw true try typeof uint undefined unescape use void while with";this.regexList=[{regex:SyntaxHighlighter.regexLib.singleLineCComments,css:"comments"},{regex:SyntaxHighlighter.regexLib.multiLineCComments,css:"comments"},{regex:SyntaxHighlighter.regexLib.doubleQuotedString,css:"string"},{regex:SyntaxHighlighter.regexLib.singleQuotedString,css:"string"},{regex:/\b([\d]+(\.[\d]+)?|0x[a-f0-9]+)\b/gi,css:"value"},{regex:new RegExp(this.getKeywords(a),"gm"),css:"color3"},{regex:new RegExp(this.getKeywords(b),"gm"),css:"keyword"},{regex:new RegExp("var","gm"),css:"variable"},{regex:new RegExp("trace","gm"),css:"color1"}],this.forHtmlScript(SyntaxHighlighter.regexLib.scriptScriptTags)}SyntaxHighlighter=SyntaxHighlighter||("undefined"!=typeof require?require("shCore").SyntaxHighlighter:null),a.prototype=new SyntaxHighlighter.Highlighter,a.aliases=["actionscript3","as3"],SyntaxHighlighter.brushes.AS3=a,"undefined"!=typeof exports?exports.Brush=a:null}(),function(){function a(){var a="after before beginning continue copy each end every from return get global in local named of set some that the then times to where whose with without",b="first second third fourth fifth sixth seventh eighth ninth tenth last front back middle",c="activate add alias AppleScript ask attachment boolean class constant delete duplicate empty exists false id integer list make message modal modified new no paragraph pi properties quit real record remove rest result reveal reverse run running save string true word yes";this.regexList=[{regex:/(--|#).*$/gm,css:"comments"},{regex:/\(\*(?:[\s\S]*?\(\*[\s\S]*?\*\))*[\s\S]*?\*\)/gm,css:"comments"},{regex:/"[\s\S]*?"/gm,css:"string"},{regex:/(?:,|:|¬|'s\b|\(|\)|\{|\}|«|\b\w*»)/g,css:"color1"},{regex:/(-)?(\d)+(\.(\d)?)?(E\+(\d)+)?/g,css:"color1"},{regex:/(?:&(amp;|gt;|lt;)?|=|� |>|<|≥|>=|≤|<=|\*|\+|-|\/|÷|\^)/g,css:"color2"},{regex:/\b(?:and|as|div|mod|not|or|return(?!\s&)(ing)?|equals|(is(n't| not)? )?equal( to)?|does(n't| not) equal|(is(n't| not)? )?(greater|less) than( or equal( to)?)?|(comes|does(n't| not) come) (after|before)|is(n't| not)?( in)? (back|front) of|is(n't| not)? behind|is(n't| not)?( (in|contained by))?|does(n't| not) contain|contain(s)?|(start|begin|end)(s)? with|((but|end) )?(consider|ignor)ing|prop(erty)?|(a )?ref(erence)?( to)?|repeat (until|while|with)|((end|exit) )?repeat|((else|end) )?if|else|(end )?(script|tell|try)|(on )?error|(put )?into|(of )?(it|me)|its|my|with (timeout( of)?|transaction)|end (timeout|transaction))\b/g,css:"keyword"},{regex:/\b\d+(st|nd|rd|th)\b/g,css:"keyword"},{regex:/\b(?:about|above|against|around|at|below|beneath|beside|between|by|(apart|aside) from|(instead|out) of|into|on(to)?|over|since|thr(ough|u)|under)\b/g,css:"color3"},{regex:/\b(?:adding folder items to|after receiving|choose( ((remote )?application|color|folder|from list|URL))?|clipboard info|set the clipboard to|(the )?clipboard|entire contents|display(ing| (alert|dialog|mode))?|document( (edited|file|nib name))?|file( (name|type))?|(info )?for|giving up after|(name )?extension|quoted form|return(ed)?|second(?! item)(s)?|list (disks|folder)|text item(s| delimiters)?|(Unicode )?text|(disk )?item(s)?|((current|list) )?view|((container|key) )?window|with (data|icon( (caution|note|stop))?|parameter(s)?|prompt|properties|seed|title)|case|diacriticals|hyphens|numeric strings|punctuation|white space|folder creation|application(s( folder)?| (processes|scripts position|support))?|((desktop )?(pictures )?|(documents|downloads|favorites|home|keychain|library|movies|music|public|scripts|sites|system|users|utilities|workflows) )folder|desktop|Folder Action scripts|font(s| panel)?|help|internet plugins|modem scripts|(system )?preferences|printer descriptions|scripting (additions|components)|shared (documents|libraries)|startup (disk|items)|temporary items|trash|on server|in AppleTalk zone|((as|long|short) )?user name|user (ID|locale)|(with )?password|in (bundle( with identifier)?|directory)|(close|open for) access|read|write( permission)?|(g|s)et eof|using( delimiters)?|starting at|default (answer|button|color|country code|entr(y|ies)|identifiers|items|name|location|script editor)|hidden( answer)?|open(ed| (location|untitled))?|error (handling|reporting)|(do( shell)?|load|run|store) script|administrator privileges|altering line endings|get volume settings|(alert|boot|input|mount|output|set) volume|output muted|(fax|random )?number|round(ing)?|up|down|toward zero|to nearest|as taught in school|system (attribute|info)|((AppleScript( Studio)?|system) )?version|(home )?directory|(IPv4|primary Ethernet) address|CPU (type|speed)|physical memory|time (stamp|to GMT)|replacing|ASCII (character|number)|localized string|from table|offset|summarize|beep|delay|say|(empty|multiple) selections allowed|(of|preferred) type|invisibles|showing( package contents)?|editable URL|(File|FTP|News|Media|Web) [Ss]ervers|Telnet hosts|Directory services|Remote applications|waiting until completion|saving( (in|to))?|path (for|to( (((current|frontmost) )?application|resource))?)|POSIX (file|path)|(background|RGB) color|(OK|cancel) button name|cancel button|button(s)?|cubic ((centi)?met(re|er)s|yards|feet|inches)|square ((kilo)?met(re|er)s|miles|yards|feet)|(centi|kilo)?met(re|er)s|miles|yards|feet|inches|lit(re|er)s|gallons|quarts|(kilo)?grams|ounces|pounds|degrees (Celsius|Fahrenheit|Kelvin)|print( (dialog|settings))?|clos(e(able)?|ing)|(de)?miniaturized|miniaturizable|zoom(ed|able)|attribute run|action (method|property|title)|phone|email|((start|end)ing|home) page|((birth|creation|current|custom|modification) )?date|((((phonetic )?(first|last|middle))|computer|host|maiden|related) |nick)?name|aim|icq|jabber|msn|yahoo|address(es)?|save addressbook|should enable action|city|country( code)?|formatte(r|d address)|(palette )?label|state|street|zip|AIM [Hh]andle(s)?|my card|select(ion| all)?|unsaved|(alpha )?value|entr(y|ies)|group|(ICQ|Jabber|MSN) handle|person|people|company|department|icon image|job title|note|organization|suffix|vcard|url|copies|collating|pages (across|down)|request print time|target( printer)?|((GUI Scripting|Script menu) )?enabled|show Computer scripts|(de)?activated|awake from nib|became (key|main)|call method|of (class|object)|center|clicked toolbar item|closed|for document|exposed|(can )?hide|idle|keyboard (down|up)|event( (number|type))?|launch(ed)?|load (image|movie|nib|sound)|owner|log|mouse (down|dragged|entered|exited|moved|up)|move|column|localization|resource|script|register|drag (info|types)|resigned (active|key|main)|resiz(e(d)?|able)|right mouse (down|dragged|up)|scroll wheel|(at )?index|should (close|open( untitled)?|quit( after last window closed)?|zoom)|((proposed|screen) )?bounds|show(n)?|behind|in front of|size (mode|to fit)|update(d| toolbar item)?|was (hidden|miniaturized)|will (become active|close|finish launching|hide|miniaturize|move|open|quit|(resign )?active|((maximum|minimum|proposed) )?size|show|zoom)|bundle|data source|movie|pasteboard|sound|tool(bar| tip)|(color|open|save) panel|coordinate system|frontmost|main( (bundle|menu|window))?|((services|(excluded from )?windows) )?menu|((executable|frameworks|resource|scripts|shared (frameworks|support)) )?path|(selected item )?identifier|data|content(s| view)?|character(s)?|click count|(command|control|option|shift) key down|context|delta (x|y|z)|key( code)?|location|pressure|unmodified characters|types|(first )?responder|playing|(allowed|selectable) identifiers|allows customization|(auto saves )?configuration|visible|image( name)?|menu form representation|tag|user(-| )defaults|associated file name|(auto|needs) display|current field editor|floating|has (resize indicator|shadow)|hides when deactivated|level|minimized (image|title)|opaque|position|release when closed|sheet|title(d)?)\b/g,css:"color3"},{regex:new RegExp(this.getKeywords(c),"gm"),css:"color3"},{regex:new RegExp(this.getKeywords(a),"gm"),css:"keyword"},{regex:new RegExp(this.getKeywords(b),"gm"),css:"keyword"}]}SyntaxHighlighter=SyntaxHighlighter||("undefined"!=typeof require?require("shCore").SyntaxHighlighter:null),a.prototype=new SyntaxHighlighter.Highlighter,a.aliases=["applescript"],SyntaxHighlighter.brushes.AppleScript=a,"undefined"!=typeof exports?exports.Brush=a:null}(),function(){function a(){var a="if fi then elif else for do done until while break continue case esac function return in eq ne ge le",b="alias apropos awk basename bash bc bg builtin bzip2 cal cat cd cfdisk chgrp chmod chown chrootcksum clear cmp comm command cp cron crontab csplit cut date dc dd ddrescue declare df diff diff3 dig dir dircolors dirname dirs du echo egrep eject enable env ethtool eval exec exit expand export expr false fdformat fdisk fg fgrep file find fmt fold format free fsck ftp gawk getopts grep groups gzip hash head history hostname id ifconfig import install join kill less let ln local locate logname logout look lpc lpr lprint lprintd lprintq lprm ls lsof make man mkdir mkfifo mkisofs mknod more mount mtools mv netstat nice nl nohup nslookup open op passwd paste pathchk ping popd pr printcap printenv printf ps pushd pwd quota quotacheck quotactl ram rcp read readonly renice remsync rm rmdir rsync screen scp sdiff sed select seq set sftp shift shopt shutdown sleep sort source split ssh strace su sudo sum symlink sync tail tar tee test time times touch top traceroute trap tr true tsort tty type ulimit umask umount unalias uname unexpand uniq units unset unshar useradd usermod users uuencode uudecode v vdir vi watch wc whereis which who whoami Wget xargs yes";this.regexList=[{regex:/^#!.*$/gm,css:"preprocessor bold"},{regex:/\/[\w-\/]+/gm,css:"plain"},{regex:SyntaxHighlighter.regexLib.singleLinePerlComments,css:"comments"},{regex:SyntaxHighlighter.regexLib.doubleQuotedString,css:"string"},{regex:SyntaxHighlighter.regexLib.singleQuotedString,css:"string"},{regex:new RegExp(this.getKeywords(a),"gm"),css:"keyword"},{regex:new RegExp(this.getKeywords(b),"gm"),css:"functions"}]}SyntaxHighlighter=SyntaxHighlighter||("undefined"!=typeof require?require("shCore").SyntaxHighlighter:null),a.prototype=new SyntaxHighlighter.Highlighter,a.aliases=["bash","shell","sh"],SyntaxHighlighter.brushes.Bash=a,"undefined"!=typeof exports?exports.Brush=a:null}(),function(){function a(){var a="Abs ACos AddSOAPRequestHeader AddSOAPResponseHeader AjaxLink AjaxOnLoad ArrayAppend ArrayAvg ArrayClear ArrayDeleteAt ArrayInsertAt ArrayIsDefined ArrayIsEmpty ArrayLen ArrayMax ArrayMin ArraySet ArraySort ArraySum ArraySwap ArrayToList Asc ASin Atn BinaryDecode BinaryEncode BitAnd BitMaskClear BitMaskRead BitMaskSet BitNot BitOr BitSHLN BitSHRN BitXor Ceiling CharsetDecode CharsetEncode Chr CJustify Compare CompareNoCase Cos CreateDate CreateDateTime CreateObject CreateODBCDate CreateODBCDateTime CreateODBCTime CreateTime CreateTimeSpan CreateUUID DateAdd DateCompare DateConvert DateDiff DateFormat DatePart Day DayOfWeek DayOfWeekAsString DayOfYear DaysInMonth DaysInYear DE DecimalFormat DecrementValue Decrypt DecryptBinary DeleteClientVariable DeserializeJSON DirectoryExists DollarFormat DotNetToCFType Duplicate Encrypt EncryptBinary Evaluate Exp ExpandPath FileClose FileCopy FileDelete FileExists FileIsEOF FileMove FileOpen FileRead FileReadBinary FileReadLine FileSetAccessMode FileSetAttribute FileSetLastModified FileWrite Find FindNoCase FindOneOf FirstDayOfMonth Fix FormatBaseN GenerateSecretKey GetAuthUser GetBaseTagData GetBaseTagList GetBaseTemplatePath GetClientVariablesList GetComponentMetaData GetContextRoot GetCurrentTemplatePath GetDirectoryFromPath GetEncoding GetException GetFileFromPath GetFileInfo GetFunctionList GetGatewayHelper GetHttpRequestData GetHttpTimeString GetK2ServerDocCount GetK2ServerDocCountLimit GetLocale GetLocaleDisplayName GetLocalHostIP GetMetaData GetMetricData GetPageContext GetPrinterInfo GetProfileSections GetProfileString GetReadableImageFormats GetSOAPRequest GetSOAPRequestHeader GetSOAPResponse GetSOAPResponseHeader GetTempDirectory GetTempFile GetTemplatePath GetTickCount GetTimeZoneInfo GetToken GetUserRoles GetWriteableImageFormats Hash Hour HTMLCodeFormat HTMLEditFormat IIf ImageAddBorder ImageBlur ImageClearRect ImageCopy ImageCrop ImageDrawArc ImageDrawBeveledRect ImageDrawCubicCurve ImageDrawLine ImageDrawLines ImageDrawOval ImageDrawPoint ImageDrawQuadraticCurve ImageDrawRect ImageDrawRoundRect ImageDrawText ImageFlip ImageGetBlob ImageGetBufferedImage ImageGetEXIFTag ImageGetHeight ImageGetIPTCTag ImageGetWidth ImageGrayscale ImageInfo ImageNegative ImageNew ImageOverlay ImagePaste ImageRead ImageReadBase64 ImageResize ImageRotate ImageRotateDrawingAxis ImageScaleToFit ImageSetAntialiasing ImageSetBackgroundColor ImageSetDrawingColor ImageSetDrawingStroke ImageSetDrawingTransparency ImageSharpen ImageShear ImageShearDrawingAxis ImageTranslate ImageTranslateDrawingAxis ImageWrite ImageWriteBase64 ImageXORDrawingMode IncrementValue InputBaseN Insert Int IsArray IsBinary IsBoolean IsCustomFunction IsDate IsDDX IsDebugMode IsDefined IsImage IsImageFile IsInstanceOf IsJSON IsLeapYear IsLocalHost IsNumeric IsNumericDate IsObject IsPDFFile IsPDFObject IsQuery IsSimpleValue IsSOAPRequest IsStruct IsUserInAnyRole IsUserInRole IsUserLoggedIn IsValid IsWDDX IsXML IsXmlAttribute IsXmlDoc IsXmlElem IsXmlNode IsXmlRoot JavaCast JSStringFormat LCase Left Len ListAppend ListChangeDelims ListContains ListContainsNoCase ListDeleteAt ListFind ListFindNoCase ListFirst ListGetAt ListInsertAt ListLast ListLen ListPrepend ListQualify ListRest ListSetAt ListSort ListToArray ListValueCount ListValueCountNoCase LJustify Log Log10 LSCurrencyFormat LSDateFormat LSEuroCurrencyFormat LSIsCurrency LSIsDate LSIsNumeric LSNumberFormat LSParseCurrency LSParseDateTime LSParseEuroCurrency LSParseNumber LSTimeFormat LTrim Max Mid Min Minute Month MonthAsString Now NumberFormat ParagraphFormat ParseDateTime Pi PrecisionEvaluate PreserveSingleQuotes Quarter QueryAddColumn QueryAddRow QueryConvertForGrid QueryNew QuerySetCell QuotedValueList Rand Randomize RandRange REFind REFindNoCase ReleaseComObject REMatch REMatchNoCase RemoveChars RepeatString Replace ReplaceList ReplaceNoCase REReplace REReplaceNoCase Reverse Right RJustify Round RTrim Second SendGatewayMessage SerializeJSON SetEncoding SetLocale SetProfileString SetVariable Sgn Sin Sleep SpanExcluding SpanIncluding Sqr StripCR StructAppend StructClear StructCopy StructCount StructDelete StructFind StructFindKey StructFindValue StructGet StructInsert StructIsEmpty StructKeyArray StructKeyExists StructKeyList StructKeyList StructNew StructSort StructUpdate Tan TimeFormat ToBase64 ToBinary ToScript ToString Trim UCase URLDecode URLEncodedFormat URLSessionFormat Val ValueList VerifyClient Week Wrap Wrap WriteOutput XmlChildPos XmlElemNew XmlFormat XmlGetNodeType XmlNew XmlParse XmlSearch XmlTransform XmlValidate Year YesNoFormat",b="cfabort cfajaximport cfajaxproxy cfapplet cfapplication cfargument cfassociate cfbreak cfcache cfcalendar cfcase cfcatch cfchart cfchartdata cfchartseries cfcol cfcollection cfcomponent cfcontent cfcookie cfdbinfo cfdefaultcase cfdirectory cfdiv cfdocument cfdocumentitem cfdocumentsection cfdump cfelse cfelseif cferror cfexchangecalendar cfexchangeconnection cfexchangecontact cfexchangefilter cfexchangemail cfexchangetask cfexecute cfexit cffeed cffile cfflush cfform cfformgroup cfformitem cfftp cffunction cfgrid cfgridcolumn cfgridrow cfgridupdate cfheader cfhtmlhead cfhttp cfhttpparam cfif cfimage cfimport cfinclude cfindex cfinput cfinsert cfinterface cfinvoke cfinvokeargument cflayout cflayoutarea cfldap cflocation cflock cflog cflogin cfloginuser cflogout cfloop cfmail cfmailparam cfmailpart cfmenu cfmenuitem cfmodule cfNTauthenticate cfobject cfobjectcache cfoutput cfparam cfpdf cfpdfform cfpdfformparam cfpdfparam cfpdfsubform cfpod cfpop cfpresentation cfpresentationslide cfpresenter cfprint cfprocessingdirective cfprocparam cfprocresult cfproperty cfquery cfqueryparam cfregistry cfreport cfreportparam cfrethrow cfreturn cfsavecontent cfschedule cfscript cfsearch cfselect cfset cfsetting cfsilent cfslider cfsprydataset cfstoredproc cfswitch cftable cftextarea cfthread cfthrow cftimer cftooltip cftrace cftransaction cftree cftreeitem cftry cfupdate cfwddx cfwindow cfxml cfzip cfzipparam",c="all and any between cross in join like not null or outer some";
this.regexList=[{regex:new RegExp("--(.*)$","gm"),css:"comments"},{regex:SyntaxHighlighter.regexLib.xmlComments,css:"comments"},{regex:SyntaxHighlighter.regexLib.doubleQuotedString,css:"string"},{regex:SyntaxHighlighter.regexLib.singleQuotedString,css:"string"},{regex:new RegExp(this.getKeywords(a),"gmi"),css:"functions"},{regex:new RegExp(this.getKeywords(c),"gmi"),css:"color1"},{regex:new RegExp(this.getKeywords(b),"gmi"),css:"keyword"}]}SyntaxHighlighter=SyntaxHighlighter||("undefined"!=typeof require?require("shCore").SyntaxHighlighter:null),a.prototype=new SyntaxHighlighter.Highlighter,a.aliases=["coldfusion","cf"],SyntaxHighlighter.brushes.ColdFusion=a,"undefined"!=typeof exports?exports.Brush=a:null}(),function(){function a(){var a="ATOM BOOL BOOLEAN BYTE CHAR COLORREF DWORD DWORDLONG DWORD_PTR DWORD32 DWORD64 FLOAT HACCEL HALF_PTR HANDLE HBITMAP HBRUSH HCOLORSPACE HCONV HCONVLIST HCURSOR HDC HDDEDATA HDESK HDROP HDWP HENHMETAFILE HFILE HFONT HGDIOBJ HGLOBAL HHOOK HICON HINSTANCE HKEY HKL HLOCAL HMENU HMETAFILE HMODULE HMONITOR HPALETTE HPEN HRESULT HRGN HRSRC HSZ HWINSTA HWND INT INT_PTR INT32 INT64 LANGID LCID LCTYPE LGRPID LONG LONGLONG LONG_PTR LONG32 LONG64 LPARAM LPBOOL LPBYTE LPCOLORREF LPCSTR LPCTSTR LPCVOID LPCWSTR LPDWORD LPHANDLE LPINT LPLONG LPSTR LPTSTR LPVOID LPWORD LPWSTR LRESULT PBOOL PBOOLEAN PBYTE PCHAR PCSTR PCTSTR PCWSTR PDWORDLONG PDWORD_PTR PDWORD32 PDWORD64 PFLOAT PHALF_PTR PHANDLE PHKEY PINT PINT_PTR PINT32 PINT64 PLCID PLONG PLONGLONG PLONG_PTR PLONG32 PLONG64 POINTER_32 POINTER_64 PSHORT PSIZE_T PSSIZE_T PSTR PTBYTE PTCHAR PTSTR PUCHAR PUHALF_PTR PUINT PUINT_PTR PUINT32 PUINT64 PULONG PULONGLONG PULONG_PTR PULONG32 PULONG64 PUSHORT PVOID PWCHAR PWORD PWSTR SC_HANDLE SC_LOCK SERVICE_STATUS_HANDLE SHORT SIZE_T SSIZE_T TBYTE TCHAR UCHAR UHALF_PTR UINT UINT_PTR UINT32 UINT64 ULONG ULONGLONG ULONG_PTR ULONG32 ULONG64 USHORT USN VOID WCHAR WORD WPARAM WPARAM WPARAM char bool short int __int32 __int64 __int8 __int16 long float double __wchar_t clock_t _complex _dev_t _diskfree_t div_t ldiv_t _exception _EXCEPTION_POINTERS FILE _finddata_t _finddatai64_t _wfinddata_t _wfinddatai64_t __finddata64_t __wfinddata64_t _FPIEEE_RECORD fpos_t _HEAPINFO _HFILE lconv intptr_t jmp_buf mbstate_t _off_t _onexit_t _PNH ptrdiff_t _purecall_handler sig_atomic_t size_t _stat __stat64 _stati64 terminate_function time_t __time64_t _timeb __timeb64 tm uintptr_t _utimbuf va_list wchar_t wctrans_t wctype_t wint_t signed",b="auto break case catch class const decltype __finally __exception __try const_cast continue private public protected __declspec default delete deprecated dllexport dllimport do dynamic_cast else enum explicit extern if for friend goto inline mutable naked namespace new noinline noreturn nothrow register reinterpret_cast return selectany sizeof static static_cast struct switch template this thread throw true false try typedef typeid typename union using uuid virtual void volatile whcar_t while",c="assert isalnum isalpha iscntrl isdigit isgraph islower isprintispunct isspace isupper isxdigit tolower toupper errno localeconv setlocale acos asin atan atan2 ceil cos cosh exp fabs floor fmod frexp ldexp log log10 modf pow sin sinh sqrt tan tanh jmp_buf longjmp setjmp raise signal sig_atomic_t va_arg va_end va_start clearerr fclose feof ferror fflush fgetc fgetpos fgets fopen fprintf fputc fputs fread freopen fscanf fseek fsetpos ftell fwrite getc getchar gets perror printf putc putchar puts remove rename rewind scanf setbuf setvbuf sprintf sscanf tmpfile tmpnam ungetc vfprintf vprintf vsprintf abort abs atexit atof atoi atol bsearch calloc div exit free getenv labs ldiv malloc mblen mbstowcs mbtowc qsort rand realloc srand strtod strtol strtoul system wcstombs wctomb memchr memcmp memcpy memmove memset strcat strchr strcmp strcoll strcpy strcspn strerror strlen strncat strncmp strncpy strpbrk strrchr strspn strstr strtok strxfrm asctime clock ctime difftime gmtime localtime mktime strftime time";this.regexList=[{regex:SyntaxHighlighter.regexLib.singleLineCComments,css:"comments"},{regex:SyntaxHighlighter.regexLib.multiLineCComments,css:"comments"},{regex:SyntaxHighlighter.regexLib.doubleQuotedString,css:"string"},{regex:SyntaxHighlighter.regexLib.singleQuotedString,css:"string"},{regex:/^ *#.*/gm,css:"preprocessor"},{regex:new RegExp(this.getKeywords(a),"gm"),css:"color1 bold"},{regex:new RegExp(this.getKeywords(c),"gm"),css:"functions bold"},{regex:new RegExp(this.getKeywords(b),"gm"),css:"keyword bold"}]}SyntaxHighlighter=SyntaxHighlighter||("undefined"!=typeof require?require("shCore").SyntaxHighlighter:null),a.prototype=new SyntaxHighlighter.Highlighter,a.aliases=["cpp","c"],SyntaxHighlighter.brushes.Cpp=a,"undefined"!=typeof exports?exports.Brush=a:null}(),function(){function a(){function a(a,b){var c=0==a[0].indexOf("///")?"color1":"comments";return[new SyntaxHighlighter.Match(a[0],a.index,c)]}var b="abstract as base bool break byte case catch char checked class const continue decimal default delegate do double else enum event explicit volatile extern false finally fixed float for foreach get goto if implicit in int interface internal is lock long namespace new null object operator out override params private protected public readonly ref return sbyte sealed set short sizeof stackalloc static string struct switch this throw true try typeof uint ulong unchecked unsafe ushort using virtual void while var from group by into select let where orderby join on equals ascending descending";this.regexList=[{regex:SyntaxHighlighter.regexLib.singleLineCComments,func:a},{regex:SyntaxHighlighter.regexLib.multiLineCComments,css:"comments"},{regex:/@"(?:[^"]|"")*"/g,css:"string"},{regex:SyntaxHighlighter.regexLib.doubleQuotedString,css:"string"},{regex:SyntaxHighlighter.regexLib.singleQuotedString,css:"string"},{regex:/^\s*#.*/gm,css:"preprocessor"},{regex:new RegExp(this.getKeywords(b),"gm"),css:"keyword"},{regex:/\bpartial(?=\s+(?:class|interface|struct)\b)/g,css:"keyword"},{regex:/\byield(?=\s+(?:return|break)\b)/g,css:"keyword"}],this.forHtmlScript(SyntaxHighlighter.regexLib.aspScriptTags)}SyntaxHighlighter=SyntaxHighlighter||("undefined"!=typeof require?require("shCore").SyntaxHighlighter:null),a.prototype=new SyntaxHighlighter.Highlighter,a.aliases=["c#","c-sharp","csharp"],SyntaxHighlighter.brushes.CSharp=a,"undefined"!=typeof exports?exports.Brush=a:null}(),function(){function a(){function a(a){return"\\b([a-z_]|)"+a.replace(/ /g,"(?=:)\\b|\\b([a-z_\\*]|\\*|)")+"(?=:)\\b"}function b(a){return"\\b"+a.replace(/ /g,"(?!-)(?!:)\\b|\\b()")+":\\b"}var c="ascent azimuth background-attachment background-color background-image background-position background-repeat background baseline bbox border-collapse border-color border-spacing border-style border-top border-right border-bottom border-left border-top-color border-right-color border-bottom-color border-left-color border-top-style border-right-style border-bottom-style border-left-style border-top-width border-right-width border-bottom-width border-left-width border-width border bottom cap-height caption-side centerline clear clip color content counter-increment counter-reset cue-after cue-before cue cursor definition-src descent direction display elevation empty-cells float font-size-adjust font-family font-size font-stretch font-style font-variant font-weight font height left letter-spacing line-height list-style-image list-style-position list-style-type list-style margin-top margin-right margin-bottom margin-left margin marker-offset marks mathline max-height max-width min-height min-width orphans outline-color outline-style outline-width outline overflow padding-top padding-right padding-bottom padding-left padding page page-break-after page-break-before page-break-inside pause pause-after pause-before pitch pitch-range play-during position quotes right richness size slope src speak-header speak-numeral speak-punctuation speak speech-rate stemh stemv stress table-layout text-align top text-decoration text-indent text-shadow text-transform unicode-bidi unicode-range units-per-em vertical-align visibility voice-family volume white-space widows width widths word-spacing x-height z-index",d="above absolute all always aqua armenian attr aural auto avoid baseline behind below bidi-override black blink block blue bold bolder both bottom braille capitalize caption center center-left center-right circle close-quote code collapse compact condensed continuous counter counters crop cross crosshair cursive dashed decimal decimal-leading-zero default digits disc dotted double embed embossed e-resize expanded extra-condensed extra-expanded fantasy far-left far-right fast faster fixed format fuchsia gray green groove handheld hebrew help hidden hide high higher icon inline-table inline inset inside invert italic justify landscape large larger left-side left leftwards level lighter lime line-through list-item local loud lower-alpha lowercase lower-greek lower-latin lower-roman lower low ltr marker maroon medium message-box middle mix move narrower navy ne-resize no-close-quote none no-open-quote no-repeat normal nowrap n-resize nw-resize oblique olive once open-quote outset outside overline pointer portrait pre print projection purple red relative repeat repeat-x repeat-y rgb ridge right right-side rightwards rtl run-in screen scroll semi-condensed semi-expanded separate se-resize show silent silver slower slow small small-caps small-caption smaller soft solid speech spell-out square s-resize static status-bar sub super sw-resize table-caption table-cell table-column table-column-group table-footer-group table-header-group table-row table-row-group teal text-bottom text-top thick thin top transparent tty tv ultra-condensed ultra-expanded underline upper-alpha uppercase upper-latin upper-roman url visible wait white wider w-resize x-fast x-high x-large x-loud x-low x-slow x-small x-soft xx-large xx-small yellow",e="[mM]onospace [tT]ahoma [vV]erdana [aA]rial [hH]elvetica [sS]ans-serif [sS]erif [cC]ourier mono sans serif";this.regexList=[{regex:SyntaxHighlighter.regexLib.multiLineCComments,css:"comments"},{regex:SyntaxHighlighter.regexLib.doubleQuotedString,css:"string"},{regex:SyntaxHighlighter.regexLib.singleQuotedString,css:"string"},{regex:/\#[a-fA-F0-9]{3,6}/g,css:"value"},{regex:/(-?\d+)(\.\d+)?(px|em|pt|\:|\%|)/g,css:"value"},{regex:/!important/g,css:"color3"},{regex:new RegExp(a(c),"gm"),css:"keyword"},{regex:new RegExp(b(d),"g"),css:"value"},{regex:new RegExp(this.getKeywords(e),"g"),css:"color1"}],this.forHtmlScript({left:/(&lt;|<)\s*style.*?(&gt;|>)/gi,right:/(&lt;|<)\/\s*style\s*(&gt;|>)/gi})}SyntaxHighlighter=SyntaxHighlighter||("undefined"!=typeof require?require("shCore").SyntaxHighlighter:null),a.prototype=new SyntaxHighlighter.Highlighter,a.aliases=["css"],SyntaxHighlighter.brushes.CSS=a,"undefined"!=typeof exports?exports.Brush=a:null}(),function(){function a(){var a="abs addr and ansichar ansistring array as asm begin boolean byte cardinal case char class comp const constructor currency destructor div do double downto else end except exports extended false file finalization finally for function goto if implementation in inherited int64 initialization integer interface is label library longint longword mod nil not object of on or packed pansichar pansistring pchar pcurrency pdatetime pextended pint64 pointer private procedure program property pshortstring pstring pvariant pwidechar pwidestring protected public published raise real real48 record repeat set shl shortint shortstring shr single smallint string then threadvar to true try type unit until uses val var varirnt while widechar widestring with word write writeln xor";this.regexList=[{regex:/\(\*[\s\S]*?\*\)/gm,css:"comments"},{regex:/{(?!\$)[\s\S]*?}/gm,css:"comments"},{regex:SyntaxHighlighter.regexLib.singleLineCComments,css:"comments"},{regex:SyntaxHighlighter.regexLib.singleQuotedString,css:"string"},{regex:/\{\$[a-zA-Z]+ .+\}/g,css:"color1"},{regex:/\b[\d\.]+\b/g,css:"value"},{regex:/\$[a-zA-Z0-9]+\b/g,css:"value"},{regex:new RegExp(this.getKeywords(a),"gmi"),css:"keyword"}]}SyntaxHighlighter=SyntaxHighlighter||("undefined"!=typeof require?require("shCore").SyntaxHighlighter:null),a.prototype=new SyntaxHighlighter.Highlighter,a.aliases=["delphi","pascal","pas"],SyntaxHighlighter.brushes.Delphi=a,"undefined"!=typeof exports?exports.Brush=a:null}(),function(){function a(){this.regexList=[{regex:/^\+\+\+ .*$/gm,css:"color2"},{regex:/^\-\-\- .*$/gm,css:"color2"},{regex:/^\s.*$/gm,css:"color1"},{regex:/^@@.*@@.*$/gm,css:"variable"},{regex:/^\+.*$/gm,css:"string"},{regex:/^\-.*$/gm,css:"color3"}]}SyntaxHighlighter=SyntaxHighlighter||("undefined"!=typeof require?require("shCore").SyntaxHighlighter:null),a.prototype=new SyntaxHighlighter.Highlighter,a.aliases=["diff","patch"],SyntaxHighlighter.brushes.Diff=a,"undefined"!=typeof exports?exports.Brush=a:null}(),function(){function a(){var a="after and andalso band begin bnot bor bsl bsr bxor case catch cond div end fun if let not of or orelse query receive rem try when xor module export import define";this.regexList=[{regex:new RegExp("[A-Z][A-Za-z0-9_]+","g"),css:"constants"},{regex:new RegExp("\\%.+","gm"),css:"comments"},{regex:new RegExp("\\?[A-Za-z0-9_]+","g"),css:"preprocessor"},{regex:new RegExp("[a-z0-9_]+:[a-z0-9_]+","g"),css:"functions"},{regex:SyntaxHighlighter.regexLib.doubleQuotedString,css:"string"},{regex:SyntaxHighlighter.regexLib.singleQuotedString,css:"string"},{regex:new RegExp(this.getKeywords(a),"gm"),css:"keyword"}]}SyntaxHighlighter=SyntaxHighlighter||("undefined"!=typeof require?require("shCore").SyntaxHighlighter:null),a.prototype=new SyntaxHighlighter.Highlighter,a.aliases=["erl","erlang"],SyntaxHighlighter.brushes.Erland=a,"undefined"!=typeof exports?exports.Brush=a:null}(),function(){function a(){var a="as assert break case catch class continue def default do else extends finally if in implements import instanceof interface new package property return switch throw throws try while public protected private static",b="void boolean byte char short int long float double",c="null",d="allProperties count get size collect each eachProperty eachPropertyName eachWithIndex find findAll findIndexOf grep inject max min reverseEach sort asImmutable asSynchronized flatten intersect join pop reverse subMap toList padRight padLeft contains eachMatch toCharacter toLong toUrl tokenize eachFile eachFileRecurse eachB yte eachLine readBytes readLine getText splitEachLine withReader append encodeBase64 decodeBase64 filterLine transformChar transformLine withOutputStream withPrintWriter withStream withStreams withWriter withWriterAppend write writeLine dump inspect invokeMethod print println step times upto use waitForOrKill getText";this.regexList=[{regex:SyntaxHighlighter.regexLib.singleLineCComments,css:"comments"},{regex:SyntaxHighlighter.regexLib.multiLineCComments,css:"comments"},{regex:SyntaxHighlighter.regexLib.doubleQuotedString,css:"string"},{regex:SyntaxHighlighter.regexLib.singleQuotedString,css:"string"},{regex:/""".*"""/g,css:"string"},{regex:new RegExp("\\b([\\d]+(\\.[\\d]+)?|0x[a-f0-9]+)\\b","gi"),css:"value"},{regex:new RegExp(this.getKeywords(a),"gm"),css:"keyword"},{regex:new RegExp(this.getKeywords(b),"gm"),css:"color1"},{regex:new RegExp(this.getKeywords(c),"gm"),css:"constants"},{regex:new RegExp(this.getKeywords(d),"gm"),css:"functions"}],this.forHtmlScript(SyntaxHighlighter.regexLib.aspScriptTags)}SyntaxHighlighter=SyntaxHighlighter||("undefined"!=typeof require?require("shCore").SyntaxHighlighter:null),a.prototype=new SyntaxHighlighter.Highlighter,a.aliases=["groovy"],SyntaxHighlighter.brushes.Groovy=a,"undefined"!=typeof exports?exports.Brush=a:null}(),function(){function a(){var a="abstract assert boolean break byte case catch char class const continue default do double else enum extends false final finally float for goto if implements import instanceof int interface long native new null package private protected public return short static strictfp super switch synchronized this throw throws true transient try void volatile while";this.regexList=[{regex:SyntaxHighlighter.regexLib.singleLineCComments,css:"comments"},{regex:/\/\*([^\*][\s\S]*)?\*\//gm,css:"comments"},{regex:/\/\*(?!\*\/)\*[\s\S]*?\*\//gm,css:"preprocessor"},{regex:SyntaxHighlighter.regexLib.doubleQuotedString,css:"string"},{regex:SyntaxHighlighter.regexLib.singleQuotedString,css:"string"},{regex:/\b([\d]+(\.[\d]+)?|0x[a-f0-9]+)\b/gi,css:"value"},{regex:/(?!\@interface\b)\@[\$\w]+\b/g,css:"color1"},{regex:/\@interface\b/g,css:"color2"},{regex:new RegExp(this.getKeywords(a),"gm"),css:"keyword"}],this.forHtmlScript({left:/(&lt;|<)%[@!=]?/g,right:/%(&gt;|>)/g})}SyntaxHighlighter=SyntaxHighlighter||("undefined"!=typeof require?require("shCore").SyntaxHighlighter:null),a.prototype=new SyntaxHighlighter.Highlighter,a.aliases=["java"],SyntaxHighlighter.brushes.Java=a,"undefined"!=typeof exports?exports.Brush=a:null}(),function(){function a(){var a="Boolean Byte Character Double Duration Float Integer Long Number Short String Void",b="abstract after and as assert at before bind bound break catch class continue def delete else exclusive extends false finally first for from function if import in indexof init insert instanceof into inverse last lazy mixin mod nativearray new not null on or override package postinit protected public public-init public-read replace return reverse sizeof step super then this throw true try tween typeof var where while with attribute let private readonly static trigger";this.regexList=[{regex:SyntaxHighlighter.regexLib.singleLineCComments,css:"comments"},{regex:SyntaxHighlighter.regexLib.multiLineCComments,css:"comments"},{regex:SyntaxHighlighter.regexLib.singleQuotedString,css:"string"},{regex:SyntaxHighlighter.regexLib.doubleQuotedString,css:"string"},{regex:/(-?\.?)(\b(\d*\.?\d+|\d+\.?\d*)(e[+-]?\d+)?|0x[a-f\d]+)\b\.?/gi,css:"color2"},{regex:new RegExp(this.getKeywords(a),"gm"),css:"variable"},{regex:new RegExp(this.getKeywords(b),"gm"),css:"keyword"}],this.forHtmlScript(SyntaxHighlighter.regexLib.aspScriptTags)}SyntaxHighlighter=SyntaxHighlighter||("undefined"!=typeof require?require("shCore").SyntaxHighlighter:null),a.prototype=new SyntaxHighlighter.Highlighter,a.aliases=["jfx","javafx"],SyntaxHighlighter.brushes.JavaFX=a,"undefined"!=typeof exports?exports.Brush=a:null}(),function(){function a(){var a="break case catch continue default delete do else false  for function if in instanceof new null return super switch this throw true try typeof var while with",b=SyntaxHighlighter.regexLib;this.regexList=[{regex:b.multiLineDoubleQuotedString,css:"string"},{regex:b.multiLineSingleQuotedString,css:"string"},{regex:b.singleLineCComments,css:"comments"},{regex:b.multiLineCComments,css:"comments"},{regex:/\s*#.*/gm,css:"preprocessor"},{regex:new RegExp(this.getKeywords(a),"gm"),css:"keyword"}],this.forHtmlScript(b.scriptScriptTags)}SyntaxHighlighter=SyntaxHighlighter||("undefined"!=typeof require?require("shCore").SyntaxHighlighter:null),a.prototype=new SyntaxHighlighter.Highlighter,a.aliases=["js","jscript","javascript"],SyntaxHighlighter.brushes.JScript=a,"undefined"!=typeof exports?exports.Brush=a:null}(),function(){function a(){var a="abs accept alarm atan2 bind binmode chdir chmod chomp chop chown chr chroot close closedir connect cos crypt defined delete each endgrent endhostent endnetent endprotoent endpwent endservent eof exec exists exp fcntl fileno flock fork format formline getc getgrent getgrgid getgrnam gethostbyaddr gethostbyname gethostent getlogin getnetbyaddr getnetbyname getnetent getpeername getpgrp getppid getpriority getprotobyname getprotobynumber getprotoent getpwent getpwnam getpwuid getservbyname getservbyport getservent getsockname getsockopt glob gmtime grep hex index int ioctl join keys kill lc lcfirst length link listen localtime lock log lstat map mkdir msgctl msgget msgrcv msgsnd oct open opendir ord pack pipe pop pos print printf prototype push quotemeta rand read readdir readline readlink readpipe recv rename reset reverse rewinddir rindex rmdir scalar seek seekdir select semctl semget semop send setgrent sethostent setnetent setpgrp setpriority setprotoent setpwent setservent setsockopt shift shmctl shmget shmread shmwrite shutdown sin sleep socket socketpair sort splice split sprintf sqrt srand stat study substr symlink syscall sysopen sysread sysseek system syswrite tell telldir time times tr truncate uc ucfirst umask undef unlink unpack unshift utime values vec wait waitpid warn write say",b="bless caller continue dbmclose dbmopen die do dump else elsif eval exit for foreach goto if import last local my next no our package redo ref require return sub tie tied unless untie until use wantarray while given when default try catch finally has extends with before after around override augment";this.regexList=[{regex:/(<<|&lt;&lt;)((\w+)|(['"])(.+?)\4)[\s\S]+?\n\3\5\n/g,css:"string"},{regex:/#.*$/gm,css:"comments"},{regex:/^#!.*\n/g,css:"preprocessor"},{regex:/-?\w+(?=\s*=(>|&gt;))/g,css:"string"},{regex:/\bq[qwxr]?\([\s\S]*?\)/g,css:"string"},{regex:/\bq[qwxr]?\{[\s\S]*?\}/g,css:"string"},{regex:/\bq[qwxr]?\[[\s\S]*?\]/g,css:"string"},{regex:/\bq[qwxr]?(<|&lt;)[\s\S]*?(>|&gt;)/g,css:"string"},{regex:/\bq[qwxr]?([^\w({<[])[\s\S]*?\1/g,css:"string"},{regex:SyntaxHighlighter.regexLib.doubleQuotedString,css:"string"},{regex:SyntaxHighlighter.regexLib.singleQuotedString,css:"string"},{regex:/(?:&amp;|[$@%*]|\$#)[a-zA-Z_](\w+|::)*/g,css:"variable"},{regex:/\b__(?:END|DATA)__\b[\s\S]*$/g,css:"comments"},{regex:/(^|\n)=\w[\s\S]*?(\n=cut\s*\n|$)/g,css:"comments"},{regex:new RegExp(this.getKeywords(a),"gm"),css:"functions"},{regex:new RegExp(this.getKeywords(b),"gm"),css:"keyword"}],this.forHtmlScript(SyntaxHighlighter.regexLib.phpScriptTags)}SyntaxHighlighter=SyntaxHighlighter||("undefined"!=typeof require?require("shCore").SyntaxHighlighter:null),a.prototype=new SyntaxHighlighter.Highlighter,a.aliases=["perl","Perl","pl"],SyntaxHighlighter.brushes.Perl=a,"undefined"!=typeof exports?exports.Brush=a:null}(),function(){function a(){var a="abs acos acosh addcslashes addslashes array_change_key_case array_chunk array_combine array_count_values array_diff array_diff_assoc array_diff_key array_diff_uassoc array_diff_ukey array_fill array_filter array_flip array_intersect array_intersect_assoc array_intersect_key array_intersect_uassoc array_intersect_ukey array_key_exists array_keys array_map array_merge array_merge_recursive array_multisort array_pad array_pop array_product array_push array_rand array_reduce array_reverse array_search array_shift array_slice array_splice array_sum array_udiff array_udiff_assoc array_udiff_uassoc array_uintersect array_uintersect_assoc array_uintersect_uassoc array_unique array_unshift array_values array_walk array_walk_recursive atan atan2 atanh base64_decode base64_encode base_convert basename bcadd bccomp bcdiv bcmod bcmul bindec bindtextdomain bzclose bzcompress bzdecompress bzerrno bzerror bzerrstr bzflush bzopen bzread bzwrite ceil chdir checkdate checkdnsrr chgrp chmod chop chown chr chroot chunk_split class_exists closedir closelog copy cos cosh count count_chars date decbin dechex decoct deg2rad delete ebcdic2ascii echo empty end ereg ereg_replace eregi eregi_replace error_log error_reporting escapeshellarg escapeshellcmd eval exec exit exp explode extension_loaded feof fflush fgetc fgetcsv fgets fgetss file_exists file_get_contents file_put_contents fileatime filectime filegroup fileinode filemtime fileowner fileperms filesize filetype floatval flock floor flush fmod fnmatch fopen fpassthru fprintf fputcsv fputs fread fscanf fseek fsockopen fstat ftell ftok getallheaders getcwd getdate getenv gethostbyaddr gethostbyname gethostbynamel getimagesize getlastmod getmxrr getmygid getmyinode getmypid getmyuid getopt getprotobyname getprotobynumber getrandmax getrusage getservbyname getservbyport gettext gettimeofday gettype glob gmdate gmmktime ini_alter ini_get ini_get_all ini_restore ini_set interface_exists intval ip2long is_a is_array is_bool is_callable is_dir is_double is_executable is_file is_finite is_float is_infinite is_int is_integer is_link is_long is_nan is_null is_numeric is_object is_readable is_real is_resource is_scalar is_soap_fault is_string is_subclass_of is_uploaded_file is_writable is_writeable mkdir mktime nl2br parse_ini_file parse_str parse_url passthru pathinfo print readlink realpath rewind rewinddir rmdir round str_ireplace str_pad str_repeat str_replace str_rot13 str_shuffle str_split str_word_count strcasecmp strchr strcmp strcoll strcspn strftime strip_tags stripcslashes stripos stripslashes stristr strlen strnatcasecmp strnatcmp strncasecmp strncmp strpbrk strpos strptime strrchr strrev strripos strrpos strspn strstr strtok strtolower strtotime strtoupper strtr strval substr substr_compare",b="abstract and array as break case catch cfunction class clone const continue declare default die do else elseif enddeclare endfor endforeach endif endswitch endwhile extends final for foreach function global goto if implements include include_once interface instanceof insteadof namespace new old_function or private protected public return require require_once static switch trait throw try use var while xor ",c="__FILE__ __LINE__ __METHOD__ __FUNCTION__ __CLASS__";this.regexList=[{regex:SyntaxHighlighter.regexLib.singleLineCComments,css:"comments"},{regex:SyntaxHighlighter.regexLib.multiLineCComments,css:"comments"},{regex:SyntaxHighlighter.regexLib.doubleQuotedString,css:"string"},{regex:SyntaxHighlighter.regexLib.singleQuotedString,css:"string"},{regex:/\$\w+/g,css:"variable"},{regex:new RegExp(this.getKeywords(a),"gmi"),css:"functions"},{regex:new RegExp(this.getKeywords(c),"gmi"),css:"constants"},{regex:new RegExp(this.getKeywords(b),"gm"),css:"keyword"}],this.forHtmlScript(SyntaxHighlighter.regexLib.phpScriptTags)}SyntaxHighlighter=SyntaxHighlighter||("undefined"!=typeof require?require("shCore").SyntaxHighlighter:null),a.prototype=new SyntaxHighlighter.Highlighter,a.aliases=["php"],SyntaxHighlighter.brushes.Php=a,"undefined"!=typeof exports?exports.Brush=a:null}(),function(){function a(){}SyntaxHighlighter=SyntaxHighlighter||("undefined"!=typeof require?require("shCore").SyntaxHighlighter:null),a.prototype=new SyntaxHighlighter.Highlighter,a.aliases=["text","plain"],SyntaxHighlighter.brushes.Plain=a,"undefined"!=typeof exports?exports.Brush=a:null}(),function(){function a(){var a="while validateset validaterange validatepattern validatelength validatecount until trap switch return ref process param parameter in if global: function foreach for finally filter end elseif else dynamicparam do default continue cmdletbinding break begin alias \\? % #script #private #local #global mandatory parametersetname position valuefrompipeline valuefrompipelinebypropertyname valuefromremainingarguments helpmessage ",b=" and as band bnot bor bxor casesensitive ccontains ceq cge cgt cle clike clt cmatch cne cnotcontains cnotlike cnotmatch contains creplace eq exact f file ge gt icontains ieq ige igt ile ilike ilt imatch ine inotcontains inotlike inotmatch ireplace is isnot le like lt match ne not notcontains notlike notmatch or regex replace wildcard",c="write where wait use update unregister undo trace test tee take suspend stop start split sort skip show set send select scroll resume restore restart resolve resize reset rename remove register receive read push pop ping out new move measure limit join invoke import group get format foreach export expand exit enter enable disconnect disable debug cxnew copy convertto convertfrom convert connect complete compare clear checkpoint aggregate add",d=" component description example externalhelp forwardhelpcategory forwardhelptargetname forwardhelptargetname functionality inputs link notes outputs parameter remotehelprunspace role synopsis";this.regexList=[{regex:new RegExp("^\\s*#[#\\s]*\\.("+this.getKeywords(d)+").*$","gim"),css:"preprocessor help bold"},{regex:SyntaxHighlighter.regexLib.singleLinePerlComments,css:"comments"},{regex:/(&lt;|<)#[\s\S]*?#(&gt;|>)/gm,css:"comments here"},{regex:new RegExp('@"\\n[\\s\\S]*?\\n"@',"gm"),css:"script string here"},{regex:new RegExp("@'\\n[\\s\\S]*?\\n'@","gm"),css:"script string single here"},{regex:new RegExp('"(?:\\$\\([^\\)]*\\)|[^"]|`"|"")*[^`]"',"g"),css:"string"},{regex:new RegExp("'(?:[^']|'')*'","g"),css:"string single"},{regex:new RegExp("[\\$|@|@@](?:(?:global|script|private|env):)?[A-Z0-9_]+","gi"),css:"variable"},{regex:new RegExp("(?:\\b"+c.replace(/ /g,"\\b|\\b")+")-[a-zA-Z_][a-zA-Z0-9_]*","gmi"),css:"functions"},{regex:new RegExp(this.getKeywords(a),"gmi"),css:"keyword"},{regex:new RegExp("-"+this.getKeywords(b),"gmi"),css:"operator value"},{regex:new RegExp("\\[[A-Z_\\[][A-Z0-9_. `,\\[\\]]*\\]","gi"),css:"constants"},{regex:new RegExp("\\s+-(?!"+this.getKeywords(b)+")[a-zA-Z_][a-zA-Z0-9_]*","gmi"),css:"color1"}]}SyntaxHighlighter=SyntaxHighlighter||("undefined"!=typeof require?require("shCore").SyntaxHighlighter:null),a.prototype=new SyntaxHighlighter.Highlighter,a.aliases=["powershell","ps","posh"],SyntaxHighlighter.brushes.PowerShell=a,"undefined"!=typeof exports?exports.Brush=a:null}(),function(){function a(){var a="and assert break class continue def del elif else except exec finally for from global if import in is lambda not or pass print raise return try yield while",b="__import__ abs all any apply basestring bin bool buffer callable chr classmethod cmp coerce compile complex delattr dict dir divmod enumerate eval execfile file filter float format frozenset getattr globals hasattr hash help hex id input int intern isinstance issubclass iter len list locals long map max min next object oct open ord pow print property range raw_input reduce reload repr reversed round set setattr slice sorted staticmethod str sum super tuple type type unichr unicode vars xrange zip",c="None True False self cls class_";this.regexList=[{regex:SyntaxHighlighter.regexLib.singleLinePerlComments,css:"comments"},{regex:/^\s*@\w+/gm,css:"decorator"},{regex:/(['\"]{3})([^\1])*?\1/gm,css:"comments"},{regex:/"(?!")(?:\.|\\\"|[^\""\n])*"/gm,css:"string"},{regex:/'(?!')(?:\.|(\\\')|[^\''\n])*'/gm,css:"string"},{regex:/\+|\-|\*|\/|\%|=|==/gm,css:"keyword"},{regex:/\b\d+\.?\w*/g,css:"value"},{regex:new RegExp(this.getKeywords(b),"gmi"),css:"functions"},{regex:new RegExp(this.getKeywords(a),"gm"),css:"keyword"},{regex:new RegExp(this.getKeywords(c),"gm"),css:"color1"}],this.forHtmlScript(SyntaxHighlighter.regexLib.aspScriptTags)}SyntaxHighlighter=SyntaxHighlighter||("undefined"!=typeof require?require("shCore").SyntaxHighlighter:null),a.prototype=new SyntaxHighlighter.Highlighter,a.aliases=["py","python"],SyntaxHighlighter.brushes.Python=a,"undefined"!=typeof exports?exports.Brush=a:null}(),function(){function a(){var a="alias and BEGIN begin break case class def define_method defined do each else elsif END end ensure false for if in module new next nil not or raise redo rescue retry return self super then throw true undef unless until when while yield",b="Array Bignum Binding Class Continuation Dir Exception FalseClass File::Stat File Fixnum Fload Hash Integer IO MatchData Method Module NilClass Numeric Object Proc Range Regexp String Struct::TMS Symbol ThreadGroup Thread Time TrueClass";this.regexList=[{regex:SyntaxHighlighter.regexLib.singleLinePerlComments,css:"comments"},{regex:SyntaxHighlighter.regexLib.doubleQuotedString,css:"string"},{regex:SyntaxHighlighter.regexLib.singleQuotedString,css:"string"},{regex:/\b[A-Z0-9_]+\b/g,css:"constants"},{regex:/:[a-z][A-Za-z0-9_]*/g,css:"color2"},{regex:/(\$|@@|@)\w+/g,css:"variable bold"},{regex:new RegExp(this.getKeywords(a),"gm"),css:"keyword"},{regex:new RegExp(this.getKeywords(b),"gm"),css:"color1"}],this.forHtmlScript(SyntaxHighlighter.regexLib.aspScriptTags)}SyntaxHighlighter=SyntaxHighlighter||("undefined"!=typeof require?require("shCore").SyntaxHighlighter:null),
a.prototype=new SyntaxHighlighter.Highlighter,a.aliases=["ruby","rails","ror","rb"],SyntaxHighlighter.brushes.Ruby=a,"undefined"!=typeof exports?exports.Brush=a:null}(),function(){function a(){function a(a){return"\\b([a-z_]|)"+a.replace(/ /g,"(?=:)\\b|\\b([a-z_\\*]|\\*|)")+"(?=:)\\b"}function b(a){return"\\b"+a.replace(/ /g,"(?!-)(?!:)\\b|\\b()")+":\\b"}var c="ascent azimuth background-attachment background-color background-image background-position background-repeat background baseline bbox border-collapse border-color border-spacing border-style border-top border-right border-bottom border-left border-top-color border-right-color border-bottom-color border-left-color border-top-style border-right-style border-bottom-style border-left-style border-top-width border-right-width border-bottom-width border-left-width border-width border bottom cap-height caption-side centerline clear clip color content counter-increment counter-reset cue-after cue-before cue cursor definition-src descent direction display elevation empty-cells float font-size-adjust font-family font-size font-stretch font-style font-variant font-weight font height left letter-spacing line-height list-style-image list-style-position list-style-type list-style margin-top margin-right margin-bottom margin-left margin marker-offset marks mathline max-height max-width min-height min-width orphans outline-color outline-style outline-width outline overflow padding-top padding-right padding-bottom padding-left padding page page-break-after page-break-before page-break-inside pause pause-after pause-before pitch pitch-range play-during position quotes right richness size slope src speak-header speak-numeral speak-punctuation speak speech-rate stemh stemv stress table-layout text-align top text-decoration text-indent text-shadow text-transform unicode-bidi unicode-range units-per-em vertical-align visibility voice-family volume white-space widows width widths word-spacing x-height z-index",d="above absolute all always aqua armenian attr aural auto avoid baseline behind below bidi-override black blink block blue bold bolder both bottom braille capitalize caption center center-left center-right circle close-quote code collapse compact condensed continuous counter counters crop cross crosshair cursive dashed decimal decimal-leading-zero digits disc dotted double embed embossed e-resize expanded extra-condensed extra-expanded fantasy far-left far-right fast faster fixed format fuchsia gray green groove handheld hebrew help hidden hide high higher icon inline-table inline inset inside invert italic justify landscape large larger left-side left leftwards level lighter lime line-through list-item local loud lower-alpha lowercase lower-greek lower-latin lower-roman lower low ltr marker maroon medium message-box middle mix move narrower navy ne-resize no-close-quote none no-open-quote no-repeat normal nowrap n-resize nw-resize oblique olive once open-quote outset outside overline pointer portrait pre print projection purple red relative repeat repeat-x repeat-y rgb ridge right right-side rightwards rtl run-in screen scroll semi-condensed semi-expanded separate se-resize show silent silver slower slow small small-caps small-caption smaller soft solid speech spell-out square s-resize static status-bar sub super sw-resize table-caption table-cell table-column table-column-group table-footer-group table-header-group table-row table-row-group teal text-bottom text-top thick thin top transparent tty tv ultra-condensed ultra-expanded underline upper-alpha uppercase upper-latin upper-roman url visible wait white wider w-resize x-fast x-high x-large x-loud x-low x-slow x-small x-soft xx-large xx-small yellow",e="[mM]onospace [tT]ahoma [vV]erdana [aA]rial [hH]elvetica [sS]ans-serif [sS]erif [cC]ourier mono sans serif",f="!important !default",g="@import @extend @debug @warn @if @for @while @mixin @include",h=SyntaxHighlighter.regexLib;this.regexList=[{regex:h.multiLineCComments,css:"comments"},{regex:h.singleLineCComments,css:"comments"},{regex:h.doubleQuotedString,css:"string"},{regex:h.singleQuotedString,css:"string"},{regex:/\#[a-fA-F0-9]{3,6}/g,css:"value"},{regex:/\b(-?\d+)(\.\d+)?(px|em|pt|\:|\%|)\b/g,css:"value"},{regex:/\$\w+/g,css:"variable"},{regex:new RegExp(this.getKeywords(f),"g"),css:"color3"},{regex:new RegExp(this.getKeywords(g),"g"),css:"preprocessor"},{regex:new RegExp(a(c),"gm"),css:"keyword"},{regex:new RegExp(b(d),"g"),css:"value"},{regex:new RegExp(this.getKeywords(e),"g"),css:"color1"}]}SyntaxHighlighter=SyntaxHighlighter||("undefined"!=typeof require?require("shCore").SyntaxHighlighter:null),a.prototype=new SyntaxHighlighter.Highlighter,a.aliases=["sass","scss"],SyntaxHighlighter.brushes.Sass=a,"undefined"!=typeof exports?exports.Brush=a:null}(),function(){function a(){var a="val sealed case def true trait implicit forSome import match object null finally super override try lazy for var catch throw type extends class while with new final yield abstract else do if return protected private this package false",b="[_:=><%#@]+";this.regexList=[{regex:SyntaxHighlighter.regexLib.singleLineCComments,css:"comments"},{regex:SyntaxHighlighter.regexLib.multiLineCComments,css:"comments"},{regex:SyntaxHighlighter.regexLib.multiLineSingleQuotedString,css:"string"},{regex:SyntaxHighlighter.regexLib.multiLineDoubleQuotedString,css:"string"},{regex:SyntaxHighlighter.regexLib.singleQuotedString,css:"string"},{regex:/0x[a-f0-9]+|\d+(\.\d+)?/gi,css:"value"},{regex:new RegExp(this.getKeywords(a),"gm"),css:"keyword"},{regex:new RegExp(b,"gm"),css:"keyword"}]}SyntaxHighlighter=SyntaxHighlighter||("undefined"!=typeof require?require("shCore").SyntaxHighlighter:null),a.prototype=new SyntaxHighlighter.Highlighter,a.aliases=["scala"],SyntaxHighlighter.brushes.Scala=a,"undefined"!=typeof exports?exports.Brush=a:null}(),function(){function a(){var a="abs avg case cast coalesce convert count current_timestamp current_user day isnull left lower month nullif replace right session_user space substring sum system_user upper user year",b="absolute action add after alter as asc at authorization begin bigint binary bit by cascade char character check checkpoint close collate column commit committed connect connection constraint contains continue create cube current current_date current_time cursor database date deallocate dec decimal declare default delete desc distinct double drop dynamic else end end-exec escape except exec execute false fetch first float for force foreign forward free from full function global goto grant group grouping having hour ignore index inner insensitive insert instead int integer intersect into is isolation key last level load local max min minute modify move name national nchar next no numeric of off on only open option order out output partial password precision prepare primary prior privileges procedure public read real references relative repeatable restrict return returns revoke rollback rollup rows rule schema scroll second section select sequence serializable set size smallint static statistics table temp temporary then time timestamp to top transaction translation trigger true truncate uncommitted union unique update values varchar varying view when where with work",c="all and any between cross in join like not null or outer some";this.regexList=[{regex:/--(.*)$/gm,css:"comments"},{regex:SyntaxHighlighter.regexLib.multiLineDoubleQuotedString,css:"string"},{regex:SyntaxHighlighter.regexLib.multiLineSingleQuotedString,css:"string"},{regex:new RegExp(this.getKeywords(a),"gmi"),css:"color2"},{regex:new RegExp(this.getKeywords(c),"gmi"),css:"color1"},{regex:new RegExp(this.getKeywords(b),"gmi"),css:"keyword"}]}SyntaxHighlighter=SyntaxHighlighter||("undefined"!=typeof require?require("shCore").SyntaxHighlighter:null),a.prototype=new SyntaxHighlighter.Highlighter,a.aliases=["sql"],SyntaxHighlighter.brushes.Sql=a,"undefined"!=typeof exports?exports.Brush=a:null}(),function(){function a(){var a="AddHandler AddressOf AndAlso Alias And Ansi As Assembly Auto Boolean ByRef Byte ByVal Call Case Catch CBool CByte CChar CDate CDec CDbl Char CInt Class CLng CObj Const CShort CSng CStr CType Date Decimal Declare Default Delegate Dim DirectCast Do Double Each Else ElseIf End Enum Erase Error Event Exit False Finally For Friend Function Get GetType GoSub GoTo Handles If Implements Imports In Inherits Integer Interface Is Let Lib Like Long Loop Me Mod Module MustInherit MustOverride MyBase MyClass Namespace New Next Not Nothing NotInheritable NotOverridable Object On Option Optional Or OrElse Overloads Overridable Overrides ParamArray Preserve Private Property Protected Public RaiseEvent ReadOnly ReDim REM RemoveHandler Resume Return Select Set Shadows Shared Short Single Static Step Stop String Structure Sub SyncLock Then Throw To True Try TypeOf Unicode Until Variant When While With WithEvents WriteOnly Xor";this.regexList=[{regex:/'.*$/gm,css:"comments"},{regex:SyntaxHighlighter.regexLib.doubleQuotedString,css:"string"},{regex:/^\s*#.*$/gm,css:"preprocessor"},{regex:new RegExp(this.getKeywords(a),"gm"),css:"keyword"}],this.forHtmlScript(SyntaxHighlighter.regexLib.aspScriptTags)}SyntaxHighlighter=SyntaxHighlighter||("undefined"!=typeof require?require("shCore").SyntaxHighlighter:null),a.prototype=new SyntaxHighlighter.Highlighter,a.aliases=["vb","vbnet"],SyntaxHighlighter.brushes.Vb=a,"undefined"!=typeof exports?exports.Brush=a:null}(),function(){function a(){function a(a,b){var c=SyntaxHighlighter.Match,d=a[0],e=new XRegExp("(&lt;|<)[\\s\\/\\?]*(?<name>[:\\w-\\.]+)","xg").exec(d),f=[];if(null!=a.attributes)for(var g,h=new XRegExp("(?<name> [\\w:\\-\\.]+)\\s*=\\s*(?<value> \".*?\"|'.*?'|\\w+)","xg");null!=(g=h.exec(d));)f.push(new c(g.name,a.index+g.index,"color1")),f.push(new c(g.value,a.index+g.index+g[0].indexOf(g.value),"string"));return null!=e&&f.push(new c(e.name,a.index+e[0].indexOf(e.name),"keyword")),f}this.regexList=[{regex:new XRegExp("(\\&lt;|<)\\!\\[[\\w\\s]*?\\[(.|\\s)*?\\]\\](\\&gt;|>)","gm"),css:"color2"},{regex:SyntaxHighlighter.regexLib.xmlComments,css:"comments"},{regex:new XRegExp("(&lt;|<)[\\s\\/\\?]*(\\w+)(?<attributes>.*?)[\\s\\/\\?]*(&gt;|>)","sg"),func:a}]}SyntaxHighlighter=SyntaxHighlighter||("undefined"!=typeof require?require("shCore").SyntaxHighlighter:null),a.prototype=new SyntaxHighlighter.Highlighter,a.aliases=["xml","xhtml","xslt","html"],SyntaxHighlighter.brushes.Xml=a,"undefined"!=typeof exports?exports.Brush=a:null}();