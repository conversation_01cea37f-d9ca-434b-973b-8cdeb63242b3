/*! UEditorPlus v2.0.0*/
!function(a,b){var c,d={},e=function(a,b){var c,d,e;if("string"==typeof a)return h(a);for(c=[],d=a.length,e=0;e<d;e++)c.push(h(a[e]));return b.apply(null,c)},f=function(a,b,c){2===arguments.length&&(c=b,b=null),e(b||[],function(){g(a,c,arguments)})},g=function(a,b,c){var f,g={exports:b};"function"==typeof b&&(c.length||(c=[e,g.exports,g]),f=b.apply(null,c),void 0!==f&&(g.exports=f)),d[a]=g.exports},h=function(b){var c=d[b]||a[b];if(!c)throw new Error("`"+b+"` is undefined");return c},i=function(a){var b,c,e,f,g,h;h=function(a){return a&&a.charAt(0).toUpperCase()+a.substr(1)};for(b in d)if(c=a,d.hasOwnProperty(b)){for(e=b.split("/"),g=h(e.pop());f=h(e.shift());)c[f]=c[f]||{},c=c[f];c[g]=d[b]}return a},j=function(c){return a.__dollar=c,i(b(a,f,e))};"object"==typeof module&&"object"==typeof module.exports?module.exports=j():"function"==typeof define&&define.amd?define(["jquery"],j):(c=a.WebUploader,a.WebUploader=j(),a.WebUploader.noConflict=function(){a.WebUploader=c})}(window,function(a,b,c){return b("dollar-third",[],function(){var b=a.require,c=a.__dollar||a.jQuery||a.Zepto||b("jquery")||b("zepto");if(!c)throw new Error("jQuery or Zepto not found!");return c}),b("dollar",["dollar-third"],function(a){return a}),b("promise-third",["dollar"],function(a){return{Deferred:a.Deferred,when:a.when,isPromise:function(a){return a&&"function"==typeof a.then}}}),b("promise",["promise-third"],function(a){return a}),b("base",["dollar","promise"],function(b,c){function d(a){return function(){return h.apply(a,arguments)}}function e(a,b){return function(){return a.apply(b,arguments)}}function f(a){var b;return Object.create?Object.create(a):(b=function(){},b.prototype=a,new b)}var g=function(){},h=Function.call;return{version:"1.0.0",$:b,Deferred:c.Deferred,isPromise:c.isPromise,when:c.when,browser:function(a){var b={},c=a.match(/WebKit\/([\d.]+)/),d=a.match(/Chrome\/([\d.]+)/)||a.match(/CriOS\/([\d.]+)/),e=a.match(/MSIE\s([\d\.]+)/)||a.match(/(?:trident)(?:.*rv:([\w.]+))?/i),f=a.match(/Firefox\/([\d.]+)/),g=a.match(/Safari\/([\d.]+)/),h=a.match(/OPR\/([\d.]+)/);return c&&(b.webkit=parseFloat(c[1])),d&&(b.chrome=parseFloat(d[1])),e&&(b.ie=parseFloat(e[1])),f&&(b.firefox=parseFloat(f[1])),g&&(b.safari=parseFloat(g[1])),h&&(b.opera=parseFloat(h[1])),b}(navigator.userAgent),os:function(a){var b={},c=a.match(/(?:Android);?[\s\/]+([\d.]+)?/),d=a.match(/(?:iPad|iPod|iPhone).*OS\s([\d_]+)/);return c&&(b.android=parseFloat(c[1])),d&&(b.ios=parseFloat(d[1].replace(/_/g,"."))),b}(navigator.userAgent),inherits:function(a,c,d){var e;return"function"==typeof c?(e=c,c=null):e=c&&c.hasOwnProperty("constructor")?c.constructor:function(){return a.apply(this,arguments)},b.extend(!0,e,a,d||{}),e.__super__=a.prototype,e.prototype=f(a.prototype),c&&b.extend(!0,e.prototype,c),e},noop:g,bindFn:e,log:function(){return a.console?e(console.log,console):g}(),nextTick:function(){return function(a){setTimeout(a,1)}}(),slice:d([].slice),guid:function(){var a=0;return function(b){for(var c=(+new Date).toString(32),d=0;d<5;d++)c+=Math.floor(65535*Math.random()).toString(32);return(b||"wu_")+c+(a++).toString(32)}}(),formatSize:function(a,b,c){var d;for(c=c||["B","K","M","G","TB"];(d=c.shift())&&a>1024;)a/=1024;return("B"===d?a:a.toFixed(b||2))+d}}}),b("mediator",["base"],function(a){function b(a,b,c,d){return f.grep(a,function(a){return a&&(!b||a.e===b)&&(!c||a.cb===c||a.cb._cb===c)&&(!d||a.ctx===d)})}function c(a,b,c){f.each((a||"").split(h),function(a,d){c(d,b)})}function d(a,b){for(var c,d=!1,e=-1,f=a.length;++e<f;)if(c=a[e],c.cb.apply(c.ctx2,b)===!1){d=!0;break}return!d}var e,f=a.$,g=[].slice,h=/\s+/;return e={on:function(a,b,d){var e,f=this;return b?(e=this._events||(this._events=[]),c(a,b,function(a,b){var c={e:a};c.cb=b,c.ctx=d,c.ctx2=d||f,c.id=e.length,e.push(c)}),this):this},once:function(a,b,d){var e=this;return b?(c(a,b,function(a,b){var c=function(){return e.off(a,c),b.apply(d||e,arguments)};c._cb=b,e.on(a,c,d)}),e):e},off:function(a,d,e){var g=this._events;return g?a||d||e?(c(a,d,function(a,c){f.each(b(g,a,c,e),function(){delete g[this.id]})}),this):(this._events=[],this):this},trigger:function(a){var c,e,f;return this._events&&a?(c=g.call(arguments,1),e=b(this._events,a),f=b(this._events,"all"),d(e,c)&&d(f,arguments)):this}},f.extend({installTo:function(a){return f.extend(a,e)}},e)}),b("uploader",["base","mediator"],function(a,b){function c(a){this.options=d.extend(!0,{},c.options,a),this._init(this.options)}var d=a.$;return c.options={debug:!1},b.installTo(c.prototype),d.each({upload:"start-upload",stop:"stop-upload",getFile:"get-file",getFiles:"get-files",addFile:"add-file",addFiles:"add-file",sort:"sort-files",removeFile:"remove-file",cancelFile:"cancel-file",skipFile:"skip-file",retry:"retry",isInProgress:"is-in-progress",makeThumb:"make-thumb",md5File:"md5-file",getDimension:"get-dimension",addButton:"add-btn",predictRuntimeType:"predict-runtime-type",refresh:"refresh",disable:"disable",enable:"enable",reset:"reset"},function(a,b){c.prototype[a]=function(){return this.request(b,arguments)}}),d.extend(c.prototype,{state:"pending",_init:function(a){var b=this;b.request("init",a,function(){b.state="ready",b.trigger("ready")})},option:function(a,b){var c=this.options;return arguments.length>1?void(d.isPlainObject(b)&&d.isPlainObject(c[a])?d.extend(c[a],b):c[a]=b):a?c[a]:c},getStats:function(){var a=this.request("get-stats");return a?{successNum:a.numOfSuccess,progressNum:a.numOfProgress,cancelNum:a.numOfCancel,invalidNum:a.numOfInvalid,uploadFailNum:a.numOfUploadFailed,queueNum:a.numOfQueue,interruptNum:a.numOfInterrupt}:{}},trigger:function(a){var c=[].slice.call(arguments,1),e=this.options,f="on"+a.substring(0,1).toUpperCase()+a.substring(1);return!(b.trigger.apply(this,arguments)===!1||d.isFunction(e[f])&&e[f].apply(this,c)===!1||d.isFunction(this[f])&&this[f].apply(this,c)===!1||b.trigger.apply(b,[this,a].concat(c))===!1)},destroy:function(){this.request("destroy",arguments),this.off()},request:a.noop}),a.create=c.create=function(a){return new c(a)},a.Uploader=c,c}),b("runtime/runtime",["base","mediator"],function(a,b){function c(b){this.options=d.extend({container:document.body},b),this.uid=a.guid("rt_")}var d=a.$,e={},f=function(a){for(var b in a)if(a.hasOwnProperty(b))return b;return null};return d.extend(c.prototype,{getContainer:function(){var a,b,c=this.options;return this._container?this._container:(a=d(c.container||document.body),b=d(document.createElement("div")),b.attr("id","rt_"+this.uid),b.css({position:"absolute",top:"0px",left:"0px",width:"1px",height:"1px",overflow:"hidden"}),a.append(b),a.addClass("webuploader-container"),this._container=b,this._parent=a,b)},init:a.noop,exec:a.noop,destroy:function(){this._container&&this._container.remove(),this._parent&&this._parent.removeClass("webuploader-container"),this.off()}}),c.orders="html5,flash",c.addRuntime=function(a,b){e[a]=b},c.hasRuntime=function(a){return!!(a?e[a]:f(e))},c.create=function(a,b){var g,h;if(b=b||c.orders,d.each(b.split(/\s*,\s*/g),function(){if(e[this])return g=this,!1}),g=g||f(e),!g)throw new Error("Runtime Error");return h=new e[g](a)},b.installTo(c.prototype),c}),b("runtime/client",["base","mediator","runtime/runtime"],function(a,b,c){function d(b,d){var f,g=a.Deferred();this.uid=a.guid("client_"),this.runtimeReady=function(a){return g.done(a)},this.connectRuntime=function(b,h){if(f)throw new Error("already connected!");return g.done(h),"string"==typeof b&&e.get(b)&&(f=e.get(b)),f=f||e.get(null,d),f?(a.$.extend(f.options,b),f.__promise.then(g.resolve),f.__client++):(f=c.create(b,b.runtimeOrder),f.__promise=g.promise(),f.once("ready",g.resolve),f.init(),e.add(f),f.__client=1),d&&(f.__standalone=d),f},this.getRuntime=function(){return f},this.disconnectRuntime=function(){f&&(f.__client--,f.__client<=0&&(e.remove(f),delete f.__promise,f.destroy()),f=null)},this.exec=function(){if(f){var c=a.slice(arguments);return b&&c.unshift(b),f.exec.apply(this,c)}},this.getRuid=function(){return f&&f.uid},this.destroy=function(a){return function(){a&&a.apply(this,arguments),this.trigger("destroy"),this.off(),this.exec("destroy"),this.disconnectRuntime()}}(this.destroy)}var e;return e=function(){var a={};return{add:function(b){a[b.uid]=b},get:function(b,c){var d;if(b)return a[b];for(d in a)if(!c||!a[d].__standalone)return a[d];return null},remove:function(b){delete a[b.uid]}}}(),b.installTo(d.prototype),d}),b("lib/dnd",["base","mediator","runtime/client"],function(a,b,c){function d(a){a=this.options=e.extend({},d.options,a),a.container=e(a.container),a.container.length&&c.call(this,"DragAndDrop")}var e=a.$;return d.options={accept:null,disableGlobalDnd:!1},a.inherits(c,{constructor:d,init:function(){var a=this;a.connectRuntime(a.options,function(){a.exec("init"),a.trigger("ready")})}}),b.installTo(d.prototype),d}),b("widgets/widget",["base","uploader"],function(a,b){function c(a){if(!a)return!1;var b=a.length,c=e.type(a);return!(1!==a.nodeType||!b)||("array"===c||"function"!==c&&"string"!==c&&(0===b||"number"==typeof b&&b>0&&b-1 in a))}function d(a){this.owner=a,this.options=a.options}var e=a.$,f=b.prototype._init,g=b.prototype.destroy,h={},i=[];return e.extend(d.prototype,{init:a.noop,invoke:function(a,b){var c=this.responseMap;return c&&a in c&&c[a]in this&&e.isFunction(this[c[a]])?this[c[a]].apply(this,b):h},request:function(){return this.owner.request.apply(this.owner,arguments)}}),e.extend(b.prototype,{_init:function(){var a=this,b=a._widgets=[],c=a.options.disableWidgets||"";return e.each(i,function(d,e){(!c||!~c.indexOf(e._name))&&b.push(new e(a))}),f.apply(a,arguments)},request:function(b,d,e){var f,g,i,j,k=0,l=this._widgets,m=l&&l.length,n=[],o=[];for(d=c(d)?d:[d];k<m;k++)f=l[k],g=f.invoke(b,d),g!==h&&(a.isPromise(g)?o.push(g):n.push(g));return e||o.length?(i=a.when.apply(a,o),j=i.pipe?"pipe":"then",i[j](function(){var b=a.Deferred(),c=arguments;return 1===c.length&&(c=c[0]),setTimeout(function(){b.resolve(c)},1),b.promise()})[e?j:"done"](e||a.noop)):n[0]},destroy:function(){g.apply(this,arguments),this._widgets=null}}),b.register=d.register=function(b,c){var f,g={init:"init",destroy:"destroy",name:"anonymous"};return 1===arguments.length?(c=b,e.each(c,function(a){return"_"===a[0]||"name"===a?void("name"===a&&(g.name=c.name)):void(g[a.replace(/[A-Z]/g,"-$&").toLowerCase()]=a)})):g=e.extend(g,b),c.responseMap=g,f=a.inherits(d,c),f._name=g.name,i.push(f),f},b.unRegister=d.unRegister=function(a){if(a&&"anonymous"!==a)for(var b=i.length;b--;)i[b]._name===a&&i.splice(b,1)},d}),b("widgets/filednd",["base","uploader","lib/dnd","widgets/widget"],function(a,b,c){var d=a.$;return b.options.dnd="",b.register({name:"dnd",init:function(b){if(b.dnd&&"html5"===this.request("predict-runtime-type")){var e,f=this,g=a.Deferred(),h=d.extend({},{disableGlobalDnd:b.disableGlobalDnd,container:b.dnd,accept:b.accept});return this.dnd=e=new c(h),e.once("ready",g.resolve),e.on("drop",function(a){f.request("add-file",[a])}),e.on("accept",function(a){return f.owner.trigger("dndAccept",a)}),e.init(),g.promise()}},destroy:function(){this.dnd&&this.dnd.destroy()}})}),b("lib/filepaste",["base","mediator","runtime/client"],function(a,b,c){function d(a){a=this.options=e.extend({},a),a.container=e(a.container||document.body),c.call(this,"FilePaste")}var e=a.$;return a.inherits(c,{constructor:d,init:function(){var a=this;a.connectRuntime(a.options,function(){a.exec("init"),a.trigger("ready")})}}),b.installTo(d.prototype),d}),b("widgets/filepaste",["base","uploader","lib/filepaste","widgets/widget"],function(a,b,c){var d=a.$;return b.register({name:"paste",init:function(b){if(b.paste&&"html5"===this.request("predict-runtime-type")){var e,f=this,g=a.Deferred(),h=d.extend({},{container:b.paste,accept:b.accept});return this.paste=e=new c(h),e.once("ready",g.resolve),e.on("paste",function(a){f.owner.request("add-file",[a])}),e.init(),g.promise()}},destroy:function(){this.paste&&this.paste.destroy()}})}),b("lib/blob",["base","runtime/client"],function(a,b){function c(a,c){var d=this;d.source=c,d.ruid=a,this.size=c.size||0,!c.type&&this.ext&&~"jpg,jpeg,png,gif,bmp".indexOf(this.ext)?this.type="image/"+("jpg"===this.ext?"jpeg":this.ext):this.type=c.type||"application/octet-stream",b.call(d,"Blob"),this.uid=c.uid||this.uid,a&&d.connectRuntime(a)}return a.inherits(b,{constructor:c,slice:function(a,b){return this.exec("slice",a,b)},getSource:function(){return this.source}}),c}),b("lib/file",["base","lib/blob"],function(a,b){function c(a,c){var f;this.name=c.name||"untitled"+d++,f=e.exec(c.name)?RegExp.$1.toLowerCase():"",!f&&c.type&&(f=/\/(jpg|jpeg|png|gif|bmp)$/i.exec(c.type)?RegExp.$1.toLowerCase():"",this.name+="."+f),this.ext=f,this.lastModifiedDate=c.lastModifiedDate||c.lastModified&&new Date(c.lastModified).toLocaleString()||(new Date).toLocaleString(),b.apply(this,arguments)}var d=1,e=/\.([^.]+)$/;return a.inherits(b,c)}),b("lib/filepicker",["base","runtime/client","lib/file"],function(b,c,d){function e(a){if(a=this.options=f.extend({},e.options,a),a.container=f(a.id),!a.container.length)throw new Error("按钮指定错误");a.innerHTML=a.innerHTML||a.label||a.container.html()||"",a.button=f(a.button||document.createElement("div")),a.button.html(a.innerHTML),a.container.html(a.button),c.call(this,"FilePicker",!0)}var f=b.$;return e.options={button:null,container:null,label:null,innerHTML:null,multiple:!0,accept:null,name:"file",style:"webuploader-pick"},b.inherits(c,{constructor:e,init:function(){var c=this,e=c.options,g=e.button,h=e.style;h&&g.addClass("webuploader-pick"),c.on("all",function(a){var b;switch(a){case"mouseenter":h&&g.addClass("webuploader-pick-hover");break;case"mouseleave":h&&g.removeClass("webuploader-pick-hover");break;case"change":b=c.exec("getFiles"),c.trigger("select",f.map(b,function(a){return a=new d(c.getRuid(),a),a._refer=e.container,a}),e.container)}}),c.connectRuntime(e,function(){c.refresh(),c.exec("init",e),c.trigger("ready")}),this._resizeHandler=b.bindFn(this.refresh,this),f(a).on("resize",this._resizeHandler)},refresh:function(){var a=this.getRuntime().getContainer(),b=this.options.button,c=b[0]&&b[0].offsetWidth||b.outerWidth()||b.width(),d=b[0]&&b[0].offsetHeight||b.outerHeight()||b.height(),e=b.offset();c&&d&&a.css({bottom:"auto",right:"auto",width:c+"px",height:d+"px"}).offset(e)},enable:function(){var a=this.options.button;a.removeClass("webuploader-pick-disable"),this.refresh()},disable:function(){var a=this.options.button;this.getRuntime().getContainer().css({top:"-99999px"}),a.addClass("webuploader-pick-disable")},destroy:function(){var b=this.options.button;f(a).off("resize",this._resizeHandler),b.removeClass("webuploader-pick-disable webuploader-pick-hover webuploader-pick")}}),e}),b("widgets/filepicker",["base","uploader","lib/filepicker","widgets/widget"],function(a,b,c){var d=a.$;return d.extend(b.options,{pick:null,accept:null}),b.register({name:"picker",init:function(a){return this.pickers=[],a.pick&&this.addBtn(a.pick)},refresh:function(){d.each(this.pickers,function(){this.refresh()})},addBtn:function(b){var e=this,f=e.options,g=f.accept,h=[];if(b)return d.isPlainObject(b)||(b={id:b}),d(b.id).each(function(){var i,j,k;k=a.Deferred(),i=d.extend({},b,{accept:d.isPlainObject(g)?[g]:g,swf:f.swf,runtimeOrder:f.runtimeOrder,id:this}),j=new c(i),j.once("ready",k.resolve),j.on("select",function(a){e.owner.request("add-file",[a])}),j.on("dialogopen",function(){e.owner.trigger("dialogOpen",j.button)}),j.init(),e.pickers.push(j),h.push(k.promise())}),a.when.apply(a,h)},disable:function(){d.each(this.pickers,function(){this.disable()})},enable:function(){d.each(this.pickers,function(){this.enable()})},destroy:function(){d.each(this.pickers,function(){this.destroy()}),this.pickers=null}})}),b("lib/image",["base","runtime/client","lib/blob"],function(a,b,c){function d(a){this.options=e.extend({},d.options,a),b.call(this,"Image"),this.on("load",function(){this._info=this.exec("info"),this._meta=this.exec("meta")})}var e=a.$;return d.options={quality:90,crop:!1,preserveHeaders:!1,allowMagnify:!1},a.inherits(b,{constructor:d,info:function(a){return a?(this._info=a,this):this._info},meta:function(a){return a?(this._meta=a,this):this._meta},loadFromBlob:function(a){var b=this,c=a.getRuid();this.connectRuntime(c,function(){b.exec("init",b.options),b.exec("loadFromBlob",a)})},resize:function(){var b=a.slice(arguments);return this.exec.apply(this,["resize"].concat(b))},crop:function(){var b=a.slice(arguments);return this.exec.apply(this,["crop"].concat(b))},getAsDataUrl:function(a){return this.exec("getAsDataUrl",a)},getAsBlob:function(a){var b=this.exec("getAsBlob",a);return new c(this.getRuid(),b)}}),d}),b("lib/browser-image-compression",[],function(){function b(a,b){return b.forEach(function(b){b&&"string"!=typeof b&&!Array.isArray(b)&&Object.keys(b).forEach(function(c){if("default"!==c&&!(c in a)){var d=Object.getOwnPropertyDescriptor(b,c);Object.defineProperty(a,c,d.get?d:{enumerable:!0,get:function(){return b[c]}})}})}),Object.freeze(a)}function c(b,c){return new Promise(function(d,e){var f;return x(b).then(function(b){try{return f=b,d(new a.Blob([c.slice(0,2),f,c.slice(2)],{type:"image/jpeg"}))}catch(b){return e(b)}},e)})}function d(b,c,d){return void 0===d&&(d=Date.now()),new Promise(function(e){for(var f=b.split(","),g=f[0].match(/:(.*?);/)[1],h=globalThis.atob(f[1]),i=h.length,j=new Uint8Array(i);i--;)j[i]=h.charCodeAt(i);var k=new a.Blob([j],{type:g});k.name=c,k.lastModified=d,e(k)})}function e(a){return new Promise(function(b,c){var d=new J;d.onload=function(){return b(d.result)},d.onerror=function(a){return c(a)},d.readAsDataURL(a)})}function f(a){return new Promise(function(b,c){var d=new Image;d.onload=function(){return b(d)},d.onerror=function(a){return c(a)},d.src=a})}function g(){if(void 0!==g.cachedResult)return g.cachedResult;var a=D.ETC,b=navigator.userAgent;return/Chrom(e|ium)/i.test(b)?a=D.CHROME:/iP(ad|od|hone)/i.test(b)&&/WebKit/i.test(b)?a=D.IOS:/Safari/i.test(b)?a=D.DESKTOP_SAFARI:/Firefox/i.test(b)?a=D.FIREFOX:(/MSIE/i.test(b)||1==!!document.documentMode)&&(a=D.IE),g.cachedResult=a,g.cachedResult}function h(a,b){for(var c=g(),d=E[c],e=a,f=b,h=e*f,i=e>f?f/e:e/f;h>d*d;){var j=(d+e)/2,k=(d+f)/2;j<k?(f=k,e=k*i):(f=j*i,e=j),h=e*f}return{width:e,height:f}}function i(a,b){var c,d;try{if(c=new OffscreenCanvas(a,b),d=c.getContext("2d"),null===d)throw new Error("getContext of OffscreenCanvas returns null")}catch(a){c=document.createElement("canvas"),d=c.getContext("2d")}return c.width=a,c.height=b,[c,d]}function j(a,b){var c=h(a.width,a.height),d=c.width,e=c.height,f=i(d,e),g=f[0],j=f[1];return b&&/jpe?g/.test(b)&&(j.fillStyle="white",j.fillRect(0,0,g.width,g.height)),j.drawImage(a,0,0,g.width,g.height),g}function k(){return void 0!==k.cachedResult||(k.cachedResult=["iPad Simulator","iPhone Simulator","iPod Simulator","iPad","iPhone","iPod"].includes(navigator.platform)||navigator.userAgent.includes("Mac")&&"undefined"!=typeof document&&"ontouchend"in document),k.cachedResult}function l(a,b){return void 0===b&&(b={}),new Promise(function(a,c){var d,h,i=function(){try{return h=j(d,b.fileType||e.type),a([d,h])}catch(e){return c(e)}},l=function(a){try{var b=function(a){try{throw a}catch(a){return c(a)}};try{var g;return e(h).then(function(a){try{return g=a,f(g).then(function(a){try{return d=a,function(){try{return i()}catch(a){return c(a)}}()}catch(a){return b(a)}},b)}catch(a){return b(a)}},b)}catch(h){b(h)}}catch(h){return c(h)}};try{if(k()||[D.DESKTOP_SAFARI,D.MOBILE_SAFARI].includes(g()))throw new Error("Skip createImageBitmap on IOS and Safari");return createImageBitmap(m).then(function(a){try{return d=a,i()}catch(a){return l()}},l)}catch(m){l()}})}function m(b,c,e,f,g){return void 0===g&&(g=1),new Promise(function(h,i){function j(){return k.call(this)}function k(){return l.call(this)}function l(){return h(m)}var m;if("image/png"===c){var n=void 0,o=void 0,p=void 0;return n=b.getContext("2d"),o=n.getImageData(0,0,b.width,b.height).data,p=A.encode([o.buffer],b.width,b.height,4096*g),m=new a.Blob([p],{type:c}),m.name=e,m.lastModified=f,l.call(this)}if("image/bmp"===c)return new Promise(function(a){return C.toBlob(b,a)}).then(function(a){try{return m=a,m.name=e,m.lastModified=f,k.call(this)}catch(a){return i(a)}}.bind(this),i);if("function"==typeof OffscreenCanvas&&b instanceof OffscreenCanvas)return b.convertToBlob({type:c,quality:g}).then(function(a){try{return m=a,m.name=e,m.lastModified=f,j.call(this)}catch(a){return i(a)}}.bind(this),i);var q=void 0;return q=b.toDataURL(c,g),d(q,e,f).then(function(a){try{return m=a,j.call(this)}catch(a){return i(a)}}.bind(this),i)})}function n(a){a.width=0,a.height=0}function o(){return new Promise(function(a,b){var c,e,f,g,h;return void 0!==o.cachedResult?a(o.cachedResult):(c="data:image/jpeg;base64,/9j/4QAiRXhpZgAATU0AKgAAAAgAAQESAAMAAAABAAYAAAAAAAD/2wCEAAEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAf/AABEIAAEAAgMBEQACEQEDEQH/xABKAAEAAAAAAAAAAAAAAAAAAAALEAEAAAAAAAAAAAAAAAAAAAAAAQEAAAAAAAAAAAAAAAAAAAAAEQEAAAAAAAAAAAAAAAAAAAAA/9oADAMBAAIRAxEAPwA/8H//2Q==",d("data:image/jpeg;base64,/9j/4QAiRXhpZgAATU0AKgAAAAgAAQESAAMAAAABAAYAAAAAAAD/2wCEAAEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAf/AABEIAAEAAgMBEQACEQEDEQH/xABKAAEAAAAAAAAAAAAAAAAAAAALEAEAAAAAAAAAAAAAAAAAAAAAAQEAAAAAAAAAAAAAAAAAAAAAEQEAAAAAAAAAAAAAAAAAAAAA/9oADAMBAAIRAxEAPwA/8H//2Q==","test.jpg",Date.now()).then(function(a){try{return e=a,l(e).then(function(a){try{return f=a[1],m(f,e.type,e.name,e.lastModified).then(function(a){try{return g=a,n(f),l(g).then(function(a){try{return h=a[0],o.cachedResult=1===h.width&&2===h.height,c(o.cachedResult)}catch(c){return b(c)}},b)}catch(c){return b(c)}},b)}catch(c){return b(c)}},b)}catch(c){return b(c)}},b))})}function p(a){return new Promise(function(b,c){var d=new J;d.onload=function(a){var c=new DataView(a.target.result);if(65496!=c.getUint16(0,!1))return b(-2);for(var d=c.byteLength,e=2;e<d;){if(c.getUint16(e+2,!1)<=8)return b(-1);var f=c.getUint16(e,!1);if(e+=2,65505==f){if(1165519206!=c.getUint32(e+=2,!1))return b(-1);var g=18761==c.getUint16(e+=6,!1);e+=c.getUint32(e+4,g);var h=c.getUint16(e,g);e+=2;for(var i=0;i<h;i++)if(274==c.getUint16(e+12*i,g))return b(c.getUint16(e+12*i+8,g))}else{if(65280!=(65280&f))break;e+=c.getUint16(e,!1)}}return b(-1)},d.onerror=function(a){return c(a)},d.readAsArrayBuffer(a)})}function q(a,b){var c,d,e=a.width,f=a.height,g=b.maxWidthOrHeight,h=a;return isFinite(g)&&(e>g||f>g)&&(c=i(e,f),h=c[0],d=c[1],e>f?(h.width=g,h.height=f/e*g):(h.width=e/f*g,h.height=g),d.drawImage(a,0,0,h.width,h.height),n(a)),h}function r(a,b){var c=a.width,d=a.height,e=i(c,d),f=e[0],g=e[1];switch(b>4&&b<9?(f.width=d,f.height=c):(f.width=c,f.height=d),b){case 2:g.transform(-1,0,0,1,c,0);break;case 3:g.transform(-1,0,0,-1,c,d);break;case 4:g.transform(1,0,0,-1,0,d);break;case 5:g.transform(0,1,1,0,0,0);break;case 6:g.transform(0,1,-1,0,d,0);break;case 7:g.transform(0,-1,-1,0,d,c);break;case 8:g.transform(0,-1,1,0,0,c)}return g.drawImage(a,0,0,c,d),n(a),f}function s(a,b,c){return void 0===c&&(c=0),new Promise(function(d,e){function f(a){if(void 0===a&&(a=5),b.signal&&b.signal.aborted)throw b.signal.reason;h+=a,b.onProgress(Math.min(h,100))}function g(a){if(b.signal&&b.signal.aborted)throw b.signal.reason;h=Math.min(Math.max(a,h),100),b.onProgress(h)}var h,j,k,s,t,u,v,w,x,y,z,A,B,C,D,E,F,G,H,I;return h=c,j=b.maxIteration||10,k=1024*b.maxSizeMB*1024,f(),l(a,b).then(function(a){try{return s=a[1],f(),t=q(s,b),f(),new Promise(function(a,d){function e(){return a(f)}var f;return(f=b.exifOrientation)?e.call(this):p(c).then(function(a){try{return f=a,e.call(this)}catch(a){return d(a)}}.bind(this),d)}).then(function(a){try{return u=a,f(),o().then(function(a){try{return v=a?t:r(t,u),f(),w=b.initialQuality||1,x=b.fileType||c.type,m(v,x,c.name,c.lastModified,w).then(function(a){function h(){var a;if(j--&&(D>k||D>B)){var b,d;return b=I?.95*H.width:H.width,d=I?.95*H.height:H.height,a=i(b,d),F=a[0],G=a[1],G.drawImage(H,0,0,b,d),w*="image/png"===x?.85:.95,m(F,x,c.name,c.lastModified,w).then(function(a){try{return E=a,n(H),H=F,D=E.size,g(Math.min(99,Math.floor((C-D)/(C-k)*100))),h}catch(a){return e(a)}},e)}return[1]}function l(){return n(H),n(F),n(t),n(v),n(s),g(100),d(E)}try{if(y=a,f(),z=y.size>k,A=y.size>c.size,!z&&!A)return g(100),d(y);var o;return B=c.size,C=y.size,D=C,H=v,I=!b.alwaysKeepResolution&&z,(o=function(a){for(;a;){if(a.then)return void a.then(o,e);try{if(a.pop){if(a.length)return a.pop()?l.call(this):a;a=h}else a=a.call(this)}catch(a){return e(a)}}}.bind(this))(h)}catch(p){return e(p)}}.bind(this),e)}catch(c){return e(c)}}.bind(this),e)}catch(c){return e(c)}}.bind(this),e)}catch(c){return e(c)}}.bind(this),e)})}function t(b,c){return new Promise(function(d,e){B||(B=function(b){var c=[];return"function"==typeof b?c.push("(".concat(b,")()")):c.push(b),URL.createObjectURL(new a.Blob(c))}(K));var f=new Worker(B);f.addEventListener("message",function(a){if(c.signal&&c.signal.aborted)f.terminate();else if(void 0===a.data.progress){if(a.data.error)return e(new Error(a.data.error)),void f.terminate();d(a.data.file),f.terminate()}else c.onProgress(a.data.progress)}),f.addEventListener("error",e),c.signal&&c.signal.addEventListener("abort",function(){e(c.signal.reason),f.terminate()}),f.postMessage({file:b,imageCompressionLibUrl:c.libURL,options:w(w({},c),{onProgress:void 0,signal:void 0})})})}function u(b,d){return new Promise(function(b,e){function f(){try{h.name=a.name,h.lastModified=a.lastModified}catch(a){}try{g.preserveExif&&"image/jpeg"===a.type&&(!g.fileType||g.fileType&&g.fileType===a.type)&&(h=c(a,h))}catch(a){}return b(h)}var g,h,i,j,k,l;if(g=w({},d),i=0,j=g.onProgress,g.maxSizeMB=g.maxSizeMB||Number.POSITIVE_INFINITY,k="boolean"!=typeof g.useWebWorker||g.useWebWorker,delete g.useWebWorker,g.onProgress=function(a){i=a,"function"==typeof j&&j(i)},!(o instanceof a.Blob||o instanceof I))return e(new Error("The file given is not an instance of Blob or File"));if(!/^image/.test(o.type))return e(new Error("The file given is not an image"));if(l="undefined"!=typeof WorkerGlobalScope&&self instanceof WorkerGlobalScope,!k||"function"!=typeof Worker||l)return s(o,g).then(function(a){try{return h=a,f.call(this)}catch(a){return e(a)}}.bind(this),e);var m=function(){try{return f.call(this)}catch(a){return e(a)}}.bind(this),n=function(a){try{return s(b,g).then(function(a){try{return h=a,m()}catch(a){return e(a)}},e)}catch(b){return e(b)}};try{return g.libURL=g.libURL||"https://cdn.jsdelivr.net/npm/browser-image-compression@2.0.2/dist/browser-image-compression.js",t(o,g).then(function(a){try{return h=a,m()}catch(a){return n()}},n)}catch(o){n()}})}var v,w=this&&this.__assign||function(){return w=Object.assign||function(a){for(var b,c=1,d=arguments.length;c<d;c++){b=arguments[c];for(var e in b)Object.prototype.hasOwnProperty.call(b,e)&&(a[e]=b[e])}return a},w.apply(this,arguments)},x=function(b){return new Promise(function(c,d){var e=new FileReader;e.addEventListener("load",function(b){var e=b.target.result,f=new DataView(e),g=0;if(65496!==f.getUint16(g))return d("not a valid JPEG");for(g+=2;;){var h=f.getUint16(g);if(65498===h)break;var i=f.getUint16(g+2);if(65505===h&&1165519206===f.getUint32(g+4)){var j=g+10,k=void 0;switch(f.getUint16(j)){case 18761:k=!0;break;case 19789:k=!1;break;default:return d("TIFF header contains invalid endian")}if(42!==f.getUint16(j+2,k))return d("TIFF header contains invalid version");for(var l=f.getUint32(j+4,k),m=j+l+2+12*f.getUint16(j+l,k),n=j+l+2;n<m;n+=12)if(274==f.getUint16(n,k)){if(3!==f.getUint16(n+2,k))return d("Orientation data type is invalid");if(1!==f.getUint32(n+4,k))return d("Orientation data count is invalid");f.setUint16(n+8,1,k);break}return c(e.slice(g,g+2+i))}g+=2+i}return c(new a.Blob)}),e.readAsArrayBuffer(b)})},y={};!function(a){var b,c,d={};a.exports=d,d.parse=function(a,b){for(var c=d.bin.readUshort,e=d.bin.readUint,f=0,g={},h=new Uint8Array(a),i=h.length-4;101010256!=e(h,i);)i--;f=i,f+=4;var j=c(h,f+=4);c(h,f+=2);var k=e(h,f+=2),l=e(h,f+=4);f+=4,f=l;for(var m=0;m<j;m++){e(h,f),f+=4,f+=4,f+=4,e(h,f+=4),k=e(h,f+=4);var n=e(h,f+=4),o=c(h,f+=4),p=c(h,f+2),q=c(h,f+4);f+=6;var r=e(h,f+=8);f+=4,f+=o+p+q,d._readLocal(h,r,g,k,n,b)}return g},d._readLocal=function(a,b,c,e,f,g){var h=d.bin.readUshort,i=d.bin.readUint;i(a,b),h(a,b+=4),h(a,b+=2);var j=h(a,b+=2);i(a,b+=2),i(a,b+=4),b+=4;var k=h(a,b+=8),l=h(a,b+=2);b+=2;var m=d.bin.readUTF8(a,b,k);if(b+=k,b+=l,g)c[m]={size:f,csize:e};else{var n=new Uint8Array(a.buffer,b);if(0==j)c[m]=new Uint8Array(n.buffer.slice(b,b+e));else{if(8!=j)throw"unknown compression method: "+j;var o=new Uint8Array(f);d.inflateRaw(n,o),c[m]=o}}},d.inflateRaw=function(a,b){return d.F.inflate(a,b)},d.inflate=function(a,b){return a[0],a[1],d.inflateRaw(new Uint8Array(a.buffer,a.byteOffset+2,a.length-6),b)},d.deflate=function(a,b){null==b&&(b={level:6});var c=0,e=new Uint8Array(50+Math.floor(1.1*a.length));e[c]=120,e[c+1]=156,c+=2,c=d.F.deflateRaw(a,e,c,b.level);var f=d.adler(a,0,a.length);return e[c+0]=f>>>24&255,e[c+1]=f>>>16&255,e[c+2]=f>>>8&255,e[c+3]=f>>>0&255,new Uint8Array(e.buffer,0,c+4)},d.deflateRaw=function(a,b){null==b&&(b={level:6});var c=new Uint8Array(50+Math.floor(1.1*a.length)),e=d.F.deflateRaw(a,c,e,b.level);return new Uint8Array(c.buffer,0,e)},d.encode=function(a,b){null==b&&(b=!1);var c=0,e=d.bin.writeUint,f=d.bin.writeUshort,g={};for(var h in a){var i=!d._noNeed(h)&&!b,j=a[h],k=d.crc.crc(j,0,j.length);g[h]={cpr:i,usize:j.length,crc:k,file:i?d.deflateRaw(j):j}}for(var h in g)c+=g[h].file.length+30+46+2*d.bin.sizeUTF8(h);c+=22;var l=new Uint8Array(c),m=0,n=[];for(var h in g){var o=g[h];n.push(m),m=d._writeHeader(l,m,h,o,0)}var p=0,q=m;for(var h in g)o=g[h],n.push(m),m=d._writeHeader(l,m,h,o,1,n[p++]);var r=m-q;return e(l,m,101010256),m+=4,f(l,m+=4,p),f(l,m+=2,p),e(l,m+=2,r),e(l,m+=4,q),m+=4,m+=2,l.buffer},d._noNeed=function(a){var b=a.split(".").pop().toLowerCase();return-1!="png,jpg,jpeg,zip".indexOf(b)},d._writeHeader=function(a,b,c,e,f,g){var h=d.bin.writeUint,i=d.bin.writeUshort,j=e.file;return h(a,b,0==f?67324752:33639248),b+=4,1==f&&(b+=2),i(a,b,20),i(a,b+=2,0),i(a,b+=2,e.cpr?8:0),h(a,b+=2,0),h(a,b+=4,e.crc),h(a,b+=4,j.length),h(a,b+=4,e.usize),i(a,b+=4,d.bin.sizeUTF8(c)),i(a,b+=2,0),b+=2,1==f&&(b+=2,b+=2,h(a,b+=6,g),b+=4),b+=d.bin.writeUTF8(a,b,c),0==f&&(a.set(j,b),b+=j.length),b},d.crc={table:function(){for(var a=new Uint32Array(256),b=0;b<256;b++){for(var c=b,d=0;d<8;d++)1&c?c=3988292384^c>>>1:c>>>=1;a[b]=c}return a}(),update:function(a,b,c,e){for(var f=0;f<e;f++)a=d.crc.table[255&(a^b[c+f])]^a>>>8;return a},crc:function(a,b,c){return 4294967295^d.crc.update(4294967295,a,b,c)}},d.adler=function(a,b,c){for(var d=1,e=0,f=b,g=b+c;f<g;){for(var h=Math.min(f+5552,g);f<h;)e+=d+=a[f++];d%=65521,e%=65521}return e<<16|d},d.bin={readUshort:function(a,b){return a[b]|a[b+1]<<8},writeUshort:function(a,b,c){a[b]=255&c,a[b+1]=c>>8&255},readUint:function(a,b){return 16777216*a[b+3]+(a[b+2]<<16|a[b+1]<<8|a[b])},writeUint:function(a,b,c){a[b]=255&c,a[b+1]=c>>8&255,a[b+2]=c>>16&255,a[b+3]=c>>24&255},readASCII:function(a,b,c){for(var d="",e=0;e<c;e++)d+=String.fromCharCode(a[b+e]);return d},writeASCII:function(a,b,c){for(var d=0;d<c.length;d++)a[b+d]=c.charCodeAt(d)},pad:function(a){return a.length<2?"0"+a:a},readUTF8:function(a,b,c){for(var e,f="",g=0;g<c;g++)f+="%"+d.bin.pad(a[b+g].toString(16));try{e=decodeURIComponent(f)}catch(e){return d.bin.readASCII(a,b,c)}return e},writeUTF8:function(a,b,c){for(var d=c.length,e=0,f=0;f<d;f++){var g=c.charCodeAt(f);if(0==(4294967168&g))a[b+e]=g,e++;else if(0==(4294965248&g))a[b+e]=192|g>>6,a[b+e+1]=128|g>>0&63,e+=2;else if(0==(4294901760&g))a[b+e]=224|g>>12,a[b+e+1]=128|g>>6&63,a[b+e+2]=128|g>>0&63,e+=3;else{if(0!=(4292870144&g))throw"e";a[b+e]=240|g>>18,a[b+e+1]=128|g>>12&63,
a[b+e+2]=128|g>>6&63,a[b+e+3]=128|g>>0&63,e+=4}}return e},sizeUTF8:function(a){for(var b=a.length,c=0,d=0;d<b;d++){var e=a.charCodeAt(d);if(0==(4294967168&e))c++;else if(0==(4294965248&e))c+=2;else if(0==(4294901760&e))c+=3;else{if(0!=(4292870144&e))throw"e";c+=4}}return c}},d.F={},d.F.deflateRaw=function(a,b,c,e){var f=[[0,0,0,0,0],[4,4,8,4,0],[4,5,16,8,0],[4,6,16,16,0],[4,10,16,32,0],[8,16,32,32,0],[8,16,128,128,0],[8,32,128,256,0],[32,128,258,1024,1],[32,258,258,4096,1]][e],g=d.F.U,h=d.F._goodIndex;d.F._hash;var i=d.F._putsE,j=0,k=c<<3,l=0,m=a.length;if(0==e){for(;j<m;)i(b,k,j+(y=Math.min(65535,m-j))==m?1:0),k=d.F._copyExact(a,j,y,b,k+8),j+=y;return k>>>3}var n=g.lits,o=g.strt,p=g.prev,q=0,r=0,s=0,t=0,u=0,v=0;for(m>2&&(o[v=d.F._hash(a,0)]=0),j=0;j<m;j++){if(u=v,j+1<m-2){v=d.F._hash(a,j+1);var w=j+1&32767;p[w]=o[v],o[v]=w}if(l<=j){(q>14e3||r>26697)&&m-j>100&&(l<j&&(n[q]=j-l,q+=2,l=j),k=d.F._writeBlock(j==m-1||l==m?1:0,n,q,t,a,s,j-s,b,k),q=r=t=0,s=j);var x=0;j<m-2&&(x=d.F._bestMatch(a,j,p,u,Math.min(f[2],m-j),f[3]));var y=x>>>16,z=65535&x;if(0!=x){z=65535&x;var A=h(y=x>>>16,g.of0);g.lhst[257+A]++;var B=h(z,g.df0);g.dhst[B]++,t+=g.exb[A]+g.dxb[B],n[q]=y<<23|j-l,n[q+1]=z<<16|A<<8|B,q+=2,l=j+y}else g.lhst[a[j]]++;r++}}for(s==j&&0!=a.length||(l<j&&(n[q]=j-l,q+=2,l=j),k=d.F._writeBlock(1,n,q,t,a,s,j-s,b,k),q=0,r=0,q=r=t=0,s=j);0!=(7&k);)k++;return k>>>3},d.F._bestMatch=function(a,b,c,e,f,g){var h=32767&b,i=c[h],j=h-i+32768&32767;if(i==h||e!=d.F._hash(a,b-j))return 0;for(var k=0,l=0,m=Math.min(32767,b);j<=m&&0!=--g&&i!=h;){if(0==k||a[b+k]==a[b+k-j]){var n=d.F._howLong(a,b,j);if(n>k){if(l=j,(k=n)>=f)break;j+2<n&&(n=j+2);for(var o=0,p=0;p<n-2;p++){var q=b-j+p+32768&32767,r=q-c[q]+32768&32767;r>o&&(o=r,i=q)}}}j+=(h=i)-(i=c[h])+32768&32767}return k<<16|l},d.F._howLong=function(a,b,c){if(a[b]!=a[b-c]||a[b+1]!=a[b+1-c]||a[b+2]!=a[b+2-c])return 0;var d=b,e=Math.min(a.length,b+258);for(b+=3;b<e&&a[b]==a[b-c];)b++;return b-d},d.F._hash=function(a,b){return(a[b]<<8|a[b+1])+(a[b+2]<<4)&65535},d.saved=0,d.F._writeBlock=function(a,b,c,e,f,g,h,i,j){var k,l,m,n,o,p,q,r,s,t=d.F.U,u=d.F._putsF,v=d.F._putsE;t.lhst[256]++,l=(k=d.F.getTrees())[0],m=k[1],n=k[2],o=k[3],p=k[4],q=k[5],r=k[6],s=k[7];var w=32+(0==(j+3&7)?0:8-(j+3&7))+(h<<3),x=e+d.F.contSize(t.fltree,t.lhst)+d.F.contSize(t.fdtree,t.dhst),y=e+d.F.contSize(t.ltree,t.lhst)+d.F.contSize(t.dtree,t.dhst);y+=14+3*q+d.F.contSize(t.itree,t.ihst)+(2*t.ihst[16]+3*t.ihst[17]+7*t.ihst[18]);for(var z=0;z<286;z++)t.lhst[z]=0;for(z=0;z<30;z++)t.dhst[z]=0;for(z=0;z<19;z++)t.ihst[z]=0;var A=w<x&&w<y?0:x<y?1:2;if(u(i,j,a),u(i,j+1,A),j+=3,0==A){for(;0!=(7&j);)j++;j=d.F._copyExact(f,g,h,i,j)}else{var B,C;if(1==A&&(B=t.fltree,C=t.fdtree),2==A){d.F.makeCodes(t.ltree,l),d.F.revCodes(t.ltree,l),d.F.makeCodes(t.dtree,m),d.F.revCodes(t.dtree,m),d.F.makeCodes(t.itree,n),d.F.revCodes(t.itree,n),B=t.ltree,C=t.dtree,v(i,j,o-257),v(i,j+=5,p-1),v(i,j+=5,q-4),j+=4;for(var D=0;D<q;D++)v(i,j+3*D,t.itree[1+(t.ordr[D]<<1)]);j+=3*q,j=d.F._codeTiny(r,t.itree,i,j),j=d.F._codeTiny(s,t.itree,i,j)}for(var E=g,F=0;F<c;F+=2){for(var G=b[F],H=G>>>23,I=E+(8388607&G);E<I;)j=d.F._writeLit(f[E++],B,i,j);if(0!=H){var J=b[F+1],K=J>>16,L=J>>8&255,M=255&J;v(i,j=d.F._writeLit(257+L,B,i,j),H-t.of0[L]),j+=t.exb[L],u(i,j=d.F._writeLit(M,C,i,j),K-t.df0[M]),j+=t.dxb[M],E+=H}}j=d.F._writeLit(256,B,i,j)}return j},d.F._copyExact=function(a,b,c,d,e){var f=e>>>3;return d[f]=c,d[f+1]=c>>>8,d[f+2]=255-d[f],d[f+3]=255-d[f+1],f+=4,d.set(new Uint8Array(a.buffer,b,c),f),e+(c+4<<3)},d.F.getTrees=function(){for(var a=d.F.U,b=d.F._hufTree(a.lhst,a.ltree,15),c=d.F._hufTree(a.dhst,a.dtree,15),e=[],f=d.F._lenCodes(a.ltree,e),g=[],h=d.F._lenCodes(a.dtree,g),i=0;i<e.length;i+=2)a.ihst[e[i]]++;for(i=0;i<g.length;i+=2)a.ihst[g[i]]++;for(var j=d.F._hufTree(a.ihst,a.itree,7),k=19;k>4&&0==a.itree[1+(a.ordr[k-1]<<1)];)k--;return[b,c,j,f,h,k,e,g]},d.F.getSecond=function(a){for(var b=[],c=0;c<a.length;c+=2)b.push(a[c+1]);return b},d.F.nonZero=function(a){for(var b="",c=0;c<a.length;c+=2)0!=a[c+1]&&(b+=(c>>1)+",");return b},d.F.contSize=function(a,b){for(var c=0,d=0;d<b.length;d++)c+=b[d]*a[1+(d<<1)];return c},d.F._codeTiny=function(a,b,c,e){for(var f=0;f<a.length;f+=2){var g=a[f],h=a[f+1];e=d.F._writeLit(g,b,c,e);var i=16==g?2:17==g?3:7;g>15&&(d.F._putsE(c,e,h,i),e+=i)}return e},d.F._lenCodes=function(a,b){for(var c=a.length;2!=c&&0==a[c-1];)c-=2;for(var d=0;d<c;d+=2){var e=a[d+1],f=d+3<c?a[d+3]:-1,g=d+5<c?a[d+5]:-1,h=0==d?-1:a[d-1];if(0==e&&f==e&&g==e){for(var i=d+5;i+2<c&&a[i+2]==e;)i+=2;(j=Math.min(i+1-d>>>1,138))<11?b.push(17,j-3):b.push(18,j-11),d+=2*j-2}else if(e==h&&f==e&&g==e){for(i=d+5;i+2<c&&a[i+2]==e;)i+=2;var j=Math.min(i+1-d>>>1,6);b.push(16,j-3),d+=2*j-2}else b.push(e,0)}return c>>>1},d.F._hufTree=function(a,b,c){var e=[],f=a.length,g=b.length,h=0;for(h=0;h<g;h+=2)b[h]=0,b[h+1]=0;for(h=0;h<f;h++)0!=a[h]&&e.push({lit:h,f:a[h]});var i=e.length,j=e.slice(0);if(0==i)return 0;if(1==i){var k=e[0].lit;return j=0==k?1:0,b[1+(k<<1)]=1,b[1+(j<<1)]=1,1}e.sort(function(a,b){return a.f-b.f});var l=e[0],m=e[1],n=0,o=1,p=2;for(e[0]={lit:-1,f:l.f+m.f,l:l,r:m,d:0};o!=i-1;)l=n!=o&&(p==i||e[n].f<e[p].f)?e[n++]:e[p++],m=n!=o&&(p==i||e[n].f<e[p].f)?e[n++]:e[p++],e[o++]={lit:-1,f:l.f+m.f,l:l,r:m};var q=d.F.setDepth(e[o-1],0);for(q>c&&(d.F.restrictDepth(j,c,q),q=c),h=0;h<i;h++)b[1+(j[h].lit<<1)]=j[h].d;return q},d.F.setDepth=function(a,b){return-1!=a.lit?(a.d=b,b):Math.max(d.F.setDepth(a.l,b+1),d.F.setDepth(a.r,b+1))},d.F.restrictDepth=function(a,b,c){var d=0,e=1<<c-b,f=0;for(a.sort(function(a,b){return b.d==a.d?a.f-b.f:b.d-a.d}),d=0;d<a.length&&a[d].d>b;d++){var g=a[d].d;a[d].d=b,f+=e-(1<<c-g)}for(f>>>=c-b;f>0;)(g=a[d].d)<b?(a[d].d++,f-=1<<b-g-1):d++;for(;d>=0;d--)a[d].d==b&&f<0&&(a[d].d--,f++);0!=f&&console.log("debt left")},d.F._goodIndex=function(a,b){var c=0;return b[16|c]<=a&&(c|=16),b[8|c]<=a&&(c|=8),b[4|c]<=a&&(c|=4),b[2|c]<=a&&(c|=2),b[1|c]<=a&&(c|=1),c},d.F._writeLit=function(a,b,c,e){return d.F._putsF(c,e,b[a<<1]),e+b[1+(a<<1)]},d.F.inflate=function(a,b){var c=Uint8Array;if(3==a[0]&&0==a[1])return b||new c(0);var e=d.F,f=e._bitsF,g=e._bitsE,h=e._decodeTiny,i=e.makeCodes,j=e.codes2map,k=e._get17,l=e.U,m=null==b;m&&(b=new c(a.length>>>2<<3));for(var n,o,p=0,q=0,r=0,s=0,t=0,u=0,v=0,w=0,x=0;0==p;)if(p=f(a,x,1),q=f(a,x+1,2),x+=3,0!=q){if(m&&(b=d.F._check(b,w+(1<<17))),1==q&&(n=l.flmap,o=l.fdmap,u=511,v=31),2==q){r=g(a,x,5)+257,s=g(a,x+5,5)+1,t=g(a,x+10,4)+4,x+=14;for(var y=0;y<38;y+=2)l.itree[y]=0,l.itree[y+1]=0;var z=1;for(y=0;y<t;y++){var A=g(a,x+3*y,3);l.itree[1+(l.ordr[y]<<1)]=A,A>z&&(z=A)}x+=3*t,i(l.itree,z),j(l.itree,z,l.imap),n=l.lmap,o=l.dmap,x=h(l.imap,(1<<z)-1,r+s,a,x,l.ttree);var B=e._copyOut(l.ttree,0,r,l.ltree);u=(1<<B)-1;var C=e._copyOut(l.ttree,r,s,l.dtree);v=(1<<C)-1,i(l.ltree,B),j(l.ltree,B,n),i(l.dtree,C),j(l.dtree,C,o)}for(;;){var D=n[k(a,x)&u];x+=15&D;var E=D>>>4;if(E>>>8==0)b[w++]=E;else{if(256==E)break;var F=w+E-254;if(E>264){var G=l.ldef[E-257];F=w+(G>>>3)+g(a,x,7&G),x+=7&G}var H=o[k(a,x)&v];x+=15&H;var I=H>>>4,J=l.ddef[I],K=(J>>>4)+f(a,x,15&J);for(x+=15&J,m&&(b=d.F._check(b,w+(1<<17)));w<F;)b[w]=b[w++-K],b[w]=b[w++-K],b[w]=b[w++-K],b[w]=b[w++-K];w=F}}}else{0!=(7&x)&&(x+=8-(7&x));var L=4+(x>>>3),M=a[L-4]|a[L-3]<<8;m&&(b=d.F._check(b,w+M)),b.set(new c(a.buffer,a.byteOffset+L,M),w),x=L+M<<3,w+=M}return b.length==w?b:b.slice(0,w)},d.F._check=function(a,b){var c=a.length;if(b<=c)return a;var d=new Uint8Array(Math.max(c<<1,b));return d.set(a,0),d},d.F._decodeTiny=function(a,b,c,e,f,g){for(var h=d.F._bitsE,i=d.F._get17,j=0;j<c;){var k=a[i(e,f)&b];f+=15&k;var l=k>>>4;if(l<=15)g[j]=l,j++;else{var m=0,n=0;16==l?(n=3+h(e,f,2),f+=2,m=g[j-1]):17==l?(n=3+h(e,f,3),f+=3):18==l&&(n=11+h(e,f,7),f+=7);for(var o=j+n;j<o;)g[j]=m,j++}}return f},d.F._copyOut=function(a,b,c,d){for(var e=0,f=0,g=d.length>>>1;f<c;){var h=a[f+b];d[f<<1]=0,d[1+(f<<1)]=h,h>e&&(e=h),f++}for(;f<g;)d[f<<1]=0,d[1+(f<<1)]=0,f++;return e},d.F.makeCodes=function(a,b){for(var c,e,f,g,h=d.F.U,i=a.length,j=h.bl_count,k=0;k<=b;k++)j[k]=0;for(k=1;k<i;k+=2)j[a[k]]++;var l=h.next_code;for(c=0,j[0]=0,e=1;e<=b;e++)c=c+j[e-1]<<1,l[e]=c;for(f=0;f<i;f+=2)0!=(g=a[f+1])&&(a[f]=l[g],l[g]++)},d.F.codes2map=function(a,b,c){for(var e=a.length,f=d.F.U.rev15,g=0;g<e;g+=2)if(0!=a[g+1])for(var h=g>>1,i=a[g+1],j=h<<4|i,k=b-i,l=a[g]<<k,m=l+(1<<k);l!=m;)c[f[l]>>>15-b]=j,l++},d.F.revCodes=function(a,b){for(var c=d.F.U.rev15,e=15-b,f=0;f<a.length;f+=2){var g=a[f]<<b-a[f+1];a[f]=c[g]>>>e}},d.F._putsE=function(a,b,c){c<<=7&b;var d=b>>>3;a[d]|=c,a[d+1]|=c>>>8},d.F._putsF=function(a,b,c){c<<=7&b;var d=b>>>3;a[d]|=c,a[d+1]|=c>>>8,a[d+2]|=c>>>16},d.F._bitsE=function(a,b,c){return(a[b>>>3]|a[1+(b>>>3)]<<8)>>>(7&b)&(1<<c)-1},d.F._bitsF=function(a,b,c){return(a[b>>>3]|a[1+(b>>>3)]<<8|a[2+(b>>>3)]<<16)>>>(7&b)&(1<<c)-1},d.F._get17=function(a,b){return(a[b>>>3]|a[1+(b>>>3)]<<8|a[2+(b>>>3)]<<16)>>>(7&b)},d.F._get25=function(a,b){return(a[b>>>3]|a[1+(b>>>3)]<<8|a[2+(b>>>3)]<<16|a[3+(b>>>3)]<<24)>>>(7&b)},d.F.U=(b=Uint16Array,c=Uint32Array,{next_code:new b(16),bl_count:new b(16),ordr:[16,17,18,0,8,7,9,6,10,5,11,4,12,3,13,2,14,1,15],of0:[3,4,5,6,7,8,9,10,11,13,15,17,19,23,27,31,35,43,51,59,67,83,99,115,131,163,195,227,258,999,999,999],exb:[0,0,0,0,0,0,0,0,1,1,1,1,2,2,2,2,3,3,3,3,4,4,4,4,5,5,5,5,0,0,0,0],ldef:new b(32),df0:[1,2,3,4,5,7,9,13,17,25,33,49,65,97,129,193,257,385,513,769,1025,1537,2049,3073,4097,6145,8193,12289,16385,24577,65535,65535],dxb:[0,0,0,0,1,1,2,2,3,3,4,4,5,5,6,6,7,7,8,8,9,9,10,10,11,11,12,12,13,13,0,0],ddef:new c(32),flmap:new b(512),fltree:[],fdmap:new b(32),fdtree:[],lmap:new b(32768),ltree:[],ttree:[],dmap:new b(32768),dtree:[],imap:new b(512),itree:[],rev15:new b(32768),lhst:new c(286),dhst:new c(30),ihst:new c(19),lits:new c(15e3),strt:new b(65536),prev:new b(32768)}),function(){function a(a,b,c){for(;0!=b--;)a.push(0,c)}for(var b=d.F.U,c=0;c<32768;c++){var e=c;e=(4278255360&(e=(4042322160&(e=(3435973836&(e=(2863311530&e)>>>1|(1431655765&e)<<1))>>>2|(858993459&e)<<2))>>>4|(252645135&e)<<4))>>>8|(16711935&e)<<8,b.rev15[c]=(e>>>16|e<<16)>>>17}for(c=0;c<32;c++)b.ldef[c]=b.of0[c]<<3|b.exb[c],b.ddef[c]=b.df0[c]<<4|b.dxb[c];a(b.fltree,144,8),a(b.fltree,112,9),a(b.fltree,24,7),a(b.fltree,8,8),d.F.makeCodes(b.fltree,9),d.F.codes2map(b.fltree,9,b.flmap),d.F.revCodes(b.fltree,9),a(b.fdtree,32,5),d.F.makeCodes(b.fdtree,5),d.F.codes2map(b.fdtree,5,b.fdmap),d.F.revCodes(b.fdtree,5),a(b.itree,19,0),a(b.ltree,286,0),a(b.dtree,30,0),a(b.ttree,320,0)}()}({get exports(){return y},set exports(a){y=a}});var z=b({__proto__:null,"default":y},[y]),A=function(){function a(a,b,c,e){var f=b*c,g=d(e),h=Math.ceil(b*g/8),j=new Uint8Array(4*f),k=new Uint32Array(j.buffer),l=e.ctype,m=e.depth,n=i.readUshort;if(6==l){var o=f<<2;if(8==m)for(var p=0;p<o;p+=4)j[p]=a[p],j[p+1]=a[p+1],j[p+2]=a[p+2],j[p+3]=a[p+3];if(16==m)for(p=0;p<o;p++)j[p]=a[p<<1]}else if(2==l){var q=e.tabs.tRNS;if(null==q){if(8==m)for(p=0;p<f;p++){var r=3*p;k[p]=255<<24|a[r+2]<<16|a[r+1]<<8|a[r]}if(16==m)for(p=0;p<f;p++)r=6*p,k[p]=255<<24|a[r+4]<<16|a[r+2]<<8|a[r]}else{var s=q[0],t=q[1],u=q[2];if(8==m)for(p=0;p<f;p++){var v=p<<2;r=3*p,k[p]=255<<24|a[r+2]<<16|a[r+1]<<8|a[r],a[r]==s&&a[r+1]==t&&a[r+2]==u&&(j[v+3]=0)}if(16==m)for(p=0;p<f;p++)v=p<<2,r=6*p,k[p]=255<<24|a[r+4]<<16|a[r+2]<<8|a[r],n(a,r)==s&&n(a,r+2)==t&&n(a,r+4)==u&&(j[v+3]=0)}}else if(3==l){var w=e.tabs.PLTE,x=e.tabs.tRNS,y=x?x.length:0;if(1==m)for(var z=0;z<c;z++){var A=z*h,B=z*b;for(p=0;p<b;p++){v=B+p<<2;var C=3*(D=a[A+(p>>3)]>>7-((7&p)<<0)&1);j[v]=w[C],j[v+1]=w[C+1],j[v+2]=w[C+2],j[v+3]=D<y?x[D]:255}}if(2==m)for(z=0;z<c;z++)for(A=z*h,B=z*b,p=0;p<b;p++)v=B+p<<2,C=3*(D=a[A+(p>>2)]>>6-((3&p)<<1)&3),j[v]=w[C],j[v+1]=w[C+1],j[v+2]=w[C+2],j[v+3]=D<y?x[D]:255;if(4==m)for(z=0;z<c;z++)for(A=z*h,B=z*b,p=0;p<b;p++)v=B+p<<2,C=3*(D=a[A+(p>>1)]>>4-((1&p)<<2)&15),j[v]=w[C],j[v+1]=w[C+1],j[v+2]=w[C+2],j[v+3]=D<y?x[D]:255;if(8==m)for(p=0;p<f;p++){var D;v=p<<2,C=3*(D=a[p]),j[v]=w[C],j[v+1]=w[C+1],j[v+2]=w[C+2],j[v+3]=D<y?x[D]:255}}else if(4==l){if(8==m)for(p=0;p<f;p++){v=p<<2;var E=a[F=p<<1];j[v]=E,j[v+1]=E,j[v+2]=E,j[v+3]=a[F+1]}if(16==m)for(p=0;p<f;p++){var F;v=p<<2,E=a[F=p<<2],j[v]=E,j[v+1]=E,j[v+2]=E,j[v+3]=a[F+2]}}else if(0==l)for(s=e.tabs.tRNS?e.tabs.tRNS:-1,z=0;z<c;z++){var G=z*h,H=z*b;if(1==m)for(var I=0;I<b;I++){var J=(E=255*(a[G+(I>>>3)]>>>7-(7&I)&1))==255*s?0:255;k[H+I]=J<<24|E<<16|E<<8|E}else if(2==m)for(I=0;I<b;I++)J=(E=85*(a[G+(I>>>2)]>>>6-((3&I)<<1)&3))==85*s?0:255,k[H+I]=J<<24|E<<16|E<<8|E;else if(4==m)for(I=0;I<b;I++)J=(E=17*(a[G+(I>>>1)]>>>4-((1&I)<<2)&15))==17*s?0:255,k[H+I]=J<<24|E<<16|E<<8|E;else if(8==m)for(I=0;I<b;I++)J=(E=a[G+I])==s?0:255,k[H+I]=J<<24|E<<16|E<<8|E;else if(16==m)for(I=0;I<b;I++)E=a[G+(I<<1)],J=n(a,G+(I<<1))==s?0:255,k[H+I]=J<<24|E<<16|E<<8|E}return j}function b(a,b,f,g){var h=d(a),i=Math.ceil(f*h/8),k=new Uint8Array((i+1+a.interlace)*g);return b=a.tabs.CgBI?j(b,k):c(b,k),0==a.interlace?b=e(b,a,0,f,g):1==a.interlace&&(b=function(a,b){for(var c=b.width,f=b.height,g=d(b),h=g>>3,i=Math.ceil(c*g/8),j=new Uint8Array(f*i),k=0,l=[0,0,4,0,2,0,1],m=[0,4,0,2,0,1,0],n=[8,8,8,4,4,2,2],o=[8,8,4,4,2,2,1],p=0;p<7;){for(var q=n[p],r=o[p],s=0,t=0,u=l[p];u<f;)u+=q,t++;for(var v=m[p];v<c;)v+=r,s++;var w=Math.ceil(s*g/8);e(a,b,k,s,t);for(var x=0,y=l[p];y<f;){for(var z=m[p],A=k+x*w<<3;z<c;){var B;if(1==g&&(B=(B=a[A>>3])>>7-(7&A)&1,j[y*i+(z>>3)]|=B<<7-((7&z)<<0)),2==g&&(B=(B=a[A>>3])>>6-(7&A)&3,j[y*i+(z>>2)]|=B<<6-((3&z)<<1)),4==g&&(B=(B=a[A>>3])>>4-(7&A)&15,j[y*i+(z>>1)]|=B<<4-((1&z)<<2)),g>=8)for(var C=y*i+z*h,D=0;D<h;D++)j[C+D]=a[(A>>3)+D];A+=g,z+=r}x++,y+=q}s*t!=0&&(k+=t*(1+w)),p+=1}return j}(b,a)),b}function c(a,b){return j(new Uint8Array(a.buffer,2,a.length-6),b)}function d(a){return[1,null,3,1,2,null,4][a.ctype]*a.depth}function e(a,b,c,e,g){var h,i,j=d(b),k=Math.ceil(e*j/8);j=Math.ceil(j/8);var l=a[c],m=0;if(l>1&&(a[c]=[0,0,1][l-2]),3==l)for(m=j;m<k;m++)a[m+1]=a[m+1]+(a[m+1-j]>>>1)&255;for(var n=0;n<g;n++)if(h=c+n*k,i=h+n+1,l=a[i-1],m=0,0==l)for(;m<k;m++)a[h+m]=a[i+m];else if(1==l){for(;m<j;m++)a[h+m]=a[i+m];for(;m<k;m++)a[h+m]=a[i+m]+a[h+m-j]}else if(2==l)for(;m<k;m++)a[h+m]=a[i+m]+a[h+m-k];else if(3==l){for(;m<j;m++)a[h+m]=a[i+m]+(a[h+m-k]>>>1);for(;m<k;m++)a[h+m]=a[i+m]+(a[h+m-k]+a[h+m-j]>>>1)}else{for(;m<j;m++)a[h+m]=a[i+m]+f(0,a[h+m-k],0);for(;m<k;m++)a[h+m]=a[i+m]+f(a[h+m-j],a[h+m-k],a[h+m-j-k])}return a}function f(a,b,c){var d=a+b-c,e=d-a,f=d-b,g=d-c;return e*e<=f*f&&e*e<=g*g?a:f*f<=g*g?b:c}function g(a,b,c){c.width=i.readUint(a,b),b+=4,c.height=i.readUint(a,b),b+=4,c.depth=a[b],b++,c.ctype=a[b],b++,c.compress=a[b],b++,c.filter=a[b],b++,c.interlace=a[b],b++}function h(a,b,c,d,e,f,g,h,i){for(var j=Math.min(b,e),k=Math.min(c,f),l=0,m=0,n=0;n<k;n++)for(var o=0;o<j;o++)if(g>=0&&h>=0?(l=n*b+o<<2,m=(h+n)*e+g+o<<2):(l=(-h+n)*b-g+o<<2,m=n*e+o<<2),0==i)d[m]=a[l],d[m+1]=a[l+1],d[m+2]=a[l+2],d[m+3]=a[l+3];else if(1==i){var p=a[l+3]*(1/255),q=a[l]*p,r=a[l+1]*p,s=a[l+2]*p,t=d[m+3]*(1/255),u=d[m]*t,v=d[m+1]*t,w=d[m+2]*t,x=1-p,y=p+t*x,z=0==y?0:1/y;d[m+3]=255*y,d[m+0]=(q+u*x)*z,d[m+1]=(r+v*x)*z,d[m+2]=(s+w*x)*z}else if(2==i)p=a[l+3],q=a[l],r=a[l+1],s=a[l+2],t=d[m+3],u=d[m],v=d[m+1],w=d[m+2],p==t&&q==u&&r==v&&s==w?(d[m]=0,d[m+1]=0,d[m+2]=0,d[m+3]=0):(d[m]=q,d[m+1]=r,d[m+2]=s,d[m+3]=p);else if(3==i){if(p=a[l+3],q=a[l],r=a[l+1],s=a[l+2],t=d[m+3],u=d[m],v=d[m+1],w=d[m+2],p==t&&q==u&&r==v&&s==w)continue;if(p<220&&t>20)return!1}return!0}var i={nextZero:function(a,b){for(;0!=a[b];)b++;return b},readUshort:function(a,b){return a[b]<<8|a[b+1]},writeUshort:function(a,b,c){a[b]=c>>8&255,a[b+1]=255&c},readUint:function(a,b){return 16777216*a[b]+(a[b+1]<<16|a[b+2]<<8|a[b+3])},writeUint:function(a,b,c){a[b]=c>>24&255,a[b+1]=c>>16&255,a[b+2]=c>>8&255,a[b+3]=255&c},readASCII:function(a,b,c){for(var d="",e=0;e<c;e++)d+=String.fromCharCode(a[b+e]);return d},writeASCII:function(a,b,c){for(var d=0;d<c.length;d++)a[b+d]=c.charCodeAt(d)},readBytes:function(a,b,c){for(var d=[],e=0;e<c;e++)d.push(a[b+e]);return d},pad:function(a){return a.length<2?"0".concat(a):a},readUTF8:function(a,b,c){for(var d,e="",f=0;f<c;f++)e+="%".concat(i.pad(a[b+f].toString(16)));try{d=decodeURIComponent(e)}catch(d){return i.readASCII(a,b,c)}return d}},j=function(){var a={H:{}};return a.H.N=function(b,c){var d,e,f=Uint8Array,g=0,h=0,i=0,j=0,k=0,l=0,m=0,n=0,o=0;if(3==b[0]&&0==b[1])return c||new f(0);var p=a.H,q=p.b,r=p.e,s=p.R,t=p.n,u=p.A,v=p.Z,w=p.m,x=null==c;for(x&&(c=new f(b.length>>>2<<5));0==g;)if(g=q(b,o,1),h=q(b,o+1,2),o+=3,0!=h){if(x&&(c=a.H.W(c,n+(1<<17))),1==h&&(d=w.J,e=w.h,l=511,m=31),2==h){i=r(b,o,5)+257,j=r(b,o+5,5)+1,k=r(b,o+10,4)+4,o+=14;for(var y=1,z=0;z<38;z+=2)w.Q[z]=0,w.Q[z+1]=0;for(z=0;z<k;z++){var A=r(b,o+3*z,3);w.Q[1+(w.X[z]<<1)]=A,A>y&&(y=A)}o+=3*k,t(w.Q,y),u(w.Q,y,w.u),d=w.w,e=w.d,o=s(w.u,(1<<y)-1,i+j,b,o,w.v);var B=p.V(w.v,0,i,w.C);l=(1<<B)-1;var C=p.V(w.v,i,j,w.D);m=(1<<C)-1,t(w.C,B),u(w.C,B,d),t(w.D,C),u(w.D,C,e)}for(;;){var D=d[v(b,o)&l];o+=15&D;var E=D>>>4;if(E>>>8==0)c[n++]=E;else{if(256==E)break;var F=n+E-254;if(E>264){var G=w.q[E-257];F=n+(G>>>3)+r(b,o,7&G),o+=7&G}var H=e[v(b,o)&m];o+=15&H;var I=H>>>4,J=w.c[I],K=(J>>>4)+q(b,o,15&J);for(o+=15&J;n<F;)c[n]=c[n++-K],c[n]=c[n++-K],c[n]=c[n++-K],c[n]=c[n++-K];n=F}}}else{0!=(7&o)&&(o+=8-(7&o));var L=4+(o>>>3),M=b[L-4]|b[L-3]<<8;x&&(c=a.H.W(c,n+M)),c.set(new f(b.buffer,b.byteOffset+L,M),n),o=L+M<<3,n+=M}return c.length==n?c:c.slice(0,n)},a.H.W=function(a,b){var c=a.length;if(b<=c)return a;var d=new Uint8Array(c<<1);return d.set(a,0),d},a.H.R=function(b,c,d,e,f,g){for(var h=a.H.e,i=a.H.Z,j=0;j<d;){var k=b[i(e,f)&c];f+=15&k;var l=k>>>4;if(l<=15)g[j]=l,j++;else{var m=0,n=0;16==l?(n=3+h(e,f,2),f+=2,m=g[j-1]):17==l?(n=3+h(e,f,3),f+=3):18==l&&(n=11+h(e,f,7),f+=7);for(var o=j+n;j<o;)g[j]=m,j++}}return f},a.H.V=function(a,b,c,d){for(var e=0,f=0,g=d.length>>>1;f<c;){var h=a[f+b];d[f<<1]=0,d[1+(f<<1)]=h,h>e&&(e=h),f++}for(;f<g;)d[f<<1]=0,d[1+(f<<1)]=0,f++;return e},a.H.n=function(b,c){for(var d,e,f,g,h=a.H.m,i=b.length,j=h.j,k=0;k<=c;k++)j[k]=0;for(k=1;k<i;k+=2)j[b[k]]++;var l=h.K;for(d=0,j[0]=0,e=1;e<=c;e++)d=d+j[e-1]<<1,l[e]=d;for(f=0;f<i;f+=2)g=b[f+1],0!=g&&(b[f]=l[g],l[g]++)},a.H.A=function(b,c,d){for(var e=b.length,f=a.H.m.r,g=0;g<e;g+=2)if(0!=b[g+1])for(var h=g>>1,i=b[g+1],j=h<<4|i,k=c-i,l=b[g]<<k,m=l+(1<<k);l!=m;)d[f[l]>>>15-c]=j,l++},a.H.l=function(b,c){for(var d=a.H.m.r,e=15-c,f=0;f<b.length;f+=2){var g=b[f]<<c-b[f+1];b[f]=d[g]>>>e}},a.H.M=function(a,b,c){c<<=7&b;var d=b>>>3;a[d]|=c,a[d+1]|=c>>>8},a.H.I=function(a,b,c){c<<=7&b;var d=b>>>3;a[d]|=c,a[d+1]|=c>>>8,a[d+2]|=c>>>16},a.H.e=function(a,b,c){return(a[b>>>3]|a[1+(b>>>3)]<<8)>>>(7&b)&(1<<c)-1},a.H.b=function(a,b,c){return(a[b>>>3]|a[1+(b>>>3)]<<8|a[2+(b>>>3)]<<16)>>>(7&b)&(1<<c)-1},a.H.Z=function(a,b){return(a[b>>>3]|a[1+(b>>>3)]<<8|a[2+(b>>>3)]<<16)>>>(7&b)},a.H.i=function(a,b){return(a[b>>>3]|a[1+(b>>>3)]<<8|a[2+(b>>>3)]<<16|a[3+(b>>>3)]<<24)>>>(7&b)},a.H.m=function(){var a=Uint16Array,b=Uint32Array;return{K:new a(16),j:new a(16),X:[16,17,18,0,8,7,9,6,10,5,11,4,12,3,13,2,14,1,15],S:[3,4,5,6,7,8,9,10,11,13,15,17,19,23,27,31,35,43,51,59,67,83,99,115,131,163,195,227,258,999,999,999],T:[0,0,0,0,0,0,0,0,1,1,1,1,2,2,2,2,3,3,3,3,4,4,4,4,5,5,5,5,0,0,0,0],q:new a(32),p:[1,2,3,4,5,7,9,13,17,25,33,49,65,97,129,193,257,385,513,769,1025,1537,2049,3073,4097,6145,8193,12289,16385,24577,65535,65535],z:[0,0,0,0,1,1,2,2,3,3,4,4,5,5,6,6,7,7,8,8,9,9,10,10,11,11,12,12,13,13,0,0],c:new b(32),J:new a(512),_:[],h:new a(32),$:[],w:new a(32768),C:[],v:[],d:new a(32768),D:[],u:new a(512),Q:[],r:new a(32768),s:new b(286),Y:new b(30),a:new b(19),t:new b(15e3),k:new a(65536),g:new a(32768)}}(),function(){function b(a,b,c){for(;0!=b--;)a.push(0,c)}for(var c=a.H.m,d=0;d<32768;d++){var e=d;e=(2863311530&e)>>>1|(1431655765&e)<<1,e=(3435973836&e)>>>2|(858993459&e)<<2,e=(4042322160&e)>>>4|(252645135&e)<<4,e=(4278255360&e)>>>8|(16711935&e)<<8,c.r[d]=(e>>>16|e<<16)>>>17}for(d=0;d<32;d++)c.q[d]=c.S[d]<<3|c.T[d],c.c[d]=c.p[d]<<4|c.z[d];b(c._,144,8),b(c._,112,9),b(c._,24,7),b(c._,8,8),a.H.n(c._,9),a.H.A(c._,9,c.J),a.H.l(c._,9),b(c.$,32,5),a.H.n(c.$,5),a.H.A(c.$,5,c.h),a.H.l(c.$,5),b(c.Q,19,0),b(c.C,286,0),b(c.D,30,0),b(c.v,320,0)}(),a.H.N}();return{decode:function(a){for(var d,e=new Uint8Array(a),f=8,h=w,i=h.readUshort,k=h.readUint,l={tabs:{},frames:[]},m=new Uint8Array(e.length),n=0,o=0,p=[137,80,78,71,13,10,26,10],q=0;q<8;q++)if(e[q]!=p[q])throw"The input is not a PNG file!";for(;f<e.length;){var r=h.readUint(e,f);f+=4;var s=h.readASCII(e,f,4);if(f+=4,"IHDR"==s)g(e,f,l);else if("iCCP"==s){for(var t=f;0!=e[t];)t++;h.readASCII(e,f,t-f),e[t+1];var u=e.slice(t+2,f+r),v=null;try{v=c(u)}catch(w){v=j(u)}l.tabs[s]=v}else if("CgBI"==s)l.tabs[s]=e.slice(f,f+4);else if("IDAT"==s){for(q=0;q<r;q++)m[n+q]=e[f+q];n+=r}else if("acTL"==s)l.tabs[s]={num_frames:k(e,f),num_plays:k(e,f+4)},d=new Uint8Array(e.length);else if("fcTL"==s){0!=o&&((H=l.frames[l.frames.length-1]).data=b(l,d.slice(0,o),H.rect.width,H.rect.height),o=0);var x={x:k(e,f+12),y:k(e,f+16),width:k(e,f+4),height:k(e,f+8)},y=i(e,f+22);y=i(e,f+20)/(0==y?100:y);var z={rect:x,delay:Math.round(1e3*y),dispose:e[f+24],blend:e[f+25]};l.frames.push(z)}else if("fdAT"==s){for(q=0;q<r-4;q++)d[o+q]=e[f+q+4];o+=r-4}else if("pHYs"==s)l.tabs[s]=[h.readUint(e,f),h.readUint(e,f+4),e[f+8]];else if("cHRM"==s)for(l.tabs[s]=[],q=0;q<8;q++)l.tabs[s].push(h.readUint(e,f+4*q));else if("tEXt"==s||"zTXt"==s){null==l.tabs[s]&&(l.tabs[s]={});var A=h.nextZero(e,f),B=h.readASCII(e,f,A-f),C=f+r-A-1;if("tEXt"==s)E=h.readASCII(e,A+1,C);else{var D=c(e.slice(A+2,A+2+C));E=h.readUTF8(D,0,D.length)}l.tabs[s][B]=E}else if("iTXt"==s){null==l.tabs[s]&&(l.tabs[s]={}),A=0,t=f,A=h.nextZero(e,t),B=h.readASCII(e,t,A-t);var E,F=e[t=A+1];e[t+1],t+=2,A=h.nextZero(e,t),h.readASCII(e,t,A-t),t=A+1,A=h.nextZero(e,t),h.readUTF8(e,t,A-t),C=r-((t=A+1)-f),0==F?E=h.readUTF8(e,t,C):(D=c(e.slice(t,t+C)),E=h.readUTF8(D,0,D.length)),l.tabs[s][B]=E}else if("PLTE"==s)l.tabs[s]=h.readBytes(e,f,r);else if("hIST"==s){var G=l.tabs.PLTE.length/3;for(l.tabs[s]=[],q=0;q<G;q++)l.tabs[s].push(i(e,f+2*q))}else if("tRNS"==s)3==l.ctype?l.tabs[s]=h.readBytes(e,f,r):0==l.ctype?l.tabs[s]=i(e,f):2==l.ctype&&(l.tabs[s]=[i(e,f),i(e,f+2),i(e,f+4)]);else if("gAMA"==s)l.tabs[s]=h.readUint(e,f)/1e5;else if("sRGB"==s)l.tabs[s]=e[f];else if("bKGD"==s)0==l.ctype||4==l.ctype?l.tabs[s]=[i(e,f)]:2==l.ctype||6==l.ctype?l.tabs[s]=[i(e,f),i(e,f+2),i(e,f+4)]:3==l.ctype&&(l.tabs[s]=e[f]);else if("IEND"==s)break;f+=r,h.readUint(e,f),f+=4}var H;return 0!=o&&((H=l.frames[l.frames.length-1]).data=b(l,d.slice(0,o),H.rect.width,H.rect.height)),l.data=b(l,m,l.width,l.height),delete l.compress,delete l.interlace,delete l.filter,l},toRGBA8:function(b){var c=b.width,d=b.height;if(null==b.tabs.acTL)return[a(b.data,c,d,b).buffer];var e=[];null==b.frames[0].data&&(b.frames[0].data=b.data);for(var f=c*d*4,g=new Uint8Array(f),i=new Uint8Array(f),j=new Uint8Array(f),k=0;k<b.frames.length;k++){var l=b.frames[k],m=l.rect.x,n=l.rect.y,o=l.rect.width,p=l.rect.height,q=a(l.data,o,p,b);if(0!=k)for(var r=0;r<f;r++)j[r]=g[r];if(0==l.blend?h(q,o,p,g,c,d,m,n,0):1==l.blend&&h(q,o,p,g,c,d,m,n,1),e.push(g.buffer.slice(0)),0==l.dispose);else if(1==l.dispose)h(i,o,p,g,c,d,m,n,0);else if(2==l.dispose)for(r=0;r<f;r++)g[r]=j[r]}return e},_paeth:f,_copyTile:h,_bin:i}}();!function(){function a(a,b,c,d){b[c]+=a[0]*d>>4,b[c+1]+=a[1]*d>>4,b[c+2]+=a[2]*d>>4,b[c+3]+=a[3]*d>>4}function b(a){return Math.max(0,Math.min(255,a))}function c(a,b){var c=a[0]-b[0],d=a[1]-b[1],e=a[2]-b[2],f=a[3]-b[3];return c*c+d*d+e*e+f*f}function d(d,e,f,g,h,i,j){null==j&&(j=1);for(var k=g.length,l=[],m=0;m<k;m++){var n=g[m];l.push([n>>>0&255,n>>>8&255,n>>>16&255,n>>>24&255])}for(m=0;m<k;m++)for(var o=4294967295,p=0,q=0;q<k;q++){var r=c(l[m],l[q]);q!=m&&r<o&&(o=r,p=q)}var s=new Uint32Array(h.buffer),t=new Int16Array(e*f*4),u=[0,8,2,10,12,4,14,6,3,11,1,9,15,7,13,5];for(m=0;m<u.length;m++)u[m]=255*((u[m]+.5)/16-.5);for(var v=0;v<f;v++)for(var w=0;w<e;w++){var x;m=4*(v*e+w),2!=j?x=[b(d[m]+t[m]),b(d[m+1]+t[m+1]),b(d[m+2]+t[m+2]),b(d[m+3]+t[m+3])]:(r=u[4*(3&v)+(3&w)],x=[b(d[m]+r),b(d[m+1]+r),b(d[m+2]+r),b(d[m+3]+r)]),p=0;var y=16777215;for(q=0;q<k;q++){var z=c(x,l[q]);z<y&&(y=z,p=q)}var A=l[p],B=[x[0]-A[0],x[1]-A[1],x[2]-A[2],x[3]-A[3]];1==j&&(w!=e-1&&a(B,t,m+4,7),v!=f-1&&(0!=w&&a(B,t,m+4*e-4,3),a(B,t,m+4*e,5),w!=e-1&&a(B,t,m+4*e+4,1))),i[m>>2]=p,s[m>>2]=g[p]}}function e(a,b,c,d,e){null==e&&(e={});var f,g=w.crc,h=u.writeUint,i=u.writeUshort,j=u.writeASCII,k=8,l=a.frames.length>1,m=!1,n=33+(l?20:0);if(null!=e.sRGB&&(n+=13),null!=e.pHYs&&(n+=21),null!=e.iCCP&&(f=pako.deflate(e.iCCP),n+=21+f.length+4),3==a.ctype){for(var o=a.plte.length,p=0;p<o;p++)a.plte[p]>>>24!=255&&(m=!0);n+=8+3*o+4+(m?8+1*o+4:0)}for(var q=0;q<a.frames.length;q++)l&&(n+=38),n+=(C=a.frames[q]).cimg.length+12,0!=q&&(n+=4);n+=12;var r=new Uint8Array(n),s=[137,80,78,71,13,10,26,10];for(p=0;p<8;p++)r[p]=s[p];if(h(r,k,13),k+=4,j(r,k,"IHDR"),k+=4,h(r,k,b),k+=4,h(r,k,c),k+=4,r[k]=a.depth,k++,r[k]=a.ctype,k++,r[k]=0,k++,r[k]=0,k++,r[k]=0,k++,h(r,k,g(r,k-17,17)),k+=4,null!=e.sRGB&&(h(r,k,1),k+=4,j(r,k,"sRGB"),k+=4,r[k]=e.sRGB,k++,h(r,k,g(r,k-5,5)),k+=4),null!=e.iCCP){var t=13+f.length;h(r,k,t),k+=4,j(r,k,"iCCP"),k+=4,j(r,k,"ICC profile"),k+=11,k+=2,r.set(f,k),k+=f.length,h(r,k,g(r,k-(t+4),t+4)),k+=4}if(null!=e.pHYs&&(h(r,k,9),k+=4,j(r,k,"pHYs"),k+=4,h(r,k,e.pHYs[0]),k+=4,h(r,k,e.pHYs[1]),k+=4,r[k]=e.pHYs[2],k++,h(r,k,g(r,k-13,13)),k+=4),l&&(h(r,k,8),k+=4,j(r,k,"acTL"),k+=4,h(r,k,a.frames.length),k+=4,h(r,k,null!=e.loop?e.loop:0),k+=4,h(r,k,g(r,k-12,12)),k+=4),3==a.ctype){for(h(r,k,3*(o=a.plte.length)),k+=4,j(r,k,"PLTE"),k+=4,p=0;p<o;p++){var v=3*p,x=a.plte[p],y=255&x,z=x>>>8&255,A=x>>>16&255;r[k+v+0]=y,r[k+v+1]=z,r[k+v+2]=A}if(k+=3*o,h(r,k,g(r,k-3*o-4,3*o+4)),k+=4,m){for(h(r,k,o),k+=4,j(r,k,"tRNS"),k+=4,p=0;p<o;p++)r[k+p]=a.plte[p]>>>24&255;k+=o,h(r,k,g(r,k-o-4,o+4)),k+=4}}var B=0;for(q=0;q<a.frames.length;q++){var C=a.frames[q];l&&(h(r,k,26),k+=4,j(r,k,"fcTL"),k+=4,h(r,k,B++),k+=4,h(r,k,C.rect.width),k+=4,h(r,k,C.rect.height),k+=4,h(r,k,C.rect.x),k+=4,h(r,k,C.rect.y),k+=4,i(r,k,d[q]),k+=2,i(r,k,1e3),k+=2,r[k]=C.dispose,k++,r[k]=C.blend,k++,h(r,k,g(r,k-30,30)),k+=4);var D=C.cimg;h(r,k,(o=D.length)+(0==q?0:4)),k+=4;var E=k;j(r,k,0==q?"IDAT":"fdAT"),k+=4,0!=q&&(h(r,k,B++),k+=4),r.set(D,k),k+=o,h(r,k,g(r,E,k-E)),k+=4}return h(r,k,0),k+=4,j(r,k,"IEND"),k+=4,h(r,k,g(r,k-4,4)),k+=4,r.buffer}function f(a,b,c){for(var d=0;d<a.frames.length;d++){var e=a.frames[d];e.rect.width;var f=e.rect.height,g=new Uint8Array(f*e.bpl+f);e.cimg=j(e.img,f,e.bpp,e.bpl,g,b,c)}}function g(a,b,c,e,f){for(var g=f[0],j=f[1],k=f[2],m=f[3],n=f[4],o=f[5],p=6,q=8,r=255,s=0;s<a.length;s++)for(var u=new Uint8Array(a[s]),v=u.length,w=0;w<v;w+=4)r&=u[w+3];var x=255!=r,y=function(a,b,c,d,e,f){for(var g=[],j=0;j<a.length;j++){var k,l=new Uint8Array(a[j]),m=new Uint32Array(l.buffer),n=0,o=0,p=b,q=c,r=d?1:0;if(0!=j){for(var s=f||d||1==j||0!=g[j-2].dispose?1:2,u=0,v=1e9,w=0;w<s;w++){for(var x=new Uint8Array(a[j-1-w]),y=new Uint32Array(a[j-1-w]),z=b,A=c,B=-1,C=-1,D=0;D<c;D++)for(var E=0;E<b;E++)m[M=D*b+E]!=y[M]&&(E<z&&(z=E),E>B&&(B=E),D<A&&(A=D),D>C&&(C=D));-1==B&&(z=A=B=C=0),e&&(1==(1&z)&&z--,1==(1&A)&&A--);var F=(B-z+1)*(C-A+1);F<v&&(v=F,u=w,n=z,o=A,p=B-z+1,q=C-A+1)}x=new Uint8Array(a[j-1-u]),1==u&&(g[j-1].dispose=2),k=new Uint8Array(p*q*4),t(x,b,c,k,p,q,-n,-o,0),r=t(l,b,c,k,p,q,-n,-o,3)?1:0,1==r?i(l,b,c,k,{x:n,y:o,width:p,height:q}):t(l,b,c,k,p,q,-n,-o,0)}else k=l.slice(0);g.push({rect:{x:n,y:o,width:p,height:q},img:k,blend:r,dispose:0})}if(d)for(j=0;j<g.length;j++)if(1!=(N=g[j]).blend){var G=N.rect,H=g[j-1].rect,I=Math.min(G.x,H.x),J=Math.min(G.y,H.y),K={x:I,y:J,width:Math.max(G.x+G.width,H.x+H.width)-I,height:Math.max(G.y+G.height,H.y+H.height)-J};g[j-1].dispose=1,j-1!=0&&h(a,b,c,g,j-1,K,e),h(a,b,c,g,j,K,e)}var L=0;if(1!=a.length)for(var M=0;M<g.length;M++){var N;L+=(N=g[M]).rect.width*N.rect.height}return g}(a,b,c,g,j,k),z={},A=[],B=[];if(0!=e){var C=[];for(w=0;w<y.length;w++)C.push(y[w].img.buffer);var D=function(a){for(var b=0,c=0;c<a.length;c++)b+=a[c].byteLength;var d=new Uint8Array(b),e=0;for(c=0;c<a.length;c++){for(var f=new Uint8Array(a[c]),g=f.length,h=0;h<g;h+=4){var i=f[h],j=f[h+1],k=f[h+2],l=f[h+3];0==l&&(i=j=k=0),d[e+h]=i,d[e+h+1]=j,d[e+h+2]=k,d[e+h+3]=l}e+=g}return d.buffer}(C),E=l(D,e);for(w=0;w<E.plte.length;w++)A.push(E.plte[w].est.rgba);var F=0;for(w=0;w<y.length;w++){var G=(J=y[w]).img.length,H=new Uint8Array(E.inds.buffer,F>>2,G>>2);B.push(H);var I=new Uint8Array(E.abuf,F,G);o&&d(J.img,J.rect.width,J.rect.height,A,I,H),J.img.set(I),F+=G}}else for(s=0;s<y.length;s++){var J=y[s],K=new Uint32Array(J.img.buffer),L=J.rect.width;for(v=K.length,H=new Uint8Array(v),B.push(H),w=0;w<v;w++){var M=K[w];if(0!=w&&M==K[w-1])H[w]=H[w-1];else if(w>L&&M==K[w-L])H[w]=H[w-L];else{var N=z[M];if(null==N&&(z[M]=N=A.length,A.push(M),A.length>=300))break;H[w]=N}}}var O=A.length;for(O<=256&&0==n&&(q=O<=2?1:O<=4?2:O<=16?4:8,q=Math.max(q,m)),s=0;s<y.length;s++){(J=y[s]).rect.x,J.rect.y,L=J.rect.width;var P=J.rect.height,Q=J.img;new Uint32Array(Q.buffer);var R=4*L,S=4;if(O<=256&&0==n){R=Math.ceil(q*L/8);for(var T=new Uint8Array(R*P),U=B[s],V=0;V<P;V++){w=V*R;var W=V*L;if(8==q)for(var X=0;X<L;X++)T[w+X]=U[W+X];else if(4==q)for(X=0;X<L;X++)T[w+(X>>1)]|=U[W+X]<<4-4*(1&X);else if(2==q)for(X=0;X<L;X++)T[w+(X>>2)]|=U[W+X]<<6-2*(3&X);else if(1==q)for(X=0;X<L;X++)T[w+(X>>3)]|=U[W+X]<<7-1*(7&X)}Q=T,p=3,S=1}else if(0==x&&1==y.length){T=new Uint8Array(L*P*3);var Y=L*P;for(w=0;w<Y;w++){var Z=3*w,$=4*w;T[Z]=Q[$],T[Z+1]=Q[$+1],T[Z+2]=Q[$+2]}Q=T,p=2,S=3,R=3*L}J.img=Q,J.bpl=R,J.bpp=S}return{ctype:p,depth:q,plte:A,frames:y}}function h(a,b,c,d,e,f,g){for(var h=Uint8Array,j=Uint32Array,k=new h(a[e-1]),l=new j(a[e-1]),m=e+1<a.length?new h(a[e+1]):null,n=new h(a[e]),o=new j(n.buffer),p=b,q=c,r=-1,s=-1,u=0;u<f.height;u++)for(var v=0;v<f.width;v++){var w=f.x+v,x=f.y+u,y=x*b+w,z=o[y];0==z||0==d[e-1].dispose&&l[y]==z&&(null==m||0!=m[4*y+3])||(w<p&&(p=w),w>r&&(r=w),x<q&&(q=x),x>s&&(s=x))}-1==r&&(p=q=r=s=0),g&&(1==(1&p)&&p--,1==(1&q)&&q--),f={x:p,y:q,width:r-p+1,height:s-q+1};var A=d[e];A.rect=f,A.blend=1,A.img=new Uint8Array(f.width*f.height*4),0==d[e-1].dispose?(t(k,b,c,A.img,f.width,f.height,-f.x,-f.y,0),i(n,b,c,A.img,f)):t(n,b,c,A.img,f.width,f.height,-f.x,-f.y,0)}function i(a,b,c,d,e){t(a,b,c,d,e.width,e.height,-e.x,-e.y,2)}function j(a,b,c,d,e,f,g){var h,i=[],j=[0,1,2,3,4];-1!=f?j=[f]:(b*d>5e5||1==c)&&(j=[0]),g&&(h={level:0});for(var l=z,m=0;m<j.length;m++){for(var n=0;n<b;n++)k(e,a,n,d,c,j[m]);i.push(l.deflate(e,h))}var o,p=1e9;for(m=0;m<i.length;m++)i[m].length<p&&(o=m,p=i[m].length);return i[o]}function k(a,b,c,d,e,f){var g=c*d,h=g+c;if(a[h]=f,h++,0==f)if(d<500)for(var i=0;i<d;i++)a[h+i]=b[g+i];else a.set(new Uint8Array(b.buffer,g,d),h);else if(1==f){for(i=0;i<e;i++)a[h+i]=b[g+i];for(i=e;i<d;i++)a[h+i]=b[g+i]-b[g+i-e]+256&255}else if(0==c){for(i=0;i<e;i++)a[h+i]=b[g+i];if(2==f)for(i=e;i<d;i++)a[h+i]=b[g+i];if(3==f)for(i=e;i<d;i++)a[h+i]=b[g+i]-(b[g+i-e]>>1)+256&255;if(4==f)for(i=e;i<d;i++)a[h+i]=b[g+i]-v(b[g+i-e],0,0)+256&255}else{if(2==f)for(i=0;i<d;i++)a[h+i]=b[g+i]+256-b[g+i-d]&255;if(3==f){for(i=0;i<e;i++)a[h+i]=b[g+i]+256-(b[g+i-d]>>1)&255;for(i=e;i<d;i++)a[h+i]=b[g+i]+256-(b[g+i-d]+b[g+i-e]>>1)&255}if(4==f){for(i=0;i<e;i++)a[h+i]=b[g+i]+256-v(0,b[g+i-d],0)&255;for(i=e;i<d;i++)a[h+i]=b[g+i]+256-v(b[g+i-e],b[g+i-d],b[g+i-e-d])&255}}}function l(a,b){var c,d=new Uint8Array(a),e=d.slice(0),f=new Uint32Array(e.buffer),g=m(e,b),h=g[0],i=g[1],j=d.length,k=new Uint8Array(j>>2);if(d.length<2e7)for(var l=0;l<j;l+=4)c=n(h,p=d[l]*(1/255),q=d[l+1]*(1/255),r=d[l+2]*(1/255),s=d[l+3]*(1/255)),k[l>>2]=c.ind,f[l>>2]=c.est.rgba;else for(l=0;l<j;l+=4){var p=d[l]*(1/255),q=d[l+1]*(1/255),r=d[l+2]*(1/255),s=d[l+3]*(1/255);for(c=h;c.left;)c=o(c.est,p,q,r,s)<=0?c.left:c.right;k[l>>2]=c.ind,f[l>>2]=c.est.rgba}return{abuf:e.buffer,inds:k,plte:i}}function m(a,b,c){null==c&&(c=1e-4);var d=new Uint32Array(a.buffer),e={i0:0,i1:a.length,bst:null,est:null,tdst:0,left:null,right:null};e.bst=r(a,e.i0,e.i1),e.est=s(e.bst);for(var f=[e];f.length<b;){for(var g=0,h=0,i=0;i<f.length;i++)f[i].est.L>g&&(g=f[i].est.L,
h=i);if(g<c)break;var j=f[h],k=p(a,d,j.i0,j.i1,j.est.e,j.est.eMq255);if(j.i0>=k||j.i1<=k)j.est.L=0;else{var l={i0:j.i0,i1:k,bst:null,est:null,tdst:0,left:null,right:null};l.bst=r(a,l.i0,l.i1),l.est=s(l.bst);var m={i0:k,i1:j.i1,bst:null,est:null,tdst:0,left:null,right:null};for(m.bst={R:[],m:[],N:j.bst.N-l.bst.N},i=0;i<16;i++)m.bst.R[i]=j.bst.R[i]-l.bst.R[i];for(i=0;i<4;i++)m.bst.m[i]=j.bst.m[i]-l.bst.m[i];m.est=s(m.bst),j.left=l,j.right=m,f[h]=l,f.push(m)}}for(f.sort(function(a,b){return b.bst.N-a.bst.N}),i=0;i<f.length;i++)f[i].ind=i;return[e,f]}function n(a,b,c,d,e){if(null==a.left)return a.tdst=function(a,b,c,d,e){var f=b-a[0],g=c-a[1],h=d-a[2],i=e-a[3];return f*f+g*g+h*h+i*i}(a.est.q,b,c,d,e),a;var f=o(a.est,b,c,d,e),g=a.left,h=a.right;f>0&&(g=a.right,h=a.left);var i=n(g,b,c,d,e);if(i.tdst<=f*f)return i;var j=n(h,b,c,d,e);return j.tdst<i.tdst?j:i}function o(a,b,c,d,e){var f=a.e;return f[0]*b+f[1]*c+f[2]*d+f[3]*e-a.eMq}function p(a,b,c,d,e,f){for(d-=4;c<d;){for(;q(a,c,e)<=f;)c+=4;for(;q(a,d,e)>f;)d-=4;if(c>=d)break;var g=b[c>>2];b[c>>2]=b[d>>2],b[d>>2]=g,c+=4,d-=4}for(;q(a,c,e)>f;)c-=4;return c+4}function q(a,b,c){return a[b]*c[0]+a[b+1]*c[1]+a[b+2]*c[2]+a[b+3]*c[3]}function r(a,b,c){for(var d=[0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0],e=[0,0,0,0],f=c-b>>2,g=b;g<c;g+=4){var h=a[g]*(1/255),i=a[g+1]*(1/255),j=a[g+2]*(1/255),k=a[g+3]*(1/255);e[0]+=h,e[1]+=i,e[2]+=j,e[3]+=k,d[0]+=h*h,d[1]+=h*i,d[2]+=h*j,d[3]+=h*k,d[5]+=i*i,d[6]+=i*j,d[7]+=i*k,d[10]+=j*j,d[11]+=j*k,d[15]+=k*k}return d[4]=d[1],d[8]=d[2],d[9]=d[6],d[12]=d[3],d[13]=d[7],d[14]=d[11],{R:d,m:e,N:f}}function s(a){var b=a.R,c=a.m,d=a.N,e=c[0],f=c[1],g=c[2],h=c[3],i=0==d?0:1/d,j=[b[0]-e*e*i,b[1]-e*f*i,b[2]-e*g*i,b[3]-e*h*i,b[4]-f*e*i,b[5]-f*f*i,b[6]-f*g*i,b[7]-f*h*i,b[8]-g*e*i,b[9]-g*f*i,b[10]-g*g*i,b[11]-g*h*i,b[12]-h*e*i,b[13]-h*f*i,b[14]-h*g*i,b[15]-h*h*i],k=j,l=x,m=[Math.random(),Math.random(),Math.random(),Math.random()],n=0,o=0;if(0!=d)for(var p=0;p<16&&(m=l.multVec(k,m),o=Math.sqrt(l.dot(m,m)),m=l.sml(1/o,m),!(0!=p&&Math.abs(o-n)<1e-9));p++)n=o;var q=[e*i,f*i,g*i,h*i];return{Cov:j,q:q,e:m,L:n,eMq255:l.dot(l.sml(255,q),m),eMq:l.dot(m,q),rgba:(Math.round(255*q[3])<<24|Math.round(255*q[2])<<16|Math.round(255*q[1])<<8|Math.round(255*q[0])<<0)>>>0}}var t=A._copyTile,u=A._bin,v=A._paeth,w={table:function(){for(var a=new Uint32Array(256),b=0;b<256;b++){for(var c=b,d=0;d<8;d++)1&c?c=3988292384^c>>>1:c>>>=1;a[b]=c}return a}(),update:function(a,b,c,d){for(var e=0;e<d;e++)a=w.table[255&(a^b[c+e])]^a>>>8;return a},crc:function(a,b,c){return 4294967295^w.update(4294967295,a,b,c)}},x={multVec:function(a,b){return[a[0]*b[0]+a[1]*b[1]+a[2]*b[2]+a[3]*b[3],a[4]*b[0]+a[5]*b[1]+a[6]*b[2]+a[7]*b[3],a[8]*b[0]+a[9]*b[1]+a[10]*b[2]+a[11]*b[3],a[12]*b[0]+a[13]*b[1]+a[14]*b[2]+a[15]*b[3]]},dot:function(a,b){return a[0]*b[0]+a[1]*b[1]+a[2]*b[2]+a[3]*b[3]},sml:function(a,b){return[a*b[0],a*b[1],a*b[2],a*b[3]]}};A.encode=function(a,b,c,d,h,i,j){null==d&&(d=0),null==j&&(j=!1);var k=g(a,b,c,d,[!1,!1,!1,0,j,!1]);return f(k,-1),e(k,b,c,h,i)},A.encodeLL=function(a,b,c,d,g,h,i,j){for(var k={ctype:0+(1==d?0:2)+(0==g?0:4),depth:h,frames:[]},l=(d+g)*h,m=l*b,n=0;n<a.length;n++)k.frames.push({rect:{x:0,y:0,width:b,height:c},img:new Uint8Array(a[n]),blend:0,dispose:1,bpp:Math.ceil(l/8),bpl:Math.ceil(m/8)});return f(k,0,!0),e(k,b,c,i,j)},A.encode.compress=g,A.encode.dither=d,A.quantize=l,A.quantize.getKDtree=m,A.quantize.getNearest=n}();var B,C={toArrayBuffer:function(a,b){function c(a){s.setUint16(w,a,!0),w+=2}function d(a){s.setUint32(w,a,!0),w+=4}function e(a){w+=a}var f,g,h,i,j=a.width,k=a.height,l=j<<2,m=a.getContext("2d").getImageData(0,0,j,k),n=new Uint32Array(m.data.buffer),o=(32*j+31)/32<<2,p=o*k,q=122+p,r=new ArrayBuffer(q),s=new DataView(r),t=1<<20,u=t,v=0,w=0,x=0;c(19778),d(q),e(4),d(122),d(108),d(j),d(-k>>>0),c(1),c(32),d(3),d(p),d(2835),d(2835),e(8),d(16711680),d(65280),d(255),d(4278190080),d(1466527264),function y(){for(;v<k&&u>0;){for(i=122+v*o,f=0;f<l;)u--,g=n[x++],h=g>>>24,s.setUint32(i+f,g<<8|h),f+=4;v++}x<n.length?(u=t,setTimeout(y,C._dly)):b(r)}()},toBlob:function(b,c){this.toArrayBuffer(b,function(b){c(new a.Blob([b],{type:"image/bmp"}))})},_dly:9},D={CHROME:"CHROME",FIREFOX:"FIREFOX",DESKTOP_SAFARI:"DESKTOP_SAFARI",IE:"IE",IOS:"IOS",ETC:"ETC"},E=(v={},v[D.CHROME]=16384,v[D.FIREFOX]=11180,v[D.DESKTOP_SAFARI]=16384,v[D.IE]=8192,v[D.IOS]=4096,v[D.ETC]=8192,v),F="undefined"!=typeof a,G="undefined"!=typeof WorkerGlobalScope&&self instanceof WorkerGlobalScope,H=F&&a.cordova&&a.cordova.require&&a.cordova.require("cordova/modulemapper"),I=(F||G)&&(H&&H.getOriginalSymbol(a,"File")||"undefined"!=typeof File&&File),J=(F||G)&&(H&&H.getOriginalSymbol(a,"FileReader")||"undefined"!=typeof FileReader&&FileReader),K="\nlet scriptImported = false\nself.addEventListener('message', async (e) => {\n  const { file, id, imageCompressionLibUrl, options } = e.data\n  options.onProgress = (progress) => self.postMessage({ progress, id })\n  try {\n    if (!scriptImported) {\n      // console.log('[worker] importScripts', imageCompressionLibUrl)\n      self.importScripts(imageCompressionLibUrl)\n      scriptImported = true\n    }\n    // console.log('[worker] self', self)\n    const compressedFile = await imageCompression(file, options)\n    self.postMessage({ file: compressedFile, id })\n  } catch (e) {\n    // console.error('[worker] error', e)\n    self.postMessage({ error: e.message + '\\n' + e.stack, id })\n  }\n})\n";return u.getDataUrlFromFile=e,u.getFilefromDataUrl=d,u.loadImage=f,u.drawImageInCanvas=j,u.drawFileInCanvas=l,u.canvasToFile=m,u.getExifOrientation=p,u.handleMaxWidthOrHeight=q,u.followExifOrientation=r,u.cleanupCanvasMemory=n,u.isAutoOrientationInBrowser=o,u.approximateBelowMaximumCanvasSizeOfBrowser=h,u.copyExifWithoutOrientation=c,u.getBrowserName=g,u.version="2.0.2",u}),b("widgets/image",["base","uploader","lib/image","lib/browser-image-compression","widgets/widget"],function(a,b,c,d){var e,f=a.$;return e=function(a){var b=0,c=[],d=function(){for(var d;c.length&&b<a;)d=c.shift(),b+=d[0],d[1]()};return function(a,e,f){c.push([e,f]),a.once("destroy",function(){b-=e,setTimeout(d,1)}),setTimeout(d,1)}}(5242880),f.extend(b.options,{thumb:{width:110,height:110,quality:70,allowMagnify:!0,crop:!0,preserveHeaders:!1,type:"image/jpeg"},compress:{enable:!1,maxWidthOrHeight:4e3,maxSize:10485760}}),b.register({name:"image",makeThumb:function(a,b,d,g){var h,i;return a=this.request("get-file",a),a.type.match(/^image/)?(h=f.extend({},this.options.thumb),f.isPlainObject(d)&&(h=f.extend(h,d),d=null),d=d||h.width,g=g||h.height,i=new c(h),i.once("load",function(){a._info=a._info||i.info(),a._meta=a._meta||i.meta(),d<=1&&d>0&&(d=a._info.width*d),g<=1&&g>0&&(g=a._info.height*g),i.resize(d,g)}),i.once("complete",function(){b(!1,i.getAsDataUrl(h.type)),i.destroy()}),i.once("error",function(a){b(a||!0),i.destroy()}),void e(i,a.source.size,function(){a._info&&i.info(a._info),a._meta&&i.meta(a._meta),i.loadFromBlob(a.source)})):void b(!0)},beforeSendFile:function(b){var c,e=this.options.compress,g=this;if(b=this.request("get-file",b),!b._widgetImageData){var h={processed:!1,success:!1,originalSize:b.size};return e&&e.enable&&~"image/jpeg,image/jpg,image/png".indexOf(b.type)?(e=f.extend({},e),c=a.Deferred(),g.owner.trigger("fileProcessStart","imageCompress",b),d(b.source.source,{maxSizeMB:e.maxSize/1024/1024,maxWidthOrHeight:e.maxWidthOrHeight}).then(function(a){g.owner.trigger("fileProcessEnd","imageCompress",b),e.debug&&console.log("webuploader.compress.output",(a.size/b.size*100).toFixed(2)+"%");var d=b.size;b.source.source=a,b.source.size=a.size,b.size=a.size,b.trigger("resize",a.size,d),h.processed=!0,h.success=!0,b._widgetImageData=h,c.resolve()})["catch"](function(a){console.warn("webuploader.compress.error",a),g.owner.trigger("fileProcessEnd","imageCompress",b),h.processed=!0,b._widgetImageData=h,c.resolve()}),c.promise()):void(b._widgetImageData=h)}}})}),b("file",["base","mediator"],function(a,b){function c(){return f+g++}function d(a){this.name=a.name||"Untitled",this.size=a.size||0,this.type=a.type||"application/octet-stream",this.lastModifiedDate=a.lastModifiedDate||1*new Date,this.id=c(),this.ext=h.exec(this.name)?RegExp.$1:"",this.statusText="",i[this.id]=d.Status.INITED,this.source=a,this.loaded=0,this.on("error",function(a){this.setStatus(d.Status.ERROR,a)})}var e=a.$,f="WU_FILE_",g=0,h=/\.([^.]+)$/,i={};return e.extend(d.prototype,{setStatus:function(a,b){var c=i[this.id];"undefined"!=typeof b&&(this.statusText=b),a!==c&&(i[this.id]=a,this.trigger("statuschange",a,c))},getStatus:function(){return i[this.id]},getSource:function(){return this.source},destroy:function(){this.off(),delete i[this.id]}}),b.installTo(d.prototype),d.Status={INITED:"inited",QUEUED:"queued",PROGRESS:"progress",ERROR:"error",COMPLETE:"complete",CANCELLED:"cancelled",INTERRUPT:"interrupt",INVALID:"invalid"},d}),b("queue",["base","mediator","file"],function(a,b,c){function d(){this.stats={numOfQueue:0,numOfSuccess:0,numOfCancel:0,numOfProgress:0,numOfUploadFailed:0,numOfInvalid:0,numOfDeleted:0,numOfInterrupt:0},this._queue=[],this._map={}}var e=a.$,f=c.Status;return e.extend(d.prototype,{append:function(a){return this._queue.push(a),this._fileAdded(a),this},prepend:function(a){return this._queue.unshift(a),this._fileAdded(a),this},getFile:function(a){return"string"!=typeof a?a:this._map[a]},fetch:function(a){var b,c,d=this._queue.length;for(a=a||f.QUEUED,b=0;b<d;b++)if(c=this._queue[b],a===c.getStatus())return c;return null},sort:function(a){"function"==typeof a&&this._queue.sort(a)},getFiles:function(){for(var a,b=[].slice.call(arguments,0),c=[],d=0,f=this._queue.length;d<f;d++)a=this._queue[d],b.length&&!~e.inArray(a.getStatus(),b)||c.push(a);return c},removeFile:function(a){var b=this._map[a.id];b&&(delete this._map[a.id],this._delFile(a),a.destroy(),this.stats.numOfDeleted++)},_fileAdded:function(a){var b=this,c=this._map[a.id];c||(this._map[a.id]=a,a.on("statuschange",function(a,c){b._onFileStatusChange(a,c)})),a.setStatus(f.QUEUED)},_delFile:function(a){for(var b=this._queue.length-1;b>=0;b--)if(this._queue[b]==a){this._queue.splice(b,1);break}},_onFileStatusChange:function(a,b){var c=this.stats;switch(b){case f.PROGRESS:c.numOfProgress--;break;case f.QUEUED:c.numOfQueue--;break;case f.ERROR:c.numOfUploadFailed--;break;case f.INVALID:c.numOfInvalid--;break;case f.INTERRUPT:c.numOfInterrupt--}switch(a){case f.QUEUED:c.numOfQueue++;break;case f.PROGRESS:c.numOfProgress++;break;case f.ERROR:c.numOfUploadFailed++;break;case f.COMPLETE:c.numOfSuccess++;break;case f.CANCELLED:c.numOfCancel++;break;case f.INVALID:c.numOfInvalid++;break;case f.INTERRUPT:c.numOfInterrupt++}}}),b.installTo(d.prototype),d}),b("widgets/queue",["base","uploader","queue","file","lib/file","runtime/client","widgets/widget"],function(a,b,c,d,e,f){var g=a.$,h=/\.\w+$/,i=d.Status;return b.register({name:"queue",init:function(b){var d,e,h,i,j,k,l,m=this;if(g.isPlainObject(b.accept)&&(b.accept=[b.accept]),b.accept){for(j=[],h=0,e=b.accept.length;h<e;h++)i=b.accept[h].extensions,i&&j.push(i);j.length&&(k="\\."+j.join(",").replace(/,/g,"$|\\.").replace(/\*/g,".*")+"$"),m.accept=new RegExp(k,"i")}if(m.queue=new c,m.stats=m.queue.stats,"html5"===this.request("predict-runtime-type"))return d=a.Deferred(),this.placeholder=l=new f("Placeholder"),l.connectRuntime({runtimeOrder:"html5"},function(){m._ruid=l.getRuid(),d.resolve()}),d.promise()},_wrapFile:function(a){if(!(a instanceof d)){if(!(a instanceof e)){if(!this._ruid)throw new Error("Can't add external files.");a=new e(this._ruid,a)}a=new d(a)}return a},acceptFile:function(a){var b=!a||!a.size||this.accept&&h.exec(a.name)&&!this.accept.test(a.name);return!b},_addFile:function(a){var b=this;if(a=b._wrapFile(a),b.owner.trigger("beforeFileQueued",a))return b.acceptFile(a)?(b.queue.append(a),b.owner.trigger("fileQueued",a),a):void b.owner.trigger("error","Q_TYPE_DENIED",a)},getFile:function(a){return this.queue.getFile(a)},addFile:function(a){var b=this;a.length||(a=[a]),a=g.map(a,function(a){return b._addFile(a)}),a.length&&(b.owner.trigger("filesQueued",a),b.options.auto&&setTimeout(function(){b.request("start-upload")},20))},getStats:function(){return this.stats},removeFile:function(a,b){var c=this;a=a.id?a:c.queue.getFile(a),this.request("cancel-file",a),b&&this.queue.removeFile(a)},getFiles:function(){return this.queue.getFiles.apply(this.queue,arguments)},fetchFile:function(){return this.queue.fetch.apply(this.queue,arguments)},retry:function(a,b){var c,d,e,f=this;if(a)return a=a.id?a:f.queue.getFile(a),a.setStatus(i.QUEUED),void(b||f.request("start-upload"));for(c=f.queue.getFiles(i.ERROR),d=0,e=c.length;d<e;d++)a=c[d],a.setStatus(i.QUEUED);f.request("start-upload")},sortFiles:function(){return this.queue.sort.apply(this.queue,arguments)},reset:function(){this.owner.trigger("reset"),this.queue=new c,this.stats=this.queue.stats},destroy:function(){this.reset(),this.placeholder&&this.placeholder.destroy()}})}),b("widgets/runtime",["uploader","runtime/runtime","widgets/widget"],function(a,b){return a.support=function(){return b.hasRuntime.apply(b,arguments)},a.register({name:"runtime",init:function(){if(!this.predictRuntimeType())throw Error("Runtime Error")},predictRuntimeType:function(){var a,c,d=this.options.runtimeOrder||b.orders,e=this.type;if(!e)for(d=d.split(/\s*,\s*/g),a=0,c=d.length;a<c;a++)if(b.hasRuntime(d[a])){this.type=e=d[a];break}return e}})}),b("lib/transport",["base","runtime/client","mediator"],function(a,b,c){function d(a){var c=this;a=c.options=e.extend(!0,{},d.options,a||{}),b.call(this,"Transport"),this._block=null,this._blob=null,this._formData=a.formData||{},this._headers=a.headers||{},this.on("progress",this._timeout),this.on("load error",function(){c.trigger("progress",1),clearTimeout(c._timer)})}var e=a.$;return d.options={server:"",method:"POST",withCredentials:!1,fileVal:"file",timeout:12e4,formData:{},headers:{},sendAsBinary:!1,customUploadResponse:null},e.extend(d.prototype,{appendBlob:function(a,b,c,d){var e=this,f=e.options;e.getRuid()&&e.disconnectRuntime(),e.connectRuntime(b.ruid,function(){e.exec("init")}),e._block=d,e._blob=b,f.fileVal=a||f.fileVal,f.filename=c||f.filename},append:function(a,b){"object"==typeof a?e.extend(this._formData,a):this._formData[a]=b},setRequestHeader:function(a,b){"object"==typeof a?e.extend(this._headers,a):this._headers[a]=b},send:function(a){var b=this,c=b.options;c.customUpload?c.customUpload(b._block,{onProgress:function(a,c){b.trigger("progress",c)},onSuccess:function(a,c){b.customUploadResponse=c,b.trigger("load")},onError:function(a,c){b.trigger("error",c,!0)}}):(this.exec("send",a),this._timeout())},abort:function(){return clearTimeout(this._timer),this.exec("abort")},destroy:function(){this.trigger("destroy"),this.off(),this.exec("destroy"),this.disconnectRuntime()},getResponseHeaders:function(){return this.exec("getResponseHeaders")},getResponse:function(){return this.exec("getResponse")},getResponseAsJson:function(){return this.exec("getResponseAsJson")},getStatus:function(){return this.exec("getStatus")},_timeout:function(){var a=this,b=a.options.timeout;b&&(clearTimeout(a._timer),a._timer=setTimeout(function(){a.abort(),a.trigger("error","timeout")},b))}}),c.installTo(d.prototype),d}),b("widgets/upload",["base","uploader","file","lib/transport","widgets/widget"],function(a,b,c,d){function e(a,b){var c,d,e=[],f=a.source,g=f.size,h=b?Math.ceil(g/b):1,i=0,j=0;for(d={file:a,has:function(){return!!e.length},shift:function(){return e.shift()},unshift:function(a){e.unshift(a)}};j<h;)c=Math.min(b,g-i),e.push({file:a,start:i,end:b?i+c:g,total:g,chunks:h,chunk:j++,cuted:d}),i+=c;return a.blocks=e.concat(),a.remaning=e.length,d}var f=a.$,g=a.isPromise,h=c.Status;f.extend(b.options,{prepareNextFile:!1,chunked:!1,chunkSize:5242880,chunkRetry:2,chunkRetryDelay:1e3,threads:1,formData:{}}),b.register({name:"upload",init:function(){var b=this.owner,c=this;this.runing=!1,this.progress=!1,b.on("startUpload",function(){c.progress=!0}).on("uploadFinished",function(){c.progress=!1}),this.pool=[],this.stack=[],this.pending=[],this.remaning=0,this.__tick=a.bindFn(this._tick,this),b.on("uploadComplete",function(a){a.blocks&&f.each(a.blocks,function(a,b){b.transport&&(b.transport.abort(),b.transport.destroy()),delete b.transport}),delete a.blocks,delete a.remaning})},reset:function(){this.request("stop-upload",!0),this.runing=!1,this.pool=[],this.stack=[],this.pending=[],this.remaning=0,this._trigged=!1,this._promise=null},startUpload:function(b){var c=this;if(f.each(c.request("get-files",h.INVALID),function(){c.request("remove-file",this)}),b?(b=b.id?b:c.request("get-file",b),b.getStatus()===h.INTERRUPT?(b.setStatus(h.QUEUED),f.each(c.pool,function(a,c){c.file===b&&(c.transport&&c.transport.send(),b.setStatus(h.PROGRESS))})):b.getStatus()!==h.PROGRESS&&b.setStatus(h.QUEUED)):f.each(c.request("get-files",[h.INITED]),function(){this.setStatus(h.QUEUED)}),c.runing)return c.owner.trigger("startUpload",b),a.nextTick(c.__tick);c.runing=!0;var d=[];b||f.each(c.pool,function(a,b){var e=b.file;if(e.getStatus()===h.INTERRUPT){if(c._trigged=!1,d.push(e),b.waiting)return;b.transport?b.transport.send():c._doSend(b)}}),f.each(d,function(){this.setStatus(h.PROGRESS)}),b||f.each(c.request("get-files",h.INTERRUPT),function(){this.setStatus(h.PROGRESS)}),c._trigged=!1,a.nextTick(c.__tick),c.owner.trigger("startUpload")},stopUpload:function(b,c){var d=this;if(b===!0&&(c=b,b=null),d.runing!==!1){if(b){if(b=b.id?b:d.request("get-file",b),b.getStatus()!==h.PROGRESS&&b.getStatus()!==h.QUEUED)return;return b.setStatus(h.INTERRUPT),f.each(d.pool,function(a,e){e.file===b&&(e.transport&&e.transport.abort(),c&&(d._putback(e),d._popBlock(e)))}),d.owner.trigger("stopUpload",b),a.nextTick(d.__tick)}d.runing=!1,this._promise&&this._promise.file&&this._promise.file.setStatus(h.INTERRUPT),c&&f.each(d.pool,function(a,b){b.transport&&b.transport.abort(),b.file.setStatus(h.INTERRUPT)}),d.owner.trigger("stopUpload")}},cancelFile:function(a){a=a.id?a:this.request("get-file",a),a.blocks&&f.each(a.blocks,function(a,b){var c=b.transport;c&&(c.abort(),c.destroy(),delete b.transport)}),a.setStatus(h.CANCELLED),this.owner.trigger("fileDequeued",a)},isInProgress:function(){return!!this.progress},_getStats:function(){return this.request("get-stats")},skipFile:function(a,b){a=a.id?a:this.request("get-file",a),a.setStatus(b||h.COMPLETE),a.skipped=!0,a.blocks&&f.each(a.blocks,function(a,b){var c=b.transport;c&&(c.abort(),c.destroy(),delete b.transport)}),this.owner.trigger("uploadSkip",a)},_tick:function(){var b,c,d=this,e=d.options;return d._promise?d._promise.always(d.__tick):void(d.pool.length<e.threads&&(c=d._nextBlock())?(d._trigged=!1,b=function(b){d._promise=null,b&&b.file&&d._startSend(b),a.nextTick(d.__tick)},d._promise=g(c)?c.always(b):b(c)):d.remaning||d._getStats().numOfQueue||d._getStats().numOfInterrupt||(d.runing=!1,d._trigged||a.nextTick(function(){d.owner.trigger("uploadFinished")}),d._trigged=!0))},_putback:function(a){var b;a.cuted.unshift(a),b=this.stack.indexOf(a.cuted),~b||(this.remaning++,a.file.remaning++,this.stack.unshift(a.cuted))},_getStack:function(){for(var a,b=0;a=this.stack[b++];){if(a.has()&&a.file.getStatus()===h.PROGRESS)return a;(!a.has()||a.file.getStatus()!==h.PROGRESS&&a.file.getStatus()!==h.INTERRUPT)&&this.stack.splice(--b,1)}return null},_nextBlock:function(){var a,b,c,d,f=this,h=f.options;return(a=this._getStack())?(h.prepareNextFile&&!f.pending.length&&f._prepareNextFile(),a.shift()):f.runing?(!f.pending.length&&f._getStats().numOfQueue&&f._prepareNextFile(),b=f.pending.shift(),c=function(b){return b?(a=h.customUpload?e(b,0):e(b,h.chunked?h.chunkSize:0),f.stack.push(a),a.shift()):null},g(b)?(d=b.file,b=b[b.pipe?"pipe":"then"](c),b.file=d,b):c(b)):void 0},_prepareNextFile:function(){var a,b=this,c=b.request("fetch-file"),d=b.pending;c&&(a=b.request("before-send-file",c,function(){return c.getStatus()===h.PROGRESS||c.getStatus()===h.INTERRUPT?c:b._finishFile(c)}),b.owner.trigger("uploadStart",c),c.setStatus(h.PROGRESS),a.file=c,a.done(function(){var b=f.inArray(a,d);~b&&d.splice(b,1,c)}),a.fail(function(a){c.setStatus(h.ERROR,a),b.owner.trigger("uploadError",c,a),b.owner.trigger("uploadComplete",c)}),d.push(a))},_popBlock:function(a){var b=f.inArray(a,this.pool);this.pool.splice(b,1),a.file.remaning--,this.remaning--},_startSend:function(b){var c,d=this,e=b.file;return e.getStatus()!==h.PROGRESS?void(e.getStatus()===h.INTERRUPT&&d._putback(b)):(d.pool.push(b),d.remaning++,b.blob=1===b.chunks?e.source:e.source.slice(b.start,b.end),b.waiting=c=d.request("before-send",b,function(){delete b.waiting,e.getStatus()===h.PROGRESS?d._doSend(b):b.file.getStatus()!==h.INTERRUPT&&d._popBlock(b),a.nextTick(d.__tick)}),void c.fail(function(){delete b.waiting,1===e.remaning?d._finishFile(e).always(function(){b.percentage=1,d._popBlock(b),d.owner.trigger("uploadComplete",e),a.nextTick(d.__tick)}):(b.percentage=1,d.updateFileProgress(e),d._popBlock(b),a.nextTick(d.__tick))}))},_doSend:function(b){var c,e,g=this,i=g.owner,j=f.extend({},g.options,b.options),k=b.file,l=new d(j),m=f.extend({},j.formData),n=f.extend({},j.headers);b.transport=l,l.on("destroy",function(){delete b.transport,g._popBlock(b),a.nextTick(g.__tick)}),l.on("progress",function(a){b.percentage=a,g.updateFileProgress(k)}),c=function(a){var c;return j.customUpload?e=l.customUploadResponse:(e=l.getResponseAsJson()||{},e._raw=l.getResponse(),e._headers=l.getResponseHeaders()),b.response=e,c=function(b){a=b},i.trigger("uploadAccept",b,e,c)||(a=a||"server"),a},l.on("error",function(a,d){var e,f,m=a.split("|");a=m[0],e=parseFloat(m[1]),f=m[2],b.retried=b.retried||0,b.chunks>1&&~"http,abort,server".indexOf(a.replace(/-.*/,""))&&b.retried<j.chunkRetry?(b.retried++,g.retryTimer=setTimeout(function(){l.send()},j.chunkRetryDelay||1e3)):(d||"server"!==a||(a=c(a)),k.setStatus(h.ERROR,a),i.trigger("uploadError",k,a,e,f),i.trigger("uploadComplete",k))}),l.on("load",function(){var a;return(a=c())?void l.trigger("error",a,!0):void(1===k.remaning?g._finishFile(k,e):l.destroy())}),m=f.extend(m,{id:k.id,name:k.name,type:k.type,lastModifiedDate:k.lastModifiedDate,size:k.size}),b.chunks>1&&f.extend(m,{chunks:b.chunks,chunk:b.chunk}),i.trigger("uploadBeforeSend",b,m,n),l.appendBlob(j.fileVal,b.blob,k.name,b),l.append(m),l.setRequestHeader(n),l.send()},_finishFile:function(a,b,c){var d=this.owner;return d.request("after-send-file",arguments,function(){a.setStatus(h.COMPLETE),d.trigger("uploadSuccess",a,b,c)}).fail(function(b){a.getStatus()===h.PROGRESS&&a.setStatus(h.ERROR,b),d.trigger("uploadError",a,b)}).always(function(){d.trigger("uploadComplete",a)})},updateFileProgress:function(a){var b=0,c=0;a.blocks&&(f.each(a.blocks,function(a,b){c+=(b.percentage||0)*(b.end-b.start)}),b=c/a.size,this.owner.trigger("uploadProgress",a,b||0))},destroy:function(){clearTimeout(this.retryTimer)}})}),b("widgets/validator",["base","uploader","file","widgets/widget"],function(a,b,c){var d,e=a.$,f={};return d={addValidator:function(a,b){f[a]=b},removeValidator:function(a){delete f[a]}},b.register({name:"validator",init:function(){var b=this;a.nextTick(function(){e.each(f,function(){this.call(b.owner)})})}}),d.addValidator("fileNumLimit",function(){var a=this,b=a.options,c=0,d=parseInt(b.fileNumLimit,10),e=!0;d&&(a.on("beforeFileQueued",function(a){return!!this.trigger("beforeFileQueuedCheckfileNumLimit",a,c)&&(c>=d&&e&&(e=!1,this.trigger("error","Q_EXCEED_NUM_LIMIT",d,a),setTimeout(function(){e=!0},1)),!(c>=d))}),a.on("fileQueued",function(){c++}),a.on("fileDequeued",function(){c--}),a.on("reset",function(){c=0}))}),d.addValidator("fileSizeLimit",function(){var a=this,b=a.options,c=0,d=parseInt(b.fileSizeLimit,10),e=!0;d&&(a.on("beforeFileQueued",function(a){var b=c+a.size>d;return b&&e&&(e=!1,this.trigger("error","Q_EXCEED_SIZE_LIMIT",d,a),setTimeout(function(){e=!0},1)),!b}),a.on("fileQueued",function(a){c+=a.size}),a.on("fileDequeued",function(a){c-=a.size}),a.on("reset",function(){c=0}))}),d.addValidator("fileSingleSizeLimit",function(){var a=this,b=a.options,d=b.fileSingleSizeLimit;d&&a.on("beforeFileQueued",function(a){if(a.size>d)return a.setStatus(c.Status.INVALID,"exceed_size"),this.trigger("error","F_EXCEED_SIZE",d,a),!1})}),d.addValidator("duplicate",function(){function a(a){for(var b,c=0,d=0,e=a.length;d<e;d++)b=a.charCodeAt(d),c=b+(c<<6)+(c<<16)-c;return c}var b=this,c=b.options,d={};c.duplicate||(b.on("beforeFileQueued",function(b){var c=b.__hash||(b.__hash=a(b.name+b.size+b.lastModifiedDate));if(d[c])return this.trigger("error","F_DUPLICATE",b),!1}),b.on("fileQueued",function(a){var b=a.__hash;b&&(d[b]=!0)}),b.on("fileDequeued",function(a){var b=a.__hash;b&&delete d[b]}),b.on("reset",function(){d={}}))}),d}),b("lib/md5",["runtime/client","mediator"],function(a,b){function c(){a.call(this,"Md5")}return b.installTo(c.prototype),c.prototype.loadFromBlob=function(a){var b=this;b.getRuid()&&b.disconnectRuntime(),b.connectRuntime(a.ruid,function(){b.exec("init"),b.exec("loadFromBlob",a)})},c.prototype.getResult=function(){return this.exec("getResult")},c}),b("widgets/md5",["base","uploader","lib/md5","lib/blob","widgets/widget"],function(a,b,c,d){return b.register({name:"md5",md5File:function(b,e,f){var g=new c,h=a.Deferred(),i=b instanceof d?b:this.request("get-file",b).source;return g.on("progress load",function(a){a=a||{},h.notify(a.total?a.loaded/a.total:1)}),g.on("complete",function(){h.resolve(g.getResult())}),g.on("error",function(a){h.reject(a)}),arguments.length>1&&(e=e||0,f=f||0,e<0&&(e=i.size+e),f<0&&(f=i.size+f),f=Math.min(f,i.size),i=i.slice(e,f)),g.loadFromBlob(i),h.promise()}})}),b("runtime/compbase",[],function(){function a(a,b){this.owner=a,this.options=a.options,this.getRuntime=function(){return b},this.getRuid=function(){return b.uid},this.trigger=function(){return a.trigger.apply(a,arguments)}}return a}),b("runtime/html5/runtime",["base","runtime/runtime","runtime/compbase"],function(b,c,d){function e(){var a={},d=this,e=this.destroy;c.apply(d,arguments),d.type=f,d.exec=function(c,e){var f,h=this,i=h.uid,j=b.slice(arguments,2);if(g[c]&&(f=a[i]=a[i]||new g[c](h,d),f[e]))return f[e].apply(f,j)},d.destroy=function(){return e&&e.apply(this,arguments)}}var f="html5",g={};return b.inherits(c,{constructor:e,init:function(){var a=this;setTimeout(function(){a.trigger("ready")},1)}}),e.register=function(a,c){var e=g[a]=b.inherits(d,c);return e},a.Blob&&a.FileReader&&a.DataView&&c.addRuntime(f,e),e}),b("runtime/html5/blob",["runtime/html5/runtime","lib/blob"],function(a,b){return a.register("Blob",{slice:function(a,c){var d=this.owner.source,e=d.slice||d.webkitSlice||d.mozSlice;return d=e.call(d,a,c),new b(this.getRuid(),d)}})}),b("runtime/html5/dnd",["base","runtime/html5/runtime","lib/file"],function(a,b,c){var d=a.$,e="webuploader-dnd-";return b.register("DragAndDrop",{init:function(){var b=this.elem=this.options.container;this.dragEnterHandler=a.bindFn(this._dragEnterHandler,this),this.dragOverHandler=a.bindFn(this._dragOverHandler,this),this.dragLeaveHandler=a.bindFn(this._dragLeaveHandler,this),this.dropHandler=a.bindFn(this._dropHandler,this),this.dndOver=!1,b.on("dragenter",this.dragEnterHandler),b.on("dragover",this.dragOverHandler),b.on("dragleave",this.dragLeaveHandler),b.on("drop",this.dropHandler),this.options.disableGlobalDnd&&(d(document).on("dragover",this.dragOverHandler),d(document).on("drop",this.dropHandler))},_dragEnterHandler:function(a){var b,c=this,d=c._denied||!1;return a=a.originalEvent||a,c.dndOver||(c.dndOver=!0,b=a.dataTransfer.items,b&&b.length&&(c._denied=d=!c.trigger("accept",b)),c.elem.addClass(e+"over"),c.elem[d?"addClass":"removeClass"](e+"denied")),a.dataTransfer.dropEffect=d?"none":"copy",!1},_dragOverHandler:function(a){var b=this.elem.parent().get(0);return!(b&&!d.contains(b,a.currentTarget))&&(clearTimeout(this._leaveTimer),this._dragEnterHandler.call(this,a),!1)},_dragLeaveHandler:function(){var a,b=this;return a=function(){b.dndOver=!1,b.elem.removeClass(e+"over "+e+"denied")},clearTimeout(b._leaveTimer),b._leaveTimer=setTimeout(a,100),!1},_dropHandler:function(a){var b,f,g=this,h=g.getRuid(),i=g.elem.parent().get(0);if(i&&!d.contains(i,a.currentTarget))return!1;a=a.originalEvent||a,b=a.dataTransfer;try{f=b.getData("text/html")}catch(j){}return g.dndOver=!1,g.elem.removeClass(e+"over"),b&&!f?(g._getTansferFiles(b,function(a){g.trigger("drop",d.map(a,function(a){return new c(h,a)}))}),!1):void 0},_getTansferFiles:function(b,c){var d,e,f,g,h,i,j,k=[],l=[];for(d=b.items,e=b.files,j=!(!d||!d[0].webkitGetAsEntry),h=0,i=e.length;h<i;h++)f=e[h],g=d&&d[h],j&&g.webkitGetAsEntry().isDirectory?l.push(this._traverseDirectoryTree(g.webkitGetAsEntry(),k)):k.push(f);a.when.apply(a,l).done(function(){k.length&&c(k)})},_traverseDirectoryTree:function(b,c){var d=a.Deferred(),e=this;return b.isFile?b.file(function(a){c.push(a),d.resolve()}):b.isDirectory&&b.createReader().readEntries(function(b){var f,g=b.length,h=[],i=[];for(f=0;f<g;f++)h.push(e._traverseDirectoryTree(b[f],i));a.when.apply(a,h).then(function(){c.push.apply(c,i),d.resolve()},d.reject)}),d.promise()},destroy:function(){var a=this.elem;a&&(a.off("dragenter",this.dragEnterHandler),a.off("dragover",this.dragOverHandler),a.off("dragleave",this.dragLeaveHandler),a.off("drop",this.dropHandler),this.options.disableGlobalDnd&&(d(document).off("dragover",this.dragOverHandler),d(document).off("drop",this.dropHandler)))}})}),b("runtime/html5/filepaste",["base","runtime/html5/runtime","lib/file"],function(a,b,c){return b.register("FilePaste",{init:function(){var b,c,d,e,f=this.options,g=this.elem=f.container,h=".*";if(f.accept){for(b=[],c=0,d=f.accept.length;c<d;c++)e=f.accept[c].mimeTypes,e&&b.push(e);b.length&&(h=b.join(","),h=h.replace(/,/g,"|").replace(/\*/g,".*"))}this.accept=h=new RegExp(h,"i"),this.hander=a.bindFn(this._pasteHander,this),g.on("paste",this.hander)},_pasteHander:function(a){var b,d,e,f,g,h=[],i=this.getRuid();for(a=a.originalEvent||a,b=a.clipboardData.items,f=0,g=b.length;f<g;f++)d=b[f],"file"===d.kind&&(e=d.getAsFile())&&h.push(new c(i,e));h.length&&(a.preventDefault(),a.stopPropagation(),this.trigger("paste",h))},destroy:function(){this.elem.off("paste",this.hander)}})}),b("runtime/html5/filepicker",["base","runtime/html5/runtime"],function(a,b){var c=a.$;return b.register("FilePicker",{init:function(){var a,b,d,e,f,g=this.getRuntime().getContainer(),h=this,i=h.owner,j=h.options,k=this.label=c(document.createElement("label")),l=this.input=c(document.createElement("input"));if(l.attr("type","file"),l.attr("name",j.name),l.addClass("webuploader-element-invisible"),k.on("click",function(a){l.trigger("click"),a.stopPropagation(),i.trigger("dialogopen")}),k.css({opacity:0,width:"100%",height:"100%",display:"block",cursor:"pointer",background:"#ffffff"}),j.multiple&&l.attr("multiple","multiple"),j.accept&&j.accept.length>0){for(a=[],b=0,d=j.accept.length;b<d;b++)a.push(j.accept[b].mimeTypes);l.attr("accept",a.join(","))}g.append(l),g.append(k),e=function(a){i.trigger(a.type)},f=function(a){var b;return 0!==a.target.files.length&&(h.files=a.target.files,b=this.cloneNode(!0),b.value=null,this.parentNode.replaceChild(b,this),l.off(),l=c(b).on("change",f).on("mouseenter mouseleave",e),void i.trigger("change"))},l.on("change",f),k.on("mouseenter mouseleave",e)},getFiles:function(){return this.files},destroy:function(){this.input.off(),this.label.off()}})}),b("runtime/html5/util",["base"],function(b){var c=a.createObjectURL&&a||a.URL&&URL.revokeObjectURL&&URL||a.webkitURL,d=b.noop,e=d;return c&&(d=function(){return c.createObjectURL.apply(c,arguments)},e=function(){return c.revokeObjectURL.apply(c,arguments)}),{createObjectURL:d,revokeObjectURL:e,dataURL2Blob:function(a){var b,c,d,e,f,g;for(g=a.split(","),b=~g[0].indexOf("base64")?atob(g[1]):decodeURIComponent(g[1]),d=new ArrayBuffer(b.length),c=new Uint8Array(d),e=0;e<b.length;e++)c[e]=b.charCodeAt(e);return f=g[0].split(":")[1].split(";")[0],this.arrayBufferToBlob(d,f);
},dataURL2ArrayBuffer:function(a){var b,c,d,e;for(e=a.split(","),b=~e[0].indexOf("base64")?atob(e[1]):decodeURIComponent(e[1]),c=new Uint8Array(b.length),d=0;d<b.length;d++)c[d]=b.charCodeAt(d);return c.buffer},arrayBufferToBlob:function(b,c){var d,e=a.BlobBuilder||a.WebKitBlobBuilder;return e?(d=new e,d.append(b),d.getBlob(c)):new Blob([b],c?{type:c}:{})},canvasToDataUrl:function(a,b,c){return a.toDataURL(b,c/100)},parseMeta:function(a,b){b(!1,{})},updateImageHead:function(a){return a}}}),b("runtime/html5/imagemeta",["runtime/html5/util"],function(a){var b;return b={parsers:{65505:[]},maxMetaDataSize:262144,parse:function(a,b){var c=this,d=new FileReader;d.onload=function(){b(!1,c._parse(this.result)),d=d.onload=d.onerror=null},d.onerror=function(a){b(a.message),d=d.onload=d.onerror=null},a=a.slice(0,c.maxMetaDataSize),d.readAsArrayBuffer(a.getSource())},_parse:function(a,c){if(!(a.byteLength<6)){var d,e,f,g,h=new DataView(a),i=2,j=h.byteLength-4,k=i,l={};if(65496===h.getUint16(0)){for(;i<j&&(d=h.getUint16(i),d>=65504&&d<=65519||65534===d)&&(e=h.getUint16(i+2)+2,!(i+e>h.byteLength));){if(f=b.parsers[d],!c&&f)for(g=0;g<f.length;g+=1)f[g].call(b,h,i,e,l);i+=e,k=i}k>6&&(a.slice?l.imageHead=a.slice(2,k):l.imageHead=new Uint8Array(a).subarray(2,k))}return l}},updateImageHead:function(a,b){var c,d,e,f=this._parse(a,!0);return e=2,f.imageHead&&(e=2+f.imageHead.byteLength),d=a.slice?a.slice(e):new Uint8Array(a).subarray(e),c=new Uint8Array(b.byteLength+2+d.byteLength),c[0]=255,c[1]=216,c.set(new Uint8Array(b),2),c.set(new Uint8Array(d),b.byteLength+2),c.buffer}},a.parseMeta=function(){return b.parse.apply(b,arguments)},a.updateImageHead=function(){return b.updateImageHead.apply(b,arguments)},b}),b("runtime/html5/imagemeta/exif",["base","runtime/html5/imagemeta"],function(a,b){var c={};return c.ExifMap=function(){return this},c.ExifMap.prototype.map={Orientation:274},c.ExifMap.prototype.get=function(a){return this[a]||this[this.map[a]]},c.exifTagTypes={1:{getValue:function(a,b){return a.getUint8(b)},size:1},2:{getValue:function(a,b){return String.fromCharCode(a.getUint8(b))},size:1,ascii:!0},3:{getValue:function(a,b,c){return a.getUint16(b,c)},size:2},4:{getValue:function(a,b,c){return a.getUint32(b,c)},size:4},5:{getValue:function(a,b,c){return a.getUint32(b,c)/a.getUint32(b+4,c)},size:8},9:{getValue:function(a,b,c){return a.getInt32(b,c)},size:4},10:{getValue:function(a,b,c){return a.getInt32(b,c)/a.getInt32(b+4,c)},size:8}},c.exifTagTypes[7]=c.exifTagTypes[1],c.getExifValue=function(b,d,e,f,g,h){var i,j,k,l,m,n,o=c.exifTagTypes[f];if(!o)return void a.log("Invalid Exif data: Invalid tag type.");if(i=o.size*g,j=i>4?d+b.getUint32(e+8,h):e+8,j+i>b.byteLength)return void a.log("Invalid Exif data: Invalid data offset.");if(1===g)return o.getValue(b,j,h);for(k=[],l=0;l<g;l+=1)k[l]=o.getValue(b,j+l*o.size,h);if(o.ascii){for(m="",l=0;l<k.length&&(n=k[l],"\0"!==n);l+=1)m+=n;return m}return k},c.parseExifTag=function(a,b,d,e,f){var g=a.getUint16(d,e);f.exif[g]=c.getExifValue(a,b,d,a.getUint16(d+2,e),a.getUint32(d+4,e),e)},c.parseExifTags=function(b,c,d,e,f){var g,h,i;if(d+6>b.byteLength)return void a.log("Invalid Exif data: Invalid directory offset.");if(g=b.getUint16(d,e),h=d+2+12*g,h+4>b.byteLength)return void a.log("Invalid Exif data: Invalid directory size.");for(i=0;i<g;i+=1)this.parseExifTag(b,c,d+2+12*i,e,f);return b.getUint32(h,e)},c.parseExifData=function(b,d,e,f){var g,h,i=d+10;if(1165519206===b.getUint32(d+4)){if(i+8>b.byteLength)return void a.log("Invalid Exif data: Invalid segment size.");if(0!==b.getUint16(d+8))return void a.log("Invalid Exif data: Missing byte alignment offset.");switch(b.getUint16(i)){case 18761:g=!0;break;case 19789:g=!1;break;default:return void a.log("Invalid Exif data: Invalid byte alignment marker.")}if(42!==b.getUint16(i+2,g))return void a.log("Invalid Exif data: Missing TIFF marker.");h=b.getUint32(i+4,g),f.exif=new c.ExifMap,h=c.parseExifTags(b,i,i+h,g,f)}},b.parsers[65505].push(c.parseExifData),c}),b("runtime/html5/jpegencoder",[],function(a,b,c){function d(a){function b(a){for(var b=[16,11,10,16,24,40,51,61,12,12,14,19,26,58,60,55,14,13,16,24,40,57,69,56,14,17,22,29,51,87,80,62,18,22,37,56,68,109,103,77,24,35,55,64,81,104,113,92,49,64,78,87,103,121,120,101,72,92,95,98,112,100,103,99],c=0;c<64;c++){var d=y((b[c]*a+50)/100);d<1?d=1:d>255&&(d=255),z[P[c]]=d}for(var e=[17,18,24,47,99,99,99,99,18,21,26,66,99,99,99,99,24,26,56,99,99,99,99,99,47,66,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99],f=0;f<64;f++){var g=y((e[f]*a+50)/100);g<1?g=1:g>255&&(g=255),A[P[f]]=g}for(var h=[1,1.387039845,1.306562965,1.175875602,1,.785694958,.5411961,.275899379],i=0,j=0;j<8;j++)for(var k=0;k<8;k++)B[i]=1/(z[P[i]]*h[j]*h[k]*8),C[i]=1/(A[P[i]]*h[j]*h[k]*8),i++}function c(a,b){for(var c=0,d=0,e=new Array,f=1;f<=16;f++){for(var g=1;g<=a[f];g++)e[b[d]]=[],e[b[d]][0]=c,e[b[d]][1]=f,d++,c++;c*=2}return e}function d(){t=c(Q,R),u=c(U,V),v=c(S,T),w=c(W,X)}function e(){for(var a=1,b=2,c=1;c<=15;c++){for(var d=a;d<b;d++)E[32767+d]=c,D[32767+d]=[],D[32767+d][1]=c,D[32767+d][0]=d;for(var e=-(b-1);e<=-a;e++)E[32767+e]=c,D[32767+e]=[],D[32767+e][1]=c,D[32767+e][0]=b-1+e;a<<=1,b<<=1}}function f(){for(var a=0;a<256;a++)O[a]=19595*a,O[a+256>>0]=38470*a,O[a+512>>0]=7471*a+32768,O[a+768>>0]=-11059*a,O[a+1024>>0]=-21709*a,O[a+1280>>0]=32768*a+8421375,O[a+1536>>0]=-27439*a,O[a+1792>>0]=-5329*a}function g(a){for(var b=a[0],c=a[1]-1;c>=0;)b&1<<c&&(I|=1<<J),c--,J--,J<0&&(255==I?(h(255),h(0)):h(I),J=7,I=0)}function h(a){H.push(N[a])}function i(a){h(a>>8&255),h(255&a)}function j(a,b){var c,d,e,f,g,h,i,j,k,l=0,m=8,n=64;for(k=0;k<m;++k){c=a[l],d=a[l+1],e=a[l+2],f=a[l+3],g=a[l+4],h=a[l+5],i=a[l+6],j=a[l+7];var o=c+j,p=c-j,q=d+i,r=d-i,s=e+h,t=e-h,u=f+g,v=f-g,w=o+u,x=o-u,y=q+s,z=q-s;a[l]=w+y,a[l+4]=w-y;var A=.707106781*(z+x);a[l+2]=x+A,a[l+6]=x-A,w=v+t,y=t+r,z=r+p;var B=.382683433*(w-z),C=.5411961*w+B,D=1.306562965*z+B,E=.707106781*y,G=p+E,H=p-E;a[l+5]=H+C,a[l+3]=H-C,a[l+1]=G+D,a[l+7]=G-D,l+=8}for(l=0,k=0;k<m;++k){c=a[l],d=a[l+8],e=a[l+16],f=a[l+24],g=a[l+32],h=a[l+40],i=a[l+48],j=a[l+56];var I=c+j,J=c-j,K=d+i,L=d-i,M=e+h,N=e-h,O=f+g,P=f-g,Q=I+O,R=I-O,S=K+M,T=K-M;a[l]=Q+S,a[l+32]=Q-S;var U=.707106781*(T+R);a[l+16]=R+U,a[l+48]=R-U,Q=P+N,S=N+L,T=L+J;var V=.382683433*(Q-T),W=.5411961*Q+V,X=1.306562965*T+V,Y=.707106781*S,Z=J+Y,$=J-Y;a[l+40]=$+W,a[l+24]=$-W,a[l+8]=Z+X,a[l+56]=Z-X,l++}var _;for(k=0;k<n;++k)_=a[k]*b[k],F[k]=_>0?_+.5|0:_-.5|0;return F}function k(){i(65504),i(16),h(74),h(70),h(73),h(70),h(0),h(1),h(1),h(0),i(1),i(1),h(0),h(0)}function l(a,b){i(65472),i(17),h(8),i(b),i(a),h(3),h(1),h(17),h(0),h(2),h(17),h(1),h(3),h(17),h(1)}function m(){i(65499),i(132),h(0);for(var a=0;a<64;a++)h(z[a]);h(1);for(var b=0;b<64;b++)h(A[b])}function n(){i(65476),i(418),h(0);for(var a=0;a<16;a++)h(Q[a+1]);for(var b=0;b<=11;b++)h(R[b]);h(16);for(var c=0;c<16;c++)h(S[c+1]);for(var d=0;d<=161;d++)h(T[d]);h(1);for(var e=0;e<16;e++)h(U[e+1]);for(var f=0;f<=11;f++)h(V[f]);h(17);for(var g=0;g<16;g++)h(W[g+1]);for(var j=0;j<=161;j++)h(X[j])}function o(){i(65498),i(12),h(3),h(1),h(0),h(2),h(17),h(3),h(17),h(0),h(63),h(0)}function p(a,b,c,d,e){for(var f,h=e[0],i=e[240],k=16,l=63,m=64,n=j(a,b),o=0;o<m;++o)G[P[o]]=n[o];var p=G[0]-c;c=G[0],0==p?g(d[0]):(f=32767+p,g(d[E[f]]),g(D[f]));for(var q=63;q>0&&0==G[q];q--);if(0==q)return g(h),c;for(var r,s=1;s<=q;){for(var t=s;0==G[s]&&s<=q;++s);var u=s-t;if(u>=k){r=u>>4;for(var v=1;v<=r;++v)g(i);u=15&u}f=32767+G[s],g(e[(u<<4)+E[f]]),g(D[f]),s++}return q!=l&&g(h),c}function q(){for(var a=String.fromCharCode,b=0;b<256;b++)N[b]=a(b)}function r(a){if(a<=0&&(a=1),a>100&&(a=100),x!=a){var c=0;c=a<50?Math.floor(5e3/a):Math.floor(200-2*a),b(c),x=a}}function s(){a||(a=50),q(),d(),e(),f(),r(a)}var t,u,v,w,x,y=(Math.round,Math.floor),z=new Array(64),A=new Array(64),B=new Array(64),C=new Array(64),D=new Array(65535),E=new Array(65535),F=new Array(64),G=new Array(64),H=[],I=0,J=7,K=new Array(64),L=new Array(64),M=new Array(64),N=new Array(256),O=new Array(2048),P=[0,1,5,6,14,15,27,28,2,4,7,13,16,26,29,42,3,8,12,17,25,30,41,43,9,11,18,24,31,40,44,53,10,19,23,32,39,45,52,54,20,22,33,38,46,51,55,60,21,34,37,47,50,56,59,61,35,36,48,49,57,58,62,63],Q=[0,0,1,5,1,1,1,1,1,1,0,0,0,0,0,0,0],R=[0,1,2,3,4,5,6,7,8,9,10,11],S=[0,0,2,1,3,3,2,4,3,5,5,4,4,0,0,1,125],T=[1,2,3,0,4,17,5,18,33,49,65,6,19,81,97,7,34,113,20,50,129,145,161,8,35,66,177,193,21,82,209,240,36,51,98,114,130,9,10,22,23,24,25,26,37,38,39,40,41,42,52,53,54,55,56,57,58,67,68,69,70,71,72,73,74,83,84,85,86,87,88,89,90,99,100,101,102,103,104,105,106,115,116,117,118,119,120,121,122,131,132,133,134,135,136,137,138,146,147,148,149,150,151,152,153,154,162,163,164,165,166,167,168,169,170,178,179,180,181,182,183,184,185,186,194,195,196,197,198,199,200,201,202,210,211,212,213,214,215,216,217,218,225,226,227,228,229,230,231,232,233,234,241,242,243,244,245,246,247,248,249,250],U=[0,0,3,1,1,1,1,1,1,1,1,1,0,0,0,0,0],V=[0,1,2,3,4,5,6,7,8,9,10,11],W=[0,0,2,1,2,4,4,3,4,7,5,4,4,0,1,2,119],X=[0,1,2,3,17,4,5,33,49,6,18,65,81,7,97,113,19,34,50,129,8,20,66,145,161,177,193,9,35,51,82,240,21,98,114,209,10,22,36,52,225,37,241,23,24,25,26,38,39,40,41,42,53,54,55,56,57,58,67,68,69,70,71,72,73,74,83,84,85,86,87,88,89,90,99,100,101,102,103,104,105,106,115,116,117,118,119,120,121,122,130,131,132,133,134,135,136,137,138,146,147,148,149,150,151,152,153,154,162,163,164,165,166,167,168,169,170,178,179,180,181,182,183,184,185,186,194,195,196,197,198,199,200,201,202,210,211,212,213,214,215,216,217,218,226,227,228,229,230,231,232,233,234,242,243,244,245,246,247,248,249,250];this.encode=function(a,b){b&&r(b),H=new Array,I=0,J=7,i(65496),k(),m(),l(a.width,a.height),n(),o();var c=0,d=0,e=0;I=0,J=7,this.encode.displayName="_encode_";for(var f,h,j,q,s,x,y,z,A,D=a.data,E=a.width,F=a.height,G=4*E,N=0;N<F;){for(f=0;f<G;){for(s=G*N+f,x=s,y=-1,z=0,A=0;A<64;A++)z=A>>3,y=4*(7&A),x=s+z*G+y,N+z>=F&&(x-=G*(N+1+z-F)),f+y>=G&&(x-=f+y-G+4),h=D[x++],j=D[x++],q=D[x++],K[A]=(O[h]+O[j+256>>0]+O[q+512>>0]>>16)-128,L[A]=(O[h+768>>0]+O[j+1024>>0]+O[q+1280>>0]>>16)-128,M[A]=(O[h+1280>>0]+O[j+1536>>0]+O[q+1792>>0]>>16)-128;c=p(K,B,c,t,v),d=p(L,C,d,u,w),e=p(M,C,e,u,w),f+=32}N+=8}if(J>=0){var P=[];P[1]=J+1,P[0]=(1<<J+1)-1,g(P)}i(65497);var Q="data:image/jpeg;base64,"+btoa(H.join(""));return H=[],Q},s()}return d.encode=function(a,b){var c=new d(b);return c.encode(a)},d}),b("runtime/html5/androidpatch",["runtime/html5/util","runtime/html5/jpegencoder","base"],function(a,b,c){var d,e=a.canvasToDataUrl;a.canvasToDataUrl=function(a,f,g){var h,i,j,k,l;return c.os.android?("image/jpeg"===f&&"undefined"==typeof d&&(k=e.apply(null,arguments),l=k.split(","),k=~l[0].indexOf("base64")?atob(l[1]):decodeURIComponent(l[1]),k=k.substring(0,2),d=255===k.charCodeAt(0)&&216===k.charCodeAt(1)),"image/jpeg"!==f||d?e.apply(null,arguments):(i=a.width,j=a.height,h=a.getContext("2d"),b.encode(h.getImageData(0,0,i,j),g))):e.apply(null,arguments)}}),b("runtime/html5/image",["base","runtime/html5/runtime","runtime/html5/util"],function(a,b,c){var d="data:image/gif;base64,R0lGODlhAQABAAD/ACwAAAAAAQABAAACADs%3D";return b.register("Image",{modified:!1,init:function(){var a=this,b=new Image;b.onload=function(){a._info={type:a.type,width:this.width,height:this.height},a._metas||"image/jpeg"!==a.type?a.owner.trigger("load"):c.parseMeta(a._blob,function(b,c){a._metas=c,a.owner.trigger("load")})},b.onerror=function(){a.owner.trigger("error")},a._img=b},loadFromBlob:function(a){var b=this,d=b._img;b._blob=a,b.type=a.type,d.src=c.createObjectURL(a.getSource()),b.owner.once("load",function(){c.revokeObjectURL(d.src)})},resize:function(a,b){var c=this._canvas||(this._canvas=document.createElement("canvas"));this._resize(this._img,c,a,b),this._blob=null,this.modified=!0,this.owner.trigger("complete","resize")},crop:function(a,b,c,d,e){var f=this._canvas||(this._canvas=document.createElement("canvas")),g=this.options,h=this._img,i=h.naturalWidth,j=h.naturalHeight,k=this.getOrientation();e=e||1,f.width=c,f.height=d,g.preserveHeaders||this._rotate2Orientaion(f,k),this._renderImageToCanvas(f,h,-a,-b,i*e,j*e),this._blob=null,this.modified=!0,this.owner.trigger("complete","crop")},getAsBlob:function(a){var b,d=this._blob,e=this.options;if(a=a||this.type,this.modified||this.type!==a){if(b=this._canvas,"image/jpeg"===a){if(d=c.canvasToDataUrl(b,a,e.quality),e.preserveHeaders&&this._metas&&this._metas.imageHead)return d=c.dataURL2ArrayBuffer(d),d=c.updateImageHead(d,this._metas.imageHead),d=c.arrayBufferToBlob(d,a)}else d=c.canvasToDataUrl(b,a);d=c.dataURL2Blob(d)}return d},getAsDataUrl:function(a){var b=this.options;return a=a||this.type,"image/jpeg"===a?c.canvasToDataUrl(this._canvas,a,b.quality):this._canvas.toDataURL(a)},getOrientation:function(){return this._metas&&this._metas.exif&&this._metas.exif.get("Orientation")||1},info:function(a){return a?(this._info=a,this):this._info},meta:function(a){return a?(this._metas=a,this):this._metas},destroy:function(){var a=this._canvas;this._img.onload=null,a&&(a.getContext("2d").clearRect(0,0,a.width,a.height),a.width=a.height=0,this._canvas=null),this._img.src=d,this._img=this._blob=null},_resize:function(a,b,c,d){var e,f,g,h,i,j=this.options,k=a.width,l=a.height,m=this.getOrientation();~[5,6,7,8].indexOf(m)&&(c^=d,d^=c,c^=d),e=Math[j.crop?"max":"min"](c/k,d/l),j.allowMagnify||(e=Math.min(1,e)),f=k*e,g=l*e,j.crop?(b.width=c,b.height=d):(b.width=f,b.height=g),h=(b.width-f)/2,i=(b.height-g)/2,j.preserveHeaders||this._rotate2Orientaion(b,m),this._renderImageToCanvas(b,a,h,i,f,g)},_rotate2Orientaion:function(a,b){var c=a.width,d=a.height,e=a.getContext("2d");switch(b){case 5:case 6:case 7:case 8:a.width=d,a.height=c}switch(b){case 2:e.translate(c,0),e.scale(-1,1);break;case 3:e.translate(c,d),e.rotate(Math.PI);break;case 4:e.translate(0,d),e.scale(1,-1);break;case 5:e.rotate(.5*Math.PI),e.scale(1,-1);break;case 6:e.rotate(.5*Math.PI),e.translate(0,-d);break;case 7:e.rotate(.5*Math.PI),e.translate(c,-d),e.scale(-1,1);break;case 8:e.rotate(-.5*Math.PI),e.translate(-c,0)}},_renderImageToCanvas:function(){function b(a,b,c){var d,e,f,g=document.createElement("canvas"),h=g.getContext("2d"),i=0,j=c,k=c;for(g.width=1,g.height=c,h.drawImage(a,0,0),d=h.getImageData(0,0,1,c).data;k>i;)e=d[4*(k-1)+3],0===e?j=k:i=k,k=j+i>>1;return f=k/c,0===f?1:f}function c(a){var b,c,d=a.naturalWidth,e=a.naturalHeight;return d*e>1048576&&(b=document.createElement("canvas"),b.width=b.height=1,c=b.getContext("2d"),c.drawImage(a,-d+1,0),0===c.getImageData(0,0,1,1).data[3])}return a.os.ios?a.os.ios>=7?function(a,c,d,e,f,g){var h=c.naturalWidth,i=c.naturalHeight,j=b(c,h,i);return a.getContext("2d").drawImage(c,0,0,h*j,i*j,d,e,f,g)}:function(a,d,e,f,g,h){var i,j,k,l,m,n,o,p=d.naturalWidth,q=d.naturalHeight,r=a.getContext("2d"),s=c(d),t="image/jpeg"===this.type,u=1024,v=0,w=0;for(s&&(p/=2,q/=2),r.save(),i=document.createElement("canvas"),i.width=i.height=u,j=i.getContext("2d"),k=t?b(d,p,q):1,l=Math.ceil(u*g/p),m=Math.ceil(u*h/q/k);v<q;){for(n=0,o=0;n<p;)j.clearRect(0,0,u,u),j.drawImage(d,-n,-v),r.drawImage(i,0,0,u,u,e+o,f+w,l,m),n+=u,o+=l;v+=u,w+=m}r.restore(),i=j=null}:function(b){var c=a.slice(arguments,1),d=b.getContext("2d");d.drawImage.apply(d,c)}}()})}),b("runtime/html5/transport",["base","runtime/html5/runtime"],function(a,b){var c=a.noop,d=a.$;return b.register("Transport",{init:function(){this._status=0,this._response=null},send:function(){var b,c,e,f=this.owner,g=this.options,h=this._initAjax(),i=f._blob,j=g.server;g.sendAsBinary?(j+=g.attachInfoToQuery!==!1?(/\?/.test(j)?"&":"?")+d.param(f._formData):"",c=i.getSource()):(b=new FormData,d.each(f._formData,function(a,c){b.append(a,c)}),b.append(g.fileVal,i.getSource(),g.filename||f._formData.name||"")),g.withCredentials&&"withCredentials"in h?(h.open(g.method,j,!0),h.withCredentials=!0):h.open(g.method,j),this._setRequestHeader(h,g.headers),c?(h.overrideMimeType&&h.overrideMimeType("application/octet-stream"),a.os.android?(e=new FileReader,e.onload=function(){h.send(this.result),e=e.onload=null},e.readAsArrayBuffer(c)):h.send(c)):h.send(b)},getResponse:function(){return this._response},getResponseAsJson:function(){return this._parseJson(this._response)},getResponseHeaders:function(){return this._headers},getStatus:function(){return this._status},abort:function(){var a=this._xhr;a&&(a.upload.onprogress=c,a.onreadystatechange=c,a.abort(),this._xhr=a=null)},destroy:function(){this.abort()},_parseHeader:function(a){var b={};return a&&a.replace(/^([^\:]+):(.*)$/gm,function(a,c,d){b[c.trim()]=d.trim()}),b},_initAjax:function(){var a=this,b=new XMLHttpRequest,d=this.options;return!d.withCredentials||"withCredentials"in b||"undefined"==typeof XDomainRequest||(b=new XDomainRequest),b.upload.onprogress=function(b){var c=0;return b.lengthComputable&&(c=b.loaded/b.total),a.trigger("progress",c)},b.onreadystatechange=function(){if(4===b.readyState){b.upload.onprogress=c,b.onreadystatechange=c,a._xhr=null,a._status=b.status;var d="|",e=d+b.status+d+b.statusText;return b.status>=200&&b.status<300?(a._response=b.responseText,a._headers=a._parseHeader(b.getAllResponseHeaders()),a.trigger("load")):b.status>=500&&b.status<600?(a._response=b.responseText,a._headers=a._parseHeader(b.getAllResponseHeaders()),a.trigger("error","server"+e)):a.trigger("error",a._status?"http"+e:"abort")}},a._xhr=b,b},_setRequestHeader:function(a,b){d.each(b,function(b,c){a.setRequestHeader(b,c)})},_parseJson:function(a){var b;try{b=JSON.parse(a)}catch(c){b={}}return b}})}),b("runtime/html5/md5",["runtime/html5/runtime"],function(a){var b=function(a,b){return a+b&4294967295},c=function(a,c,d,e,f,g){return c=b(b(c,a),b(e,g)),b(c<<f|c>>>32-f,d)},d=function(a,b,d,e,f,g,h){return c(b&d|~b&e,a,b,f,g,h)},e=function(a,b,d,e,f,g,h){return c(b&e|d&~e,a,b,f,g,h)},f=function(a,b,d,e,f,g,h){return c(b^d^e,a,b,f,g,h)},g=function(a,b,d,e,f,g,h){return c(d^(b|~e),a,b,f,g,h)},h=function(a,c){var h=a[0],i=a[1],j=a[2],k=a[3];h=d(h,i,j,k,c[0],7,-680876936),k=d(k,h,i,j,c[1],12,-389564586),j=d(j,k,h,i,c[2],17,606105819),i=d(i,j,k,h,c[3],22,-1044525330),h=d(h,i,j,k,c[4],7,-176418897),k=d(k,h,i,j,c[5],12,1200080426),j=d(j,k,h,i,c[6],17,-1473231341),i=d(i,j,k,h,c[7],22,-45705983),h=d(h,i,j,k,c[8],7,1770035416),k=d(k,h,i,j,c[9],12,-1958414417),j=d(j,k,h,i,c[10],17,-42063),i=d(i,j,k,h,c[11],22,-1990404162),h=d(h,i,j,k,c[12],7,1804603682),k=d(k,h,i,j,c[13],12,-40341101),j=d(j,k,h,i,c[14],17,-1502002290),i=d(i,j,k,h,c[15],22,1236535329),h=e(h,i,j,k,c[1],5,-165796510),k=e(k,h,i,j,c[6],9,-1069501632),j=e(j,k,h,i,c[11],14,643717713),i=e(i,j,k,h,c[0],20,-373897302),h=e(h,i,j,k,c[5],5,-701558691),k=e(k,h,i,j,c[10],9,38016083),j=e(j,k,h,i,c[15],14,-660478335),i=e(i,j,k,h,c[4],20,-405537848),h=e(h,i,j,k,c[9],5,568446438),k=e(k,h,i,j,c[14],9,-1019803690),j=e(j,k,h,i,c[3],14,-187363961),i=e(i,j,k,h,c[8],20,1163531501),h=e(h,i,j,k,c[13],5,-1444681467),k=e(k,h,i,j,c[2],9,-51403784),j=e(j,k,h,i,c[7],14,1735328473),i=e(i,j,k,h,c[12],20,-1926607734),h=f(h,i,j,k,c[5],4,-378558),k=f(k,h,i,j,c[8],11,-2022574463),j=f(j,k,h,i,c[11],16,1839030562),i=f(i,j,k,h,c[14],23,-35309556),h=f(h,i,j,k,c[1],4,-1530992060),k=f(k,h,i,j,c[4],11,1272893353),j=f(j,k,h,i,c[7],16,-155497632),i=f(i,j,k,h,c[10],23,-1094730640),h=f(h,i,j,k,c[13],4,681279174),k=f(k,h,i,j,c[0],11,-358537222),j=f(j,k,h,i,c[3],16,-722521979),i=f(i,j,k,h,c[6],23,76029189),h=f(h,i,j,k,c[9],4,-640364487),k=f(k,h,i,j,c[12],11,-421815835),j=f(j,k,h,i,c[15],16,530742520),i=f(i,j,k,h,c[2],23,-995338651),h=g(h,i,j,k,c[0],6,-198630844),k=g(k,h,i,j,c[7],10,1126891415),j=g(j,k,h,i,c[14],15,-1416354905),i=g(i,j,k,h,c[5],21,-57434055),h=g(h,i,j,k,c[12],6,1700485571),k=g(k,h,i,j,c[3],10,-1894986606),j=g(j,k,h,i,c[10],15,-1051523),i=g(i,j,k,h,c[1],21,-2054922799),h=g(h,i,j,k,c[8],6,1873313359),k=g(k,h,i,j,c[15],10,-30611744),j=g(j,k,h,i,c[6],15,-1560198380),i=g(i,j,k,h,c[13],21,1309151649),h=g(h,i,j,k,c[4],6,-145523070),k=g(k,h,i,j,c[11],10,-1120210379),j=g(j,k,h,i,c[2],15,718787259),i=g(i,j,k,h,c[9],21,-343485551),a[0]=b(h,a[0]),a[1]=b(i,a[1]),a[2]=b(j,a[2]),a[3]=b(k,a[3])},i=function(a){var b,c=[];for(b=0;b<64;b+=4)c[b>>2]=a.charCodeAt(b)+(a.charCodeAt(b+1)<<8)+(a.charCodeAt(b+2)<<16)+(a.charCodeAt(b+3)<<24);return c},j=function(a){var b,c=[];for(b=0;b<64;b+=4)c[b>>2]=a[b]+(a[b+1]<<8)+(a[b+2]<<16)+(a[b+3]<<24);return c},k=function(a){var b,c,d,e,f,g,j=a.length,k=[1732584193,-271733879,-1732584194,271733878];for(b=64;b<=j;b+=64)h(k,i(a.substring(b-64,b)));for(a=a.substring(b-64),c=a.length,d=[0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0],b=0;b<c;b+=1)d[b>>2]|=a.charCodeAt(b)<<(b%4<<3);if(d[b>>2]|=128<<(b%4<<3),b>55)for(h(k,d),b=0;b<16;b+=1)d[b]=0;return e=8*j,e=e.toString(16).match(/(.*?)(.{0,8})$/),f=parseInt(e[2],16),g=parseInt(e[1],16)||0,d[14]=f,d[15]=g,h(k,d),k},l=function(a){var b,c,d,e,f,g,i=a.length,k=[1732584193,-271733879,-1732584194,271733878];for(b=64;b<=i;b+=64)h(k,j(a.subarray(b-64,b)));for(a=b-64<i?a.subarray(b-64):new Uint8Array(0),c=a.length,d=[0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0],b=0;b<c;b+=1)d[b>>2]|=a[b]<<(b%4<<3);if(d[b>>2]|=128<<(b%4<<3),b>55)for(h(k,d),b=0;b<16;b+=1)d[b]=0;return e=8*i,e=e.toString(16).match(/(.*?)(.{0,8})$/),f=parseInt(e[2],16),g=parseInt(e[1],16)||0,d[14]=f,d[15]=g,h(k,d),k},m=["0","1","2","3","4","5","6","7","8","9","a","b","c","d","e","f"],n=function(a){var b,c="";for(b=0;b<4;b+=1)c+=m[a>>8*b+4&15]+m[a>>8*b&15];return c},o=function(a){var b;for(b=0;b<a.length;b+=1)a[b]=n(a[b]);return a.join("")},p=function(a){return o(k(a))},q=function(){this.reset()};return"5d41402abc4b2a76b9719d911017c592"!==p("hello")&&(b=function(a,b){var c=(65535&a)+(65535&b),d=(a>>16)+(b>>16)+(c>>16);return d<<16|65535&c}),q.prototype.append=function(a){return/[\u0080-\uFFFF]/.test(a)&&(a=unescape(encodeURIComponent(a))),this.appendBinary(a),this},q.prototype.appendBinary=function(a){this._buff+=a,this._length+=a.length;var b,c=this._buff.length;for(b=64;b<=c;b+=64)h(this._state,i(this._buff.substring(b-64,b)));return this._buff=this._buff.substr(b-64),this},q.prototype.end=function(a){var b,c,d=this._buff,e=d.length,f=[0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0];for(b=0;b<e;b+=1)f[b>>2]|=d.charCodeAt(b)<<(b%4<<3);return this._finish(f,e),c=a?this._state:o(this._state),this.reset(),c},q.prototype._finish=function(a,b){var c,d,e,f=b;if(a[f>>2]|=128<<(f%4<<3),f>55)for(h(this._state,a),f=0;f<16;f+=1)a[f]=0;c=8*this._length,c=c.toString(16).match(/(.*?)(.{0,8})$/),d=parseInt(c[2],16),e=parseInt(c[1],16)||0,a[14]=d,a[15]=e,h(this._state,a)},q.prototype.reset=function(){return this._buff="",this._length=0,this._state=[1732584193,-271733879,-1732584194,271733878],this},q.prototype.destroy=function(){delete this._state,delete this._buff,delete this._length},q.hash=function(a,b){/[\u0080-\uFFFF]/.test(a)&&(a=unescape(encodeURIComponent(a)));var c=k(a);return b?c:o(c)},q.hashBinary=function(a,b){var c=k(a);return b?c:o(c)},q.ArrayBuffer=function(){this.reset()},q.ArrayBuffer.prototype.append=function(a){var b,c=this._concatArrayBuffer(this._buff,a),d=c.length;for(this._length+=a.byteLength,b=64;b<=d;b+=64)h(this._state,j(c.subarray(b-64,b)));return this._buff=b-64<d?c.subarray(b-64):new Uint8Array(0),this},q.ArrayBuffer.prototype.end=function(a){var b,c,d=this._buff,e=d.length,f=[0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0];for(b=0;b<e;b+=1)f[b>>2]|=d[b]<<(b%4<<3);return this._finish(f,e),c=a?this._state:o(this._state),this.reset(),c},q.ArrayBuffer.prototype._finish=q.prototype._finish,q.ArrayBuffer.prototype.reset=function(){return this._buff=new Uint8Array(0),this._length=0,this._state=[1732584193,-271733879,-1732584194,271733878],this},q.ArrayBuffer.prototype.destroy=q.prototype.destroy,q.ArrayBuffer.prototype._concatArrayBuffer=function(a,b){var c=a.length,d=new Uint8Array(c+b.byteLength);return d.set(a),d.set(new Uint8Array(b),c),d},q.ArrayBuffer.hash=function(a,b){var c=l(new Uint8Array(a));return b?c:o(c)},a.register("Md5",{init:function(){},loadFromBlob:function(a){var b,c,d=a.getSource(),e=2097152,f=Math.ceil(d.size/e),g=0,h=this.owner,i=new q.ArrayBuffer,j=this,k=d.mozSlice||d.webkitSlice||d.slice;c=new FileReader,(b=function(){var l,m;l=g*e,m=Math.min(l+e,d.size),c.onload=function(b){i.append(b.target.result),h.trigger("progress",{total:a.size,loaded:m})},c.onloadend=function(){c.onloadend=c.onload=null,++g<f?setTimeout(b,1):setTimeout(function(){h.trigger("load"),j.result=i.end(),b=a=d=i=null,h.trigger("complete")},50)},c.readAsArrayBuffer(k.call(d,l,m))})()},getResult:function(){return this.result}})}),b("preset/all",["base","widgets/filednd","widgets/filepaste","widgets/filepicker","widgets/image","widgets/queue","widgets/runtime","widgets/upload","widgets/validator","widgets/md5","runtime/html5/blob","runtime/html5/dnd","runtime/html5/filepaste","runtime/html5/filepicker","runtime/html5/imagemeta/exif","runtime/html5/androidpatch","runtime/html5/image","runtime/html5/transport","runtime/html5/md5"],function(a){return a}),b("widgets/log",["base","uploader","widgets/widget"],function(a,b){function c(a){var b=e.extend({},d,a),c=f.replace(/^(.*)\?/,"$1"+e.param(b)),g=new Image;g.src=c}var d,e=a.$,f=" http://static.tieba.baidu.com/tb/pms/img/st.gif??",g=(location.hostname||location.host||"protected").toLowerCase(),h=g&&/baidu/i.exec(g);if(h)return d={dv:3,master:"webuploader",online:/test/.exec(g)?0:1,module:"",product:g,type:0},b.register({name:"log",init:function(){var a=this.owner,b=0,d=0;a.on("error",function(a){c({type:2,c_error_code:a})}).on("uploadError",function(a,b){c({type:2,c_error_code:"UPLOAD_ERROR",c_reason:""+b})}).on("uploadComplete",function(a){b++,d+=a.size}).on("uploadFinished",function(){c({c_count:b,c_size:d}),b=d=0}),c({c_usage:1})}})}),b("webuploader",["preset/all","widgets/log"],function(a){return a}),c("webuploader")});