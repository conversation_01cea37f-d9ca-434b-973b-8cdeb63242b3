import { createBrowserRouter, RouterProvider } from 'react-router-dom';
import { useSelector, useDispatch } from 'react-redux';
import { createRouterData } from '@/routes';
import { useEffect, useState } from 'react';
import { setRouterList } from '@/store/userSlice';
import { createFormatRouterData } from '@/utils/router';

function createRouter(routerList) {
	return createBrowserRouter(routerList, {
		basename: import.meta.env.VITE_BASE_PATH,
	});
}

const App = () => {
	const dispatch = useDispatch();
	const userInfo = useSelector((state) => {
		return state.user.userInfo || {};
	});

	const [router, setRouter] = useState(createRouter(createRouterData()));

	useEffect(() => {
		if (userInfo.permissionList) {
			// 生成路由数据并写入
			const routerData = createFormatRouterData(userInfo.permissionList);
			dispatch(setRouterList(JSON.parse(JSON.stringify(routerData))));
			setRouter(createRouter(createRouterData(routerData)));
		}
	}, [userInfo]);
	return <RouterProvider router={router} fallbackElement={<></>}></RouterProvider>;
};

export default App;
