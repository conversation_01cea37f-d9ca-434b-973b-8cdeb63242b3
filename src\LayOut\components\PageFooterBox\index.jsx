import './index.scss';
import { getImageSrc } from '@/assets/images/index';
import { useRouterLink } from '@/hook/useRouter';
// 页面底部栏
const PageFooterBox = () => {
	const { openNewTab } = useRouterLink();
	return (
		<div className="bg-color-434b6d footer-box">
			<div className="width-1200 padding-top-100 padding-bottom-40 margin-0-auto">
				<div className="flex">
					<div>
						<div className="heigit-50 flex align-center margin-right-78">
							<img src={getImageSrc('@/assets/images/Public/logo-footer.png')} alt="logo" className="width-50 height-50" />
							<div className="margin-left-12 font-size-22 line-height-26 color-ffffff">科技成果转化平台</div>
						</div>
						<div className="font-size-16 font-weight-500 color-c9cdd4 line-height-22 margin-top-20  margin-bottom-30">
							<div className="margin-bottom-10">邮箱：<EMAIL></div>
							<div>地址：广州市天河区广州（国际）成果转化天河基地C栋6楼</div>
						</div>
						<div className="qrcode-box width-100">
							<img
								src={'https://achvt-prod.oss-cn-shenzhen.aliyuncs.com/logo/mp-gzh-qrcode-20230814.png?' + new Date().getHours()}
								alt="qrcode"
								className="width-100 height-100"
							/>
							<div className="font-size-16 line-height-26 color-ffffff text-align-center margin-top-20">关注公众号</div>
						</div>
					</div>
					<div className="flex justify-end  margin-left-188">
						<div className="font-size-16 font-weight-500 color-c9cdd4 line-height-22 margin-right-220">
							<div className="heigit-50 font-size-22 font-weight-500 color-ffffff line-height-50">关于我们</div>
							<div className="a margin-top-26" onClick={() => openNewTab('/aboutUs')}>
								公司简介
							</div>
						</div>
						<div className="font-size-16 font-weight-500 color-c9cdd4 line-height-22">
							<div className="heigit-50 font-size-22 font-weight-500 color-ffffff line-height-50">法律声明</div>
							<div className="a margin-top-26" onClick={() => openNewTab('/userServiceAgreement')}>
								用户服务协议
							</div>
							<div className="a margin-tb-10" onClick={() => openNewTab('/userInformationProtectionPolicy')}>
								用户隐私政策
							</div>
						</div>
					</div>
				</div>
				<div className="copyright-box flex align-center font-size-16 line-height-22 margin-top-94">
					<div>Copyright 2023 All Rights Reserved.</div>
					<div className="margin-left-30">粤ICP备20034432号-1</div>
					<div className="margin-left-30">版权所有：大湾区科技创新服务中心（广州）股份有限公司</div>
					<div className="margin-left-30">粤公网安备 44010502001637号</div>
				</div>
			</div>
		</div>
	);
};
export default PageFooterBox;
