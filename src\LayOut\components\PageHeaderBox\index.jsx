import { useLocation } from 'react-router-dom';
import { Dropdown, message, Space, Tooltip } from 'antd';
import { CaretDownOutlined, LogoutOutlined, FormOutlined } from '@ant-design/icons';
import { useEffect, useState, memo } from 'react';
import { useSelector, useDispatch } from 'react-redux';
import { useRouterLink } from '@/hook/useRouter';
import { setCurSystemInfo } from '@/store/userSlice';
import { logout } from '@/utils/common';
import { getImageSrc } from '@/assets/images/index';

import Permission from '@/components/Permission';
import EditPasswordModal from '@/components/EditPasswordModal';
import ZsMessage from '@/LayOut/components/ZsMessage';

import './index.scss';

// 顶部导航栏 按钮
const Index = (props = {}) => {
	const { linkTo, openNewTab } = useRouterLink();
	// const { menuClass, dispatchMenuHide, dispatchMenuShow } = stateMenu();

	// 登录凭证
	const userInfo = useSelector((state) => {
		return state.user.userInfo;
	});
	// 系统列表
	const routerList = useSelector((state) => {
		return state.user.routerList || [];
	}).filter((item) => item.perms && item.perms !== 'system');

	// 当前选中系统信息
	const curSystemInfo = useSelector((state) => {
		return state.user.curSystemInfo;
	});
	const systemName = useSelector((state) => {
		return state.user.systemName;
	});

	const systemLogo = useSelector((state) => {
		return state.user.systemLogo;
	});

	const location = useLocation();
	const [curPathname, setPathname] = useState('');

	useEffect(() => {
		setPathname(location.pathname);
	}, [location.pathname]);

	const dispatch = useDispatch();
	const loginOut = () => {
		window.location.href = import.meta.env.VITE_BASE_PATH + (import.meta.env.VITE_BASE_PATH ? '/login'.slice(1) : '/login');
		logout().then(() => {
			message.warning('退出成功');
		});
	};

	const checkedSystem = (e) => {
		const {
			path,
			meta: { title, logoUrl },
			children,
			perms,
		} = routerList[e.key];
		if (curSystemInfo.perms === perms) {
			return;
		}
		dispatch(
			setCurSystemInfo({
				perms,
				path,
				title,
				logoUrl,
				menuList: children,
			})
		);
		document.title = systemName || title;
		linkTo(`/${path}`);
	};

	return (
		<div className="padding-30">
			<div
				className={
					'page-header-box position-fixed top-0 left-0 right-0 z-index-10 bg-color-ffffff' + ((props.meta && props.meta.className) || '')
				}
			>
				<div className="margin-auto padding-right-20 border-box">
					<div className="flex align-center justify-between height-60 ">
						<div className="flex align-center justify-between height-60">
							<Space>
								<div className="padding-left-20 a logo-box flex align-center" onClick={() => linkTo(`/${curSystemInfo.path}`)}>
									{(curSystemInfo.logoUrl || systemLogo) && (
										<img className="margin-right-8 width-32 height-32" src={systemLogo || curSystemInfo.logoUrl} />
									)}
									<div className="font-size-20 font-weight-500 color-1d2129">{systemName || curSystemInfo.title}</div>

									{/* <img
									src={getImageSrc(
										'@/assets/images/Public/dwq-logo.png'
									)}
									alt='logo'
									className={`${
										props.meta && props.meta.menu == 'hide'
											? 'padding-lr-20'
											: 'padding-lr-10'
									}  width-auto height-40`}
								/> */}
								</div>

								{/* 系统切换 开始 */}
								{routerList.length > 1 && (
									<Dropdown
										menu={{
											items: routerList.map((item, index) => {
												return {
													key: index,
													label: (
														<div className={curSystemInfo.perms === item.perms ? 'color-165dff' : ''}>
															{item?.meta?.title || ''}
														</div>
													),
												};
											}),
											onClick: checkedSystem,
										}}
									>
										<CaretDownOutlined className="hover-color-165dff cursor-pointer" />
									</Dropdown>
								)}
								{/* 系统切换 结束 */}
							</Space>
						</div>
						<div className="flex align-center font-size-16 font-weight-500">
							{/* 招商系统特有 查看大屏 开始 */}
							{(curSystemInfo.perms || '').includes('bidmgt') && (
								<Permission hasPermi={['bidmgt:daping']}>
									<div
										className="width-102 height-32 border-radius-16 flex justify-center align-center background-no-repeat background-position-center-center background-size-100-100 a margin-right-20"
										style={{
											backgroundImage: `url(${getImageSrc('@/assets/images/Public/bg-big-screen.png')})`,
										}}
										onClick={() => {
											if (!document.fullscreenElement) {
												document.documentElement.requestFullscreen();
											}
											linkTo('/bidmgt/daping');
										}}
									>
										<img className="width-18 height-18" src={getImageSrc('@/assets/images/Public/big-screen-enter.png')} />
										<div className="margin-left-2 line-height-22 font-size-14 color-ffffff">查看大屏</div>
									</div>
								</Permission>
							)}
							{/* 招商系统特有 查看大屏 结束 */}

							{/* 招商 特有消息 开始 */}
							{(curSystemInfo.perms || '').includes('bidmgt') && <ZsMessage />}
							{/* 招商 特有消息 结束 */}

							<Dropdown
								placement="bottomRight"
								dropdownRender={() => {
									return (
										<div className="width-180 border-radius-8 bg-color-ffffff box-shadow  padding-tb-4 color-4e5969">
											<EditPasswordModal className="a flex align-center justify-center padding-tb-12 line-height-22 font-size-16 hover-color-165dff">
												<div className="margin-right-8" style={{ lineHeight: 0 }}>
													<FormOutlined
														style={{
															fontSize: '16px',
														}}
													/>
												</div>
												修改密码
											</EditPasswordModal>
											<div className="space-line margin-lr-8"></div>
											<div
												className="a flex align-center justify-center padding-lr-0 padding-tb-8 line-height-22 font-size-16 hover-color-165dff"
												onClick={loginOut}
											>
												<div className="margin-right-8" style={{ lineHeight: 0 }}>
													<LogoutOutlined
														style={{
															fontSize: '16px',
														}}
													/>
												</div>
												退出登录
											</div>
										</div>
									);
								}}
							>
								<div
									className="flex align-center a border-radius-16 flex justify-center align-center margin-left-16  margin-right-10"
									onClick={(e) => e.preventDefault()}
								>
									<img
										src={userInfo.wxAvatarUrl || getImageSrc('@/assets/images/Public/defaultAvatar.png')}
										alt="arrow-down"
										className="margin-right-8 width-40 height-40 border-radius-40 overflow-hidden bg-color-e3f4fc border-color-e3f4fc"
									/>
									<div className="font-weight-500 line-height-22">{userInfo.userName || '-'}</div>
								</div>
							</Dropdown>
						</div>
					</div>
				</div>
			</div>
		</div>
	);
};

export default memo(Index);
