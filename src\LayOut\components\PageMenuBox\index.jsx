import { Menu } from 'antd';
import { useEffect, useState } from 'react';
import { useDispatch } from 'react-redux';
import { useRouterLink } from '@/hook/useRouter';
import SvgIcon from '@/assets/icons';

import store from '@/store';
import { setCurSystemInfo } from '@/store/userSlice';

import { getCurSystemName } from '@/utils/common';

import './index.scss';
import {TaskCount} from "@/LayOut/components/PageMenuBox/index-new";

function loop(list = [], pPath = '') {
	const newItem = [];
	list.filter((item) => !(item.meta && item.meta.hidden)).forEach((ov) => {
		if (ov.meta && ov.path) {
			const item = {
				key: `${pPath}/${ov.path}`,
				icon: ov.icon ? <SvgIcon type={ov.icon} /> : null,
				label: !['businessOppty:personalCenter'].includes(ov.perms) ? (
					ov.meta.title
				) : (
					<div className="flex align-center justify-between">
						<div className="flex-sub text-cut">{ov.meta.title}</div>
						<TaskCount perms={ov.perms} />
					</div>
				),
			};

			// 判断子元素是否显示个数
			const childrenLen = ov.children && ov.children.length ? ov.children.filter((item) => item.meta && item.meta.hidden === false).length : 0;
			// 大于1个显示 小于不显示子集
			if (childrenLen > 1) {
				const list = loop(ov.children, item.key);
				item.children = list.length ? list : null;
			}
			newItem.push(item);
		}
	});
	return newItem;
}

const getPathList = (pathname) => {
	const pathList = [];
	if (pathname) {
		pathname
			.split('/')
			.filter((ov) => ov)
			.reduce((pre, cur) => {
				const path = (pre += `/${cur}`);
				pathList.push(path);
				return path;
			}, '');
	}
	return pathList;
};

// 顶部导航栏 按钮
const PageMenuBox = (props = {}) => {
	const dispatch = useDispatch();
	const { linkTo, location } = useRouterLink();

	const curSystemInfo = store.getState().user.curSystemInfo;
	const routerList = store.getState().user.routerList;

	const [selectedKeys, setSelectedKeys] = useState([]);
	const [openKeys, setOpenKeys] = useState(getPathList(location.pathname));
	const [menuList, setMenuList] = useState([]);

	useEffect(() => {
		// 为空 根据路由选中系统 路由为空就默认第一个
		if (curSystemInfo.menuList.length) {
			setMenuList([...loop(curSystemInfo.menuList, `/${curSystemInfo.path}`), ...loop(routerList.filter((item) => item.perms === 'system'))]);
		} else {
			const list = routerList.filter((item) => item.perms && item.perms !== 'system');

			const perms = getCurSystemName() || (getPathList(location.pathname)[0] || '').replace('/', '');

			let index = (perms && list.findIndex((item) => item.perms === perms)) || 0;

			index = index > -1 ? index : 0;
			const {
				path,
				meta: { title },
				children,
			} = list[index];
			document.title = title;
			if (perms) {
				dispatch(
					setCurSystemInfo({
						perms,
						path,
						title,
						menuList: children,
					})
				);
			}
		}
	}, [curSystemInfo, JSON.stringify(routerList), location.pathname]);

	useEffect(() => {
		setSelectedKeys(getPathList(location.pathname));
	}, [location.pathname]);
	return (
		props.display && (
			<div className="max-content-height overflowY-auto scrollbar bg-color-ffffff width-220 padding-8 border-box flex-shrink">
				<Menu
					className="menu-box"
					onClick={(e) => linkTo(e.key)}
					defaultSelectedKeys={selectedKeys}
					selectedKeys={selectedKeys}
					defaultOpenKeys={openKeys}
					mode="inline"
					items={menuList}
					inlineCollapsed={props.inlineCollapsed}
				/>
			</div>
		)
	);
};
export default PageMenuBox;
