import { useSelector, useDispatch } from "react-redux";
import { setSwitchSystemOpen, setCurSystemInfo } from "@/store/userSlice";
import { useRouterLink } from "@/hook/useRouter";
import { getImageSrc } from "@/assets/images";

import { Modal } from "antd";
import SvgIcon from "@/assets/icons";

const Index = (props = {}) => {
    const { linkTo } = useRouterLink();
    const dispatch = useDispatch();

    const switchSystemOpen = useSelector((state) => {
        return state.user.switchSystemOpen;
    });

    const curSystemInfo = useSelector((state) => {
        return state.user.curSystemInfo;
    });

    // 系统列表
    const routerList = useSelector((state) => {
        return state.user.routerList || [];
    }).filter((item) => item.perms && item.perms !== "system");

    // 关闭
    const close = () => {
        dispatch(setSwitchSystemOpen(false));
    };

    // 切换系统
    const switchSystem = (index) => {
        const {
            path,
            meta: { title, logoUrl },
            children,
            perms,
        } = routerList[index];
        if (curSystemInfo.perms !== perms) {
            dispatch(
                setCurSystemInfo({
                    perms,
                    path,
                    title,
                    logoUrl,
                    menuList: children,
                })
            );
            document.title = title;
            linkTo(`/${path}`);
        }
        close();
    };

    return (
        <Modal
            title="切换系统"
            open={switchSystemOpen}
            width={600}
            footer={null}
            centered
            onCancel={close}
        >
            {routerList.map((item, index) => {
                return (
                    <div
                        className={`a flex align-center padding-12 border-radius-4 ${
                            curSystemInfo.perms === item.perms
                                ? "color-165dff"
                                : "color-333333"
                        } hover hover-165dff`}
                        key={item.perms}
                        type="primary"
                        onClick={() => {
                            switchSystem(index);
                        }}
                    >
                        <img
                            className="width-32 height-32 margin-right-16"
                            src={
                                item?.meta?.logoUrl ||
                                getImageSrc(
                                    "@/assets/images/Login/tenant-icon.png"
                                )
                            }
                        />
                        <div className="flex-sub line-height-20 font-size-14">
                            {item?.meta?.title || ""}
                        </div>
                        {curSystemInfo.perms === item.perms ? (
                            <div className="padding-right-14">当前选中</div>
                        ) : (
                            <SvgIcon
                                className="opacity-70"
                                style={{ fontSize: "22px" }}
                                type="icon-arrow-right"
                            />
                        )}
                    </div>
                );
            })}
        </Modal>
    );
};

export default Index;
