import { Dropdown, Modal, Space, Button } from 'antd';
import { useEffect, useState, memo } from 'react';
import { useRouterLink } from '@/hook/useRouter';
import { getImageSrc } from '@/assets/images/index';

import Permission from '@/components/Permission';
import { notificationPage, updateReadFlag } from '@/api/Bidmgt/PersonalCenter/index';

// 顶部导航栏 按钮
const Index = (props = {}) => {
	const { linkTo, openNewTab, location } = useRouterLink();

	const [dataSource, setDataSource] = useState([]);
	const [total, setTotal] = useState(0);

	const getList = () => {
		return new Promise((resolve) => {
			notificationPage(
				{
					pageNum: 1,
					pageSize: 5,
					readFlag: 0,
				},
				{
					showLoading: !true,
					isWhiteList: true,
				}
			).then((res) => {
				setDataSource(res.data.records || []);
				setTotal(res.data.total - 0);
				resolve();
			});
		});
	};
	useEffect(() => {
		getList();
	}, [location.pathname]);

	const [detail, setDetail] = useState({});
	const [isModalOpen, setIsModalOpen] = useState(false);
	const showModal = (data) => {
		updateReadFlag({
			id: data.id,
		}).then(() => {
			setDetail(data);
			setIsModalOpen(true);
			getList();
		});
	};
	const handleOk = () => {
		const obj = {
			// 预警
			1: `/bidmgt/ProjectManage/detail?id=${detail.projectId}`,
			// 催办
			2: `/bidmgt/ProjectManage/detail?id=${detail.projectId}`,
			// 系统消息
			3: '',
			// 指派消息
			4: `/bidmgt/taskCenter/detail?id=${detail.projectId}&toDoListType=${detail.toDoListType}&todoId=${detail.id}`,
			// 项目审核消息
			5: `/bidmgt/taskCenter/detail?id=${detail.projectId}&toDoListType=${detail.toDoListType}&todoId=${detail.id}`,
			// 审核通过消息
			6: `/bidmgt/ProjectManage/detail?id=${detail.projectId}`,
			// 审核不通过消息
			7: `/bidmgt/ProjectManage/detail?id=${detail.projectId}`,
			// 接受认领消息
			8: `/bidmgt/ProjectManage/detail?id=${detail.projectId}`,
			// 拒绝认领消息
			9: `/bidmgt/ProjectManage/detail?id=${detail.projectId}`,
			// 消息1
			10: '',
			// 消息1
			11: '',
			// 消息1
			12: '',
			// 消息1
			13: '',
		};
		setIsModalOpen(false);
		if (obj[detail.noticeType]) {
			openNewTab(obj[detail.noticeType]);
		}
	};
	const handleCancel = () => {
		setIsModalOpen(false);
		setDetail({});
	};

	return (
		<>
			<Permission hasPermi={['bidmgt:zsMessage']}>
				<Dropdown
					overlayClassName="z-index-10"
					placement="bottom"
					dropdownRender={() => {
						return (
							<div className="width-400 border-radius-4 bg-color-ffffff box-shadow  color-4e5969">
								{dataSource.map((ov) => {
									return (
										<div
											key={ov.id}
											className="a flex justify-start align-center padding-16 border-solid-bottom-f2f3f5"
											onClick={(e) => {
												e.preventDefault();
												showModal(ov);
											}}
										>
											<div className="widht-50 height-50 position-relative">
												<img
													src={getImageSrc('@/assets/images/PageHeaderBox/icon-message-list.png')}
													alt="msg"
													className="widht-50 height-50"
												/>
												<div className="width-8 height-8 border-radius-10 bg-color-f53f3f position-absolute left-44 top-0 transform-y-50per"></div>
											</div>
											<div className="flex-sub margin-left-8">
												<div className="font-bold font-size-16 line-height-24 text-cut">
													【{ov.noticeType == 1 ? '预警' : ''}
													{ov.noticeType == 2 ? '催办' : ''}
													{ov.noticeType == 3 ? '系统消息' : ''}
													{ov.noticeType == 4 ? '指派消息' : ''}
													{ov.noticeType == 5 ? '项目审核消息' : ''}
													{ov.noticeType == 6 ? '审核通过消息' : ''}
													{ov.noticeType == 7 ? '审核不通过消息' : ''}
													{ov.noticeType == 8 ? '接受认领消息' : ''}
													{ov.noticeType == 9 ? '拒绝认领消息' : ''}
													{ov.noticeType == 10 ? '消息' : ''}
													{ov.noticeType == 11 ? '消息' : ''}
													{ov.noticeType == 12 ? '消息' : ''}
													{ov.noticeType == 13 ? '消息' : ''}】{ov.noticeTitle || ''}
												</div>
												<div className="font-size-14 line-height-24 line-height-22 text-cut color-4e5969">
													{ov.noticeContent || ''}
												</div>
											</div>
										</div>
									);
								})}
								<div
									className="color-165dff flex align-center justify-center height-46 a"
									onClick={() => {
										openNewTab('/bidmgt/zsMessage');
									}}
								>
									<div>查看更多</div>
									<img src={getImageSrc('@/assets/images/Public/arrow-right-blue.png')} alt="msg" className="widht-16 height-16" />
								</div>
							</div>
						);
					}}
				>
					<div
						className="width-40 height-40 border-radius-16 flex justify-center align-center bg-color-f7f8fa a margin-right-16 position-relative"
						onClick={(e) => e.preventDefault()}
					>
						<img className="width-18 height-18" src={getImageSrc('@/assets/images/PageHeaderBox/icon-xiaoxi.png')} />
						{total > 0 && (
							<div className="position-absolute top-0 left-30 min-width-16 height-16 font-size-12 line-height-16 text-align-center color-ffffff bg-color-f53f3f padding-lr-4 border-box border-radius-10">
								{total}
							</div>
						)}
					</div>
				</Dropdown>
			</Permission>

			<Modal
				title="消息详情"
				open={isModalOpen}
				onCancel={handleCancel}
				footer={() => (
					<Space>
						<Button onClick={handleCancel}>关闭</Button>
						{/* <Button type="primary" onClick={handleOk}>查看详情</Button> */}
					</Space>
				)}
			>
				<div className="font-bold font-size-16 line-height-24">
					【{detail.noticeType == 1 ? '预警' : ''}
					{detail.noticeType == 2 ? '催办' : ''}
					{detail.noticeType == 3 ? '系统消息' : ''}
					{detail.noticeType == 4 ? '指派消息' : ''}
					{detail.noticeType == 5 ? '项目审核消息' : ''}
					{detail.noticeType == 6 ? '审核通过消息' : ''}
					{detail.noticeType == 7 ? '审核不通过消息' : ''}
					{detail.noticeType == 8 ? '接受认领消息' : ''}
					{detail.noticeType == 9 ? '拒绝认领消息' : ''}
					{detail.noticeType == 10 ? '消息' : ''}
					{detail.noticeType == 11 ? '消息' : ''}
					{detail.noticeType == 12 ? '消息' : ''}
					{detail.noticeType == 13 ? '消息' : ''}】{detail.noticeTitle || ''}
				</div>
				<div className="font-size-14 line-height-24 line-height-22 color-4e5969">{detail.noticeContent || ''}</div>
			</Modal>
		</>
	);
};

export default memo(Index);
