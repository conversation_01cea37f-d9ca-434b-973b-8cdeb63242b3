import { useState, useEffect, memo } from 'react';
import { useSelector } from 'react-redux';

import { Outlet } from 'react-router-dom';
import { Spin, Popover } from 'antd';
import SvgIcon from '@/assets/icons';
import PageMenuBox from '@/LayOut/components/PageMenuBox/index-new';
import SwitchSystem from '@/LayOut/components/SwitchSystem/index';
import Permission from '@/components/Permission';

import { stateLoading } from '@/hook/useLoading';
import { useRouterLink } from '@/hook/useRouter';
import { useUserHandle } from '@/hook/useUserHandle';
import { getImageSrc } from '@/assets/images/index';

import { notificationPage } from '@/api/Bidmgt/PersonalCenter/index';

import './index.scss';

// logo 标题
const LogoTitle = (props = {}) => {
	const { linkTo } = useRouterLink();
	// 当前选中系统信息
	const curSystemInfo = useSelector((state) => {
		return state.user.curSystemInfo;
	});
	const systemName = useSelector((state) => {
		return state.user.systemName;
	});
	const systemLogo = useSelector((state) => {
		return state.user.systemLogo;
	});
	return (
		<div className="flex align-center justify-center">
			{(curSystemInfo.logoUrl || systemLogo) && <img className="margin-right-8 width-32 height-32" src={systemLogo || curSystemInfo.logoUrl} />}
			<div
				className={`line-height-24 ${curSystemInfo.title.length > 6 ? 'font-size-16' : 'font-size-18'} font-weight-500 color-1d2129`}
				onClick={() => {
					if (props.justShow) {
						return;
					}
					linkTo(`/${curSystemInfo.path}`);
				}}
			>
				{systemName || curSystemInfo.title}
			</div>
		</div>
	);
};

// 特别入口
const CustomEnter = () => {
	const { linkTo, openNewTab } = useRouterLink();

	const { curSystemInfo } = useUserHandle();

	// 消息统计
	const [zsMessageTotal, setZsMessageTotal] = useState(0);

	const getZsMessageTotal = () => {
		if (zsMessageTotal > 0 || !(curSystemInfo.perms || '').includes('bidmgt')) {
			return;
		}
		notificationPage(
			{
				pageNum: 1,
				pageSize: 1,
				readFlag: 0,
			},
			{
				showLoading: !true,
				isWhiteList: true,
			}
		).then((res) => {
			setZsMessageTotal(res.data.total - 0);
		});
	};

	// 获取消息
	useEffect(() => {
		getZsMessageTotal();
	}, [location.pathname]);

	return (
		<>
			{/* 招商 特有消息 开始 */}
			{(curSystemInfo.perms || '').includes('bidmgt') && (
				<Permission hasPermi={['bidmgt:zsMessage']}>
					<div
						className="a position-relative flex align-center margin-4 padding-left-24 padding-right-34 height-40"
						onClick={() => {
							openNewTab('/bidmgt/zsMessage');
						}}
					>
						<SvgIcon style={{ fontSize: '18px' }} type="icon-notice" />
						<div className="flex-sub margin-left-10">消息通知</div>
						{zsMessageTotal > 0 ? (
							<div className="position-absolute right-16 top-50per transform-y-50per">
								<div className="flex align-center justify-center width-16 height-16 border-radius-16 font-size-10 bg-color-f53f3f color-ffffff">
									{zsMessageTotal > 99 ? '99+' : zsMessageTotal}
								</div>
							</div>
						) : (
							<SvgIcon
								className="position-absolute right-16 top-50per transform-y-50per"
								style={{ fontSize: '16px' }}
								type="icon-arrow-right"
							/>
						)}
					</div>
				</Permission>
			)}
			{/* 招商 特有消息 结束 */}
			{/* 招商特有 查看大屏  开始 */}
			{(curSystemInfo.perms || '').includes('bidmgt') && (
				<Permission hasPermi={['bidmgt:daping']}>
					<div
						className="a position-relative flex align-center margin-4 padding-left-24 padding-right-34 height-40"
						onClick={() => {
							if (!document.fullscreenElement) {
								document.documentElement.requestFullscreen();
							}
							linkTo('/bidmgt/daping');
						}}
					>
						<SvgIcon style={{ fontSize: '18px' }} type="icon-dashboard" />
						<div className="flex-sub margin-left-10">查看大屏</div>
						<SvgIcon
							className="position-absolute right-16 top-50per transform-y-50per"
							style={{ fontSize: '16px' }}
							type="icon-arrow-right"
						/>
					</div>
				</Permission>
			)}
			{/* 招商特有 查看大屏 结束 */}
		</>
	);
};

/// 用户操作
const UserHandle = () => {
	const { userInfo, loginOut, switchSystem, routerList } = useUserHandle();

	return (
		<Popover
			rootClassName="user-handle-box"
			placement="right"
			arrow={false}
			content={(e) => {
				return (
					<div>
						<div className="layout-user-handle-bg padding-top-30 padding-bottom-20 padding-lr-24">
							<LogoTitle justShow={true} />
						</div>
						<div className="padding-bottom-20">
							{routerList.length > 1 && (
								<div className="a flex align-center padding-tb-10 padding-lr-24 hover-color-165dff" onClick={switchSystem}>
									<SvgIcon style={{ fontSize: '16px' }} type="icon-yqfqiehuan" />
									<div className="margin-left-12 line-height-22 font-size-16">切换系统</div>
								</div>
							)}
							<div className="a flex align-center padding-tb-10 padding-lr-24 hover-color-165dff" onClick={loginOut}>
								<SvgIcon style={{ fontSize: '16px' }} type="icon-logout" />
								<div className="margin-left-12 line-height-22 font-size-16">退出登录</div>
							</div>
						</div>
					</div>
				);
			}}
		>
			<div className="a position-relative flex align-center margin-4 padding-left-24 padding-right-34 height-40">
				<img
					className="width-18 height-18 border-radius-18"
					src={userInfo.wxAvatarUrl || getImageSrc('@/assets/images/Public/defaultAvatar.png')}
				/>
				<div className="flex-sub margin-left-10">{userInfo.userName || '-'}</div>
				<SvgIcon className="position-absolute right-16 top-50per transform-y-50per" style={{ fontSize: '16px' }} type="icon-arrow-right" />
			</div>
			<></>
		</Popover>
	);
};

const Index = (props = {}) => {
	const { loading } = stateLoading();
	const { layOutClass = '', menuLayout = true } = props.meta || {};

	// 点击菜单刷新路由
	const [state, setState] = useState('');
	const forceUpdate = (str) => {
		setState(str);
	};
	useEffect(() => {
		if (state) {
			setTimeout(() => {
				setState('');
			}, 100);
		}
	}, [state]);

	return (
		<Spin className="height-100vh" spinning={loading}>
			{/* 左侧 开始 */}
			{menuLayout && (
				<div className="z-index-2 position-fixed left-0 top-0 flex flex-direction-column padding-top-10 padding-bottom-20 padding-left-16 width-210 height-100vh border-box">
					{/* 顶部 logo 开始 */}
					<div className="a flex align-center justify-center height-48">
						<LogoTitle />
					</div>
					{/* 顶部 logo 结束 */}
					{/* 导航 开始 */}
					<div className="position-relative flex-sub overflowY-auto scrollbar">
						<PageMenuBox display={menuLayout} inlineCollapsed={false} state={state} updateFn={forceUpdate} />
					</div>
					{/* 导航 结束 */}
					{/* 底部 操作 开始 */}
					<CustomEnter />
					<UserHandle />
					{/* 底部 操作 结束 */}
				</div>
			)}
			{/* 左侧 结束 */}
			{/* 右侧 开始 */}
			<div
				className={`layout-bg flex flex-direction-column min-height-100vh padding-top-10 padding-bottom-20 border-box ${
					menuLayout && 'padding-left-216'
				} ${layOutClass}`}
			>
				<div className="position-relative flex-sub overflowX-auto">
					{/* 定位层 如过想侧边全屏就用这个 */}
					{/* <div className="position-absolute inset-0 margin-auto bg-color-ff0000"></div> */}
					{/* 正常限定宽度就 用这个 默认都是这个 可以用定位覆盖他 */}
					{/* <div className="right-box-width"></div> */}
					<div className="right-box-width">{state ? null : <Outlet />}</div>
				</div>
			</div>
			{/* 右侧 结束 */}
			{/* 切换系统 开始 */}
			<SwitchSystem />
			{/* 切换系统 结束 */}
		</Spin>
	);
};

export default memo(Index);
