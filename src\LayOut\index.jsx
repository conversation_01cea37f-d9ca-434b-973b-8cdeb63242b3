import { Outlet } from "react-router-dom";
import PageHeaderBox from "./components/PageHeaderBox/index";
import PageMenuBox from "./components/PageMenuBox/index";
import { Spin } from "antd";
import { stateLoading } from "@/hook/useLoading";
import { stateMenu } from "@/hook/useMenu";
import { useState, useEffect, memo } from "react";
import store from "@/store";
import "./index.scss";

const Index = (props = {}) => {
    const { loading } = stateLoading();
    const layout = store.getState().layout || {};
    const { layOutClass = "", menuLayout = true } = props.meta || {};

    // 点击菜单刷新路由
    const [state, setState] = useState("");
    const forceUpdate = (str) => {
        setState(str);
    };
    useEffect(() => {
        if (state) {
            setTimeout(() => {
                setState("");
            }, 100);
        }
    }, [state]);

    return (
        <Spin className="min-height-100vh" spinning={loading}>
            <div
                className={`flex flex-direction-column min-height-100vh ${layOutClass}`}
            >
                {/* 头部 开始 */}
                {layout.hideHeader ? null : (
                    <PageHeaderBox meta={props.meta || {}} />
                )}
                {/* 头部 结束 */}
                <div className="flex-sub flex">
                    {/* 左侧 开始 */}
                    <PageMenuBox
                        display={menuLayout}
                        inlineCollapsed={false}
                        state={state}
                        updateFn={forceUpdate}
                    />
                    {/* 左侧 结束 */}
                    {/* 右侧 开始 */}
                    <div
                        className="position-relative flex-sub flex flex-direction-column min-content-height overflowY-auto scrollbar"
                        id="layout-scrollbar"
                    >
                        <div className="position-absolute inset-0 margin-auto">
                            {state ? null : <Outlet />}
                        </div>
                    </div>
                    {/* 右侧 结束 */}
                </div>
            </div>
        </Spin>
    );
};

export default memo(Index);
