import { request } from "@/utils/axios";

/**
 * 获取机构 分页
 * @param {*} data
 * id           机构id
 * keyWord      关键字
 * pageSize     每页数量
 * pageNum      页码
 * @returns
 */
export const pageAgency = (data) => {
  return request({
    url: '/achv/agency/pageAgency',
    method: 'post',
    data
  })
}

/**
 * 新增机构
 * @param {*} data
 * name	              名称
 * introduction	      简介
 * address	          地址
 * logo	              logo
 * rankingNum	        排名序号
 * phoneNo	          电话号码
 * studyDirectionList	研究领域方向id
 * @returns
 */
export const agencyAdd = (data) => {
  return request({
    url: '/achv/agency/add',
    method: 'post',
    data
  })
}

/**
 * 更新机构
 * @param {*} data
 * id                 机构id
 * name	              名称
 * introduction	      简介
 * address	          地址
 * logo	              logo
 * rankingNum	        排名序号
 * phoneNo	          电话号码
 * studyDirectionList	研究领域方向id
 * @returns
 */
export const agencyUpdate = (data) => {
  return request({
    url: '/achv/agency/update',
    method: 'post',
    data
  })
}

/**
 * 删除机构
 * @param {*} data
 * ids 删除 机构id数组
 * @returns
 */
export const agencyDel = (data) => {
  return request({
    url: '/achv/agency/batchDel',
    method: 'post',
    data
  })
}

/**
 * 获取机构详情
 * @param {*} data
 * id 机构id
 * @returns
 */
export const getAgencyDetail = (data) => {
  return request({
    url: '/achv/agency/getAgency',
    method: 'post',
    data
  })
}

/**
 * 搜索机构列表
 * @param {*} data
 * keyWord 关键词
 * @returns
 */
export const searchAgency = (data) => {
  return request({
    url: '/achv/agency/searchAgency',
    method: 'post',
    data,
		showLoading:!true,
  })
}



/**
 * 获取意向合作列表
 * @param {*} data
 * id                 合作需求id
 * cooperateType	    合作类型：1 成果合作需求 2 需求合作意向 3机构合作详情
 * cooperateProjectId	合作项目id:对应成果，需求，机构id
 * pageSize           每页数量
 * pageNum            页码
 * @returns
 */
export const getCooperateList = (data) => {
  return request({
    url: '/achv/cooperate/page',
    method: 'post',
    data
  })
}


/**
 * 获取意向合作列表
 * @param {*} data
 * id                 合作需求id
 * ids                合作需求ids
 * @returns
 */
export const delCooperate = (data) => {
  return request({
    url: '/achv/cooperate/del',
    method: 'post',
    data
  })
}
/**
 * 获取所有机构
 * @param {*} data
 * id                 合作需求id
 * ids                合作需求ids
 * @returns
 */
export const getAgencyToBasic = (data={}) => {
  return request({
    url: '/achv/agency/getAgencyToBasic',
    method: 'post',
    data
  })
}

/**
 * 导出机构
 * @param {object} params 机构查询dto
 * @returns
 */
export function exportAgency(data={}) {
  return request({
    url: '/achv/export/exportAgency',
    method: 'post',
    data,
		responseType: 'blob',
    timeout: 60000 * 5
  })
}

/**
 * 审核机构
 * @param {object} params 专家审核对象
 * @param {number} params.id id
 * @param {string} params.reason 不通过原因
 * @param {number} params.status 审核状态:1 审核中 2 审核不通过  3 审核通过
 * @returns
 */
export function auditAgent(params) {
  return request({
    url: `/achv/agency/audit`,
    method: 'post',
    data: params,
  });
}
