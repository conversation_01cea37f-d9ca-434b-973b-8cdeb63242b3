import { request } from '@/utils/axios';

/**
 * 获取成果 分页
 * @param {*} data
 * id           成果id
 * releaseType  发布来源类型：1 后台发布 2小程序发布
 * keyWord      关键字
 * auditStatus  审核状态 1 审核中 2 审核不通过  3 审核通过
 * pageSize     每页数量
 * pageNum      页码
 * @returns
 */
export const pageAchievement = (data) => {
	return request({
		url: '/achv/achievement/pageAchievement',
		method: 'post',
		data,
	});
};

/**
 * 新增成果
 * @param {*} data
 * stageId	        成果研发阶段id
 * name	            成果名称
 * achvtImgUrl	    成果封面图url
 * detailsContent   详情内容
 * orgId	          所属机构id
 * rankingNum	      排名序号
 * areaCategoryList	领域id
 * transformList	  转化方式id
 * @returns
 */
export const achievementAdd = (data) => {
	return request({
		url: '/achv/achievement/add',
		method: 'post',
		data,
	});
};

/**
 * 更新成果
 * @param {*} data
 * id	              主键id
 * name	            成果名称
 * orgId	          所属机构id
 * areaCategoryList	领域id
 * stageId	        成果研发阶段id
 * transformList	  转化方式id
 * achvtImgUrl	    成果封面图url
 * detailsContent   详情内容
 * rankingNum	      排名序号
 * @returns
 */
export const achievementUpdate = (data) => {
	return request({
		url: '/achv/achievement/update',
		method: 'post',
		data,
	});
};

/**
 * 删除成果
 * @param {*} data
 * ids 删除数组id
 * @returns
 */
export const achievementDel = (data) => {
	return request({
		url: '/achv/achievement/batchDel',
		method: 'post',
		data,
	});
};

/**
 * 获取成果详情
 * @param {*} data
 * id
 * @returns
 */
export const getAchievementDetail = (data) => {
	return request({
		url: '/achv/achievement/getAchievement',
		method: 'post',
		data,
	});
};

/**
 * 导出成果
 * @param {object} params 成果查询dto
 * @param {number} params.pageNum 页码
 * @param {number} params.pageSize 每页记录数
 * @param {number} params.auditStatus 审核状态： 审核状态:1 审核中 2 审核不通过  3 审核通过（已发布）
 * @param {number} params.orgId 机构id
 * @param {number} params.id 成果id
 * @param {array} params.ids 成果ids（批量操作接口）
 * @param {string} params.keywords 关键字
 * @param {number} params.areaCategoryId 领域id
 * @param {number} params.releaseType 发布来源类型：1 后台发布 2小程序发布
 * @param {number} params.stageId 阶段id
 * @param {number} params.transformId 转化方式id
 * @param {number} params.isUpdate  是否修改 0 不是修改的 1 是修改的
 * @param {array} params.provinceCodes 多选所属省份code
 * @param {array} params.cityCodes 多选所属城市code
 * @param {array} params.areaCodes 多选所属区域code
 * @param {string} params.sortValue 排序值(传)：desc/asc
 * @param {string} params.sortKey 排序字段,传返回字段名称如：时间-updateTime
 * @param {number} params.tenantId
 * @param {array} params.excludeIds 成果ids（排除的结果id）
 * @returns
 */
export function exportList(data = {}) {
	return request({
		url: '/achv/achievement/exportAchievement',
		method: 'post',
		data,
		responseType: 'blob',
	});
}

/**
 * 成果审核
 * @param {object} params 审核状态dto
 * @param {number} params.id 当前审核数据主键id
 * @param {number} params.status 审核状态:  2 审核不通过  3 审核通过
 * @param {string} params.reason 审核原因
 * @returns
 */
export function auditAchievement(data = {}) {
	return request({
		url: '/achv/achievement/audit',
		method: 'post',
		data,
		responseType: 'blob',
	});
}

/**
 * 更新排序
 * @param {object} params 需求与成果 排序更新dto
 * @param {number} params.id
 * @param {number} params.rankingNum
 * @param {array} params.saveDtoList
 * @returns
 */
export function updateRankingNum(data = {}) {
	return request({
		url: '/achv/achievement/updateRankingNum',
		method: 'post',
		data,
		responseType: 'blob',
	});
}

/**
 * 成果统计
 * @param {object} params 查询dto
 * @param {number} params.pageNum 页码
 * @param {number} params.pageSize 每页记录数
 * @param {string} params.keywords 名称
 * @param {string} params.signUpName 参赛人员名称
 * @param {number} params.competitionGroup 大赛组别：1 全国技术经理人组 2 江夏技术经理人组 3武汉高校大学生组
 * @param {number} params.releaseType 发布类型：1 后台管理员发布 2 小程序发布  3 大赛发布
 * @param {array} params.releaseTypeList 发布类型：1 后台管理员发布 2 小程序发布  3 大赛发布
 * @param {array} params.provinceCodes 多选所属省份code
 * @param {array} params.cityCodes 多选所属城市code
 * @param {array} params.areaCodes 多选所属区域code
 * @param {number} params.auditStatus 审核状态： 审核状态:1 审核中 2 审核不通过  3 审核通过（已发布）
 * @returns
 */
export function queryAchievementStatistics(params = {}, option = {}) {
	return request({
		url: `/achv/achievement/queryAchievementStatistics`,
		method: 'POST',
		data: params,
		...option,
	});
}
