import { request } from '@/utils/axios';

/**
 * 分页查询九宫格活动
 * @param {object} params 活动查询dto
 * @param {number} params.pageNum 页码
 * @param {number} params.pageSize 每页记录数
 * @param {number} params.id  id
 * @param {array} params.regionIds 多选所属区域id
 * @param {string} params.keywords 关键字
 * @param {array} params.ids ids
 * @param {number} params.activityType 活动类型：1普通活动 2九宫格活动
 * @param {number} params.recommendStatus 推荐状态： 0否 1是
 * @returns
 */
export function gridPage(data = {}, option = {}) {
	return request({
		url: `/achv/activity/gridPage`,
		method: 'POST',
		data,
		...option,
	});
}

/**
 * 删除活动详情
 * @param {object} params 活动查询dto
 * @param {number} params.pageNum 页码
 * @param {number} params.pageSize 每页记录数
 * @param {number} params.id  id
 * @param {array} params.regionIds 多选所属区域id
 * @param {string} params.keywords 关键字
 * @param {array} params.ids ids
 * @param {number} params.activityType 活动类型：1普通活动 2九宫格活动
 * @param {number} params.recommendStatus 推荐状态： 0否 1是
 * @returns
 */
export function delActivity(data = {}, option = {}) {
	return request({
		url: `/achv/activity/del`,
		method: 'POST',
		data,
		...option,
	});
}

/**
 * 新增活动详情
 * @param {object} params 活动保存dto
 * @param {number} params.id 主键id
 * @param {number} params.regionId 所属区域id
 * @param {string} params.title 活动标题
 * @param {string} params.guidanceOrg 指导单位
 * @param {string} params.hostOrg 主办单位
 * @param {string} params.undertakeOrg 承办单位
 * @param {string} params.venue 活动地点
 * @param {string} params.introduce 活动介绍
 * @param {object} params.startTime 活动开始时间
 * @param {object} params.endTime 结束开始时间
 * @param {string} params.coverImageUrl 封面图片地址
 * @param {string} params.provinceCode 所属省份code
 * @param {string} params.cityCode 所属城市code
 * @param {number} params.activityType 活动类型：1普通活动 2九宫格活动
 * @param {number} params.recommendStatus 推荐状态： 0否 1是
 * @returns
 */
export function addActivity(data = {}, option = {}) {
	return request({
		url: `/achv/activity/add`,
		method: 'POST',
		data,
		...option,
	});
}

/**
 * 修改活动详情
 * @param {object} params 活动保存dto
 * @param {number} params.id 主键id
 * @param {number} params.regionId 所属区域id
 * @param {string} params.title 活动标题
 * @param {string} params.guidanceOrg 指导单位
 * @param {string} params.hostOrg 主办单位
 * @param {string} params.undertakeOrg 承办单位
 * @param {string} params.venue 活动地点
 * @param {string} params.introduce 活动介绍
 * @param {object} params.startTime 活动开始时间
 * @param {object} params.endTime 结束开始时间
 * @param {string} params.coverImageUrl 封面图片地址
 * @param {string} params.provinceCode 所属省份code
 * @param {string} params.cityCode 所属城市code
 * @param {number} params.activityType 活动类型：1普通活动 2九宫格活动
 * @param {number} params.recommendStatus 推荐状态： 0否 1是
 * @returns
 */
export function updateActivity(data = {}, option = {}) {
	return request({
		url: `/achv/activity/update`,
		method: 'POST',
		data,
		...option,
	});
}


/**
 * 获取活动详情
 * @param {*} data
 * id 活动id
 * @returns
 */
export const getActivityDetail = (data) => {
	return request({
	  url: '/achv/activity/getDetail',
	  method: 'post',
	  data
	})
  }

  /**
 * 活动企业数据导入
 * @param {string} file
  * @returns
 */
export function activityEnterpriseImport(data) {
	return request({
		url: `/achv/activity/activityEnterpriseImport`,
		method: 'POST',
		data,
		header: {
			'content-type': 'multipart/form-data',
		},
	});
  }

/**
 * 活动配置详情
 * @param {object} params 活动配置参数
 * @param {number} params.eventId 活动唯一标识
 * @param {number} params.type 活动配置类型 1证书配置
 * @returns
 */
export function detailConfig(params) {
	return request({
		url: `/event/event/config/detail`,
		method: 'POST',
		data: params,
	});
}

/**
 * 活动配置保存
 * @param {object} params 活动配置VO
 * @param {number} params.id 配置唯一标识
 * @param {number} params.eventId 活动唯一标识
 * @param {number} params.type 活动配置类型 1证书配置 2.. 待定
 * @param {object} params.findConfig 证书查询配置
 * @param {object} params.resultConfig 证书结果配置
 * @returns
 */
export function saveConfig(params) {
	return request({
		url: `/event/event/config/save`,
		method: 'POST',
		data: params,
	});
}

/**
 * 活动配置删除
 * @param {string} id 配置唯一标识
 * @returns
 */
export function delConfig(id) {
	return request({
		url: `/event/event/config/del?id=${id}`,
		method: 'POST',
	});
}
