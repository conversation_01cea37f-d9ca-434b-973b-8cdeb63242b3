import { request } from '@/utils/axios';

/**
 * 分页查询机构
 * @param {object} params 机构查询dto
 * @param {number} params.pageNum 页码
 * @param {number} params.pageSize 每页记录数
 * @param {number} params.regionId 所属区域id
 * @param {array} params.regionIds 多选所属区域id
 * @param {number} params.agencyTypeId 机构类型id
 * @param {array} params.provinceCodes 多选所属省份code
 * @param {array} params.areaCodes 多选所属区域code
 * @param {array} params.cityCodes 多选所属城市code
 * @param {number} params.studyDirectionId 研究方向id
 * @param {number} params.id 机构id
 * @param {array} params.ids 机构ids
 * @param {string} params.keywords 关键字
 * @param {number} params.auditStatus
 * @param {number} params.tenantId
 * @param {number} params.hideStatus
 * @param {number} params.recommendShowStatus 推荐展示状态：0 不推荐 1 推荐
 * @param {string} params.name 机构名称
 * @returns
 */
export function pageAgency(params = {}, option = {}) {
	return request({
		url: `/achv/agency/pageAgency`,
		method: 'POST',
		data: params,
		...option,
	});
}

/**
 * 新增机构
 * @param {object} params 后台管理端：机构保存dto
 * @param {number} params.auditStatus 审核状态:1 审核中 2 审核不通过  3 审核通过
 * @param {number} params.id 主键id,修改时传
 * @param {string} params.name 名称
 * @param {string} params.introduction 简介
 * @param {number} params.regionId 所属区域id
 * @param {string} params.address 地址
 * @param {string} params.logo logo
 * @param {string} params.provinceCode 所属省份code
 * @param {string} params.cityCode 所属城市code
 * @param {number} params.rankingNum 排名序号
 * @param {string} params.phoneNo 系人电话号码
 * @param {string} params.contacts 联系人
 * @param {string} params.contactsPosition 联系人职位
 * @param {string} params.businessLicense 营业执照
 * @param {array} params.studyDirectionList 研究领域方向id
 * @param {number} params.recommendShowStatus 推荐展示状态：0 不推荐 1 推荐
 * @param {array} params.agencyTypeList 机构类型id
 * @param {string} params.mainBusiness 主营业务
 * @returns
 */
export function addAgency(params = {}, option = {}) {
	return request({
		url: `/achv/agency/add`,
		method: 'POST',
		data: params,
		...option,
	});
}

/**
 * 修改机构
 * @param {object} params 后台管理端：机构保存dto
 * @param {number} params.auditStatus 审核状态:1 审核中 2 审核不通过  3 审核通过
 * @param {number} params.id 主键id,修改时传
 * @param {string} params.name 名称
 * @param {string} params.introduction 简介
 * @param {number} params.regionId 所属区域id
 * @param {string} params.address 地址
 * @param {string} params.logo logo
 * @param {string} params.provinceCode 所属省份code
 * @param {string} params.cityCode 所属城市code
 * @param {number} params.rankingNum 排名序号
 * @param {string} params.phoneNo 系人电话号码
 * @param {string} params.contacts 联系人
 * @param {string} params.contactsPosition 联系人职位
 * @param {string} params.businessLicense 营业执照
 * @param {array} params.studyDirectionList 研究领域方向id
 * @param {number} params.recommendShowStatus 推荐展示状态：0 不推荐 1 推荐
 * @param {array} params.agencyTypeList 机构类型id
 * @param {string} params.mainBusiness 主营业务
 * @returns
 */
export function updateAgency(params = {}, option = {}) {
	return request({
		url: `/achv/agency/update`,
		method: 'POST',
		data: params,
		...option,
	});
}

/**
 * 根据id查询数据
 * @param {object} params 机构查询dto
 * @param {number} params.id 机构id
 * @param {array} params.ids 机构ids
 * @returns
 */
export function getAgency(params = {}, option = {}) {
	return request({
		url: `/achv/agency/getAgency`,
		method: 'POST',
		data: params,
		...option,
	});
}

/**
 * 批量根据id删除数据
 * @param {object} params 机构查询dto
 * @param {number} params.id 机构id
 * @param {array} params.ids 机构ids
 * @returns
 */
export function batchDelAgency(params = {}, option = {}) {
	return request({
		url: `/achv/agency/batchDelAgency`,
		method: 'POST',
		data: params,
		...option,
	});
}

/**
 * 审核机构
 * @param {object} params 专家审核对象
 * @param {number} params.id id
 * @param {string} params.reason 不通过原因
 * @param {number} params.status 审核状态:1 审核中 2 审核不通过  3 审核通过
 * @returns
 */
export function auditAgent(params = {}, option = {}) {
	return request({
		url: `/achv/agency/audit`,
		method: 'POST',
		data: params,
		...option,
	});
}

/**
 * 审核机构
 * @param {object} params 专家审核对象
 * @param {number} params.id id
 * @param {string} params.reason 不通过原因
 * @param {number} params.status 审核状态:1 审核中 2 审核不通过  3 审核通过
 * @returns
 */
export function auditDataStatistics(params = {}, option = {}) {
	return request({
		url: `/achv/agency/auditDataStatistics`,
		method: 'POST',
		data: params,
		...option,
	});
}

/**
 * 更新排序
 * @param {object} params
 * @param {number} params.id
 * @param {number} params.rankingNum
 * @param {array} params.saveDtoList
 * @returns
 */
export function updateRankingNum(data = {}) {
	return request({
		url: '/achv/agency/updateRankingNum',
		method: 'post',
		data,
		responseType: 'blob',
	});
}

/**
 * 导出机构
 * @param {object} params 机构查询dto
 * @param {number} params.pageNum 页码
 * @param {number} params.pageSize 每页记录数
 * @param {number} params.regionId 所属区域id
 * @param {array} params.regionIds 多选所属区域id
 * @param {number} params.agencyTypeId 机构类型id
 * @param {array} params.provinceCodes 多选所属省份code
 * @param {array} params.areaCodes 多选所属区域code
 * @param {array} params.cityCodes 多选所属城市code
 * @param {number} params.studyDirectionId 研究方向id
 * @param {number} params.id 机构id
 * @param {array} params.ids 机构ids
 * @param {string} params.keywords 关键字
 * @param {number} params.auditStatus
 * @param {number} params.tenantId
 * @param {number} params.hideStatus
 * @param {number} params.recommendShowStatus 推荐展示状态：0 不推荐 1 推荐
 * @param {string} params.name 机构名称
 * @returns
 */
export function exportAgency(data = {}) {
	return request({
		url: '/achv/export/exportAgency',
		method: 'post',
		data,
		responseType: 'blob',
	});
}

/**
 * 更新隐藏状态
 * @param {object} params
 * @param {number} params.id
 * @param {number} params.hideStatus
 * @returns
 */
export function updateHideStatusChange(data = {}) {
	return request({
		url: '/achv/agency/updateHideStatus',
		method: 'post',
		data,
		responseType: 'blob',
	});
}

/**
 * 更新推荐状态
 * @param {object} params
 * @param {number} params.id
 * @param {number} params.hideStatus
 * @returns
 */
export function updateRecommendStatusChange(data = {}) {
	return request({
		url: '/achv/agency/updateRecommend',
		method: 'post',
		data,
		responseType: 'blob',
	});
}
