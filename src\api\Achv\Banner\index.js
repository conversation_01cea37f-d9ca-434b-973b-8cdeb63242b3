import { request } from '@/utils/axios';

/**
 * 分页查询
 * @param {object} params 成果转化平台banner图配置
 * @param {number} params.pageNum 页码
 * @param {number} params.pageSize 每页记录数
 * @param {number} params.id
 * @param {string} params.title
 * @param {number} params.tenantId
 * @param {number} params.type
 * @param {number} params.loginStatus
 * @param {string} params.appid
 * @param {number} params.showStatus
 * @param {array} params.ids
 * @returns
 */
export function pageBanner(data = {}, option = {}) {
	return request({
		url: `/gbac-tip-achv-service/achv/bannerConfig/pageBanner`,
		method: 'POST',
		data,
		...option,
	});
}

/**
 * 获取配置详情
 * @param {object} params 成果转化平台banner图配置
 * @param {number} params.pageNum 页码
 * @param {number} params.pageSize 每页记录数
 * @param {number} params.id
 * @param {string} params.title
 * @param {number} params.tenantId
 * @param {number} params.type
 * @param {number} params.loginStatus
 * @param {string} params.appid
 * @param {number} params.showStatus
 * @param {array} params.ids
 * @returns
 */
export function getBanner(data = {}, option = {}) {
	return request({
		url: `/gbac-tip-achv-service/achv/bannerConfig/getBanner`,
		method: 'POST',
		data,
		...option,
	});
}

/**
 * 保存配置
 * @param {object} params 成果转化平台banner图配置
 * @param {number} params.id
 * @param {string} params.bannerUrl
 * @param {string} params.title
 * @param {string} params.introduce
 * @param {string} params.btnUrl
 * @param {number} params.type
 * @param {number} params.loginStatus
 * @param {string} params.path
 * @param {string} params.appid
 * @param {number} params.rankingNum
 * @param {number} params.showStatus
 * @returns
 */
export function addBanner(data = {}, option = {}) {
	return request({
		url: `/gbac-tip-achv-service/achv/bannerConfig/addBanner`,
		method: 'POST',
		data,
		...option,
	});
}
/**
 * 更新配置
 * @param {object} params 成果转化平台banner图配置
 * @param {number} params.id
 * @param {string} params.bannerUrl
 * @param {string} params.title
 * @param {string} params.introduce
 * @param {string} params.btnUrl
 * @param {number} params.type
 * @param {number} params.loginStatus
 * @param {string} params.path
 * @param {string} params.appid
 * @param {number} params.rankingNum
 * @param {number} params.showStatus
 * @returns
 */
export function updateBanner(data = {}, option = {}) {
	return request({
		url: `/gbac-tip-achv-service/achv/bannerConfig/updateBanner`,
		method: 'POST',
		data,
		...option,
	});
}

/**
 * 批量删除
 * @param {object} params 成果转化平台banner图配置
 * @param {number} params.pageNum 页码
 * @param {number} params.pageSize 每页记录数
 * @param {number} params.id
 * @param {string} params.title
 * @param {number} params.tenantId
 * @param {number} params.type
 * @param {number} params.loginStatus
 * @param {string} params.appid
 * @param {number} params.showStatus
 * @param {array} params.ids
 * @returns
 */
export function batchDel(data = {}, option = {}) {
	return request({
		url: `/gbac-tip-achv-service/achv/bannerConfig/batchDel`,
		method: 'POST',
		data,
		...option,
	});
}

/**
 * 更新排序
 * @param {object} params 更新排序dto
 * @param {number} params.id
 * @param {number} params.rankingNum
 * @param {array} params.saveDtoList
 * @returns
 */
export function updateRankingNum(data = {}, option = {}) {
	return request({
		url: `/gbac-tip-achv-service/achv/bannerConfig/updateRankingNum`,
		method: 'POST',
		data,
		...option,
	});
}

/**
 * 更新显示状态
 * @param {object} params 更新排序dto
 * @param {number} params.id
 * @param {number} params.showStatus
 * @returns
 */
export function updateShowStatus(data = {}, option = {}) {
	return request({
		url: `/gbac-tip-achv-service/achv/bannerConfig/updateShowStatus`,
		method: 'POST',
		data,
		...option,
	});
}
