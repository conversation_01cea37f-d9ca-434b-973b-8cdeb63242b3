import { request } from '@/utils/axios';

/**
 * 技术经纪人申请表分页
 * @param {object} params 技术经纪人申请查询
 * @param {number} params.pageNum 页码
 * @param {number} params.pageSize 每页记录数
 * @param {string} params.name 名称
 * @param {number} params.id id
 * @param {number} params.userId
 * @param {number} params.auditStatus 审核状态:1 审核中 2 审核不通过  3 审核通过
 * @param {array} params.ids 批量操作ids
 * @param {number} params.userType 用户类型:1-技术经理人 2-科研财务助理 3-科创品牌助理
 * @param {number} params.tenantId
 * @param {number} params.displayStatus
 * @param {number} params.platformDisplayStatus
 * @param {array} params.demandAreaIds 领域ids
 * @param {number} params.inviteBrokerId
 * @param {string} params.sortValue 排序值(传)：desc/asc
 * @param {string} params.sortKey 排序字段,传返回字段名称如：创建时间-createTime 排序号-rankingNum 审核时间-auditTime
 * @param {string} params.inviteCode 邀请码
 * @param {number} params.authLevel 认证级别：1注册级别 2认证级别
 * @returns
 */
export const pageBroker = (data) => {
	return request({
		url: '/achv/technicalBroker/page',
		method: 'post',
		data,
	});
};

/**
 * 新增技术经纪人申请表
 * @param {object} params 技术经纪人保存dto
 * @param {string} params.miniProgramAppId
 * @param {number} params.id
 * @param {string} params.userName 姓名
 * @param {number} params.gender 性别(1:男,2:女)
 * @param {string} params.companyName 入职单位
 * @param {string} params.positionName 职位
 * @param {string} params.provinceCode 省份code
 * @param {string} params.cityCode 城市code
 * @param {array} params.areaCategoryList 领域id
 * @param {array} params.companyTypeList 公司类别id
 * @param {string} params.phone 手机
 * @param {string} params.pwd 密码
 * @param {number} params.auditStatus 审核状态:1 审核中 2 审核不通过  3 审核通过
 * @param {number} params.sourceType 来源：0 后台创建 1 外部申请
 * @param {string} params.wxAvatarUrl 微信头像地址
 * @param {string} params.professionalCertificateUrl 职称证书url
 * @param {string} params.workingExperience 从业经验
 * @param {string} params.brokerCertificateUrl 技术经理人证书附件
 * @param {number} params.degreeId 学历id
 * @param {number} params.userType 用户类型:1-技术经理人 2-科研财务助理 3-科创品牌助理
 * @param {string} params.professionalCertificateName 职称名称
 * @param {string} params.introduction 个人简介
 * @param {string} params.mainHonorsProjects 主要项目
 * @param {number} params.recommendStatus
 * @param {string} params.inviteCode 邀请码
 * @param {string} params.contactPhone 联系电话
 * @param {number} params.brokerCertificateStatus 经理人证书拥有状态： 0 没有 1有
 * @param {number} params.brokerCertificateLevel 经理人证书等级：1 初级 2中级 3高级
 * @param {string} params.birthDay 出生年月
 * @param {string} params.partTimeDesc 社会兼职
 * @param {number} params.rankingNum
 * @param {array} params.workExperiencesList 经理人工作经验表dto
 * @param {number} params.isWorkStatus 是否在职状态：0 否 1是
 * @param {number} params.platformDisplayStatus 平台显示状态：0 隐藏 1 显示
 * @param {number} params.displayStatus 平台显示状态：0 隐藏 1 显示
 * @returns
 */
export const brokerAdd = (data) => {
	return request({
		url: '/achv/technicalBroker/add',
		method: 'post',
		data,
	});
};

/**
 * 修改技术经纪人申请表
 * @param {object} params 技术经纪人保存dto
 * @param {string} params.miniProgramAppId
 * @param {number} params.id
 * @param {string} params.userName 姓名
 * @param {number} params.gender 性别(1:男,2:女)
 * @param {string} params.companyName 入职单位
 * @param {string} params.positionName 职位
 * @param {string} params.provinceCode 省份code
 * @param {string} params.cityCode 城市code
 * @param {array} params.areaCategoryList 领域id
 * @param {array} params.companyTypeList 公司类别id
 * @param {string} params.phone 手机
 * @param {string} params.pwd 密码
 * @param {number} params.auditStatus 审核状态:1 审核中 2 审核不通过  3 审核通过
 * @param {number} params.sourceType 来源：0 后台创建 1 外部申请
 * @param {string} params.wxAvatarUrl 微信头像地址
 * @param {string} params.professionalCertificateUrl 职称证书url
 * @param {string} params.workingExperience 从业经验
 * @param {string} params.brokerCertificateUrl 技术经理人证书附件
 * @param {number} params.degreeId 学历id
 * @param {number} params.userType 用户类型:1-技术经理人 2-科研财务助理 3-科创品牌助理
 * @param {string} params.professionalCertificateName 职称名称
 * @param {string} params.introduction 个人简介
 * @param {string} params.mainHonorsProjects 主要项目
 * @param {number} params.recommendStatus
 * @param {string} params.inviteCode 邀请码
 * @param {string} params.contactPhone 联系电话
 * @param {number} params.brokerCertificateStatus 经理人证书拥有状态： 0 没有 1有
 * @param {number} params.brokerCertificateLevel 经理人证书等级：1 初级 2中级 3高级
 * @param {string} params.birthDay 出生年月
 * @param {string} params.partTimeDesc 社会兼职
 * @param {number} params.rankingNum
 * @param {array} params.workExperiencesList 经理人工作经验表dto
 * @param {number} params.isWorkStatus 是否在职状态：0 否 1是
 * @param {number} params.platformDisplayStatus 平台显示状态：0 隐藏 1 显示
 * @param {number} params.displayStatus 平台显示状态：0 隐藏 1 显示
 * @returns
 */
export const brokerUpdate = (data) => {
	return request({
		url: '/achv/technicalBroker/update',
		method: 'post',
		data,
	});
};

/**
 * 批量删除技术经纪人申请表
 * @param {object} params 技术经纪人申请查询
 * @param {number} params.pageNum 页码
 * @param {number} params.pageSize 每页记录数
 * @param {string} params.name 名称
 * @param {number} params.id id
 * @param {number} params.userId
 * @param {number} params.auditStatus 审核状态:1 审核中 2 审核不通过  3 审核通过
 * @param {array} params.ids 批量操作ids
 * @param {number} params.userType 用户类型:1-技术经理人 2-科研财务助理 3-科创品牌助理
 * @param {number} params.tenantId
 * @param {number} params.displayStatus
 * @param {number} params.platformDisplayStatus
 * @param {array} params.demandAreaIds 领域ids
 * @param {number} params.inviteBrokerId
 * @param {string} params.sortValue 排序值(传)：desc/asc
 * @param {string} params.sortKey 排序字段,传返回字段名称如：创建时间-createTime 排序号-rankingNum 审核时间-auditTime
 * @param {string} params.inviteCode 邀请码
 * @param {number} params.authLevel 认证级别：1注册级别 2认证级别
 * @returns
 */
export const brokerDel = (data) => {
	return request({
		url: '/achv/technicalBroker/batchDel',
		method: 'post',
		data,
	});
};

/**
 * 技术经纪人申请表详情
 * @param {object} params 技术经纪人申请查询
 * @param {number} params.id id
 * @returns
 */
export const getBrokerDetail = (data) => {
	return request({
		url: '/achv/technicalBroker/detail',
		method: 'post',
		data,
	});
};

/**
 * 导出成果
 * @param {object} params 成果查询dto
 * @param {number} params.pageNum 页码
 * @param {number} params.pageSize 每页记录数
 * @param {number} params.auditStatus 审核状态： 审核状态:1 审核中 2 审核不通过  3 审核通过（已发布）
 * @param {number} params.orgId 机构id
 * @param {number} params.id 成果id
 * @param {array} params.ids 成果ids（批量操作接口）
 * @param {string} params.keywords 关键字
 * @param {number} params.areaCategoryId 领域id
 * @param {number} params.releaseType 发布来源类型：1 后台发布 2小程序发布
 * @param {number} params.stageId 阶段id
 * @param {number} params.transformId 转化方式id
 * @param {number} params.isUpdate  是否修改 0 不是修改的 1 是修改的
 * @param {array} params.provinceCodes 多选所属省份code
 * @param {array} params.cityCodes 多选所属城市code
 * @param {array} params.areaCodes 多选所属区域code
 * @param {string} params.sortValue 排序值(传)：desc/asc
 * @param {string} params.sortKey 排序字段,传返回字段名称如：时间-updateTime
 * @param {number} params.tenantId
 * @param {array} params.excludeIds 成果ids（排除的结果id）
 * @returns
 */
export function exportList(data = {}) {
	return request({
		url: '/achv/export/exportBroker',
		method: 'post',
		data,
		responseType: 'blob',
	});
}

/**
 * 审核
 * @param {object} params 技术经纪人审核对象
 * @param {number} params.id id
 * @param {string} params.reason 不通过原因
 * @param {number} params.status 审核状态:1 审核中 2 审核不通过  3 审核通过
 * @returns
 */
export function auditBroker(data = {}) {
	return request({
		url: '/achv/technicalBroker/audit',
		method: 'post',
		data,
		responseType: 'blob',
	});
}

/**
 * 更新排序
 * @param {object} params
 * @param {number} params.id
 * @param {number} params.rankingNum
 * @param {array} params.saveDtoList
 * @returns
 */
export function updateRankingNum(data = {}) {
	return request({
		url: '/achv/technicalBroker/updateRankingNum',
		method: 'post',
		data,
		responseType: 'blob',
	});
}

/**
 * 成果统计
 * @param {object} params 查询dto
 * @param {number} params.pageNum 页码
 * @param {number} params.pageSize 每页记录数
 * @param {string} params.keywords 名称
 * @param {string} params.signUpName 参赛人员名称
 * @param {number} params.competitionGroup 大赛组别：1 全国技术经理人组 2 江夏技术经理人组 3武汉高校大学生组
 * @param {number} params.releaseType 发布类型：1 后台管理员发布 2 小程序发布  3 大赛发布
 * @param {array} params.releaseTypeList 发布类型：1 后台管理员发布 2 小程序发布  3 大赛发布
 * @param {array} params.provinceCodes 多选所属省份code
 * @param {array} params.cityCodes 多选所属城市code
 * @param {array} params.areaCodes 多选所属区域code
 * @param {number} params.auditStatus 审核状态： 审核状态:1 审核中 2 审核不通过  3 审核通过（已发布）
 * @returns
 */
export function queryBrokerStatistics(params = {}, option = {}) {
	return request({
		url: `/achv/technicalBroker/queryBrokerStatistics`,
		method: 'POST',
		data: params,
		...option,
	});
}

/**
 * 审核数据统计
 * @returns
 */
export function getAuditDataStatistics(params = {}, option = {}) {
	return request({
		url: `/achv/technicalBroker/auditDataStatistics`,
		method: 'POST',
		data: params,
		...option,
	});
}
