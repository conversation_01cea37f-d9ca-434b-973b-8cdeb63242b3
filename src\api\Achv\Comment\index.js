import { request } from '@/utils/axios';
/**
 * 评论分页查询
 * @param {object} params 科转号评论查询dto
 * @param {number} params.pageNum 页码
 * @param {number} params.pageSize 每页记录数
 * @param {number} params.businessId 关联业务id(图文详情id,视频详情id等)
 * @param {number} params.id 评论表主键id
 * @param {array} params.ids
 * @param {number} params.auditStatus 审核状态:  2 审核不通过  3 审核通过
 * @param {string} params.keyword 关键字
 * @returns
 */
export const pageComment = (data = {}, options = {}) => {
	return request({
		url: '/achv/ttComment/page',
		method: 'post',
		data,
		...options,
	});
};

/**
 * 获取评论详情
 * @param {number} params.id 评论表主键id
 * @returns
 */
export const getComment = (data = {}, options = {}) => {
	return request({
		url: '/achv/ttComment/detail',
		method: 'post',
		data,
		...options,
	});
};

/**
 * 获取统计数量
 * @returns
 */
export const getStatistics = (data = {}, options = {}) => {
	return request({
		url: '/achv/ttComment/getStatistics',
		method: 'post',
		data,
		...options,
	});
};

/**
 * 批量删除
 * @param {object} params 科转号评论查询dto
 * @param {number} params.pageNum 页码
 * @param {number} params.pageSize 每页记录数
 * @param {number} params.businessId 关联业务id(图文详情id,视频详情id等)
 * @param {number} params.id 评论表主键id
 * @param {array} params.ids
 * @param {number} params.auditStatus 审核状态:  2 审核不通过  3 审核通过
 * @param {string} params.keyword 关键字
 * @returns
 */
export const batchDelComment = (data = {}, options = {}) => {
	return request({
		url: '/achv/ttComment/batchDel',
		method: 'post',
		data,
		...options,
	});
};

/**
 * 审核评论
 * @param {object} params 审核状态dto
 * @param {number} params.id 当前审核数据主键id
 * @param {number} params.status 审核状态:  2 审核不通过  3 审核通过
 * @param {string} params.reason 审核原因
 * @returns
 */
export const auditComment = (data = {}, options = {}) => {
	return request({
		url: '/achv/ttComment/auditComment',
		method: 'post',
		data,
		...options,
	});
};
