import { request } from '@/utils/axios';

/**
 * 新增需求与成果
 * @param {object} params 大赛需求与成果保存dto
 * @param {array} params.sourceIds
 * @param {number} params.sourceType  1需求 2成果
 * @returns
 */
export function addAchievementDemand(params = {}, option = {}) {
	return request({
		url: `/achv/competition/addAchievementDemand`,
		method: 'POST',
		data: params,
		...option,
	});
}

/**
 * 删除需求与成果
 * @param {object} params 大赛需求与成果保存dto
 * @param {array} params.sourceIds
 * @param {number} params.sourceType
 * @returns
 */
export function deleteAchievementDemand(params = {}, option = {}) {
	return request({
		url: `/achv/competition/deleteAchievementDemand`,
		method: 'POST',
		data: params,
		...option,
	});
}

/**
 * 批量更新排序字段
 * @param {object} params 大赛需求与成果保存dto
 * @param {number} params.id
 * @param {number} params.sourceId
 * @param {number} params.recommendType
 * @param {number} params.rankingNum
 * @returns
 */
export function updateRankingNum(params = {}, option = {}) {
	return request({
		url: `/achv/competition/updateRankingNum`,
		method: 'POST',
		data: params,
		...option,
	});
}

/**
 * 大赛需求分页
 * @param {object} params 查询dto
 * @param {number} params.pageNum 页码
 * @param {number} params.pageSize 每页记录数
 * @param {string} params.name 名称
 * @param {string} params.signUpName 参赛人员名称
 * @param {number} params.competitionGroup 大赛组别：1 全国技术经理人组 2 江夏技术经理人组 3武汉高校大学生组
 * @param {array} params.releaseTypeList 发布类型：1 后台管理员发布 2 小程序发布  3 大赛发布
 * @param {array} params.provinceCodes 多选所属省份code
 * @param {array} params.cityCodes 多选所属城市code
 * @param {array} params.areaCodes 多选所属区域code
 * @returns
 */
export function queryDemandPage(params = {}, option = {}) {
	return request({
		url: `/achv/competition/pageDemand`,
		method: 'POST',
		data: params,
		...option,
	});
}

/**
 * 导出需求表
 * @param {object} params 查询dto
 * @param {number} params.pageNum 页码
 * @param {number} params.pageSize 每页记录数
 * @param {string} params.name 名称
 * @param {string} params.signUpName 参赛人员名称
 * @param {number} params.competitionGroup 大赛组别：1 全国技术经理人组 2 江夏技术经理人组 3武汉高校大学生组
 * @param {array} params.releaseTypeList 发布类型：1 后台管理员发布 2 小程序发布  3 大赛发布
 * @param {array} params.provinceCodes 多选所属省份code
 * @param {array} params.cityCodes 多选所属城市code
 * @param {array} params.areaCodes 多选所属区域code
 * @returns
 */
export function exportDemand(params = {}, option = {}) {
	return request({
		url: `/achv/competition/exportDemand`,
		method: 'POST',
		data: params,
		responseType: 'blob',
		...option,
	});
}

/**
 * 需求统计
 * @param {object} params 查询dto
 * @param {number} params.pageNum 页码
 * @param {number} params.pageSize 每页记录数
 * @param {string} params.keywords 名称
 * @param {string} params.signUpName 参赛人员名称
 * @param {number} params.competitionGroup 大赛组别：1 全国技术经理人组 2 江夏技术经理人组 3武汉高校大学生组
 * @param {number} params.releaseType 发布类型：1 后台管理员发布 2 小程序发布  3 大赛发布
 * @param {array} params.releaseTypeList 发布类型：1 后台管理员发布 2 小程序发布  3 大赛发布
 * @param {array} params.provinceCodes 多选所属省份code
 * @param {array} params.cityCodes 多选所属城市code
 * @param {array} params.areaCodes 多选所属区域code
 * @param {number} params.auditStatus 审核状态： 审核状态:1 审核中 2 审核不通过  3 审核通过（已发布）
 * @returns
 */
export function queryDemandStatistics(params = {}, option = {}) {
	return request({
		url: `/achv/competition/queryDemandStatistics`,
		method: 'POST',
		data: params,
		...option,
	});
}
