import { request } from '@/utils/axios';

/**
 * 审核数量统计
 * @returns
 */
export function dataStatistics(params = {}, option = {}) {
	return request({
		url: `/achv/signUp/dataStatistics`,
		method: 'POST',
		data: params,
		...option,
	});
}

/**
 * 报名申请表分页
 * @param {object} params 大赛报名申请查询
 * @param {number} params.pageNum 页码
 * @param {number} params.pageSize 每页记录数
 * @param {number} params.id id
 * @param {string} params.name 名称
 * @param {number} params.auditStatus 审核状态:1 审核中 2 审核不通过  3 审核通过
 * @param {array} params.ids 批量操作ids
 * @param {number} params.tenantId
 * @param {number} params.competitionGroup 大赛组别：1 全国技术经理人组 2 江夏技术经理人组 3武汉高校大学生组
 * @param {string} params.sortValue 排序值(传)：desc/asc
 * @param {string} params.sortKey 排序字段,传返回字段名称如：创建时间-createTime 排序号-rankingNum 审核时间-auditTime
 * @param {string} params.provinceCode 省份code
 * @param {string} params.cityCode 城市code
 * @param {string} params.areaCode 地区code
 * @returns
 */
export function pageSignUp(params = {}, option = {}) {
	return request({
		url: `/achv/signUp/page`,
		method: 'POST',
		data: params,
		...option,
	});
}

/**
 * 报名申请表列表
 * @param {object} params 大赛报名申请查询
 * @param {number} params.id id
 * @param {string} params.name 名称
 * @param {number} params.auditStatus 审核状态:1 审核中 2 审核不通过  3 审核通过
 * @param {array} params.ids 批量操作ids
 * @param {number} params.tenantId
 * @param {number} params.competitionGroup 大赛组别：1 全国技术经理人组 2 江夏技术经理人组 3武汉高校大学生组
 * @param {string} params.sortValue 排序值(传)：desc/asc
 * @param {string} params.sortKey 排序字段,传返回字段名称如：创建时间-createTime 排序号-rankingNum 审核时间-auditTime
 * @param {string} params.provinceCode 省份code
 * @param {string} params.cityCode 城市code
 * @param {string} params.areaCode 地区code
 * @returns
 */
export function listSignUp(params = {}, option = {}) {
	return request({
		url: `/achv/signUp/list`,
		method: 'POST',
		data: params,
		...option,
	});
}

/**
 * 报名申请表详情
 * @param {object} params 大赛报名申请查询
 * @param {number} params.pageNum 页码
 * @param {number} params.pageSize 每页记录数
 * @param {number} params.id id
 * @param {string} params.name 名称
 * @param {number} params.auditStatus 审核状态:1 审核中 2 审核不通过  3 审核通过
 * @param {array} params.ids 批量操作ids
 * @param {number} params.tenantId
 * @param {number} params.competitionGroup 大赛组别：1 全国技术经理人组 2 江夏技术经理人组 3武汉高校大学生组
 * @param {string} params.sortValue 排序值(传)：desc/asc
 * @param {string} params.sortKey 排序字段,传返回字段名称如：创建时间-createTime 排序号-rankingNum 审核时间-auditTime
 * @param {string} params.provinceCode 省份code
 * @param {string} params.cityCode 城市code
 * @param {string} params.areaCode 地区code
 * @returns
 */
export function getSignUp(params = {}, option = {}) {
	return request({
		url: `/achv/signUp/detail`,
		method: 'POST',
		data: params,
		...option,
	});
}

/**
 * 修改报名
 * @param {object} params 大赛报名保存dto
 * @param {number} params.id
 * @param {string} params.name 用户姓名
 * @param {string} params.provinceCode 省份code
 * @param {string} params.cityCode 城市code
 * @param {string} params.areaCode 地区code
 * @param {string} params.workingExperience 从业经验
 * @param {string} params.professionalCertificateName 职称名称
 * @param {string} params.mainHonorsProjects 重点业绩
 * @param {number} params.degreeId 学历id
 * @param {number} params.rankingNum 排名序号
 * @param {string} params.birthDay 出生年月
 * @param {string} params.contactPhone 联系电话
 * @param {string} params.companyName 所在企业名称
 * @param {string} params.positionName 所在企业岗位
 * @param {string} params.competitionName 大赛名称
 * @param {number} params.gender 性别(1:男,2:女)
 * @param {string} params.wxAvatarUrl 微信头像地址
 * @param {string} params.politicalStatus 政治面貌
 * @param {string} params.contactEmail 联系邮箱
 * @param {string} params.projectsNumber 促成项目数量
 * @param {string} params.contractAmount 促成合同金额
 * @param {string} params.schoolName 学校名称
 * @param {string} params.majorName 专业名称
 * @param {string} params.graduationYear 毕业年份
 * @param {string} params.currentGrade 现任年级
 * @param {number} params.auditStatus 审核状态( 1:审核中,2:审核不通过,3:审核通过)
 * @param {number} params.competitionGroup 大赛组别：1 全国技术经理人组 2 江夏技术经理人组 3武汉高校大学生组
 * @param {array} params.workExperiencesList 大赛报名工作/教育经历表dto
 * @param {array} params.educationExperiencesList 大赛报名工作/教育经历表dto
 * @returns
 */
export function updateSignUp(params = {}, option = {}) {
	return request({
		url: `/achv/signUp/update`,
		method: 'POST',
		data: params,
		...option,
	});
}

/**
 * 批量删除报名申请
 * @param {object} params 大赛报名申请查询
 * @param {number} params.pageNum 页码
 * @param {number} params.pageSize 每页记录数
 * @param {number} params.id id
 * @param {string} params.name 名称
 * @param {number} params.auditStatus 审核状态:1 审核中 2 审核不通过  3 审核通过
 * @param {array} params.ids 批量操作ids
 * @param {number} params.tenantId
 * @param {number} params.competitionGroup 大赛组别：1 全国技术经理人组 2 江夏技术经理人组 3武汉高校大学生组
 * @param {string} params.sortValue 排序值(传)：desc/asc
 * @param {string} params.sortKey 排序字段,传返回字段名称如：创建时间-createTime 排序号-rankingNum 审核时间-auditTime
 * @param {string} params.provinceCode 省份code
 * @param {string} params.cityCode 城市code
 * @param {string} params.areaCode 地区code
 * @returns
 */
export function delSignUp(params = {}, option = {}) {
	return request({
		url: `/achv/signUp/batchDel`,
		method: 'POST',
		data: params,
		...option,
	});
}

/**
 * 审核
 * @param {object} params 大赛报名审核对象
 * @param {number} params.id id
 * @param {string} params.reason 不通过原因
 * @param {number} params.status 审核状态:1 审核中 2 审核不通过  3 审核通过
 * @returns
 */
export function auditSignUp(params = {}, option = {}) {
	return request({
		url: `/achv/signUp/audit`,
		method: 'POST',
		data: params,
		...option,
	});
}

/**
 * 导出报名表
 * @param {object} params 大赛报名申请查询
 * @param {number} params.pageNum 页码
 * @param {number} params.pageSize 每页记录数
 * @param {number} params.id id
 * @param {string} params.name 名称
 * @param {number} params.auditStatus 审核状态:1 审核中 2 审核不通过  3 审核通过
 * @param {array} params.ids 批量操作ids
 * @param {number} params.tenantId
 * @param {number} params.competitionGroup 大赛组别：1 全国技术经理人组 2 江夏技术经理人组 3武汉高校大学生组
 * @param {string} params.sortValue 排序值(传)：desc/asc
 * @param {string} params.sortKey 排序字段,传返回字段名称如：创建时间-createTime 排序号-rankingNum 审核时间-auditTime
 * @param {string} params.provinceCode 省份code
 * @param {string} params.cityCode 城市code
 * @param {string} params.areaCode 地区code
 * @returns
 */
export function exportSignUp(params = {}, option = {}) {
	return request({
		url: `/achv/signUp/export`,
		method: 'POST',
		data: params,
		responseType: 'blob',
		...option,
	});
}

/**
 * 报名申请表详情
 * @param {object} params 大赛报名申请查询
 * @param {number} params.pageNum 页码
 * @param {number} params.pageSize 每页记录数
 * @param {number} params.id id
 * @param {string} params.name 名称
 * @param {number} params.auditStatus 审核状态:1 审核中 2 审核不通过  3 审核通过
 * @param {array} params.ids 批量操作ids
 * @param {number} params.tenantId
 * @param {number} params.competitionGroup 大赛组别：1 全国技术经理人组 2 江夏技术经理人组 3武汉高校大学生组
 * @param {string} params.sortValue 排序值(传)：desc/asc
 * @param {string} params.sortKey 排序字段,传返回字段名称如：创建时间-createTime 排序号-rankingNum 审核时间-auditTime
 * @param {string} params.provinceCode 省份code
 * @param {string} params.cityCode 城市code
 * @param {string} params.areaCode 地区code
 * @param {number} params.userId
 * @returns
 */
export function detailByUserId(params = {}, option = {}) {
	return request({
		url: `/achv/signUp/detailByUserId`,
		method: 'POST',
		data: params,
		...option,
	});
}
