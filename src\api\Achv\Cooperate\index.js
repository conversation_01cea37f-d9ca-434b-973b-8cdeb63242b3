import { request } from '@/utils/axios';

/**
 * 分页查询合作意向-需求
 * @param {object} params 合作查询dto
 * @param {number} params.pageNum 页码
 * @param {number} params.pageSize 每页记录数
 * @param {number} params.id 合作需求id
 * @param {number} params.cooperateType 合作类型：1 成果合作需求  2 需求合作意向  3机构合作详情 4 专家合作详情
 * @param {number} params.cooperateProjectId 合作项目id:对应成果，需求，机构id
 * @param {array} params.ids ids
 * @param {number} params.createBy
 * @returns
 */
export function pageCooperate(params = {}, option = {}) {
	return request({
		url: `/achv/cooperate/page`,
		method: 'POST',
		data: params,
		...option,
	});
}

/**
 * 获取合作意向-需求
 * @param {object} params 合作查询dto
 * @param {number} params.pageNum 页码
 * @param {number} params.pageSize 每页记录数
 * @param {number} params.id 合作需求id
 * @param {number} params.cooperateType 合作类型：1 成果合作需求  2 需求合作意向  3机构合作详情 4 专家合作详情
 * @param {number} params.cooperateProjectId 合作项目id:对应成果，需求，机构id
 * @param {array} params.ids ids
 * @param {number} params.createBy
 * @returns
 */
export function detailCooperate(params = {}, option = {}) {
	return request({
		url: `/achv/cooperate/getDetail`,
		method: 'POST',
		data: params,
		...option,
	});
}

/**
 * 删除合作内容
 * @param {object} params 合作查询dto
 * @param {number} params.pageNum 页码
 * @param {number} params.pageSize 每页记录数
 * @param {number} params.id 合作需求id
 * @param {number} params.cooperateType 合作类型：1 成果合作需求  2 需求合作意向  3机构合作详情 4 专家合作详情
 * @param {number} params.cooperateProjectId 合作项目id:对应成果，需求，机构id
 * @param {array} params.ids ids
 * @param {number} params.createBy
 * @returns
 */
export function delCooperate(params = {}, option = {}) {
	return request({
		url: `/achv/cooperate/del`,
		method: 'POST',
		data: params,
		...option,
	});
}
