import { request } from "@/utils/axios";

/**
 * 分页查询推荐内容
 * @param {object} params DemandAchievementRecommendQueryDto
 * @param {number} params.pageNum 页码
 * @param {number} params.pageSize 每页记录数
 * @param {string} params.recommendData 主推日期：yyyy-MM-dd 格式
 * @param {number} params.recommendType 推荐数据类型：1 需求 2 成果
 * @param {array} params.ids 主键id，批量操作
 * @param {number} params.publishStatus
 * @param {number} params.tenantId
 * @returns
 */
export function pageRecommend(params = {}, option = {}) {
    return request({
        url: `/achv/recommend/page`,
        method: "POST",
        data: params,
        ...option,
    });
}

/**
 * 新增推荐内容
 * @param {object} params DemandAchievementRecommendSaveDto
 * @param {number} params.id
 * @param {number} params.sourceId
 * @param {number} params.recommendType
 * @param {number} params.rankingNum
 * @returns
 */
export function addRecommend(params = {}, option = {}) {
    return request({
        url: `/achv/recommend/add`,
        method: "POST",
        data: params,
        ...option,
    });
}

/**
 * 批量根据id删除数据
 * @param {object} params
 * @param {array} params.ids 主键id，批量操作
 * @returns
 */
export function removeRecommend(params = {}, option = {}) {
    return request({
        url: `/achv/recommend/batchDel`,
        method: "POST",
        data: params,
        ...option,
    });
}

/**
 * 获取最后发布时间
 * @returns
 */
export function getLastPublishTime(params = {}, option = {}) {
    return request({
        url: `/achv/recommend/getLastPublishTime`,
        method: "POST",
        data: params,
        ...option,
    });
}
/**
 * 发布推荐
 * @returns
 */
export function publish(params = {}, option = {}) {
    return request({
        url: `/achv/recommend/publish`,
        method: "POST",
        data: params,
        ...option,
    });
}

/**
 * 更新排序字段
 * @param {object} params DemandAchievementRecommendSaveDto
 * @param {number} params.id
 * @param {number} params.sourceId
 * @param {number} params.recommendType
 * @param {number} params.rankingNum
 * @returns
 */
export function updateSort(params = {}, option = {}) {
    return request({
        url: `/achv/recommend/updateSort`,
        method: "POST",
        data: params,
        ...option,
    });
}

/**
 * 批量更新排序字段
 * @param {object} params DemandAchievementRecommendSaveDto
 * @param {number} params.id
 * @param {number} params.sourceId
 * @param {number} params.recommendType
 * @param {number} params.rankingNum
 * @returns
 */
export function batchUpdateSort(params = {}, option = {}) {
    return request({
        url: `/achv/recommend/batchUpdateSort`,
        method: "POST",
        data: params,
        ...option,
    });
}
