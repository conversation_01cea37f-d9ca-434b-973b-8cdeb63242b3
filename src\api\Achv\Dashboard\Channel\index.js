import { request } from '@/utils/axios';

/**
 * 分页查询渠道
 * @param {object} params DataPointChannelQueryDto
 * @param {number} params.pageNum 页码
 * @param {number} params.pageSize 每页记录数
 * @param {string} params.channelName 渠道名称
 * @param {array} params.ids
 * @param {number} params.id
 * @returns
 */
export function pageChannel(params = {}, options = {}) {
	return request({
		url: '/achv/dataPoint/pageChannel',
		method: 'post',
		data: params,
		...options,
	});
}

/**
 * 新增渠道
 * @param {object} params DataPointChannelDto
 * @param {string} params.channelName 渠道名称
 * @param {number} params.id
 * @returns
 */
export function addChannel(params = {}, options = {}) {
	return request({
		url: '/achv/dataPoint/addChannel',
		method: 'post',
		data: params,
		...options,
	});
}

/**
 * 修改渠道
 * @param {object} params DataPointChannelDto
 * @param {string} params.channelName 渠道名称
 * @param {number} params.id
 * @returns
 */
export function updateChannel(params = {}, options = {}) {
	return request({
		url: '/achv/dataPoint/updateChannel',
		method: 'post',
		data: params,
		...options,
	});
}

/**
 * 批量删除渠道
 * @param {object} params DataPointChannelQueryDto
 * @param {number} params.pageNum 页码
 * @param {number} params.pageSize 每页记录数
 * @param {string} params.channelName 渠道名称
 * @param {array} params.ids
 * @param {number} params.id
 * @returns
 */
export function batchDelChannel(params = {}, options = {}) {
	return request({
		url: '/achv/dataPoint/batchDelChannel',
		method: 'post',
		data: params,
		...options,
	});
}

/**
 * 查询渠道详情
 * @param {object} params DataPointChannelQueryDto
 * @param {number} params.pageNum 页码
 * @param {number} params.pageSize 每页记录数
 * @param {string} params.channelName 渠道名称
 * @param {array} params.ids
 * @param {number} params.id
 * @returns
 */
export function getChannel(params = {}, options = {}) {
	return request({
		url: '/achv/dataPoint/getChannel',
		method: 'post',
		data: params,
		...options,
	});
}
