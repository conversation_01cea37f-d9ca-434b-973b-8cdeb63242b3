import { request } from '@/utils/axios';
/**
 * 认领需求分页查询
 * @param {object} params 需求查询dto
 * @param {number} params.pageNum 页码
 * @param {number} params.pageSize 每页记录数
 * @param {number} params.auditStatus 审核状态： 审核状态:1 审核中 2 审核不通过  3 审核通过（已发布）
 * @param {number} params.id 需求id
 * @param {string} params.keywords 关键字
 * @param {number} params.areaCategoryId 领域id
 * @param {array} params.ids   需求ids（批量操作时用）
 * @param {number} params.releaseType 发布类型：1 后台管理员发布 2 小程序发布  3 大赛发布
 * @param {array} params.releaseTypeList 发布类型：1 后台管理员发布 2 小程序发布  3 大赛发布
 * @param {number} params.isDesensitize 是否 加密公司名：0 否 1是
 * @param {number} params.isUpdate  是否修改 0 不是修改的 1 是修改的
 * @param {number} params.demandUpdateAuditStatus
 * @param {array} params.provinceCodes 多选所属省份code
 * @param {array} params.cityCodes 多选所属城市code
 * @param {array} params.areaCodes 多选所属区域code
 * @param {number} params.isOpen
 * @param {number} params.tenantId
 * @param {string} params.sortValue 排序值(传)：desc/asc
 * @param {string} params.sortKey 排序字段,传返回字段名称如：时间-updateTime 预算-budget 创建时间-createTime
 * @param {number} params.demandTypeId 需求类别id
 * @param {number} params.transformId 转化方式id
 * @param {number} params.isAuth 是否认证：0 否 1是
 * @param {number} params.contentDisplayStatus 内容显示状态：0不显示  1显示
 * @param {string} params.authStartTime 认证开始时间：yyyy-MM-dd hh:mm:ss
 * @param {string} params.authEndTime 认证结束时间：yyyy-MM-dd hh:mm:ss
 * @param {string} params.claimStartTime 认领开始时间：yyyy-MM-dd hh:mm:ss
 * @param {string} params.claimEndTime 认领结束时间：yyyy-MM-dd hh:mm:ss
 * @returns
 */
export function pageToClaim(params) {
	return request({
		url: '/achv/demand/pageToClaim',
		method: 'post',
		data: params,
	});
}
