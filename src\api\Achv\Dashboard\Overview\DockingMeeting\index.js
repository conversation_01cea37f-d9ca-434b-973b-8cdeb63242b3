import { request } from '@/utils/axios';

/**
 * 三方会议分页查询
 * @param {object} params 派单供应商表
 * @param {number} params.pageNum 页码
 * @param {number} params.pageSize 每页记录数
 * @param {number} params.id 供应商线索主键id
 * @param {number} params.sendOrderId 派单id
 * @param {number} params.demandId 需求id
 * @param {number} params.stageStatus 阶段状态：1.待三方会议 2 已三方会议 3 已签约 4 已终止谈判(不采纳) 5第二次三方会议 6 第三次三方会议
 * @param {number} params.brokerId
 * @param {number} params.createBy
 * @param {number} params.sourceId 来源id(需求或成果id)
 * @param {number} params.createSource 创建来源：1 后台创建 2小程序创建  3 大赛创建
 * @param {number} params.sourceType 来源(1:需求,2:成果)
 * @param {string} params.startTime 创建开始时间：yyyy-MM-dd
 * @param {string} params.endTime 创建结束时间：yyyy-MM-dd
 * @param {string} params.meetingStartTime 会议开始时间：yyyy-MM-dd
 * @param {string} params.meetingEndTime 会议结束时间：yyyy-MM-dd
 * @returns
 */
export function querySupplierMeetingVoPage(params) {
	const data = { ...params };
	if (data.hasMeeting === undefined) {
		data.hasMeeting = 1;
	}
	return request({
		url: '/achv/sendOrderSupplier/querySupplierMeetingVoPage',
		method: 'post',
		data,
	});
}
