/**
 * @description workbench - 工作台
 * <AUTHOR>
 *
 * Created on 2024/9/26 17:43
 */
import { request } from '@/utils/axios';

/**
 * 数据查询
 * @param {object} params 数据总览查询dto
 * @param {number} params.dataType 数据请求查询字段： 数据类型-1今日实时数据  2数据概况 3对接数据
 * @param {string} params.queryDate 数据请求查询字段： 查询日期，格式：yyyy-MM-dd
 * @param {number} params.demandType 需求分布查询字段： 需求类型-1：全部需求 2：认证需求
 * @param {number} params.demandDistributionStatus 需求分布查询字段： 分布条件-1：按揭榜状态 2：按行业领域 3：按揭榜方式
 * @param {number} params.brokerRankingCondition 技术经理人活跃度查询字段： 排名条件-1:按认领需求 2:按挖掘需求 3:按负责需求 4:按提供线索
 * @returns
 */
export function queryAchievementData(params) {
	return request({
		url: '/achv/data/allData',
		method: 'post',
		data: params,
	});
}

/**
 * 技术经理人活跃度top10查询
 * @param {object} params 数据总览查询dto
 * @param {number} params.dataType 数据请求查询字段： 数据类型-1今日实时数据  2数据概况 3对接数据
 * @param {string} params.queryDate 数据请求查询字段： 查询日期，格式：yyyy-MM-dd
 * @param {number} params.demandType 需求分布查询字段： 需求类型-1：全部需求 2：认证需求
 * @param {number} params.demandDistributionStatus 需求分布查询字段： 分布条件-1：按揭榜状态 2：按行业领域 3：按揭榜方式
 * @param {number} params.brokerRankingCondition 技术经理人活跃度查询字段： 排名条件-1:按认领需求 2:按挖掘需求 3:按负责需求 4:按提供线索
 * @returns
 */
export function brokerRanking(params) {
	return request({
		url: '/achv/data/brokerRankingTop10',
		method: 'post',
		data: params,
	});
}

/**
 * 需求分布查询
 * @param {object} params 数据总览查询dto
 * @param {number} params.dataType 数据请求查询字段： 数据类型-1今日实时数据  2数据概况 3对接数据
 * @param {string} params.queryDate 数据请求查询字段： 查询日期，格式：yyyy-MM-dd
 * @param {number} params.demandType 需求分布查询字段： 需求类型-1：全部需求 2：认证需求
 * @param {number} params.demandDistributionStatus 需求分布查询字段： 分布条件-1：按揭榜状态 2：按行业领域 3：按揭榜方式
 * @param {number} params.brokerRankingCondition 技术经理人活跃度查询字段： 排名条件-1:按认领需求 2:按挖掘需求 3:按负责需求 4:按提供线索
 * @returns
 */
export function demandDistribution(params) {
	return request({
		url: '/achv/data/demandDistribution',
		method: 'post',
		data: params,
	});
}
