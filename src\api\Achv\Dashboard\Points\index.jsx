import { request } from '@/utils/axios';

/**
 * 全部积分查询
 * @param {object} params 当月积分查询dto
 * @param {number} params.pageNum 页码
 * @param {number} params.pageSize 每页记录数
 * @param {string} params.month 时间：yyyy-MM
 * @param {number} params.userId
 * @param {string} params.pointsTypeCode
 * @param {string} params.showTypeCode
 * @param {array} params.showTypeCodeList
 * @param {number} params.validStatus
 * @param {string} params.businessId
 * @param {array} params.userIds
 * @returns
 */
export function allPoints(data = {}) {
	return request({
		url: '/achv/points/allPoints',
		method: 'post',
		data,
	});
}

/**
 * 积分导出
 * @param {object} params 当月积分查询dto
 * @param {number} params.pageNum 页码
 * @param {number} params.pageSize 每页记录数
 * @param {string} params.month 时间：yyyy-MM
 * @param {number} params.userId
 * @param {string} params.pointsTypeCode
 * @param {string} params.showTypeCode
 * @param {array} params.showTypeCodeList
 * @param {number} params.validStatus
 * @param {string} params.businessId
 * @param {array} params.userIds
 * @returns
 */
export function exportPoints(data = {}) {
	return request({
		url: '/achv/points/exportPoints',
		method: 'post',
		data,
		responseType: 'blob',
	});
}
