import { request } from '@/utils/axios';

/**
 * 技术经理人数据统计
 * @param {object} params 技术经纪人数据统计
 * @param {number} params.pageNum 页码
 * @param {number} params.pageSize 每页记录数
 * @param {number} params.tenantId
 * @param {number} params.scoreType 排序类型：  1 需求挖掘 2 需求认证  3 需求负责 4 需求负责 5 需求分享数 6 成果分享数 7资讯分享数 8实时简报分享 9分享总数 10活动报名分享数 11大赛报名分享数 12拼团分享数13企业需求预判 14 直播 海报
 * @param {string} params.sortStr 排序,传 desc/asc
 * @param {string} params.brokerName 技术经纪人名称
 * @param {array} params.companyNames 公司名称
 * @param {number} params.isWorkStatus 是否在职：0 否 1是
 * @param {string} params.startTime 开始时间：yyyy-MM-dd HH:mm:ss
 * @param {string} params.endTime 结束时间：yyyy-MM-dd HH:mm:ss
 * @returns
 */
export function pageTechnicalBrokerActive(params = {}, options = {}) {
	return request({
		url: '/achv/technicalBroker/dataStatistics',
		method: 'post',
		data: params,
		...options,
	});
}

/**
 * 导出技术人统计
 * @param {object} params 技术经纪人数据统计
 * @param {number} params.pageNum 页码
 * @param {number} params.pageSize 每页记录数
 * @param {number} params.tenantId
 * @param {number} params.scoreType 排序类型：  1 需求挖掘 2 需求认证  3 需求负责 4 需求负责 5 需求分享数 6 成果分享数 7资讯分享数 8实时简报分享 9分享总数 10活动报名分享数 11大赛报名分享数 12拼团分享数13企业需求预判 14 直播 海报
 * @param {string} params.sortStr 排序,传 desc/asc
 * @param {string} params.brokerName 技术经纪人名称
 * @param {array} params.companyNames 公司名称
 * @param {number} params.isWorkStatus 是否在职：0 否 1是
 * @param {string} params.startTime 开始时间：yyyy-MM-dd HH:mm:ss
 * @param {string} params.endTime 结束时间：yyyy-MM-dd HH:mm:ss
 * @returns
 */
export function exportBrokerData(data = {}) {
	return request({
		url: '/achv/technicalBroker/exportBrokerData',
		method: 'post',
		data,
		responseType: 'blob',
	});
}
