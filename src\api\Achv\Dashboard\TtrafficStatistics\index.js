/**
 * @description workbench - 工作台
 * <AUTHOR>
 *
 * Created on 2024/9/26 17:43
 */
import { request } from '@/utils/axios';

/**
 * 访问流量统计
 * @param {object} params DataPointQueryDto
 * @param {number} params.pageNum 页码
 * @param {number} params.pageSize 每页记录数
 * @param {string} params.startTime 开始时间：yyyy-MM-dd
 * @param {string} params.endTime 结束时间：yyyy-MM-dd
 * @param {string} params.specifiedDate 指定日期：yyyy-MM-dd
 * @param {string} params.businessCode
 * @returns
 */
export function dataPointStatistics(params) {
	return request({
		url: '/achv/data/dataPointStatistics',
		method: 'post',
		data: params,
	});
}

/**
 * 访问流量统计明细
 * @param {object} params DataPointQueryDto
 * @param {number} params.pageNum 页码
 * @param {number} params.pageSize 每页记录数
 * @param {string} params.startTime 开始时间：yyyy-MM-dd
 * @param {string} params.endTime 结束时间：yyyy-MM-dd
 * @param {string} params.specifiedDate 指定日期：yyyy-MM-dd
 * @param {string} params.businessCode
 * @returns
 */
export function dataPointPage(params) {
	return request({
		url: '/achv/data/dataPointPage',
		method: 'post',
		data: params,
	});
}
