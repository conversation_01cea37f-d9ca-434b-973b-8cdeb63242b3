import { request } from '@/utils/axios';

/**
 * 分页查询需求
 * @param {object} params 需求查询dto
 * @param {number} params.pageNum 页码
 * @param {number} params.pageSize 每页记录数
 * @param {number} params.auditStatus 审核状态： 审核状态:1 审核中 2 审核不通过  3 审核通过（已发布）
 * @param {number} params.id 需求id
 * @param {string} params.keywords 关键字
 * @param {number} params.areaCategoryId 领域id
 * @param {array} params.ids   需求ids（批量操作时用）
 * @param {number} params.releaseType 发布类型：1 后台管理员发布 2 成果小程序/PC版(企业)发布  3 技术经理人小程序/PC版发布
 * @param {number} params.isDesensitize 是否 加密公司名：0 否 1是
 * @param {number} params.isUpdate  是否修改 0 不是修改的 1 是修改的
 * @param {number} params.demandUpdateAuditStatus
 * @param {array} params.provinceCodes 多选所属省份code
 * @param {array} params.cityCodes 多选所属城市code
 * @param {array} params.areaCodes 多选所属区域code
 * @param {number} params.isOpen
 * @param {number} params.tenantId
 * @param {string} params.sortValue 排序值(传)：desc/asc
 * @param {string} params.sortKey 排序字段,传返回字段名称如：时间-updateTime 预算-budget
 * @param {number} params.demandTypeId 需求类别id
 * @param {number} params.transformId 转化方式id
 * @returns
 */
export function pageDemand(params = {}, option = {}) {
	return request({
		url: `/achv/demand/pageDemand`,
		method: 'POST',
		data: params,
		...option,
	});
}

/**
 * 新增需求
 * @param {object} params  需求保存实体
 * @param {string} params.miniProgramAppId
 * @param {number} params.weChatSchemeId 获取微信scheme地址id
 * @param {number} params.id 主键id,修改时传
 * @param {string} params.name 需求名称
 * @param {number} params.regionId 所属区域id
 * @param {number} params.orgId 所属机构id
 * @param {string} params.companyName 需求方
 * @param {string} params.budgetDesc 项目预算描述(选择面议直接填：面议)
 * @param {string} params.requirement 需求描述
 * @param {string} params.contacts 联系人
 * @param {string} params.contactsPhone 联系电话
 * @param {array} params.areaCategoryList 需求领域id
 * @param {number} params.demandTypeId 需求类别id
 * @param {array} params.transformList 转化方式id
 * @param {number} params.auditStatus 审核状态:  2 审核不通过  3 审核通过
 * @param {number} params.rankingNum 排序序号
 * @param {number} params.releaseType 发布类型：1 后台管理员发布 2 成果小程序/PC版(企业)发布  3 技术经理人小程序/PC版发布
 * @param {number} params.isOpen 是否公开需求：0不公开 1公开
 * @param {number} params.isAuth 是否认证需求：0 否 1是
 * @param {string} params.contactsPosition 联系人职位
 * @param {string} params.reason 审核不通过原因
 * @param {string} params.provinceCode 所属省份code
 * @param {string} params.cityCode 所属城市code
 * @param {string} params.areaCode 所属区域code
 * @param {array} params.authFileList 合作相关文件保存dto
 * @param {number} params.publicType 公开类型(0:不公开，1:半公开,2:公开)
 * @param {number} params.proclamationStatus 揭榜状态：1 可揭榜 2 揭榜中 3 已揭榜
 * @param {array} params.excavatePersonnelList 需求跟进技术经理人
 * @param {array} params.responsiblePersonnelList 需求跟进技术经理人
 * @param {array} params.authPersonnelList 需求跟进技术经理人
 * @param {array} params.brokerList 需求跟进技术经理人
 * @returns
 */
export function addDemand(params = {}, option = {}) {
	return request({
		url: `/achv/demand/add`,
		method: 'POST',
		data: params,
		...option,
	});
}

/**
 * 修改需求
 * @param {object} params  需求保存实体
 * @param {string} params.miniProgramAppId
 * @param {number} params.weChatSchemeId 获取微信scheme地址id
 * @param {number} params.id 主键id,修改时传
 * @param {string} params.name 需求名称
 * @param {number} params.regionId 所属区域id
 * @param {number} params.orgId 所属机构id
 * @param {string} params.companyName 需求方
 * @param {string} params.budgetDesc 项目预算描述(选择面议直接填：面议)
 * @param {string} params.requirement 需求描述
 * @param {string} params.contacts 联系人
 * @param {string} params.contactsPhone 联系电话
 * @param {array} params.areaCategoryList 需求领域id
 * @param {number} params.demandTypeId 需求类别id
 * @param {array} params.transformList 转化方式id
 * @param {number} params.auditStatus 审核状态:  2 审核不通过  3 审核通过
 * @param {number} params.rankingNum 排序序号
 * @param {number} params.releaseType 发布类型：1 后台管理员发布 2 成果小程序/PC版(企业)发布  3 技术经理人小程序/PC版发布
 * @param {number} params.isOpen 是否公开需求：0不公开 1公开
 * @param {number} params.isAuth 是否认证需求：0 否 1是
 * @param {string} params.contactsPosition 联系人职位
 * @param {string} params.reason 审核不通过原因
 * @param {string} params.provinceCode 所属省份code
 * @param {string} params.cityCode 所属城市code
 * @param {string} params.areaCode 所属区域code
 * @param {array} params.authFileList 合作相关文件保存dto
 * @param {number} params.publicType 公开类型(0:不公开，1:半公开,2:公开)
 * @param {number} params.proclamationStatus 揭榜状态：1 可揭榜 2 揭榜中 3 已揭榜
 * @param {array} params.excavatePersonnelList 需求跟进技术经理人
 * @param {array} params.responsiblePersonnelList 需求跟进技术经理人
 * @param {array} params.authPersonnelList 需求跟进技术经理人
 * @param {array} params.brokerList 需求跟进技术经理人
 * @returns
 */
export function updateDemand(params = {}, option = {}) {
	return request({
		url: `/achv/demand/update`,
		method: 'POST',
		data: params,
		...option,
	});
}

/**
 * 批量根据id删除数据
 * @param {object} params
 * @param {array} params.ids  需求ids（批量操作时用）
 * @returns
 */
export function delDemand(params = {}, option = {}) {
	return request({
		url: `/achv/demand/batchDel`,
		method: 'POST',
		data: params,
		...option,
	});
}

/**
 * 根据id查询详情数据
 * @param {object} params
 * @param {number} params.id 需求id
 * @returns
 */
export function getDemand(params = {}, option = {}) {
	return request({
		url: `/achv/demand/getDemand`,
		method: 'POST',
		data: params,
		...option,
	});
}

/**
 * 导出
 * @param {object} params 需求查询dto
 * @param {number} params.pageNum 页码
 * @param {number} params.pageSize 每页记录数
 * @param {number} params.auditStatus 审核状态： 审核状态:1 审核中 2 审核不通过  3 审核通过（已发布）
 * @param {number} params.id 需求id
 * @param {string} params.keywords 关键字
 * @param {number} params.areaCategoryId 领域id
 * @param {array} params.ids   需求ids（批量操作时用）
 * @param {number} params.releaseType 发布类型：1 后台管理员发布 2 成果小程序/PC版(企业)发布  3 技术经理人小程序/PC版发布
 * @param {number} params.isDesensitize 是否 加密公司名：0 否 1是
 * @param {number} params.isUpdate  是否修改 0 不是修改的 1 是修改的
 * @param {number} params.demandUpdateAuditStatus
 * @param {array} params.provinceCodes 多选所属省份code
 * @param {array} params.cityCodes 多选所属城市code
 * @param {array} params.areaCodes 多选所属区域code
 * @param {number} params.isOpen
 * @param {number} params.tenantId
 * @param {string} params.sortValue 排序值(传)：desc/asc
 * @param {string} params.sortKey 排序字段,传返回字段名称如：时间-updateTime 预算-budget
 * @param {number} params.demandTypeId 需求类别id
 * @param {number} params.transformId 转化方式id
 * @returns
 */
export function exportDemand(params = {}, option = {}) {
	return request({
		url: `/achv/demand/export`,
		method: 'POST',
		data: params,
		responseType: 'blob',
		...option,
	});
}

/**
 * 需求审核
 * @param {object} params 审核状态dto
 * @param {number} params.id 当前审核数据主键id
 * @param {number} params.status 审核状态:  2 审核不通过  3 审核通过
 * @param {string} params.reason 审核原因
 * @returns
 */
export function auditDemand(params = {}, option = {}) {
	return request({
		url: `/achv/demand/audit`,
		method: 'POST',
		data: params,
		...option,
	});
}

/**
 * 更新排序
 * @param {object} params 需求与成果 排序更新dto
 * @param {number} params.id
 * @param {number} params.rankingNum
 * @param {array} params.saveDtoList
 * @returns
 */
export function updateRankingNum(params = {}, option = {}) {
	return request({
		url: `/achv/demand/updateRankingNum`,
		method: 'POST',
		data: params,
		...option,
	});
}

/**
 * 需求统计
 * @param {object} params 查询dto
 * @param {number} params.pageNum 页码
 * @param {number} params.pageSize 每页记录数
 * @param {string} params.keywords 名称
 * @param {string} params.signUpName 参赛人员名称
 * @param {number} params.competitionGroup 大赛组别：1 全国技术经理人组 2 江夏技术经理人组 3武汉高校大学生组
 * @param {number} params.releaseType 发布类型：1 后台管理员发布 2 小程序发布  3 大赛发布
 * @param {array} params.releaseTypeList 发布类型：1 后台管理员发布 2 小程序发布  3 大赛发布
 * @param {array} params.provinceCodes 多选所属省份code
 * @param {array} params.cityCodes 多选所属城市code
 * @param {array} params.areaCodes 多选所属区域code
 * @param {number} params.auditStatus 审核状态： 审核状态:1 审核中 2 审核不通过  3 审核通过（已发布）
 * @returns
 */
export function queryDemandStatistics(params = {}, option = {}) {
	return request({
		url: `/achv/demand/queryDemandStatistics`,
		method: 'POST',
		data: params,
		...option,
	});
}

/**
 * 更新显示内容状态(传ids)
 * @param {object} params 需求查询dto
 * @param {array} params.ids   需求ids（批量操作时用）
 * @param {number} params.contentDisplayStatus 内容显示状态：0不显示  1显示
 * @returns
 */
export function batchUpdateContentDisplayStatus(data = {}, option = {}) {
	return request({
		url: `/achv/demand/batchUpdateContentDisplayStatus`,
		method: 'POST',
		data,
		...option,
	});
}
