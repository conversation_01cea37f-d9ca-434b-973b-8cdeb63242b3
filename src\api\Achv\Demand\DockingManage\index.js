import { request } from '@/utils/axios';

/**
 * 根据需求id查询供方列表
 * @param {object} params 派单供应商表
 * @param {number} params.demandId 需求id
 * @returns
 */
export function getSupplierListByDemandId(params = {}, option = {}) {
	return request({
		url: `/achv/sendOrderSupplier/getOrderSupplierByDemandId`,
		method: 'POST',
		data: params,
		...option,
	});
}

/**
 * 根据需求id查询供方列表
 * @param {object} params 派单供应商表
 * @param {number} params.sourceId 订单或者成果id
 * @param {number} params.sourceType 1 需求 2 成果
 * @returns
 */
export function getOrderSupplierBySourceId(params = {}, option = {}) {
	return request({
		url: `/achv/sendOrderSupplier/getOrderSupplierBySourceId`,
		method: 'POST',
		data: params,
		...option,
	});
}

/**
 * 添加供应方
 * @param {object} params 派单表
 * @param {number} params.id 主键
 * @param {number} params.sourceId 订单或者成果id
 * @param {number} params.sourceType 1 需求 2 成果
 * @param {number} params.brokerId 认领经理人id
 * @param {string} params.applyReason 申请理由
 * @param {number} params.claimStatus 认领状态(1:待认领,2:拒绝认领,3:已认领 4 待审核 5 审核不通过 6 等待联系 )
 * @param {string} params.refuseReason 拒绝理由
 * @param {number} params.supplierType 类型:1专家团队 2科研机构 3企业
 * @param {string} params.supplierName 供给方名称
 * @param {string} params.supplierIntroduction 供给方简介
 * @param {string} params.supplierContacts 供给方联系人
 * @param {string} params.supplierContactsPhone 供给方电话
 * @returns
 */
export function addSupplier(params = {}, option = {}) {
	return request({
		url: `/achv/sendOrder/addSupplier`,
		method: 'POST',
		data: params,
		...option,
	});
}

/**
 * 修改供给方
 * @param {object} params 派单供应商表
 * @param {number} params.id 主键id
 * @param {number} params.sendOrderSupplierId 派单id
 * @param {number} params.demandId 需求id
 * @param {number} params.sourceType 1 需求 2 成果
 * @param {number} params.stageStatus 阶段状态：1.待三方会议 2 已三方会议 3 已签约 4 已终止谈判
 * @param {number} params.brokerId 认领经理人id
 * @param {number} params.supplierType 类型:1专家团队 2科研机构 3企业
 * @param {string} params.supplierName 供给方名称
 * @param {string} params.supplierIntroduction 供给方简介
 * @param {string} params.supplierContacts 供给方联系人
 * @param {string} params.supplierContactsPhone 供给方电话
 * @param {string} params.reasonReason 终止原因
 * @param {object} params.meetingTime 三方会议时间,格式：yyyy-MM-dd HH:mm:ss
 * @param {string} params.meetingContent 三方会议内容
 * @returns
 */
export function updateSupplier(params = {}, option = {}) {
	return request({
		url: `/achv/sendOrderSupplier/updateOrderSupplier`,
		method: 'POST',
		data: params,
		...option,
	});
}

/**
 * 删除供给方(传id)
 * @param {object} params
 * @param {number} params.id 主键id
 * @returns
 */
export function delSupplier(params = {}, option = {}) {
	return request({
		url: `/achv/sendOrderSupplier/delOrderSupplier`,
		method: 'POST',
		data: params,
		...option,
	});
}

/**
 * 查询供给方详情(传id)
 * @param {object} params
 * @param {number} params.id 需求id
 * @returns
 */
export function getSupplier(params = {}, option = {}) {
	return request({
		url: `/achv/sendOrderSupplier/getOrderSupplierById`,
		method: 'POST',
		data: params,
		...option,
	});
}

/**
 * 更新供方状态
 * @param {object} params 派单供应商表
 * @param {number} params.pageNum 页码
 * @param {number} params.pageSize 每页记录数
 * @param {number} params.id 供应商线索主键id
 * @param {number} params.sendOrderId
 * @param {number} params.stageStatus 阶段状态：  2 已三方会议 3 已签约 4 已终止谈判(不采纳)
 * @param {string} params.reasonReason 终止原因
 * @param {object} params.meetingTime 三方会议时间,格式：yyyy-MM-dd HH:mm:ss
 * @param {string} params.meetingContent 三方会议内容
 * @returns
 */
export function updateStageStatus(params = {}, option = {}) {
	return request({
		url: `/achv/sendOrderSupplier/updateSupplierStageStatus`,
		method: 'POST',
		data: params,
		...option,
	});
}
