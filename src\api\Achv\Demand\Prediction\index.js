import { request } from '@/utils/axios';

/**
 * 企业分页查询
 * @param {object} params 企业查询
 * @param {number} params.pageNum 页码
 * @param {number} params.pageSize 每页记录数
 * @param {number} params.id id
 * @param {string} params.name 企业名称
 * @param {string} params.aptitude 资质
 * @param {number} params.confirmStatus 确认状态(1:待确认,2:确认,3:驳回)
 * @param {string} params.provinceCode 省编码
 * @param {string} params.cityCode 城市编码
 * @param {string} params.areaCode 区域编码
 * @returns
 */
export function pageEnterprise(params = {}, option = {}) {
	return request({
		url: `/achv/scienceForum/enterprise/page`,
		method: 'POST',
		data: params,
		...option,
	});
}

/**
 * 企业详情
 * @param {object} params 企业查询
 * @param {number} params.id id
 * @returns
 */
export function detailEnterprise(params = {}, option = {}) {
	return request({
		url: `/achv/scienceForum/enterprise/detail`,
		method: 'POST',
		data: params,
		...option,
	});
}

/**
 * 企业更新
 * @param {object} params 更新参数
 * @param {number} params.id id
 * @param {string} params.name 企业名称
 * @param {string} params.introduction 简介
 * @param {string} params.mainBusiness 主营业务
 * @param {string} params.coreProduct 核心产品
 * @param {string} params.industryChainName 产业链
 * @param {string} params.industryChainNode 产业链节点
 * @param {string} params.technologicalRoute 技术路线
 * @param {string} params.technologicalRequirement 技术需求
 * @param {string} params.keyTechnologicalRequirement 关键技术需求
 * @param {string} params.requirementKeyword 需求关键词
 * @param {string} params.provinceCode 省份编码
 * @param {string} params.cityCode 城市编码
 * @param {string} params.areaCode 区域编码
 * @param {number} params.confirmStatus 确认状态 1：待确认 2：已确认 3：驳回
 * @param {string} params.phone 手机号
 * @param {number} params.demandSource 需求来源 导入： 2-后台创建  新增：1-平台提交
 * @param {string} params.aptitude 企业资质
 * @param {number} params.demandType 需求类型 1：技术需求(默认)
 * @param {string} params.reason 驳回原因
 * @returns
 */
export function addEnterprise(params = {}, option = {}) {
	return request({
		url: `/achv/scienceForum/enterprise/add`,
		method: 'POST',
		data: params,
		...option,
	});
}

/**
 * 企业更新
 * @param {object} params 更新参数
 * @param {number} params.id id
 * @param {string} params.name 企业名称
 * @param {string} params.introduction 简介
 * @param {string} params.mainBusiness 主营业务
 * @param {string} params.coreProduct 核心产品
 * @param {string} params.industryChainName 产业链
 * @param {string} params.industryChainNode 产业链节点
 * @param {string} params.technologicalRoute 技术路线
 * @param {string} params.technologicalRequirement 技术需求
 * @param {string} params.keyTechnologicalRequirement 关键技术需求
 * @param {string} params.requirementKeyword 需求关键词
 * @param {string} params.provinceCode 省份编码
 * @param {string} params.cityCode 城市编码
 * @param {string} params.areaCode 区域编码
 * @param {number} params.confirmStatus 确认状态 1：待确认 2：已确认 3：驳回
 * @param {string} params.phone 手机号
 * @param {number} params.demandSource 需求来源 导入： 2-后台创建  新增：1-平台提交
 * @param {string} params.aptitude 企业资质
 * @param {number} params.demandType 需求类型 1：技术需求(默认)
 * @param {string} params.reason 驳回原因
 * @returns
 */
export function updateEnterprise(params = {}, option = {}) {
	return request({
		url: `/achv/scienceForum/enterprise/update`,
		method: 'POST',
		data: params,
		...option,
	});
}

/**
 * 企业删除
 * @param {object} params 企业查询
 * @param {number} params.id id
 * @param {number} params.ids ids
 * @returns
 */
export function deleteEnterprise(params = {}, option = {}) {
	return request({
		url: `/achv/scienceForum/enterprise/deleteEnterprise`,
		method: 'POST',
		data: params,
		...option,
	});
}

/**
 * 导出
 * @param {object} params 企业查询
 * @param {string} params.name 企业名称
 * @param {string} params.aptitude 资质
 * @param {number} params.confirmStatus 确认状态(1:待确认,2:确认,3:驳回)
 * @param {string} params.provinceCode 省编码
 * @param {string} params.cityCode 城市编码
 * @param {string} params.areaCode 区域编码
 * @returns
 */
export function exportEnterprise(params = {}, option = {}) {
	return request({
		url: `/achv/scienceForum/enterprise/export`,
		method: 'POST',
		data: params,
		responseType: 'blob',
		...option,
	});
}

/**
 * 导入
 * @param {object} params 企业查询
 * @returns
 */
export function importEnterprise(params = {}, option = {}) {
	return request({
		url: `/achv/scienceForum/enterprise/import`,
		method: 'POST',
		data: params,
		header: {
			'Content-Type': 'multipart/form-data',
		},
		...option,
	});
}

/**
 * 企业分页查询
 * @param {object} params 企业查询
 * @param {number} params.id id
 * @param {string} params.name 企业名称
 * @param {string} params.aptitude 资质
 * @param {number} params.confirmStatus 确认状态(1:待确认,2:确认,3:驳回)
 * @param {string} params.provinceCode 省编码
 * @param {string} params.cityCode 城市编码
 * @param {string} params.areaCode 区域编码
 * @returns
 */
export function statusStatistic(params = {}, option = {}) {
	return request({
		url: `/achv/scienceForum/enterprise/statusStatistic`,
		method: 'POST',
		data: params,
		...option,
	});
}
