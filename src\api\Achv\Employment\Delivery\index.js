import { request } from '@/utils/axios';

/**
 * 岗位投递分页记录
 * @param {object} params 科转就业岗位投递管理dto
 * @param {number} params.pageNum 页码
 * @param {number} params.pageSize 每页记录数
 * @param {number} params.id
 * @param {number} params.tenantId
 * @param {array} params.ids
 * @param {number} params.postId 岗位id
 * @param {number} params.submissionStatus 投递状态:1 待助力 2成功投递
 * @param {number} params.contactStatus 联系状态:1 待联系  2已联系
 * @returns
 */
export function workPostSubmissionPage(params = {}, option = {}) {
	return request({
		url: `/achv/ttWorkPost/workPostSubmissionPage`,
		method: 'POST',
		data: params,
		...option,
	});
}

/**
 * 岗位投递状态统计
 * @param {object} params 科转就业岗位投递管理dto
 * @param {number} params.pageNum 页码
 * @param {number} params.pageSize 每页记录数
 * @param {number} params.id
 * @param {number} params.tenantId
 * @param {array} params.ids
 * @param {number} params.postId 岗位id
 * @param {number} params.submissionStatus 投递状态:1 待助力 2成功投递
 * @param {number} params.contactStatus 联系状态:1 待联系  2已联系
 * @returns
 */
export function workPostSubmissionStatusStatistics(params = {}, option = {}) {
	return request({
		url: `/achv/ttWorkPost/workPostSubmissionStatusStatistics`,
		method: 'POST',
		data: params,
		...option,
	});
}

/**
 * 编辑 岗位投递
 * @param {object} params 科转就业岗位投递管理dto
 * @param {number} params.id
 * @param {number} params.postId 岗位id
 * @param {number} params.submissionStatus 投递状态:1 待助力 2成功投递
 * @param {number} params.contactStatus 联系状态:1 待联系  2已联系
 * @returns
 */
export function workPostSubmissionEdit(params = {}, option = {}) {
	return request({
		url: `/achv/ttWorkPost/workPostSubmissionEdit`,
		method: 'POST',
		data: params,
		...option,
	});
}

/**
 * 岗位投递详情
 * @param {object} params 科转就业岗位投递管理dto
 * @param {number} params.pageNum 页码
 * @param {number} params.pageSize 每页记录数
 * @param {number} params.id
 * @param {number} params.tenantId
 * @param {array} params.ids
 * @param {number} params.postId 岗位id
 * @param {number} params.submissionStatus 投递状态:1 待助力 2成功投递
 * @param {number} params.contactStatus 联系状态:1 待联系  2已联系
 * @returns
 */
export function workPostSubmissionDetail(params = {}, option = {}) {
	return request({
		url: `/achv/ttWorkPost/workPostSubmissionDetail`,
		method: 'POST',
		data: params,
		...option,
	});
}

/**
 * 批量删除 岗位投递
 * @param {object} params 科转就业岗位投递管理dto
 * @param {number} params.pageNum 页码
 * @param {number} params.pageSize 每页记录数
 * @param {number} params.id
 * @param {number} params.tenantId
 * @param {array} params.ids
 * @param {number} params.postId 岗位id
 * @param {number} params.submissionStatus 投递状态:1 待助力 2成功投递
 * @param {number} params.contactStatus 联系状态:1 待联系  2已联系
 * @returns
 */
export function workPostSubmissionBatchDel(params = {}, option = {}) {
	return request({
		url: `/achv/ttWorkPost/workPostSubmissionBatchDel`,
		method: 'POST',
		data: params,
		...option,
	});
}
