import { request } from '@/utils/axios';

/**
 * 科转就业岗位分页
 * @param {object} params 科转就业岗位保存dto
 * @param {number} params.pageNum 页码
 * @param {number} params.pageSize 每页记录数
 * @param {number} params.tenantId
 * @param {number} params.id
 * @param {array} params.ids
 * @param {string} params.postName 岗位名称
 * @param {number} params.releasePlatform 发布平台:1 后台管理 2小程序
 * @param {number} params.releaseType 发布类型:1 管理员发布 2科转号发布
 * @param {number} params.recommendStatus 推荐状态: 0否 1是
 * @param {number} params.showStatus 展示状态: 0不展示 1展示
 * @param {array} params.degrees  学历：1博士 2硕士 3本科 4专科 5不限
 * @param {array} params.provinceCodes 省份code
 * @param {array} params.cityCodes 城市code
 * @param {number} params.recommendNumberPeople 推荐人数要求
 * @param {string} params.postContactName 联系人姓名
 * @param {number} params.minimumYears 经验：最低年限
 * @param {number} params.maximumYears  经验：最高年限
 * @param {number} params.highestMonthlySalary 薪酬：最高月薪
 * @param {number} params.minimumMonthlySalary 薪酬：最低月薪
 * @param {number} params.annualSalary 年薪
 * @param {number} params.auditStatus 审核状态:1 审核中 2 审核不通过  3 审核通过
 * @returns
 */
export function pagePost(params = {}, option = {}) {
	return request({
		url: `/achv/ttWorkPost/page`,
		method: 'POST',
		data: params,
		...option,
	});
}

/**
 * 新增科转就业岗位
 * @param {object} params 科转就业岗位保存dto
 * @param {number} params.id
 * @param {string} params.postName 岗位名称
 * @param {number} params.releasePlatform 发布平台:1 后台管理 2小程序
 * @param {number} params.releaseType 发布类型:1 管理员发布 2科转号发布
 * @param {number} params.recommendStatus 推荐状态: 0否 1是
 * @param {number} params.showStatus 展示状态: 0不展示 1展示
 * @param {string} params.reason 审核原因
 * @param {number} params.degree 学历：1博士 2硕士 3本科 4专科 5不限
 * @param {string} params.provinceCode 省份code
 * @param {string} params.cityCode 城市code
 * @param {number} params.recommendNumberPeople 推荐人数要求
 * @param {string} params.postDuty 岗位职责
 * @param {string} params.postRequire 岗位要求
 * @param {string} params.sourceUnitAvatarUrl 来源单位头像
 * @param {string} params.sourceUnitName 来源单位名称
 * @param {string} params.postContactPhone 联系人电话
 * @param {string} params.postContactName 联系人姓名
 * @param {number} params.minimumYears 经验：最低年限
 * @param {number} params.maximumYears 经验：最高年限
 * @param {number} params.highestMonthlySalary 薪酬：最高月薪
 * @param {number} params.minimumMonthlySalary 薪酬：最低月薪
 * @param {number} params.annualSalary 年薪
 * @param {number} params.rankingNum 排序序号
 * @param {array} params.ttChannelsIds 关联科转号
 * @param {array} params.delTTChannelsIds 删除关联科转号
 * @returns
 */
export function addPost(params = {}, option = {}) {
	return request({
		url: `/achv/ttWorkPost/add`,
		method: 'POST',
		data: params,
		...option,
	});
}

/**
 * 修改科转就业岗位
 * @param {object} params 科转就业岗位保存dto
 * @param {number} params.id
 * @param {string} params.postName 岗位名称
 * @param {number} params.releasePlatform 发布平台:1 后台管理 2小程序
 * @param {number} params.releaseType 发布类型:1 管理员发布 2科转号发布
 * @param {number} params.recommendStatus 推荐状态: 0否 1是
 * @param {number} params.showStatus 展示状态: 0不展示 1展示
 * @param {string} params.reason 审核原因
 * @param {number} params.degree 学历：1博士 2硕士 3本科 4专科 5不限
 * @param {string} params.provinceCode 省份code
 * @param {string} params.cityCode 城市code
 * @param {number} params.recommendNumberPeople 推荐人数要求
 * @param {string} params.postDuty 岗位职责
 * @param {string} params.postRequire 岗位要求
 * @param {string} params.sourceUnitAvatarUrl 来源单位头像
 * @param {string} params.sourceUnitName 来源单位名称
 * @param {string} params.postContactPhone 联系人电话
 * @param {string} params.postContactName 联系人姓名
 * @param {number} params.minimumYears 经验：最低年限
 * @param {number} params.maximumYears 经验：最高年限
 * @param {number} params.highestMonthlySalary 薪酬：最高月薪
 * @param {number} params.minimumMonthlySalary 薪酬：最低月薪
 * @param {number} params.annualSalary 年薪
 * @param {number} params.rankingNum 排序序号
 * @param {array} params.ttChannelsIds 关联科转号
 * @param {array} params.delTTChannelsIds 删除关联科转号
 * @returns
 */
export function updatePost(params = {}, option = {}) {
	return request({
		url: `/achv/ttWorkPost/update`,
		method: 'POST',
		data: params,
		...option,
	});
}

/**
 * 科转就业岗位详情
 * @param {object} params 科转就业岗位保存dto
 * @param {number} params.pageNum 页码
 * @param {number} params.pageSize 每页记录数
 * @param {number} params.tenantId
 * @param {number} params.id
 * @param {array} params.ids
 * @param {string} params.postName 岗位名称
 * @param {number} params.releasePlatform 发布平台:1 后台管理 2小程序
 * @param {number} params.releaseType 发布类型:1 管理员发布 2科转号发布
 * @param {number} params.recommendStatus 推荐状态: 0否 1是
 * @param {number} params.showStatus 展示状态: 0不展示 1展示
 * @param {array} params.degrees  学历：1博士 2硕士 3本科 4专科 5不限
 * @param {array} params.provinceCodes 省份code
 * @param {array} params.cityCodes 城市code
 * @param {number} params.recommendNumberPeople 推荐人数要求
 * @param {string} params.postContactName 联系人姓名
 * @param {number} params.minimumYears 经验：最低年限
 * @param {number} params.maximumYears  经验：最高年限
 * @param {number} params.highestMonthlySalary 薪酬：最高月薪
 * @param {number} params.minimumMonthlySalary 薪酬：最低月薪
 * @param {number} params.annualSalary 年薪
 * @param {number} params.auditStatus 审核状态:1 审核中 2 审核不通过  3 审核通过
 * @returns
 */
export function detailPost(params = {}, option = {}) {
	return request({
		url: `/achv/ttWorkPost/detail`,
		method: 'POST',
		data: params,
		...option,
	});
}

/**
 * 批量删除科转就业岗位
 * @param {object} params 科转就业岗位保存dto
 * @param {number} params.pageNum 页码
 * @param {number} params.pageSize 每页记录数
 * @param {number} params.tenantId
 * @param {number} params.id
 * @param {array} params.ids
 * @param {string} params.postName 岗位名称
 * @param {number} params.releasePlatform 发布平台:1 后台管理 2小程序
 * @param {number} params.releaseType 发布类型:1 管理员发布 2科转号发布
 * @param {number} params.recommendStatus 推荐状态: 0否 1是
 * @param {number} params.showStatus 展示状态: 0不展示 1展示
 * @param {array} params.degrees  学历：1博士 2硕士 3本科 4专科 5不限
 * @param {array} params.provinceCodes 省份code
 * @param {array} params.cityCodes 城市code
 * @param {number} params.recommendNumberPeople 推荐人数要求
 * @param {string} params.postContactName 联系人姓名
 * @param {number} params.minimumYears 经验：最低年限
 * @param {number} params.maximumYears  经验：最高年限
 * @param {number} params.highestMonthlySalary 薪酬：最高月薪
 * @param {number} params.minimumMonthlySalary 薪酬：最低月薪
 * @param {number} params.annualSalary 年薪
 * @param {number} params.auditStatus 审核状态:1 审核中 2 审核不通过  3 审核通过
 * @returns
 */
export function batchDelPost(params = {}, option = {}) {
	return request({
		url: `/achv/ttWorkPost/batchDel`,
		method: 'POST',
		data: params,
		...option,
	});
}

/**
 * 审核
 * @param {object} params 专家审核对象
 * @param {number} params.id id
 * @param {string} params.reason 不通过原因
 * @param {number} params.status 审核状态:1 审核中 2 审核不通过  3 审核通过
 * @returns
 */

export function auditPost(params = {}, option = {}) {
	return request({
		url: `/achv/ttWorkPost/audit`,
		method: 'POST',
		data: params,
		...option,
	});
}

/**
 * 更新排序
 * @param {object} params 更新排序dto
 * @param {number} params.id
 * @param {number} params.rankingNum
 * @param {array} params.saveDtoList
 * @returns
 */

export function updateRankingNum(params = {}, option = {}) {
	return request({
		url: `/achv/ttWorkPost/updateRankingNum`,
		method: 'POST',
		data: params,
		...option,
	});
}

/**
 * 更新推荐
 * @param {object} params 推荐状态对象
 * @param {number} params.id id
 * @param {number} params.recommendStatus 推荐状态： 0否 1是
 * @returns
 */
export function updateRecommendStatus(params = {}, option = {}) {
	return request({
		url: `/achv/ttWorkPost/updateRecommendStatus`,
		method: 'POST',
		data: params,
		...option,
	});
}

/**
 * 更新显示
 * @param {object} params 显示状态对象
 * @param {number} params.id id
 * @param {number} params.showStatus 展示状态： 0不展示 1展示
 * @returns
 */
export function updateShowStatus(params = {}, option = {}) {
	return request({
		url: `/achv/ttWorkPost/updateShowStatus`,
		method: 'POST',
		data: params,
		...option,
	});
}

/**
 * 统计审核数
 * @returns
 */
export function getAuditDataStatistics(params = {}, option = {}) {
	return request({
		url: `/achv/ttWorkPost/getAuditDataStatistics`,
		method: 'POST',
		data: params,
		...option,
	});
}
