/**
 * @description index - 文件描述
 * <AUTHOR>
 *
 * Created on 2024/10/28 19:04
 */
import { request } from '@/utils/axios';

/**
 * 分页查询拼团活动
 * @param {object} params 拼轩活动查询dto
 * @param {number} params.pageNum 页码
 * @param {number} params.pageSize 每页记录数
 * @param {number} params.id
 * @param {number} params.tenantId
 * @param {array} params.ids
 * @param {string} params.title 活动标题
 * @param {number} params.auditStatus 审核状态:1 审核中 2 审核不通过  3 审核通过
 * @param {number} params.eventStatus 活动状态:1 未开始 2 进行中 3已结束
 * @param {number} params.eventValidStatus 活动是否有效:0 无效 1 有效
 * @param {object} params.startTime 活动开始时间,格式：yyyy-MM-dd HH:mm:ss
 * @param {object} params.endTime 活动结束时间,格式：yyyy-MM-dd HH:mm:ss
 * @returns
 */
export function pageGroupEvent(params, option) {
	return request({
		url: `/achv/groupEvent/pageGroupEvent`,
		method: 'POST',
		data: params,
		...option,
	});
}

/**
 * 拼团活动详情
 * @param {object} params 拼轩活动查询dto
 * @param {number} params.pageNum 页码
 * @param {number} params.pageSize 每页记录数
 * @param {number} params.id
 * @param {number} params.tenantId
 * @param {array} params.ids
 * @param {string} params.title 活动标题
 * @param {number} params.auditStatus 审核状态:1 审核中 2 审核不通过  3 审核通过
 * @param {number} params.eventStatus 活动状态:1 未开始 2 进行中 3已结束
 * @param {number} params.eventValidStatus 活动是否有效:0 无效 1 有效
 * @param {object} params.startTime 活动开始时间,格式：yyyy-MM-dd HH:mm:ss
 * @param {object} params.endTime 活动结束时间,格式：yyyy-MM-dd HH:mm:ss
 * @returns
 */
export function groupEventDetail(params) {
	return request({
		url: `/achv/groupEvent/detail`,
		method: 'POST',
		data: params,
	});
}

/**
 * 新增拼团活动
 * @param {object} params 拼轩活动dto
 * @param {number} params.id
 * @param {string} params.title 活动标题
 * @param {string} params.provinceCode 所属省份code
 * @param {string} params.cityCode 所属城市code
 * @param {string} params.areaCode 所属区域code
 * @param {string} params.address 活动地址
 * @param {string} params.coverImageUrl 封面图片地址
 * @param {number} params.auditStatus 审核状态:1 审核中 2 审核不通过  3 审核通过
 * @param {number} params.eventStatus 活动状态:1 未开始 2 进行中 3已结束
 * @param {number} params.eventValidStatus 活动是否有效:0 无效 1 有效
 * @param {string} params.groupLeaderName 团长组长名称
 * @param {string} params.groupLeaderLogo 团长组长logo
 * @param {array} params.joinMembers 参加人员类型与数量
 * @param {string} params.introduce 活动介绍
 * @param {object} params.startTime 活动开始时间,格式：yyyy-MM-dd HH:mm:ss
 * @param {object} params.endTime 活动结束时间,格式：yyyy-MM-dd HH:mm:ss
 * @param {number} params.rankingNum 排序序号
 * @param {array} params.adminStaffs 活动管理员信息dto
 * @returns
 */
export function addGroupEvent(params) {
	return request({
		url: `/achv/groupEvent/addGroupEvent`,
		method: 'POST',
		data: params,
	});
}

/**
 * 删除拼团活动
 * @param {object} params 拼轩活动查询dto
 * @param {number} params.pageNum 页码
 * @param {number} params.pageSize 每页记录数
 * @param {number} params.id
 * @param {number} params.tenantId
 * @param {array} params.ids
 * @param {string} params.title 活动标题
 * @param {number} params.auditStatus 审核状态:1 审核中 2 审核不通过  3 审核通过
 * @param {number} params.eventStatus 活动状态:1 未开始 2 进行中 3已结束
 * @param {number} params.eventValidStatus 活动是否有效:0 无效 1 有效
 * @param {object} params.startTime 活动开始时间,格式：yyyy-MM-dd HH:mm:ss
 * @param {object} params.endTime 活动结束时间,格式：yyyy-MM-dd HH:mm:ss
 * @returns
 */
export function deleteGroupEvent(params) {
	return request({
		url: `/achv/groupEvent/deleteGroupEvent`,
		method: 'POST',
		data: params,
	});
}

/**
 * 更新审核状态
 * @param {object} params 拼团审核dto
 * @param {number} params.id
 * @param {number} params.auditStatus 审核状态:1 审核中 2 审核不通过  3 审核通过
 * @param {string} params.reason 不通过原因
 * @returns
 */
export function updateAuditStatus(params) {
	return request({
		url: `/achv/groupEvent/updateAuditStatus`,
		method: 'POST',
		data: params,
	});
}
/**
 * 批量更新参加审核状态(传ids)
 * @param {object} params 拼团审核dto
 * @param {array} params.ids
 * @param {number} params.eventId
 * @param {number} params.auditStatus 审核状态:1 审核中 2 审核不通过  3 审核通过
 * @param {string} params.reason 不通过原因
 * @returns
 */
export function updateJoinAuditStatus(params) {
	return request({
		url: `/achv/groupEvent/updateJoinAuditStatus`,
		method: 'POST',
		data: params,
	});
}

/**
 * 修改拼团活动
 * @param {object} params 拼轩活动dto
 * @param {number} params.id
 * @param {string} params.title 活动标题
 * @param {string} params.provinceCode 所属省份code
 * @param {string} params.cityCode 所属城市code
 * @param {string} params.areaCode 所属区域code
 * @param {string} params.address 活动地址
 * @param {string} params.coverImageUrl 封面图片地址
 * @param {number} params.auditStatus 审核状态:1 审核中 2 审核不通过  3 审核通过
 * @param {number} params.eventStatus 活动状态:1 未开始 2 进行中 3已结束
 * @param {number} params.eventValidStatus 活动是否有效:0 无效 1 有效
 * @param {string} params.groupLeaderName 团长组长名称
 * @param {string} params.groupLeaderLogo 团长组长logo
 * @param {array} params.joinMembers 参加人员类型与数量
 * @param {string} params.introduce 活动介绍
 * @param {object} params.startTime 活动开始时间,格式：yyyy-MM-dd HH:mm:ss
 * @param {object} params.endTime 活动结束时间,格式：yyyy-MM-dd HH:mm:ss
 * @param {number} params.rankingNum 排序序号
 * @param {array} params.adminStaffs 活动管理员信息dto
 * @returns
 */
export function updateGroupEvent(params) {
	return request({
		url: `/achv/groupEvent/updateGroupEvent`,
		method: 'POST',
		data: params,
	});
}

/**
 * 统计拼团活动参加审核状态(传eventId)
 * @param {object} params 拼轩活动参加查询dto
 * @param {number} params.pageNum 页码
 * @param {number} params.pageSize 每页记录数
 * @param {number} params.id
 * @param {number} params.tenantId
 * @param {array} params.ids
 * @param {number} params.eventId
 * @param {number} params.auditStatus 审核状态:1 审核中 2 审核不通过  3 审核通过
 * @param {number} params.joinType  参加类型：1 企业 2 研究院 3技术经理 4银行 5投资机构
 * @param {string} params.company 公司名称
 * @returns
 */
export function getGroupEventJoinAuditStatistics(params) {
	return request({
		url: `/achv/groupEvent/getGroupEventJoinAuditStatistics`,
		method: 'POST',
		data: params,
	});
}

/**
 * 分页查询参加记录
 * @param {object} params 拼轩活动参加查询dto
 * @param {number} params.pageNum 页码
 * @param {number} params.pageSize 每页记录数
 * @param {number} params.id
 * @param {number} params.tenantId
 * @param {array} params.ids
 * @param {number} params.eventId
 * @param {number} params.auditStatus 审核状态:1 审核中 2 审核不通过  3 审核通过
 * @param {number} params.joinType  参加类型：1 企业 2 研究院 3技术经理 4银行 5投资机构
 * @param {string} params.company 公司名称
 * @returns
 */
export function pageGroupEventJoin(params) {
	return request({
		url: `/achv/groupEvent/pageGroupEventJoin`,
		method: 'POST',
		data: params,
	});
}

/**
 * 查询拼团活动参加详情
 * @param {object} params 拼轩活动参加查询dto
 * @param {number} params.pageNum 页码
 * @param {number} params.pageSize 每页记录数
 * @param {number} params.id
 * @param {number} params.tenantId
 * @param {array} params.ids
 * @param {number} params.eventId
 * @param {number} params.auditStatus 审核状态:1 审核中 2 审核不通过  3 审核通过
 * @param {number} params.joinType  参加类型：1 企业 2 研究院 3技术经理 4银行 5投资机构
 * @param {string} params.company 公司名称
 * @returns
 */
export function detailGroupEventJoin(params) {
	return request({
		url: `/achv/groupEvent/detailGroupEventJoin`,
		method: 'POST',
		data: params,
	});
}

/**
 * 新增我的拼团信息
 * @param {object} params GroupEventJoinDto
 * @param {number} params.id
 * @param {number} params.eventId 活动主键
 * @param {string} params.name 姓名
 * @param {string} params.phone 手机
 * @param {string} params.company 公司
 * @param {string} params.companyLogo 公司logo
 * @param {string} params.demandType 需求类型： 1上游合作资源 2下游合作资源 3 技术资源 4人才资源 5金融资源 6其它
 * @param {string} params.demandDesc 需求描述
 * @param {number} params.inviterUserId 邀请人用户id
 * @param {number} params.joinType  参加类型：1 企业 2 研究院 3技术经理 4银行 5投资机构
 * @returns
 */
export function addGroupEventJoin(params) {
	return request({
		url: `/achv/groupEvent/addGroupEventJoin`,
		method: 'POST',
		data: params,
	});
}

/**
 * 更新我的拼团信息
 * @param {object} params GroupEventJoinDto
 * @param {number} params.id
 * @param {number} params.eventId 活动主键
 * @param {string} params.name 姓名
 * @param {string} params.phone 手机
 * @param {string} params.company 公司
 * @param {string} params.companyLogo 公司logo
 * @param {string} params.demandType 需求类型： 1上游合作资源 2下游合作资源 3 技术资源 4人才资源 5金融资源 6其它
 * @param {string} params.demandDesc 需求描述
 * @param {number} params.inviterUserId 邀请人用户id
 * @param {number} params.joinType  参加类型：1 企业 2 研究院 3技术经理 4银行 5投资机构
 * @returns
 */
export function updateGroupEventJoin(params) {
	return request({
		url: `/achv/groupEvent/updateGroupEventJoin`,
		method: 'POST',
		data: params,
	});
}

/**
 * 统计拼团活动审核状态
 * @returns
 */
export function getGroupEventAuditStatistics(params) {
	return request({
		url: `/achv/groupEvent/getGroupEventAuditStatistics`,
		method: 'POST',
		data: params,
	});
}

/**
 * 统计拼团活动 小程序 首页推荐设置
 * @returns
 */
export function updateGroupEventRecommendStatus(data) {
	return request({
		url: `/achv/groupEvent/updateGroupEventRecommendStatus`,
		method: 'POST',
		data,
	});
}

/**
 * 更新排序
 * @param {object} params
 * @param {number} params.id
 * @param {number} params.rankingNum
 * @param {array} params.saveDtoList
 * @returns
 */
export function updateRankingNum(data) {
	return request({
		url: `/achv/groupEvent/updateRankingNum`,
		method: 'POST',
		data,
	});
}
/**
 * 统计拼团活动 数据导出
 * @returns
 */
export function exportGroupEventJoin(data) {
	return request({
		url: `/achv/groupEvent/exportGroupEventJoin`,
		method: 'POST',
		data,
		responseType: 'blob',
	});
}

/**
 * 更新参加人员排序
 * @param {object} params 更新排序dto
 * @param {number} params.id
 * @param {number} params.rankingNum
 * @param {array} params.saveDtoList
 * @returns
 */
export function updateJoinRankingNum(data) {
	return request({
		url: `/achv/groupEvent/updateJoinRankingNum`,
		method: 'POST',
		data,
		responseType: 'blob',
	});
}
