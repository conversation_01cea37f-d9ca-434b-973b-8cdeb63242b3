import { request } from '@/utils/axios';

/**
 * 获取活动报名统计
 * @param {object} params 活动议程
 * @param {number} params.pageNum 页码
 * @param {number} params.pageSize 每页记录数
 * @param {number} params.eventId 活动主键
 * @param {array} params.ids 批量操作主键ids
 * @param {number} params.id 报名id主键
 * @param {object} params.applyStartTime 活动开始时间,格式：yyyy-MM-dd HH:mm:ss
 * @param {object} params.applyEndTime 活动结束时间,格式：yyyy-MM-dd HH:mm:ss
 * @param {number} params.auditStatus 审核状态：0 待审核 1 通过 2 不通过
 * @param {number} params.signInStatus 签到状态： 0 未签到 1 已签到
 * @param {string} params.phone 手机
 * @param {string} params.name 名称
 * @param {number} params.userId
 * @returns
 */
export function statistics(params = {}, option = {}) {
	return request({
		url: `/achv/event/join/statistics`,
		method: 'POST',
		data: params,
		...option,
	});
}

/**
 * 分页查询活动报名
 * @param {object} params 活动议程
 * @param {number} params.pageNum 页码
 * @param {number} params.pageSize 每页记录数
 * @param {number} params.eventId 活动主键
 * @param {array} params.ids 批量操作主键ids
 * @param {number} params.id 报名id主键
 * @param {object} params.applyStartTime 活动开始时间,格式：yyyy-MM-dd HH:mm:ss
 * @param {object} params.applyEndTime 活动结束时间,格式：yyyy-MM-dd HH:mm:ss
 * @param {number} params.auditStatus 审核状态：0 待审核 1 通过 2 不通过
 * @param {number} params.signInStatus 签到状态： 0 未签到 1 已签到
 * @param {string} params.phone 手机
 * @param {string} params.name 名称
 * @param {number} params.userId
 * @returns
 */
export function pageEventJoin(params = {}, option = {}) {
	return request({
		url: `/achv/event/join/pageEventJoin`,
		method: 'POST',
		data: params,
		...option,
	});
}

/**
 * 活动报名审核
 * @param {object} params 活动议程
 * @param {number} params.pageNum 页码
 * @param {number} params.pageSize 每页记录数
 * @param {number} params.eventId 活动主键
 * @param {array} params.ids 批量操作主键ids
 * @param {number} params.id 报名id主键
 * @param {object} params.applyStartTime 活动开始时间,格式：yyyy-MM-dd HH:mm:ss
 * @param {object} params.applyEndTime 活动结束时间,格式：yyyy-MM-dd HH:mm:ss
 * @param {number} params.auditStatus 审核状态：0 待审核 1 通过 2 不通过
 * @param {number} params.signInStatus 签到状态： 0 未签到 1 已签到
 * @param {string} params.phone 手机
 * @param {string} params.name 名称
 * @param {number} params.userId
 * @returns
 */
export function auditEventJoin(params = {}, option = {}) {
	return request({
		url: `/achv/event/join/auditEventJoin`,
		method: 'POST',
		data: params,
		...option,
	});
}

/**
 * 导出报名
 * @param {object} params 活动议程
 * @param {number} params.pageNum 页码
 * @param {number} params.pageSize 每页记录数
 * @param {number} params.eventId 活动主键
 * @param {array} params.ids 批量操作主键ids
 * @param {number} params.id 报名id主键
 * @param {object} params.applyStartTime 活动开始时间,格式：yyyy-MM-dd HH:mm:ss
 * @param {object} params.applyEndTime 活动结束时间,格式：yyyy-MM-dd HH:mm:ss
 * @param {number} params.auditStatus 审核状态：0 待审核 1 通过 2 不通过
 * @param {number} params.signInStatus 签到状态： 0 未签到 1 已签到
 * @param {string} params.phone 手机
 * @param {string} params.name 名称
 * @param {number} params.userId
 * @returns
 */
export function exportEventJoin(params = {}, option = {}) {
	return request({
		url: `/achv/event/join/exportEventJoin`,
		method: 'POST',
		data: params,
		responseType: 'blob',
		...option,
	});
}

/**
 * 短信批量发送
 * @param {string} file
 * @returns
 */
export function smsBatchSendImport(params = {}, option = {}) {
	return request({
		url: `/achv/event/join/smsBatchSendImport`,
		method: 'POST',
		data: params,
		header: {
			'Content-Type': 'multipart/form-data',
		},
		...option,
	});
}

/**
 * 导出短信报名内容
 * @param {object} params 活动议程
 * @param {number} params.pageNum 页码
 * @param {number} params.pageSize 每页记录数
 * @param {number} params.eventId 活动主键
 * @param {array} params.ids 批量操作主键ids
 * @param {number} params.id 报名id主键
 * @param {object} params.applyStartTime 活动开始时间,格式：yyyy-MM-dd HH:mm:ss
 * @param {object} params.applyEndTime 活动结束时间,格式：yyyy-MM-dd HH:mm:ss
 * @param {number} params.auditStatus 审核状态：0 待审核 1 通过 2 不通过
 * @param {number} params.signInStatus 签到状态： 0 未签到 1 已签到
 * @param {string} params.phone 手机
 * @param {string} params.name 名称
 * @param {number} params.userId
 * @param {string} params.shortCode 短码
 * @returns
 */
export function exportEventJoinSignInSms(params = {}, option = {}) {
	return request({
		url: `/achv/event/join/exportEventJoinSignInSms`,
		method: 'POST',
		data: params,
		responseType: 'blob',
		...option,
	});
}

/** 
 * excel导入
 * @param {string} file 
  * @param {string} eventId 
  * @returns
 */
export function importJoin(params = {}, option = {}) {
	return request({
		url: `/achv/event/join/import`,
		method: 'POST',
		data: params,
		header: {
			'Content-Type': 'multipart/form-data',
		},
		...option,
	});
  }

  
/**
 * 更新活动座位图片
 * @param {object} params 活动保存信息
 * @param {number} params.id
 * @param {string} params.eventSeatImageUrl 活动座图片地址
 * @param {number} params.seatArrangeStatus 座位编排状态：0 未编排 1 已编排
 * @returns
 */
export function updateEventSeatImage(params = {}, option = {}) {
    return request({
        url: `/achv/event/updateEventSeatImage`,
        method: "POST",
        data: params,
        ...option,
    });
}