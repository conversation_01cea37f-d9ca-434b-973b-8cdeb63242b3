import { request } from '@/utils/axios';

/**
 * 分页查询活动
 * @param {object} params 活动查询条件
 * @param {number} params.pageNum 页码
 * @param {number} params.pageSize 每页记录数
 * @param {array} params.ids 批量操作主键ids
 * @param {number} params.id id主键
 * @param {string} params.title 活动标题
 * @param {string} params.eventCode 活动编码
 * @param {number} params.applicationAuditStatus 是否需要审核报名 ： 1需要 0不需要
 * @param {number} params.showOaStatus  是否显示公众号 ： 1需要 0不需要
 * @param {number} params.eventStatus 活动状态：1 未开始 2 进行中 3已结束
 * @param {number} params.eventValidStatus 活动有效状态：0 无效 1 有效
 * @param {object} params.startTime 活动开始时间,格式：yyyy-MM-dd HH:mm:ss
 * @param {object} params.endTime 活动结束时间,格式：yyyy-MM-dd HH:mm:ss
 * @param {number} params.seatArrangeStatus  座位编排状态：0 未编排 1 已编排
 * @param {number} params.topUpStatus 置顶状态：0 不置顶 1置顶
 * @param {number} params.recommendStatus   推荐状态：0 不推荐 1推荐
 * @param {number} params.auditStatus 审核状态:0 草稿 1 审核中 2 审核不通过  3 审核通过
 * @param {number} params.showStatus 展示状态： 0不展示 1展示
 * @param {number} params.releasePlatform  发布平台：1 后台管理 2小程序
 * @param {number} params.releaseType 发布类型：1 管理员发布 2科转号发布
 * @param {number} params.tenantId
 * @returns
 */
export function pageEvent(params = {}, option = {}) {
	return request({
		url: `/achv/event/pageEvent`,
		method: 'POST',
		data: params,
		...option,
	});
}

/**
 * 新增活动
 * @param {object} params 活动保存信息
 * @param {number} params.id
 * @param {string} params.title 活动标题
 * @param {string} params.eventCode  活动编码
 * @param {string} params.address 活动地点
 * @param {string} params.coverImageUrl 封面图片地址
 * @param {string} params.posterImageUrl 海报图片地址
 * @param {string} params.eventQrcodeUrl 活动二维码地址
 * @param {string} params.eventSeatImageUrl 活动座图片地址
 * @param {string} params.eventSeatQrcodeUrl 活动座位查询二维码
 * @param {number} params.applicationAuditStatus 是否需要审核报名 ： 1需要 0不需要
 * @param {number} params.showOaStatus 是否显示公众号 ： 1需要 0不需要
 * @param {number} params.maximumApplicationNumber 限制最大报名人数，如为null时为不限制
 * @param {number} params.eventStatus 活动状态：1 未开始 2 进行中 3已结束
 * @param {number} params.eventValidStatus 活动有效状态：0 无效 1 有效
 * @param {object} params.startTime 活动开始时间,格式：yyyy-MM-dd HH:mm:ss
 * @param {object} params.endTime 活动结束时间,格式：yyyy-MM-dd HH:mm:ss
 * @param {string} params.introduce 活动介绍
 * @param {number} params.seatArrangeStatus 座位编排状态：0 未编排 1 已编排
 * @param {object} params.rankingNum 排序序号
 * @param {array} params.adminStaffList 活动管理员信息dto
 * @param {string} params.enrollDesignJson 报名内容设计json
 * @param {string} params.eventSignInQrcodeUrl 活动签到二维码
 * @param {number} params.recommendStatus
 * @param {number} params.topUpStatus
 * @param {string} params.userAgreement 用户协议
 * @param {number} params.openAgreementStatus 用户协议状态： 0 关闭 1启用
 * @param {string} params.resolveAddress
 * @param {string} params.customConfig 自定义配置
 * @param {number} params.auditStatus 审核状态:-1草稿 1 审核中 2 审核不通过  3 审核通过
 * @param {number} params.showStatus 展示状态： 0不展示 1展示
 * @param {string} params.reason 审核原因
 * @param {string} params.showPictures 展示图片
 * @param {number} params.releasePlatform  发布平台：1 后台管理 2小程序
 * @param {number} params.releaseType 发布类型：1 管理员发布 2科转号发布
 * @param {array} params.ttChannelsIds 关联科转号
 * @param {array} params.delTTChannelsIds 删除关联科转号
 * @param {string} params.qrCode 活动展示的微信二维码
 * @param {string} params.qrCodeTips 二维码提示语
 * @param {string} params.shareImage 分享图片
 * @returns
 */
export function addEvent(params = {}, option = {}) {
	return request({
		url: `/achv/event/addEvent`,
		method: 'POST',
		data: params,
		...option,
	});
}

/**
 * 修改微信视频
 */
export function updateEvent(params = {}, option = {}) {
	return request({
		url: `/achv/event/updateEvent`,
		method: 'POST',
		data: params,
		...option,
	});
}

/**
 * 批量删除活动
 * @param {object} params 活动查询条件
 * @param {number} params.pageNum 页码
 * @param {number} params.pageSize 每页记录数
 * @param {array} params.ids 批量操作主键ids
 * @param {number} params.id id主键
 * @param {string} params.title 活动标题
 * @param {string} params.eventCode 活动编码
 * @param {number} params.applicationAuditStatus 是否需要审核报名 ： 1需要 0不需要
 * @param {number} params.showOaStatus  是否显示公众号 ： 1需要 0不需要
 * @param {number} params.eventStatus 活动状态：1 未开始 2 进行中 3已结束
 * @param {number} params.eventValidStatus 活动有效状态：0 无效 1 有效
 * @param {object} params.startTime 活动开始时间,格式：yyyy-MM-dd HH:mm:ss
 * @param {object} params.endTime 活动结束时间,格式：yyyy-MM-dd HH:mm:ss
 * @param {number} params.seatArrangeStatus  座位编排状态：0 未编排 1 已编排
 * @param {number} params.topUpStatus 置顶状态：0 不置顶 1置顶
 * @param {number} params.recommendStatus   推荐状态：0 不推荐 1推荐
 * @param {number} params.auditStatus 审核状态:0 草稿 1 审核中 2 审核不通过  3 审核通过
 * @param {number} params.showStatus 展示状态： 0不展示 1展示
 * @param {number} params.releasePlatform  发布平台：1 后台管理 2小程序
 * @param {number} params.releaseType 发布类型：1 管理员发布 2科转号发布
 * @param {number} params.tenantId
 * @returns
 */
export function delEvent(params = {}, option = {}) {
	return request({
		url: `/achv/event/batchDelEvent`,
		method: 'POST',
		data: params,
		...option,
	});
}

/**
 * 获取活动详情
 * @param {object} params 活动查询条件
 * @param {number} params.pageNum 页码
 * @param {number} params.pageSize 每页记录数
 * @param {array} params.ids 批量操作主键ids
 * @param {number} params.id id主键
 * @param {string} params.title 活动标题
 * @param {string} params.eventCode 活动编码
 * @param {number} params.applicationAuditStatus 是否需要审核报名 ： 1需要 0不需要
 * @param {number} params.showOaStatus  是否显示公众号 ： 1需要 0不需要
 * @param {number} params.eventStatus 活动状态：1 未开始 2 进行中 3已结束
 * @param {number} params.eventValidStatus 活动有效状态：0 无效 1 有效
 * @param {object} params.startTime 活动开始时间,格式：yyyy-MM-dd HH:mm:ss
 * @param {object} params.endTime 活动结束时间,格式：yyyy-MM-dd HH:mm:ss
 * @param {number} params.seatArrangeStatus  座位编排状态：0 未编排 1 已编排
 * @param {number} params.topUpStatus 置顶状态：0 不置顶 1置顶
 * @param {number} params.recommendStatus   推荐状态：0 不推荐 1推荐
 * @param {number} params.auditStatus 审核状态:0 草稿 1 审核中 2 审核不通过  3 审核通过
 * @param {number} params.showStatus 展示状态： 0不展示 1展示
 * @param {number} params.releasePlatform  发布平台：1 后台管理 2小程序
 * @param {number} params.releaseType 发布类型：1 管理员发布 2科转号发布
 * @param {number} params.tenantId
 * @returns
 */
export function getEvent(params = {}, option = {}) {
	return request({
		url: `/achv/event/detailEvent`,
		method: 'POST',
		data: params,
		...option,
	});
}

/**
 * 更新排序
 * @param {object} params 更新排序dto
 * @param {number} params.id
 * @param {number} params.rankingNum
 * @param {array} params.saveDtoList
 * @returns
 */
export function updateRankingNum(params = {}, option = {}) {
	return request({
		url: `/achv/event/updateRankingNum`,
		method: 'POST',
		data: params,
		...option,
	});
}

/**
 * 更新活动推荐状态
 * @param {object} params 微信视频号管理保存dto
 * @param {number} params.id
 * @param {number} params.recommendStatus
 * @returns
 */
export function updateEventTopUpStatus(params = {}, option = {}) {
	return request({
		url: `/achv/event/updateEventTopUpStatus`,
		method: 'POST',
		data: params,
		...option,
	});
}

/**
 * 审核
 * @param {object} params 专家审核对象
 * @param {number} params.id id
 * @param {string} params.reason 不通过原因
 * @param {number} params.status 审核状态:1 审核中 2 审核不通过  3 审核通过
 * @returns
 */
export function auditEvent(data = {}) {
	return request({
		url: '/achv/event/audit',
		method: 'post',
		data,
	});
}

/**
 * 更新显示状态
 * @param {object} params 显示状态对象
 * @param {number} params.id id
 * @param {number} params.showStatus 展示状态： 0不展示 1展示
 * @returns
 */
export function updateEventValidStatus(data = {}) {
	return request({
		url: '/achv/event/updateEventValidStatus',
		method: 'post',
		data,
	});
}

/**
 * 统计审核数
 * @returns
 */
export function getEventAuditNum(data = {}) {
	return request({
		url: '/achv/event/getEventAuditNum',
		method: 'post',
		data,
	});
}
