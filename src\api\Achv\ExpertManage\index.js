import { request } from '@/utils/axios';

/**
 * 专家分页查询
 * @param {object} params 专家查询dto
 * @param {number} params.pageNum 页码
 * @param {number} params.pageSize 每页记录数
 * @param {number} params.sortTenantId
 * @param {array} params.provinceCodes 多选所属省份code
 * @param {array} params.cityCodes 多选所属城市code
 * @param {array} params.areaCodes 多选所属区域code
 * @param {number} params.agencyId 机构id
 * @param {number} params.id 专家id
 * @param {array} params.ids 专家ids
 * @param {string} params.expertName 专家名称
 * @param {array} params.industryDisciplineIds 行业学科ids
 * @param {array} params.industryAreaIds 细分领域ids
 * @param {number} params.scoreType 排序类型：  1 综合评分 2 专业指数  3 产业指数
 * @param {string} params.sortStr 排序,传 desc/asc
 * @param {number} params.isEncryptionName 是否加密专家名称：1 是  0 否
 * @param {number} params.auditStatus 审核状态:1 审核中 2 审核不通过  3 审核通过
 * @param {array} params.industryList
 * @param {array} params.excludeIds ids（排除的结果id）
 * @param {number} params.tenantId
 * @returns
 */
export const pageExpert = (data) => {
	return request({
		url: '/achv/expert/page',
		method: 'post',
		data,
	});
};

/**
 * 专家新增
 * @param {object} params 新增专家dto
 * @param {number} params.id 主键id
 * @param {number} params.auditStatus
 * @param {string} params.expertName 专家名称
 * @param {number} params.gender 性别(1:男 2：女)
 * @param {string} params.education 学历
 * @param {string} params.expertTitle 职称
 * @param {number} params.agencyId 专家所属机构id
 * @param {string} params.agencyName 专家所属机构名称
 * @param {string} params.researchDirection 研究方向,多个研究方向使用：|分割
 * @param {string} params.headUrl 专家头像url
 * @param {number} params.rankingNum 排名序号
 * @param {string} params.careerInformation 在职信息
 * @param {number} params.citesNumber 引用量
 * @param {number} params.papersNumber 论文数量
 * @param {number} params.activationScore 活跃度
 * @param {number} params.diversityScore 多样性
 * @param {number} params.socialityScore 社交性
 * @param {number} params.honorIndexScore 荣誉指数
 * @param {number} params.professionalIndex 专业指数
 * @param {number} params.industrialScaleScore 产业规模
 * @param {number} params.industryManagementScore 产业经营
 * @param {number} params.industrialCreditScore 产业信用
 * @param {number} params.industrialIpScore 产业IP
 * @param {number} params.industrialQualificationsScore 产业资质
 * @param {number} params.industryIndex 产业指数
 * @param {number} params.compositeIndex 综合指数
 * @param {string} params.introduction 个人简介
 * @param {string} params.representativePapers 代表性论文/成果
 * @param {string} params.mainHonors 主要荣誉/项目
 * @param {array} params.industryAreaList 专家行业细分dto
 * @param {array} params.industryDisciplineList 专家行业细分dto
 * @param {string} params.phone 手机号码
 * @param {string} params.email
 * @param {number} params.userId
 * @param {number} params.sourceType
 * @param {string} params.provinceCode 所属省份编码
 * @param {string} params.cityCode 所属城市编码
 * @param {string} params.areaCode 所属区域code
 * @param {number} params.hindexScore
 * @param {number} params.gindexScore
 * @returns
 */
export const addExpert = (data) => {
	return request({
		url: '/achv/expert/add',
		method: 'post',
		data,
	});
};

/**
 * 专家修改
 * @param {object} params 新增专家dto
 * @param {number} params.id 主键id
 * @param {number} params.auditStatus
 * @param {string} params.expertName 专家名称
 * @param {number} params.gender 性别(1:男 2：女)
 * @param {string} params.education 学历
 * @param {string} params.expertTitle 职称
 * @param {number} params.agencyId 专家所属机构id
 * @param {string} params.agencyName 专家所属机构名称
 * @param {string} params.researchDirection 研究方向,多个研究方向使用：|分割
 * @param {string} params.headUrl 专家头像url
 * @param {number} params.rankingNum 排名序号
 * @param {string} params.careerInformation 在职信息
 * @param {number} params.citesNumber 引用量
 * @param {number} params.papersNumber 论文数量
 * @param {number} params.activationScore 活跃度
 * @param {number} params.diversityScore 多样性
 * @param {number} params.socialityScore 社交性
 * @param {number} params.honorIndexScore 荣誉指数
 * @param {number} params.professionalIndex 专业指数
 * @param {number} params.industrialScaleScore 产业规模
 * @param {number} params.industryManagementScore 产业经营
 * @param {number} params.industrialCreditScore 产业信用
 * @param {number} params.industrialIpScore 产业IP
 * @param {number} params.industrialQualificationsScore 产业资质
 * @param {number} params.industryIndex 产业指数
 * @param {number} params.compositeIndex 综合指数
 * @param {string} params.introduction 个人简介
 * @param {string} params.representativePapers 代表性论文/成果
 * @param {string} params.mainHonors 主要荣誉/项目
 * @param {array} params.industryAreaList 专家行业细分dto
 * @param {array} params.industryDisciplineList 专家行业细分dto
 * @param {string} params.phone 手机号码
 * @param {string} params.email
 * @param {number} params.userId
 * @param {number} params.sourceType
 * @param {string} params.provinceCode 所属省份编码
 * @param {string} params.cityCode 所属城市编码
 * @param {string} params.areaCode 所属区域code
 * @param {number} params.hindexScore
 * @param {number} params.gindexScore
 * @returns
 */
export const updateExpert = (data) => {
	return request({
		url: '/achv/expert/update',
		method: 'post',
		data,
	});
};

/**
 * 专家详情查询
 * @param {object} params 专家查询dto
 * @param {number} params.pageNum 页码
 * @param {number} params.pageSize 每页记录数
 * @param {number} params.sortTenantId
 * @param {array} params.provinceCodes 多选所属省份code
 * @param {array} params.cityCodes 多选所属城市code
 * @param {array} params.areaCodes 多选所属区域code
 * @param {number} params.agencyId 机构id
 * @param {number} params.id 专家id
 * @param {array} params.ids 专家ids
 * @param {string} params.expertName 专家名称
 * @param {array} params.industryDisciplineIds 行业学科ids
 * @param {array} params.industryAreaIds 细分领域ids
 * @param {number} params.scoreType 排序类型：  1 综合评分 2 专业指数  3 产业指数
 * @param {string} params.sortStr 排序,传 desc/asc
 * @param {number} params.isEncryptionName 是否加密专家名称：1 是  0 否
 * @param {number} params.auditStatus 审核状态:1 审核中 2 审核不通过  3 审核通过
 * @param {array} params.industryList
 * @param {array} params.excludeIds ids（排除的结果id）
 * @param {number} params.tenantId
 * @returns
 */
export const detailExpert = (data) => {
	return request({
		url: '/achv/expert/detail',
		method: 'post',
		data,
	});
};

/**
 * 专家批量删除
 * @param {object} params 专家查询dto
 * @param {number} params.pageNum 页码
 * @param {number} params.pageSize 每页记录数
 * @param {number} params.sortTenantId
 * @param {array} params.provinceCodes 多选所属省份code
 * @param {array} params.cityCodes 多选所属城市code
 * @param {array} params.areaCodes 多选所属区域code
 * @param {number} params.agencyId 机构id
 * @param {number} params.id 专家id
 * @param {array} params.ids 专家ids
 * @param {string} params.expertName 专家名称
 * @param {array} params.industryDisciplineIds 行业学科ids
 * @param {array} params.industryAreaIds 细分领域ids
 * @param {number} params.scoreType 排序类型：  1 综合评分 2 专业指数  3 产业指数
 * @param {string} params.sortStr 排序,传 desc/asc
 * @param {number} params.isEncryptionName 是否加密专家名称：1 是  0 否
 * @param {number} params.auditStatus 审核状态:1 审核中 2 审核不通过  3 审核通过
 * @param {array} params.industryList
 * @param {array} params.excludeIds ids（排除的结果id）
 * @param {number} params.tenantId
 * @returns
 */
export const batchDelExpert = (data) => {
	return request({
		url: '/achv/expert/batchDel',
		method: 'post',
		data,
	});
};

/**
 * 更新排序
 * @param {object} params 更新排序dto
 * @param {number} params.id
 * @param {number} params.rankingNum
 * @param {array} params.saveDtoList
 * @returns
 */
export const updateRankingNum = (data) => {
	return request({
		url: '/achv/expert/updateRankingNum',
		method: 'post',
		data,
	});
};

/**
 * 审核专家
 * @param {object} params 专家审核对象
 * @param {number} params.id id
 * @param {string} params.reason 不通过原因
 * @param {number} params.status 审核状态:1 审核中 2 审核不通过  3 审核通过
 * @returns
 */
export const auditExpert = (data) => {
	return request({
		url: '/achv/expert/audit',
		method: 'post',
		data,
	});
};

/**
 * 专家细分行业查询
 * @returns
 */
export const listExpertIndustry = (data) => {
	return request({
		url: '/achv/expert/industry/list',
		method: 'post',
		data,
	});
};

/**
 * 获取专家全部地区
 * @returns
 */
export const regionList = (data) => {
	return request({
		url: '/achv/expert/regionList',
		method: 'post',
		data,
	});
};

/**
 * 导出专家
 * @param {object} params 专家查询dto
 * @param {number} params.pageNum 页码
 * @param {number} params.pageSize 每页记录数
 * @param {number} params.sortTenantId
 * @param {array} params.provinceCodes 多选所属省份code
 * @param {array} params.cityCodes 多选所属城市code
 * @param {array} params.areaCodes 多选所属区域code
 * @param {number} params.agencyId 机构id
 * @param {number} params.id 专家id
 * @param {array} params.ids 专家ids
 * @param {string} params.expertName 专家名称
 * @param {array} params.industryDisciplineIds 行业学科ids
 * @param {array} params.industryAreaIds 细分领域ids
 * @param {number} params.scoreType 排序类型：  1 综合评分 2 专业指数  3 产业指数
 * @param {string} params.sortStr 排序,传 desc/asc
 * @param {number} params.isEncryptionName 是否加密专家名称：1 是  0 否
 * @param {number} params.auditStatus 审核状态:1 审核中 2 审核不通过  3 审核通过
 * @param {array} params.industryList
 * @param {array} params.excludeIds ids（排除的结果id）
 * @param {number} params.tenantId
 * @returns
 */
export const exportExpert = (data) => {
	return request({
		url: '/achv/export/exportExpert',
		method: 'post',
		data,
		responseType: 'blob',
	});
};

/**
 * 专家导入
 * @param {string} file
 * @returns
 */
export function importExpert(params = {}, option = {}) {
	return request({
		url: `/achv/import/expertImport`,
		method: 'POST',
		data: params,
		header: {
			'Content-Type': 'multipart/form-data',
		},
		...option,
	});
}
