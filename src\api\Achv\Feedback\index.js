import { request } from '@/utils/axios';

/**
 * 反馈分页
 * @param {object} params 反馈查询条件
 * @param {number} params.pageNum 页码
 * @param {number} params.pageSize 每页记录数
 * @param {number} params.id 主键id
 * @param {array} params.ids id列表
 * @param {string} params.title 标题
 * @param {number} params.status 状态(1:已采纳,2:已实现,3:不合适)
 * @returns
 */
export function pageFeedback(params = {}, option = {}) {
	return request({
		url: `/achv/feedback/page`,
		method: 'POST',
		data: params,
		...option,
	});
}

/**
 * 反馈详情
 * @param {object} params 反馈查询条件
 * @param {number} params.pageNum 页码
 * @param {number} params.pageSize 每页记录数
 * @param {number} params.id 主键id
 * @param {array} params.ids id列表
 * @param {string} params.title 标题
 * @param {number} params.status 状态(1:已采纳,2:已实现,3:不合适)
 * @returns
 */
export function detailFeedback(params = {}, option = {}) {
	return request({
		url: `/achv/feedback/detail`,
		method: 'POST',
		data: params,
		...option,
	});
}
/**
 * 反馈删除
 * @param {object} params 反馈查询条件
 * @param {number} params.pageNum 页码
 * @param {number} params.pageSize 每页记录数
 * @param {number} params.id 主键id
 * @param {array} params.ids id列表
 * @param {string} params.title 标题
 * @param {number} params.status 状态(1:已采纳,2:已实现,3:不合适)
 * @returns
 */
export function deleteFeedback(params = {}, option = {}) {
	return request({
		url: `/achv/feedback/delete`,
		method: 'POST',
		data: params,
		...option,
	});
}

/**
 * 反馈修改状态
 * @param {object} params 反馈
 * @param {number} params.id id
 * @param {string} params.title 标题
 * @param {string} params.content 内容
 * @param {string} params.file 文件内容(json存储)
 * @param {string} params.contactInfo 联系方式
 * @param {number} params.status 状态(1:已采纳,2:已实现,3:不合适)
 * @param {number} params.userId 用户id
 * @param {number} params.anonymousFlag 匿名标识(0:非匿名,1:匿名)
 * @returns
 */
export function updateFeedbackStatus(params = {}, option = {}) {
	return request({
		url: `/achv/feedback/updateStatus`,
		method: 'POST',
		data: params,
		...option,
	});
}

/**
 * 反馈评论添加
 * @param {object} params 反馈评论
 * @param {number} params.feedbackId 反馈id
 * @param {string} params.content 内容
 * @param {number} params.officialFlag 官方标识(0:非官方,1:官方)
 * @param {number} params.parentId 上级id
 * @returns
 */
export function addFeedbackComment(params = {}, option = {}) {
	return request({
		url: `/achv/feedback/comment/add`,
		method: 'POST',
		data: params,
		...option,
	});
}

/**
 * 反馈评论删除
 * @param {object} params 评论查询条件
 * @param {array} params.feedbackIds 反馈id列表
 * @param {array} params.ids id列表
 * @returns
 */
export function deleteFeedbackComment(params = {}, option = {}) {
	return request({
		url: `/achv/feedback/comment/delete`,
		method: 'POST',
		data: params,
		...option,
	});
}
