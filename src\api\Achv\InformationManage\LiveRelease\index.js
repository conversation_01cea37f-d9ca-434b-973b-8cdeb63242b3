import { request } from '@/utils/axios';

/**
 * 分页查询资讯直播
 * @param {object} params 资讯直播查询dto
 * @param {number} params.pageNum 页码
 * @param {number} params.pageSize 每页记录数
 * @param {number} params.releaseStatus 发布状态：0 否 1是
 * @param {number} params.recommendStatus 推荐状态： 0否 1是
 * @param {string} params.liveSource 直播来源
 * @param {number} params.id
 * @param {array} params.ids
 * @param {number} params.tenantId
 * @returns
 */
export function pageLive(params = {}, option = {}) {
	return request({
		url: `/achv/info/live/page`,
		method: 'POST',
		data: params,
		...option,
	});
}

/**
 * 新增资讯直播
 * @param {object} params 资讯直播保存dto
 * @param {number} params.id 主键id,修改时传
 * @param {string} params.name 直播名称
 * @param {object} params.startTime 开始时间
 * @param {number} params.liveStatus 直播状态： 1直播预告 2 直播中 3直播回顾 4 结束
 * @param {string} params.coverUrl 封面图片url
 * @param {string} params.address 直播地址
 * @param {string} params.liveSource 直播来源
 * @param {string} params.sharePicUrl 分享图url
 * @param {number} params.releaseStatus 发布状态：0 否 1是
 * @param {number} params.recommendStatus 推荐状态： 0否 1是
 * @param {number} params.rankingNum 排序序号
 * @param {string} params.remarks 备注
 * @returns
 */
export function addLive(params = {}, option = {}) {
	return request({
		url: `/achv/info/live/add`,
		method: 'POST',
		data: params,
		...option,
	});
}

/**
 * 修改资讯直播
 * @param {object} params 资讯直播保存dto
 * @param {number} params.id 主键id,修改时传
 * @param {string} params.name 直播名称
 * @param {object} params.startTime 开始时间
 * @param {number} params.liveStatus 直播状态： 1直播预告 2 直播中 3直播回顾 4 结束
 * @param {string} params.coverUrl 封面图片url
 * @param {string} params.address 直播地址
 * @param {string} params.liveSource 直播来源
 * @param {string} params.sharePicUrl 分享图url
 * @param {number} params.releaseStatus 发布状态：0 否 1是
 * @param {number} params.recommendStatus 推荐状态： 0否 1是
 * @param {number} params.rankingNum 排序序号
 * @param {string} params.remarks 备注
 * @returns
 */
export function updateLive(params = {}, option = {}) {
	return request({
		url: `/achv/info/live/update`,
		method: 'POST',
		data: params,
		...option,
	});
}

/**
 * 删除资讯直播
 * @param {object} params 资讯直播查询dto
 * @param {number} params.pageNum 页码
 * @param {number} params.pageSize 每页记录数
 * @param {number} params.releaseStatus 发布状态：0 否 1是
 * @param {number} params.recommendStatus 推荐状态： 0否 1是
 * @param {string} params.liveSource 直播来源
 * @param {number} params.id
 * @param {array} params.ids
 * @param {number} params.tenantId
 * @returns
 */
export function delLive(params = {}, option = {}) {
	return request({
		url: `/achv/info/live/batchDel`,
		method: 'POST',
		data: params,
		...option,
	});
}

/**
 * 获取资讯直播
 * @param {object} params 资讯直播查询dto
 * @param {number} params.pageNum 页码
 * @param {number} params.pageSize 每页记录数
 * @param {number} params.releaseStatus 发布状态：0 否 1是
 * @param {number} params.recommendStatus 推荐状态： 0否 1是
 * @param {string} params.liveSource 直播来源
 * @param {number} params.id
 * @param {array} params.ids
 * @param {number} params.tenantId
 * @returns
 */
export function getLive(params = {}, option = {}) {
	return request({
		url: `/achv/info/live/getDetail`,
		method: 'POST',
		data: params,
		...option,
	});
}

/**
 * 更新资讯直播发布状态
 * @param {object} params 资讯直播保存dto
 * @param {number} params.id 主键id,修改时传
 * @param {string} params.name 直播名称
 * @param {object} params.startTime 开始时间
 * @param {number} params.liveStatus 直播状态： 1直播预告 2 直播中 3直播回顾 4 结束
 * @param {string} params.coverUrl 封面图片url
 * @param {string} params.address 直播地址
 * @param {string} params.liveSource 直播来源
 * @param {string} params.sharePicUrl 分享图url
 * @param {number} params.releaseStatus 发布状态：0 否 1是
 * @param {number} params.recommendStatus 推荐状态： 0否 1是
 * @param {number} params.rankingNum 排序序号
 * @param {string} params.remarks 备注
 * @returns
 */
export function updateReleaseStatus(params = {}, option = {}) {
	return request({
		url: `/achv/info/live/updateReleaseStatus`,
		method: 'POST',
		data: params,
		...option,
	});
}

/**
 * 更新资讯直播推荐状态
 * @param {object} params 资讯直播保存dto
 * @param {number} params.id 主键id,修改时传
 * @param {string} params.name 直播名称
 * @param {object} params.startTime 开始时间
 * @param {number} params.liveStatus 直播状态： 1直播预告 2 直播中 3直播回顾 4 结束
 * @param {string} params.coverUrl 封面图片url
 * @param {string} params.address 直播地址
 * @param {string} params.liveSource 直播来源
 * @param {string} params.sharePicUrl 分享图url
 * @param {number} params.releaseStatus 发布状态：0 否 1是
 * @param {number} params.recommendStatus 推荐状态： 0否 1是
 * @param {number} params.rankingNum 排序序号
 * @param {string} params.remarks 备注
 * @returns
 */
export function updateRecommendStatus(params = {}, option = {}) {
	return request({
		url: `/achv/info/live/updateRecommendStatus`,
		method: 'POST',
		data: params,
		...option,
	});
}

/**
 * 更新资讯直播发布状态
 * @param {object} params 资讯直播保存dto
 * @param {number} params.id 主键id,修改时传
 * @param {string} params.name 直播名称
 * @param {object} params.startTime 开始时间
 * @param {number} params.liveStatus 直播状态： 1直播预告 2 直播中 3直播回顾 4 结束
 * @param {string} params.coverUrl 封面图片url
 * @param {string} params.address 直播地址
 * @param {string} params.liveSource 直播来源
 * @param {string} params.sharePicUrl 分享图url
 * @param {number} params.releaseStatus 发布状态：0 否 1是
 * @param {number} params.recommendStatus 推荐状态： 0否 1是
 * @param {number} params.rankingNum 排序序号
 * @param {string} params.remarks 备注
 * @returns
 */
export function updateLiveStatus(params = {}, option = {}) {
	return request({
		url: `/achv/info/live/updateLiveStatus`,
		method: 'POST',
		data: params,
		...option,
	});
}

/**
 * 更新排序
 * @param {object} params 更新排序dto
 * @param {number} params.id
 * @param {number} params.rankingNum
 * @param {array} params.saveDtoList
 * @returns
 */
export function updateRankingNum(params = {}, option = {}) {
	return request({
		url: `/achv/info/live/updateRankingNum`,
		method: 'POST',
		data: params,
		...option,
	});
}
