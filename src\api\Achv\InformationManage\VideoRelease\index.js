import { request } from '@/utils/axios';
/**
 * 分页查询微信视频
 * @param {object} params 微信视频号管理查询dto
 * @param {number} params.pageNum 页码
 * @param {number} params.pageSize 每页记录数
 * @param {number} params.id
 * @param {array} params.ids
 * @param {string} params.title 标题
 * @param {string} params.channelsName 视频号名称
 * @param {number} params.releaseStatus 发布状态：0-未发布，1-已发布
 * @param {number} params.tenantId
 * @returns
 */
export function pageWechatChannels(params = {}, option = {}) {
	return request({
		url: `/achv/wechatChannels/page`,
		method: 'POST',
		data: params,
		...option,
	});
}

/**
 * 新增微信视频
 * @param {object} params 微信视频号管理保存dto
 * @param {number} params.id
 * @param {string} params.title 标题
 * @param {string} params.coverUrl 封面图片url
 * @param {string} params.channelsName 视频号名称
 * @param {string} params.channelsLogoUrl 视频号logo图片url
 * @param {number} params.rankingNum 排序序号
 * @param {string} params.duration 时长
 * @param {number} params.releaseStatus
 * @param {string} params.finderUserName 视频号id
 * @param {string} params.feedId 视频id
 * @param {string} params.remarks
 * @returns
 */
export function addWechatChannels(params = {}, option = {}) {
	return request({
		url: `/achv/wechatChannels/add`,
		method: 'POST',
		data: params,
		...option,
	});
}

/**
 * 修改微信视频
 * @param {object} params 微信视频号管理保存dto
 * @param {number} params.id
 * @param {string} params.title 标题
 * @param {string} params.coverUrl 封面图片url
 * @param {string} params.channelsName 视频号名称
 * @param {string} params.channelsLogoUrl 视频号logo图片url
 * @param {number} params.rankingNum 排序序号
 * @param {string} params.duration 时长
 * @param {number} params.releaseStatus
 * @param {string} params.finderUserName 视频号id
 * @param {string} params.feedId 视频id
 * @param {string} params.remarks
 * @returns
 */
export function updateWechatChannels(params = {}, option = {}) {
	return request({
		url: `/achv/wechatChannels/update`,
		method: 'POST',
		data: params,
		...option,
	});
}

/**
 * 批量删除微信视频
 * @param {object} params 微信视频号管理查询dto
 * @param {number} params.pageNum 页码
 * @param {number} params.pageSize 每页记录数
 * @param {number} params.id
 * @param {array} params.ids
 * @param {string} params.title 标题
 * @param {string} params.channelsName 视频号名称
 * @param {number} params.releaseStatus 发布状态：0-未发布，1-已发布
 * @param {number} params.tenantId
 * @returns
 */
export function delWechatChannels(params = {}, option = {}) {
	return request({
		url: `/achv/wechatChannels/batchDel`,
		method: 'POST',
		data: params,
		...option,
	});
}

/**
 * 获取微信视频详情
 * @param {object} params 微信视频号管理查询dto
 * @param {number} params.pageNum 页码
 * @param {number} params.pageSize 每页记录数
 * @param {number} params.id
 * @param {array} params.ids
 * @param {string} params.title 标题
 * @param {string} params.channelsName 视频号名称
 * @param {number} params.releaseStatus 发布状态：0-未发布，1-已发布
 * @param {number} params.tenantId
 * @returns
 */
export function getWechatChannels(params = {}, option = {}) {
	return request({
		url: `/achv/wechatChannels/getDetail`,
		method: 'POST',
		data: params,
		...option,
	});
}

/**
 * 更新排序
 * @param {object} params 更新排序dto
 * @param {number} params.id
 * @param {number} params.rankingNum
 * @param {array} params.saveDtoList
 * @returns
 */
export function updateRankingNum(params = {}, option = {}) {
	return request({
		url: `/achv/wechatChannels/updateRankingNum`,
		method: 'POST',
		data: params,
		...option,
	});
}
/**
 * 更新微信视频推荐状态
 * @param {object} params 微信视频号管理保存dto
 * @param {number} params.id
 * @param {string} params.title 标题
 * @param {string} params.coverUrl 封面图片url
 * @param {string} params.channelsName 视频号名称
 * @param {string} params.channelsLogoUrl 视频号logo图片url
 * @param {number} params.rankingNum 排序序号
 * @param {string} params.duration 时长
 * @param {number} params.releaseStatus
 * @param {string} params.finderUserName 视频号id
 * @param {string} params.feedId 视频id
 * @param {number} params.recommendStatus
 * @param {string} params.remarks
 * @param {string} params.feedToken
 * @returns
 */
export function updateRecommendStatus(params = {}, option = {}) {
	return request({
		url: `/achv/wechatChannels/updateRecommendStatus`,
		method: 'POST',
		data: params,
		...option,
	});
}

/**
 * 审核操作
 * @param {object} params 专家审核对象
 * @param {number} params.id id
 * @param {string} params.reason 不通过原因
 * @param {number} params.status 审核状态:1 审核中 2 审核不通过  3 审核通过
 * @returns
 */
export const auditRecommendStatus = (data = {}) => {
	return request({
		url: '/achv/wechatChannels/audit',
		method: 'post',
		data,
	});
};

/**
 * 更新显示状态
 * @param {object} params 显示状态对象
 * @param {number} params.id id
 * @param {number} params.showStatus 展示状态： 0不展示 1展示
 * @returns
 */
export const updateShowStatus = (data = {}) => {
	return request({
		url: '/achv/wechatChannels/updateShowStatus',
		method: 'post',
		data,
	});
};
