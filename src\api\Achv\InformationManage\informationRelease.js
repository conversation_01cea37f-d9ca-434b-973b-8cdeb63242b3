import { request } from '@/utils/axios';

/**
 * 获取政策 分页
 * @param {*} data
 * id           政策id
 * pageSize     每页数量
 * pageNum      页码
 * @returns
 */
export const pagePolicy = (data) => {
	return request({
		url: '/achv/policy/page',
		method: 'post',
		data,
	});
};

/**
 * 新增政策
 * @param {*} data
 * title           政策标题
 * releaseOrg      发布机构部门
 * releaseTime     发布时间
 * content         政策内容
 * @returns
 */
export const policyAdd = (data) => {
	return request({
		url: '/achv/policy/add',
		method: 'post',
		data,
	});
};

/**
 * 更新政策
 * @param {*} data
 * id           	  主键id
 * title            政策标题
 * guidanceOrg      指导单位
 * hostOrg          主办单位
 * undertakeOrg     承办单位
 * venue            政策地点
 * introduce        政策介绍
 * startTime        政策开始时间
 * endTime          结束开始时间
 * coverImageUrl    封面图片地址
 * @returns
 */
export const policyUpdate = (data) => {
	return request({
		url: '/achv/policy/update',
		method: 'post',
		data,
	});
};

/**
 * 删除政策
 * @param {*} data
 * ids 删除 政策id数组
 * @returns
 */
export const policyDel = (data) => {
	return request({
		url: '/achv/policy/del',
		method: 'post',
		data,
	});
};

/**
 * 获取政策详情
 * @param {*} data
 * id 政策id
 * @returns
 */
export const getPolicyDetail = (data) => {
	return request({
		url: '/achv/policy/getDetail',
		method: 'post',
		data,
	});
};

/**
 * 审核操作
 * @param {object} params 专家审核对象
 * @param {number} params.id id
 * @param {string} params.reason 不通过原因
 * @param {number} params.status 审核状态:1 审核中 2 审核不通过  3 审核通过
 * @returns
 */
export const auditPolicy = (data = {}) => {
	return request({
		url: '/achv/policy/audit',
		method: 'post',
		data,
	});
};

/**
 * 更新排序
 * @param {object} params 更新排序dto
 * @param {number} params.id
 * @param {number} params.rankingNum
 * @param {array} params.saveDtoList
 * @returns
 */
export const updateRankingNum = (data = {}) => {
	return request({
		url: '/achv/policy/updateRankingNum',
		method: 'post',
		data,
	});
};

/**
 * 更新显示状态
 * @param {object} params 显示状态对象
 * @param {number} params.id id
 * @param {number} params.showStatus 展示状态： 0不展示 1展示
 * @returns
 */
export const updateShowStatus = (data = {}) => {
	return request({
		url: '/achv/policy/updateShowStatus',
		method: 'post',
		data,
	});
};
