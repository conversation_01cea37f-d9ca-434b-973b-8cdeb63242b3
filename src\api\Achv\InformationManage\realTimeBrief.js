import { request } from '@/utils/axios';

/**
 * 获取政策 分页
 * @param {*} data
 * id           政策id
 * pageSize     每页数量
 * pageNum      页码
 * @returns
 */
export const pageNewsletter = (data) => {
	return request({
		url: '/achv/newsletter/page',
		method: 'post',
		data,
	});
};

/**
 * 新增政策
 * @param {*} data
 * title           政策标题
 * releaseOrg      发布机构部门
 * releaseTime     发布时间
 * content         政策内容
 * @returns
 */
export const newsletterAdd = (data) => {
	return request({
		url: '/achv/newsletter/add',
		method: 'post',
		data,
	});
};

/**
 * 更新政策
 * @param {*} data
 * id           	  主键id
 * title            政策标题
 * guidanceOrg      指导单位
 * hostOrg          主办单位
 * undertakeOrg     承办单位
 * venue            政策地点
 * introduce        政策介绍
 * startTime        政策开始时间
 * endTime          结束开始时间
 * coverImageUrl    封面图片地址
 * @returns
 */
export const newsletterUpdate = (data) => {
	return request({
		url: '/achv/newsletter/update',
		method: 'post',
		data,
	});
};

/**
 * 删除政策
 * @param {*} data
 * ids 删除 政策id数组
 * @returns
 */
export const newsletterDel = (data) => {
	return request({
		url: '/achv/newsletter/batchDel',
		method: 'post',
		data,
	});
};

/**
 * 获取政策详情
 * @param {*} data
 * id 政策id
 * @returns
 */
export const getNewsletterDetail = (data) => {
	return request({
		url: '/achv/newsletter/getDetail',
		method: 'post',
		data,
	});
};

/**
 * url内容总结
 * @param {object} params url总结
 * @param {string} params.url url
 * @returns
 */
export const urlSummary = (data) => {
	return request({
		url: '/achv/front/urlSummary',
		method: 'post',
		data,
		showLoading: false,
	});
};
