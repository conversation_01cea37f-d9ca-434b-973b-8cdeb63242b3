import { request } from '@/utils/axios';

/**
 * 预判企业分页
 * @param {object} params 预判需求分页查询参数
 * @param {number} params.pageNum 页码
 * @param {number} params.pageSize 每页记录数
 * @param {string} params.enterpriseName 企业名称
 * @param {number} params.auditStatus 审核状态(0:待审核,1:审核通过,2:审核不通过)
 * @param {number} params.source 来源(0:导入,1:大模型,2:手动添加)
 * @param {number} params.auditPersonId 审核人id
 * @param {string} params.auditPerson 审核人名称
 * @param {object} params.createTimeStart 创建开始时间
 * @param {object} params.createTimeEnd 创建结束时间
 * @returns
 */
export function pagePredictEnterprise(data = {}, option = {}) {
	return request({
		url: `/achv/enterprise/predict/page`,
		method: 'POST',
		data,
		...option,
	});
}

/**
 * 预判企业详情
 * @param {object} params 预判企业查询条件
 * @param {number} params.id 主键id
 * @param {array} params.ids 主键id列表
 * @param {string} params.name 企业名称
 * @param {number} params.auditStatus 审核状态(0:待审核,1:审核通过,2:审核不通过)
 * @returns
 */
export function detailPredictEnterprise(data = {}, option = {}) {
	return request({
		url: `/achv/enterprise/predict/detail`,
		method: 'POST',
		data,
		...option,
	});
}

/**
 * 预判企业添加
 * @param {object} params 预判企业
 * @param {number} params.id 主键id(更新用)
 * @param {string} params.enterpriseName 企业名称
 * @param {string} params.introduction 简介
 * @param {string} params.mainBusiness 主营业务
 * @param {string} params.coreProduct 核心产品
 * @param {string} params.industryChainNode 产业链及节点
 * @param {string} params.predictRequirement 预判需求
 * @param {string} params.technologicalRoute 技术路线
 * @param {number} params.source 数据来源 0：导入 1：大模型 2:手动添加
 * @param {number} params.auditStatus 审核状态 0：待审核 1：审核通过 2：审核不通过
 * @returns
 */
export function addPredictEnterprise(data = {}, option = {}) {
	return request({
		url: `/achv/enterprise/predict/add`,
		method: 'POST',
		data,
		...option,
	});
}

/**
 * 预判企业修改
 * @param {object} params 预判企业
 * @param {number} params.id 主键id(更新用)
 * @param {string} params.enterpriseName 企业名称
 * @param {string} params.introduction 简介
 * @param {string} params.mainBusiness 主营业务
 * @param {string} params.coreProduct 核心产品
 * @param {string} params.industryChainNode 产业链及节点
 * @param {string} params.predictRequirement 预判需求
 * @param {string} params.technologicalRoute 技术路线
 * @param {number} params.source 数据来源 0：导入 1：大模型 2:手动添加
 * @param {number} params.auditStatus 审核状态 0：待审核 1：审核通过 2：审核不通过
 * @returns
 */
export function updatePredictEnterprise(data = {}, option = {}) {
	return request({
		url: `/achv/enterprise/predict/update`,
		method: 'POST',
		data,
		...option,
	});
}

/**
 * 预判企业删除
 * @param {object} params 预判企业查询条件
 * @param {number} params.id 主键id
 * @param {array} params.ids 主键id列表
 * @param {string} params.name 企业名称
 * @param {number} params.auditStatus 审核状态(0:待审核,1:审核通过,2:审核不通过)
 * @returns
 */
export function deletePredictEnterprise(data = {}, option = {}) {
	return request({
		url: `/achv/enterprise/predict/delete`,
		method: 'POST',
		data,
		...option,
	});
}

/**
 * 预判企业统计
 * @returns
 */
export function statisticPredictEnterprise(data = {}, option = {}) {
	return request({
		url: `/achv/enterprise/predict/statistic`,
		method: 'POST',
		data,
		...option,
	});
}

/**
 * 预判企业审核
 * @param {object} params 预判企业审核参数
 * @param {number} params.id 主键id
 * @param {number} params.auditStatus 审核状态(0:待审核,1:审核通过,2:审核不通过)
 * @returns
 */
export function auditPredictEnterprise(data = {}, option = {}) {
	return request({
		url: `/achv/enterprise/predict/audit`,
		method: 'POST',
		data,
		...option,
	});
}
