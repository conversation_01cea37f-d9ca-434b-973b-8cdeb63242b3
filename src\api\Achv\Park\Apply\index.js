import { request } from '@/utils/axios';

/**
 * 分页查询申请
 * @param {object} params 活动报名表
 * @param {number} params.pageNum 页码
 * @param {number} params.pageSize 每页记录数
 * @param {number} params.id
 * @param {array} params.ids
 * @param {number} params.parkId 园区id主键
 * @param {string} params.parkName 园区名
 * @param {number} params.followStatus 跟进状态：1待联系 2已联系 3已签约 4不合适
 * @param {string} params.openId
 * @param {number} params.tenantId
 * @returns
 */
export function pageParkApply(data = {}, option = {}) {
	return request({
		url: `/achv/park/apply/pageParkApply`,
		method: 'POST',
		data,
		...option,
	});
}

/**
 * 查询详情
 * @param {object} params 活动报名表
 * @param {number} params.pageNum 页码
 * @param {number} params.pageSize 每页记录数
 * @param {number} params.id
 * @param {array} params.ids
 * @param {number} params.parkId 园区id主键
 * @param {string} params.parkName 园区名
 * @param {number} params.followStatus 跟进状态：1待联系 2已联系 3已签约 4不合适
 * @param {string} params.openId
 * @param {number} params.tenantId
 * @returns
 */
export function getParkApplyDetail(data = {}, option = {}) {
	return request({
		url: `/achv/park/apply/getParkApplyDetail`,
		method: 'POST',
		data,
		...option,
	});
}

/**
 * 批量删除
 * @param {array} params integer
 * @returns
 */
export function delByIds(data = {}, option = {}) {
	return request({
		url: `/achv/park/apply/delByIds`,
		method: 'POST',
		data,
		...option,
	});
}

/**
 * 更新跟进状态
 * @param {object} params 活动报名表
 * @param {number} params.pageNum 页码
 * @param {number} params.pageSize 每页记录数
 * @param {number} params.id
 * @param {array} params.ids
 * @param {number} params.parkId 园区id主键
 * @param {string} params.parkName 园区名
 * @param {number} params.followStatus 跟进状态：1待联系 2已联系 3已签约 4不合适
 * @param {string} params.openId
 * @param {number} params.tenantId
 * @returns
 */
export function updateFollowStatus(data = {}, option = {}) {
	return request({
		url: `/achv/park/apply/updateFollowStatus`,
		method: 'POST',
		data,
		...option,
	});
}
