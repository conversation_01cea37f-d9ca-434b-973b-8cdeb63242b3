import { request } from '@/utils/axios';

/**
 * 分页查询全运会信息信息表参加
 * @param {object} params 会运会信息查询条件
 * @param {number} params.pageNum 页码
 * @param {number} params.pageSize 每页记录数
 * @param {number} params.id
 * @param {string} params.name
 * @param {number} params.tenantId
 * @param {string} params.openId
 * @param {array} params.ids
 * @returns
 */
export function pageNationalGamesInfoCollect(data = {}, option = {}) {
	return request({
		url: `/achv/nationalGames/page`,
		method: 'POST',
		data,
		...option,
	});
}

/**
 * 查询全运会信息信息表参加
 * @param {object} params 会运会信息查询条件
 * @param {number} params.pageNum 页码
 * @param {number} params.pageSize 每页记录数
 * @param {number} params.id
 * @param {string} params.name
 * @param {number} params.tenantId
 * @param {string} params.openId
 * @param {array} params.ids
 * @returns
 */
export function queryNationalGamesInfoCollect(data = {}, option = {}) {
	return request({
		url: `/achv/nationalGames/query`,
		method: 'POST',
		data,
		...option,
	});
}

/**
 * 新增全运会信息信息表参加
 * @param {object} params 全运会信息收集
 * @param {number} params.id
 * @param {string} params.name
 * @param {string} params.phone
 * @param {string} params.company
 * @param {string} params.industrySector
 * @param {string} params.productName
 * @param {string} params.productDesc
 * @param {string} params.usageScenario
 * @param {string} params.openId
 * @returns
 */
export function addNationalGamesInfoCollect(data = {}, option = {}) {
	return request({
		url: `/achv/nationalGames/add`,
		method: 'POST',
		data,
		...option,
	});
}

/**
 * 修改全运会信息信息表参加
 * @param {object} params 全运会信息收集
 * @param {number} params.id
 * @param {string} params.name
 * @param {string} params.phone
 * @param {string} params.company
 * @param {string} params.industrySector
 * @param {string} params.productName
 * @param {string} params.productDesc
 * @param {string} params.usageScenario
 * @param {string} params.openId
 * @returns
 */
export function updateNationalGamesInfoCollect(data = {}, option = {}) {
	return request({
		url: `/achv/nationalGames/update`,
		method: 'POST',
		data,
		...option,
	});
}

/**
 * 批量删除全运会信息信息表参加
 * @param {object} params 会运会信息查询条件
 * @param {number} params.pageNum 页码
 * @param {number} params.pageSize 每页记录数
 * @param {number} params.id
 * @param {string} params.name
 * @param {number} params.tenantId
 * @param {string} params.openId
 * @param {array} params.ids
 * @returns
 */
export function batchDel(data = {}, option = {}) {
	return request({
		url: `/achv/nationalGames/batchDel`,
		method: 'POST',
		data,
		...option,
	});
}

/**
 * 全运会信息导出
 * @param {object} params 会运会信息查询条件
 * @param {number} params.pageNum 页码
 * @param {number} params.pageSize 每页记录数
 * @param {number} params.id
 * @param {string} params.name
 * @param {number} params.tenantId
 * @param {string} params.openId
 * @param {array} params.ids
 * @returns
 */
export function exportNationalGamesInfoCollect(data = {}, option = {}) {
	return request({
		url: `/achv/nationalGames/exportNationalGamesInfoCollect`,
		method: 'POST',
		data,
		responseType: 'blob',
		...option,
	});
}
