import { request } from '@/utils/axios';

/**
 * 分页查询合伙人信息表参加
 * @param {object} params 合伙人信息查询条件
 * @param {number} params.pageNum 页码
 * @param {number} params.pageSize 每页记录数
 * @param {number} params.id
 * @param {string} params.name
 * @param {string} params.openId
 * @param {array} params.ids
 * @returns
 */

export function pagePartnerInfo(data = {}, option = {}) {
	return request({
		url: `/achv/partnerInfo/page`,
		method: 'POST',
		data,
		...option,
	});
}

/**
 * 新增合伙人信息表参加
 * @param {object} params 合伙人信息表
 * @param {number} params.id
 * @param {string} params.name
 * @param {string} params.phone
 * @param {string} params.position
 * @param {string} params.company
 * @param {string} params.openId
 * @param {string} params.cooperationContent
 * @returns
 */

export function addPartnerInfo(data = {}, option = {}) {
	return request({
		url: `/achv/partnerInfo/add`,
		method: 'POST',
		data,
		...option,
	});
}

/**
 * 修改合伙人信息表参加
 * @param {object} params 合伙人信息表
 * @param {number} params.id
 * @param {string} params.name
 * @param {string} params.phone
 * @param {string} params.position
 * @param {string} params.company
 * @param {string} params.openId
 * @param {string} params.cooperationContent
 * @returns
 */

export function updatePartnerInfo(data = {}, option = {}) {
	return request({
		url: `/achv/partnerInfo/update`,
		method: 'POST',
		data,
		...option,
	});
}

/**
 * 查询合伙人信息表参加
 * @param {object} params 合伙人信息查询条件
 * @param {number} params.pageNum 页码
 * @param {number} params.pageSize 每页记录数
 * @param {number} params.id
 * @param {string} params.name
 * @param {string} params.openId
 * @param {array} params.ids
 * @returns
 */

export function queryPartnerInfo(data = {}, option = {}) {
	return request({
		url: `/achv/partnerInfo/query`,
		method: 'POST',
		data,
		...option,
	});
}

/**
 * 批量删除合伙人信息表参加
 * @param {object} params 合伙人信息查询条件
 * @param {number} params.pageNum 页码
 * @param {number} params.pageSize 每页记录数
 * @param {number} params.id
 * @param {string} params.name
 * @param {string} params.openId
 * @param {array} params.ids
 * @returns
 */

export function batchDel(data = {}, option = {}) {
	return request({
		url: `/achv/partnerInfo/batchDel`,
		method: 'POST',
		data,
		...option,
	});
}
