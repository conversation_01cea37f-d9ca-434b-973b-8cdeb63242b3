import { request } from '@/utils/axios';

/**
 * 派单认领审核
 * @param {object} params 派单表
 * @param {number} params.id 主键
 * @param {number} params.claimStatus 认领状态(1:待认领,2:拒绝认领,3:已认领 4 待审核 5 审核不通过 6 等待联系 )
 * @param {string} params.refuseReason 拒绝理由
 * @returns
 */
export function auditSendOrder(params = {}, option = {}) {
	return request({
		url: `/achv/sendOrder/audit`,
		method: 'POST',
		data: params,
		...option,
	});
}
