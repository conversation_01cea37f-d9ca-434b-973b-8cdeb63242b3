import { request } from '@/utils/axios';

/**
 * 分页
 * @param {object} params 科转号大咖咨询条件查询
 * @param {number} params.pageNum 页码
 * @param {number} params.pageSize 每页记录数
 * @param {number} params.ttChannelsId 科转号id
 * @param {number} params.tenantId 租户id
 * @param {number} params.id
 * @param {array} params.ids
 * @param {number} params.consultationType 大咖类型: 1.技术专家 2.投资专家 3.法律专家 4.知识产权专家
 * @param {array} params.provinceCodes 省份code
 * @param {array} params.cityCodes 城市code
 * @param {number} params.categoryId 类目领域id
 * @param {number} params.showStatus 显示状态： 0否 1是
 * @returns
 */
export function pageConsultation(params = {}, option = {}) {
	return request({
		url: `/achv/ttChannelsConsultation/page`,
		method: 'POST',
		data: params,
		...option,
	});
}

/**
 * 详情
 * @param {object} params 科转号大咖咨询条件查询
 * @param {number} params.pageNum 页码
 * @param {number} params.pageSize 每页记录数
 * @param {number} params.ttChannelsId 科转号id
 * @param {number} params.tenantId 租户id
 * @param {number} params.id
 * @param {array} params.ids
 * @param {number} params.consultationType 大咖类型: 1.技术专家 2.投资专家 3.法律专家 4.知识产权专家
 * @param {array} params.provinceCodes 省份code
 * @param {array} params.cityCodes 城市code
 * @param {number} params.categoryId 类目领域id
 * @param {number} params.showStatus 显示状态： 0否 1是
 * @returns
 */
export function getConsultation(params = {}, option = {}) {
	return request({
		url: `/achv/ttChannelsConsultation/detail`,
		method: 'POST',
		data: params,
		...option,
	});
}

/**
 * 新增
 * @param {object} params 科转号大咖咨询保存dto
 * @param {number} params.id
 * @param {number} params.ttChannelsId 科转号id
 * @param {number} params.consultationType 大咖类型: 1.技术专家 2.投资专家 3.法律专家 4.知识产权专家
 * @param {number} params.baseNum  基数
 * @param {string} params.avatarUrl 头像地址
 * @param {string} params.researchDirection 研究领域
 * @param {string} params.consultationContent 咨询内容
 * @param {number} params.rankingNum 排序
 * @param {string} params.name 名称
 * @param {string} params.provinceCode 省份code
 * @param {string} params.cityCode 城市code
 * @param {string} params.companyName 公司名
 * @param {string} params.positionName 职位
 * @param {array} params.industryLabelIds 行业标签id
 * @param {array} params.ids
 * @returns
 */
export function addConsultation(params = {}, option = {}) {
	return request({
		url: `/achv/ttChannelsConsultation/add`,
		method: 'POST',
		data: params,
		...option,
	});
}

/**
 * 修改
 * @param {object} params 科转号大咖咨询保存dto
 * @param {number} params.id
 * @param {number} params.ttChannelsId 科转号id
 * @param {number} params.consultationType 大咖类型: 1.技术专家 2.投资专家 3.法律专家 4.知识产权专家
 * @param {number} params.baseNum  基数
 * @param {string} params.avatarUrl 头像地址
 * @param {string} params.researchDirection 研究领域
 * @param {string} params.consultationContent 咨询内容
 * @param {number} params.rankingNum 排序
 * @param {string} params.name 名称
 * @param {string} params.provinceCode 省份code
 * @param {string} params.cityCode 城市code
 * @param {string} params.companyName 公司名
 * @param {string} params.positionName 职位
 * @param {array} params.industryLabelIds 行业标签id
 * @param {array} params.ids
 * @returns
 */
export function updateConsultation(params = {}, option = {}) {
	return request({
		url: `/achv/ttChannelsConsultation/update`,
		method: 'POST',
		data: params,
		...option,
	});
}

/**
 * 审核
 * @param {object} params 专家审核对象
 * @param {number} params.id id
 * @param {string} params.reason 不通过原因
 * @param {number} params.status 审核状态:1 审核中 2 审核不通过  3 审核通过
 * @returns
 */
export function auditConsultation(params = {}, option = {}) {
	return request({
		url: `/achv/ttChannelsConsultation/audit`,
		method: 'POST',
		data: params,
		...option,
	});
}

/**
 * 批量删除科】
 * @param {object} params 科转号大咖咨询保存dto
 * @param {number} params.id
 * @param {number} params.ttChannelsId 科转号id
 * @param {number} params.consultationType 大咖类型: 1.技术专家 2.投资专家 3.法律专家 4.知识产权专家
 * @param {number} params.baseNum  基数
 * @param {string} params.avatarUrl 头像地址
 * @param {string} params.researchDirection 研究领域
 * @param {string} params.consultationContent 咨询内容
 * @param {number} params.rankingNum 排序
 * @param {string} params.name 名称
 * @param {string} params.provinceCode 省份code
 * @param {string} params.cityCode 城市code
 * @param {string} params.companyName 公司名
 * @param {string} params.positionName 职位
 * @param {array} params.industryLabelIds 行业标签id
 * @param {array} params.ids
 * @returns
 */
export function batchDel(params = {}, option = {}) {
	return request({
		url: `/achv/ttChannelsConsultation/batchDel`,
		method: 'POST',
		data: params,
		...option,
	});
}

/**
 * 更新排序
 * @param {object} params 更新排序dto
 * @param {number} params.id
 * @param {number} params.rankingNum
 * @param {array} params.saveDtoList
 * @returns
 */
export function updateRankingNum(params = {}, option = {}) {
	return request({
		url: `/achv/ttChannelsConsultation/updateRankingNum`,
		method: 'POST',
		data: params,
		...option,
	});
}

/**
 * 更新推荐
 * @param {object} params 推荐状态对象
 * @param {number} params.id id
 * @param {number} params.recommendStatus 推荐状态： 0否 1是
 * @returns
 */
export function updateRecommendStatus(params = {}, option = {}) {
	return request({
		url: `/achv/ttChannelsConsultation/updateRecommendStatus`,
		method: 'POST',
		data: params,
		...option,
	});
}
