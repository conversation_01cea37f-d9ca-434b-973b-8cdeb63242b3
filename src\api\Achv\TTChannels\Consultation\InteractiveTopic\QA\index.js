import { request } from '@/utils/axios';

// 分页查询互动专题问题
export function getTTInteractiveTopicQaPage(params = {}, option = {}) {
	return request({
		url: `/achv/interactiveTopic/getTTInteractiveTopicQaPage`,
		method: 'POST',
		data: params,
		...option,
	});
}

// 查询专题问题详情
export function getTTInteractiveTopicQa(params = {}, option = {}) {
	return request({
		url: `/achv/interactiveTopic/getTTInteractiveTopicQa`,
		method: 'POST',
		data: params,
		...option,
	});
}

/**
 * 新增互动专题问题
 * @param {object} params 互动专题管理表保存dto
 * @param {number} params.id
 * @param {string} params.content 内容
 * @param {number} params.auditStatus 审核状态:1 审核中 2 审核不通过  3 审核通过
 * @param {number} params.rankingNum 排序
 * @param {number} params.interactiveTopicId 互动专题id
 * @param {number} params.parentId 问题id,只有回答的时候需要传
 * @param {number} params.featuredStatus 精选状态： 0 否 1是
 * @param {number} params.replyStatus 回复状态： 0 否 1是
 * @param {number} params.releasePlatform 发布平台：1 后台管理 2小程序 3 财经app
 * @param {string} params.userName 用户名称
 * @param {string} params.avatarUrl 头像地址
 * @param {array} params.replyList 回复list
 * @returns
 */
export function saveTTInteractiveTopicQa(params = {}, option = {}) {
	return request({
		url: `/achv/interactiveTopic/saveTTInteractiveTopicQa`,
		method: 'POST',
		data: params,
		...option,
	});
}

/**
 * 修改互动专题问题
 * @param {object} params 互动专题管理表保存dto
 * @param {number} params.id
 * @param {string} params.content 内容
 * @param {number} params.auditStatus 审核状态:1 审核中 2 审核不通过  3 审核通过
 * @param {number} params.rankingNum 排序
 * @param {number} params.interactiveTopicId 互动专题id
 * @param {number} params.parentId 问题id,只有回答的时候需要传
 * @param {number} params.featuredStatus 精选状态： 0 否 1是
 * @param {number} params.replyStatus 回复状态： 0 否 1是
 * @param {number} params.releasePlatform 发布平台：1 后台管理 2小程序 3 财经app
 * @param {string} params.userName 用户名称
 * @param {string} params.avatarUrl 头像地址
 * @param {array} params.replyList 回复list
 * @returns
 */
export function updateTTInteractiveTopicQa(params = {}, option = {}) {
	return request({
		url: `/achv/interactiveTopic/updateTTInteractiveTopicQa`,
		method: 'POST',
		data: params,
		...option,
	});
}

/**
 * 批量删除问题
 * @param {object} params 互动专题问答件查询
 * @param {number} params.pageNum 页码
 * @param {number} params.pageSize 每页记录数
 * @param {number} params.id
 * @param {number} params.auditStatus 审核状态:1 审核中 2 审核不通过  3 审核通过
 * @param {number} params.interactiveTopicId 互动专题id
 * @param {number} params.parentId 问题id
 * @param {number} params.featuredStatus 精选状态： 0 否 1是
 * @param {number} params.replyStatus 回复状态： 0 否 1是
 * @param {number} params.releasePlatform 发布平台：1 后台管理 2小程序 3 财经app
 * @param {number} params.qaType
 * @param {array} params.ids
 * @returns
 */
export function batchDelQa(params = {}, option = {}) {
	return request({
		url: `/achv/interactiveTopic/batchDelQa`,
		method: 'POST',
		data: params,
		...option,
	});
}

/**
 * 更新精选状态
 * @param {object} params 互动专题管理表保存dto
 * @param {number} params.id
 * @param {string} params.content 内容
 * @param {number} params.auditStatus 审核状态:1 审核中 2 审核不通过  3 审核通过
 * @param {number} params.rankingNum 排序
 * @param {number} params.interactiveTopicId 互动专题id
 * @param {number} params.parentId 问题id,只有回答的时候需要传
 * @param {number} params.featuredStatus 精选状态： 0 否 1是
 * @param {number} params.replyStatus 回复状态： 0 否 1是
 * @param {number} params.releasePlatform 发布平台：1 后台管理 2小程序 3 财经app
 * @param {string} params.userName 用户名称
 * @param {string} params.avatarUrl 头像地址
 * @param {array} params.replyList 回复list
 * @returns
 */
export function updateFeaturedStatus(params = {}, option = {}) {
	return request({
		url: `/achv/interactiveTopic/updateFeaturedStatus`,
		method: 'POST',
		data: params,
		...option,
	});
}

/**
 * 更新专题问答排序
 * @param {object} params 更新排序dto
 * @param {number} params.id
 * @param {number} params.rankingNum
 * @param {array} params.saveDtoList
 * @returns
 */
export function updateRankingNumToQa(params = {}, option = {}) {
	return request({
		url: `/achv/interactiveTopic/updateRankingNumToQa`,
		method: 'POST',
		data: params,
		...option,
	});
}

/**
 * 互动专题问题审核
 * @param {object} params 专家审核对象
 * @param {array} params.ids
 * @param {number} params.id id
 * @param {string} params.reason 不通过原因
 * @param {number} params.status 审核状态:1 审核中 2 审核不通过  3 审核通过
 * @returns
 */
export function auditQa(params = {}, option = {}) {
	return request({
		url: `/achv/interactiveTopic/auditQa`,
		method: 'POST',
		data: params,
		...option,
	});
}
