import { request } from '@/utils/axios';

/**
 * 分页查询
 * @param {object} params 互动专题管理查询dto
 * @param {number} params.pageNum 页码
 * @param {number} params.pageSize 每页记录数
 * @param {number} params.id
 * @param {number} params.auditStatus 审核状态:1 审核中 2 审核不通过  3 审核通过
 * @param {number} params.showStatus 显示状态：0 否 1 是
 * @param {string} params.name
 * @param {number} params.tenantId
 * @param {array} params.ids
 * @returns
 */
export function getTTInteractiveTopicPage(params = {}, option = {}) {
	return request({
		url: `/achv/interactiveTopic/getTTInteractiveTopicPage`,
		method: 'POST',
		data: params,
		...option,
	});
}

/**
 * 获取统计数量
 * @returns
 */
export function getTTIntegetStatisticsractiveTopic(params = {}, option = {}) {
	return request({
		url: `/achv/interactiveTopic/getStatistics`,
		method: 'POST',
		data: params,
		...option,
	});
}

/**
 * 获取互动专题详情
 * @param {object} params 互动专题管理查询dto
 * @param {number} params.pageNum 页码
 * @param {number} params.pageSize 每页记录数
 * @param {number} params.id
 * @param {number} params.auditStatus 审核状态:1 审核中 2 审核不通过  3 审核通过
 * @param {number} params.showStatus 显示状态：0 否 1 是
 * @param {string} params.name
 * @param {number} params.tenantId
 * @param {array} params.ids
 * @returns
 */
export function getTTInteractiveTopic(params = {}, option = {}) {
	return request({
		url: `/achv/interactiveTopic/getTTInteractiveTopic`,
		method: 'POST',
		data: params,
		...option,
	});
}

/**
 * 新增互动专题
 * @param {object} params 互动专题管理表保存dto
 * @param {number} params.id
 * @param {string} params.name 专题名称
 * @param {object} params.startTime 互动开始时间,格式：yyyy-MM-dd HH:mm:ss
 * @param {object} params.endTime 互动结束时间,格式：yyyy-MM-dd HH:mm:ss
 * @param {string} params.content 互动内容
 * @param {string} params.topicCoverUrl 专题封面地址
 * @param {string} params.sharePosterUrl 分享海报地址
 * @param {string} params.friendShareCoverUrl 好友分享封面地址
 * @param {string} params.timelineShareCoverUrl 朋友圈分享封面地址
 * @param {number} params.auditStatus 审核状态:1 审核中 2 审核不通过  3 审核通过
 * @param {number} params.showStatus 显示状态：0 否 1 是
 * @param {number} params.rankingNum 排序
 * @param {array} params.ttChannelsIds 关联科转号
 * @param {string} params.shareDesc
 * @param {string} params.bannerUrl
 * @returns
 */
export function saveTTInteractiveTopic(params = {}, option = {}) {
	return request({
		url: `/achv/interactiveTopic/save`,
		method: 'POST',
		data: params,
		...option,
	});
}

/**
 * 修改互动专题
 * @param {object} params 互动专题管理表保存dto
 * @param {number} params.id
 * @param {string} params.name 专题名称
 * @param {object} params.startTime 互动开始时间,格式：yyyy-MM-dd HH:mm:ss
 * @param {object} params.endTime 互动结束时间,格式：yyyy-MM-dd HH:mm:ss
 * @param {string} params.content 互动内容
 * @param {string} params.topicCoverUrl 专题封面地址
 * @param {string} params.sharePosterUrl 分享海报地址
 * @param {string} params.friendShareCoverUrl 好友分享封面地址
 * @param {string} params.timelineShareCoverUrl 朋友圈分享封面地址
 * @param {number} params.auditStatus 审核状态:1 审核中 2 审核不通过  3 审核通过
 * @param {number} params.showStatus 显示状态：0 否 1 是
 * @param {number} params.rankingNum 排序
 * @param {array} params.ttChannelsIds 关联科转号
 * @param {string} params.shareDesc
 * @param {string} params.bannerUrl
 * @returns
 */
export function updateTTInteractiveTopic(params = {}, option = {}) {
	return request({
		url: `/achv/interactiveTopic/update`,
		method: 'POST',
		data: params,
		...option,
	});
}

/**
 * 批量删除互动专题
 * @param {object} params 互动专题管理查询dto
 * @param {number} params.pageNum 页码
 * @param {number} params.pageSize 每页记录数
 * @param {number} params.id
 * @param {number} params.auditStatus 审核状态:1 审核中 2 审核不通过  3 审核通过
 * @param {number} params.showStatus 显示状态：0 否 1 是
 * @param {string} params.name
 * @param {number} params.tenantId
 * @param {array} params.ids
 * @returns
 */
export function batchDelTTInteractiveTopic(params = {}, option = {}) {
	return request({
		url: `/achv/interactiveTopic/batchDel`,
		method: 'POST',
		data: params,
		...option,
	});
}

/**
 * 互动专题审核
 * @param {object} params 专家审核对象
 * @param {array} params.ids
 * @param {number} params.id id
 * @param {string} params.reason 不通过原因
 * @param {number} params.status 审核状态:1 审核中 2 审核不通过  3 审核通过
 * @returns
 */
export function auditTTInteractiveTopic(params = {}, option = {}) {
	return request({
		url: `/achv/interactiveTopic/audit`,
		method: 'POST',
		data: params,
		...option,
	});
}

/**
 * 更新排序
 * @param {object} params 更新排序dto
 * @param {number} params.id
 * @param {number} params.rankingNum
 * @param {array} params.saveDtoList
 * @returns
 */
export function updateRankingNum(params = {}, option = {}) {
	return request({
		url: `/achv/interactiveTopic/updateRankingNum`,
		method: 'POST',
		data: params,
		...option,
	});
}

/**
 * 更新推荐
 * @param {object} params 推荐状态对象
 * @param {number} params.id id
 * @param {number} params.recommendStatus 推荐状态： 0否 1是
 * @returns
 */
export function updateShowStatus(params = {}, option = {}) {
	return request({
		url: `/achv/interactiveTopic/updateShowStatus`,
		method: 'POST',
		data: params,
		...option,
	});
}
