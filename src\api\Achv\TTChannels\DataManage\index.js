import { request } from '@/utils/axios';

/**
 * 科转号导出
 * @param {object} params 科转号条件查询
 * @param {number} params.pageNum 页码
 * @param {number} params.pageSize 每页记录数
 * @param {number} params.userId
 * @param {number} params.tenantId 租户id
 * @param {number} params.id
 * @param {array} params.ids
 * @param {number} params.channelType 科转号类型: 1.科研机构 2.技术经理人 3.科创企业 4.行业专家 5.投资机构 6.国有企业 7.政府部门 8行业协会
 * @param {string} params.sortValue 排序值(传)：desc/asc
 * @param {string} params.sortKey 排序字段,传返回字段名称如：创建时间-createTime 排序号-rankingNum 审核时间-auditTime 先审核状态后审核时间-auditStatusAndAuditTime
 * @param {number} params.auditStatus 审核状态:1 审核中 2 审核不通过  3 审核通过
 * @param {array} params.auditStatusList 审核状态:1 审核中 2 审核不通过  3 审核通过
 * @param {number} params.joinPlatform 入驻平台类型：1后台管理 2小程序 3PC端
 * @param {number} params.isAuth 是否认证：0 否 1 是
 * @param {string} params.accountName 帐号名称
 * @param {string} params.auditStartTime 审核开始时间：yyyy-MM-dd
 * @param {string} params.auditEndTime 审核结束时间：yyyy-MM-dd
 * @param {array} params.provinceCodes 省份code
 * @param {array} params.cityCodes 城市code
 * @param {number} params.categoryId 类目领域id
 * @param {number} params.inviteTtChannelsId 邀请科转号id
 * @param {number} params.personnelType
 * @param {string} params.inviteCode
 * @param {number} params.recommendStatus
 * @param {string} params.startTime 开始时间：yyyy-MM-dd
 * @param {string} params.endTime 结束时间：yyyy-MM-dd
 * @returns
 */
export function exportTtChannels(params = {}, option = {}) {
	return request({
		url: `/achv/ttChannels/exportTtChannels`,
		method: 'POST',
		data: params,
		responseType: 'blob',
		...option,
	});
}

/**
 * 动态圈导出
 * @param {object} params 科转号用户行为dto
 * @param {number} params.pageNum 页码
 * @param {number} params.pageSize 每页记录数
 * @param {number} params.id
 * @param {array} params.ids ids
 * @param {number} params.tenantId 租户id
 * @param {number} params.ttChannelsId 科转号id
 * @param {array} params.ttChannelsIds 批量科转号id
 * @param {number} params.businessId 关联业务id
 * @param {array} params.businessIds 批量关联业务id
 * @param {number} params.timeLineType 关联业务类型：1需求 2成果 3图文 4视频 5活动 6发起拼团  7参加拼团
 * @param {number} params.auditStatus 审核状态:1 审核中 2 审核不通过  3 审核通过
 * @param {array} params.auditStatusList 审核状态:1 审核中 2 审核不通过  3 审核通过
 * @param {array} params.timeLineTypes 关联业务类型：1需求 2成果 3图文 4视频 5活动 6发起拼团  7参加拼团
 * @param {number} params.userId 用户id
 * @param {number} params.isFollow 是否查询关注的科转号动态: 0 否  1是
 * @param {number} params.showStatus 展示状态： 0不展示 1展示
 * @param {string} params.orderSql
 * @param {number} params.recommendStatus
 * @param {number} params.orderType 排序方式: 0 默认 1 时间最新 2 流量最高 3 人数最多 4 按businessIds排序
 * @param {string} params.startTime 开始时间：yyyy-MM-dd
 * @param {string} params.endTime 结束时间：yyyy-MM-dd
 * @returns
 */
export function exportTTTimeLine(params = {}, option = {}) {
	return request({
		url: `/achv/ttTimeLine/exportTTTimeLine`,
		method: 'POST',
		data: params,
		responseType: 'blob',
		...option,
	});
}

/**
 * 合伙人导出
 * @param {object} params 合伙人信息查询条件
 * @param {number} params.pageNum 页码
 * @param {number} params.pageSize 每页记录数
 * @param {number} params.id
 * @param {string} params.name
 * @param {number} params.tenantId
 * @param {string} params.openId
 * @param {array} params.ids
 * @returns
 */
export function exportPartnerInfo(params = {}, option = {}) {
	return request({
		url: `/achv/partnerInfo/exportPartnerInfo`,
		method: 'POST',
		data: params,
		responseType: 'blob',
		...option,
	});
}

/**
 * 白名单删除
 * @param {object} params 创新引擎白名单
 * @param {number} params.type 类型(1:知识产权,2:AI报告)
 * @param {string} params.phone 手机号
 * @returns
 */
export function innovationWhitelistPhoneDelete(params = {}, option = {}) {
	return request({
		url: `/achv/innovationWhitelist/phoneDelete`,
		method: 'POST',
		data: params,
		...option,
	});
}

/**
 * 白名单存在
 * @param {object} params 创新引擎白名单
 * @param {number} params.type 类型(1:知识产权,2:AI报告)
 * @param {string} params.phone 手机号
 * @returns
 */
export function innovationWhitelistPhoneExists(params = {}, option = {}) {
	return request({
		url: `/achv/innovationWhitelist/phoneExists`,
		method: 'POST',
		data: params,
		...option,
	});
}

/**
 * 白名单保存
 * @param {object} params 创新引擎白名单
 * @param {number} params.type 类型(1:知识产权,2:AI报告)
 * @param {string} params.phone 手机号
 * @returns
 */
export function innovationWhitelistPhoneSave(params = {}, option = {}) {
	return request({
		url: `/achv/innovationWhitelist/phoneSave`,
		method: 'POST',
		data: params,
		...option,
	});
}
