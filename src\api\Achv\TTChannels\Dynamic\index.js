import { request } from '@/utils/axios';

/**
 * 动态圈分页查询
 * @param {object} params 科转号用户行为dto
 * @param {number} params.pageNum 页码
 * @param {number} params.pageSize 每页记录数
 * @param {number} params.ttChannelsId 科转号id
 * @param {array} params.ttChannelsIds 批量科转号id
 * @param {number} params.businessId 关联业务id
 * @param {array} params.businessIds 批量关联业务id
 * @param {number} params.timeLineType 关联业务类型：1需求 2成果 3图文 4视频 5活动 6发起拼团  7参加拼团
 * @param {number} params.auditStatus 审核状态:1 审核中 2 审核不通过  3 审核通过
 * @param {array} params.auditStatusList 审核状态:1 审核中 2 审核不通过  3 审核通过
 * @param {array} params.timeLineTypes 关联业务类型：1需求 2成果 3图文 4视频 5活动 6发起拼团  7参加拼团
 * @param {number} params.userId 用户id
 * @param {number} params.isFollow 是否查询关注的科转号动态: 0 否  1是
 * @returns
 */
export const ttTimeLinePage = (data = {}, options = {}) => {
	return request({
		url: '/achv/ttTimeLine/ttTimeLinePage',
		method: 'post',
		data,
		...options,
	});
};

/**
 * 更新排序
 * @param {object} params 更新排序dto
 * @param {number} params.id
 * @param {number} params.rankingNum
 * @param {array} params.saveDtoList
 * @returns
 */
export const updateRankingNum = (data = {}, options = {}) => {
	return request({
		url: '/achv/ttTimeLine/updateRankingNum',
		method: 'post',
		data,
		...options,
	});
};

/**
 * 更新推荐
 * @param {object} params 推荐状态对象
 * @param {number} params.id id
 * @param {number} params.recommendStatus 推荐状态： 0否 1是
 * @returns
 */
export const updateRecommendStatus = (data = {}, options = {}) => {
	return request({
		url: '/achv/ttTimeLine/updateRecommendStatus',
		method: 'post',
		data,
		...options,
	});
};

/**
 * 更新显示状态
 * @param {object} params 显示状态对象
 * @param {number} params.id id
 * @param {number} params.showStatus 展示状态： 0不展示 1展示
 * @returns
 */
export const updateShowStatus = (data = {}, options = {}) => {
	return request({
		url: '/achv/ttTimeLine/updateShowStatus',
		method: 'post',
		data,
		...options,
	});
};

/**
 * 批量删除
 * @param {object} params 科转号用户行为dto
 * @param {number} params.pageNum 页码
 * @param {number} params.pageSize 每页记录数
 * @param {array} params.ids ids
 * @param {number} params.ttChannelsId 科转号id
 * @param {array} params.ttChannelsIds 批量科转号id
 * @param {number} params.businessId 关联业务id
 * @param {array} params.businessIds 批量关联业务id
 * @param {number} params.timeLineType 关联业务类型：1需求 2成果 3图文 4视频 5活动 6发起拼团  7参加拼团
 * @param {number} params.auditStatus 审核状态:1 审核中 2 审核不通过  3 审核通过
 * @param {array} params.auditStatusList 审核状态:1 审核中 2 审核不通过  3 审核通过
 * @param {array} params.timeLineTypes 关联业务类型：1需求 2成果 3图文 4视频 5活动 6发起拼团  7参加拼团
 * @param {number} params.userId 用户id
 * @param {number} params.isFollow 是否查询关注的科转号动态: 0 否  1是
 * @returns
 */
export const deleteTTTimeLineList = (data = {}, options = {}) => {
	return request({
		url: '/achv/ttTimeLine/deleteTTTimeLineList',
		method: 'post',
		data,
		...options,
	});
};

export const getTTTimeLine = (data = {}, options = {}) => {
	return request({
		url: '/achv/ttTimeLine/list',
		method: 'post',
		data,
		...options,
	});
};
