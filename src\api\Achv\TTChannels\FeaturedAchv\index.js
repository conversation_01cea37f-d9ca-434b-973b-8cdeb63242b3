import { request } from '@/utils/axios';

/**
 * 成果甄选统计
 * @returns
 */
export function achievementStatistics(params = {}, option = {}) {
	return request({
		url: `/achv/preferred/achievementStatistics`,
		method: 'POST',
		data: params,
		...option,
	});
}

/**
 * 分页查询成果甄选
 * @param {object} params 成果查询dto
 * @param {number} params.pageNum 页码
 * @param {number} params.pageSize 每页记录数
 * @param {array} params.tenantIds 租户ids
 * @param {string} params.achievementName 成果名称
 * @param {number} params.categoryValueId 行业/领域id
 * @returns
 */
export function pagePreferredAchv(params = {}, option = {}) {
	return request({
		url: `/achv/preferred/page`,
		method: 'POST',
		data: params,
		...option,
	});
}

/**
 * 更新排序字段
 * @param {object} params 更新排序dto
 * @param {number} params.id
 * @param {number} params.rankingNum
 * @param {array} params.saveDtoList
 * @returns
 */
export function updateRankingNum(params = {}, option = {}) {
	return request({
		url: `/achv/preferred/updateRankingNum`,
		method: 'POST',
		data: params,
		...option,
	});
}
