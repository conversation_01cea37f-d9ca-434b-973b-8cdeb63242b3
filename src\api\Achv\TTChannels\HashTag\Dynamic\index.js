import { request } from '@/utils/axios';

/**
 * 指定话题id查对应动态统计
 * @param {object} params 科转号话题标签条件查询
 * @param {number} params.pageNum 页码
 * @param {number} params.pageSize 每页记录数
 * @param {number} params.tenantId
 * @param {number} params.hashtagId
 * @returns
 */
export function getTTHashtagAssociationTypeStatistics(params = {}, option = {}) {
	return request({
		url: `/achv/ttHashtag/getTTHashtagAssociationTypeStatistics`,
		method: 'POST',
		data: params,
		...option,
	});
}

/**
 * 指定话题id查对应动态
 * @param {object} params 科转号话题标签条件查询
 * @param {number} params.pageNum 页码
 * @param {number} params.pageSize 每页记录数
 * @param {number} params.tenantId
 * @param {number} params.hashtagId
 * @param {number} params.businessType 动态类型
 * @returns
 */
export function getTimeByHashtagId(params = {}, option = {}) {
	return request({
		url: `/achv/ttHashtag/getTimeByHashtagId`,
		method: 'POST',
		data: params,
		...option,
	});
}
