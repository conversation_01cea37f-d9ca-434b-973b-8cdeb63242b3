import { request } from '@/utils/axios';

/**
 * 话题列表
 * @param {object} params 科转号话题标签条件查询
 * @param {number} params.pageNum 页码
 * @param {number} params.pageSize 每页记录数
 * @param {number} params.tenantId
 * @param {number} params.id
 * @param {array} params.ids
 * @param {number} params.auditStatus 审核状态:1 审核中 2 审核不通过  3 审核通过
 * @param {string} params.title
 * @returns
 */
export function listHashTag(params = {}, option = {}) {
	return request({
		url: `/achv/ttHashtag/list`,
		method: 'POST',
		data: params,
		...option,
	});
}

/**
 * 话题分页
 * @param {object} params 科转号话题标签条件查询
 * @param {number} params.pageNum 页码
 * @param {number} params.pageSize 每页记录数
 * @param {number} params.tenantId
 * @param {number} params.id
 * @param {array} params.ids
 * @param {number} params.auditStatus 审核状态:1 审核中 2 审核不通过  3 审核通过
 * @param {string} params.title
 * @returns
 */
export function pageHashTag(params = {}, option = {}) {
	return request({
		url: `/achv/ttHashtag/page`,
		method: 'POST',
		data: params,
		...option,
	});
}

/**
 * 新增话题
 * @param {object} params 科转号话题标签dto
 * @param {number} params.id
 * @param {string} params.title 标题
 * @param {string} params.desc 话题描述
 * @param {number} params.interactionNum 互动基数
 * @returns
 */
export function addHashTag(params = {}, option = {}) {
	return request({
		url: `/achv/ttHashtag/add`,
		method: 'POST',
		data: params,
		...option,
	});
}

/**
 * 修改科话题
 * @param {object} params 科转号话题标签dto
 * @param {number} params.id
 * @param {string} params.title 标题
 * @param {string} params.desc 话题描述
 * @param {number} params.interactionNum 互动基数
 * @returns
 */
export function updateHashTag(params = {}, option = {}) {
	return request({
		url: `/achv/ttHashtag/update`,
		method: 'POST',
		data: params,
		...option,
	});
}

/**
 * 话题详情
 * @param {object} params 科转号话题标签条件查询
 * @param {number} params.pageNum 页码
 * @param {number} params.pageSize 每页记录数
 * @param {number} params.tenantId
 * @param {number} params.id
 * @param {array} params.ids
 * @param {number} params.auditStatus 审核状态:1 审核中 2 审核不通过  3 审核通过
 * @param {string} params.title
 * @returns
 */
export function getHashTag(params = {}, option = {}) {
	return request({
		url: `/achv/ttHashtag/detail`,
		method: 'POST',
		data: params,
		...option,
	});
}

/**
 * 批量删除话题
 * @param {object} params 科转号话题标签条件查询
 * @param {number} params.pageNum 页码
 * @param {number} params.pageSize 每页记录数
 * @param {number} params.tenantId
 * @param {number} params.id
 * @param {array} params.ids
 * @param {number} params.auditStatus 审核状态:1 审核中 2 审核不通过  3 审核通过
 * @param {string} params.title
 * @returns
 */
export function batchDelHashTag(params = {}, option = {}) {
	return request({
		url: `/achv/ttHashtag/batchDel`,
		method: 'POST',
		data: params,
		...option,
	});
}

/**
 * 审核
 * @param {object} params 专家审核对象
 * @param {number} params.id id
 * @param {string} params.reason 不通过原因
 * @param {number} params.status 审核状态:1 审核中 2 审核不通过  3 审核通过
 * @returns
 */
export function auditHashTag(params = {}, option = {}) {
	return request({
		url: `/achv/ttHashtag/audit`,
		method: 'POST',
		data: params,
		...option,
	});
}

/**
 * 统计审核数
 * @returns
 */
export function getTTHashtagAuditNum(params = {}, option = {}) {
	return request({
		url: `/achv/ttHashtag/getTTHashtagAuditNum`,
		method: 'POST',
		data: params,
		...option,
	});
}

/**
 * 更新排序
 * @param {object} params 更新排序dto
 * @param {number} params.id
 * @param {number} params.rankingNum
 * @param {array} params.saveDtoList
 * @returns
 */
export function updateRankingNum(params = {}, option = {}) {
	return request({
		url: `/achv/ttHashtag/updateRankingNum`,
		method: 'POST',
		data: params,
		...option,
	});
}
