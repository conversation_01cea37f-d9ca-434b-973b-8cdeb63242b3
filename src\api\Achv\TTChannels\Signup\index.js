import { request } from '@/utils/axios';

/**
 * 科转号分页
 * @param {object} params 科转号条件查询
 * @param {number} params.pageNum 页码
 * @param {number} params.pageSize 每页记录数
 * @param {number} params.tenantId 租户id
 * @param {number} params.id
 * @param {array} params.ids
 * @param {string} params.sortValue 排序值(传)：desc/asc
 * @param {string} params.sortKey 排序字段,传返回字段名称如：创建时间-createTime 排序号-rankingNum 审核时间-auditTime 先审核状态后审核时间-auditStatusAndAuditTime
 * @returns
 */
export const pageTTChannels = (data = {}, options = {}) => {
	return request({
		url: '/achv/ttChannels/page',
		method: 'post',
		data,
		...options,
	});
};

/**
 * 新增科转号
 * @param {object} params 科转号保存dto
 * @param {number} params.id
 * @param {string} params.name 名称
 * @param {string} params.provinceCode 省份code
 * @param {string} params.cityCode 城市code
 * @param {number} params.orgTypeId 机构类型id(字典表id)
 * @param {string} params.contactAddress 联系地址
 * @param {string} params.contactPhone 联系电话
 * @param {number} params.inviteId 邀约人科转号id
 * @param {number} params.auditStatus 审核状态:1 审核中 2 审核不通过  3 审核通过
 * @param {number} params.channelType 科转号类型: 1.科研机构 2.技术经理人 3.科创企业 4.行业专家 5.金融机构
 * @param {number} params.isAuth 是否认证：0 否 1 是
 * @param {number} params.fansNum 粉丝基数
 * @param {string} params.reason 审核原因
 * @param {number} params.rankingNum 排名序号
 * @param {string} params.wechatChannels 微信视频号
 * @param {number} params.wechatChannelsAssociationStatus 视频号关联状态： 0 否 1是
 * @param {string} params.operatorPhone 运营人联系电话
 * @param {string} params.operatorName 运营人姓名
 * @param {string} params.accountPhone 运营手机号
 * @param {string} params.accountName 帐号名称
 * @param {string} params.accountDesc 帐号描述(一句话介绍)
 * @param {string} params.birthDay 出生日期
 * @param {string} params.companyName 所属/就职公司名
 * @param {string} params.positionName 职位
 * @param {string} params.professionalCertificateName 职称名称
 * @param {number} params.degree 学历：1博士 2硕士 3本科 4专科 5中专及以下
 * @param {number} params.brokerCertificateStatus 经理人证书拥有状态： 0 没有 1有
 * @param {number} params.brokerCertificateLevel 经理人证书等级：1 初级 2中级 3高级
 * @param {string} params.workingExperience 从业经验
 * @param {string} params.achievementPaper 成果论文
 * @param {string} params.graduationSchool 毕业学校
 * @param {string} params.introduction 简介
 * @param {string} params.businessLicense 营业执照
 * @param {string} params.video 展示视频
 * @param {string} params.showPhotos 展示照片
 * @param {string} params.idCardPhoto 身份证正面
 * @param {string} params.idCardPhotoBack 身份证反面
 * @param {string} params.homePageBackground 主页背景图
 * @param {string} params.brokerCertificatePhoto 经理人证书照片
 * @param {string} params.operationAuthorizationBook 运营授权书
 * @param {string} params.avatarUrl 头像地址
 * @param {array} params.industryIds 领域id
 * @param {array} params.associationMemberIds 关联成员id
 * @param {array} params.workExperiences 科转号人员工作经验表dto
 * @returns
 */
export const addTTChannels = (data = {}, options = {}) => {
	return request({
		url: '/achv/ttChannels/add',
		method: 'post',
		data,
		...options,
	});
};

/**
 * 修改科转号
 * @param {object} params 科转号保存dto
 * @param {number} params.id
 * @param {string} params.name 名称
 * @param {string} params.provinceCode 省份code
 * @param {string} params.cityCode 城市code
 * @param {number} params.orgTypeId 机构类型id(字典表id)
 * @param {string} params.contactAddress 联系地址
 * @param {string} params.contactPhone 联系电话
 * @param {number} params.inviteId 邀约人科转号id
 * @param {number} params.auditStatus 审核状态:1 审核中 2 审核不通过  3 审核通过
 * @param {number} params.channelType 科转号类型: 1.科研机构 2.技术经理人 3.科创企业 4.行业专家 5.金融机构
 * @param {number} params.isAuth 是否认证：0 否 1 是
 * @param {number} params.fansNum 粉丝基数
 * @param {string} params.reason 审核原因
 * @param {number} params.rankingNum 排名序号
 * @param {string} params.wechatChannels 微信视频号
 * @param {number} params.wechatChannelsAssociationStatus 视频号关联状态： 0 否 1是
 * @param {string} params.operatorPhone 运营人联系电话
 * @param {string} params.operatorName 运营人姓名
 * @param {string} params.accountPhone 运营手机号
 * @param {string} params.accountName 帐号名称
 * @param {string} params.accountDesc 帐号描述(一句话介绍)
 * @param {string} params.birthDay 出生日期
 * @param {string} params.companyName 所属/就职公司名
 * @param {string} params.positionName 职位
 * @param {string} params.professionalCertificateName 职称名称
 * @param {number} params.degree 学历：1博士 2硕士 3本科 4专科 5中专及以下
 * @param {number} params.brokerCertificateStatus 经理人证书拥有状态： 0 没有 1有
 * @param {number} params.brokerCertificateLevel 经理人证书等级：1 初级 2中级 3高级
 * @param {string} params.workingExperience 从业经验
 * @param {string} params.achievementPaper 成果论文
 * @param {string} params.graduationSchool 毕业学校
 * @param {string} params.introduction 简介
 * @param {string} params.businessLicense 营业执照
 * @param {string} params.video 展示视频
 * @param {string} params.showPhotos 展示照片
 * @param {string} params.idCardPhoto 身份证正面
 * @param {string} params.idCardPhotoBack 身份证反面
 * @param {string} params.homePageBackground 主页背景图
 * @param {string} params.brokerCertificatePhoto 经理人证书照片
 * @param {string} params.operationAuthorizationBook 运营授权书
 * @param {string} params.avatarUrl 头像地址
 * @param {array} params.industryIds 领域id
 * @param {array} params.associationMemberIds 关联成员id
 * @param {array} params.workExperiences 科转号人员工作经验表dto
 * @returns
 */
export const updateTTChannels = (data = {}, options = {}) => {
	return request({
		url: '/achv/ttChannels/update',
		method: 'post',
		data,
		...options,
	});
};

/**
 * 科转号详情
 * @param {object} params 科转号条件查询
 * @param {number} params.pageNum 页码
 * @param {number} params.pageSize 每页记录数
 * @param {number} params.tenantId 租户id
 * @param {number} params.id
 * @param {array} params.ids
 * @param {string} params.sortValue 排序值(传)：desc/asc
 * @param {string} params.sortKey 排序字段,传返回字段名称如：创建时间-createTime 排序号-rankingNum 审核时间-auditTime 先审核状态后审核时间-auditStatusAndAuditTime
 * @returns
 */
export const getTTChannelsDetail = (data = {}, options = {}) => {
	return request({
		url: '/achv/ttChannels/detail',
		method: 'post',
		data,
		...options,
	});
};

/**
 * 查询关联人员(传科转号id)
 * @param {object} params 科转号条件查询
 * @param {number} params.pageNum 页码
 * @param {number} params.pageSize 每页记录数
 * @param {number} params.tenantId 租户id
 * @param {number} params.id
 * @param {array} params.ids
 * @param {string} params.sortValue 排序值(传)：desc/asc
 * @param {string} params.sortKey 排序字段,传返回字段名称如：创建时间-createTime 排序号-rankingNum 审核时间-auditTime 先审核状态后审核时间-auditStatusAndAuditTime
 * @returns
 */
export const getTTChannelsAssociationMember = (data = {}, options = {}) => {
	return request({
		url: '/achv/ttChannels/getTTChannelsAssociationMember',
		method: 'post',
		data,
		...options,
	});
};

/**
 * 批量删除科转号
 * @param {object} params 科转号条件查询
 * @param {number} params.pageNum 页码
 * @param {number} params.pageSize 每页记录数
 * @param {number} params.tenantId 租户id
 * @param {number} params.id
 * @param {array} params.ids
 * @param {string} params.sortValue 排序值(传)：desc/asc
 * @param {string} params.sortKey 排序字段,传返回字段名称如：创建时间-createTime 排序号-rankingNum 审核时间-auditTime 先审核状态后审核时间-auditStatusAndAuditTime
 * @returns
 */
export const batchDel = (data = {}, options = {}) => {
	return request({
		url: '/achv/ttChannels/batchDel',
		method: 'post',
		data,
		...options,
	});
};

/**
 * 更新排序
 * @param {object} params 更新排序dto
 * @param {number} params.id
 * @param {number} params.rankingNum
 * @param {array} params.saveDtoList
 * @returns
 */
export const updateRankingNum = (data = {}, options = {}) => {
	return request({
		url: '/achv/ttChannels/updateRankingNum',
		method: 'post',
		data,
		...options,
	});
};

/**
 * 审核
 * @param {object} params 科转号审核dto
 * @param {number} params.id
 * @param {number} params.auditStatus 审核状态:1 审核中 2 审核不通过  3 审核通过
 * @param {string} params.reason 审核原因
 * @returns
 */
export const auditTTChannels = (data = {}, options = {}) => {
	return request({
		url: '/achv/ttChannels/audit',
		method: 'post',
		data,
		...options,
	});
};

/**
 * 科转号统计
 * @param {object} params 科转号条件查询
 * @param {number} params.pageNum 页码
 * @param {number} params.pageSize 每页记录数
 * @param {number} params.tenantId 租户id
 * @param {number} params.id
 * @param {array} params.ids
 * @param {string} params.sortValue 排序值(传)：desc/asc
 * @param {string} params.sortKey 排序字段,传返回字段名称如：创建时间-createTime 排序号-rankingNum 审核时间-auditTime 先审核状态后审核时间-auditStatusAndAuditTime
 * @returns
 */
export function queryStatistics(params = {}, option = {}) {
	return request({
		url: `/achv/ttChannels/getTTChannelsAuditNum`,
		method: 'POST',
		data: params,
		...option,
	});
}

/**
 * 查询领域类目对应的科传号(传categoryId和channelType)
 * @param {object} params 科转号条件查询
 * @param {number} params.channelType 科转号类型: 1.科研机构 2.技术经理人 3.科创企业 4.行业专家 5.金融机构
 * @param {number} params.categoryId 类目领域id
 * @returns
 */
export function getTTChannelsList(params = {}, option = {}) {
	return request({
		url: `/achv/ttChannels/getTTChannelsByCategoryIdAndChannelType`,
		method: 'POST',
		data: params,
		...option,
	});
}

/**
 * 更新推荐
 * @param {object} params 推荐状态对象
 * @param {number} params.id id
 * @param {number} params.recommendStatus 推荐状态： 0否 1是
 * @returns
 */
export function updateRecommendStatus(params = {}, option = {}) {
	return request({
		url: `/achv/ttChannels/updateRecommendStatus`,
		method: 'POST',
		data: params,
		...option,
	});
}
