import { request } from '@/utils/axios';

/**
 * 分页查询
 * @param {object} params 科转号条件查询
 * @param {number} params.pageNum 页码
 * @param {number} params.pageSize 每页记录数
 * @param {number} params.tenantId 租户id
 * @param {number} params.id
 * @param {array} params.ids
 * @returns
 */
export function pageAccount(params = {}, option = {}) {
	return request({
		url: `/achv/ttWechatChannelsAccount/page`,
		method: 'POST',
		data: params,
		...option,
	});
}

/**
 * 新增帐号管理
 * @param {object} params 微信视频帐号保存dto
 * @param {number} params.id
 * @param {string} params.wechatChannelsAccountName 帐号名称
 * @param {string} params.wechatChannelsAccount_id 帐号id
 * @param {string} params.avatarUrl 头像地址
 * @param {array} params.ttChannelsIds 关联科转号
 * @returns
 */
export function addAccount(params = {}, option = {}) {
	return request({
		url: `/achv/ttWechatChannelsAccount/addAccount`,
		method: 'POST',
		data: params,
		...option,
	});
}

/**
 * 修改帐号
 * @param {object} params 微信视频帐号保存dto
 * @param {number} params.id
 * @param {string} params.wechatChannelsAccountName 帐号名称
 * @param {string} params.wechatChannelsAccount_id 帐号id
 * @param {string} params.avatarUrl 头像地址
 * @param {array} params.ttChannelsIds 关联科转号
 * @returns
 */
export function updateAccount(params = {}, option = {}) {
	return request({
		url: `/achv/ttWechatChannelsAccount/updateAccount`,
		method: 'POST',
		data: params,
		...option,
	});
}

/**
 * 批量删除
 * @param {object} params 科转号条件查询
 * @param {number} params.pageNum 页码
 * @param {number} params.pageSize 每页记录数
 * @param {number} params.tenantId 租户id
 * @param {number} params.id
 * @param {array} params.ids
 * @returns
 */
export function batchDelAccount(params = {}, option = {}) {
	return request({
		url: `/achv/ttWechatChannelsAccount/batchDel`,
		method: 'POST',
		data: params,
		...option,
	});
}

/**
 * 获取帐号详情
 * @param {object} params 科转号条件查询
 * @param {number} params.pageNum 页码
 * @param {number} params.pageSize 每页记录数
 * @param {number} params.tenantId 租户id
 * @param {number} params.id
 * @param {array} params.ids
 * @returns
 */
export function getAccount(params = {}, option = {}) {
	return request({
		url: `/achv/ttWechatChannelsAccount/getAccount`,
		method: 'POST',
		data: params,
		...option,
	});
}
