import { request } from "@/utils/axios";

/**
 * 统计待办事项数据
 * @returns
 */
export function getTaskCount(params = {}, option = {}) {
    return request({
        url: `/achv/task/getTaskCount`,
        method: "POST",
        data: params,
        ...option,
    });
}

/**
 * 查询待办事项数据
 * @param {object} params 任务查询dto
 * @param {number} params.pageNum 页码
 * @param {number} params.pageSize 每页记录数
 * @param {number} params.id
 * @param {number} params.tenantId
 * @returns
 */
export function taskPage(params = {}, option = {}) {
    return request({
        url: `/achv/task/taskPage`,
        method: "POST",
        data: params,
        ...option,
    });
}

/**
 * 查询详情
 * @param {object} params 任务查询dto
 * @param {number} params.pageNum 页码
 * @param {number} params.pageSize 每页记录数
 * @param {number} params.id
 * @param {number} params.tenantId
 * @param {string} params.typeCode
 * @returns
 */
export function taskDetail(params = {}, option = {}) {
    return request({
        url: `/achv/task/detail`,
        method: "POST",
        data: params,
        ...option,
    });
}

/**
 * 更新处理状态
 * @param {object} params 任务查询dto
 * @param {number} params.pageNum 页码
 * @param {number} params.pageSize 每页记录数
 * @param {number} params.id
 * @param {number} params.tenantId
 * @returns
 */
export function updateHandlingStatus(params = {}, option = {}) {
    return request({
        url: `/achv/task/updateHandlingStatus`,
        method: "POST",
        data: params,
        ...option,
    });
}
