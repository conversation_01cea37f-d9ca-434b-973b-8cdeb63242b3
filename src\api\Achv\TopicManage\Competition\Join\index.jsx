import { request } from '@/utils/axios';

/**
 * 分页查询大赛专题参加
 * @param {object} params 大赛专题管理查询dto
 * @param {number} params.pageNum 页码
 * @param {number} params.pageSize 每页记录数
 * @param {number} params.id
 * @param {number} params.competitionTopicId
 * @param {number} params.tenantId
 * @param {array} params.ids
 * @param {array} params.tenantIds
 * @returns
 */
export function getCompetitionTopicJoinInPage(data = {}, option = {}) {
	return request({
		url: `/achv/competitionTopic/getCompetitionTopicJoinInPage`,
		method: 'POST',
		data,
		...option,
	});
}

/**
 * 查询大赛专题参加详情
 * @param {object} params 大赛专题管理查询dto
 * @param {number} params.pageNum 页码
 * @param {number} params.pageSize 每页记录数
 * @param {number} params.id
 * @param {number} params.competitionTopicId
 * @param {number} params.tenantId
 * @param {array} params.ids
 * @param {array} params.tenantIds
 * @returns
 */
export function getCompetitionTopicJoinInVo(data = {}, option = {}) {
	return request({
		url: `/achv/competitionTopic/getCompetitionTopicJoinInVo`,
		method: 'POST',
		data,
		...option,
	});
}

/**
 * 新增大赛专题参加
 * @param {object} params 大赛专题管理表保存dto
 * @param {number} params.id
 * @param {number} params.competitionTopicId 大赛id
 * @param {string} params.name 参赛名称
 * @param {string} params.scoreJson 分数(多个以,分割)
 * @param {string} params.roomName 赛室名称
 * @param {number} params.rankingNum 排序
 * @returns
 */
export function saveCompetitionTopicJoinIn(data = {}, option = {}) {
	return request({
		url: `/achv/competitionTopic/saveCompetitionTopicJoinIn`,
		method: 'POST',
		data,
		...option,
	});
}

/**
 * 修改大赛专题参加
 * @param {object} params 大赛专题管理表保存dto
 * @param {number} params.id
 * @param {number} params.competitionTopicId 大赛id
 * @param {string} params.name 参赛名称
 * @param {string} params.scoreJson 分数(多个以,分割)
 * @param {string} params.roomName 赛室名称
 * @param {number} params.rankingNum 排序
 * @returns
 */
export function updateCompetitionTopicJoinIn(data = {}, option = {}) {
	return request({
		url: `/achv/competitionTopic/updateCompetitionTopicJoinIn`,
		method: 'POST',
		data,
		...option,
	});
}

/**
 * 批量删除大赛专题参加
 * @param {object} params 大赛专题管理查询dto
 * @param {number} params.pageNum 页码
 * @param {number} params.pageSize 每页记录数
 * @param {number} params.id
 * @param {number} params.competitionTopicId
 * @param {number} params.tenantId
 * @param {array} params.ids
 * @param {array} params.tenantIds
 * @returns
 */
export function batchDelJoinIn(data = {}, option = {}) {
	return request({
		url: `/achv/competitionTopic/batchDelJoinIn`,
		method: 'POST',
		data,
		...option,
	});
}

/**
 * 更新大赛专题参加排序
 * @param {object} params 更新排序dto
 * @param {number} params.id
 * @param {number} params.rankingNum
 * @param {array} params.saveDtoList
 * @returns
 */
export function updateRankingNumToJoinIn(data = {}, option = {}) {
	return request({
		url: `/achv/competitionTopic/updateRankingNumToJoinIn`,
		method: 'POST',
		data,
		...option,
	});
}

/**
 * 导入名单
 * @param {object} params 更新排序dto
 * @param {number} params.id
 * @param {number} params.rankingNum
 * @param {array} params.saveDtoList
 * @returns
 */
export function importJoinInExcelData(data = {}, option = {}) {
	return request({
		url: `/achv/competitionTopic/importJoinInExcelData`,
		method: 'POST',
		data,
		header: {
			'content-type': 'multipart/form-data',
		},
		...option,
	});
}
