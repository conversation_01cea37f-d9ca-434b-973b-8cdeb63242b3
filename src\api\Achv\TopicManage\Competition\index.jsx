import { request } from '@/utils/axios';

/**
 * 分页查询
 * @param {object} params 大赛专题管理查询dto
 * @param {number} params.pageNum 页码
 * @param {number} params.pageSize 每页记录数
 * @param {number} params.id
 * @param {number} params.auditStatus 审核状态:1 审核中 2 审核不通过  3 审核通过
 * @param {string} params.name
 * @param {number} params.tenantId
 * @param {array} params.ids
 * @param {array} params.tenantIds
 * @returns
 */
export function getCompetitionTopicPage(data = {}, option = {}) {
	return request({
		url: `/achv/competitionTopic/getCompetitionTopicPage`,
		method: 'POST',
		data,
		...option,
	});
}

/**
 * 获取大赛专题详情
 * @param {object} params 大赛专题管理查询dto
 * @param {number} params.pageNum 页码
 * @param {number} params.pageSize 每页记录数
 * @param {number} params.id
 * @param {number} params.auditStatus 审核状态:1 审核中 2 审核不通过  3 审核通过
 * @param {string} params.name
 * @param {number} params.tenantId
 * @param {array} params.ids
 * @param {array} params.tenantIds
 * @returns
 */
export function getCompetitionTopicVo(data = {}, option = {}) {
	return request({
		url: `/achv/competitionTopic/getCompetitionTopicVo`,
		method: 'POST',
		data,
		...option,
	});
}

/**
 * 新增大赛专题
 * @param {object} params 大赛专题管理表保存dto
 * @param {number} params.id
 * @param {string} params.name 大赛名称
 * @param {number} params.auditStatus 审核状态:1 审核中 2 审核不通过  3 审核通过
 * @param {number} params.reviewQuantity 评审数量
 * @param {number} params.ratingType 评分类型：1算平均分 2去最高最低分
 * @param {string} params.timeLineId 关联动态id集合(多个用,分割)
 * @param {number} params.rankingNum 排序
 * @param {string} params.groupName 组名称
 * @returns
 */
export function save(data = {}, option = {}) {
	return request({
		url: `/achv/competitionTopic/save`,
		method: 'POST',
		data,
		...option,
	});
}

/**
 * 修改大赛专题
 * @param {object} params 大赛专题管理表保存dto
 * @param {number} params.id
 * @param {string} params.name 大赛名称
 * @param {number} params.auditStatus 审核状态:1 审核中 2 审核不通过  3 审核通过
 * @param {number} params.reviewQuantity 评审数量
 * @param {number} params.ratingType 评分类型：1算平均分 2去最高最低分
 * @param {string} params.timeLineId 关联动态id集合(多个用,分割)
 * @param {number} params.rankingNum 排序
 * @param {string} params.groupName 组名称
 * @returns
 */
export function update(data = {}, option = {}) {
	return request({
		url: `/achv/competitionTopic/update`,
		method: 'POST',
		data,
		...option,
	});
}

/**
 * 更新专题排序
 * @param {object} params 更新排序dto
 * @param {number} params.id
 * @param {number} params.rankingNum
 * @param {array} params.saveDtoList
 * @returns
 */
export function updateRankingNum(data = {}, option = {}) {
	return request({
		url: `/achv/competitionTopic/updateRankingNum`,
		method: 'POST',
		data,
		...option,
	});
}

/**
 * 批量删除大赛专题
 * @param {object} params 大赛专题管理查询dto
 * @param {number} params.pageNum 页码
 * @param {number} params.pageSize 每页记录数
 * @param {number} params.id
 * @param {number} params.auditStatus 审核状态:1 审核中 2 审核不通过  3 审核通过
 * @param {string} params.name
 * @param {number} params.tenantId
 * @param {array} params.ids
 * @param {array} params.tenantIds
 * @returns
 */
export function batchDel(data = {}, option = {}) {
	return request({
		url: `/achv/competitionTopic/batchDel`,
		method: 'POST',
		data,
		...option,
	});
}

/**
 * 获取统计数量
 * @returns
 */
export function getStatistics(data = {}, option = {}) {
	return request({
		url: `/achv/competitionTopic/getStatistics`,
		method: 'POST',
		data,
		...option,
	});
}

/**
 * 大赛专题审核
 * @param {object} params 专家审核对象
 * @param {array} params.ids
 * @param {number} params.id id
 * @param {string} params.reason 不通过原因
 * @param {number} params.status 审核状态:1 审核中 2 审核不通过  3 审核通过
 * @returns
 */
export function audit(data = {}, option = {}) {
	return request({
		url: `/achv/competitionTopic/audit`,
		method: 'POST',
		data,
		...option,
	});
}

/**
 * 查询大赛专题参加详情
 * @param {object} params 大赛专题管理查询dto
 * @param {number} params.pageNum 页码
 * @param {number} params.pageSize 每页记录数
 * @param {number} params.id
 * @param {number} params.competitionTopicId
 * @param {number} params.tenantId
 * @param {array} params.ids
 * @param {array} params.tenantIds
 * @returns
 */
export function getCompetitionTopicJoinInVo(data = {}, option = {}) {
	return request({
		url: `/achv/competitionTopic/getCompetitionTopicJoinInVo`,
		method: 'POST',
		data,
		...option,
	});
}
