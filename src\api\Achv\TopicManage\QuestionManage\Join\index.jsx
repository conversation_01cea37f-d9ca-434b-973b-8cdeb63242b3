import { request } from '@/utils/axios';

/**
 * 分页查询表单填写
 * @param {object} params 表单信息表查询dto
 * @param {number} params.pageNum 页码
 * @param {number} params.pageSize 每页记录数
 * @param {number} params.id
 * @param {number} params.tenantId
 * @param {array} params.ids
 * @param {array} params.tenantIds
 * @param {number} params.formDataId 表单主键
 * @returns
 */
export function getFormDataFillInPage(data = {}, option = {}) {
	return request({
		url: `/achv/formData/getFormDataFillInPage`,
		method: 'POST',
		data,
		...option,
	});
}

/**
 * 查询表单填写详情
 * @param {object} params 表单信息表查询dto
 * @param {number} params.pageNum 页码
 * @param {number} params.pageSize 每页记录数
 * @param {number} params.id
 * @param {number} params.tenantId
 * @param {array} params.ids
 * @param {array} params.tenantIds
 * @param {number} params.formDataId 表单主键
 * @returns
 */
export function getFormDataFillInVo(data = {}, option = {}) {
	return request({
		url: `/achv/formData/getFormDataFillInVo`,
		method: 'POST',
		data,
		...option,
	});
}

/**
 * 新增表单填写参加
 * @param {object} params 表单填写保存dto
 * @param {number} params.id
 * @param {number} params.formDataId 表单主键
 * @param {string} params.additionalInfoJson 报名附加信息json
 * @param {string} params.inviterUserName 邀请人名称
 * @param {string} params.sharUserShortCode 分享用户的用户短码
 * @returns
 */
export function saveFormDataFillIn(data = {}, option = {}) {
	return request({
		url: `/achv/formData/saveFormDataFillIn`,
		method: 'POST',
		data,
		...option,
	});
}

/**
 * 修改表单填写参加
 * @param {object} params 表单填写保存dto
 * @param {number} params.id
 * @param {number} params.formDataId 表单主键
 * @param {string} params.additionalInfoJson 报名附加信息json
 * @param {string} params.inviterUserName 邀请人名称
 * @param {string} params.sharUserShortCode 分享用户的用户短码
 * @returns
 */
export function updateFormDataFillIn(data = {}, option = {}) {
	return request({
		url: `/achv/formData/updateFormDataFillIn`,
		method: 'POST',
		data,
		...option,
	});
}

/**
 * 批量删除表单填写参加
 * @param {object} params 表单信息表查询dto
 * @param {number} params.pageNum 页码
 * @param {number} params.pageSize 每页记录数
 * @param {number} params.id
 * @param {number} params.tenantId
 * @param {array} params.ids
 * @param {array} params.tenantIds
 * @param {number} params.formDataId 表单主键
 * @returns
 */
export function batchDelFormDataFillIn(data = {}, option = {}) {
	return request({
		url: `/achv/formData/batchDelFormDataFillIn`,
		method: 'POST',
		data,
		...option,
	});
}

/**
 * 表单填写审核
 * @param {object} params 专家审核对象
 * @param {array} params.ids
 * @param {number} params.id id
 * @param {string} params.reason 不通过原因
 * @param {number} params.status 审核状态:1 审核中 2 审核不通过  3 审核通过
 * @returns
 */
export function auditFormDataFillIn(data = {}, option = {}) {
	return request({
		url: `/achv/formData/auditFormDataFillIn`,
		method: 'POST',
		data,
		...option,
	});
}

/**
 * 导出填写
 * @param {object} params 表单信息表查询dto
 * @param {number} params.pageNum 页码
 * @param {number} params.pageSize 每页记录数
 * @param {number} params.id
 * @param {number} params.tenantId
 * @param {array} params.ids
 * @param {array} params.tenantIds
 * @param {number} params.formDataId 表单主键
 * @returns
 */
export function exportFormDataFillIn(data = {}, option = {}) {
	return request({
		url: `/achv/formData/exportFormDataFillIn`,
		method: 'POST',
		data,
		responseType: 'blob',
		...option,
	});
}

/**
 * 统计
 * @param {object} params
 * @returns
 */
export function getStatistics(data = {}, option = {}) {
	return request({
		url: `/achv/formData/fillInStatistics`,
		method: 'POST',
		data,
		...option,
	});
}
