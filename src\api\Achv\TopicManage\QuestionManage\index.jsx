import { request } from '@/utils/axios';

/**
 * 分页查询
 * @param {object} params 表单信息表查询dto
 * @param {number} params.pageNum 页码
 * @param {number} params.pageSize 每页记录数
 * @param {number} params.id
 * @param {number} params.auditStatus 审核状态:1 审核中 2 审核不通过  3 审核通过
 * @param {string} params.title
 * @param {number} params.tenantId
 * @param {array} params.ids
 * @param {array} params.tenantIds
 * @returns
 */
export function getFormDataPage(data = {}, option = {}) {
	return request({
		url: `/achv/formData/getFormDataPage`,
		method: 'POST',
		data,
		...option,
	});
}

/**
 * 新增表单填写
 * @param {object} params 表单信息表保存dto
 * @param {number} params.id
 * @param {string} params.title 表单标题
 * @param {object} params.startTime 填写开始时间,yyyy-MM-dd HH:ss:mm
 * @param {object} params.endTime 填写结束时间,yyyy-MM-dd HH:ss:mm
 * @param {string} params.introduce 表单介绍
 * @param {string} params.enrollDesignJson 报名内容设计json
 * @param {number} params.auditStatus 审核状态:1 审核中 2 审核不通过  3 审核通过
 * @param {number} params.rankingNum 排序序号
 * @returns
 */
export function saveFormData(data = {}, option = {}) {
	return request({
		url: `/achv/formData/save`,
		method: 'POST',
		data,
		...option,
	});
}

/**
 * 修改表单填写
 * @param {object} params 表单信息表保存dto
 * @param {number} params.id
 * @param {string} params.title 表单标题
 * @param {object} params.startTime 填写开始时间,yyyy-MM-dd HH:ss:mm
 * @param {object} params.endTime 填写结束时间,yyyy-MM-dd HH:ss:mm
 * @param {string} params.introduce 表单介绍
 * @param {string} params.enrollDesignJson 报名内容设计json
 * @param {number} params.auditStatus 审核状态:1 审核中 2 审核不通过  3 审核通过
 * @param {number} params.rankingNum 排序序号
 * @returns
 */
export function updateFormData(data = {}, option = {}) {
	return request({
		url: `/achv/formData/update`,
		method: 'POST',
		data,
		...option,
	});
}

/**
 * 获取表单填写详情
 * @param {object} params 表单信息表查询dto
 * @param {number} params.pageNum 页码
 * @param {number} params.pageSize 每页记录数
 * @param {number} params.id
 * @param {number} params.auditStatus 审核状态:1 审核中 2 审核不通过  3 审核通过
 * @param {string} params.title
 * @param {number} params.tenantId
 * @param {array} params.ids
 * @param {array} params.tenantIds
 * @returns
 */
export function getFormData(data = {}, option = {}) {
	return request({
		url: `/achv/formData/getFormDataVo`,
		method: 'POST',
		data,
		...option,
	});
}

/**
 * 批量删除表单填写
 * @param {object} params 表单信息表查询dto
 * @param {number} params.pageNum 页码
 * @param {number} params.pageSize 每页记录数
 * @param {number} params.id
 * @param {number} params.auditStatus 审核状态:1 审核中 2 审核不通过  3 审核通过
 * @param {string} params.title
 * @param {number} params.tenantId
 * @param {array} params.ids
 * @param {array} params.tenantIds
 * @returns
 */
export function batchDelFormData(data = {}, option = {}) {
	return request({
		url: `/achv/formData/batchDel`,
		method: 'POST',
		data,
		...option,
	});
}

/**
 * 更新表单填写排序
 * @param {object} params 更新排序dto
 * @param {number} params.id
 * @param {number} params.rankingNum
 * @param {array} params.saveDtoList
 * @returns
 */
export function updateRankingNum(data = {}, option = {}) {
	return request({
		url: `/achv/formData/updateRankingNum`,
		method: 'POST',
		data,
		...option,
	});
}

/**
 * 表单填写审核
 * @param {object} params 专家审核对象
 * @param {array} params.ids
 * @param {number} params.id id
 * @param {string} params.reason 不通过原因
 * @param {number} params.status 审核状态:1 审核中 2 审核不通过  3 审核通过
 * @returns
 */
export function auditFormData(data = {}, option = {}) {
	return request({
		url: `/achv/formData/audit`,
		method: 'POST',
		data,
		...option,
	});
}

/**
 * 统计
 * @param {object} params
 * @returns
 */
export function getStatistics(data = {}, option = {}) {
	return request({
		url: `/achv/formData/getStatistics`,
		method: 'POST',
		data,
		...option,
	});
}
