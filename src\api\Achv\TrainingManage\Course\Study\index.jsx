import { request } from '@/utils/axios';

/**
 * 分页查询考试情况
 * @param {object} params 培训课程考试查询dto
 * @param {number} params.pageNum 页码
 * @param {number} params.pageSize 每页记录数
 * @param {number} params.id
 * @param {number} params.tenantId
 * @param {array} params.ids
 * @param {array} params.tenantIds
 * @param {number} params.courseId 课程id
 * @param {number} params.userId 用户id
 * @param {number} params.finishStatus 学习完成状态：1未完成 2已完成
 * @param {number} params.examFishStatus 考试完成状态:1未完成 2已完成
 * @param {number} params.examResultStatus 考试结果
 * @param {array} params.examResultStatusList
 * @returns
 */
export function getTrainingCourseExaminationPage(data = {}, option = {}) {
	return request({
		url: `/achv/training/getTrainingCourseExaminationPage`,
		method: 'POST',
		data,
		...option,
	});
}

/**
 * 分页查询考试情况
 * @param {number} params.id 学习Id
 * @returns
 */
export function queryExamination(data = {}, option = {}) {
	return request({
		url: `/achv/training/queryExamination`,
		method: 'POST',
		data,
		...option,
	});
}

/**
 * 统计
 * @param {number} params.id 学习Id
 * @param {number} params.courseId 课程id
 * @returns
 */
export function scoreDataStatistics(data = {}, option = {}) {
	return request({
		url: `/achv/training/scoreDataStatistics`,
		method: 'POST',
		data,
		...option,
	});
}

/**
 * 保存评分
 * @param {object} params 培训课程考试答案提交dto
 * @param {number} params.id
 * @param {number} params.courseId
 * @param {string} params.examUseTime
 * @param {array} params.answers 培训课程考试答案内容dto
 * @returns
 */
export function saveScore(data = {}, option = {}) {
	return request({
		url: `/achv/training/saveScore`,
		method: 'POST',
		data,
		...option,
	});
}

/**
 * 查询科目学习进度
 * @param {object} params 培训课程考试答案提交dto
 * @param {number} params.id
 * @param {number} params.courseId
 * @param {number} params.userId
 * @returns
 */
export function querySubjectStudyProgress(data = {}, option = {}) {
	return request({
		url: `/achv/training/querySubjectStudyProgress`,
		method: 'POST',
		data,
		...option,
	});
}

/**
 * 导出学习考试情况
 * @param {object} params
 * @param {number} params.courseId
 * @returns
 */
export function exportExamination(data = {}, option = {}) {
	return request({
		url: `/achv/training/exportExamination`,
		method: 'POST',
		data,
		responseType: 'blob',
		...option,
	});
}
