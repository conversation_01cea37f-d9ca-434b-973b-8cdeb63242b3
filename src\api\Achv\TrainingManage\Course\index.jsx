import { request } from '@/utils/axios';

/**
 * 分页查询课程
 * @param {object} params 培训课程管理表保存dto
 * @param {number} params.pageNum 页码
 * @param {number} params.pageSize 每页记录数
 * @param {number} params.id
 * @param {number} params.tenantId
 * @param {array} params.ids
 * @param {array} params.tenantIds
 * @param {string} params.name 课程名称
 * @param {number} params.auditStatus 审核状态:1 审核中 2 审核不通过  3 审核通过
 * @param {number} params.courseStatus 课程状态： 0无效 1有效
 * @param {number} params.signUpStatus 是否需要报名状态： 0否 1是
 * @param {number} params.signUpAuditStatus 是否需要报名审核状态： 0否 1是
 * @param {number} params.examStatus 是否需要考试状态： 0否 1是
 * @returns
 */
export function getTrainingCoursePage(data = {}, option = {}) {
	return request({
		url: `/achv/training/queryTrainingCoursePage`,
		method: 'POST',
		data,
		...option,
	});
}

/**
 * 培训课程添加
 * @param {object} params 培训课程管理表保存dto
 * @param {number} params.id
 * @param {string} params.name 课程名称
 * @param {string} params.introduce 课程简介
 * @param {string} params.courseDesc 课程描述
 * @param {number} params.auditStatus 审核状态:1 审核中 2 审核不通过  3 审核通过
 * @param {number} params.courseStatus 课程状态： 0无效 1有效
 * @param {number} params.signUpStatus 是否需要报名状态： 0否 1是
 * @param {number} params.examStatus 是否需要考试状态： 0否 1是
 * @param {number} params.studentsNum 在学人员基数
 * @param {number} params.formDataId 报名表单id
 * @param {number} params.rankingNum 排序序号
 * @param {string} params.courseCoverUrl 课程封面图片地址
 * @param {string} params.posterImageUrl 海报地址
 * @param {string} params.shareDesc 分享文案
 * @param {string} params.timelineShareCoverUrl 朋友圈分享封面地址
 * @param {string} params.friendShareCoverUrl 好友分享封面地址
 * @param {string} params.reason 审核原因
 * @param {object} params.auditTime 审核时间
 * @param {array} params.subjectList 培训课程课时科目管理表保存dto
 * @param {array} params.staffList 培训课程人员管理表保存dto
 * @param {array} params.serviceList 培训课程人员管理表保存dto
 * @param {array} params.managerList 培训课程人员管理表保存dto
 * @returns
 */
export function saveTrainingCourse(data = {}, option = {}) {
	return request({
		url: `/achv/training/saveTrainingCourse`,
		method: 'POST',
		data,
		...option,
	});
}

/**
 * 培训课程修改
 * @param {object} params 培训课程管理表保存dto
 * @param {number} params.id
 * @param {string} params.name 课程名称
 * @param {string} params.introduce 课程简介
 * @param {string} params.courseDesc 课程描述
 * @param {number} params.auditStatus 审核状态:1 审核中 2 审核不通过  3 审核通过
 * @param {number} params.courseStatus 课程状态： 0无效 1有效
 * @param {number} params.signUpStatus 是否需要报名状态： 0否 1是
 * @param {number} params.examStatus 是否需要考试状态： 0否 1是
 * @param {number} params.studentsNum 在学人员基数
 * @param {number} params.formDataId 报名表单id
 * @param {number} params.rankingNum 排序序号
 * @param {string} params.courseCoverUrl 课程封面图片地址
 * @param {string} params.posterImageUrl 海报地址
 * @param {string} params.shareDesc 分享文案
 * @param {string} params.timelineShareCoverUrl 朋友圈分享封面地址
 * @param {string} params.friendShareCoverUrl 好友分享封面地址
 * @param {string} params.reason 审核原因
 * @param {object} params.auditTime 审核时间
 * @param {array} params.subjectList 培训课程课时科目管理表保存dto
 * @param {array} params.staffList 培训课程人员管理表保存dto
 * @param {array} params.serviceList 培训课程人员管理表保存dto
 * @param {array} params.managerList 培训课程人员管理表保存dto
 * @returns
 */
export function updateTrainingCourse(data = {}, option = {}) {
	return request({
		url: `/achv/training/updateTrainingCourse`,
		method: 'POST',
		data,
		...option,
	});
}

/**
 * 查询详情
 * @param {object} params 培训课程管理表保存dto
 * @param {number} params.pageNum 页码
 * @param {number} params.pageSize 每页记录数
 * @param {number} params.id
 * @param {number} params.tenantId
 * @param {array} params.ids
 * @param {array} params.tenantIds
 * @param {string} params.name 课程名称
 * @param {number} params.auditStatus 审核状态:1 审核中 2 审核不通过  3 审核通过
 * @param {number} params.courseStatus 课程状态： 0无效 1有效
 * @param {number} params.signUpStatus 是否需要报名状态： 0否 1是
 * @param {number} params.signUpAuditStatus 是否需要报名审核状态： 0否 1是
 * @param {number} params.examStatus 是否需要考试状态： 0否 1是
 * @returns
 */
export function getTrainingCourse(data = {}, option = {}) {
	return request({
		url: `/achv/training/getTrainingCourse`,
		method: 'POST',
		data,
		...option,
	});
}

/**
 * 批量删除
 * @param {object} params 培训课程管理表保存dto
 * @param {number} params.pageNum 页码
 * @param {number} params.pageSize 每页记录数
 * @param {number} params.id
 * @param {number} params.tenantId
 * @param {array} params.ids
 * @param {array} params.tenantIds
 * @param {string} params.name 课程名称
 * @param {number} params.auditStatus 审核状态:1 审核中 2 审核不通过  3 审核通过
 * @param {number} params.courseStatus 课程状态： 0无效 1有效
 * @param {number} params.signUpStatus 是否需要报名状态： 0否 1是
 * @param {number} params.signUpAuditStatus 是否需要报名审核状态： 0否 1是
 * @param {number} params.examStatus 是否需要考试状态： 0否 1是
 * @returns
 */
export function batchDeleteTrainingCourse(data = {}, option = {}) {
	return request({
		url: `/achv/training/batchDeleteTrainingCourse`,
		method: 'POST',
		data,
		...option,
	});
}

/**
 * 审核课程
 * @param {object} params 专家审核对象
 * @param {array} params.ids
 * @param {number} params.id id
 * @param {string} params.reason 不通过原因
 * @param {number} params.status 审核状态:1 审核中 2 审核不通过  3 审核通过
 * @returns
 */
export function auditTrainingCourse(data = {}, option = {}) {
	return request({
		url: `/achv/training/auditTrainingCourse`,
		method: 'POST',
		data,
		...option,
	});
}

/**
 * 更新课程状态
 * @param {object} params 培训课程管理表保存dto
 * @param {number} params.id
 * @param {string} params.name 课程名称
 * @param {string} params.introduce 课程简介
 * @param {string} params.courseDesc 课程描述
 * @param {number} params.auditStatus 审核状态:1 审核中 2 审核不通过  3 审核通过
 * @param {number} params.courseStatus 课程状态： 0无效 1有效
 * @param {number} params.signUpStatus 是否需要报名状态： 0否 1是
 * @param {number} params.examStatus 是否需要考试状态： 0否 1是
 * @param {number} params.studentsNum 在学人员基数
 * @param {number} params.formDataId 报名表单id
 * @param {number} params.rankingNum 排序序号
 * @param {string} params.courseCoverUrl 课程封面图片地址
 * @param {string} params.posterImageUrl 海报地址
 * @param {string} params.shareDesc 分享文案
 * @param {string} params.timelineShareCoverUrl 朋友圈分享封面地址
 * @param {string} params.friendShareCoverUrl 好友分享封面地址
 * @param {string} params.reason 审核原因
 * @param {object} params.auditTime 审核时间
 * @param {array} params.subjectList 培训课程课时科目管理表保存dto
 * @param {array} params.staffList 培训课程人员管理表保存dto
 * @param {array} params.serviceList 培训课程人员管理表保存dto
 * @param {array} params.managerList 培训课程人员管理表保存dto
 * @returns
 */
export function updateCourseStatus(data = {}, option = {}) {
	return request({
		url: `/achv/training/updateCourseStatus`,
		method: 'POST',
		data,
		...option,
	});
}

/**
 * 更新排序
 * @param {object} params 更新排序dto
 * @param {number} params.id
 * @param {number} params.rankingNum
 * @param {array} params.saveDtoList
 * @returns
 */
export function updateRankingNum(data = {}, option = {}) {
	return request({
		url: `/achv/training/updateRankingNum`,
		method: 'POST',
		data,
		...option,
	});
}

/**
 * 审核统计数量
 * @returns
 */
export function getStatistics(data = {}, option = {}) {
	return request({
		url: `/achv/training/getStatistics`,
		method: 'POST',
		data,
		...option,
	});
}
