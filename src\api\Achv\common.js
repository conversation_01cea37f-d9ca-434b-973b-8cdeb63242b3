import { request } from '@/utils/axios';

/**
 * 根据分类编码获取对应数据值
 * @param {object} params 分类类别值查询dto
 * @param {number} params.pageNum 页码
 * @param {number} params.pageSize 每页记录数
 * @param {number} params.id 需求id
 * @param {array} params.ids 需求id(批量操作传ids)
 * @param {string} params.categoryCode 分类编码code
 * @param {string} params.cityType
 * @returns
 */
export function queryCategoryByCode(params = {}, option = {}) {
	return request({
		url: `/achv/category/queryCategoryByCode`,
		method: 'POST',
		data: params,
		...option,
	});
}

/**
 * 读取成果配置
 * @param {object} params
 * @returns
 */
export function getConfig(params = {}, option = {}) {
	return request({
		url: `/achv/config/getConfig`,
		method: 'POST',
		data: params,
		...option,
	});
}

/**
 * 保存成果配置
 * @param {object} params
 * @returns
 */
export function saveConfig(params = {}, option = {}) {
	return request({
		url: `/achv/config/save`,
		method: 'POST',
		data: params,
		...option,
	});
}
