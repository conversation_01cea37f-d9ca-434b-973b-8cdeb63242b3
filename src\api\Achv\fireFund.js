/**
 * @description fireFund - 文件描述
 * <AUTHOR>
 *
 * Created on 2024/9/20 15:42
 */
import { request } from "@/utils/axios";

/**
 * 分页查询点燃基金申请列表
 * @param {object} params IgniteFundQueryDto
 * @param {number} params.pageNum 页码
 * @param {number} params.pageSize 每页记录数
 * @param {number} params.id
 * @param {number} params.tenantId
 * @returns `/gbac-tip-achv-service/achv/igniteFund/page`
 */
export function pagePolicyToArea(params) {
    return request({
        url: `/achv/igniteFund/page`,
        method: "POST",
        data: params,
    });
}

/**
 * 获取点燃基金详情
 * @param {object} params IgniteFundQueryDto
 * @param {number} params.pageNum 页码
 * @param {number} params.pageSize 每页记录数
 * @param {number} params.id
 * @param {number} params.tenantId
 * @returns`/gbac-tip-achv-service/achv/igniteFund/getDetail`
 */
export function getFundDetail(params) {
    return request({
        url: `/achv/igniteFund/getDetail`,
        method: "POST",
        data: params,
    });
}