/**
 * @description AptitudeConfig - 文件描述
 * <AUTHOR>
 *
 * Created on 2025/6/9 16:57
 */
import { request } from '@/utils/axios';

/**
 * 新增资质
 * @param {object} params 资质管理
 * @param option
 * @param {number} params.id id
 * @param {string} params.aptitudeName 资质名称
 * @param {number} params.rankingNum 排序
 * @returns
 */
export function addAptitude(params = {}, option = {}) {
	return request({
		url: `/crm/aptitude/add`,
		method: 'POST',
		data: params,
		...option,
	});
}

/**
 * 删除资质
 * @param {object} params 资质管理
 * @param option
 * @returns
 */
export function delAptitude(params = {}, option = {}) {
	return request({
		url: `/crm/aptitude/delete`,
		method: 'POST',
		data: params,
		...option,
	});
}

/**
 * 分页查询资质
 * @param {object} params 资质管理
 * @param option
 * @param {number} params.pageNum 页码
 * @param {number} params.pageSize 每页记录数
 * @param {number} params.id id
 * @param {number} params.tenantId id
 * @param {string} params.aptitudeName 资质名称
 * @param {array} params.ids id
 * @returns
 */
export function pageAptitude(params = {}, option = {}) {
	return request({
		url: `/crm/aptitude/page`,
		method: 'POST',
		data: params,
		...option,
	});
}

/**
 * 修改资质
 * @param {object} params 资质管理
 * @param option
 * @param {number} params.id id
 * @param {string} params.aptitudeName 资质名称
 * @param {number} params.rankingNum 排序
 * @returns
 */
export function updateAptitude(params = {}, option = {}) {
	return request({
		url: `/crm/aptitude/update`,
		method: 'POST',
		data: params,
		...option,
	});
}

/**
 * 更新排序
 * @param {object} params 资质管理
 * @param option
 * @param {number} params.id id
 * @param {string} params.aptitudeName 资质名称
 * @param {number} params.rankingNum 排序
 * @returns
 */
export function updateAptitudeRanking(params = {}, option = {}) {
	return request({
		url: `/crm/aptitude/updateSort`,
		method: 'POST',
		data: params,
		...option,
	});
}
