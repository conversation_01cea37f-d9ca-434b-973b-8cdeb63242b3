import { request } from '@/utils/axios';

/**
 * 查询招商单位
 * @param {object} params
 * @param {} option
 */
export function listInvestmentResponsibleDept(params = {}, option = {}) {
	return request({
		url: `/bidmgt/project/listInvestmentResponsibleDept`,
		method: 'POST',
		data: params,
		...option,
	});
}

/**
 * 保存招商单位
 * @param {array} params
 * @param {string} params.deptId 部门id
 * @param {string} params.manageStatus 管理状态：0不是管理 1是管理
 * @param {} option
 */
export function saveInvestmentResponsibleDept(params = {}, option = {}) {
	return request({
		url: `/bidmgt/project/saveInvestmentResponsibleDept`,
		method: 'POST',
		data: params,
		...option,
	});
}

/**
 * 查询招商单位及员工
 * @param {array} params
 * @param {string} params.deptId 部门id
 * @param {string} params.manageStatus 管理状态：0不是管理 1是管理
 * @param {} option
 */
export function listInvestmentResponsibleDeptUser(params = {}, option = {}) {
	return request({
		url: `/bidmgt/project/listInvestmentResponsibleDeptUser`,
		method: 'POST',
		data: params,
		...option,
	});
}


/** 
 * 查询责任用户列表
 * @param {object} params 招商责任用户查询
 * @param {number} params.id 
 * @param {array} params.deptIds 部门id
 * @param {array} params.ids ids
 * @param {string} params.phone 
 * @returns
 */
export function listInvestmentResponsibleUser(params = {}, option = {}) {
	return request({
		url: `/bidmgt/project/listInvestmentResponsibleUser`,
		method: 'POST',
		data: params,
		refreshCache: [
			'/bidmgt/project/saveInvestmentResponsibleUser',
			'/bidmgt/project/updateInvestmentResponsibleUser',
			'/bidmgt/project/batchDelInvestmentResponsibleUser',
		],
		...option,
	});
}

/** 
 * 查询责任用户
 * @param {object} params 招商责任用户查询
 * @param {number} params.id 
 * @param {array} params.deptIds 部门id
 * @param {array} params.ids ids
 * @param {string} params.phone 
 * @returns
 */
export function getInvestmentResponsibleUser(params = {}, option = {}) {
	return request({
		url: `/bidmgt/project/getInvestmentResponsibleUser`,
		method: 'POST',
		data: params,
		...option,
	});
}

  /** 
 * 保存责任用户
 * @param {object} params 招商责任部门表
 * @param {number} params.id 
 * @param {number} params.deptId 部门id
 * @param {number} params.systemUserId 系统表用户id
 * @param {string} params.userName Schema
 * @param {string} params.phone 手机号码
 * @param {string} params.extendField 扩展字段
 * @param {string} params.userType 用户类型
 * @returns
 */
export function saveInvestmentResponsibleUser(params = {}, option = {}) {
	return request({
		url: `/bidmgt/project/saveInvestmentResponsibleUser`,
		method: 'POST',
		data: params,
		...option,
	});
}

  /** 
 * 修改责任用户
 * @param {object} params 招商责任部门表
 * @param {number} params.id 
 * @param {number} params.deptId 部门id
 * @param {number} params.systemUserId 系统表用户id
 * @param {string} params.userName Schema
 * @param {string} params.phone 手机号码
 * @param {string} params.extendField 扩展字段
 * @param {string} params.userType 用户类型
 * @returns
 */
export function updateInvestmentResponsibleUser(params = {}, option = {}) {
	return request({
		url: `/bidmgt/project/updateInvestmentResponsibleUser`,
		method: 'POST',
		data: params,
		...option,
	});
}
  /** 
 * 删除责任用户
 * @param {object} params 招商责任用户查询
 * @param {number} params.id 
 * @param {array} params.deptIds 部门id
 * @param {array} params.ids ids
 * @param {string} params.phone 
 * @returns
 */
export function batchDelInvestmentResponsibleUser(params = {}, option = {}) {
	return request({
		url: `/bidmgt/project/batchDelInvestmentResponsibleUser`,
		method: 'POST',
		data: params,
		...option,
	});
}