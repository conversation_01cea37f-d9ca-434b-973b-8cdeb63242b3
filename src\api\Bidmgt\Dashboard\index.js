import { request } from '@/utils/axios';

/**
 * 获取全部项目统计
 * @returns
 */
export function allProjectStatistics(params = {}, option = {}) {
	return request({
		url: `/bidmgt/project/allProjectStatistics`,
		method: 'POST',
		data: params,
		...option,
	});
}

/**
 * 获取近半年项目新增统计
 * @returns
 */
export function projectSixMonthStatistics(params = {}, option = {}) {
	return request({
		url: `/bidmgt/project/projectSixMonthStatistics`,
		method: 'POST',
		data: params,
		...option,
	});
}

/**
 * 各街道集团项目统计
 * @returns
 */
export function statistics(params = {}, option = {}) {
	return request({
		url: `/bidmgt/project/statistics`,
		method: 'POST',
		data: params,
		...option,
	});
}

/**
 * 统计消息未读数量
 * @returns
 */
export function statisticsUnreadNumber(params = {}, option = {}) {
	return request({
		url: `/bidmgt/project/statisticsUnreadNumber`,
		method: 'POST',
		data: params,
		...option,
	});
}
/**
 * 首页统计
 * @param {object} params 项目统计查询dto
 * @param {number} params.signStatus 项目签约状态：0 未签约 1 已签约
 * @param {number} params.keyProjectStatus 重点项目状态 ：0 不是重点 1是重点
 * @param {number} params.openingStatus 是否开工开业状态：0 否 1是
 * @param {number} params.reviewMeetingStatus 过会状态： 0未过会 1已过会
 * @param {number} params.negotiationStatus 洽谈状态：0 未洽谈 1洽谈中
 * @returns
 */
export function indexStatistics(params = {}, option = {}) {
	return request({
		url: `/bidmgt/project/indexStatistics`,
		method: 'POST',
		data: params,
		...option,
	});
}

/** 
 * 分页查询任务中心数据
 * @param {object} params 商情分派审核查询dto
 * @param {number} params.pageNum 页码
 * @param {number} params.pageSize 每页记录数
 * @param {number} params.id 分派审核id(主键id)
 * @param {number} params.projectIntelligenceId 
 * @param {number} params.projectId 
 * @param {number} params.operateUserId 待操作(审核/认领)用户id
 * @param {number} params.operateStatus 操作状态：0 等待操作  1 操作拒绝/不通过 2 操作同意/通过
 * @param {number} params.operateType 操作类型：1 审核  2 认领
 * @param {number} params.projectType 
 * @param {array} params.createDeptIds 
 * @param {number} params.toDoListType 待办事项类型：1 项目认领   2 项目审核 3 商情审核 4 商情认领 5 已处理任务 6全部待处理
 * @param {array} params.operateStatusList 
 * @returns
 */
export function taskCenterPage(params = {}, option = {}) {
	return request({
		url: `/bidmgt/project/taskCenterPage`,
		method: 'POST',
		data: params,
		...option,
	});
}

/** 
* 更新任务中心已读
* @param {object} params 商情分派审核查询dto
* @param {number} params.pageNum 页码
* @param {number} params.pageSize 每页记录数
* @param {number} params.id 分派审核id(主键id)
* @param {number} params.projectIntelligenceId 
* @param {number} params.projectId 
* @param {number} params.operateUserId 待操作(审核/认领)用户id
* @param {number} params.operateStatus 操作状态：0 等待操作  1 操作拒绝/不通过 2 操作同意/通过
* @param {number} params.operateType 操作类型：1 审核  2 认领
* @param {number} params.projectType 
* @param {array} params.createDeptIds 
* @param {number} params.toDoListType 待办事项类型：1 项目认领   2 项目审核 3 商情审核 4 商情认领 5 已处理任务 6全部待处理
* @param {array} params.operateStatusList 
* @returns
*/
export function updateTaskCenterReadFlag(params = {}, option = {}) {
	return request({
		url: `/bidmgt/project/updateTaskCenterReadFlag`,
		method: 'POST',
		data: params,
		...option,
	});
}


/** 
* 任务中心数量统计
* @param {object} params 项目管理内容查询dto
* @returns
*/
export function taskCenterStatistics(params = {}, option = {}) {
	return request({
		url: `/bidmgt/project/taskCenterStatistics`,
		method: 'POST',
		data: params,
		...option,
	});
}
