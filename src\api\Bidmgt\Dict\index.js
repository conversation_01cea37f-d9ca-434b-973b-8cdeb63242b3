import { request } from '@/utils/axios';
/**
 * 新增分类类别值
 * @param {object} params 后台管理端：分类类别值保存dto
 * @param {number} params.id 主键id,修改时传
 * @param {string} params.value 值名称
 * @param {number} params.serialNo 序号
 * @param {string} params.categoryCode 分类code
 * @param {string} params.iconUrl 图标url地址
 * @param {string} params.iconActiveUrl 选中高亮的图标url地址
 * @returns
 */
export function add(params = {}, option = {}) {
	return request({
		url: `/bidmgt/category/add`,
		method: 'POST',
		data: params,
		...option,
	});
}
/**
 * 批量根据id删除数据
 * @param {object} params 分类类别值查询dto
 * @param {number} params.pageNum 页码
 * @param {number} params.pageSize 每页记录数
 * @param {number} params.id 需求id
 * @param {array} params.ids 需求id(批量操作传ids)
 * @param {string} params.categoryCode 分类编码code
 * @param {string} params.cityType
 * @returns
 */
export function batchDel(params = {}, option = {}) {
	return request({
		url: `/bidmgt/category/batchDel`,
		method: 'POST',
		data: params,
		...option,
	});
}
/**
 * 根据id查询数据
 * @param {object} params 分类类别值查询dto
 * @param {number} params.pageNum 页码
 * @param {number} params.pageSize 每页记录数
 * @param {number} params.id 需求id
 * @param {array} params.ids 需求id(批量操作传ids)
 * @param {string} params.categoryCode 分类编码code
 * @param {string} params.cityType
 * @returns
 */
export function getCategoryValue(params = {}, option = {}) {
	return request({
		url: `/bidmgt/category/getCategoryValue`,
		method: 'POST',
		data: params,
		...option,
	});
}
/**
 * 分页查询分类类别值
 * @param {object} params 分类类别值查询dto
 * @param {number} params.pageNum 页码
 * @param {number} params.pageSize 每页记录数
 * @param {number} params.id 需求id
 * @param {array} params.ids 需求id(批量操作传ids)
 * @param {string} params.categoryCode 分类编码code
 * @param {string} params.cityType
 * @returns
 */
export function pageCategoryValue(params = {}, option = {}) {
	return request({
		url: `/bidmgt/category/pageCategoryValue`,
		method: 'POST',
		data: params,
		...option,
	});
}

/**
 * 列表查询配置项目值
 * @param {object} params 分类类别值查询dto
 * @param {number} params.pageNum 页码
 * @param {number} params.pageSize 每页记录数
 * @param {number} params.id 需求id
 * @param {array} params.ids 需求id(批量操作传ids)
 * @param {string} params.categoryCode 分类编码code
 * @param {string} params.name 分类名称
 * @param {string} params.cityType
 * @returns
 */
export function listCategoryValue(params = {}, option = {}) {
	return request({
		url: `/bidmgt/category/listCategoryValue`,
		method: 'POST',
		data: params,
		...option,
	});
}

/**
 * 根据分类编码获取对应数据值
 * @param {object} params 分类类别值查询dto
 * @param {number} params.pageNum 页码
 * @param {number} params.pageSize 每页记录数
 * @param {number} params.id 需求id
 * @param {array} params.ids 需求id(批量操作传ids)
 * @param {string} params.categoryCode 分类编码code
 * @param {string} params.cityType
 * @returns
 */
export function queryCategoryByCode(params = {}, option = {}) {
	return request({
		url: `/bidmgt/category/queryCategoryByCode`,
		method: 'POST',
		data: params,
		...option,
	});
}
/**
 * 修改分类类值
 * @param {object} params 后台管理端：分类类别值保存dto
 * @param {number} params.id 主键id,修改时传
 * @param {string} params.value 值名称
 * @param {number} params.serialNo 序号
 * @param {string} params.categoryCode 分类code
 * @param {string} params.iconUrl 图标url地址
 * @param {string} params.iconActiveUrl 选中高亮的图标url地址
 * @returns
 */
export function update(params = {}, option = {}) {
	return request({
		url: `/bidmgt/category/update`,
		method: 'POST',
		data: params,
		...option,
	});
}
