import { request } from '@/utils/axios'

/**
 * 招商 分页查询我的项目
 * @param {object} params 项目管理内容查询dto
 * @param {number} params.pageNum 页码
 * @param {number} params.pageSize 每页记录数
 * @param {number} params.id id
 * @param {array} params.ids ids
 * @param {number} params.industryId 产业id
 * @param {string} params.projectName 项目名称
 * @param {string} params.projectCode 项目编号
 * @param {number} params.investmentResponsibleId  招商责任单位id
 * @param {number} params.projectStageId 项目阶段id
 * @param {number} params.projectTypeId 项目类型id
 * @param {number} params.coordinatedResolutionStatus 是否需要区领导协调解决： 0 否 1是
 * @param {number} params.investmentScaleStart 投资规模范围开始值(单位：亿元)
 * @param {number} params.investmentScaleEnd 投资规模范围结束值(单位：亿元)
 * @param {string} params.createTimeStart 上报开始时间
 * @param {string} params.createTimeEnd 上报结束时间
 * @param {number} params.reviewMeetingStatus 过会状态： 0未过会 1已过会
 * @param {number} params.userId
 * @returns
 */
export function myProjectPage(params = {}, option = {}) {
	return request({
		url: `/bidmgt/project/myProjectPage`,
		method: 'POST',
		data: params,
		...option,
	})
}

/**
 * 招商 分页查询消息列表
 * @param {object} params 通知查询条件
 * @param {number} params.pageNum 页码
 * @param {number} params.pageSize 每页记录数
 * @param {number} params.id 消息id
 * @param {number} params.readFlag 已读标记(0:未读,1:已读)
 * @param {number} params.businessCodeList 业务类型：BIDMGT_PROJECT_AUDIT (项目审核通知code) , BIDMGT_PROJECT_ASSIGN(项目指派通知CODE) , BIDMGT_PROJECT_REFUSE(项目指派拒绝认领通知CODE) ,BIDMGT_PROJECT_CLAIM(项目指派认领通知CODE), BIDMGT_PROJECT_AUDIT_PASS(项目审核通过通知CODE), BIDMGT_PROJECT_AUDIT_NO_PASS(项目审核不通过ODE)
 * @returns
 * 	预警类型：1预警 2催办 3消息 4 指派消息 5 项目审核消息 6 审核通过消息 7 审核不通过消息 8 接受认领消息 9 拒绝认领消息
 */
export function notificationPage(params = {}, option = {}) {
	return request({
		url: `/bidmgt/project/notificationPage`,
		method: 'POST',
		data: params,
		...option,
	})
}

/**
 * 招商 更新消息已读
 * @param {object} params 通知查询条件
 * @param {number} params.id 消息id
 * @param {number} params.readFlag 已读标记(0:未读,1:已读)
 * @returns
 */
export function updateReadFlag(params = {}, option = {}) {
	return request({
		url: `/bidmgt/project/updateReadFlag`,
		method: 'POST',
		data: params,
		...option,
	})
}
/** 
 * 审核操作
 * @param {object} params 项目分派dto
 * @param {number} params.id 
 * @param {number} params.auditStatus 审核状态：0 等待审核 1 审核不通过 2 审核通过
 * @param {string} params.auditDesc 审核描述
 * @returns
 */
export function auditProject(data = {}, option = {}) {
	return request({
		url: `/bidmgt/project/auditProject`,
		method: 'POST',
		data: data,
		...option,
	})
}

/** 
* 认领指派操作
* @param {object} params 项目分派dto
* @param {number} params.id 
* @param {number} params.claimStatus 责任单位认领状态：0 待认领 1 拒绝认领 2 同意认领
* @param {string} params.claimDesc 认领描述
* @returns
*/
export function claimProjectAssign(data = {}, option = {}) {
	return request({
		url: `/bidmgt/project/claimProjectAssign`,
		method: 'POST',
		data: data,
		...option,
	})
}

/** 
 * 判断是否有审核权限
 * @param {number} params.id id
 * @param {array} params.ids ids
 * @param {number} params.industryId 产业id
 * @param {string} params.projectName 项目名称
 * @param {string} params.projectCode 项目编号
 * @param {number} params.activityId 所属活动id
 * @returns
 */
export function checkAuditPermission(data = {}, option = {}) {
	return request({
		url: `/bidmgt/project/checkAuditPermission`,
		method: 'POST',
		data: data,
		...option,
	})
}

/** 
 * 判断是否有审核权限
 * @param {object} params 项目情报查询dto
 * @param {number} params.pageNum 页码
 * @param {number} params.pageSize 每页记录数
 * @param {array} params.ids ids
 * @param {number} params.id id
 * @param {number} params.userId 
 * @param {number} params.auditStatus 0代审核  1 审核不通过 2 审核通过
 * @param {array} params.investmentResponsibleIds 跟进单位ids
 * @param {array} params.acceptStatus 采纳状态：0等待采纳 1 不通过采纳 2 通过采纳
 * @param {string} params.projectName 
 * @param {string} params.startTime 开始时间,格式：yyyy-MM-dd HH:mm:ss
 * @param {string} params.endTime 结束时间,格式：yyyy-MM-dd HH:mm:ss
 * @returns
 */
export function projectIntelligenceCheckAuditPermission(data = {}, option = {}) {
	return request({
		url: `/bidmgt/projectIntelligence/checkAuditPermission`,
		method: 'POST',
		data: data,
		...option,
	})
  }