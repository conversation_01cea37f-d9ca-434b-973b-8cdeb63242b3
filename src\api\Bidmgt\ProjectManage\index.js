import { request } from '@/utils/axios';

/**
 * 分页查询项目
 * @param {object} params 项目管理内容查询dto
 * @param {number} params.pageNum 页码
 * @param {number} params.pageSize 每页记录数
 * @param {number} params.id id
 * @param {array} params.ids ids
 * @param {string} params.projectName 项目名称
 * @param {string} params.projectCode 项目编号
 * @param {number} params.investmentResponsibleId  招商责任单位id
 * @param {number} params.projectStageId 项目阶段id
 * @param {number} params.projectTypeId 项目类型id
 * @param {number} params.coordinatedResolutionStatus 是否需要区领导协调解决： 0 否 1是
 * @param {number} params.userId
 * @returns
 */
export function pageProject(params = {}, option = {}) {
	return request({
		url: `/bidmgt/project/pageProject`,
		method: 'POST',
		data: params,
		...option,
	});
}

/**
 * 新增项目
 * @param {object} params 项目管理内容保存dto
 * @param {number} params.id
 * @param {string} params.projectName 项目名称
 * @param {string} params.investmentEnterprisesName 投资企业名称
 * @param {string} params.investmentScale 投资规模(单位：亿元)
 * @param {string} params.totalAreaUsed 使用空间总面积
 * @param {number} params.investmentResponsibleId  招商责任单位id
 * @param {string} params.investmentContent 招商内容
 * @param {number} params.projectStageId 项目阶段id
 * @param {number} params.logOffStatus 是否销号项目： 0 否 1是
 * @param {number} params.signStatus 项目签约状态：0 未签约 1 已签约
 * @param {number} params.projectTypeId 项目类型id
 * @param {number} params.coordinatedResolutionStatus 是否需要区领导协调解决： 0 否 1是
 * @param {string} params.latestDevelopments 最新进展
 * @param {string} params.existingProblems 存在问题
 * @param {string} params.projectLeaderContactInformation 项目包保领导联系方式
 * @param {number} params.projectHandlingId 项目经办责任人id
 * @param {string} params.districtBusinessTracker 商务局项目跟踪人
 * @param {array} params.industryList 产业id
 * @param {string} params.projectCode 项目编号
 * @returns
 */
export function addProject(params = {}, option = {}) {
	return request({
		url: `/bidmgt/project/addProject`,
		method: 'POST',
		data: params,
		...option,
	});
}
/**
 * 修改项目
 * @param {object} params 项目管理内容保存dto
 * @param {number} params.id
 * @param {string} params.projectName 项目名称
 * @param {string} params.investmentEnterprisesName 投资企业名称
 * @param {number} params.investmentScale 投资规模(单位：亿元)
 * @param {string} params.totalAreaUsed 使用空间总面积
 * @param {number} params.investmentResponsibleId  招商责任单位id
 * @param {string} params.investmentContent 招商内容
 * @param {number} params.projectStageId 项目阶段id
 * @param {number} params.logOffStatus 是否销号项目： 0 否 1是
 * @param {number} params.signStatus 项目签约状态：0 未签约 1 已签约
 * @param {number} params.projectTypeId 项目类型id
 * @param {number} params.coordinatedResolutionStatus 是否需要区领导协调解决： 0 否 1是
 * @param {string} params.latestDevelopments 最新进展
 * @param {string} params.existingProblems 存在问题
 * @param {string} params.projectLeaderContactInformation 项目包保领导联系方式
 * @param {number} params.projectHandlingId 项目经办责任人id
 * @param {string} params.districtBusinessTracker 商务局项目跟踪人
 * @param {array} params.industryList 产业id
 * @param {string} params.projectCode 项目编号
 * @param {number} params.reviewMeetingStatus 过会状态： 0未过会 1已过会
 * @returns
 */
export function updateProject(params = {}, option = {}) {
	return request({
		url: `/bidmgt/project/updateProject`,
		method: 'POST',
		data: params,
		...option,
	});
}
/**
 * 获取项目详情
 * @param {object} params 项目管理内容查询dto
 * @param {number} params.id id
 * @returns
 */
export function detailProject(params = {}, option = {}) {
	return request({
		url: `/bidmgt/project/detailProject`,
		method: 'POST',
		data: params,
		...option,
	});
}
/**
 * 批量删除项目
 * @param {object} params 项目管理内容查询dto
 * @param {array} params.ids ids
 * @returns
 */
export function batchDelProject(params = {}, option = {}) {
	return request({
		url: `/bidmgt/project/batchDelProject`,
		method: 'POST',
		data: params,
		...option,
	});
}

/**
 * 新增跟进记录
 * @param {object} params 项目跟进记录保存dto
 * @param {number} params.projectId 项目id
 * @param {string} params.latestDevelopments 最新进展
 * @param {string} params.dynamicKeywords 动态关键字
 * @param {string} params.recorder 记录人
 * @param {string} params.contactInformation 联系方式
 * @param {number} params.departmentId 所属部门id
 * @returns
 */
export function addFollowUpRecord(params = {}, option = {}) {
	return request({
		url: `/bidmgt/followUpRecord/addFollowUpRecord`,
		method: 'POST',
		data: params,
		...option,
	});
}

/**
 * 根据项目id删除跟进记录
 * @param {object} params 项目跟进记录查询dto
 * @param {number} params.projectId 项目id
 * @param {array} params.ids ids
 * @returns
 */
export function delFollowUpRecord(params = {}, option = {}) {
	return request({
		url: `/bidmgt/followUpRecord/batchDelFollowUpRecord`,
		method: 'POST',
		data: params,
		...option,
	});
}

/**
 * 根据项目id删除跟进记录
 * @param {object} params 项目跟进记录查询dto
 * @param {number} params.projectId 项目id
 * @param {number} params.id id
 * @param {array} params.ids ids
 * @returns
 */
export function delByProjectId(params) {
	return request({
		url: `/bidmgt/followUpRecord/delByProjectId`,
		method: 'POST',
		data: params,
	});
}

/**
 * 获取跟进记录详情
 * @param {object} params 项目跟进记录查询dto
 * @param {number} params.projectId 项目id
 * @param {number} params.id id
 * @param {array} params.ids ids
 * @returns
 */
export function detailFollowUpRecord(params = {}, option = {}) {
	return request({
		url: `/bidmgt/followUpRecord/detailFollowUpRecord`,
		method: 'POST',
		data: params,
		...option,
	});
}
/**
 * 批量查询跟进记录
 * @param {object} params 项目跟进记录查询dto
 * @param {number} params.projectId 项目id
 * @returns
 */
export function listFollowUpRecord(params = {}, option = {}) {
	return request({
		url: `/bidmgt/followUpRecord/listFollowUpRecord`,
		method: 'POST',
		data: params,
		...option,
	});
}

/**
 * 新增-取消跟踪项目
 * @param {object} params 跟踪的项目保存dto
 * @param {number} params.projectId 项目id
 * @returns
 */
export function projectFollow(params = {}, option = {}) {
	return request({
		url: `/bidmgt/projectFollow/projectFollow`,
		method: 'POST',
		data: params,
		...option,
	});
}

/**
 * 分页查询跟踪项目
 * @param {object} params 跟踪的项目查询dto
 * @param {number} params.pageNum 页码
 * @param {number} params.pageSize 每页记录数
 * @param {number} params.userId
 * @returns
 */
export function pageProjectFollow(params = {}, option = {}) {
	return request({
		url: `/bidmgt/projectFollow/pageProjectFollow`,
		method: 'POST',
		data: params,
		...option,
	});
}
/**
 * 发送项目预警
 * @param {object} params 发送项目预警内容dto
 * @param {number} params.projectId 项目id
 * @param {string} params.earlyWarningContent 预警内容
 * @returns
 */
export function sendEarlyWarning(params = {}, option = {}) {
	return request({
		url: `/bidmgt/project/sendEarlyWarning`,
		method: 'POST',
		data: params,
		...option,
	});
}

/**
 *  更新项目阶段
 * @param {object} params 项目管理阶段时间更新dto
 * @param {number} params.updateProjectStageId 项目阶段id
 * @param {number} params.projectId 项目id
 * @returns
 */
export function updateProjectStage(params = {}, option = {}) {
	return request({
		url: `/bidmgt/project/updateProjectStage`,
		method: 'POST',
		data: params,
		...option,
	});
}

/**
 * 项目导入
 * @param {string} file
 * @returns
 */
export function projectImport(params = {}, option = {}) {
	return request({
		url: `/bidmgt/project/projectImport`,
		method: 'POST',
		data: params,
		header: {
			'Content-Type': 'multipart/form-data',
		},
		...option,
	});
}

/**
 * 项目导出
 * @param {object} params 项目管理内容查询dto
 * @param {number} params.pageNum 页码
 * @param {number} params.pageSize 每页记录数
 * @param {number} params.id id
 * @param {array} params.ids ids
 * @param {number} params.industryId 产业id
 * @param {string} params.projectName 项目名称
 * @param {string} params.projectCode 项目编号
 * @param {number} params.investmentResponsibleId  招商责任单位id
 * @param {number} params.projectStageId 项目阶段id
 * @param {number} params.projectTypeId 项目类型id
 * @param {number} params.coordinatedResolutionStatus 是否需要区领导协调解决： 0 否 1是
 * @param {number} params.investmentScaleStart 投资规模范围开始值(单位：亿元)
 * @param {number} params.investmentScaleEnd 投资规模范围结束值(单位：亿元)
 * @param {string} params.createTimeStart 上报开始时间
 * @param {string} params.createTimeEnd 上报结束时间
 * @param {number} params.reviewMeetingStatus 过会状态： 0未过会 1已过会
 * @param {number} params.userId
 * @returns
 */
export function projectExport(params = {}, option = {}) {
	return request({
		url: `/bidmgt/project/projectExport`,
		method: 'POST',
		data: params,
		...option,
	});
}

/**
 * 新增项目问题
 * @param {object} params 项目管理内容dto
 * @param {number} params.id
 * @param {number} params.projectId 项目id
 * @param {number} params.solveStatus 解决状态：0 待解决 1 已解决
 * @param {number} params.coordinatedResolutionStatus 是否需要区领导协调解决： 0 否 1是
 * @param {string} params.questionContent 问题内容
 * @returns
 */
export function addProjectProblem(params = {}, option = {}) {
	return request({
		url: `/bidmgt/projectProblem/addProjectProblem`,
		method: 'POST',
		data: params,
		...option,
	});
}

/**
 * 批量删除项目问题
 * @param {object} params 项目管理问题查询dto
 * @param {number} params.id id
 * @param {array} params.ids ids
 * @param {number} params.projectId 项目id
 * @returns
 */
export function batchDelProjectProblem(params = {}, option = {}) {
	return request({
		url: `/bidmgt/projectProblem/batchDelProjectProblem`,
		method: 'POST',
		data: params,
		...option,
	});
}

/**
 * 获取项目问题详情
 * @param {object} params 项目管理问题查询dto
 * @param {number} params.id id
 * @param {array} params.ids ids
 * @param {number} params.projectId 项目id
 * @returns
 */
export function detailProjectProblem(params = {}, option = {}) {
	return request({
		url: `/bidmgt/projectProblem/detailProjectProblem`,
		method: 'POST',
		data: params,
		...option,
	});
}

/**
 * 列表查询项目
 * @param {object} params 项目管理问题查询dto
 * @param {number} params.id id
 * @param {array} params.ids ids
 * @param {number} params.projectId 项目id
 * @returns
 */
export function listProjectProblem(params = {}, option = {}) {
	return request({
		url: `/bidmgt/projectProblem/listProjectProblem`,
		method: 'POST',
		data: params,
		...option,
	});
}

/**
 * 修改项目问题
 * @param {object} params 项目管理内容dto
 * @param {number} params.id
 * @param {number} params.projectId 项目id
 * @param {number} params.solveStatus 解决状态：0 待解决 1 已解决
 * @param {number} params.coordinatedResolutionStatus 是否需要区领导协调解决： 0 否 1是
 * @param {string} params.questionContent 问题内容
 * @returns
 */
export function updateProjectProblem(params = {}, option = {}) {
	return request({
		url: `/bidmgt/projectProblem/updateProjectProblem`,
		method: 'POST',
		data: params,
		...option,
	});
}

/**
 * 批量删除项目评价
 * @param {object} params 项目评价查询dto
 * @param {number} params.id id
 * @param {array} params.ids ids
 * @param {number} params.projectId 项目id
 * @param {number} params.evaluationLeaderId
 * @returns
 */
export function batchDelProjectEvaluate(params = {}, option = {}) {
	return request({
		url: `/bidmgt/project/batchDelProjectEvaluate`,
		method: 'POST',
		data: params,
		...option,
	});
}

/**
 * 分页查询我的项目
 * @param {object} params 项目管理内容查询dto
 * @param {number} params.pageNum 页码
 * @param {number} params.pageSize 每页记录数
 * @param {number} params.auditByUserId 审核人id
 * @param {number} params.auditStatus 0代审核  1 审核不通过 2 审核通过
 * @returns
 */
export function myProjectPage(data = {}, options = {}) {
	return request({
		url: `/bidmgt/project/myProjectPage`,
		method: 'POST',
		data,
		...options,
	});
}

/**
 * 分页查询关联部门的项目
 * @param {object} params 项目管理内容查询dto
 * @param {number} params.pageNum 页码
 * @param {number} params.pageSize 每页记录数
 * @param {number} params.auditByUserId 审核人id
 * @param {number} params.auditStatus 0代审核  1 审核不通过 2 审核通过
 * @returns
 */
export function myDeptProjectPage(data = {}, options = {}) {
	return request({
		url: `/bidmgt/project/myDeptProjectPage`,
		method: 'POST',
		data,
		...options,
	});
}
/**
 * 更新排序
 * @param {object} params
 * @param {number} params.id 项目id
 * @param {number} params.rankingNum 排序数
 * @returns
 */
export function updateProjectSort(data = {}, options = {}) {
	return request({
		url: `/bidmgt/project/updateSort`,
		method: 'POST',
		data,
		...options,
	});
}
