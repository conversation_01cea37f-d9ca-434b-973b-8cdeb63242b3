import { request } from '@/utils/axios';

/**
 * 分页查询情报
 * @param {object} params 项目情报查询dto
 * @param {number} params.pageNum 页码
 * @param {number} params.pageSize 每页记录数
 * @param {array} params.ids ids
 * @param {number} params.id id
 * @param {string} params.enterpriseNature 企业性质
 * @param {array} params.enterpriseNatureIds 企业性质ids
 * @param {string} params.industryChain 产业链
 * @param {string} params.projectName
 * @param {string} params.startTime 开始时间,格式：yyyy-MM-dd HH:mm:ss
 * @param {string} params.endTime 结束时间,格式：yyyy-MM-dd HH:mm:ss
 * @returns
 */

export function pageSituation(data = {}, options = {}) {
	return request({
		url: `/bidmgt/projectIntelligence/page`,
		method: 'POST',
		data,
		...options,
	});
}

/**
 * 分页查询我的部门情报
 * @param {object} params 项目情报查询dto
 * @param {number} params.pageNum 页码
 * @param {number} params.pageSize 每页记录数
 * @param {array} params.ids ids
 * @param {number} params.id id
 * @param {string} params.enterpriseNature 企业性质
 * @param {array} params.enterpriseNatureIds 企业性质ids
 * @param {string} params.industryChain 产业链
 * @param {string} params.projectName
 * @param {string} params.startTime 开始时间,格式：yyyy-MM-dd HH:mm:ss
 * @param {string} params.endTime 结束时间,格式：yyyy-MM-dd HH:mm:ss
 * @returns
 */

export function myDeptProjectIntelligencePage(data = {}, options = {}) {
	return request({
		url: `/bidmgt/projectIntelligence/myDeptProjectIntelligencePage`,
		method: 'POST',
		data,
		...options,
	});
}

/**
 * 情报详情
 * @param {object} params 项目情报查询dto
 * @param {number} params.pageNum 页码
 * @param {number} params.pageSize 每页记录数
 * @param {array} params.ids ids
 * @param {number} params.id id
 * @param {string} params.enterpriseNature 企业性质
 * @param {array} params.enterpriseNatureIds 企业性质ids
 * @param {string} params.industryChain 产业链
 * @param {string} params.projectName
 * @param {string} params.startTime 开始时间,格式：yyyy-MM-dd HH:mm:ss
 * @param {string} params.endTime 结束时间,格式：yyyy-MM-dd HH:mm:ss
 * @returns
 */

export function detailSituation(data = {}, options = {}) {
	return request({
		url: `/bidmgt/projectIntelligence/detail`,
		method: 'POST',
		data,
		...options,
	});
}

/**
 * 新增商情
 * @param {object} params 项目商情dto
 * @param {number} params.id
 * @param {string} params.projectName
 * @param {string} params.enterpriseName 企业名称
 * @param {string} params.industryChain 产业链
 * @param {string} params.investmentProbability 新增投资概率
 * @param {array} params.enterpriseNatureIds 企业性质ids
 * @param {string} params.evaluationScore 测试分数
 * @param {string} params.recommendReason 推荐理由
 * @param {string} params.enterpriseIntroduction 企业简介
 * @param {string} params.mainBusiness 主营业务
 * @param {string} params.contactInformation
 * @param {string} params.contactAddress
 * @param {string} params.enterpriseWebsite
 * @param {object} params.recommendTime 推荐时间
 * @param {string} params.logoUrl logoUrl
 * @returns
 */

export function addSituation(data = {}, options = {}) {
	return request({
		url: `/bidmgt/projectIntelligence/addProjectIntelligence`,
		method: 'POST',
		data,
		...options,
	});
}

/**
 * 修改商情
 * @param {object} params 项目商情dto
 * @param {number} params.id
 * @param {string} params.projectName
 * @param {string} params.enterpriseName 企业名称
 * @param {string} params.industryChain 产业链
 * @param {string} params.investmentProbability 新增投资概率
 * @param {array} params.enterpriseNatureIds 企业性质ids
 * @param {string} params.evaluationScore 测试分数
 * @param {string} params.recommendReason 推荐理由
 * @param {string} params.enterpriseIntroduction 企业简介
 * @param {string} params.mainBusiness 主营业务
 * @param {string} params.contactInformation
 * @param {string} params.contactAddress
 * @param {string} params.enterpriseWebsite
 * @param {object} params.recommendTime 推荐时间
 * @param {string} params.logoUrl logoUrl
 * @returns
 */

export function updateSituation(data = {}, options = {}) {
	return request({
		url: `/bidmgt/projectIntelligence/updateProjectIntelligence`,
		method: 'POST',
		data,
		...options,
	});
}

/**
 * 根据id批量删录
 * @param {object} params 项目情报查询dto
 * @param {number} params.pageNum 页码
 * @param {number} params.pageSize 每页记录数
 * @param {array} params.ids ids
 * @param {number} params.id id
 * @param {string} params.enterpriseNature 企业性质
 * @param {array} params.enterpriseNatureIds 企业性质ids
 * @param {string} params.industryChain 产业链
 * @param {string} params.projectName
 * @param {string} params.startTime 开始时间,格式：yyyy-MM-dd HH:mm:ss
 * @param {string} params.endTime 结束时间,格式：yyyy-MM-dd HH:mm:ss
 * @returns
 */

export function delSituation(data = {}, options = {}) {
	return request({
		url: `/bidmgt/projectIntelligence/batchDelProjectIntelligence`,
		method: 'POST',
		data,
		...options,
	});
}

/**
 * 更新已经状态
 * @param {object} params 项目情报查询dto
 * @param {number} params.pageNum 页码
 * @param {number} params.pageSize 每页记录数
 * @param {array} params.ids ids
 * @param {number} params.id id
 * @param {string} params.enterpriseNature 企业性质
 * @param {array} params.enterpriseNatureIds 企业性质ids
 * @param {string} params.industryChain 产业链
 * @param {string} params.projectName
 * @param {string} params.startTime 开始时间,格式：yyyy-MM-dd HH:mm:ss
 * @param {string} params.endTime 结束时间,格式：yyyy-MM-dd HH:mm:ss
 * @returns
 */

export function updateReadStatus(data = {}, options = {}) {
	return request({
		url: `/bidmgt/projectIntelligence/updateReadStatus`,
		method: 'POST',
		data,
		...options,
	});
}

/**
 * 获取当前用户当前用户查询未读数量
 * @returns
 */

export function unreadNumber(data = {}, options = {}) {
	return request({
		url: `/bidmgt/projectIntelligence/unreadNumber`,
		method: 'POST',
		data,
		...options,
	});
}

/**
 * 认领指派操作
 * @param {object} params 商情/项目认领审核dto
 * @param {number} params.id 操作id(主表id)
 * @param {number} params.operateStatus 操作状态：0 等待操作  1 操作拒绝/不通过 2 操作同意/通过
 * @param {string} params.operateDesc 操作描述
 * @returns
 */
export function claimProjectIntelligenceAssign(data = {}, options = {}) {
	return request({
		url: `/bidmgt/projectIntelligence/claimProjectIntelligenceAssign`,
		method: 'POST',
		data,
		...options,
	});
}
/**
 * 审核操作
 * @param {object} params 商情/项目认领审核dto
 * @param {number} params.id 操作id(主表id)
 * @param {number} params.operateStatus 操作状态：0 等待操作  1 操作拒绝/不通过 2 操作同意/通过
 * @param {string} params.operateDesc 操作描述
 * @returns
 */
export function auditProjectIntelligence(data = {}, options = {}) {
	return request({
		url: `/bidmgt/projectIntelligence/auditProjectIntelligence`,
		method: 'POST',
		data,
		...options,
	});
}

/**
 * 商情采纳操作
 * @param {object} params 项目商情采纳操作dto
 * @param {number} params.id
 * @param {number} params.acceptStatus 采纳状态：0等待采纳 1 不通过采纳 2 通过采纳
 * @returns
 */
export function acceptOperate(data = {}, options = {}) {
	return request({
		url: `/bidmgt/projectIntelligence/acceptOperate`,
		method: 'POST',
		data,
		...options,
	});
}
