/**
 * @description groupEvent - 重点项目
 * <AUTHOR>
 *
 * Created on 2024/10/31 15:33
 */
import { request } from '@/utils/axios';

/* 督办项目参数列表查询 */
export function pageParamList(data = {}) {
	return request({
		url: '/spv/pro/param/list',
		method: 'GET',
		params: data,
		header: {},
		showLoading: true,
		isWhiteList: false,
	});
}

/* 督办项目参数删除 */
export function deleteParam(data = {}) {
	return request({
		url: '/spv/pro/param/del',
		method: 'GET',
		params: data,
		header: {},
		showLoading: true,
		isWhiteList: false,
	});
}

/* 督办项目参数下移 */
export function downParam(data = {}) {
	return request({
		url: '/spv/pro/param/moveDown',
		method: 'GET',
		params: data,
		header: {},
		showLoading: true,
		isWhiteList: false,
	});
}

/* 督办项目参数上移 */
export function upParam(data = {}) {
	return request({
		url: '/spv/pro/param/moveUp',
		method: 'GET',
		params: data,
		header: {},
		showLoading: true,
		isWhiteList: false,
	});
}

/* 督办项目参数保存 */
export function paramSave(data = {}) {
	return request({
		url: '/spv/pro/param/save',
		method: 'POST',
		data,
		header: {},
		showLoading: true,
		isWhiteList: false,
	});
}

/* 督办项目点评详情 */
export function commentDetail(data = {}) {
	return request({
		url: '/spv/pro/comment/detail',
		method: 'GET',
		data,
		header: {},
		showLoading: true,
		isWhiteList: false,
	});
}

/* 督办项目点评列表 */
export function commentList(data = {}) {
	return request({
		url: '/spv/pro/comment/list',
		method: 'GET',
		data,
		header: {},
		showLoading: true,
		isWhiteList: false,
	});
}

/* 督办项目点评创建或编辑保存 */
export function saveComment(data = {}) {
	return request({
		url: '/spv/pro/comment/save',
		method: 'POST',
		data,
		header: {},
		showLoading: true,
		isWhiteList: false,
	});
}

/* 督办项目创建 */
export function addProject(data = {}) {
	return request({
		url: '/spv/pro/add',
		method: 'POST',
		data,
		header: {},
		showLoading: true,
		isWhiteList: false,
	});
}

/* 督办项目审核审批 */
export function auditProject(data = {}) {
	return request({
		url: '/spv/pro/audit',
		method: 'POST',
		data,
		header: {},
		showLoading: true,
		isWhiteList: false,
	});
}

/* 督办项目查询详情 */
export function detailProject(data = {}) {
	return request({
		url: '/spv/pro/detail',
		method: 'POST',
		params: data,
		header: {},
		showLoading: true,
		isWhiteList: false,
	});
}

/* 督办项目首页查询 */
export function homeProject(data = {}) {
	return request({
		url: '/spv/pro/home',
		method: 'GET',
		data,
		header: {},
		showLoading: true,
		isWhiteList: false,
	});
}

/* 督办项目导入 */
export function importProject(data = {}) {
	return request({
		url: '/spv/pro/import',
		method: 'POST',
		data,
		header: {
			'content-type': 'multipart/form-data',
		},
		showLoading: true,
		isWhiteList: false,
	});
}

/* 我的项目 */
export function myProjectList(data = {}) {
	return request({
		url: '/spv/pro/myProList',
		method: 'POST',
		data,
		header: {},
		showLoading: true,
		isWhiteList: false,
	});
}

/* 我的待办 */
export function myTodoList(data = {}) {
	return request({
		url: '/spv/pro/myTodoList',
		method: 'POST',
		data,
		header: {},
		showLoading: true,
		isWhiteList: false,
	});
}

/* 督办项目分页查询 */
export function pageProject(data = {}) {
	return request({
		url: '/spv/pro/page',
		method: 'POST',
		data,
		header: {},
		showLoading: true,
		isWhiteList: false,
	});
}

/* 督办项目进展详情 */
export function nodeDetailProgress(data = {}) {
	return request({
		url: '/spv/pro/node/dispatch/detail',
		method: 'GET',
		params: data,
		header: {},
		showLoading: true,
		isWhiteList: false,
	});
}

/* 督办项目节点调度列表 */
export function progressList(data = {}) {
	return request({
		url: '/spv/pro/node/dispatch/list',
		method: 'GET',
		params: data,
		header: {},
		showLoading: true,
		isWhiteList: false,
	});
}

/* 督办项目节点调度回复 */
export function replyProgress(data = {}) {
	return request({
		url: '/spv/pro/node/dispatch/reply',
		method: 'POST',
		data,
		header: {},
		showLoading: true,
		isWhiteList: false,
	});
}

/* 督办项目节点调度保存 */
export function saveProgress(data = {}) {
	return request({
		url: '/spv/pro/node/dispatch/save',
		method: 'POST',
		data,
		header: {},
		showLoading: true,
		isWhiteList: false,
	});
}

/* 督办项目节点详情 */
export function nodeDetail(data = {}) {
	return request({
		url: '/spv/pro/node/detail',
		method: 'GET',
		params: data,
		header: {},
		showLoading: true,
		isWhiteList: false,
	});
}

/* 督办项目节点列表 */
export function nodeList(data = {}) {
	return request({
		url: '/spv/pro/node/list',
		method: 'GET',
		data,
		header: {},
		showLoading: true,
		isWhiteList: false,
	});
}

/* 督办项目节点创建或编辑保存 */
export function saveNode(data = {}) {
	return request({
		url: '/spv/pro/node/save',
		method: 'POST',
		data,
		header: {},
		showLoading: true,
		isWhiteList: false,
	});
}

/* 督办项目节点 */
export function nodeUpd(data = {}) {
	return request({
		url: '/spv/pro/node/upd',
		method: 'GET',
		data,
		header: {},
		showLoading: true,
		isWhiteList: false,
	});
}

/* 督办项目节点审批详情 */
export function auditDetail(data = {}) {
	return request({
		url: '/spv/pro/node/audit/detail',
		method: 'GET',
		params: data,
		header: {},
		showLoading: true,
		isWhiteList: false,
	});
}

/* 督办项目节点审批处理保存 */
export function saveAudit(data = {}) {
	return request({
		url: '/spv/pro/node/audit/handle',
		method: 'POST',
		data,
		header: {},
		showLoading: true,
		isWhiteList: false,
	});
}

/* 督办项目节点审批列表 */
export function auditList(data = {}) {
	return request({
		url: '/spv/pro/node/audit/list',
		method: 'GET',
		params: data,
		header: {},
		showLoading: true,
		isWhiteList: false,
	});
}

/* 督办项目节点审批创建保存 */
export function saveAuditNode(data = {}) {
	return request({
		url: '/spv/pro/node/audit/save',
		method: 'POST',
		data,
		header: {},
		showLoading: true,
		isWhiteList: false,
	});
}

/* 督办项目节点问题详情 */
export function problemDetail(data = {}) {
	return request({
		url: '/spv/pro/node/issue/detail',
		method: 'GET',
		data,
		header: {},
		showLoading: true,
		isWhiteList: false,
	});
}

/* 督办项目节点问题列表 */
export function problemList(data = {}) {
	return request({
		url: '/spv/pro/node/issue/list',
		method: 'GET',
		params: data,
		header: {},
		showLoading: true,
		isWhiteList: false,
	});
}

/* 督办项目节点问题创建或编辑保存 */
export function saveProblem(data = {}) {
	return request({
		url: '/spv/pro/node/issue/save',
		method: 'POST',
		data,
		header: {},
		showLoading: true,
		isWhiteList: false,
	});
}

/* 督办项目进展详情 */
export function progressDetail(data = {}) {
	return request({
		url: '/spv/pro/progress/detail',
		method: 'GET',
		data,
		header: {},
		showLoading: true,
		isWhiteList: false,
	});
}

/* 督办项目进展列表 */
export function proProgressList(data = {}) {
	return request({
		url: '/spv/pro/progress/list',
		method: 'GET',
		data,
		header: {},
		showLoading: true,
		isWhiteList: false,
	});
}

/* 督办项目进展创建或编辑保存 */
export function saveProgressNode(data = {}) {
	return request({
		url: '/spv/pro/progress/save',
		method: 'POST',
		data,
		header: {},
		showLoading: true,
		isWhiteList: false,
	});
}

/* --------------------------------站内消息------------------------------------ */

/* 删除站内消息 */
export function delMessage(data = {}) {
	return request({
		url: '/spv/pro/message/del',
		method: 'POST',
		data,
		header: {},
		showLoading: true,
		isWhiteList: false,
	});
}

/* 我的站内消息分页查询 */
export function messageList(data = {}) {
	return request({
		url: '/spv/pro/message/mySiteMessage',
		method: 'POST',
		data,
		header: {},
		showLoading: true,
		isWhiteList: false,
	});
}
/* 读站内消息 */
export function readMessage(data = {}) {
	return request({
		url: '/spv/pro/message/read',
		method: 'POST',
		data,
		header: {},
		showLoading: true,
		isWhiteList: false,
	});
}

/*------------------------------------权限功能----------------------------------------- */

/* 角色权限项配置列表查询 */
export function roleAuthList(data = {}) {
	return request({
		url: '/spv/pro/param/listRolePermissions',
		method: 'GET',
		params: data,
		header: {},
		showLoading: true,
		isWhiteList: false,
	});
}

/* 权限项列表查询 */
export function authList(data = {}) {
	return request({
		url: '/spv/pro/param/listPermissionOptions',
		method: 'GET',
		data,
		header: {},
		showLoading: true,
		isWhiteList: false,
	});
}

/* 我的角色权限项配置 */
export function myRoleAuth(data = {}) {
	return request({
		url: '/spv/pro/param/myRolePermissions',
		method: 'GET',
		data,
		header: {},
		showLoading: true,
		isWhiteList: false,
	});
}

/* 角色权限项配置保存 */
export function saveRoleAuth(data = {}) {
	return request({
		url: '/spv/pro/param/saveRolePermissions',
		method: 'POST',
		data,
		header: {},
		showLoading: true,
		isWhiteList: false,
	});
}

/**
 * 督办项目导出
 * @param {object} params 督办项目导出参数DTO
 * @param {string} params.name 名称
 * @param {number} params.typeId 类型ID
 * @param {number} params.categoryId 类别ID
 * @param {number} params.progressId 阶段ID
 * @param {number} params.unitId 区责任单位ID
 * @returns
 */
export function exportSpvPro(params) {
	return request({
		url: '/spv/pro/export',
		method: 'POST',
		data: params,
		header: {
			// 'content-type': 'application/octet-stream',
			'Content-Type': 'application/json;charset=UTF-8',
			Accept: 'application/vnd.ms-excel',
		},
		showLoading: true,
		isWhiteList: false,
		responseType: 'blob',
	});
}

/** 节点状态刷新 spv.pro.node.waringCheck  spv.pro.node.waringCheck" */
export function refreshNodeStatus(params) {
	return request({
		url: '/spv/pro/node/waringCheck',
		method: 'POST',
		header: {},
		showLoading: true,
		isWhiteList: false,
	});
}
