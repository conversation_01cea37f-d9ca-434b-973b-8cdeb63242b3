import { request } from '@/utils/axios';

/**
 * 获取考勤员工
 * @param {object} params 考勤汇报
 * @param {array} params.userIds 主键
 * @returns
 */
export function employeeList(params = {}, option = {}) {
	return request({
		url: `/wpm/attendanceReport/employeeList`,
		method: 'POST',
		data: params,
		...option,
	});
}

/**
 * 添加考勤员工
 * @param {object} params 考勤汇报
 * @param {array} params.userIds 主键
 * @returns
 */
export function saveEmployee(params = {}, option = {}) {
	return request({
		url: `/wpm/attendanceReport/saveEmployee`,
		method: 'POST',
		data: params,
		...option,
	});
}

/**
 * 批量删除考勤员工
 * @param {object} params 考勤汇报
 * @param {array} params.userIds 主键
 * @returns
 */
export function deleteBatch(params = {}, option = {}) {
	return request({
		url: `/wpm/attendanceReport/deleteBatch`,
		method: 'POST',
		data: params,
		...option,
	});
}

/**
 * 分页查询考勤发布状态
 * @param {object} params 考勤数据汇报
 * @param {number} params.pageNum 页码
 * @param {number} params.pageSize 每页记录数
 * @param {number} params.id
 * @param {string} params.reportYearMonth 考勤所属年月
 * @param {array} params.userIds 用户id
 * @param {number} params.departmentId 部门id
 * @param {number} params.releaseStatus 发布状态： 0否 1是
 * @param {number} params.dataType
 * @returns
 */
export function getReleaseStatusPage(params = {}, option = {}) {
	return request({
		url: `/wpm/attendanceReport/getReleaseStatusPage`,
		method: 'POST',
		data: params,
		...option,
	});
}

/**
 * 导出考勤数据
 * @param {object} params 考勤数据汇报
 * @param {number} params.pageNum 页码
 * @param {number} params.pageSize 每页记录数
 * @param {number} params.id
 * @param {string} params.reportYearMonth 考勤所属年月
 * @param {array} params.userIds 用户id
 * @param {number} params.departmentId 部门id
 * @param {number} params.releaseStatus 发布状态： 0否 1是
 * @param {number} params.dataType
 * @returns
 */
export function exportAttendanceData(params = {}) {
	return request({
		url: `/wpm/attendanceReport/exportAttendanceData`,
		method: 'POST',
		data: params,
		responseType: 'blob',
	});
}

/**
 * 分页查询考勤
 * @param {object} params 考勤数据汇报
 * @param {number} params.pageNum 页码
 * @param {number} params.pageSize 每页记录数
 * @param {number} params.id
 * @param {string} params.reportYearMonth 考勤所属年月
 * @param {array} params.userIds 用户id
 * @param {number} params.departmentId 部门id
 * @param {number} params.releaseStatus 发布状态： 0否 1是
 * @param {number} params.dataType
 * @returns
 */
export function getAttendanceReportPage(params = {}, option = {}) {
	return request({
		url: `/wpm/attendanceReport/getAttendanceReportPage`,
		method: 'POST',
		data: params,
		...option,
	});
}

/**
 * 统计考勤
 * @param {object} params 考勤数据汇报
 * @param {number} params.pageNum 页码
 * @param {number} params.pageSize 每页记录数
 * @param {number} params.id
 * @param {string} params.reportYearMonth 考勤所属年月
 * @param {array} params.userIds 用户id
 * @param {number} params.departmentId 部门id
 * @param {number} params.releaseStatus 发布状态： 0否 1是
 * @param {number} params.dataType
 * @returns
 */
export function getAttendanceStatistics(params = {}, option = {}) {
	return request({
		url: `/wpm/attendanceReport/getAttendanceStatistics`,
		method: 'POST',
		data: params,
		...option,
	});
}

/**
 * 发布考勤（传：reportYearMonth）
 * @param {object} params 考勤数据汇报
 * @param {number} params.pageNum 页码
 * @param {number} params.pageSize 每页记录数
 * @param {number} params.id
 * @param {string} params.reportYearMonth 考勤所属年月
 * @param {array} params.userIds 用户id
 * @param {number} params.departmentId 部门id
 * @param {number} params.releaseStatus 发布状态： 0否 1是
 * @param {number} params.dataType
 * @returns
 */
export function releaseAttendanceReport(params = {}, option = {}) {
	return request({
		url: `/wpm/attendanceReport/releaseAttendanceReport`,
		method: 'POST',
		data: params,
		...option,
	});
}

/**
 * 修改考勤
 * @param {object} params 考勤汇报
 * @param {number} params.id 主键
 * @param {number} params.lateEarlyCount 迟到/早退次数
 * @param {number} params.missingCardCount 缺卡次数
 * @param {number} params.personalLeaveDays 事假/天
 * @param {number} params.sickLeaveDays 病假/天
 * @param {number} params.annualLeaveDays 年假/天
 * @param {number} params.adjustmentLeaveDays 调休/天
 * @param {number} params.marriageLeaveDays 婚假/天
 * @param {number} params.maternityLeaveDays 产假/天
 * @param {number} params.prenatalCheckLeaveDays 产检假/天
 * @param {number} params.bereavementLeaveDays 丧假/天
 * @param {number} params.paternityLeaveDays 陪产假/天
 * @returns
 */
export function updateAttendanceReport(params = {}, option = {}) {
	return request({
		url: `/wpm/attendanceReport/updateAttendanceReport`,
		method: 'POST',
		data: params,
		...option,
	});
}
