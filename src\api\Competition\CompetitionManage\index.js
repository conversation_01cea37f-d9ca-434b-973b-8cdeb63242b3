import { request } from '@/utils/axios';
/**
 * 保存活动
 * @param {object} params 活动
 * @param {number} params.id 活动唯一标识ID
 * @param {number} params.ieType 内外类型（1内部活动，2外部活动）
 * @param {string} params.name 名称
 * @param {string} params.shortName 简介
 * @param {string} params.theme 主题
 * @param {object} params.beginDate 开始时间
 * @param {object} params.endDate 结束时间
 * @param {object} params.entryBeginTime 报名起始时间
 * @param {object} params.entryEndTime 报名结束时间
 * @param {string} params.venue 地点
 * @param {string} params.introduction 介绍
 * @param {string} params.flowInfo 流程
 * @param {string} params.eligibility 资格条件
 * @param {string} params.reviewRule 评审规则
 * @param {string} params.awardSetting 奖项设置
 * @param {string} params.bg 背景图
 * @param {string} params.carouselChart 轮播图
 * @param {string} params.carouselChartLink 轮播图点击链接
 * @param {string} params.largeScreenBg 大屏背景图
 * @param {object} params.activityContactDto 活动联系信息
 * @param {object} params.activityOrganizationDto 活动组织信息
 * @param {object} params.actObj 赛事活动对象
 * @param {object} params.actIndustry 赛事活动行业
 * @param {object} params.actGroup 赛事活动组别
 * @returns
 */
export function saveActivity(data = {}, option = {}) {
	return request({
		url: `/competition/activity/add`,
		method: 'POST',
		data,
		...option,
	});
}

/**
 * 删除活动
 * @param {string} activityId
 * @returns
 */
export function deleteActivity(data = {}, option = {}) {
	return request({
		url: `/competition/activity/delete`,
		method: 'POST',
		params: data,
		...option,
	});
}
/**
 * 活动详情
 * @param {string} id
 * @returns
 */
export function getActivityDetail(data = {}, option = {}) {
	return request({
		url: `/competition/activity/detail`,
		method: 'POST',
		params: data,
		...option,
	});
}

/**
 * 赛事标签列表
 * @returns
 */
export function listActivity(data = {}, option = {}) {
	return request({
		url: `/competition/activity/listShortName`,
		method: 'POST',
		data,
		...option,
	});
}
/**
 * 活动分页
 * @param {object} params 赛事分页参数
 * @param {number} params.pageNum 页码
 * @param {number} params.pageSize 每页记录数
 * @param {string} params.shortName 赛事名称简称
 * @param {number} params.year 赛事年份
 * @param {number} params.status 赛事状态
 * @returns
 */
export function pageActivity(data = {}, option = {}) {
	return request({
		url: `/competition/activity/page`,
		method: 'POST',
		data,
		...option,
	});
}
/**
 * 保存活动评审配置
 * @param {object} params 活动评价配置参数
 * @param {number} params.activityId 活动唯一标识ID
 * @param {number} params.calculationMethod 评分计算方式（1评委人数平均 2去最高最低平均）
 * @param {array} params.ratingItems 活动评审项
 * @param {array} params.ratingPersons 活动评委
 * @param {array} params.ratingGroups 活动评审组
 * @returns
 */
export function saveActivityRatingConfig(data = {}, option = {}) {
	return request({
		url: `/competition/activity/rating/config`,
		method: 'POST',
		data,
		...option,
	});
}

/**
 * 活动评分配置查询
 * @param {string} id
 * @returns
 */
export function getActivityRatingConfig(data = {}, option = {}) {
	return request({
		url: `/competition/activity/rating/config/get`,
		method: 'POST',
		params: data,
		...option,
	});
}

/**
 * 活动评审组查询
 * @param {string} id
 * @returns
 */
export function getActivityRatingGroup(data = {}, option = {}) {
	return request({
		url: `/competition/activity/rating/group/get`,
		method: 'POST',
		params: data,
		...option,
	});
}

/**
 * 企业excel导入
 * @param {string} file
 * @param {string} id
 * @returns
 */
export function importActivityRatingSessionEntrant(data = {}, option = {}) {
	return request({
		url: `/competition/activity/rating/session/entrant/import`,
		method: 'POST',
		data,
		...option,
		header: {
			'Content-Type': 'multipart/form-data',
		},
	});
}

/**
 * 活动场次保存
 * @param {object} params 活动评审场次
 * @param {number} params.id 场次唯一标识ID
 * @param {number} params.activityId 活动唯一标识ID
 * @param {string} params.name 场次名称
 * @param {number} params.industryId 行业
 * @param {number} params.groupId 组别
 * @param {object} params.beginTime 开始时间
 * @param {object} params.endTime 结束时间
 * @param {string} params.site 地点
 * @param {number} params.ratingGroupId 评审组唯一标识ID
 * @param {number} params.scoreStatus 评分开启状态(0:未开启,1:开启)
 * @param {number} params.delFlag 删除标识(0:未删除,1:删除)
 * @returns
 */
export function saveActivityRatingSession(data = {}, option = {}) {
	return request({
		url: `/competition/activity/rating/session/save`,
		method: 'POST',
		data,
		...option,
	});
}

/**
 * 活动场次删除
 * @param {object} data
 * @param {string} data.id
 * @returns
 */
export function deleteActivityRatingSession(data = {}, option = {}) {
	return request({
		url: `/competition/activity/rating/session/delete`,
		method: 'POST',
		params: data,
		...option,
	});
}

/**
 * 表单模版详情
 * @param {object} data 表单模版
 * @param {number} data.id 表单模版唯一标识ID
 * @returns
 */
export function getFormTemplateDetail(data = {}, option = {}) {
	return request({
		url: `/competition/formTemplate/detail`,
		method: 'POST',
		params: data,
		...option,
	});
}

/**
 * 修改表单模版
 * @param {object} data 表单模版
 * @param {number} data.id 表单模版唯一标识ID
 * @param {string} data.name 模版名称
 * @param {number} data.used 是否被使用（0否 1是）
 * @param {array} data.pieces 表单信息块
 * @returns
 */
export function updateFormTemplate(data = {}, option = {}) {
	return request({
		url: `/competition/formTemplate/update`,
		method: 'POST',
		data,
		...option,
	});
}

/**
 * 报名列表
 * @param {object} params 大赛活动报名查询dto
 * @param {number} params.pageNum 页码
 * @param {number} params.pageSize 每页记录数
 * @param {number} params.id 报名id
 * @param {array} params.ids 报名ids
 * @param {number} params.enterpriseId 企业id
 * @param {number} params.activityId 大赛活动id
 * @param {array} params.activityIds 大赛活动id
 * @param {string} params.name 企业名称
 * @param {string} params.creditCode 统一社会信用代码
 * @param {number} params.status 状态：0 暂存 1 审核中 2 未通过 3 己通过 4 待评审 5 已评审 6 已结束
 * @param {number} params.industryId 行业id
 * @param {number} params.activityGroupId 活动组别id
 * @param {number} params.activitySessionId 活动场次id
 * @returns
 */
export function pageActivitySignUp(data = {}, option = {}) {
	return request({
		url: `/competition/signUp/pageActivitySignUp`,
		method: 'POST',
		data,
		...option,
	});
}
/**
 * 活动报名分页列表
 * @param {object} params 赛事活动报名记录分页参数
 * @param {number} params.pageNum 页码
 * @param {number} params.pageSize 每页记录数
 * @param {number} params.activityId 活动唯一标识ID
 * @returns
 */
export function getPageListActivityEntry(data = {}, option = {}) {
	return request({
		url: `/competition/activity/entry/list/page`,
		method: 'POST',
		data,
		...option,
	});
}

/**
 * 删除报名数据
 * @param {object} params 大赛活动报名查询dto
 * @param {number} params.pageNum 页码
 * @param {number} params.pageSize 每页记录数
 * @param {number} params.id 报名id
 * @param {array} params.ids 报名ids
 * @param {number} params.enterpriseId 企业id
 * @param {number} params.activityId 大赛活动id
 * @param {array} params.activityIds 大赛活动id
 * @param {string} params.name 企业名称
 * @param {string} params.creditCode 统一社会信用代码
 * @param {number} params.status 状态：0 暂存 1 审核中 2 未通过 3 己通过 4 待评审 5 已评审 6 已结束
 * @param {number} params.industryId 行业id
 * @param {number} params.activityGroupId 活动组别id
 * @param {number} params.activitySessionId 活动场次id
 * @returns
 */
export function delSignUp(data = {}, option = {}) {
	return request({
		url: `/competition/signUp/delSignUp`,
		method: 'POST',
		data,
		...option,
	});
}
/**
 * 根据报名id获取详情
 * @param {object} params 大赛活动报名查询dto
 * @param {number} params.pageNum 页码
 * @param {number} params.pageSize 每页记录数
 * @param {number} params.id 报名id
 * @param {array} params.ids 报名ids
 * @param {number} params.enterpriseId 企业id
 * @param {number} params.activityId 大赛活动id
 * @param {array} params.activityIds 大赛活动id
 * @param {string} params.name 企业名称
 * @param {string} params.creditCode 统一社会信用代码
 * @param {number} params.status 状态：0 暂存 1 审核中 2 未通过 3 己通过 4 待评审 5 已评审 6 已结束
 * @param {number} params.industryId 行业id
 * @param {number} params.activityGroupId 活动组别id
 * @param {number} params.activitySessionId 活动场次id
 * @returns
 */
export function getActivitySignUpById(data = {}, option = {}) {
	return request({
		url: `/competition/signUp/getActivitySignUpById`,
		method: 'POST',
		data,
		...option,
	});
}
/**
 * 修改报名数据
 * @param {object} params 大赛活动报名字段保存dto
 * @param {number} params.id
 * @param {number} params.enterpriseId 企业id
 * @param {number} params.activityId 大赛活动id
 * @param {number} params.status
 * @param {string} params.name 企业名称
 * @param {string} params.creditCode 统一社会信用代码
 * @param {number} params.industryId 行业id
 * @param {number} params.projectGroupId 项目组别id
 * @param {number} params.activitySessionId 活动场次id
 * @param {string} params.auditReason 审核原因
 * @param {array} params.attachedList 报名自定义字段保存dto
 * @returns
 */
export function updateSignUp(data = {}, option = {}) {
	return request({
		url: `/competition/signUp/updateSignUp`,
		method: 'POST',
		data,
		...option,
	});
}

/**
 * 活动场次查询
 * @param {string} activityId
 * @returns
 */
export function getActivityRatingSession(data = {}, option = {}) {
	return request({
		url: `/competition/activity/rating/session/get`,
		method: 'POST',
		data: data,
		...option,
	});
}

/**
 * 显示状态修改
 * @param {object} params 显示状态入参
 * @param {number} params.id 主键id
 * @param {number} params.showStatus 显示状态(1:开启, 2:关闭)
 * @returns
 */
export function updateShowStatus(data = {}, option = {}) {
	return request({
		url: `/competition/activity/update/showStatus`,
		method: 'POST',
		data,
		...option,
	});
}

/**
 * 批量审核报名(传ids和status:2,3)
 * @param {object} params 大赛活动报名查询dto
 * @param {number} params.pageNum 页码
 * @param {number} params.pageSize 每页记录数
 * @param {number} params.id 报名id
 * @param {array} params.ids 报名ids
 * @param {number} params.enterpriseId 企业id
 * @param {number} params.activityId 大赛活动id
 * @param {array} params.activityIds 大赛活动id
 * @param {string} params.name 企业名称
 * @param {string} params.creditCode 统一社会信用代码
 * @param {number} params.status 状态：0 暂存 1 审核中 2 未通过 3 己通过 4 待评审 5 已评审 6 已结束
 * @param {number} params.industryId 行业id
 * @param {number} params.activityGroupId 活动组别id
 * @param {number} params.activitySessionId 活动场次id
 * @param {string} params.auditReason 审核不通过原因
 * @returns
 */
export function batchAuditStatus(data = {}, option = {}) {
	return request({
		url: `/competition/signUp/batchAuditStatus`,
		method: 'POST',
		data,
		...option,
	});
}

/**
 * 导出报名名
 * @param {string} activityId
 * @returns
 */
export function signUpExportList(data = {}, option = {}) {
	return request({
		url: `/competition/signUp/exportList`,
		method: 'POST',
		data,
		responseType: 'blob',
		...option,
	});
}

/**
 * 导入报名名
 * @param {string} activityId
 * @returns
 */
export function signUpImportList(data = {}, params = {}, option = {}) {
	return request({
		url: `/competition/signUp/importList`,
		method: 'POST',
		data,
		params,
		header: {
			'Content-Type': 'multipart/form-data',
		},
		...option,
	});
}

/**
 * 活动场次参赛者评分信息列表
 * @param {string} id
 * @returns
 */
export function getActivityRatingSessionScoreInfo(params = {}, option = {}) {
	return request({
		url: `/competition/activity/rating/session/score/info/get`,
		method: 'GET',
		params,
		...option,
	});
}

/**
 * 活动场次开启评分状态
 * @param {number} params.ratingSessionId 场次唯一标识ID
 * @param {number} params.scoreStatus 评分状态(1开启评分 2已完成评分 )
 * @returns
 */
export function enableActivityRatingSessionScoreStatus(data = {}, option = {}) {
	return request({
		url: `/competition/activity/rating/session/score/status/enable`,
		method: 'POST',
		data,
		...option,
	});
}

/**
 * 活动场次详情查询
 * @param {object} params 活动评审场次查询
 * @param {number} params.id 场次唯一标识ID
 * @returns
 */
export function getActivityRatingSessionDetail(data = {}, option = {}) {
	return request({
		url: `/competition/activity/rating/session/getActivityRatingSessionDetail`,
		method: 'POST',
		data,
		...option,
	});
}

/**
 * 活动场次参赛者路演顺序编辑保存
 * @param {object} params 活动场次参赛者路演顺序编辑参数DTO
 * @param {number} params.ratingSessionEntrantId 场次参赛者唯一标识ID
 * @param {number} params.sequence 场次参赛者路演顺序
 * @returns
 */
export function saveRatingSessionEntrantSequence(data = {}, option = {}) {
	return request({
		url: `/competition/activity/rating/session/entrant/sequence/save`,
		method: 'POST',
		data,
		...option,
	});
}

/**
 * 评委评分记录重置
 * @param {string} scoreId
 * @returns
 */
export function removeRatingSessionEntrantScore(params = {}, option = {}) {
	return request({
		url: `/competition/activity/rating/session/entrant/score/remove`,
		method: 'POST',
		params,
		...option,
	});
}

/**
 * 活动场次参赛者排名
 * @param {object} params 活动评审场次参赛者排名DTO
 * @param {number} params.ratingSessionId 场次唯一标识ID
 * @param {number} params.rankingType 排名类型，1并列跳跃排名 2并列连续排序 3连续排名
 * @returns
 */
export function rankingActivityRatingSessionEntrant(data = {}, option = {}) {
	return request({
		url: `/competition/activity/rating/session/entrant/ranking`,
		method: 'POST',
		data,
		...option,
	});
}

/**
 * 导出活动场次参赛者评分信息
 * @param {string} id
 * @returns
 */
export function exportActivityRatingSessionScoreInfo(data = {}, option = {}) {
	return request({
		url: `/competition/activity/rating/session/score/info/export`,
		method: 'GET',
		params: data,
		responseType: 'blob',
		...option,
	});
}
