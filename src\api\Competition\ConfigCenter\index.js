import { request } from '@/utils/axios'
/** 
 * 新增字典数据
 * @param {object} params 字典保存dto
 * @param {number} params.id 
 * @param {string} params.name 字典名称
 * @param {string} params.code 字典编码
 * @param {number} params.status 状态:0停用 1启用
 * @param {number} params.rankingNum 排名序号
 * @returns
 */
export function addDict(data = {}, option = {}) {
	return request({
		url: `/competition/dict/addDict`,
		method: 'POST',
		data,
		...option,
	})
}
/** 
 * 新增字典条目数据
 * @param {object} params 字典条目保存dto
 * @param {number} params.id 
 * @param {number} params.dictId 所属字典主键
 * @param {string} params.code 所属字典编码
 * @param {string} params.itemName 字典条目名称
 * @param {string} params.itemCode 字典条目编码
 * @param {string} params.itemValue 字典条目值
 * @param {number} params.parentDictItemId 所属字典条目主键
 * @param {number} params.status 状态:0停用 1启用
 * @param {number} params.rankingNum 排名序号
 * @returns
 */
export function addDictItem(data = {}, option = {}) {
	return request({
		url: `/competition/dict/addDictItem`,
		method: 'POST',
		data,
		...option,
	})
}
/** 
* 删除字典数据
* @param {object} params 字典查询dto
* @param {number} params.id 
* @param {number} params.status 使用状态： 0 停用 1 启用
* @param {string} params.name 字典名称
* @param {string} params.code 字典编码
* @returns
*/
export function delDict(data = {}, option = {}) {
	return request({
		url: `/competition/dict/delDict`,
		method: 'POST',
		data,
		...option,
	})
}

/** 
 * 删除字典条目数据
 * @param {object} params 字典条目查询dto
 * @param {number} params.id 
 * @param {number} params.status 使用状态： 0 停用 1 启用
 * @param {number} params.dictId 所属字典主键
 * @param {array} params.dictIds 批量查询所属字典主键
 * @param {string} params.itemName 字典条目名称
 * @param {string} params.itemCode 字典条目编码
 * @param {number} params.parentDictItemId 所属字典条目主键
 * @returns
 */
export function delDictItem(data = {}, option = {}) {
	return request({
		url: `/competition/dict/delDictItem`,
		method: 'POST',
		data,
		...option,
	})
}
/** 
 * 查询字典详情数据
 * @param {object} params 字典查询dto
 * @param {number} params.id 
 * @param {number} params.status 使用状态： 0 停用 1 启用
 * @param {string} params.name 字典名称
 * @param {string} params.code 字典编码
 * @returns
 */
export function detailDict(data = {}, option = {}) {
	return request({
		url: `/competition/dict/detailDict`,
		method: 'POST',
		data,
		...option,
	})
}

/** 
* 查询字典条目详情数据
* @param {object} params 字典条目查询dto
* @param {number} params.id 
* @param {number} params.status 使用状态： 0 停用 1 启用
* @param {number} params.dictId 所属字典主键
* @param {array} params.dictIds 批量查询所属字典主键
* @param {string} params.itemName 字典条目名称
* @param {string} params.itemCode 字典条目编码
* @param {number} params.parentDictItemId 所属字典条目主键
* @returns
*/
export function detailDictItem(data = {}, option = {}) {
	return request({
		url: `/competition/dict/detailDictItem`,
		method: 'POST',
		data,
		...option,
	})
}
/** 
 * 查询字典列表数据
 * @param {object} params 字典查询dto
 * @param {number} params.id 
 * @param {number} params.status 使用状态： 0 停用 1 启用
 * @param {string} params.name 字典名称
 * @param {string} params.code 字典编码
 * @returns
 */
export function listDict(data = {}, option = {}) {
	return request({
		url: `/competition/dict/listDict`,
		method: 'POST',
		data,
		...option,
	})
}

/** 
 * 查询字典条目列表数据
 * @param {object} params 字典条目查询dto
 * @param {number} params.id 
 * @param {number} params.status 使用状态： 0 停用 1 启用
 * @param {number} params.dictId 所属字典主键
 * @param {array} params.dictIds 批量查询所属字典主键
 * @param {string} params.itemName 字典条目名称
 * @param {string} params.itemCode 字典条目编码
 * @param {number} params.parentDictItemId 所属字典条目主键
 * @returns
 */
export function listDictItem(data = {}, option = {}) {
	return request({
		url: `/competition/dict/listDictItem`,
		method: 'POST',
		data,
		...option,
	})
}

/** 
 * 更新字典数据
 * @param {object} params 字典保存dto
 * @param {number} params.id 
 * @param {string} params.name 字典名称
 * @param {string} params.code 字典编码
 * @param {number} params.status 状态:0停用 1启用
 * @param {number} params.rankingNum 排名序号
 * @returns
 */
export function updateDict(data = {}, option = {}) {
	return request({
		url: `/competition/dict/updateDict`,
		method: 'POST',
		data,
		...option,
	})
}
/** 
 * 更新字典条目数据
 * @param {object} params 字典条目保存dto
 * @param {number} params.id 
 * @param {number} params.dictId 所属字典主键
 * @param {string} params.code 所属字典编码
 * @param {string} params.itemName 字典条目名称
 * @param {string} params.itemCode 字典条目编码
 * @param {string} params.itemValue 字典条目值
 * @param {number} params.parentDictItemId 所属字典条目主键
 * @param {number} params.status 状态:0停用 1启用
 * @param {number} params.rankingNum 排名序号
 * @returns
 */
export function updateDictItem(data = {}, option = {}) {
	return request({
		url: `/competition/dict/updateDictItem`,
		method: 'POST',
		data,
		...option,
	})
}

/** 
* 更新字典条目状态
* @param {object} params 字典条目查询dto
* @param {number} params.id 
* @param {number} params.status 使用状态： 0 停用 1 启用
* @param {number} params.dictId 所属字典主键
* @param {array} params.dictIds 批量查询所属字典主键
* @param {string} params.itemName 字典条目名称
* @param {string} params.itemCode 字典条目编码
* @param {number} params.parentDictItemId 所属字典条目主键
* @returns
*/
export function updateDictItemIdStatus(data = {}, option = {}) {
	return request({
		url: `/competition/dict/updateDictItemIdStatus`,
		method: 'POST',
		data,
		...option,
	})
}
/** 
 * 新增内容配置
 * @param {object} params 赛事内容显示配置保存dto
 * @param {number} params.id 
 * @param {string} params.homePageConfig 首页内容配置JSON
 * @param {string} params.aboutUsConfig      * 关于我们页面内容配置json
 * @returns
 */
export function addConfig(data = {}, option = {}) {
	return request({
		url: `/competition/contentShowConfig/addConfig`,
		method: 'POST',
		data,
		...option,
	})
}

/** 
 * 修改内容配置
 * @param {object} params 赛事内容显示配置保存dto
 * @param {number} params.id 
 * @param {string} params.homePageConfig 首页内容配置JSON
 * @param {string} params.aboutUsConfig      * 关于我们页面内容配置json
 * @returns
 */
export function updateConfig(data = {}, option = {}) {
	return request({
		url: `/competition/contentShowConfig/updateConfig`,
		method: 'POST',
		data,
		...option,
	})
}

/** 
 * 获取当前租户内容配置
 * @returns
 */
export function detailConfig(data = {}, option = {}) {
	return request({
		url: `/competition/contentShowConfig/detailConfig`,
		method: 'POST',
		data,
		...option,
	})
}