import { request } from '@/utils/axios'
/** 
 * 新增大赛资料
 * @param {object} params 大赛资料
 * @param {number} params.id 主键
 * @param {number} params.activityId 活动id
 * @param {string} params.name 资源名称
 * @param {string} params.fileUrl 附件地址
 * @param {number} params.createBy 创建人
 * @param {object} params.createTime 创建时间
 * @param {number} params.updateBy 更新人
 * @param {object} params.updateTime 更新时间
 * @returns
 */
export function saveActivityResource(data = {}, option = {}) {
	return request({
		url: `/competition/activityResource/add`,
		method: 'POST',
		data,
		...option,
	})
}

/** 
 * 删除大赛资料
 * @param {string} id 
  * @returns
 */
export function deleteActivityResource(data = {}, option = {}) {
	return request({
		url: `/competition/activityResource/delete?id=${data.id}`,
		method: 'POST',
		data,
		...option,
	})
}
/** 
 * 大赛资料详情
 * @param {string} id 
  * @returns
 */
export function getActivityResourceDetail(data = {}, option = {}) {
	return request({
		url: `/competition/activityResource/detail?id=${data.id}`,
		method: 'POST',
		data,
		...option,
	})
}

/** 
 * 大赛资料分页
 * @param {object} params 资料分页参数
 * @param {number} params.pageNum 页码
 * @param {number} params.pageSize 每页记录数
 * @param {string} params.name 名称
 * @param {number} params.activityId 活动id
 * @returns
 */
export function pageActivityResource(data = {}, option = {}) {
	return request({
		url: `/competition/activityResource/page`,
		method: 'POST',
		data,
		...option,
	})
}

/** 
* 修改大赛资料
* @param {object} params 大赛资料
* @param {number} params.id 主键
* @param {number} params.activityId 活动id
* @param {string} params.name 资源名称
* @param {string} params.fileUrl 附件地址
* @param {number} params.createBy 创建人
* @param {object} params.createTime 创建时间
* @param {number} params.updateBy 更新人
* @param {object} params.updateTime 更新时间
* @returns
*/
export function updateActivityResource(data = {}, option = {}) {
	return request({
		url: `/competition/activityResource/update`,
		method: 'POST',
		data,
		...option,
	})
}