import { request } from '@/utils/axios'

/** 
 * 新增回顾
 * @param {object} params 新闻
 * @param {number} params.id 主键
 * @param {number} params.activityId 活动id
 * @param {object} params.startDate 开始日期
 * @param {object} params.endDate 结束日期
 * @param {string} params.address 地址
 * @param {string} params.pictureUrl 图片地址
 * @param {array} params.videos 资源文件关联
 * @param {number} params.createBy 创建人
 * @param {object} params.createTime 创建时间
 * @param {number} params.updateBy 更新人
 * @param {object} params.updateTime 更新时间
 * @returns
 */
export function saveActivityReview(data = {}, option = {}) {
	return request({
		url: `/competition/activityReview/add`,
		method: 'POST',
		data,
		...option,
	})
}
/** 
 * 修改回顾
 * @param {object} params 新闻
 * @param {number} params.id 主键
 * @param {number} params.activityId 活动id
 * @param {object} params.startDate 开始日期
 * @param {object} params.endDate 结束日期
 * @param {string} params.address 地址
 * @param {string} params.pictureUrl 图片地址
 * @param {array} params.videos 资源文件关联
 * @param {number} params.createBy 创建人
 * @param {object} params.createTime 创建时间
 * @param {number} params.updateBy 更新人
 * @param {object} params.updateTime 更新时间
 * @returns
 */
export function updateActivityReview(data = {}, option = {}) {
	return request({
		url: `/competition/activityReview/update`,
		method: 'POST',
		data,
		...option,
	})
}
/**
 * 删除回顾
 * @param {string} id
 * @returns
 */
export function deleteActivityReview(data = {}, option = {}) {
	return request({
		url: `/competition/activityReview/delete?id=${data.id}`,
		method: 'POST',
		data,
		...option,
	})
}
/**
 * 回顾详情
 * @param {string} id
 * @returns
 */
export function getActivityReviewDetail(data = {}, option = {}) {
	return request({
		url: `/competition/activityReview/detail?id=${data.id}`,
		method: 'POST',
		data,
		...option,
	})
}
/**
 * 回顾分页
 * @param {object} params 回顾分页参数
 * @param {number} params.pageNum 页码
 * @param {number} params.pageSize 每页记录数
 * @param {number} params.activityId 赛事id
 * @returns
 */
export function pageActivityReview(data = {}, option = {}) {
	return request({
		url: `/competition/activityReview/page`,
		method: 'POST',
		data,
		...option,
	})
}
