import { request } from '@/utils/axios'

/**
 * 新增大赛通知
 * @param {object} params 大赛通知
 * @param {number} params.id 主键
 * @param {number} params.activityId 活动id
 * @param {string} params.title 标题
 * @param {string} params.publishUnit 发布单位
 * @param {string} params.content 内容
 * @param {number} params.seqNumber 序号
 * @param {string} params.pictureUrl 图片地址
 * @param {array} params.videos 资源文件关联
 * @param {number} params.type 类型 1:通知,2:新闻
 * @param {number} params.createBy 创建人
 * @param {object} params.createTime 创建时间
 * @param {number} params.updateBy 更新人
 * @param {object} params.updateTime 更新时间
 * @returns
 */
export function saveActivityNotice(data = {}, option = {}) {
	return request({
		url: `/competition/activityNotice/add`,
		method: 'POST',
		data,
		...option,
	})
}
/**
 * 修改大赛通知
 * @param {object} params 大赛通知
 * @param {number} params.id 主键
 * @param {number} params.activityId 活动id
 * @param {string} params.title 标题
 * @param {string} params.publishUnit 发布单位
 * @param {string} params.content 内容
 * @param {number} params.seqNumber 序号
 * @param {string} params.pictureUrl 图片地址
 * @param {array} params.videos 资源文件关联
 * @param {number} params.type 类型 1:通知,2:新闻
 * @param {number} params.createBy 创建人
 * @param {object} params.createTime 创建时间
 * @param {number} params.updateBy 更新人
 * @param {object} params.updateTime 更新时间
 * @returns
 */
export function updateActivityNotice(data = {}, option = {}) {
	return request({
		url: `/competition/activityNotice/update`,
		method: 'POST',
		data,
		...option,
	})
}

/**
 * 删除大赛通知
 * @param {string} id
 * @returns
 */
export function deleteActivityNotice(data = {}, option = {}) {
	return request({
		url: `/competition/activityNotice/delete?id=${data.id}`,
		method: 'POST',
		data,
		...option,
	})
}
/**
 * 大赛通知详情
 * @param {string} id
 * @returns
 */
export function getActivityNoticeDetail(data = {}, option = {}) {
	return request({
		url: `/competition/activityNotice/detail?id=${data.id}`,
		method: 'POST',
		data,
		...option,
	})
}

/**
 * 大赛通知分页
 * @param {object} params 通知分页参数
 * @param {number} params.pageNum 页码
 * @param {number} params.pageSize 每页记录数
 * @param {number} params.activityId 赛事id
 * @param {string} params.publishUnit 发布单位
 * @param {string} params.content 赛事内容关键词
 * @param {number} params.type 类型 1:通知,2:新闻
 * @returns
 */
export function pageActivityNotice(data = {}, option = {}) {
	return request({
		url: `/competition/activityNotice/page`,
		method: 'POST',
		data,
		...option,
	})
}
