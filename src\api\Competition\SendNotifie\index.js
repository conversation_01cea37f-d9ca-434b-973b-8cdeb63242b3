import { request } from '@/utils/axios'

/** 
* 发送赛事站内消息通知
* @param {string} file 
* @param {object} params 消息通知发送实体dto
* @param {number} params.id 
* @param {number} params.activityId 活动赛事id
* @param {string} params.notificationTitle 通知标题
* @param {string} params.notificationContent 通知内容
* @param {array} params.creditCodeList 统一信用代码
* @param {string} params.configCode 发送手机短信配置code
* @param {string} params.businessCode 发送手机业务编码code
* @returns
*/
export function sendSite(data = {}, option = {}) {
	return request({
		url: `/competition/notification/sendSite`,
		method: 'POST',
		data,
		header: {
			'content-type': 'multipart/form-data',
		},
		...option,
	})
}
/** 
 * 根据id删除消息通告
 * @param {object} params 通知查询条件
 * @param {number} params.id 消息id
 * @returns
 */
export function delNotification(data = {}, option = {}) {
	return request({
		url: `/competition/notification/delNotification`,
		method: 'POST',
		data,
		...option,
	})
  }

  /** 
 * 分页查询消息通告
 * @param {object} params 通知查询条件
 * @param {number} params.pageNum 页码
 * @param {number} params.pageSize 每页记录数
 * @param {number} params.id 消息id
 * @param {number} params.type 消息类型(1:系统消息,2:站内消息 )
 * @param {number} params.activityId 大赛活动id
 * @param {string} params.title 标题
 * @param {string} params.content 内容
 * @returns
 */
export function pageNotification(data = {}, option = {}) {
	return request({
		url: `/competition/notification/pageNotification`,
		method: 'POST',
		data,
		...option,
	})
  }