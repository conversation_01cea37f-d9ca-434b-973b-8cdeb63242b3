import { request } from '@/utils/axios'
/** 
 * 新增赛事服务
 * @param {object} params 赛事服务
 * @param {number} params.id 主键
 * @param {string} params.name 名称
 * @param {string} params.jumpUrl 跳转地址
 * @param {number} params.status 状态(0:停用,1:启用)
 * @param {number} params.seqNumber 序号
 * @param {string} params.iconUrl 图标地址
 * @param {string} params.description 描述
 * @param {number} params.createBy 创建人
 * @param {object} params.createTime 创建时间
 * @param {number} params.updateBy 更新人
 * @param {object} params.updateTime 更新时间
 * @returns
 */
export function saveActivityService(data = {}, option = {}) {
	return request({
		url: `/competition/activityService/add`,
		method: 'POST',
		data,
		...option,
	})
}
/** 
 * 删除赛事服务
 * @param {string} id 
  * @returns
 */
export function deleteActivityService(data = {}, option = {}) {
	return request({
		url: `/competition/activityService/delete`,
		method: 'POST',
		params: data,
		...option,
	})
}

/** 
 * 赛事服务详情
 * @param {string} id 
  * @returns
 */
export function getActivityServiceDetail(data = {}, option = {}) {
	return request({
		url: `/competition/activityService/detail`,
		method: 'POST',
		params: data,
		...option,
	})
}
/** 
 * 赛事服务分页
 * @param {object} params ActivityServicePageDto
 * @param {number} params.pageNum 页码
 * @param {number} params.pageSize 每页记录数
 * @returns
 */
export function pageActivityService(data = {}, option = {}) {
	return request({
		url: `/competition/activityService/page`,
		method: 'POST',
		data,
		...option,
	})
}

/** 
 * 修改赛事服务
 * @param {object} params 赛事服务
 * @param {number} params.id 主键
 * @param {string} params.name 名称
 * @param {string} params.jumpUrl 跳转地址
 * @param {number} params.status 状态(0:停用,1:启用)
 * @param {number} params.seqNumber 序号
 * @param {string} params.iconUrl 图标地址
 * @param {string} params.description 描述
 * @param {number} params.createBy 创建人
 * @param {object} params.createTime 创建时间
 * @param {number} params.updateBy 更新人
 * @param {object} params.updateTime 更新时间
 * @returns
 */
export function updateActivityService(data = {}, option = {}) {
	return request({
		url: `/competition/activityService/update`,
		method: 'POST',
		data,
		...option,
	})
  }