import { request } from '@/utils/axios';

/**
 * 分页查询参赛企业
 * @param {object} data 参赛企业查询dto
 * @param {number} data.pageNum 页码
 * @param {number} data.pageSize 每页记录数
 * @param {number} data.id
 * @param {array} data.ids
 * @param {number} data.accountId 用户帐号id
 * @param {number} data.useStatus 使用状态： 0 停用 1 启用
 * @param {number} data.auditStatus 审核状态：0 审核中  1 审核不通过 2 审核通过
 * @param {string} data.name 企业名称
 * @param {string} data.creditCode 统一社会信用代码
 * @param {object} data.establishTime 成立时间
 * @param {string} data.provinceCode 所在地区省份编码
 * @param {string} data.cityCode 所在地区城市编码
 * @param {string} data.areaCode 所在地区区域编码
 * @param {string} data.auditReason 审核原因
 * @returns
 */
export function pageEntryEnterprise(data = {}, option = {}) {
	return request({
		url: `/competition/entryEnterprise/page`,
		method: 'POST',
		data,
		...option,
	});
}

/**
 * 批量修改参赛企业停启用状态
 * @param {object} data 参赛企业查询dto
 * @param {number} data.pageNum 页码
 * @param {number} data.pageSize 每页记录数
 * @param {number} data.id
 * @param {array} data.ids
 * @param {number} data.accountId 用户帐号id
 * @param {number} data.useStatus 使用状态： 0 停用 1 启用
 * @param {number} data.auditStatus 审核状态：0 审核中  1 审核不通过 2 审核通过
 * @param {string} data.name 企业名称
 * @param {string} data.creditCode 统一社会信用代码
 * @param {object} data.establishTime 成立时间
 * @param {string} data.provinceCode 所在地区省份编码
 * @param {string} data.cityCode 所在地区城市编码
 * @param {string} data.areaCode 所在地区区域编码
 * @param {string} data.auditReason 审核原因
 * @returns
 */
export function batchUseStatus(data = {}, option = {}) {
	return request({
		url: '/competition/entryEnterprise/batchUseStatus',
		method: 'POST',
		data,
		...option,
	});
}

/**
 * 批量审核参赛企业
 * @param {object} data 参赛企业查询dto
 * @param {number} data.pageNum 页码
 * @param {number} data.pageSize 每页记录数
 * @param {number} data.id
 * @param {array} data.ids
 * @param {number} data.accountId 用户帐号id
 * @param {number} data.useStatus 使用状态： 0 停用 1 启用
 * @param {number} data.auditStatus 审核状态：0 审核中  1 审核不通过 2 审核通过
 * @param {string} data.name 企业名称
 * @param {string} data.creditCode 统一社会信用代码
 * @param {object} data.establishTime 成立时间
 * @param {string} data.provinceCode 所在地区省份编码
 * @param {string} data.cityCode 所在地区城市编码
 * @param {string} data.areaCode 所在地区区域编码
 * @param {string} data.auditReason 审核原因
 * @returns
 */
export function batchAuditStatus(data = {}, option = {}) {
	return request({
		url: '/competition/entryEnterprise/batchAuditStatus',
		method: 'post',
		data,
		...option,
	});
}

/**
 * 获取企业id参赛企业资料详情
 * @param {object} data 参赛企业查询dto
 * @param {number} data.pageNum 页码
 * @param {number} data.pageSize 每页记录数
 * @param {number} data.id
 * @param {array} data.ids
 * @param {number} data.accountId 用户帐号id
 * @param {number} data.useStatus 使用状态： 0 停用 1 启用
 * @param {number} data.auditStatus 审核状态：0 审核中  1 审核不通过 2 审核通过
 * @param {string} data.name 企业名称
 * @param {string} data.creditCode 统一社会信用代码
 * @param {object} data.establishTime 成立时间
 * @param {string} data.provinceCode 所在地区省份编码
 * @param {string} data.cityCode 所在地区城市编码
 * @param {string} data.areaCode 所在地区区域编码
 * @param {string} data.auditReason 审核原因
 * @returns
 */
export function detailEntryEnterprise(data = {}, option = {}) {
	return request({
		url: '/competition/entryEnterprise/detail',
		method: 'post',
		data,
		...option,
	});
}

/**
 * 参赛企业资料导出
 * @param {object} data 参赛企业查询dto
 * @param {number} data.pageNum 页码
 * @param {number} data.pageSize 每页记录数
 * @param {number} data.id
 * @param {array} data.ids
 * @param {number} data.accountId 用户帐号id
 * @param {number} data.useStatus 使用状态： 0 停用 1 启用
 * @param {number} data.auditStatus 审核状态：0 审核中  1 审核不通过 2 审核通过
 * @param {string} data.name 企业名称
 * @param {string} data.creditCode 统一社会信用代码
 * @param {object} data.establishTime 成立时间
 * @param {string} data.provinceCode 所在地区省份编码
 * @param {string} data.cityCode 所在地区城市编码
 * @param {string} data.areaCode 所在地区区域编码
 * @param {string} data.auditReason 审核原因
 * @returns
 */
export function entryEnterpriseExport(data = {}, option = {}) {
	return request({
		url: '/competition/entryEnterprise/export',
		method: 'post',
		data,
		responseType: 'blob',
		...option,
	});
}

/**
 * 修改企业手机和密码
 * @param {object} data 参赛企业查询dto
 * @param {number} data.id id
 * @param {number} data.pwd 密码
 * @param {string} data.contactPersonPhone 手机
 * @returns
 */
export function updatePwdAndPhone(data = {}, option = {}) {
	return request({
		url: '/competition/entryEnterprise/updatePwdAndPhone',
		method: 'post',
		data,
		...option,
	});
}
