import { request } from '@/utils/axios';

/**
 * 分页查询评委
 * @param {object} data 参赛评委查询dto
 * @param {number} data.pageNum 页码
 * @param {number} data.pageSize 每页记录数
 * @param {number} data.id
 * @param {array} data.ids
 * @param {number} data.accountId 用户帐号id
 * @param {number} data.useStatus 使用状态： 0 停用 1 启用
 * @param {string} data.name 评委名称
 * @param {string} data.account 评委账号
 * @returns
 */
export function entryJudgesPage(data = {}, option = {}) {
	return request({
		url: '/competition/entryJudges/page',
		method: 'post',
		data,
		...option,
	});
}

/**
 * 获取评委资料详情
 * @param {object} data 参赛评委查询dto
 * @param {number} data.pageNum 页码
 * @param {number} data.pageSize 每页记录数
 * @param {number} data.id
 * @param {array} data.ids
 * @param {number} data.accountId 用户帐号id
 * @param {number} data.useStatus 使用状态： 0 停用 1 启用
 * @param {string} data.name 评委名称
 * @param {string} data.account 评委账号
 * @returns
 */
export function entryJudgesDetail(data = {}, option = {}) {
	return request({
		url: '/competition/entryJudges/detail',
		method: 'post',
		data,
		...option,
	});
}

/**
 * 评委注册
 * @param {object} data 参赛评委保存dto
 * @param {number} data.id
 * @param {string} data.name 评委名称
 * @param {string} data.account 评委账号
 * @param {string} data.pwd 密码
 * @returns
 */
export function entryJudgesRegister(data = {}, option = {}) {
	return request({
		url: '/competition/entryJudges/register',
		method: 'post',
		data,
		...option,
	});
}

/**
 * 修改评委资料
 * @param {object} data 参赛评委保存dto
 * @param {number} data.id
 * @param {string} data.name 评委名称
 * @param {string} data.account 评委账号
 * @param {string} data.pwd 密码
 * @returns
 */
export function entryJudgesUpdate(data = {}, option = {}) {
	return request({
		url: '/competition/entryJudges/update',
		method: 'post',
		data,
		...option,
	});
}

/**
 * 批量修改评委停启用状态
 * @param {object} data 参赛评委查询dto
 * @param {number} data.pageNum 页码
 * @param {number} data.pageSize 每页记录数
 * @param {number} data.id
 * @param {array} data.ids
 * @param {number} data.accountId 用户帐号id
 * @param {number} data.useStatus 使用状态： 0 停用 1 启用
 * @param {string} data.name 评委名称
 * @param {string} data.account 评委账号
 * @returns
 */
export function batchUseStatus(data = {}, option = {}) {
	return request({
		url: '/competition/entryJudges/batchUseStatus',
		method: 'post',
		data,
		...option,
	});
}

/**
 * 批量删除评委
 * @param {object} data 参赛评委查询dto
 * @param {number} data.pageNum 页码
 * @param {number} data.pageSize 每页记录数
 * @param {number} data.id
 * @param {array} data.ids
 * @param {number} data.accountId 用户帐号id
 * @param {number} data.useStatus 使用状态： 0 停用 1 启用
 * @param {string} data.name 评委名称
 * @param {string} data.account 评委账号
 * @returns
 */
export function entryJudgesBatchDel(data = {}, option = {}) {
	return request({
		url: '/competition/entryJudges/batchDel',
		method: 'post',
		data,
		...option,
	});
}

/**
 * 评委资料导出
 * @param {object} data 参赛评委查询dto
 * @param {number} data.pageNum 页码
 * @param {number} data.pageSize 每页记录数
 * @param {number} data.id
 * @param {array} data.ids
 * @param {number} data.accountId 用户帐号id
 * @param {number} data.useStatus 使用状态： 0 停用 1 启用
 * @param {string} data.name 评委名称
 * @param {string} data.account 评委账号
 * @returns
 */
export function entryJudgesExport(data = {}, option = {}) {
	return request({
		url: '/competition/entryJudges/export',
		data,
		method: 'post',
		responseType: 'blob',
		...option,
	});
}

/**
 * 评委资料导入
 * @param {object} data
 * @param {string} data.file
 * @returns
 */
export const entryJudgesImport = (data = {}) => {
	return request({
		url: `/competition/entryJudges/import`,
		method: 'post',
		data,
		header: {
			'content-type': 'multipart/form-data',
		},
		showLoading: true,
		isWhiteList: false,
	});
};
