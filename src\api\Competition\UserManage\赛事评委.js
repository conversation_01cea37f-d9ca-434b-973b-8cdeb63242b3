/**
 * 活动评委分页
 * @param {object} data ActivityRatingPersonPageDto
 * @param {number} data.pageNum 页码
 * @param {number} data.pageSize 每页记录数
 * @returns
 */
export function pageActivityRatingPerson(data = {}, option = {}) {
	return request({
		url: '/competition/activityRatingPerson/page',
		method: 'post',
		data,
		...option,
	});
}

/**
 * 活动评委详情
 * @param {object} data
 * @param {number} data.id 评委id
 * @returns
 */
export function getActivityRatingPersonDetail(data = {}, option = {}) {
	return request({
		url: '/competition/activityRatingPerson/detail',
		method: 'post',
		param: data,
		...option,
	});
}

/**
 * 新增活动评委
 * @param {object} data 活动评委
 * @param {number} data.id 评委唯一标识ID
 * @param {number} data.activityId 活动唯一标识ID
 * @param {string} data.name 评委姓名
 * @param {string} data.alias 评委别名
 * @param {number} data.groupId 评审组唯一标识
 * @returns
 */
export function saveActivityRatingPerson(data = {}, option = {}) {
	return request({
		url: '/competition/activityRatingPerson/add',
		method: 'post',
		data,
		...option,
	});
}

/**
 * 修改活动评委
 * @param {object} data 活动评委
 * @param {number} data.id 评委唯一标识ID
 * @param {number} data.activityId 活动唯一标识ID
 * @param {string} data.name 评委姓名
 * @param {string} data.alias 评委别名
 * @param {number} data.groupId 评审组唯一标识
 * @returns
 */
export function updateActivityRatingPerson(data = {}, option = {}) {
	return request({
		url: '/competition/activityRatingPerson/update',
		method: 'post',
		data,
		...option,
	});
}

/**
 * 删除活动评委
 * @param {object} data
 * @param {number} data.activityRatingPersonId 评委id
 * @returns
 */
export function deleteActivityRatingPerson(data = {}, option = {}) {
	return request({
		url: '/competition/activityRatingPerson/delete',
		method: 'post',
		param: data,
		...option,
	});
}

/**
 * 活动评委列表
 * @param {object} data ActivityRatingPersonPageDto
 * @param {number} data.pageNum 页码
 * @param {number} data.pageSize 每页记录数
 * @returns
 */
export function listActivityRatingPerson(data = {}, option = {}) {
	return request({
		url: '/competition/activityRatingPerson/list',
		method: 'post',
		data,
		...option,
	});
}
