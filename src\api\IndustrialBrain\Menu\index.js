import { request } from '@/utils/axios';

/**
 * 新增菜单模块
 * @param {object} params 用户信息
 * @param {number} params.id id
 * @param {string} params.pcPermissionModule 菜单模块内容
 * @param {string} params.weChatMiniProgramPermissionModule
 * @param {string} params.permissionName 名称
 * @returns
 */
export function addUserPermission(data = {}) {
	return request({
		url: '/enterprise/user/addUserPermission',
		method: 'post',
		data,
		showLoading: true,
		isWhiteList: false,
	});
}

/**
 * 修改菜单模块
 * @param {object} params 用户信息
 * @param {number} params.id id
 * @param {string} params.pcPermissionModule 菜单模块内容
 * @param {string} params.weChatMiniProgramPermissionModule
 * @param {string} params.permissionName 名称
 * @returns
 */
export function updateUserPermission(data = {}) {
	return request({
		url: '/enterprise/user/updateUserPermission',
		method: 'post',
		data,
		showLoading: true,
		isWhiteList: false,
	});
}

/**
 * 获取菜单模块
 * @returns
 */
export function listUserPermission(data = {}) {
	return request({
		url: '/enterprise/user/listUserPermission',
		method: 'post',
		data,
		showLoading: true,
		isWhiteList: false,
	});
}

/**
 * 删除菜单模块
 * @returns
 */
export function removeUserPermission(data = {}) {
	return request({
		url: '/enterprise/user/removeUserPermission',
		method: 'post',
		data,
		showLoading: true,
		isWhiteList: false,
	});
}
/**
 * 导入企业
 * @returns
 * @param {string} file
 */
export function importEnterpriseByExcel(data = {}) {
	return request({
		url: '/enterprise/import/importEnterpriseByExcel',
		method: 'POST',
		data,
		header: {
			'content-type': 'multipart/form-data',
		},
		showLoading: true,
		isWhiteList: false,
	});
}
