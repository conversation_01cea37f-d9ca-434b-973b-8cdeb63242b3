import { request } from '@/utils/axios';

/**
 * 分页查询用户
 * @param {object} params 企业用户vo
 * @param {number} params.pageNum 页码
 * @param {number} params.pageSize 每页记录数
 * @param {number} params.userId 用户id
 * @param {string} params.userName 用户姓名
 * @param {string} params.mobile 手机号
 * @param {number} params.tenantId
 * @returns
 */
export function pageUsers(data = {}) {
	return request({
		url: '/enterprise/user/pageUsers',
		method: 'post',
		data,
		showLoading: true,
		isWhiteList: false,
	});
}
/**
 * 新增用户信息
 * @param {object} params 用户信息
 * @param {number} params.userId 用户ID
 * @param {string} params.userName 用户姓名
 * @param {array} params.deptIds 部门id列表
 * @param {array} params.roleIds 角色id集合
 * @param {string} params.mobile 手机号码
 * @param {array} params.permissionIds 权限id集合
 * @param {number} params.aiReportNumber ai项目报告初始次数
 * @param {number} params.enterpriseQueryNumber 企业查询初始次数
 * @param {string} params.validStartTime 帐号有效开始时间,格式：yyyy-MM-dd HH:mm:ss
 * @param {string} params.validEndTime 帐号有效结束时间,格式：yyyy-MM-dd HH:mm:ss
 * @returns
 */
export function addUser(data = {}) {
	return request({
		url: '/enterprise/user/addUser',
		method: 'post',
		data,
		showLoading: true,
		isWhiteList: false,
	});
}
/**
 * 修改用户信息
 * @param {object} params 用户信息
 * @param {number} params.userId 用户ID
 * @param {string} params.userName 用户姓名
 * @param {array} params.deptIds 部门id列表
 * @param {array} params.roleIds 角色id集合
 * @param {string} params.mobile 手机号码
 * @param {array} params.permissionIds 权限id集合
 * @param {number} params.aiReportNumber ai项目报告初始次数
 * @param {number} params.enterpriseQueryNumber 企业查询初始次数
 * @param {string} params.validStartTime 帐号有效开始时间,格式：yyyy-MM-dd HH:mm:ss
 * @param {string} params.validEndTime 帐号有效结束时间,格式：yyyy-MM-dd HH:mm:ss
 * @returns
 */
export function updateUser(data = {}) {
	return request({
		url: '/enterprise/user/updateUser',
		method: 'post',
		data,
		showLoading: true,
		isWhiteList: false,
	});
}

/**
 * 判断用户是否在有效期内（传userId）
 * @param {object} params 企业用户vo
 * @param {number} params.pageNum 页码
 * @param {number} params.pageSize 每页记录数
 * @param {number} params.userId 用户id
 * @param {string} params.userName 用户姓名
 * @param {string} params.mobile 手机号
 * @param {number} params.tenantId
 * @returns
 */
export function isUserValid(data = {}) {
	return request({
		url: '/enterprise/user/isUserValid',
		method: 'post',
		data,
		showLoading: true,
		isWhiteList: false,
	});
}

/**
 * 查询用户信息
 * @param {object} params 企业用户vo
 * @param {number} params.pageNum 页码
 * @param {number} params.pageSize 每页记录数
 * @param {number} params.userId 用户id
 * @param {string} params.userName 用户姓名
 * @param {string} params.mobile 手机号
 * @param {number} params.tenantId
 * @returns
 */
export function getUserByUserId(data = {}) {
	return request({
		url: '/enterprise/user/getUserByUserId',
		method: 'post',
		data,
		showLoading: true,
		isWhiteList: false,
	});
}
