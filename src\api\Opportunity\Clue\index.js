/**
 * @description index - 文件描述
 * <AUTHOR>
 *
 * Created on 2025/4/3 14:12
 */

import { request } from '@/utils/axios';
/**
 * 分页查询分类类别值
 * @param {object} params 分类类别值查询dto
 * @param {object} option 请求配置
 * @param {number} params.pageNum 页码
 * @param {number} params.pageSize 每页记录数
 * @param {number} params.id 需求id
 * @param {array} params.ids 需求id(批量操作传ids)
 * @param {string} params.categoryCode 分类编码code
 * @param {string} params.cityType
 * @returns
 */
export function pageCategoryValue(params = {}, option = {}) {
	return request({
		url: `/bidmgt/category/pageCategoryValue`,
		method: 'POST',
		data: params,
		...option,
	});
}

/**
 * 新增线索
 * @param {object} params 线索保存dto
 * @param {number} params.id
 * @param {string} params.clueName 线索名称
 * @param {string} params.clueSourceName 线索来源名称
 * @param {number} params.clueSourceType 线索来源类型： 1用户创建 2 会议派发
 * @param {string} params.clueDesc 线索描述
 * @param {string} params.customName 客户名称
 * @param {number} params.customTypeId 客户类型id
 * @param {number} params.expectCharge 预测收费(单位：万元)
 * @param {number} params.clueDispatchId 线索派发人id
 * @param {number} params.clueDispatchDeptId 线索派发人部门id
 * @param {number} params.clueReceiveId 线索接收人id
 * @param {number} params.clueReceiveDeptId 线索接收人部门id
 * @param {number} params.clueStatus 线索状态：0 等待领取 1 跟进中 2 已转为商机 4 已作废
 * @param {number} params.meetingId 转过来的会议id
 * @param {string} params.reason 作废原因
 * @param {object} params.receiveTime 接收时间
 * @returns
 */
export function addProjectClue(params) {
	return request({
		url: `/bidmgt/projectClue/addProjectClue`,
		method: 'POST',
		data: params,
		header: {},
		showLoading: true,
		isWhiteList: false,
	});
}

/**
 * 列表查询线索
 * @param {object} params 线索查询dto
 * @param {number} params.pageNum 页码
 * @param {number} params.pageSize 每页记录数
 * @param {number} params.id
 * @param {string} params.clueName 线索名称
 * @param {number} params.clueSourceType 线索来源类型： 1用户创建 2 会议派发
 * @param {number} params.clueStatus 线索状态：0 等待领取 1 跟进中 2 已转为商机 4 已作废
 * @param {array} params.clueStatusList
 * @param {number} params.meetingId 会议id
 * @param {array} params.deptIds
 * @param {array} params.useIds
 * @param {array} params.ids
 * @returns
 */
export function listProjectClue(params) {
	return request({
		url: `/bidmgt/projectClue/listProjectClue`,
		method: 'POST',
		data: params,
		header: {},
		showLoading: true,
		isWhiteList: false,
	});
}

/**
 * 线索作废(传id和原因)
 * */
export function updateProjectClueCancel(params) {
	return request({
		url: `/bidmgt/projectClue/updateProjectClueCancel`,
		method: 'POST',
		data: params,
		header: {},
		showLoading: true,
		isWhiteList: false,
	});
}

/**
 * 查询线索详情
 * */
export function getProjectClue(params) {
	return request({
		url: `/bidmgt/projectClue/getProjectClue`,
		method: 'POST',
		data: params,
		header: {},
		showLoading: true,
		isWhiteList: false,
	});
}

/**
 * 新增跟进记录
 * @param {object} params 线索跟进记录保存dto
 * @param {number} params.id
 * @param {number} params.clueId 线索id
 * @param {string} params.latestDevelopments 最新进展
 * @param {string} params.dynamicKeywords 动态关键字
 * @param {string} params.contactInformation 附件
 * @param {array} params.followPersonList 线索跟进记录人保存dto
 * @returns
 */
export function addProjectClueFollowUp(params) {
	return request({
		url: `/bidmgt/projectClue/addProjectClueFollowUp`,
		method: 'POST',
		data: params,
		header: {},
		showLoading: true,
		isWhiteList: false,
	});
}

/**
 * 修改线索
 * @param {object} params 线索保存dto
 * @param {number} params.id
 * @param {string} params.clueName 线索名称
 * @param {string} params.clueSourceName 线索来源名称
 * @param {number} params.clueSourceType 线索来源类型： 1用户创建 2 会议派发
 * @param {string} params.clueDesc 线索描述
 * @param {string} params.customName 客户名称
 * @param {number} params.customTypeId 客户类型id
 * @param {number} params.expectCharge 预测收费(单位：万元)
 * @param {number} params.clueDispatchId 线索派发人id
 * @param {number} params.clueDispatchDeptId 线索派发人部门id
 * @param {number} params.clueReceiveId 线索接收人id
 * @param {number} params.clueReceiveDeptId 线索接收人部门id
 * @param {number} params.clueStatus 线索状态：0 等待领取 1 跟进中 2 已转为商机 3 已作废
 * @param {number} params.meetingId 转过来的会议id
 * @param {string} params.reason 作废原因
 * @param {object} params.receiveTime 接收时间
 * @returns
 */
export function updateProjectClue(params) {
	return request({
		url: `/bidmgt/projectClue/updateProjectClue`,
		method: 'POST',
		data: params,
		header: {},
		showLoading: true,
		isWhiteList: false,
	});
}

/**
 * 根据线索id查询跟进记录
 * @param {object} params 线索跟进记录保存dto
 * @param {number} params.id
 * @param {number} params.clueId 线索id
 * @returns
 */
export function listProjectClueFollowUpByClueIdByClueId(params) {
	return request({
		url: `/bidmgt/projectClue/listProjectClueFollowUpByClueIdByClueId`,
		method: 'POST',
		data: params,
		header: {},
		showLoading: true,
		isWhiteList: false,
	});
}

/**
 * 线索恢复
 * @param {object} params 线索保存dto
 * @param {number} params.id
 * @param {string} params.clueName 线索名称
 * @param {string} params.clueSourceName 线索来源名称
 * @param {number} params.clueSourceType 线索来源类型： 1用户创建 2 会议派发
 * @param {string} params.clueDesc 线索描述
 * @param {string} params.customName 客户名称
 * @param {number} params.customTypeId 客户类型id
 * @param {number} params.expectCharge 预测收费(单位：万元)
 * @param {number} params.clueDispatchId 线索派发人id
 * @param {number} params.clueDispatchDeptId 线索派发人部门id
 * @param {number} params.clueReceiveId 线索接收人id
 * @param {number} params.clueReceiveDeptId 线索接收人部门id
 * @param {number} params.clueStatus 线索状态：0 等待领取 1 跟进中 2 已转为商机 3 已作废
 * @param {number} params.meetingId 转过来的会议id
 * @param {string} params.reason 作废原因
 * @param {object} params.receiveTime 接收时间
 * @returns
 */
export function updateProjectClueRecover(params) {
	return request({
		url: `/bidmgt/projectClue/updateProjectClueRecover`,
		method: 'POST',
		data: params,
		header: {},
		showLoading: true,
		isWhiteList: false,
	});
}

/**
 * 分页查询
 * @param {object} params 线索查询dto
 * @param {number} params.pageNum 页码
 * @param {number} params.pageSize 每页记录数
 * @param {number} params.id
 * @param {string} params.clueName 线索名称
 * @param {number} params.clueSourceType 线索来源类型： 1用户创建 2 会议派发
 * @param {number} params.clueStatus 线索状态：0 等待领取 1 跟进中 2 已转为商机
 * @param {array} params.clueStatusList
 * @param {number} params.meetingId 会议id
 * @param {array} params.deptIds
 * @param {array} params.useIds
 * @param {array} params.ids
 * @param {number} params.clueType 线索类型：1 全部 2 我接收的 3 我派发的 4 回收站
 * @param {number} params.clueDispatchId
 * @param {number} params.clueReceiveId
 * @param {string} params.startTime 开始时间
 * @param {string} params.endTime 结束时间
 * @param {number} params.cancelStatus
 * @returns
 */
export function pageProjectClue(params) {
	return request({
		url: `/bidmgt/projectClue/pageProjectClue`,
		method: 'POST',
		data: params,
		header: {},
		showLoading: false,
		isWhiteList: false,
	});
}

/**
 * 领取线索(传id)
 * @param {object} params 线索保存dto
 * @param {number} params.id
 * @param {string} params.clueName 线索名称
 * @param {string} params.clueSourceName 线索来源名称
 * @param {number} params.clueSourceType 线索来源类型： 1用户创建 2 会议派发
 * @param {string} params.clueDesc 线索描述
 * @param {string} params.customName 客户名称
 * @param {number} params.customTypeId 客户类型id
 * @param {number} params.expectCharge 预测收费(单位：万元)
 * @param {number} params.clueDispatchId 线索派发人id
 * @param {number} params.clueDispatchDeptId 线索派发人部门id
 * @param {number} params.clueReceiveId 线索接收人id
 * @param {number} params.clueReceiveDeptId 线索接收人部门id
 * @param {number} params.clueStatus 线索状态：0 等待领取 1 跟进中 2 已转为商机 3 已作废
 * @param {number} params.meetingId 转过来的会议id
 * @param {string} params.reason 作废原因
 * @param {object} params.receiveTime 接收时间
 * @returns
 */
export function updateProjectClueReceive(params) {
	return request({
		url: `/bidmgt/projectClue/updateProjectClueReceive`,
		method: 'POST',
		data: params,
		header: {},
		showLoading: true,
		isWhiteList: false,
	});
}

/**
 * 更新记录信息
 * */
export function updateProjectClueFollowUp(params) {
	return request({
		url: `/bidmgt/projectClue/updateProjectClueFollowUp`,
		method: 'POST',
		data: params,
		header: {},
		showLoading: true,
		isWhiteList: false,
	});
}

/**
 * 线索导出
 * */
export function exportProjectClue(params) {
	return request({
		url: `/bidmgt/projectClue/export`,
		method: 'POST',
		data: params,
		responseType: 'blob',
		showLoading: true,
		isWhiteList: false,
	});
}
