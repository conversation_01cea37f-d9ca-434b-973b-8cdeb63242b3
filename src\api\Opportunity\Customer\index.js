/**
 * @description index - 文件描述
 * <AUTHOR>
 *
 * Created on 2025/5/20 16:41
 */
import { request } from '@/utils/axios';

/**
 * 添加客户信息
 * @OpenService("crm.front.customer.addCustomer")
 * @Operation(summary = "添加客户信息")
 * @PostMapping("/customer/addCustomer")
 */
export function addCustomer(data = {}) {
	return request({
		url: '/crm/front/customer/addCustomer',
		method: 'post',
		data: data,
	});
}

/**
 * 修改客户信息
 * @OpenService("crm.front.customer.updateCustomer")
 * @Operation(summary = "修改客户信息")
 * @PostMapping("/customer/addCustomer")
 */
export function updateCustomer(data = {}) {
	return request({
		url: '/crm/front/customer/updateCustomer',
		method: 'post',
		data: data,
	});
}

/**
 * 查看客户信息
 * @OpenService("crm.front.customer.detailCustomer")
 * @Operation(summary = "客户详情")
 * @PostMapping("/customer/detailCustomer")
 * */
export function detailCustomer(data = {}) {
	return request({
		url: '/crm/front/customer/detailCustomer',
		method: 'post',
		data: data,
	});
}

/**
 * 客户分页
 * @OpenService("crm.front.customer.pageCustomer")
 * @Operation(summary = "客户分页")
 * @PostMapping("/customer/pageCustomer")
 */
export function pageCustomer(data = {}) {
	return request({
		url: '/crm/front/customer/pageCustomer',
		method: 'post',
		data: data,
	});
}

/**
 *  根据统一社会代码获取企业详情
 *  @OpenService("crm.front.customer.getEnterpriseByCreditCode")
 *  @Operation(summary = "根据统一社会代码获取企业详情")
 *  @PostMapping("/customer/getEnterpriseByCreditCode")
 */
export function getEnterpriseInfo(params = {}) {
	return request({
		url: '/crm/front/customer/getEnterpriseByCreditCode',
		method: 'post',
		params: params,
		showLoading: false,
		isWhiteList: true,
	});
}

/**
 * 新增客户联系人
 * @OpenService("crm.front.customer.addCustomerContact")
 * @Operation(summary = "新增客户联系人")
 * @PostMapping("/visit/addCustomerContact")
 */
export function addCustomerContact(data = {}) {
	return request({
		url: '/crm/front/customer/addCustomerContact',
		method: 'post',
		data: data,
	});
}

/**
 * 更新客户联系人
 * @OpenService("crm.front.customer.updateCustomerContact")
 * @Operation(summary = "更新客户联系人")
 * @PostMapping("/visit/updateCustomerContact")
 */
export function updateCustomerContact(data = {}) {
	return request({
		url: '/crm/front/customer/updateCustomerContact',
		method: 'post',
		data: data,
	});
}

/**
 * 删除客户联系人
 * @OpenService("crm.front.customer.deleteCustomerContact")
 * @Operation(summary = "删除客户联系人")
 * @PostMapping("/visit/deleteCustomerContact")
 */
export function deleteCustomerContact(data = {}) {
	return request({
		url: '/crm/front/customer/deleteCustomerContact',
		method: 'post',
		params: data,
	});
}

/**
 * 更新知识产权
 * @OpenService("crm.front.customer.updateCustomerIntellectualProperty")
 * @Operation(summary = "更新知识产权")
 * @PostMapping("/visit/updateCustomerIntellectualProperty")
 * */
export function updateCustomerIntellectualProperty(data = {}) {
	return request({
		url: '/crm/front/customer/updateCustomerIntellectualProperty',
		method: 'post',
		data: data,
	});
}

/**
 * 新增跟进记录
 * @OpenService("crm.front.customer.addCustomerFollowupRecord")
 * @Operation(summary = "新增跟进记录")
 * @PostMapping("/visit/addCustomerFollowupRecord")
 */
export function addCustomerFollowupRecord(data = {}) {
	return request({
		url: '/crm/front/customer/addCustomerFollowupRecord',
		method: 'post',
		data: data,
	});
}

/**
 * 更新跟进记录
 * @OpenService("crm.front.customer.updateCustomerFollowupRecord")
 * @Operation(summary = "更新跟进记录")
 * @PostMapping("/visit/updateCustomerFollowupRecord")
 */
export function updateCustomerFollowupRecord(data = {}) {
	return request({
		url: '/crm/front/customer/updateCustomerFollowupRecord',
		method: 'post',
		data: data,
	});
}

/**
 * 删除跟进记录
 * @OpenService("crm.front.customer.deleteCustomerFollowupRecord")
 * @Operation(summary = "删除跟进记录")
 * @PostMapping("/visit/deleteCustomerFollowupRecord")
 */
export function deleteCustomerFollowupRecord(data = {}) {
	return request({
		url: '/crm/front/customer/deleteCustomerFollowupRecord',
		method: 'post',
		params: data,
	});
}

/**
 * 新增财务记录
 * @OpenService("crm.front.customer.addCustomerFinance")
 * @Operation(summary = "新增财务记录")
 * @PostMapping("/visit/addCustomerFinance")
 */
export function addCustomerFinance(data = {}) {
	return request({
		url: '/crm/front/customer/addCustomerFinance',
		method: 'post',
		data: data,
	});
}

/**
 * 更新财务记录
 * @OpenService("crm.front.customer.updateCustomerFinance")
 * @Operation(summary = "更新财务记录")
 * @PostMapping("/visit/updateCustomerFinance")
 */
export function updateCustomerFinance(data = {}) {
	return request({
		url: '/crm/front/customer/updateCustomerFinance',
		method: 'post',
		data: data,
	});
}

/**
 * 删除财务记录
 * @OpenService("crm.front.customer.deleteCustomerFinance")
 * @Operation(summary = "删除财务记录")
 * @PostMapping("/visit/deleteCustomerFinance")
 */
export function deleteCustomerFinance(data = {}) {
	return request({
		url: '/crm/front/customer/deleteCustomerFinance',
		method: 'post',
		params: data,
	});
}

/**
 * @OpenService("crm.front.customer.updateCustomerCooperationRecord")
 * @Operation(summary = "更新合作记录")
 * @PostMapping("/visit/updateCustomerCooperationRecord")
 */
export function updateCustomerCooperationRecord(data = {}) {
	return request({
		url: '/crm/front/customer/updateCustomerCooperationRecord',
		method: 'post',
		data: data,
	});
}

/**
 * 新增合作记录
 * @OpenService("crm.front.customer.addCustomerCooperationRecord")
 * @Operation(summary = "新增合作记录")
 * @PostMapping("/visit/addCustomerCooperationRecord")
 */
export function addCustomerCooperationRecord(data = {}) {
	return request({
		url: '/crm/front/customer/addCustomerCooperationRecord',
		method: 'post',
		data: data,
	});
}

/**
 * 删除合作记录
 * @OpenService("crm.front.customer.deleteCustomerCooperationRecord")
 * @Operation(summary = "删除合作记录")
 * @PostMapping("/visit/deleteCustomerCooperationRecord")
 */
export function deleteCustomerCooperationRecord(data = {}) {
	return request({
		url: '/crm/front/customer/deleteCustomerCooperationRecord',
		method: 'post',
		params: data,
	});
}

/**
 * 获取拜访会议
 * @OpenService("crm.front.customer.getPageInvite")
 @Operation(summary = "获取拜访会议")
 @PostMapping("/customer/getPageInvite")
 */
export function getPageInvite(data = {}) {
	return request({
		url: '/crm/front/customer/getPageInvite',
		method: 'post',
		data: data,
	});
}

/**
 * 根据公司名称查询活动
 * @OpenService(value = "achv.event.join.getEventByCompanyName")
 * @PostMapping("/getEventByCompanyName")
 * @Operation(summary  = "根据公司名称查询活动")
 */
export function getEventByCompanyName(data = {}) {
	return request({
		url: '/achv/event/join/getEventByCompanyName',
		method: 'post',
		data: data,
	});
}

/**
 *导入客户Excel文件
 * @OpenService(value = "crm.front.customer.importCustomerData")
 * @PostMapping("/importCustomerData")
 * @Operation(summary  = "importCustomerData")
 */
export function importCustomerData(data = {}) {
	return request({
		url: '/crm/front/customer/importCustomerData',
		method: 'post',
		data,
		header: {
			'content-type': 'multipart/form-data',
		},
		showLoading: true,
		isWhiteList: false,
	});
}

/**
 *导出客户Excel文件
 * @OpenService(value = "crm.front.customer.exportCustomerData")
 * @PostMapping("/exportCustomerData")
 * @Operation(summary  = "exportCustomerData")
 */
export function exportCustomerData(data = {}) {
	return request({
		url: '/crm/front/customer/exportCustomerData',
		method: 'post',
		data: data,
		responseType: 'blob',
	});
}

/**
 * 客户分配
 * @OpenService("crm.customer.customerAllocation")
 * @Operation(summary = "客户分配")
 * @Operation(allocationType = "客户分配")
 * @PostMapping("/customerAllocation")
 * @Schema(description = "分配类型(1:分配 2:认领 3:转移 4:释放)")
 */
export function customerAllocation(data = {}) {
	return request({
		url: '/crm/customer/customerAllocation',
		method: 'post',
		data: data,
	});
}

/**
 * 公海池客户分页
 * @OpenService("crm.customer.pagePoolCustomer")
 * @Operation(summary = "公海池客户分页")
 * @PostMapping("/customer/pagePoolCustomer")
 * */
export function pagePoolCustomer(data = {}) {
	return request({
		url: '/crm/customer/pagePoolCustomer',
		method: 'post',
		data: data,
	});
}
