import { request } from '@/utils/axios';

/**
 * 首页统计数据
 * @returns
 */
export function indexStatistics(params = {}, option = {}) {
	return request({
		url: `/bidmgt/opportunity/indexStatistics`,
		method: 'POST',
		data: params,
		...option,
	});
}

/**
 * 分页查询全员商机排名
 * @param {object} params 商机管理查询条件dto
 * @param {number} params.pageNum 页码
 * @param {number} params.pageSize 每页记录数
 * @param {string} params.sortValue 排序值(传)：desc/asc
 * @param {string} params.sortKey 排序字段,传返回字段名称如：数量-number,进度-schedule,金额-amount,新增数量-new
 * @returns
 */
export function pageProjectOpportunityRanking(params) {
	return request({
		url: `/bidmgt/opportunity/pageProjectOpportunityRanking`,
		method: 'POST',
		data: params,
	});
}

/**
 * 获取客户获取分布统计
 * @OpenService("crm.front.customer.getDistributionStatistics")
 * @Operation(summary = "获取分布统计")
 * @PostMapping("/customer/getDistributionStatistics")
 */
export function getDistributionStatistics(params) {
	return request({
		url: '/crm/front/customer/getDistributionStatistics',
		method: 'post',
		data: params
	})
}

/**
 * 会议统计
 * @OpenService("event.front.visit.getMeetingStatistics")
 * @Operation(summary = "会议统计")
 * @PostMapping("/visit/getMeetingStatistics")
 * public ResponseResult getMeetingStatistics(@RequestBody VisitMeetingQueryDto visitMeetingQueryDto)
 */
export function getMeetingStatistics(params) {
	return request({
		url: '/event/front/visit/getMeetingStatistics',
		method: 'post',
		data: params,
		showLoading: false,
	})
}
