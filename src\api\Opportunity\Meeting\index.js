/**
 * @description index - 文件描述
 * <AUTHOR>
 *
 * Created on 2025/3/6 15:29
 */
import { request } from '@/utils/axios';

/* 文件类型添加 */
export function addFileType(params) {
	return request({
		url: '/event/front/tag/addFileType',
		method: 'post',
		data: params,
	});
}

/* 文件类型更新 */
export function updateFileType(params) {
	return request({
		url: '/event/front/tag/updateFileType',
		method: 'post',
		data: params,
	});
}

/* 文件类型列表 */
export function listFileType(params) {
	return request({
		url: '/event/front/tag/listFileType',
		method: 'post',
		data: params,
		showLoading: false,
	});
}

/* 删除文件类型 */
export function deleteFileType(params) {
	return request({
		url: '/event/front/tag/deleteFileType',
		method: 'post',
		data: params,
	});
}

/* 标签列表 */
export function listTag(params) {
	return request({
		url: '/event/front/tag/list',
		method: 'post',
		data: params,
		showLoading: false,
	});
}

/* 标签添加 */
export function addTag(params) {
	return request({
		url: '/event/front/tag/addTag',
		method: 'post',
		data: params,
	});
}

/* 标签更新 */
export function updateTag(params) {
	return request({
		url: '/event/front/tag/updateTag',
		method: 'post',
		data: params,
	});
}

/* 删除标签 */
export function deleteTag(params) {
	return request({
		url: '/event/front/tag/deleteTag',
		method: 'post',
		data: params,
	});
}

/**
 * 邀约分页
 * @param {object} params 邀约分页参数
 * @param {number} params.pageNum 页码
 * @param {number} params.pageSize 每页记录数
 * @param {number} params.id 主键id
 * @param {array} params.status 状态(0:撤回,1:待登记,2:待确认,3:已安排会议)
 * @returns
 */
export function pageInvite(data = {}) {
	return request({
		url: `/event/front/visit/pageInvite`,
		method: 'POST',
		data,
		header: {},
		showLoading: true,
		isWhiteList: false,
	});
}

/**
 * 查询全部拜访列表（管理端）
 * @param {object} params VisitCallQueryDto
 * @param {number} params.id 主键
 * @param {number} params.status 状态(0:关闭,1:待确认)
 * @param {number} params.type 类型(1:拜访,2:预约)
 * @param {number} params.userId
 * @returns
 */
export function allVisitCall(params) {
	return request({
		url: `/event/front/visit/allCall`,
		method: 'POST',
		data: params,
		header: {},
		showLoading: false,
		isWhiteList: false,
	});
}

/**
 * 邀约详情
 * @param {object} params 邀约分页参数
 * @param {number} params.id 主键id
 * @param {array} params.status 状态(0:撤回,1:待登记,2:待确认,3:已安排会议)
 * @returns
 */
export function detailInvite(data = {}) {
	return request({
		url: `/event/front/visit/detailInvite`,
		method: 'POST',
		data,
		header: {},
		showLoading: true,
		isWhiteList: false,
	});
}

/**
 * 来访详情
 * @param {string} id
 * @returns
 */
export function registrationDetail(data = {}) {
	return request({
		url: `/event/front/visit/registrationDetail`,
		method: 'POST',
		data,
		header: {},
		showLoading: true,
		isWhiteList: false,
	});
}

/**
 * 邀约添加
 * @param {object} params 来访邀约
 * @param {number} params.id 主键
 * @param {string} params.title 标题
 * @param {string} params.inviter 邀请人
 * @param {string} params.contactPhone 联系电话
 * @param {string} params.companyName 企业名称
 * @param {string} params.address 会议地址
 * @param {number} params.status 状态(0:撤回,1:待登记,2:待确认,3:已安排会议,4:拒绝)
 * @param {array} params.fieldTags 字段标签表
 * @param {number} params.createBy 创建人
 * @param {object} params.createTime 创建时间
 * @param {number} params.updateBy 更新人
 * @param {object} params.updateTime 更新时间
 * @returns
 */
export function addInvite(data = {}) {
	return request({
		url: `/event/front/visit/addInvite`,
		method: 'POST',
		data,
		header: {},
		showLoading: true,
		isWhiteList: false,
	});
}
/**
 * 邀约更新
 * @param {object} params 来访邀约
 * @param {number} params.id 主键
 * @param {string} params.title 标题
 * @param {string} params.inviter 邀请人
 * @param {string} params.contactPhone 联系电话
 * @param {string} params.companyName 企业名称
 * @param {string} params.address 会议地址
 * @param {number} params.status 状态(0:撤回,1:待登记,2:待确认,3:已安排会议,4:拒绝)
 * @param {array} params.fieldTags 字段标签表
 * @param {number} params.createBy 创建人
 * @param {object} params.createTime 创建时间
 * @param {number} params.updateBy 更新人
 * @param {object} params.updateTime 更新时间
 * @returns
 */
export function updateInvite(data = {}) {
	return request({
		url: `/event/front/visit/updateInvite`,
		method: 'POST',
		data,
		header: {},
		showLoading: true,
		isWhiteList: false,
	});
}

/**
 * 会议导出
 * */
export function exportMeeting(data = {}) {
	return request({
		url: `/event/front/open/visit/exportMeeting`,
		method: 'POST',
		data,
		responseType: 'blob',
		showLoading: true,
		isWhiteList: false,
	});
}

/**
 * 会议详情
 * @param {object} params VisitMeetingQueryDto
 * @param {number} params.id 主键id
 * @param {array} params.inviteIds 邀约id
 * @returns
 */
export function detailMeeting(data = {}) {
	return request({
		url: `/event/front/visit/detailMeeting`,
		method: 'POST',
		data,
		header: {},
		showLoading: true,
		isWhiteList: false,
	});
}

/**
 * 议程列表
 * @param {object} params 议程查询条件
 * @param {number} params.visitId 来访id
 * @param {number} params.meetingId 来访id
 * @returns
 */
export function listAgenda(data = {}) {
	return request({
		url: `/event/front/visit/listAgenda`,
		method: 'POST',
		data,
		header: {},
		showLoading: true,
		isWhiteList: false,
	});
}
/**
 * 议程修改
 * @param {object} params 访问议程
 * @param {number} params.id 主键
 * @param {number} params.visitId 来访id
 * @param {object} params.startTime 结束时间
 * @param {object} params.endTime 结束时间
 * @param {string} params.theme 主题
 * @param {string} params.sharer 分享者
 * @param {number} params.createBy 创建人
 * @param {object} params.createTime 创建时间
 * @param {number} params.updateBy 更新人
 * @param {object} params.updateTime 更新时间
 * @returns
 */
export function updateAgenda(data = {}) {
	return request({
		url: `/event/front/visit/updateAgenda`,
		method: 'POST',
		data,
		header: {},
		showLoading: true,
		isWhiteList: false,
	});
}
/**
 * 议程添加
 * @param {object} params 访问议程
 * @param {number} params.id 主键
 * @param {number} params.visitId 来访id
 * @param {object} params.startTime 结束时间
 * @param {object} params.endTime 结束时间
 * @param {string} params.theme 主题
 * @param {string} params.sharer 分享者
 * @param {number} params.createBy 创建人
 * @param {object} params.createTime 创建时间
 * @param {number} params.updateBy 更新人
 * @param {object} params.updateTime 更新时间
 * @returns
 */
export function addAgenda(data = {}) {
	return request({
		url: `/event/front/visit/addAgenda`,
		method: 'POST',
		data,
		header: {},
		showLoading: true,
		isWhiteList: false,
	});
}
/**
 * 议程删除
 * @param {object} params 议程查询条件
 * @param {number} params.visitId 来访id
 * @param {number} params.id 主键id
 * @returns
 */
export function deleteAgenda(data = {}) {
	return request({
		url: `/event/front/visit/deleteAgenda`,
		method: 'POST',
		data,
		header: {},
		showLoading: true,
		isWhiteList: false,
	});
}
/**
 * 安排会议
 * @param {object} params 来访会议室
 * @param {number} params.id 主键
 * @param {number} params.inviteId 邀约id
 * @param {string} params.theme 主题
 * @param {object} params.meetingStartTime 会议开始时间
 * @param {string} params.address 地址
 * @param {string} params.contactPerson 联系人
 * @param {string} params.contactPhone 联系电话
 * @param {number} params.createBy 创建人
 * @param {object} params.createTime 创建时间
 * @param {number} params.updateBy 更新人
 * @param {object} params.updateTime 更新时间
 * @param {array} params.fieldTags 字段标签表
 * @returns
 */
export function addMeeting(data = {}) {
	return request({
		url: `/event/front/visit/addMeeting`,
		method: 'POST',
		data,
		header: {},
		showLoading: true,
		isWhiteList: false,
	});
}
/**
 * 会议修改
 * @param {object} params 来访会议室
 * @param {number} params.id 主键
 * @param {number} params.inviteId 邀约id
 * @param {string} params.theme 主题
 * @param {object} params.meetingStartTime 会议开始时间
 * @param {string} params.address 地址
 * @param {string} params.contactPerson 联系人
 * @param {string} params.contactPhone 联系电话
 * @param {number} params.createBy 创建人
 * @param {object} params.createTime 创建时间
 * @param {number} params.updateBy 更新人
 * @param {object} params.updateTime 更新时间
 * @param {array} params.fieldTags 字段标签表
 * @returns
 */
export function updateMeeting(data = {}) {
	return request({
		url: `/event/front/visit/updateMeeting`,
		method: 'POST',
		data,
		header: {},
		showLoading: true,
		isWhiteList: false,
	});
}

/**
 * 会议订阅
 * @param {object} params
 * @param {number} params.meetingId 主键
 * @param {number} params.openId 邀约id
 * @returns
 */
export function addMeetingSubscribe(data = {}) {
	return request({
		url: `/event/front/visit/addMeetingSubscribe`,
		method: 'POST',
		data,
		header: {},
		showLoading: true,
		isWhiteList: false,
	});
}

/**
 * 查询是否订阅
 * @param {object} params
 * @param {number} params.meetingId 主键
 * @param {number} params.openId 邀约id
 * @returns
 */
export function subscribeOrNot(data = {}) {
	return request({
		url: `/event/front/visit/subscribeOrNot`,
		method: 'POST',
		data,
		header: {},
		showLoading: true,
		isWhiteList: false,
	});
}

/**
 * 批量新增活动议程
 * @param {object} params 活动议程批量操作dto
 * @param {number} params.id
 * @param {array} params.eventAgendaList 活动议程
 * @returns
 */
export function batchAddFieldTag(params) {
	return request({
		url: `/event/front/visit/fieldTag/batchAdd`,
		method: 'POST',
		data: params,
		header: {},
		showLoading: true,
		isWhiteList: false,
	});
}

/**
 * 字段标签缓存列表
 * @param {object} params 字段标签
 * @param {array} params.sourceIds 源id
 * @param {string} params.sourceCode 源编码
 * @returns
 */
export function listFieldTagCache(params) {
	return request({
		url: `/event/front/visit/fieldTag/cacheList`,
		method: 'POST',
		data: params,
		header: {},
		showLoading: true,
		isWhiteList: false,
	});
}

/**
 * 字段标签列表
 * @param {object} params 字段标签
 * @param {array} params.sourceIds 源id
 * @param {string} params.sourceCode 源编码
 * @returns
 */
export function listFieldTag(params) {
	return request({
		url: `/event/front/visit/fieldTag/list`,
		method: 'POST',
		data: params,
		header: {},
		showLoading: true,
		isWhiteList: false,
	});
}
/**
 * 字段标签删除
 * @param {object} params 字段标签
 * @param {array} params.sourceIds 源id
 * @param {string} params.sourceCode 源编码
 * @returns
 */
export function deleteFieldTag(params) {
	return request({
		url: `/event/front/visit/fieldTag/delete`,
		method: 'POST',
		data: params,
		header: {},
		showLoading: true,
		isWhiteList: false,
	});
}

/**
 * 来访预约
 * @param {object} params 来访登记
 * @param {number} params.id 主键
 * @param {string} params.companyName 企业名称
 * @param {number} params.purpose 目的(1:商务合作交流,2:考察调研)
 * @param {string} params.description 描述
 * @param {object} params.visitTime 访问时间
 * @param {string} params.contactPerson 联系人
 * @param {string} params.contactPhone 联系电话
 * @param {number} params.status 状态
 * @param {string} params.theme 主题
 * @param {number} params.createBy 创建人
 * @param {object} params.createTime 创建时间
 * @param {number} params.updateBy 更新人
 * @param {object} params.updateTime 更新时间
 * @param {array} params.participants 参与人
 * @returns
 */
export function addRegistration(data = {}) {
	return request({
		url: `/event/front/visit/addRegistration`,
		method: 'POST',
		data,
		header: {},
		showLoading: true,
		isWhiteList: false,
	});
}
/**
 * 来访修改
 * @param {object} params 来访登记
 * @param {number} params.id 主键
 * @param {string} params.companyName 企业名称
 * @param {number} params.purpose 目的(1:商务合作交流,2:考察调研)
 * @param {string} params.description 描述
 * @param {object} params.visitTime 访问时间
 * @param {string} params.contactPerson 联系人
 * @param {string} params.contactPhone 联系电话
 * @param {number} params.status 状态
 * @param {string} params.theme 主题
 * @param {number} params.createBy 创建人
 * @param {object} params.createTime 创建时间
 * @param {number} params.updateBy 更新人
 * @param {object} params.updateTime 更新时间
 * @param {array} params.participants 参与人
 * @returns
 */
export function updateRegistration(data = {}) {
	return request({
		url: `/event/front/visit/updateRegistration`,
		method: 'POST',
		data,
		header: {},
		showLoading: true,
		isWhiteList: false,
	});
}
/**
 * 会议添加人员
 * @param {object} params 参与人
 * @param {number} params.id 主键
 * @param {number} params.visitId 来访id
 * @param {number} params.sourceId 会议id
 * @param {string} params.name 名称
 * @param {string} params.position 职位
 * @param {number} params.type 类型(1:来访人员,2:接待人员,3:车牌号)
 * @param {string} params.licensePlateNumber 车牌号
 * @param {number} params.personType 人员类型(1:来访添加人员,2:会议添加人员)
 * @param {number} params.status 人员状态
 * @param {number} params.createBy 创建人
 * @param {object} params.createTime 创建时间
 * @param {number} params.updateBy 更新人
 * @param {object} params.updateTime 更新时间
 * @returns
 */
export function addParticipant(params) {
	return request({
		url: `/event/front/visit/addParticipant`,
		method: 'POST',
		data: params,
		header: {},
		showLoading: true,
		isWhiteList: false,
	});
}

/**
 * 会议访客保存
 * @param {object} params 会议访客
 * @param {number} params.meetingId 会议id
 * @param {number} params.userId 用户id
 * @param {string} params.username 用户名
 * @param {string} params.wxAvatarUrl 微信头像
 * @returns
 */
export function saveMeetingVisitor(params) {
	return request({
		url: `/event/front/visit/saveMeetingVisitor`,
		method: 'POST',
		data: params,
		header: {},
		showLoading: true,
		isWhiteList: false,
	});
}
/*		会议纪要		*/
/**
 * 会议纪要详情
 * @param {object} params 会议纪要查询参数
 * @param {number} params.id 会议纪要id
 * @returns
 */
export function detailMeetingMinutes(params) {
	return request({
		url: `/event/front/visit/detailMeetingMinutes`,
		method: 'POST',
		data: params,
		header: {},
		showLoading: true,
		isWhiteList: false,
	});
}
/**
 * 会议纪要 访客
 * @param {object} params 会议纪要访客
 * @param {number} params.minutesId 纪要id
 * @param {number} params.meetingId 会议id
 * @param {number} params.userId 用户id
 * @param {number} params.edition 版本
 * @returns
 */
export function addMeetingMinutesVisitor(params) {
	return request({
		url: `/event/front/visit/addMeetingMinutesVisitor`,
		method: 'POST',
		data: params,
		header: {},
		showLoading: true,
		isWhiteList: false,
	});
}

/**
 * 添加会议纪要
 * @param {object} params 会议纪要入参
 * @param {number} params.id id
 * @param {number} params.meetingId 会议id
 * @param {string} params.title 标题
 * @param {string} params.content 内容
 * @param {string} params.attachmentUrl 附件url
 * @param {string} params.attachmentName 附件name
 * @param {string} params.attachmentSize 附件size
 * @param {number} params.edition 版本号
 * @returns
 */
export function addMeetingMinutes(params) {
	return request({
		url: `/event/front/visit/addMeetingMinutes`,
		method: 'POST',
		data: params,
		header: {},
		showLoading: true,
		isWhiteList: false,
	});
}

/**
 * 修改会议纪要
 * @param {object} params 会议纪要入参
 * @param {number} params.id id
 * @param {number} params.meetingId 会议id
 * @param {string} params.title 标题
 * @param {string} params.content 内容
 * @param {string} params.attachmentUrl 附件url
 * @param {string} params.attachmentName 附件name
 * @param {string} params.attachmentSize 附件size
 * @param {number} params.edition 版本号
 * @returns
 */
export function updateMeetingMinutes(params) {
	return request({
		url: `/event/front/visit/updateMeetingMinutes`,
		method: 'POST',
		data: params,
		header: {},
		showLoading: true,
		isWhiteList: false,
	});
}
/**
 * 删除会议纪要
 * @param {object} params 会议纪要查询参数
 * @param {number} params.id 会议纪要id
 * @returns
 */
export function deleteMeetingMinutes(params) {
	return request({
		url: `/event/front/visit/deleteMeetingMinutes`,
		method: 'POST',
		data: params,
		header: {},
		showLoading: true,
		isWhiteList: false,
	});
}
/**
 * 检查是否读取最新会议纪要
 * @param {object} params 会议纪要访客查询参数
 * @param {number} params.minutesId 会议纪要id
 * @returns
 */
export function checkReadNewestMeetingMinutes(params) {
	return request({
		url: `/event/front/visit/checkReadNewestMeetingMinutes`,
		method: 'POST',
		data: params,
		header: {},
		showLoading: true,
		isWhiteList: false,
	});
}
/**
 * 会议纪要发送短信通知
 * @param {object} params 会议纪要查询参数
 * @param {string} params.id 会议纪要id
 * @param {string} params.meetingId 会议id
 * @returns
 */
export function minutesSms(params) {
	return request({
		url: `/event/front/visit/minutesSms`,
		method: 'POST',
		data: params,
		header: {},
		showLoading: true,
		isWhiteList: false,
	});
}

/**
 * 文档内容获取（模型：qwen-long）
 * @param {object} params ExtractMeetingMinutesDto
 * @param {number} params.meetingId 会议唯一标识
 * @param {string} params.url 会议纪要文档地址
 * @returns
 */
export function extractMeetingMinutes(params) {
	return request({
		url: `/event/front/visit/extractMeetingMinutes`,
		method: 'POST',
		data: params,
		header: {},
		showLoading: true,
		isWhiteList: false,
	});
}

/* 离线会议纪要语音识别（模型：通义灵悟） */
export function meetingMinutesRecognition(params) {
	return request({
		url: '/event/front/visit/meetingMinutesAsr',
		method: 'post',
		data: params,
		showLoading: false,
	});
}
/* 获取音频转化状态及内容（模型：通义灵悟） */
export function getMeetingMinutesContent(params) {
	return request({
		url: '/event/front/visit/getMeetingMinutesContent',
		method: 'post',
		data: params,
		showLoading: false,
	});
}

/**
 * 删除邀约
 * @param {object} params 邀约分页参数
 * @param {number} params.pageNum 页码
 * @param {number} params.pageSize 每页记录数
 * @param {number} params.id 主键id
 * @param {array} params.status 状态(0:撤回,1:待登记,2:待确认,3:已安排会议)
 * @returns
 */
export function deleteInvite(data = {}) {
	return request({
		url: `/event/front/visit/deleteInvite`,
		method: 'POST',
		data,
		header: {},
		showLoading: true,
		isWhiteList: false,
	});
}
/**
 * 邀约撤回(状态修改)
 * @param {object} params 邀请状态参数
 * @param {number} params.id 主键
 * @param {number} params.status 状态(0:撤回,1:待登记,2:待确认,3:已安排会议)
 * @returns
 */
export function updateInviteStatus(data = {}) {
	return request({
		url: `/event/front/visit/updateInviteStatus`,
		method: 'POST',
		data,
		header: {},
		showLoading: true,
		isWhiteList: false,
	});
}
