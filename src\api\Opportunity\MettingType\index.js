/**
 * @description index - 文件描述
 * <AUTHOR>
 *
 * Created on 2025/4/28 11:23
 */
import { request } from '@/utils/axios'

/**
 * 会议类型添加
 * @OpenService("event.front.visit.addMeetingType")
 * @Operation(summary = "会议类型添加")
 * @PostMapping("/visit/addMeetingType")
 */
export function addMeetingType(params) {
  return request({
    url: '/event/front/visit/addMeetingType',
    method: 'post',
    data: params,
    showLoading: false,
  })
}

/**
 * 会议类型更新
 * @OpenService("event.front.visit.updateMeetingType")
 * @Operation(summary = "会议类型更新")
 * @PostMapping("/visit/updateMeetingType")
 * */
export function updateMeetingType(params) {
  return request({
    url: '/event/front/visit/updateMeetingType',
    method: 'post',
    data: params,
    showLoading: false,
  })
}

/**
 * 会议类型列表
 * @OpenService("event.front.visit.listMeetingType")
 * @Operation(summary = "会议类型列表")
 * @PostMapping("/visit/listMeetingType")
 */
export function listMeetingType(params) {
  return request({
    url: '/event/front/visit/listMeetingType',
    method: 'post',
    data: params,
    showLoading: false,
  })
}

/**
 * 删除会议类型
 * @OpenService("event.front.visit.deleteMeetingType")
 * @Operation(summary = "删除会议类型")
 * @PostMapping("/visit/deleteMeetingType")
 */
export function deleteMeetingType(params) {
  return request({
    url: '/event/front/visit/deleteMeetingType',
    method: 'post',
    data: params,
    showLoading: false,
  })
}
