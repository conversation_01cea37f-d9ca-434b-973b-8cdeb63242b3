import { request } from '@/utils/axios';

/**
 * 新增商机
 * @param {object} params 商机管理保存dto
 * @param {number} params.id
 * @param {string} params.projectOpportunityName 项目商机名称
 * @param {number} params.projectTypeId 项目类型id
 * @param {number} params.customTypeId 客户类型id
 * @param {number} params.productTypeId 产品分类id
 * @param {number} params.expectCharge 预测收费(单位：万元)
 * @param {string} params.expectCooperateProduct 预期合作产品
 * @param {number} params.projectStageId 项目阶段id
 * @param {number} params.projectReliabilityId 商机可靠度分类id
 * @param {string} params.latestDevelopments 跟新进展
 * @param {number} params.projectHandlingId 项目责任人id
 * @param {number} params.projectHandlingDeptId 项目负责部门id
 * @param {string} params.partakeDept 参与部门
 * @returns
 */
export function addOpportunity(params = {}, option = {}) {
	return request({
		url: `/bidmgt/opportunity/addOpportunity`,
		method: 'POST',
		data: params,
		...option,
	});
}
/**
 * 修改商机
 * @param {object} params 商机管理保存dto
 * @param {number} params.id
 * @param {string} params.projectOpportunityName 项目商机名称
 * @param {number} params.projectTypeId 项目类型id
 * @param {number} params.customTypeId 客户类型id
 * @param {number} params.productTypeId 产品分类id
 * @param {number} params.expectCharge 预测收费(单位：万元)
 * @param {string} params.expectCooperateProduct 预期合作产品
 * @param {number} params.projectStageId 项目阶段id
 * @param {number} params.projectReliabilityId 商机可靠度分类id
 * @param {string} params.latestDevelopments 跟新进展
 * @param {number} params.projectHandlingId 项目责任人id
 * @param {number} params.projectHandlingDeptId 项目负责部门id
 * @param {string} params.partakeDept 参与部门
 * @returns
 */
export function updateOpportunity(params = {}, option = {}) {
	return request({
		url: `/bidmgt/opportunity/updateOpportunity`,
		method: 'POST',
		data: params,
		...option,
	});
}

/**
 * 更新置顶状态
 * @param {object} params
 * @param {number} params.id id
 * @param {array} params.topStatus 置顶状态 0 不置顶 1 置顶
 */
export function updateTopStatus(params = {}, option = {}) {
	return request({
		url: `/bidmgt/opportunity/updateTopStatus`,
		method: 'POST',
		data: params,
		...option,
	});
}

/**
 * 批量删除商机
 * @param {object} params 商机管理查询条件dto
 * @param {number} params.id id
 * @param {array} params.ids ids
 */
export function batchDelOpportunity(params = {}, option = {}) {
	return request({
		url: `/bidmgt/opportunity/batchDelOpportunity`,
		method: 'POST',
		data: params,
		...option,
	});
}
/**
 *  更新商机暂停状态
 * @param {array} params.ids ids
 * @returns
 */
export function updateSuspendStatus(params, option = {}) {
	return request({
		url: `/bidmgt/opportunity/updateSuspendStatus`,
		method: 'POST',
		data: params,
		...option,
	});
}
/**
 * 获取商机详情
 * @param {object} params 商机管理查询条件dto
 * @param {number} params.id id
 * @returns
 */
export function detailOpportunity(params = {}, option = {}) {
	return request({
		url: `/bidmgt/opportunity/detailOpportunity`,
		method: 'POST',
		data: params,
		...option,
	});
}
/**
 *  更新项目阶段
 * @param {object} params 项目管理阶段时间更新dto
 * @param {number} params.updateProjectStageId 项目阶段id
 * @param {number} params.reviewMeetingStatus 过会状态： 0未过会 1已过会
 * @param {number} params.projectId 项目id
 * @returns
 */
export function updateProjectStage(params = {}, option = {}) {
	return request({
		url: `/bidmgt/opportunity/updateProjectStage`,
		method: 'POST',
		data: params,
		...option,
	});
}
/**
 * 发送项目预警
 * @param {object} params 发送项目预警内容dto
 * @param {number} params.projectId 项目id
 * @param {string} params.earlyWarningContent 预警内容
 * @param {number} params.noticeType 预警类型：1预警 2催办  3消息
 * @returns
 */
export function sendEarlyWarning(params = {}, option = {}) {
	return request({
		url: `/bidmgt/opportunity/sendEarlyWarning`,
		method: 'POST',
		data: params,
		...option,
	});
}

/**
 * 分页查询商机
 * @param {object} params 商机管理查询条件dto
 * @param {number} params.pageNum 页码
 * @param {number} params.pageSize 每页记录数
 * @param {number} params.id id
 * @param {array} params.ids ids
 * @param {string} params.projectOpportunityName 项目商机名称
 * @param {number} params.projectTypeId 项目类型id
 * @param {number} params.customTypeId 客户类型id
 * @param {number} params.productTypeId 产品分类id
 * @param {number} params.projectStageId 项目阶段id
 * @param {number} params.projectReliabilityId 商机可靠度分类id
 * @param {number} params.projectHandlingDeptId 项目负责部门id
 * @param {number} params.userId
 * @returns
 */
export function pageOpportunity(params = {}, option = {}) {
	return request({
		url: `/bidmgt/opportunity/pageOpportunity`,
		method: 'POST',
		data: params,
		...option,
	});
}
/**
 * 不分页查询商机
 * @param {object} params 商机管理查询条件dto
 * @param {number} params.id id
 * @param {array} params.ids ids
 * @param {string} params.projectOpportunityName 项目商机名称
 * @param {number} params.projectTypeId 项目类型id
 * @param {number} params.customTypeId 客户类型id
 * @param {number} params.productTypeId 产品分类id
 * @param {number} params.projectStageId 项目阶段id
 * @param {number} params.projectReliabilityId 商机可靠度分类id
 * @param {number} params.projectHandlingDeptId 项目负责部门id
 * @param {number} params.userId
 * @returns
 */
export function listOpportunity(params = {}, option = {}) {
	return request({
		url: `/bidmgt/opportunity/front/listOpportunity`,
		method: 'POST',
		data: params,
		...option,
	});
}

/**
 * 项目导入
 * @param {string} file
 * @returns
 */
export function opportunityImport(params = {}, option = {}) {
	return request({
		url: `/bidmgt/opportunity/opportunityImport`,
		method: 'POST',
		data: params,
		header: {
			'Content-Type': 'multipart/form-data',
		},
		...option,
	});
}

/**
 * 商机导出
 * @param {object} params 项目管理内容查询dto
 * @param {number} params.pageNum 页码
 * @param {number} params.pageSize 每页记录数
 * @param {number} params.id id
 * @param {array} params.ids ids
 * @param {number} params.industryId 产业id
 * @param {string} params.projectName 项目名称
 * @param {string} params.projectCode 项目编号
 * @param {number} params.investmentResponsibleId  招商责任单位id
 * @param {number} params.projectStageId 项目阶段id
 * @param {number} params.projectTypeId 项目类型id
 * @param {number} params.coordinatedResolutionStatus 是否需要区领导协调解决： 0 否 1是
 * @param {number} params.investmentScaleStart 投资规模范围开始值(单位：亿元)
 * @param {number} params.investmentScaleEnd 投资规模范围结束值(单位：亿元)
 * @param {string} params.createTimeStart 上报开始时间
 * @param {string} params.createTimeEnd 上报结束时间
 * @param {number} params.reviewMeetingStatus 过会状态： 0未过会 1已过会
 * @param {number} params.userId
 * @returns
 */
export function opportunityExport(params = {}, option = {}) {
	return request({
		url: `/bidmgt/opportunity/opportunityExport`,
		method: 'POST',
		data: params,
		...option,
	});
}

/**
 * 新增-取消跟踪项目
 * @param {object} params 跟踪的项目保存dto
 * @param {number} params.projectId 项目id
 * @returns
 */
export function opportunityFollow(params = {}, option = {}) {
	return request({
		url: `/bidmgt/projectFollow/opportunityFollow`,
		method: 'POST',
		data: params,
		...option,
	});
}

/**
 * 分页查询商机统计
 * @param {object} params 商机管理查询条件dto
 * @param {number} params.pageNum 页码
 * @param {number} params.pageSize 每页记录数
 * @param {number} params.id id
 * @param {array} params.ids ids
 * @param {string} params.projectOpportunityName 项目商机名称
 * @param {number} params.projectTypeId 项目类型id
 * @param {number} params.customTypeId 客户类型id
 * @param {number} params.productTypeId 产品分类id
 * @param {number} params.projectStageId 项目阶段id
 * @param {number} params.projectReliabilityId 商机可靠度分类id
 * @param {number} params.projectHandlingDeptId 项目负责部门id
 * @param {array} params.projectHandlingDeptIds
 * @param {array} params.projectStageIds 项目阶段ids
 * @param {number} params.userId
 * @param {number} params.isFilterData
 * @param {string} params.sortValue 排序值(传)：desc/asc
 * @param {string} params.sortKey 排序字段,传返回字段名称如：创建时间-createTime 预测收费-expectCharge 进度-stage 上报时间-reportTime 跟进时间-followUpTime
 * @param {number} params.suspendStatus  商机暂停状态：0 没暂停 1已暂停
 * @param {number} params.topStatus  置顶状态： 0 否 1置顶
 * @param {object} params.reportStartTime 上报开始时间,格式：yyyy-MM-dd
 * @param {object} params.reportEndTime 上报结束时间,格式：yyyy-MM-dd
 * @returns
 */
export function getProjectOpportunityProjectTypeStatistics(params = {}, option = {}) {
	return request({
		url: `/bidmgt/opportunity/pageOpportunityStatistics`,
		method: 'POST',
		data: params,
		...option,
	});
}

/**
 * 获取测评试卷
 * @returns
 */
export function getOpportunityAssessExamVo(params) {
	return request({
		url: `/bidmgt/opportunity/getOpportunityAssessExam`,
		method: 'POST',
		data: params,
	});
}

/**
 * 提交测评
 * @param {object} params 商机测评考试分数Dto
 * @param {number | string} params.assessExaId 试卷id
 * @param {number | string} params.opportunityId 商机id
 * @param {array} params.answerList 商机测评考试科目vo
 * @returns
 */
export function saveAnswerContentAndScore(params) {
	return request({
		url: `/bidmgt/opportunity/saveAnswerContentAndScore`,
		method: 'POST',
		data: params,
	});
}
/**
 * 获取商机最新测评记录
 * @param {object} params 商机测评考试分数Dto
 * @param {number} params.opportunityId 商机id
 * @returns
 */
export function getOpportunityAssessScoreVo(params) {
	return request({
		url: `/bidmgt/opportunity/getOpportunityAssessScore`,
		method: 'POST',
		data: params,
	});
}
