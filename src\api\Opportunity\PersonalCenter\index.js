import { request } from '@/utils/axios';
/**
 * 商机 分页查询我的项目
 * @param {object} params 商机管理查询条件dto
 * @param {number} params.pageNum 页码
 * @param {number} params.pageSize 每页记录数
 * @param {number} params.id id
 * @param {array} params.ids ids
 * @param {string} params.projectOpportunityName 项目商机名称
 * @param {number} params.projectTypeId 项目类型id
 * @param {number} params.customTypeId 客户类型id
 * @param {number} params.productTypeId 产品分类id
 * @param {number} params.projectStageId 项目阶段id
 * @param {number} params.projectReliabilityId 商机可靠度分类id
 * @param {number} params.projectHandlingDeptId 项目负责部门id
 * @param {number} params.userId
 * @returns
 */
export function myOpportunityPage(params = {}, option = {}) {
	return request({
		url: `/bidmgt/opportunity/myOpportunityPage`,
		method: 'POST',
		data: params,
		...option,
	});
}

/**
 * 商机 分页查询消息列表
 * @param {object} params 通知查询条件
 * @param {number} params.pageNum 页码
 * @param {number} params.pageSize 每页记录数
 * @param {number} params.id 消息id
 * @param {number} params.readFlag 已读标记(0:未读,1:已读)
 * @returns
 */
export function notificationPage(params = {}, option = {}) {
	return request({
		url: `/bidmgt/opportunity/notificationPage`,
		method: 'POST',
		data: params,
		...option,
	});
}

/**
 * 更新消息已读
 * @param {object} params 通知查询条件
 * @param {number} params.pageNum 页码
 * @param {number} params.pageSize 每页记录数
 * @param {number} params.id 消息id
 * @param {number} params.readFlag 已读标记(0:未读,1:已读)
 * @returns
 */
export function updateReadFlag(params = {}, option = {}) {
	return request({
		url: `/bidmgt/opportunity/updateReadFlag`,
		method: 'POST',
		data: params,
		...option,
	});
}
/**
 * 我的统计
 * @param {object} params 通知查询条件
 * @returns
 */
export function myStatistics(params = {}, option = {}) {
	return request({
		url: `/bidmgt/opportunity/myStatistics`,
		method: 'POST',
		data: params,
		...option,
	});
}

/**
 * 商机 分页查询跟踪列表
 * @param {object} params 通知查询条件
 * @param {number} params.pageNum 页码
 * @param {number} params.pageSize 每页记录数
 * @param {number} params.id 消息id
 * @param {number} params.readFlag 已读标记(0:未读,1:已读)
 * @returns
 */
export function myProjectFollowPage(params = {}, option = {}) {
	return request({
		url: `/bidmgt/opportunity/myProjectFollowPage`,
		method: 'POST',
		data: params,
		...option,
	});
}
