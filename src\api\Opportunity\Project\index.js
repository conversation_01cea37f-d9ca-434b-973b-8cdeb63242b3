/**
 * @description index - Project
 * <AUTHOR>
 *
 * Created on 2025-07-21 下午 2:46
 */
import { request } from '@/utils/axios';

/**
 * @OpenService("wpm.project.pageProject")
 * @Operation(summary = "项目分页")
 * @PostMapping("/pageProject")
 */
export function pageProject(params = {}, option = {}) {
	return request({
		url: `/wpm/project/pageProject`,
		method: 'POST',
		data: params,
		...option,
	});
}

/**
 * 导入项目Excel文件
 * @OpenService(value = "wpm.project.importProjectData")
 * @PostMapping("/importProjectData")
 * @Operation(summary  = "importProjectData")
 * */
export function importProjectData(params = {}, option = {}) {
	return request({
		url: `/wpm/project/importProjectData`,
		method: 'POST',
		data: params,
		header: {
			'Content-Type': 'multipart/form-data',
		},
		...option,
	});
}

/**
 * @description 添加项目类型
 * @OpenService("wpm.projectType.addProjectType")
 * @Operation(summary = "项目类型添加")
 * @PostMapping("/addProjectType")
 */
export function addProjectType(params = {}, option = {}) {
	return request({
		url: `/wpm/projectType/addProjectType`,
		method: 'POST',
		data: params,
		...option,
	});
}

/**
 * @description 修改项目类型
 * @OpenService("wpm.projectType.updateProjectType")
 * @Operation(summary = "项目类型更新")
 * @PostMapping("/updateProjectType")
 */
export function updateProjectType(params = {}, option = {}) {
	return request({
		url: `/wpm/projectType/updateProjectType`,
		method: 'POST',
		data: params,
		...option,
	});
}

/**
 * @description 删除项目类型
 * @OpenService("wpm.projectType.deleteProjectType")
 * @Operation(summary = "项目类型删除")
 * @PostMapping("/deleteProjectType")
 */
export function deleteProjectType(params = {}, option = {}) {
	return request({
		url: `/wpm/projectType/deleteProjectType`,
		method: 'POST',
		data: params,
		...option,
	});
}

/**
 * @description 项目类型列表
 * @OpenService("wpm.projectType.listProjectType")
 * @Operation(summary = "项目类型列表")
 * @PostMapping("/listProjectType")
 */
export function listProjectType(params = {}, option = {}) {
	return request({
		url: `/wpm/projectType/listProjectType`,
		method: 'POST',
		data: params,
		...option,
	});
}

/**
 * /**
 * 获取项目下拉选项数据
 * @OpenService("wpm.project.getProjectComboboxData")
 * @return
 * @param params
 * @param option
 */
export function getProjectComboboxData(params = {}, option = {}) {
	return request({
		url: `/wpm/project/getProjectComboboxData`,
		method: 'POST',
		data: params,
		...option,
	})
}
