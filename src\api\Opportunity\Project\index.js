/**
 * @description index - Project
 * <AUTHOR>
 *
 * Created on 2025-07-21 下午 2:46
 */
import {request} from '@/utils/axios';

/**
 * @OpenService("wpm.project.pageProject")
 * @Operation(summary = "项目分页")
 * @PostMapping("/pageProject")
 */
export function pageProject(params = {}, option = {}) {
    return request({
        url: `/wpm/project/pageProject`,
        method: 'POST',
        data: params,
        ...option
    });
}

/**
 * 导入项目Excel文件
 * @OpenService(value = "wpm.project.importProjectData")
 * @PostMapping("/importProjectData")
 * @Operation(summary  = "importProjectData")
 * */
export function importProjectData(params = {}, option = {}) {
    return request({
        url: `/wpm/project/importProjectData`,
        method: 'POST',
        data: params,
        responseType: "blob",
        ...option
    });
}
