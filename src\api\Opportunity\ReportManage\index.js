/**
 * @description index - 文件描述
 * <AUTHOR>
 *
 * Created on 2025-08-04 下午 5:00
 */
import { request } from '@/utils/axios';

/**
 * 添加汇报
 * @description
 * api wpm.workReport.addWorkReport.1.0
 * @param {object} params 工作汇报
 * @param {number} params.id 主键
 * @param {string} params.reportDate 汇报日期：yyyy-MM-dd
 * @param {string} params.reportEndDate 汇报结束日期（周报的时候需要传）：yyyy-MM-dd
 * @param {number} params.reportType 汇报类型：1 日报 2周报
 * @param {string} params.reportYearMonth 汇报所属年月：yyyy-MM( 格式如：2025-07)
 * @param {string} params.currentContent 当日/当周工作内容
 * @param {string} params.futureContent 明天/下周工作计划
 * @returns
 */
export function addWorkReport(params = {}) {
	return request({
		url: '/wpm/workReport/addWorkReport',
		method: 'POST',
		data: params,
		header: {},
		showLoading: true,
		isWhiteList: false,
	});
}

/**
 * 批量删除汇报
 * @param {object} params 工作汇报
 * @param {number} params.id 主键
 * @param {array} params.ids
 * @param {string} params.reportYearMonth 汇报年月日期：yyyy-MM(查询月的汇报一定要按这格式如：2025-07)
 * @param {array} params.reportDateList 汇报日期：yyyy-MM-dd
 * @param {number} params.reportType 汇报类型：1 日报 2周报
 * @param {number} params.reportStatus 汇报状态：0未填报  1 已填写 2请假
 * @param {string} params.currentContent 当日/当周工作内容
 * @param {string} params.futureContent 明天/下周工作计划
 * @param {number} params.createBy 创建人id（员工id）
 * @param {number} params.roleType 角色类型：1 部门负责人 2 普通员工
 * @param {string} params.departmentName 部门名称
 * @param {string} params.employeeName 员工名称
 * @returns
 */
export function batchDelWorkReport(params) {
	return request({
		url: '/wpm/workReport/batchDelWorkReport',
		method: 'POST',
		data: params,
		header: {},
		showLoading: true,
		isWhiteList: false,
	});
}

/**
 * 部门汇报导出
 * @param {object} params 工作汇报
 * @param {number} params.id 主键
 * @param {array} params.ids
 * @param {string} params.reportYearMonth 汇报年月日期：yyyy-MM(查询月的汇报一定要按这格式如：2025-07)
 * @param {array} params.reportDateList 汇报日期：yyyy-MM-dd
 * @param {number} params.reportType 汇报类型：1 日报 2周报
 * @param {number} params.reportStatus 汇报状态：0未填报  1 已填写 2请假
 * @param {string} params.currentContent 当日/当周工作内容
 * @param {string} params.futureContent 明天/下周工作计划
 * @param {number} params.createBy 创建人id（员工id）
 * @param {number} params.roleType 角色类型：1 部门负责人 2 普通员工
 * @param {string} params.departmentName 部门名称
 * @param {string} params.employeeName 员工名称
 * @returns
 */
export function exportWorkReportToDepartment(params = {}) {
	return request({
		url: '/wpm/workReport/exportWorkReportToDepartment',
		method: 'POST',
		data: params,
		header: {},
		showLoading: true,
		isWhiteList: false,
	});
}

/**
 * 根据id查询工作汇报
 * @param {object} params 工作汇报
 * @param {number} params.id 主键
 * @param {array} params.ids
 * @param {string} params.reportYearMonth 汇报年月日期：yyyy-MM(查询月的汇报一定要按这格式如：2025-07)
 * @param {array} params.reportDateList 汇报日期：yyyy-MM-dd
 * @param {number} params.reportType 汇报类型：1 日报 2周报
 * @param {number} params.reportStatus 汇报状态：0未填报  1 已填写 2请假
 * @param {string} params.currentContent 当日/当周工作内容
 * @param {string} params.futureContent 明天/下周工作计划
 * @param {number} params.createBy 创建人id（员工id）
 * @param {number} params.roleType 角色类型：1 部门负责人 2 普通员工
 * @param {string} params.departmentName 部门名称
 * @param {string} params.employeeName 员工名称
 * @returns
 */
export function getWorkReportById(params) {
	return request({
		url: '/wpm/workReport/getWorkReportById',
		method: 'POST',
		data: params,
		header: {},
		showLoading: true,
		isWhiteList: false,
	});
}

/**
 * 查询我的汇报
 * @param {object} params 工作汇报
 * @param {number} params.id 主键
 * @param {array} params.ids
 * @param {string} params.reportYearMonth 汇报年月日期：yyyy-MM(查询月的汇报一定要按这格式如：2025-07)
 * @param {array} params.reportDateList 汇报日期：yyyy-MM-dd
 * @param {number} params.reportType 汇报类型：1 日报 2周报
 * @param {number} params.reportStatus 汇报状态：0未填报  1 已填写 2请假
 * @param {string} params.currentContent 当日/当周工作内容
 * @param {string} params.futureContent 明天/下周工作计划
 * @param {number} params.createBy 创建人id（员工id）
 * @param {number} params.roleType 角色类型：1 部门负责人 2 普通员工
 * @param {string} params.departmentName 部门名称
 * @param {string} params.employeeName 员工名称
 * @returns
 */
export function queryMyWorkReport(params) {
	return request({
		url: '/wpm/workReport/queryMyWorkReport',
		method: 'POST',
		data: params,
		header: {},
		showLoading: true,
		isWhiteList: false,
	});
}

/**
 * 查询部门汇报
 * @param {object} params 工作汇报
 * @param {number} params.id 主键
 * @param {array} params.ids
 * @param {string} params.reportYearMonth 汇报年月日期：yyyy-MM(查询月的汇报一定要按这格式如：2025-07)
 * @param {array} params.reportDateList 汇报日期：yyyy-MM-dd
 * @param {number} params.reportType 汇报类型：1 日报 2周报
 * @param {number} params.reportStatus 汇报状态：0未填报  1 已填写 2请假
 * @param {string} params.currentContent 当日/当周工作内容
 * @param {string} params.futureContent 明天/下周工作计划
 * @param {number} params.createBy 创建人id（员工id）
 * @param {number} params.roleType 角色类型：1 部门负责人 2 普通员工
 * @param {string} params.departmentName 部门名称
 * @param {string} params.employeeName 员工名称
 * @returns
 */
export function queryWorkReportToDepartment(params) {
	return request({
		url: '/wpm/workReport/queryWorkReportToDepartment',
		method: 'POST',
		data: params,
		header: {},
		showLoading: true,
		isWhiteList: false,
	});
}

/**
 * 修改汇报
 * @param {object} params 工作汇报
 * @param {number} params.id 主键
 * @param {string} params.reportDate 汇报日期：yyyy-MM-dd
 * @param {string} params.reportEndDate 汇报结束日期（周报的时候需要传）：yyyy-MM-dd
 * @param {number} params.reportType 汇报类型：1 日报 2周报
 * @param {string} params.reportYearMonth 汇报所属年月：yyyy-MM( 格式如：2025-07)
 * @param {string} params.currentContent 当日/当周工作内容
 * @param {string} params.futureContent 明天/下周工作计划
 * @returns
 */
export function updateWorkReport(params) {
	return request({
		url: '/wpm/workReport/updateWorkReport',
		method: 'POST',
		data: params,
		header: {},
		showLoading: true,
		isWhiteList: false,
	});
}

/*-----------------周配置---------------*/
/**
 * 添加周配置
 * @param {object} params 日期周配置
 * @param {number} params.id 主键
 * @param {string} params.belongYearMonth 所属年月:yyyy-MM
 * @param {number} params.rankingNum 排序序号
 * @param {string} params.startDate 开始日期:yyyy-MM-dd
 * @param {string} params.endDate 结束日期:yyyy-MM-dd
 * @returns
 */
export function addWeekConfig(params) {
	return request({
		url: '/wpm/weekConfig/addWeekConfig',
		method: 'POST',
		data: params,
		header: {},
		showLoading: true,
		isWhiteList: false,
	});
}

/**
 * 批量删除
 * @param {object} params 日期周配置
 * @param {string} params.belongYearMonth 所属年月:yyyy-MM
 * @param {number} params.id
 * @param {array} params.ids
 * @returns
 */
export function batchDel(params) {
	return request({
		url: '/wpm/weekConfig/batchDel',
		method: 'POST',
		data: params,
		header: {},
		showLoading: true,
		isWhiteList: false,
	});
}

/**
 * 详情查询
 * @param {object} params 日期周配置
 * @param {string} params.belongYearMonth 所属年月:yyyy-MM
 * @param {number} params.id
 * @param {array} params.ids
 * @returns
 */
export function getWorkReport(params) {
	return request({
		url: '/wpm/weekConfig/getWorkReport',
		method: 'POST',
		data: params,
		header: {},
		showLoading: true,
		isWhiteList: false,
	});
}

/**
 * 获取周列表
 * @param {object} params 日期周配置
 * @param {string} params.belongYearMonth 所属年月:yyyy-MM
 * @param {number} params.id
 * @param {array} params.ids
 * @returns
 */
export function listWorkReport(params) {
	return request({
		url: '/wpm/weekConfig/listWorkReport',
		method: 'POST',
		data: params,
		header: {},
		showLoading: true,
		isWhiteList: false,
	});
}

/**
 * 修改周配置
 * @param {object} params 日期周配置
 * @param {number} params.id 主键
 * @param {string} params.belongYearMonth 所属年月:yyyy-MM
 * @param {number} params.rankingNum 排序序号
 * @param {string} params.startDate 开始日期:yyyy-MM-dd
 * @param {string} params.endDate 结束日期:yyyy-MM-dd
 * @returns
 */
export function updateWeekConfig(params) {
	return request({
		url: '/wpm/weekConfig/updateWorkReport',
		method: 'POST',
		data: params,
		header: {},
		showLoading: true,
		isWhiteList: false,
	});
}

/**
 * 工时审批列表
 * @param {object} params 客户（基本信息）
 * @param {number} params.id 主键
 * @param {number} params.approvalUserId '审批人id'
 * @param {number} params.departmentId 部门id
 * @param {number} params.projectId 项目id
 * @param {number} params.type 审批类型(1:部门工时,2:项目工时)
 * @param {number} params.status 状态(0:待审批,1:已审批2:审批拒绝)
 * @param {string} params.approvalRemark 审批备注
 * @param {string} params.currentMonth 当前月份
 * @returns
 */
export function getTaskTimeApprovalList(params) {
	return request({
		url: '/wpm/taskTimeApproval/getTaskTimeApprovalList',
		method: 'POST',
		data: params,
		header: {},
		showLoading: true,
		isWhiteList: false,
	});
}
/**
 * 删除工时审批
 * @param {string} id
 * @returns
 */
export function deleteTaskTimeApproval({ id }) {
	return request({
		url: `/wpm/taskTimeApproval/deleteTaskTimeApproval?id=${id}`,
		method: 'POST',
		header: {},
		showLoading: true,
		isWhiteList: false,
	});
}

/**
 * 工时审批详情
 * @param {object} params 客户（基本信息）
 * @param {number} params.id 主键
 * @param {number} params.approvalUserId '审批人id'
 * @param {number} params.departmentId 部门id
 * @param {number} params.projectId 项目id
 * @param {number} params.type 审批类型(1:部门工时,2:项目工时)
 * @param {number} params.status 状态(0:待审批,1:已审批2:审批拒绝)
 * @param {string} params.approvalRemark 审批备注
 * @param {string} params.currentMonth 当前月份
 * @returns
 */
export function detailTaskTime(params) {
	return request({
		url: '/wpm/taskTimeApproval/detailTaskTime',
		method: 'POST',
		data: params,
		header: {},
		showLoading: true,
		isWhiteList: false,
	});
}
/**
 * 获取工时详情
 * @param {object} params 客户（基本信息）
 * @param {number} params.id 主键
 * @param {number} params.approvalUserId '审批人id'
 * @param {number} params.departmentId 部门id
 * @param {number} params.projectId 项目id
 * @param {number} params.type 审批类型(1:部门工时,2:项目工时)
 * @param {number} params.status 状态(0:待审批,1:已审批2:审批拒绝)
 * @param {string} params.approvalRemark 审批备注
 * @param {string} params.currentMonth 当前月份
 * @returns
 */
export function getTaskTimeApprovalById(params) {
	return request({
		url: '/wpm/taskTimeApproval/getTaskTimeApprovalById',
		method: 'POST',
		data: params,
		showLoading: true,
		isWhiteList: false,
	});
}

/**
 * 修改工时审批
 * @param {object} params 客户（基本信息）
 * @param {number} params.id 主键
 * @param {number} params.approvalUserId '审批人id'
 * @param {number} params.departmentId 部门id
 * @param {number} params.projectId 项目id
 * @param {number} params.type 审批类型(1:部门工时,2:项目工时)
 * @param {number} params.status 状态(0:待审批,1:已审批2:审批拒绝)
 * @param {string} params.approvalRemark 审批备注
 * @param {string} params.currentMonth 当前月份
 * @returns
 */
export function updateTaskTime(params) {
	return request({
		url: '/wpm/taskTimeApproval/updateTaskTime',
		method: 'POST',
		data: params,
		showLoading: true,
		isWhiteList: false,
	});
}

/**
 *	@OpenService("wpm.workDate.queryDayStatus")
 *	@Operation(summary = "查询指定日期的性质")
 *	@PostMapping("/queryDayStatus")
 */
export function queryDayStatus(params) {
	return request({
		url: '/wpm/workDate/queryDayStatus',
		method: 'POST',
		data: params,
		showLoading: true,
		isWhiteList: false,
	})
}

/**
 *	@OpenService("wpm.workReport.workReportStatistics")
 *	@Operation(summary = "获取统计")
 *	@PostMapping("/workReportStatistics")
 */
export function workReportStatistics(params) {
	return request({
		url: '/wpm/workReport/workReportStatistics',
		method: 'POST',
		data: params,
		showLoading: true,
		isWhiteList: false,
	})
}
