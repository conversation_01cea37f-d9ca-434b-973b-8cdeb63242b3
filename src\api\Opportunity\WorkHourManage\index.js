/**
 * @description index - 文件描述
 * <AUTHOR>
 *
 * Created on 2025-07-11 下午 5:10
 */
import {request} from '@/utils/axios';

/**
 * 查询节假日
 * */
export async function queryHoliday(data = {}, options = {}) {
    const {year, region = 'CN',} = data;
    const response = await fetch(`https://unpkg.com/holiday-calendar@1.1.9/data/${region}/${year}.json`);
    return response.json();
}

/**
 * 查询工作日&非工作日
 * @param {object} params 工作日查询条件
 * @param {number} params.month 月份
 * @param {number} params.year 年
 * @returns
 */
export function queryWorkDate(params) {
    return request({
        url: '/wpm/workDate/queryWorkDate',
        method: 'POST',
        data: params,
        header: {},
        showLoading: true,
        isWhiteList: false
    });
}
