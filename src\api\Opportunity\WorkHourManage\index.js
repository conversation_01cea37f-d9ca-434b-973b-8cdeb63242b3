/**
 * @description index - 文件描述
 * <AUTHOR>
 *
 * Created on 2025-07-11 下午 5:10
 */
import { request } from '@/utils/axios';

/**
 * 查询节假日
 * */
export async function queryHoliday(data = {}, options = {}) {
	const { year, region = 'CN' } = data;
	const response = await fetch(`https://unpkg.com/holiday-calendar@1.1.9/data/${region}/${year}.json`);
	return response.json();
}

/**
 * 查询工作日&非工作日
 * @param {object} params 工作日查询条件
 * @param {number} params.month 月份
 * @param {number} params.year 年
 * @returns
 */
export function queryWorkDate(params) {
	return request({
		url: '/wpm/workDate/queryWorkDate',
		method: 'POST',
		data: params,
		header: {},
		showLoading: true,
		isWhiteList: false,
	});
}

/**
 * 获取工时列表
 * @OpenService("wpm.taskTime.getTaskTimeSummaryList")
 * @Operation(summary = "工时汇总列表")
 * @PostMapping("/getTaskTimeSummaryList")
 */
export function getTaskTimeSummaryList(params) {
	return request({
		url: '/wpm/taskTime/getTaskTimeSummaryList',
		method: 'POST',
		data: params,
		header: {},
		showLoading: true,
		isWhiteList: false,
	});
}

/**
 * 添加工时
 *  @OpenService("wpm.taskTime.addTaskTime")
 *  @Operation(summary = "添加工时")
 *  @PostMapping("/addTaskTime")
 */
export function addTaskTime(params) {
	return request({
		url: '/wpm/taskTime/addTaskTime',
		method: 'POST',
		data: params,
		header: {},
		showLoading: true,
		isWhiteList: false,
	});
}

/**
 * 批量添加工时
 * */
export function batchAddOrUpdateTaskTime(params) {
	return request({
		url: '/wpm/taskTime/batchAddOrUpdateTaskTime',
		method: 'POST',
		data: params,
		header: {},
		showLoading: true,
		isWhiteList: false,
	});
}

/**
 * 修改工时
 * @OpenService("wpm.taskTime.updateTaskTime")
 * @Operation(summary = "修改工时")
 * @PostMapping("/updateTaskTime")
 */
export function updateTaskTime(params) {
	return request({
		url: '/wpm/taskTime/updateTaskTime',
		method: 'POST',
		data: params,
		header: {},
		showLoading: true,
		isWhiteList: false,
	});
}

/**
 * 删除工时
 * @OpenService("wpm.taskTime.deleteTaskTime")
 * @Operation(summary = "删除工时")
 * @PostMapping("/deleteCustomer")
 */
export function deleteTaskTime(params) {
	return request({
		url: '/wpm/taskTime/deleteTaskTime',
		method: 'POST',
		data: params,
		header: {},
		showLoading: true,
		isWhiteList: false,
	});
}

/**
 * 工时详情
 * @OpenService("wpm.taskTime.detailCustomer")
 * @Operation(summary = "工时详情")
 * @PostMapping("/detailCustomer")
 */
export function detailTaskTime(params) {
	return request({
		url: '/wpm/taskTime/detailTaskTime',
		method: 'POST',
		data: params,
		header: {},
		showLoading: true,
		isWhiteList: false,
	});
}

/**
 * 获取工时详情
 * @OpenService("wpm.taskTime.detailTaskTimes")
 * @Operation(summary = "工时详情汇总")
 * @PostMapping("/detailTaskTimes")
 */
export function detailTaskTimes(params) {
	return request({
		url: '/wpm/taskTime/detailTaskTimes',
		method: 'post',
		data: params,
	});
}

/**
 * 工时报表列表
 * @param {object} params TaskTimeQueryDto
 * @param {number} params.id 主键id
 * @param {string} params.currentMonth 当前月份
 * @param {number} params.departmentId 部门Id
 * @param {string} params.departmentName 部门名称
 * @param {string} params.taskDate 工时月份（xxxx-xx）
 * @param {number} params.userId 员工id
 * @param {string} params.userName 员工姓名
 * @param {number} params.projectId 项目id
 * @param {string} params.projectName 项目名称
 * @param {number} params.approvalStatus 审批状态
 * @param {number} params.queryType 查询类型(1:按用户 2:按部门 3:按项目)
 * @returns
 */
export function getTaskTimeReportList(params) {
	return request({
		url: '/wpm/taskTime/getTaskTimeReportList',
		method: 'post',
		data: params,
	});
}

/**
 * 工时报表导出
 * @param {object} params TaskTimeQueryDto
 * @param {number} params.id 主键id
 * @param {string} params.currentMonth 当前月份
 * @param {number} params.departmentId 部门Id
 * @param {string} params.departmentName 部门名称
 * @param {string} params.taskDate 工时月份（xxxx-xx）
 * @param {number} params.userId 员工id
 * @param {string} params.userName 员工姓名
 * @param {number} params.projectId 项目id
 * @param {string} params.projectName 项目名称
 * @param {number} params.approvalStatus 审批状态
 * @param {number} params.queryType 查询类型(1:按用户 2:按部门 3:按项目)
 * @returns
 */
export function exportTaskTimeReportData(params) {
	return request({
		url: '/wpm/taskTime/exportTaskTimeReportData',
		method: 'post',
		data: params,
		responseType: 'blob',
	});
}

/**
 *导出汇总报表Excel文件
 * @OpenService(value = "wpm.taskTime.exportSummaryReportData")
 * Operation(summary  = "汇总报表导出")
 * @PostMapping("/exportSummaryReportData")
 */
export function exportSummaryReportData(params) {
	return request({
		url: '/wpm/taskTime/exportSummaryReportData',
		method: 'post',
		data: params,
		responseType: 'blob',
	});
}

/**
 * 更新工时填写时所选择的项目
 * @OpenService("wpm.projectSelection.updateProjectSelection")
 * @Operation(summary = "更新工时选择项目")
 * @PostMapping("/updateProjectSelection")
 * public ResponseResult<ProjectVo> updateProjectSelection(@RequestBody ProjectSelectionDto projectSelectionDto)
 * @return
 * @param params
 */
export function updateProjectSelection(params) {
	return request({
		url: '/wpm/projectSelection/updateProjectSelection',
		method: 'post',
		data: params
	})
}

/**
 * 查询保存的选择项目
 * @Schema(description = "用户id")
 * private Long userId;
 * @Schema(description = "当前月份")
 * private String currentMonth;
 * @OpenService("wpm.projectSelection.getProjectSelectionList")
 * @Operation(summary = "查询保存的选择项目")
 * */
export function projectSelection(params) {
	return request({
		url: '/wpm/projectSelection/getProjectSelectionList',
		method: 'post',
		data: params
	})
}

/**
 * 批量修改工时审批
 * @OpenService("wpm.taskTimeApproval.updateBatchTaskTimeApproval")
 * @Operation(summary = "批量修改工时审批")
 * @PostMapping("/updateBatchTaskTimeApproval")
 * public ResponseResult updateBatchTaskTimeApproval(@RequestBody List<TaskTimeApprovalDto> taskTimeApprovalDtos)
 * */
export function updateBatchTaskTimeApproval(params) {
	return request({
		url: '/wpm/taskTimeApproval/updateBatchTaskTimeApproval',
		method: 'post',
		data: params
	})
}
