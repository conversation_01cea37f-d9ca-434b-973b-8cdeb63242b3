import { request } from '@/utils/axios';

/**
 * 获取用户列表 分页
 * @param {*} data
 * @param {*} data.pageSize           页大小
 * @param {*} data.pageNum            页码
 * @param {*} data.appCode            终端类型
 * @param {*} data.keywords           用户名称
 * @param {*} data.status             用户状态
 * @param {*} data.deptId             部门ID
 * @param {*} data.roleId             角色ID
 * @returns
 */
export const getUserPageData = (data = {}) => {
	return request({
		url: '/system/user/page',
		method: 'post',
		data,
		showLoading: true,
		isWhiteList: false,
	});
};

/**
 * 新增用户
 * @param {*} data
 * @param {*} data.id	                用户ID
 * @param {*} data.userName	          用户姓名
 * @param {*} data.loginName	        登录帐号
 * @param {*} data.mobile	            手机号
 * @param {*} data.gender	            性别(1:男,2:女)
 * @param {*} data.email	            邮箱
 * @param {*} data.status	            状态(0:禁用,1:启用)
 * @param {*} data.userSource	        用户来源(1:系统用户,2:微信小程序用户,3:)
 * @param {*} data.loginType	        登录类型(1:帐号,2:手机号,3:邮箱)
 * @param {*} data.deptIds	          部门id列表
 * @param {*} data.roleIds	          角色id集合
 * @param {*} data.wxQrCodeUrl	      信二维码图片地址
 * @param {*} data.wxAvatarUrl	      头像地址
 * @returns
 */
export const userAdd = (data = {}) => {
	return request({
		url: '/system/user/create',
		method: 'post',
		data,
		showLoading: true,
		isWhiteList: false,
	});
};

/**
 * 更新用户
 * @param {*} data
 * @param {*} data.id	                用户ID
 * @param {*} data.userName	          用户姓名
 * @param {*} data.loginName	        登录帐号
 * @param {*} data.mobile	            手机号
 * @param {*} data.gender	            性别(1:男,2:女)
 * @param {*} data.email	            邮箱
 * @param {*} data.status	            状态(0:禁用,1:启用)
 * @param {*} data.userSource	        用户来源(1:系统用户,2:微信小程序用户,3:)
 * @param {*} data.loginType	        登录类型(1:帐号,2:手机号,3:邮箱)
 * @param {*} data.deptIds	          部门id列表
 * @param {*} data.roleIds	          角色id集合
 * @returns
 */
export const userUpdate = (data = {}) => {
	return request({
		url: '/system/user/update',
		method: 'post',
		data,
		showLoading: true,
		isWhiteList: false,
	});
};

/**
 * 更改用户状态
 * @param {*} data
 * @param {*} data.userId             用户id
 * @param {*} data.status             用户状态(1-正常；0-停用)
 * @returns
 */
export const changeUserStatus = (data = {}) => {
	return request({
		url: `/system/user/updateStatus`,
		params: data,
		method: 'post',
		showLoading: true,
		isWhiteList: false,
	});
};

/**
 * 批量更改用户状态
 * @param {*} data
 * @param {*} data.ids             用户id
 * @param {*} data.status             用户状态(1-正常；0-停用)
 * @returns
 */
export const batchUpdateStatus = (data = {}) => {
	return request({
		url: `/system/user/batchUpdateStatus`,
		data,
		method: 'post',
		showLoading: true,
		isWhiteList: false,
	});
};

/**
 * 删除用户
 * @param {*} data
 * @param {*} data.userId 用户id
 * @returns
 */
export const userDel = (data = {}) => {
	return request({
		url: `/system/user/batchDelete`,
		data,
		method: 'post',
		showLoading: true,
		isWhiteList: false,
	});
};

/**
 * 获取用户详情
 * @param {*} data
 * @param {*} data.userId 用户id
 * @returns
 */
export const userDetail = (data = {}) => {
	return request({
		url: `/system/user/detail`,
		params: data,
		method: 'post',
		showLoading: true,
		isWhiteList: false,
	});
};

/**
 * 管理方用户导出
 * @param {object} data 用户分页查询参数
 * @param {number} data.pageNum 页码
 * @param {number} data.pageSize 每页记录数
 * @param {string} data.keywords 关键字(用户名/昵称/手机号)
 * @param {number} data.status 用户状态
 * @param {number} data.deptId 部门ID
 * @param {number} data.roleId 角色id
 * @param {string} data.appCode 应用编码
 * @param {string} data.perms 权限字符
 * @returns
 */
export const managementUserExport = (data = {}) => {
	return request({
		url: `/system/user/managementUserExport`,
		data,
		method: 'post',
		responseType: 'blob',
		showLoading: true,
		isWhiteList: false,
	});
};

/**
 * 管理方用户导入
 * @param {object} data
 * @param {string} data.file
 * @returns
 */
export const managementUserImport = (data = {}) => {
	return request({
		url: `/system/user/managementUserImport`,
		method: 'post',
		data,
		header: {
			'content-type': 'multipart/form-data',
		},
		showLoading: true,
		isWhiteList: false,
	});
};

/**
 * 获取部门列表 分页
 * @param {*} data
 * @param {*} data.pageSize           页大小
 * @param {*} data.pageNum            页码
 * @param {*} data.name               部门名称
 * @param {*} data.status             状态(1->正常；0->禁用)
 * @returns
 */
export const getDeptPageData = (data = {}) => {
	return request({
		url: '/system/dept/page',
		method: 'post',
		data,
		showLoading: true,
		isWhiteList: false,
	});
};

/**
 * 新增部门
 * @param {*} data
 * @param {*} data.id                 部门ID
 * @param {*} data.name               部门编码
 * @param {*} data.parentId           父部门ID
 * @param {*} data.status             部门状态(1-正常；0-停用)
 * @param {*} data.description        描述
 * @returns
 */
export const deptAdd = (data = {}) => {
	return request({
		url: '/system/dept/add',
		method: 'post',
		data,
		showLoading: true,
		isWhiteList: false,
	});
};

/**
 * 更新部门
 * @param {*} data
 * @param {*} data.id                 部门ID
 * @param {*} data.name               部门编码
 * @param {*} data.deptName           部门名称
 * @param {*} data.status             部门状态(1-正常；0-停用)
 * @param {*} data.description        描述
 * @returns
 */
export const deptUpdate = (data = {}) => {
	return request({
		url: '/system/dept/update',
		method: 'post',
		data,
		showLoading: true,
		isWhiteList: false,
	});
};

/**
 * 删除部门
 * @param {*} data
 * @param {*} data.deptId 部门id
 * @returns
 */
export const deptDel = (data = {}) => {
	return request({
		url: `/system/dept/delete`,
		method: 'post',
		params: data,
		showLoading: true,
		isWhiteList: false,
	});
};

/**
 * 获取部门详情
 * @param {*} data
 * @param {*} data.deptId 部门id
 * @returns
 */
export const deptDetail = (data = {}) => {
	return request({
		url: `/system/dept/detail`,
		params: data,
		method: 'post',
		showLoading: true,
		isWhiteList: false,
	});
};

/**
 * 获取部门列表
 * @returns
 */
export const getDeptList = (data = {}, option = {}) => {
	return request({
		url: '/system/dept/list',
		method: 'post',
		data,
		refreshCache: ['/system/dept/add', '/system/dept/update', '/system/dept/updateStatus', '/system/dept/delete'],
		...option,
	});
};

/**
 * 获取角色列表 分页
 * @param {*} data
 * @param {*} data.pageSize           页大小
 * @param {*} data.pageNum            页码
 * @param {*} data.roleName           角色名称
 * @returns
 */
export const getRolePageData = (data = {}) => {
	return request({
		url: '/system/role/page',
		method: 'post',
		data,
		showLoading: true,
		isWhiteList: false,
	});
};

/**
 * 获取角色列表
 * @param {*} data
 * @returns
 */
export const getRoleList = (data = {}, option = {}) => {
	return request({
		url: '/system/role/list',
		method: 'post',
		data,
		refreshCache: ['/system/role/add', '/system/role/update', '/system/role/updateStatus', '/system/role/delete'],
		...option,
	});
};

/**
 * 新增角色
 * @param {*} data
 * @param {*} data.id                 角色ID
 * @param {*} data.roleCode           角色编码
 * @param {*} data.roleName           角色名称
 * @param {*} data.description        描述
 * @param {*} data.status             角色状态(1-正常；0-停用)
 * @param {*} data.permissionIds      权限id列表
 * @returns
 */
export const roleAdd = (data = {}) => {
	return request({
		url: '/system/role/add',
		method: 'post',
		data,
		showLoading: true,
		isWhiteList: false,
	});
};

/**
 * 更新角色
 * @param {*} data
 * @param {*} data.id                 角色ID
 * @param {*} data.roleCode           角色编码
 * @param {*} data.roleName           角色名称
 * @param {*} data.description        描述
 * @param {*} data.status             角色状态(1-正常；0-停用)
 * @param {*} data.permissionIds      权限id列表
 * @returns
 */
export const roleUpdate = (data = {}) => {
	return request({
		url: '/system/role/update',
		method: 'post',
		data,
		showLoading: true,
		isWhiteList: false,
	});
};

/**
 * 更改角色状态
 * @param {*} data
 * @param {*} data.roleId             角色id
 * @param {*} data.status             角色状态(1-正常；0-停用)
 * @returns
 */
export const changeRoleStatus = (data = {}) => {
	return request({
		url: `/system/role/updateStatus`,
		method: 'post',
		params: data,
		showLoading: true,
		isWhiteList: false,
	});
};

/**
 * 删除角色
 * @param {*} data
 * @param {*} data.roleId 角色id
 * @returns
 */
export const roleDel = (data = {}) => {
	return request({
		url: `/system/role/delete`,
		method: 'post',
		params: data,
		showLoading: true,
		isWhiteList: false,
	});
};

/**
 * 获取角色详情
 * @param {*} data
 * @param {*} data.roleId 角色id
 * @returns
 */
export const roleDetail = (data = {}) => {
	return request({
		url: '/system/role/detail',
		method: 'post',
		params: data,
		showLoading: true,
		isWhiteList: false,
	});
};

export const getLink = (data = {}) => {
	return request({
		url: '/weixin/wxa/generate_urllink?access_token=86_9G6peUyZtDHW14MGRFCoyCrnlCLlmJXNzuYvr4dwro4Eg1FjhW1LWqA89-VVYdTCSmxN3BlPq_NXfJB48UERFxeDiP7Yx4kN0EBMPGwKxpc2WOLcsNYFrOjMyTMDBNhAAANLX',
		method: 'post',
		data: {
			path: '/event/ActivityDetail/index',
			query: 'id=1859939752708255746',
		},
		showLoading: true,
		isWhiteList: false,
	});
};

/** 
 * 批量更新用户是否否内部人员(传 ids,insiderStatus)
 * @param {object} params 用户信息
 * @param {array} params.ids 用户IDs 
 * @param {string} params.thirdAppId 
 * @param {number} params.id 用户ID(新增不填)
 * @param {string} params.userName 用户姓名
 * @param {string} params.pwd 登录密码
 * @param {string} params.loginName 登录帐号(必传)
 * @param {string} params.mobile 手机号
 * @param {number} params.gender 性别(1:男,2:女)
 * @param {string} params.email 邮箱
 * @param {number} params.status 状态(0:禁用,1:启用)
 * @param {number} params.userSource 用户来源(1:后台管理添加的用户,2:微信小程序用户,3:PC端用户)
 * @param {number} params.loginType 登录类型(1:帐号,2:手机号,3:邮箱)
 * @param {string} params.wxQrCodeUrl 微信二维码图片地址
 * @param {string} params.wxAvatarUrl 头像地址
 * @param {string} params.appCode 应用编码
 * @param {string} params.identifyMark 身份唯一标识(微信unionId)
 * @param {array} params.deptIds 部门id列表
 * @param {array} params.roleIds 角色id集合
 * @param {array} params.permissionIds 权限id集合
 * @param {string} params.companyName 所在企业名称
 * @param {string} params.positionName 所在企业岗位
 * @param {number} params.userType 用户类型：1.前台C端用户 2 后台管理用户 3 超级管理后台用户
 * @param {number} params.insiderStatus 是否内部人员状态：0否 1是
 * @returns
 */
export function batchUpdateIsInternalStatus(data={}) {
	return request({
		url: '/system/user/batchUpdateIsInternalStatus',
		method: 'post',
		data,
		showLoading: true,
		isWhiteList: false,
	});
  }
