import { request } from '@/utils/axios';

/*
 * @description: 上传文件
 * @param {*} headers: appCode
 * @return {*}
 */
export function fileUpload(data, options = {}) {
	return request({
		url: `/system/file/upload`,
		method: 'POST',
		data,
		header: {
			'content-type': 'multipart/form-data',
		},
		showLoading: false,
		isWhiteList: true,
		...options,
	});
}

/*
 * @description: 获取省市区三级全部数据
 * @param {object} config
 * @param {number} config.level 级别 1国 2省 3市 4区
 * @return {*}
 */
export function getThreeLevelData(config = { level: 3, defaultArea: {} }, option) {
	return request({
		url: `/system/region/getThreeLevelData`,
		method: 'POST',
		data: {},
		...option,
	}).then((res) => {
		const keyName = ['provinceCode', 'cityCode', 'areaCode'];
		function _deepData(list, index = 0) {
			const defaultValue = (config.defaultArea && keyName[index] && config.defaultArea[keyName[index]]) || '';
			return (list || [])
				.map(({ children, ...args }) => {
					return {
						...args,
						label: args.name,
						value: args.code,
						children: args.level <= config.level && children && children.length > 0 ? _deepData(children, index + 1) : [],
					};
				})
				.sort((a, b) => {
					if (defaultValue && (a.value === defaultValue || b.value === defaultValue)) {
						return a.value === defaultValue ? -1 : b.value === defaultValue ? 1 : 0;
					} else {
						return 0;
					}
				});
		}
		return {
			data: _deepData(res.data),
		};
	});
}

export function getTwoLevelData(config = { level: 2, defaultArea: {} }, option) {
	return request({
		url: `/system/region/getTwoLevelData`,
		method: 'POST',
		data: {},
		...option,
	}).then((res) => {
		const keyName = ['provinceCode', 'cityCode', 'areaCode'];
		function _deepData(list, index = 0) {
			const defaultValue = (config.defaultArea && keyName[index] && config.defaultArea[keyName[index]]) || '';
			return (list || [])
				.map(({ children, ...args }) => {
					return {
						...args,
						label: args.name,
						value: args.code,
						children: args.level <= config.level && children && children.length > 0 ? _deepData(children, index + 1) : [],
					};
				})
				.sort((a, b) => {
					if (defaultValue && (a.value === defaultValue || b.value === defaultValue)) {
						return a.value === defaultValue ? -1 : b.value === defaultValue ? 1 : 0;
					} else {
						return 0;
					}
				});
		}
		return {
			data: _deepData(res.data),
		};
	});
}

/**
 *  根据地区编号获取数据
 * @param {object} data 地区查询dto
 * @param {number} data.pageNum 页码
 * @param {number} data.pageSize 每页记录数
 * @param {string} data.code 编码
 * @returns
 */
export function getSmpRegionByCode(data) {
	return request({
		url: `/system/region/getSmpRegionByCode`,
		method: 'POST',
		data,
		showLoading: true,
		isWhiteList: false,
	});
}

/**
 *  根据权限字符查询对应的用户
 * @param {object} data
 * @param {number} data.deptIds
 * @param {number} data.roleId
 * @param {string} data.appCode
 * @returns
 */
export function getByPermissionPerms(data = {}, options = {}) {
	return request({
		url: `/system/user/getByPermissionPerms`,
		method: 'POST',
		data,
		...options,
	});
}

/**
 *  组织架构 部门-用户
 * @param {object} data
 * @returns
 */
export function deptUserTreeList(data = {}, options = {}) {
	return request({
		url: `/system/dept/deptUserTreeList`,
		method: 'POST',
		data,
		...options,
	});
}

/**
 * 获取STSToken
 * @param {object} data
 * @param {number} data.expires
 * @returns
 */
export function getStsToken(data = {}, options = {}) {
	return request({
		url: '/system/file/getStsToken',
		method: 'post',
		data: data,
		...options,
	});
}

/**
 * 获取小程序码
 * @param {object} data 小程序码dto
 * @param {string} data.appId 应用ID
 * @param {string} data.pathName 小程序码路径
 * @param {string} data.params 小程序码参数
 * @returns
 */
export function getMiniProgramCode(data = {}, options = {}) {
	return request({
		url: '/system/weixin/getMiniProgramCode',
		// url: '/achv/front/info/getMiniProgramCode',
		method: 'post',
		data: data,
		...options,
	});
}

/** 
 * 租户应用信息查询
 * @returns
 */
export function tenantAppInfo(data = {}, options = {}) {
	return request({
		url: '/system/tenant/tenantAppInfo', 
		method: 'post',
		data: data,
		...options,
	});
	
  }

