import { request } from '@/utils/axios';

// 机构导入
export function achvAgencyImport(data) {
	return request({
		url: '/achv/import/agencyImport',
		method: 'post',
		data: data,
		header: {
			'content-type': 'multipart/form-data',
		},
	});
}

// 成果导入
export function achvImport(data) {
	return request({
		url: '/achv/import/achvImport',
		method: 'post',
		data: data,
		header: {
			'content-type': 'multipart/form-data',
		},
	});
}

// 需求导入
export function demandImport(data) {
	return request({
		url: '/achv/import/demandImport',
		method: 'post',
		data: data,
		header: {
			'content-type': 'multipart/form-data',
		},
	});
}
