import { request } from '@/utils/axios';

/**
 * 登录
 * @param {object} params 登录参数
 * @param {string} params.loginType 密码登录方式，用户名或邮箱或手机等
 * @param {string} params.username 用户名/邮箱/手机
 * @param {string} params.password 密码
 * @param {string} params.verifyCode 验证码
 * @returns
 */
export function login({ loginType = '', username = '', password = '', verifyCode = '' }) {
	return request({
		url: `/auth/user/adminLogin`,
		method: 'POST',
		data: {
			loginType,
			username,
			password,
			verifyCode,
		},
		header: {},
		showLoading: true,
		isWhiteList: false,
	});
}

// 租户登录
export function adminSelectTenant(data) {
	return request({
		url: '/auth/user/adminSelectTenant',
		method: 'post',
		data,
	});
}

/**
 * 手机短信验证码登录
 * @param {object} params MobileSmsLoginDto
 * @param {string} params.mobile 手机号码
 * @param {string} params.smsCode 手机短信验证码
 * @returns
 */
export function smsLogin({ mobile = '', smsCode = '' }) {
	return request({
		url: `/auth/user/mobile/sms/login`,
		method: 'POST',
		data: {
			mobile,
			smsCode,
		},
		header: {},
		showLoading: true,
		isWhiteList: false,
	});
}
/**
 * 获取验证码
 * @param {object} params 获取验证码参数
 * @param {string} params.clientMark 用户端标识（后端可根据会话生成）
 * @param {string} params.verifyCodeType 验证码类型 VERIFY_CODE_TYPE_IMG:图片验证码 VERIFY_CODE_TYPE_SMS:手机短信验证码 VERIFY_CODE_TYPE_EMAIL:邮箱验证码
 * @param {string} params.sceneType 验证码使用场 ,SCENE_TYPE_LOGIN:登录 SCENE_TYPE_REGISTER:注册 根据不同场景采用不同验证码生成策略
 * @param {string} params.mobile 手机号，验证码类型为手机短信验证码
 * @param {string} params.email 邮箱，验证码类型为邮件验证码
 * @returns
 */
export function verifyCodeGet({ clientMark = '', verifyCodeType = '', sceneType = '', mobile = '', email = '' }) {
	return request({
		url: `/auth/user/verify/code/get`,
		method: 'POST',
		data: {
			clientMark,
			verifyCodeType,
			sceneType,
			mobile,
			email,
		},
		header: {},
		showLoading: true,
		isWhiteList: false,
	});
}

/*
 * @description: 获取用户信息
 * @param {*}
 * @param {*}
 * @return {*}
 */
export function userInfo() {
	return request({
		url: `/system/user/userInfo`,
		method: 'POST',
		data: {},
		header: {},
		showLoading: true,
		isWhiteList: false,
	});
}

/**
 * 修改密码
 * @param {string} userId  手机号
 * @param {string} password   用户id
 * @returns
 */
export function updatePassword(data = {}) {
	return request({
		url: `/system/user/updatePassword`,
		method: 'POST',
		params: data,
		header: {},
		showLoading: true,
		isWhiteList: false,
	});
}

/**
 * 找回密码
 * @param {string} phone  手机号
 * @param {string} pwd   密码
 * @param {string} verificationCode  短信验证码
 * @returns
 */
export function findPassword({ phone = '', pwd = '', verificationCode = '' }) {
	return request({
		url: `/auth/user/findPassword`,
		method: 'POST',
		data: {
			phone,
			pwd,
			verificationCode,
		},
		header: {},
		showLoading: true,
		isWhiteList: false,
	});
}

/**
 * 修改用户基本信息
 * @param {object} params 修改用户基本信息实体
 * @param {string} params.userName 用户名
 * @param {string} params.email 邮箱
 * @param {string} params.companyName 所在企业名称
 * @param {string} params.positionName 所在企业岗位
 * @param {string} params.wxAvatarUrl 头像地址
 * @returns
 */
export function updateBasicInfo(data = {}) {
	return request({
		url: `/system/user/updateBasicInfo`,
		method: 'POST',
		data,
		header: {},
		showLoading: true,
		isWhiteList: false,
	});
}

/**
 * 获取用户基本信息
 * @returns
 */
export function getUserBasicInfo(header = {}) {
	return request({
		url: `/system/user/getUserBasicInfo`,
		method: 'POST',
		data: {},
		header: { ...header },
		showLoading: true,
		isWhiteList: false,
	});
}

/* 企业微信登录数据接口 */

/**
 * 登录state获取
 * */
export function getLoginState() {
	return request({
		url: `/auth/user/oauth2/state/get`,
		method: 'get',
		data: {},
		header: {},
		showLoading: true,
		isWhiteList: false,
	});
}
/**
 * 登录回调接口
 * */
export function oauthCallback(data = {}) {
	return request({
		url: `/auth/user/oauth2/ww/login`,
		method: 'get',
		params: data,
		header: {},
		showLoading: true,
		isWhiteList: false,
	});
}
/**
 * 登录绑定接口
 * */
export function oauthBind(data = {}) {
	return request({
		url: `/auth/user/oauth2/ww/bind`,
		method: 'POST',
		data,
		header: {},
		showLoading: true,
		isWhiteList: false,
	});
}

/**
 * 后台找回密码
 * @param {object} params 修改密码dto
 * @param {string} params.phone 手机号
 * @param {string} params.pwd 密码
 * @param {string} params.verificationCode 短信验证码
 * @param {number} params.tenantId
 * @returns
 */
export function findPasswordByBackground(data = {}) {
	return request({
		url: `/auth/user/findPasswordByBackground`,
		method: 'POST',
		data,
		header: {},
		showLoading: true,
		isWhiteList: false,
	});
}

/**
 * 判断用户手机号码是否存在
 * @param {string} phone
 * @param {string} tenantId
 * @returns
 */
export function isExistMobileByBackground(data = {}) {
	return request({
		url: `/system/user/isExistMobileByBackground?phone=${data.phone}`,
		method: 'POST',
		data,
		header: {},
		showLoading: true,
		isWhiteList: false,
	});
}
