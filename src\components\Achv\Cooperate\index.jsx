import { useState, useEffect } from 'react';
import { Table, But<PERSON>, Popconfirm, Space, message } from 'antd';

import { useRouterLink } from '@/hook/useRouter';
import { useTableData } from '@/hook/useTableData';

import { pageCooperate as getTablePageData, delCooperate as delTableItemData, detailCooperate as getDetailData } from '@/api/Achv/Cooperate/index';

/**
 * 合作意向表格
 * @param {*} props
 * @returns { number } props.cooperateType 合作类型：1 成果合作需求 2 需求合作意向 3机构合作详情 4 专家合作详情
 * @returns { string } props.cooperateProjectId 合作id
 */
const TableData = (props = {}) => {
	const { linkTo } = useRouterLink();

	const { dataSource, pagination, changePage, delTableData } = useTableData({
		params: {
			cooperateType: props.cooperateType,
			cooperateProjectId: props.cooperateProjectId,
		},
		getTablePageData,
		delTableItemData,
		closeFilter: true,
		disabledReplace: true,
	});

	// 跳转详情
	const toDetail = (id) => {
		linkTo(`${props.linkToPath}/cooperate?id=${id}&fromList=1`);
	};

	return (
		<Table rowKey="id" dataSource={dataSource} pagination={pagination} onChange={changePage} scroll={{ x: 'max-content' }}>
			<Table.Column
				title="序号"
				key="index"
				width={60}
				render={(_, __, index) => {
					return index + 1;
				}}
			/>
			<Table.Column title="姓名" dataIndex="contacts" />
			<Table.Column title="联系方式" dataIndex="contactsPhone" />
			<Table.Column title="所在单位名" dataIndex="name" />
			<Table.Column title="所在单位岗位" dataIndex="position" />
			<Table.Column title="合作方式" dataIndex="transformListName" />
			<Table.Column title="需求描述" dataIndex="content" />
			<Table.Column
				title="提交时间"
				dataIndex="createTime"
				render={(text) => {
					return (text || '--').slice(0, 16);
				}}
			/>
			<Table.Column
				title="操作"
				key="option"
				dataIndex="option"
				align="center"
				fixed="right"
				render={(_, record) => {
					return (
						<>
							<Button type="link" size="small" onClick={() => toDetail(record.id)}>
								详情
							</Button>
							<Popconfirm
								title="提示"
								description="确定删除吗？"
								onConfirm={() => {
									delTableData(record.id);
								}}
								okText="确定"
								cancelText="取消"
							>
								<Button type="link" size="small">
									删除
								</Button>
							</Popconfirm>
						</>
					);
				}}
			/>
		</Table>
	);
};

// 合作意向详情
const Detail = () => {
	const { linkTo, searchParams } = useRouterLink();
	const id = searchParams.get('id');
	const fromList = !!searchParams.get('fromList');

	const [detailData, setDetailData] = useState({});
	const [path, setPath] = useState('');

	useEffect(() => {
		// 获取列表数据
		getDetailData({ id }).then((res) => {
			if (!res.data) {
				message.error('数据不存在或已被删除');
				setTimeout(() => {
					linkTo(-1);
				}, 1000);
				return;
			}
			setDetailData(res.data);

			const path = ['', '/newAchv/achievement/manage', '/newAchv/demand/demandManage', ''][res.data.cooperateType || 0];
			setPath(path);
		});
	}, []);
	return (
		<div>
			<div className="flex flex-shrink align-center margin-bottom-4 height-48 font-size-22 font-weight-500 color-1d2129">
				<Space size={10}>
					<div className="a color-86909c" onClick={() => linkTo(`${path}`)}>
						{['', '成果管理', '需求管理', '专家管理'][detailData.cooperateType || 0]}
					</div>
					<div className="margin-lr-10 color-86909c">/</div>
					<div className="a color-86909c" onClick={() => linkTo(fromList ? -1 : `${path}/detail?id=${detailData.cooperateProjectId}`)}>
						{['', '成果详情', '需求详情', '专家详情'][detailData.cooperateType || 0]}
					</div>
					<div className="margin-lr-10 color-86909c">/</div>
					<div className="color-1d2129">外部合作意向详情</div>
				</Space>
			</div>

			{/* 详情 开始 */}
			<div className="margin-top-20 padding-top-20 padding-lr-20 padding-bottom-30 border-radius-8 bg-color-ffffff">
				<div className="flex align-center justify-between">
					<div className="line-height-26 font-size-18 font-weight-500 color-1d2129">外部合作意向</div>
				</div>
				<BaseData detailData={detailData} fromList={fromList} />
			</div>
			{/* 详情 结束 */}
		</div>
	);
};

// 基础数据
export const BaseData = (props = {}) => {
	const { linkTo } = useRouterLink();
	const detailData = props.detailData;
	const [path, setPath] = useState('');

	useEffect(() => {
		const path = ['', '/newAchv/achievement/manage', '/newAchv/demand/demandManage', ''][detailData.cooperateType || 0];
		setPath(path);
	}, [detailData.cooperateType]);
	return (
		<>
			<div className="flex margin-top-20">
				<div className="margin-right-4 line-height-22 color-86909c">
					{['', '成果名称', '需求名称', '专家名称'][detailData.cooperateType || 0]}：
				</div>
				<div className="flex-sub flex line-height-22 ">
					<div
						className="a font-weight-500 color-165dff"
						onClick={() => linkTo(props.fromList ? -1 : `${path}/detail?id=${detailData.cooperateProjectId}`)}
					>
						{detailData.cooperateProjectName}
					</div>
				</div>
			</div>
			<div className="flex margin-top-20">
				<div className="margin-right-4 line-height-22 color-86909c">单位名称：</div>
				<div className="flex-sub line-height-22 color-1d2129">{detailData.name || '--'}</div>
			</div>
			<div className="flex margin-top-20">
				<div className="margin-right-4 line-height-22 color-86909c">姓名：</div>
				<div className="flex-sub line-height-22 color-1d2129">{detailData.contacts || '--'}</div>
			</div>
			<div className="flex margin-top-20">
				<div className="margin-right-4 line-height-22 color-86909c">职位：</div>
				<div className="flex-sub line-height-22 color-1d2129">{detailData.position || '--'}</div>
			</div>
			<div className="flex margin-top-20">
				<div className="margin-right-4 line-height-22 color-86909c">联系方式：</div>
				<div className="flex-sub line-height-22 color-1d2129">{detailData.contactsPhone || '--'}</div>
			</div>
			<div className="flex margin-top-20">
				<div className="margin-right-4 line-height-22 color-86909c">意向合作方式：</div>
				<div className="flex-sub line-height-22 color-1d2129">
					{(detailData.transformListName && detailData.transformListName.join('，')) || '--'}
				</div>
			</div>
			<div className="flex margin-top-20">
				<div className="margin-right-4 line-height-22 color-86909c">需求描述：</div>
				<div className="flex-sub line-height-22 color-1d2129">{detailData.content || '--'}</div>
			</div>
			<div className="flex margin-top-20">
				<div className="margin-right-4 line-height-22 color-86909c">提交时间：</div>
				<div className="flex-sub line-height-22 color-1d2129">{detailData.createTime || '--'}</div>
			</div>
		</>
	);
};

const Index = () => {
	return <></>;
};

Index.Table = TableData;
Index.Detail = Detail;

export default Index;
