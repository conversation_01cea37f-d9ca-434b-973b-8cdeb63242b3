import { useEffect, useState, useRef, forwardRef, useImperativeHandle } from 'react';
import { useRouterLink } from '@/hook/useRouter';

import { Button, Form, Input, Space, message, Modal, Radio, Select } from 'antd';
import { EditOutlined } from '@ant-design/icons';
import ModalForm from '@/components/ModalForm';
import FormComp from '@/components/FormComp';

import { getSupplier, updateStageStatus } from '@/api/Achv/Demand/DockingManage/index';
import { auditSendOrder } from '@/api/Achv/SendOrder';

import { updateTaskCount } from '@/utils/achv';
import { dockingStageStatusData, dockingStageStatusDataTextList } from '@/pages/Achv/config';

import { brokerCategoryList } from '@/api/Achv/Broker/index';
import { listSignUp } from '@/api/Achv/Competition/Signup/index';

const Detail = (props = {}) => {
	const ModalFormRef = useRef();
	const { linkTo, searchParams } = useRouterLink();
	const id = searchParams.get('id');
	const fromDetail = !!searchParams.get('fromDetail');

	const [detail, setDetail] = useState({});
	const [stageStatus, setStageStatus] = useState('');

	// 改变状态 1 待三方会议 2 已三方会议 3 已签约 4 已终止谈判
	const changeStatus = (stageStatus) => {
		setStageStatus(stageStatus);
		ModalFormRef.current.setOpen(true);
		ModalFormRef.current.setTitle(['', '', '第一次会议信息', '确认签约', '终止原因', '第二次会议信息', '第三次会议信息'][stageStatus]);
	};

	// 更新状态

	const updateStatus = (values) => {
		updateStageStatus({
			id,
			...values,
			sendOrderId: detail.sendOrderId,
		}).then(() => {
			message.success('操作成功');
			getDetail();
			updateTaskCount();
		});
	};

	// 审核 (3:已认领 5 审核不通过)
	const onAudit = (claimStatus) => {
		if (claimStatus === 3) {
			Modal.confirm({
				title: '是否确认通过审核?',
				content: '',
				onOk() {
					updateAuditStatus({ claimStatus });
				},
			});
		} else {
			ModalFormRef.current.setOpen(true);
			ModalFormRef.current.setTitle('不通过原因');
		}
	};

	// 更新审核状态
	const updateAuditStatus = ({ claimStatus, refuseReason }) => {
		auditSendOrder({
			id: detail.sendOrderId,
			claimStatus,
			refuseReason,
		}).then(() => {
			message.success('操作成功');
			updateTaskCount();
			if (claimStatus === 3) {
				getDetail();
			} else {
				setTimeout(() => {
					linkTo(-1);
				}, 1000);
			}
		});
	};

	// 获取供给方详情
	const getDetail = () => {
		getSupplier({ id }).then((res) => {
			if (res.data) {
				setDetail(res.data);
			} else {
				message.error('供给方/合作方不存在');
				setTimeout(() => {
					linkTo(-1);
				}, 500);
			}
		});
	};

	useEffect(() => {
		if (id) {
			getDetail();
		} else {
			message.error('参数错误');
			setTimeout(() => {
				linkTo(-1);
			}, 500);
		}
	}, []);

	return (
		<div>
			<div className="flex flex-shrink align-center margin-bottom-4 height-48 font-size-22 font-weight-500 color-1d2129">
				<Space size={10}>
					<div className="a color-86909c" onClick={() => linkTo(props.linkToPath)}>
						{props.type === 1 ? '需求' : '成果'}管理
					</div>
					<div className="color-86909c">/</div>
					<div className="a color-86909c" onClick={() => linkTo(`${props.linkToPath}/detail?id=${detail.sourceId}`)}>
						{props.type === 1 ? '需求' : '成果'}详情
					</div>
					<div className="color-86909c">/</div>
					<div>{props.type === 1 ? '供给方' : '合作方'}详情</div>
				</Space>
			</div>

			{/* 供给方详情 开始 */}
			<div className="margin-top-20 padding-top-20 padding-lr-20 padding-bottom-30 border-radius-8 bg-color-ffffff">
				<div className="flex align-center justify-between">
					<div className="line-height-26 font-size-18 font-weight-500 color-1d2129">{props.type === 1 ? '供给方' : '合作方'}</div>
					{!!detail.stageStatus && (
						<div
							className="a flex align-center color-165dff"
							onClick={() => {
								linkTo(`${props.linkToPath}/docking/curd?id=${id}&sourceId=${detail.sourceId}`);
							}}
						>
							<EditOutlined />
							<div className="margin-left-8 line-height-24 font-size-16 font-weight-500">编辑信息</div>
						</div>
					)}
				</div>
				<div className="flex margin-top-20">
					<div className="margin-right-4 line-height-22 color-86909c">{props.type === 1 ? '需求' : '成果'}名称：</div>
					<div className="flex-sub flex line-height-22 ">
						<div
							className="a font-weight-500 color-165dff"
							onClick={() => {
								linkTo(fromDetail ? -1 : `${props.linkToPath}/detail?id=${detail.sourceId}`);
							}}
						>
							{detail.sourceName}
						</div>
					</div>
				</div>
				<props.BaseData detailData={detail} />

				<div className="flex margin-top-20">
					<div className="margin-right-4 line-height-22 color-86909c">当前阶段：</div>
					<div className="flex-sub line-height-22 color-1d2129">
						<div
							className={` tag-status-${
								['error', 'warning', 'primary', 'success', 'default', 'primary', 'primary'][detail.stageStatus || 0]
							}`}
						>
							{dockingStageStatusDataTextList[detail.stageStatus || 0]}
						</div>
					</div>
				</div>

				{/* 会议信息 除 待三方状态 外 有会议信息就显示 */}
				{detail.stageStatus !== 1 && (
					<>
						{detail.meetingTime && (
							<div className="flex margin-top-20">
								<div className="margin-right-4 line-height-22 color-86909c">第一次供需对接会信息：</div>
								<Space direction="vertical" className="flex-sub line-height-22 color-1d2129">
									<div className="flex">
										<div>会议时间：</div>
										<div className="flex-sub color-86909c pre-wrap">{detail.meetingTime || '--'}</div>
									</div>
									<div className="flex">
										<div>组织单位：</div>
										<div className="flex-sub color-86909c pre-wrap">{detail.meetingOrganization || '--'}</div>
									</div>
									<div className="flex">
										<div>组织人：</div>
										<div className="flex-sub color-86909c pre-wrap">{detail.meetingBrokerName || '--'}</div>
									</div>
									<div className="flex">
										<div>会议纪要：</div>
										<div className="flex-sub color-86909c pre-wrap">{detail.meetingContent || '--'}</div>
									</div>
									<div className="flex">
										<div>会议结论：</div>
										<div className="flex-sub color-86909c pre-wrap">{detail.meetingConclusion || '--'}</div>
									</div>
								</Space>
							</div>
						)}
						{detail.secondMeetingTime && (
							<div className="flex margin-top-20">
								<div className="margin-right-4 line-height-22 color-86909c">第二次供需对接会信息：</div>
								<Space direction="vertical" className="flex-sub line-height-22 color-1d2129">
									<div className="flex">
										<div>会议时间：</div>
										<div className="flex-sub color-86909c pre-wrap">{detail.secondMeetingTime || '--'}</div>
									</div>
									<div className="flex">
										<div>组织单位：</div>
										<div className="flex-sub color-86909c pre-wrap">{detail.secondMeetingOrganization || '--'}</div>
									</div>
									<div className="flex">
										<div>组织人：</div>
										<div className="flex-sub color-86909c pre-wrap">{detail.secondMeetingBrokerName || '--'}</div>
									</div>
									<div className="flex">
										<div>会议纪要：</div>
										<div className="flex-sub color-86909c pre-wrap">{detail.secondMeetingContent || '--'}</div>
									</div>
									<div className="flex">
										<div>会议结论：</div>
										<div className="flex-sub color-86909c pre-wrap">{detail.secondMeetingConclusion || '--'}</div>
									</div>
								</Space>
							</div>
						)}

						{detail.thirdMeetingTime && (
							<div className="flex margin-top-20">
								<div className="margin-right-4 line-height-22 color-86909c">第三次供需对接会信息：</div>
								<Space direction="vertical" className="flex-sub line-height-22 color-1d2129">
									<div className="flex">
										<div>会议时间：</div>
										<div className="flex-sub color-86909c pre-wrap">{detail.thirdMeetingTime || '--'}</div>
									</div>
									<div className="flex">
										<div>组织单位：</div>
										<div className="flex-sub color-86909c pre-wrap">{detail.thirdMeetingOrganization || '--'}</div>
									</div>
									<div className="flex">
										<div>组织人：</div>
										<div className="flex-sub color-86909c pre-wrap">{detail.thirdMeetingBrokerName || '--'}</div>
									</div>
									<div className="flex">
										<div>会议纪要：</div>
										<div className="flex-sub color-86909c pre-wrap">{detail.thirdMeetingContent || '--'}</div>
									</div>
									<div className="flex">
										<div>会议结论：</div>
										<div className="flex-sub color-86909c pre-wrap">{detail.thirdMeetingConclusion || '--'}</div>
									</div>
								</Space>
							</div>
						)}
					</>
				)}

				{/* 签约备注 */}
				{detail.stageStatus === 3 && detail.signingDesc && (
					<div className="flex margin-top-20">
						<div className="margin-right-4 line-height-22 color-86909c">签约备注：</div>
						<div className="flex-sub line-height-22 color-1d2129">
							<div>签约时间：{detail.updateTime}</div>
							<div className="margin-top-8 color-86909c">备注：{detail.signingDesc || '无'}</div>
						</div>
					</div>
				)}

				{/* 终止原因 */}
				{detail.stageStatus === 4 && detail.reasonReason && (
					<div className="flex margin-top-20">
						<div className="margin-right-4 line-height-22 color-86909c">终止原因：</div>
						<div className="flex-sub line-height-22 color-1d2129">
							<div>操作时间：{detail.updateTime}</div>
							<div className="margin-top-8 color-86909c">备注：{detail.reasonReason || '无'}</div>
						</div>
					</div>
				)}

				<div className="flex margin-top-20">
					<div className="margin-right-4 line-height-22 color-86909c">跟进人：</div>
					<div className="flex-sub line-height-22 color-1d2129">{detail.brokerName}</div>
				</div>
				<div className="flex margin-top-20">
					<div className="margin-right-4 line-height-22 color-86909c">更新时间：</div>
					<div className="flex-sub line-height-22 color-1d2129">{detail.updateTime || detail.createTime}</div>
				</div>
				{/* 功能按钮 开始 */}
				<div className="flex align-center justify-between margin-top-20">
					<div className="flex align-center"></div>
					<Space size={16}>
						{[1, 2, 5].includes(detail.stageStatus) && (
							<>
								<Button
									onClick={() => {
										changeStatus(4);
									}}
								>
									终止流程
								</Button>
								<Button
									type="primary"
									onClick={() => {
										changeStatus(detail.stageStatus === 1 ? 2 : detail.stageStatus === 2 ? 5 : 6);
									}}
								>
									{detail.stageStatus === 1
										? '设置第一次对接会'
										: detail.stageStatus === 2
										? '设置第二次对接会'
										: '设置第三次对接会'}
								</Button>
							</>
						)}
						{detail.stageStatus !== null && detail.stageStatus !== 3 && detail.stageStatus !== 4 && (
							<Button
								type="primary"
								onClick={() => {
									changeStatus(3);
								}}
							>
								设为已签约
							</Button>
						)}
						{detail.stageStatus === null && (
							<>
								<Button
									onClick={() => {
										onAudit(5);
									}}
								>
									不通过
								</Button>
								<Button
									type="primary"
									onClick={() => {
										onAudit(3);
									}}
								>
									通过
								</Button>
							</>
						)}
					</Space>
				</div>
				{/* 功能按钮 结束 */}
			</div>
			{/* 供给方详情 结束 */}
			{/* 弹窗 开始 */}
			<ModalForm
				ref={ModalFormRef}
				onOk={stageStatus ? updateStatus : updateAuditStatus}
				FormComp={(props) => <ConrirmForm ref={props.FormCompRef} releaseType={props.releaseType} stageStatus={stageStatus} />}
			/>

			{/* 弹窗 结束 */}
		</div>
	);
};

const Curd = (props = {}) => {
	const { searchParams } = useRouterLink();
	const id = searchParams.get('id') || '';
	const stageStatus = Form.useWatch('stageStatus', props.form);

	return (
		<>
			<Form.Item name="stageStatus" label="当前阶段" hidden={props.hideStatus} required>
				<Radio.Group options={dockingStageStatusData} disabled={!id} />
			</Form.Item>
			{/* 第一次会议  开始 */}
			<>
				<Form.Item
					name="meetingTime"
					label="会议时间"
					hidden={stageStatus !== 2}
					rules={[{ required: stageStatus === 2, message: '请选择会议时间' }]}
				>
					<FormComp.DatePicker placeholder="请选择会议时间" showTime />
				</Form.Item>
				<Form.Item
					name="meetingOrganization"
					label="组织单位"
					hidden={stageStatus !== 2}
					rules={[{ required: stageStatus === 2, message: '请输入组织单位' }]}
				>
					<Input placeholder="请输入组织单位" />
				</Form.Item>
				<Form.Item name="meetingBrokerName" hidden>
					<Input />
				</Form.Item>
				<Form.Item
					name="meetingBrokerId"
					label="组织人"
					hidden={stageStatus !== 2}
					rules={[{ required: stageStatus === 2, message: '请选择组织人' }]}
				>
					<BrokerSelect
						placeholder="请选择组织人"
						releaseType={props.releaseType}
						onChange={(_, option) => {
							props.form.setFieldsValue({ meetingBrokerName: option.brokerName });
						}}
					/>
				</Form.Item>
				<Form.Item
					name="meetingContent"
					label="会议纪要"
					hidden={stageStatus !== 2}
					rules={[{ required: stageStatus === 2, message: '请输入会议纪要' }]}
				>
					<Input.TextArea rows={4} placeholder="请输入会议纪要" />
				</Form.Item>
				<Form.Item
					name="meetingConclusion"
					label="会议结论"
					hidden={stageStatus !== 2}
					rules={[{ required: stageStatus === 2, message: '请输入会议结论' }]}
				>
					<Input.TextArea rows={4} placeholder="请输入会议结论" />
				</Form.Item>
			</>
			{/* 第一次会议  结束 */}
			{/* 第二次会议  开始 */}
			<>
				<Form.Item
					name="secondMeetingTime"
					label="会议时间"
					hidden={stageStatus !== 5}
					rules={[{ required: stageStatus === 5, message: '请选择会议时间' }]}
				>
					<FormComp.DatePicker placeholder="请选择会议时间" showTime />
				</Form.Item>
				<Form.Item
					name="secondMeetingOrganization"
					label="组织单位"
					hidden={stageStatus !== 5}
					rules={[{ required: stageStatus === 5, message: '请输入组织单位' }]}
				>
					<Input placeholder="请输入组织单位" />
				</Form.Item>
				<Form.Item name="secondMeetingBrokerName" hidden>
					<Input />
				</Form.Item>
				<Form.Item
					name="secondMeetingBrokerId"
					label="组织人"
					hidden={stageStatus !== 5}
					rules={[{ required: stageStatus === 5, message: '请选择组织人' }]}
				>
					<BrokerSelect
						placeholder="请选择组织人"
						releaseType={props.releaseType}
						onChange={(_, option) => {
							props.form.setFieldsValue({ secondMeetingBrokerName: option.brokerName });
						}}
					/>
				</Form.Item>
				<Form.Item
					name="secondMeetingContent"
					label="会议纪要"
					hidden={stageStatus !== 5}
					rules={[{ required: stageStatus === 5, message: '请输入会议纪要' }]}
				>
					<Input.TextArea rows={4} placeholder="请输入会议纪要" />
				</Form.Item>
				<Form.Item
					name="secondMeetingConclusion"
					label="会议结论"
					hidden={stageStatus !== 5}
					rules={[{ required: stageStatus === 5, message: '请输入会议结论' }]}
				>
					<Input.TextArea rows={4} placeholder="请输入会议结论" />
				</Form.Item>
			</>
			{/* 第二次会议  结束 */}

			{/* 第三次会议  开始 */}
			<>
				<Form.Item
					name="thirdMeetingTime"
					label="会议时间"
					hidden={stageStatus !== 6}
					rules={[{ required: stageStatus === 6, message: '请选择会议时间' }]}
				>
					<FormComp.DatePicker placeholder="请选择会议时间" showTime />
				</Form.Item>
				<Form.Item
					name="thirdMeetingOrganization"
					label="组织单位"
					hidden={stageStatus !== 6}
					rules={[{ required: stageStatus === 6, message: '请输入组织单位' }]}
				>
					<Input placeholder="请输入组织单位" />
				</Form.Item>
				<Form.Item name="thirdMeetingBrokerName" hidden>
					<Input />
				</Form.Item>
				<Form.Item
					name="thirdMeetingBrokerId"
					label="组织人"
					hidden={stageStatus !== 6}
					rules={[{ required: stageStatus === 6, message: '请选择组织人' }]}
				>
					<BrokerSelect
						placeholder="请选择组织人"
						releaseType={props.releaseType}
						onChange={(_, option) => {
							props.form.setFieldsValue({ thirdMeetingBrokerName: option.brokerName });
						}}
					/>
				</Form.Item>
				<Form.Item
					name="thirdMeetingContent"
					label="会议纪要"
					hidden={stageStatus !== 6}
					rules={[{ required: stageStatus === 6, message: '请输入会议纪要' }]}
				>
					<Input.TextArea rows={4} placeholder="请输入会议纪要" />
				</Form.Item>
				<Form.Item
					name="thirdMeetingConclusion"
					label="会议结论"
					hidden={stageStatus !== 6}
					rules={[{ required: stageStatus === 6, message: '请输入会议结论' }]}
				>
					<Input.TextArea rows={4} placeholder="请输入会议结论" />
				</Form.Item>
			</>
			{/* 第三次会议  结束 */}

			{/* 签约备注 特有 结束 */}

			{/* 签约备注 特有 开始 */}
			{stageStatus === 3 && (
				<>
					<Form.Item
						label="签约备注"
						name="signingDesc"
						rules={[
							{
								required: true,
								message: '请输入签约备注',
							},
						]}
					>
						<Input.TextArea rows={4} placeholder="请输入终止流程原因" />
					</Form.Item>
				</>
			)}
			{/* 签约备注 特有 结束 */}

			{/* 已终止谈判 特有 开始 */}
			{stageStatus === 4 && (
				<>
					<Form.Item
						label="终止原因"
						name="reasonReason"
						rules={[
							{
								required: true,
								message: '请输入终止流程原因',
							},
						]}
					>
						<Input.TextArea rows={4} placeholder="请输入终止流程原因" />
					</Form.Item>
				</>
			)}
			{/* 已终止谈判 特有 结束 */}
			{!props.hideStatus && (
				<Form.Item name="brokerId" label="跟进技术经理人" rules={[{ required: true, message: '请选择跟进技术经理人' }]}>
					<BrokerSelect releaseType={props.releaseType} placeholder="请选择跟进技术经理人" />
				</Form.Item>
			)}
		</>
	);
};

// 询问表单
const ConrirmForm = forwardRef((props = {}, ref) => {
	const stageStatus = props.stageStatus;
	const [form] = Form.useForm();

	useImperativeHandle(ref, () => {
		return {
			onCancel: () => Promise.resolve(),
			onOk: () => {
				return new Promise((resolve) => {
					form.validateFields().then((values) => {
						resolve({ stageStatus, ...values });
					});
				});
			},
		};
	});

	useEffect(() => {
		if (props.stageStatus) {
			form.setFieldsValue({
				stageStatus: props.stageStatus,
			});
			getBrokerList();
		}
	}, [props.stageStatus]);

	return (
		<Form form={form} labelCol={{ style: { width: '100px' } }} initialValues={{ claimStatus: 5, stageStatus: undefined }}>
			{props.stageStatus ? (
				<>
					{/* 流程流转 开始 */}
					<Curd form={form} hideStatus releaseType={props.releaseType} />
					{/* 流程流转 结束 */}
				</>
			) : (
				<>
					{/* 审核 开始 */}
					<Form.Item name="claimStatus" hidden>
						<Input />
					</Form.Item>
					<Form.Item label="原因" name="refuseReason" rules={[{ required: true, message: '请输入不通过原因' }]}>
						<Input.TextArea rows={4} placeholder="请输入不通过原因" />
					</Form.Item>
					{/* 审核 结束 */}
				</>
			)}
		</Form>
	);
});

// 经理人选择
const BrokerSelect = (props = {}) => {
	const [value, setValue] = useState(null);
	const [borkerOptions, setBorkerOptions] = useState([]);

	useEffect(() => {
		getBrokerList(props.releaseType).then((borkerOptions) => {
			setBorkerOptions(borkerOptions);
		});
	}, []);

	useEffect(() => {
		if (props.value && borkerOptions.length) {
			setValue();
			const { title } = borkerOptions.find((ov) => ov.options.some((oov) => oov.value.includes(props.value))) || {};

			if (title) {
				setValue(props.value + '|' + title);
			}
		}
		if (props.value === undefined || props.value === null) {
			setValue(null);
		}
	}, [props.value, borkerOptions]);

	return (
		<Select
			value={value}
			options={borkerOptions}
			placeholder={props.placeholder}
			optionFilterProp="label"
			allowClear
			showSearch
			onChange={(_, option) => {
				props.onChange(option.brokerId, option);
			}}
		/>
	);
};

const Index = () => {
	return <></>;
};

Index.Detail = Detail;
Index.Curd = Curd;
Index.ConrirmForm = ConrirmForm;
export default Index;

// 获取经理人列表数据
export const getBrokerList = (releaseType = '') => {
	return Promise.all([_getBrokerList(), _getSignUplist()]).then((resList) => {
		const result = resList.flat(2);
		return result;
	});

	function _getBrokerList() {
		return new Promise((resolve) => {
			brokerCategoryList({}, { isCache: true })
				.then((res) => {
					const result = (res.data || [])
						.filter((ov) => ov.technicalBrokerVoList && ov.technicalBrokerVoList.length > 0)
						.map((ov, oi) => {
							return {
								key: oi,
								label: ov.areaCategoryName,
								title: ov.areaCategoryName,
								options: ov.technicalBrokerVoList.map((oov) => {
									return {
										label: oov.userName,
										value: oov.id + '|' + ov.areaCategoryName,
										brokerId: oov.id,
										brokerName: oov.userName,
									};
								}),
							};
						});
					resolve(result);
				})
				.catch(() => resolve([]));
		});
	}

	function _getSignUplist() {
		return new Promise((resolve) => {
			if (releaseType !== 3) {
				return resolve([]);
			} else {
				listSignUp(
					{
						auditStatus: 3,
					},
					{ isCache: true }
				).then((res) => {
					resolve({
						key: new Date().getTime().toString(),
						label: '大赛人员',
						title: '大赛人员',
						options: (res.data || []).map((ov) => {
							return {
								label: (ov.name || '') + (ov.broker.userName !== ov.name ? `（${ov.broker.userName}）` : ''),
								value: ov.broker.id + '|' + '大赛人员',
								brokerId: ov.broker.id,
								brokerName: ov.broker.userName,
							};
						}),
					});
				});
			}
		});
	}
};
