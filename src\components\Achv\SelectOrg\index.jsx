import { useEffect, useState } from 'react';
import { Select } from 'antd';
import { searchAgency } from '@/api/Achv/Achievement/Area/index';
import { debounce } from '@/utils/common';

const Index = (props = {}) => {
	const [agencyList, setAgencyList] = useState([]);

	const onSearchAgency = (keywords) => {
		const defalut = [
			{
				label: keywords,
				value: keywords,
			},
		];
		if (keywords && keywords.length >= 3) {
			searchAgency({ keywords }).then((res) => {
				const resd = res.data || [];
				setAgencyList(resd.length ? resd.map((ov) => ({ label: ov.name, value: ov.id })) : defalut);

				if (resd.length === 0) {
					props.onChange(keywords);
					props.form.setFieldValue(props.keyName, null);
				}
			});
		} else {
			setAgencyList(defalut);
			props.onChange(keywords);
			props.form.setFieldValue(props.keyName, null);
		}
	};

	useEffect(() => {
		if (props.value) {
			setAgencyList([
				{
					label: props.value,
					value: props.value,
				},
			]);
		}
	}, [props]);

	return (
		<Select
			value={props.value}
			allowClear
			showSearch
			placeholder={props.placeholder}
			defaultActiveFirstOption={false}
			filterOption={false}
			onSearch={debounce(onSearchAgency, 300)}
			onChange={(_, e) => {
				const { label = null, value = null } = e || {};
				props.onChange(label);
				if (value || value === null) {
					props.form.setFieldValue(props.keyName, value);
				}
			}}
			notFoundContent={null}
			options={agencyList}
		/>
	);
};

export default Index;
