import { useEffect, useState } from 'react';

import { Space, Form, Input, Checkbox, Col, Row, Modal, message, Button } from 'antd';
import { CloseCircleOutlined, PlusOutlined, SearchOutlined, UpOutlined, RightOutlined } from '@ant-design/icons';

import { getCategoryValueList } from '@/utils/achv';
import { TTChannelsTypeData } from '@/pages/Achv/config';

import { getTTChannelsList, pageTTChannels } from '@/api/Achv/TTChannels/Signup/index';

import './index.scss';

const Index = (props = {}) => {
	const [modalInfo, setModalInfo] = useState({
		open: false,
		checkedList: [],
		disableIds: [],
	});

	const delTTChannelsIds = Form.useWatch('delTTChannelsIds', props.form);
	const checkedList = Form.useWatch(props.name, props.form);

	// 打来新增弹窗
	const onAddModal = () => {
		const checkedIds = (checkedList || []).map((ov) => ov.id);
		const disableIds = props.disableIds || props.collectDelIds ? checkedIds : [];

		setModalInfo({
			open: true,
			checkedList,
			disableIds,
		});
	};

	// 关闭弹窗
	const cloneModal = () => {
		setModalInfo({
			open: false,
			checkedList: [],
			disableIds: [],
		});
	};

	// 添加人员
	const onCheckedList = (checkedList = []) => {
		props.form.setFieldValue(props.name, checkedList);
		props.form.validateFields([props.name]);
		cloneModal();
	};

	return (
		<>
			<div className={props.className || 'margin-top-20 padding-20 border-radius-8 bg-color-ffffff'}>
				<Form.Item
					name={props.name}
					label={<div className="line-height-26 font-size-18 font-weight-500 color-1d2129">{props.label}</div>}
					labelCol={{ span: 24 }}
					rules={props.rules || []}
				>
					<Row gutter={[20, 20]}>
						<Form.List name={props.name}>
							{(fields, { remove }) => {
								return (
									<>
										{fields.map((field, index) => {
											return (
												<Col xxl={6} xl={8} lg={12} md={12} key={props.name + index} className="flex">
													<div className="flex flex-sub">
														<Form.Item {...field} key={field.key} noStyle>
															<ChannelItem
																onDel={() => {
																	if (props.collectDelIds) {
																		Modal.confirm({
																			title: '确认解除关联',
																			content: '操作后相关关联动态将会删除、此操作不可逆，确认继续操作吗？',
																			centered: true,
																			onOk: () => {
																				remove(field.name);
																				props.form.setFieldValue(
																					'delTTChannelsIds',
																					delTTChannelsIds.concat(checkedList[index].id)
																				);
																				props.form.validateFields([props.name]);
																				console.log(props.form.getFieldsValue());
																			},
																		});
																	} else {
																		remove(field.name);
																		props.form.validateFields([props.name]);
																	}
																}}
																hideDel={props.hideDel}
															/>
														</Form.Item>
													</div>
												</Col>
											);
										})}
										{!props.hideAdd && (props.maxCount === undefined || fields.length < props.maxCount) && (
											<Col xxl={6} xl={8} lg={12} md={12} className="flex">
												<div
													className="a flex-sub flex align-center justify-center padding-16 min-height-74 border-radius-6 border-solid-e5e6eb"
													onClick={() => onAddModal()}
												>
													<PlusOutlined style={{ fontSize: '24px', color: '#e5e6eb' }} />
													<div className="margin-left-4 line-height-20 font-size-14 color-1d2129">添加</div>
												</div>
											</Col>
										)}
									</>
								);
							}}
						</Form.List>
					</Row>
				</Form.Item>
			</div>

			<PresonModal
				{...modalInfo}
				maxCount={props.maxCount}
				title={props.label}
				releaseType={props.releaseType}
				onCancel={cloneModal}
				onChange={onCheckedList}
			/>
		</>
	);
};

// 选择人员弹窗
const PresonModal = (props = {}) => {
	// 所有列表
	const [keyword, setKeyword] = useState('');
	const [disableIds, setDisabledIds] = useState([]);
	const [checkedIds, setCheckedIds] = useState([]);
	const [flatTreeData, setFlatTreeData] = useState([]);
	const [checkedList, setCheckedList] = useState([]);

	// 选择
	const checkedItem = (id = '') => {
		const index = checkedIds.findIndex((ov) => ov === id);

		if (index > -1) {
			checkedIds.splice(index, 1);
		} else {
			if (props.maxCount && checkedIds.length >= props.maxCount) {
				message.warning('最多选择' + props.maxCount + '个');
				return;
			}
			checkedIds.push(id);
		}
		setCheckedIds([...checkedIds]);
	};

	// 搜索
	const [searchFlat, setSearchFlat] = useState(false);
	const [serchList, setSearchList] = useState([]);
	const onSearch = () => {
		if (searchFlat) {
			setKeyword('');
			setSearchFlat(false);
			return;
		}
		if (!keyword.trim()) {
			message.warning('请输入搜索内容');
			return;
		}
		setSearchFlat(true);
		pageTTChannels({
			accountName: keyword,
		}).then((res) => {
			const list = res?.data?.records || [];
			list.forEach((ov) => {
				ov.ttChannelsId = ov.id;
			});
			setSearchList(list);
			const flatTreeDataIds = flatTreeData.map((ov) => ov.id);
			setFlatTreeData([...flatTreeData, ...list.filter((ov) => !flatTreeDataIds.includes(ov.id))]);
		});
	};

	useEffect(() => {
		if (props.open) {
			setCheckedList(flatTreeData.filter((ov) => checkedIds.includes(ov.id)));
			setDisabledIds(props.disableIds);
		}
	}, [checkedIds, props.open]);

	useEffect(() => {
		if (props.open) {
			const checkedList = props.checkedList || [];

			// 去重加入列表
			const ids = flatTreeData.map((ov) => ov.id);
			checkedList.forEach((ov) => {
				if (!ids.includes(ov.id)) {
					flatTreeData.push(ov);
				}
			});
			setFlatTreeData([...flatTreeData]);

			setCheckedIds(checkedList.map((ov) => ov.id));
		}
	}, [props.open]);

	return (
		<Modal
			open={props.open}
			title={`设置${props.title}`}
			centered
			width="1100px"
			classNames={{
				body: 'presons-modal-box flex flex-direction-column scrollbar',
			}}
			styles={{
				content: {
					padding: 0,
				},
				header: {
					padding: '20px 24px 8px',
					borderBottom: 'solid 1px #e5e6eb',
				},
				body: {
					padding: '16px 24px',
					height: '60vh',
				},
				footer: {
					padding: '8px 24px 20px',
					borderTop: 'solid 1px #e5e6eb',
				},
			}}
			onCancel={() => props.onCancel()}
			onOk={() => {
				props.onChange(checkedList);
			}}
		>
			<div className="flex flex-sub">
				<div className="flex flex-direction-column width-320">
					<Space>
						<Input
							value={keyword}
							allowClear
							placeholder="请输入名字搜索"
							suffix={keyword ? null : <SearchOutlined />}
							onChange={(e) => {
								setKeyword(e.target.value);
							}}
						/>
						<Button type="primary" onClick={onSearch}>
							{searchFlat ? '清空' : '搜索'}
						</Button>
					</Space>
					<div className="flex-sub position-relative margin-top-16">
						<div className="position-absolute inset-0 margin-auto padding-right-12 overflowY-auto overflowX-hidden scrollbar border-solid-e5e6e8 border-radius-4">
							{!searchFlat ? (
								<TreeData
									checkedIds={checkedIds}
									setCheckedIds={setCheckedIds}
									flatTreeData={flatTreeData}
									setFlatTreeData={setFlatTreeData}
									disableIds={disableIds}
									maxCount={props.maxCount}
								/>
							) : (
								<Space direction="vertical" className="padding-16">
									{serchList.map((ov, oi) => {
										return (
											<Checkbox key={oi} checked={ov.checked} onClick={(e) => e.stopPropagation()} className={`width-100per`}>
												<div
													className="flex flex-direction-column justify-center"
													onClick={() => {
														if (ov.checked) {
															setCheckedIds([...checkedIds.filter((oov) => oov.id !== ov.id)]);
														} else {
															setCheckedIds([...checkedIds, ov.id]);
														}
														ov.checked = !ov.checked;
														setSearchList([...serchList]);
													}}
												>
													<div className="flex align-center">
														<div className={`font-size-14 ${ov.checked ? 'color-165dff' : 'color-1d2129'}`}>
															{ov.accountName || ''}
														</div>
														{ov.isAuth === 1 && (
															<img
																src="https://gbac-produce.oss-cn-shenzhen.aliyuncs.com/static-assets/gbac-achv-applet/Home/icon-renzheng.png"
																className="margin-left-4 width-20 height-20"
															/>
														)}
													</div>
													{(ov.companyName || ov.positionName) && (
														<Space
															className={`flex-sub flex align-end font-size-14 ${
																ov.checked ? 'color-165dff' : 'color-86909c'
															}`}
														>
															<div>{ov.companyName}</div>
															<div>{ov.positionName}</div>
														</Space>
													)}
												</div>
											</Checkbox>
										);
									})}
								</Space>
							)}
						</div>
					</div>
				</div>
				<div className="flex-sub position-relative margin-left-30">
					<div className="position-absolute inset-0 margin-auto overflowY-auto overflowX-hidden scrollbar">
						<Space size={12} className="line-height-24 font-size-16 font-weight-500">
							<div>已选</div>
							<div>{props.title}</div>
							<div className="color-165dff">（{checkedIds.length}人）</div>
						</Space>
						<div className="margin-top-24 ">
							<Space size={12} wrap>
								{checkedList.map((ov) => {
									const params = {
										...ov,
										onDel: !props.disableIds.includes(ov.id) ? checkedItem : undefined,
									};
									return (
										<div key={ov.id} className="width-300">
											<ChannelItem {...params} />
										</div>
									);
								})}
							</Space>
						</div>
					</div>
				</div>
			</div>
		</Modal>
	);
};

const TreeData = (props = {}) => {
	const { disableIds, flatTreeData = [], setFlatTreeData = () => {}, checkedIds = [], setCheckedIds = () => {}, maxCount } = props;
	const [treeData, setTreeData] = useState([]);
	const [expandedKeys, setExpandedKeys] = useState([]);

	// 获取树结构数据
	const getTreeData = () => {
		// 获取领域数据
		getCategoryValueList('ttchannels_area').then((list) => {
			setTreeData(
				TTChannelsTypeData.map(({ label, value }) => {
					return {
						value: value,
						label: label,
						children: list.map((oov) => {
							const { label: cLabel, value: cValue } = oov || {};
							return {
								pid: value,
								value: cValue,
								label: cLabel,
								children: [],
								loadData: false,
							};
						}),
						loadData: true,
					};
				})
			);
		});
	};

	// 获取成员数据
	const getLoadData = (ov = {}) => {
		if (ov.loadData) {
			return;
		}

		getTTChannelsList({
			channelType: ov.pid,
			categoryId: ov.value,
		}).then((res) => {
			const resData = res.data || [];
			ov.children = resData;
			ov.loadData = true;
			setTreeData([...treeData]);

			// 去重加入列表
			const ids = flatTreeData.map((ov) => ov.id);
			resData.forEach((ov) => {
				ov.id = ov.ttChannelsId;
				if (!ids.includes(ov.id)) {
					flatTreeData.push(ov);
				}
			});
			setFlatTreeData([...flatTreeData]);
		});
	};

	useEffect(() => {
		if (treeData.length === 0) {
			getTreeData();
		}
	}, []);
	return (
		<>
			<TreeItem
				listData={treeData}
				getLoadData={getLoadData}
				expandedKeys={expandedKeys}
				setExpandedKeys={setExpandedKeys}
				checkedIds={checkedIds}
				setCheckedIds={setCheckedIds}
				disableIds={disableIds}
				maxCount={maxCount}
			/>
		</>
	);
};

const TreeItem = (props = {}) => {
	const {
		disableIds,
		expandedKeys = [],
		setExpandedKeys = () => {},
		checkedIds = [],
		setCheckedIds = () => {},
		getLoadData = () => {},
		maxCount,
	} = props;
	const values = props.listData.map((ov) => (ov.pid || '') + (ov.value + ''));

	return (
		props.listData &&
		props.listData
			.filter((ov) => ov.children !== 0 || !ov.loadData) // 空白的过滤
			.map((ov, oi) => {
				const key = `${ov.pid || ''}${ov.value || ''}` || new Date().valueOf() + oi;

				const checked = ov.id && checkedIds.includes(ov.id);
				const disabled = disableIds.includes(ov.id);
				return (
					<div key={key} className="padding-left-12 padding-tb-6">
						<div
							className=""
							onClick={() => {
								if (ov.children) {
									// 同级只打开一个
									if (expandedKeys.includes(key)) {
										setExpandedKeys(expandedKeys.filter((item) => item !== key));
									} else {
										setExpandedKeys([key, ...expandedKeys.filter((item) => !values.includes(item))]);
									}
									// 未加载的去加载
									if (!ov.loadData) {
										getLoadData(ov);
									}
								} else {
									if (disabled) {
										return;
									}
									// 选中或取消
									if (checked) {
										setCheckedIds(checkedIds.filter((item) => item !== ov.id));
									} else {
										if (maxCount && checkedIds.length >= maxCount) {
											message.warning('最多选择' + maxCount + '个');
											return;
										}
										setCheckedIds([ov.id, ...checkedIds]);
									}
								}
							}}
						>
							{ov.children ? (
								<div className="a flex align-center justify-between">
									<div>{ov.label}</div>
									{expandedKeys.includes(key) ? (
										<UpOutlined
											style={{
												fontSize: '10px',
											}}
										/>
									) : (
										<RightOutlined
											style={{
												fontSize: '10px',
											}}
										/>
									)}
								</div>
							) : (
								<Checkbox
									checked={checked}
									disabled={disabled}
									onClick={(e) => e.stopPropagation()}
									className={`width-100per ${disabled ? 'cursor-not-allowed' : 'a'}`}
								>
									<div className="flex flex-direction-column justify-center">
										<div className="flex align-center">
											<div className={`font-size-14 ${checked ? 'color-165dff' : 'color-1d2129'}`}>{ov.accountName || ''}</div>
											{ov.isAuth === 1 && (
												<img
													src="https://gbac-produce.oss-cn-shenzhen.aliyuncs.com/static-assets/gbac-achv-applet/Home/icon-renzheng.png"
													className="margin-left-4 width-20 height-20"
												/>
											)}
										</div>
										{(ov.companyName || ov.positionName) && (
											<Space className={`flex-sub flex align-end font-size-14 ${checked ? 'color-165dff' : 'color-86909c'}`}>
												<div>{ov.companyName}</div>
												<div>{ov.positionName}</div>
											</Space>
										)}
									</div>
								</Checkbox>
							)}
						</div>
						{expandedKeys.includes(key) && ov.children && (
							<TreeItem
								listData={ov.children}
								getLoadData={getLoadData}
								expandedKeys={expandedKeys}
								setExpandedKeys={setExpandedKeys}
								checkedIds={checkedIds}
								setCheckedIds={setCheckedIds}
								disableIds={disableIds}
								maxCount={props.maxCount}
							/>
						)}
					</div>
				);
			})
	);
};

const ChannelItem = (props = {}) => {
	const { avatarUrl, accountName, isAuth, companyName, positionName, id, claimStatus } = props.value || props;
	const { onDel } = props;

	return (
		<div className="position-relative flex align-center padding-12 width-100per border-radius-4 border-solid-e5e6eb border-box">
			<img src={avatarUrl} className="width-50 height-50 border-radius-50" />
			<div className="flex-sub padding-lr-12">
				<div className="flex align-center">
					<div className="font-size-14 color-1d2129 text-cut" title={accountName}>
						{accountName || ''}
					</div>
					{isAuth === 1 && (
						<img
							src="https://gbac-produce.oss-cn-shenzhen.aliyuncs.com/static-assets/gbac-achv-applet/Home/icon-renzheng.png"
							className="margin-left-4 width-20 height-20"
						/>
					)}
					{[1, 3, 4].includes(claimStatus) && (
						<div className={`tag-status-${['default', 'primary', 'default', 'success', 'warning'][claimStatus]} flex-shrink`}>
							{['', '待认领', '', '已认领', '待审核', ''][claimStatus]}
						</div>
					)}
				</div>
				{(companyName || positionName) && (
					<div className="font-size-14 color-86909c">
						<span>{companyName}</span>
						<span className="margin-left-16">{positionName}</span>
					</div>
				)}
			</div>
			{!props.hideDel && onDel && (
				<CloseCircleOutlined
					className="position-absolute top-12 right-12"
					style={{ fontSize: '18px', color: '#86909c' }}
					onClick={() => {
						onDel(id);
					}}
				/>
			)}
		</div>
	);
};

export default Index;
