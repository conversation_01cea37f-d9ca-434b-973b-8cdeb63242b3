import { useEffect, useState } from 'react';
import { Select, Space, Form, DatePicker } from 'antd';
import { CalendarOutlined, CloseOutlined } from '@ant-design/icons';

import dayjs from 'dayjs';

// 已选条件
export const FilterChecked = (props = {}) => {
	const [checkedList, setCheckedList] = useState([]);

	// 删除
	const delCheck = (index = 0) => {
		const { id } = checkedList.splice(index, 1)[0];

		props.form.setFieldValue(
			id,
			checkedList.filter((ov) => ov.id === id).map((ov) => ov.value)
		);
		props.setCheckedList([...checkedList]);
	};

	useEffect(() => {
		setCheckedList(props.checkedList);
	}, [props.checkedList]);
	return (
		<Space wrap size={[12, 8]} className="margin-left-10 padding-tb-4">
			{checkedList.map((ov, oi) => {
				return (
					<div key={oi} className="flex align-center padding-lr-8 line-height-26 border-radius-2 nowrap bg-color-165dff color-ffffff">
						<div className="label">
							{ov.name}：{ov.label}
						</div>
						<div className="a padding-left-4 font-size-12" onClick={() => delCheck(oi)}>
							<CloseOutlined />
						</div>
					</div>
				);
			})}
		</Space>
	);
};

// 选项组件
export const FilterOption = (props = {}) => {
	// 选中/取消
	const checked = (value = '', label = '') => {
		const checkedList = props.checkedList;
		if (value === '') {
			props.onChange([]);
			props.setCheckedList(checkedList.filter((ov) => ov.id !== props.id));
		} else {
			const list = props.value;
			const index = list.indexOf(value);
			if (index > -1) {
				list.splice(index, 1);
				const cIndex = checkedList.findIndex((ov) => ov.id === props.id && ov.value === value);
				if (cIndex > -1) {
					checkedList.splice(cIndex, 1);
				}
			} else {
				list.push(value);
				checkedList.push({
					id: props.id,
					name: props.name,
					label: label,
					value: value,
				});
			}
			props.onChange([...list]);
			props.setCheckedList([...checkedList]);
		}
	};
	return (
		<>
			<div
				className={`padding-lr-10 cursor-pointer hover-color-165dff ${props.value.length === 0 ? 'color-165dff' : ''}`}
				onClick={() => {
					checked();
				}}
			>
				不限
			</div>
			{props.options.map((ov) => {
				return (
					<div
						key={ov.value}
						className={`padding-lr-10 cursor-pointer hover-color-165dff ${props.value.includes(ov.value) ? 'color-165dff' : ''}`}
						onClick={() => {
							checked(ov.value, ov.label);
						}}
					>
						{ov.label}
					</div>
				);
			})}
		</>
	);
};

// 标题
export const FilterTitle = (props = {}) => {
	return (
		<div className={`flex line-height-34 ${props.className}`}>
			<div className="flex justify-end width-120 font-size-14 color-86909c">{props.title}</div>
			<div className="flex-sub flex flex-wrap padding-lr-10">{props.children}</div>
		</div>
	);
};
// 时间组件
const nowYear = dayjs().year();
export const yearOptions = new Array(nowYear - 2021).fill(0).map((ov, oi) => {
	return {
		label: `${nowYear - oi}年`,
		value: nowYear - oi,
	};
});

export const FilterDate = (props = {}) => {
	return (
		<Form.Item noStyle name={props.name}>
			<Select
				className="width-100per"
				allowClear
				options={yearOptions}
				placeholder={`请选择${props.label}`}
				suffixIcon={<CalendarOutlined />}
			/>
		</Form.Item>
	);
};

// FilterRangeDate
export const FilterRangeDate = (props = {}) => {
	const [timeList, setTimeList] = useState([]);

	useEffect(() => {
		if (props.value) {
			const [start, end] = props.value;
			setTimeList([start ? dayjs(start) : null, end ? dayjs(end) : null]);
		}
	}, [props.value]);
	return (
		<DatePicker.RangePicker
			size="small"
			className="margin-left-10 bg-color-f7f8fa"
			value={timeList}
			onChange={(e) => {
				if (e) {
					props.onChange([dayjs(e[0]).format('YYYY-MM-DD'), dayjs(e[1]).format('YYYY-MM-DD')]);
				} else {
					props.onChange([]);
				}
			}}
		></DatePicker.RangePicker>
	);
};
