import { useEffect, memo } from 'react';
import { Chart } from '@antv/g2';

const initPie = (el, data = []) => {
	const chart = new Chart({
		container: el,
		autoFit: true,
	});

	chart.coordinate({
		type: 'theta',
		innerRadius: 0.65,
	});

	chart
		.interval()
		.data(data)
		.transform({ type: 'stackY' })
		.encode('y', 'value')
		.encode('color', 'color')
		.scale('color', {
			type: 'identity'
		})
		.tooltip({
			title: (d) => d.name, 
		})
		.legend(false);

	chart.render();
};

const Index = (props) => {
	useEffect(() => {
		if (props.id && props.data && props.data.length) {
			initPie(props.id, (props.data || []).map(ov => {
				return {
					...ov,
					color: `#${ov.color}`
				}
			}));
		}
	}, [props.data]);

	return <div className='width-100per height-100per' id={props.id}></div>;
};

export default memo(Index);
