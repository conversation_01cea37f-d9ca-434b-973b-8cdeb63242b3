import { Space } from 'antd';
import { useRouterLink } from '@/hook/useRouter';
import SvgIcon from '@/assets/icons';

const Index = (props) => {
	const { linkTo } = useRouterLink();
	return (
		<Space className={`flex align-center margin-lr-20 margin-bottom-12 line-height-20 font-size-14 color-4e5969 ${props.className || ''}`}>
			<div className="width-18 height-18 color-4e5969">
				<SvgIcon style={{ fontSize: '16px' }} type={props.icon} />
			</div>
			<div>/</div>
			{(props.list || []).map((item, index) => {
				return (
					<div className="flex align-center" key={index}>
						<div
							className="a hover-color-165dff"
							onClick={() => {
								linkTo(item.link);
							}}
						>
							{item.name}
						</div>
						<div className="margin-left-10">/</div>
					</div>
				);
			})}
			<div className="font-bold color-1d2129">{props.name}</div>
		</Space>
	);
};

export default Index;
