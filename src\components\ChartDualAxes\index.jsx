import { DualAxes } from '@ant-design/plots';

const Index = (props = {}) => {
	const { config = {}, className = '' } = props;

	// 如果配置中没有指定颜色，则添加默认颜色配置
	const chartConfig = {
		...config,
		...(config.geometryOptions ? {
			geometryOptions: config.geometryOptions.map((option, index) => {
				// 如果已经配置了color，直接使用配置的color
				if (option.color) {
					return option;
				}

				// 为柱状图(column)添加默认颜色配置
				if (option.geometry === 'column') {
					return {
						...option,
						color: '#386BFF' // 柱状图默认橙色
					};
				}
				// 为线图(line)添加默认颜色配置
				if (option.geometry === 'line') {
					return {
						...option,
						color: '#3BA4FF' // 线图默认蓝色
					};
				}
				return option;
			})
		} : {})
	};

	return (
		<div className={className}>
			<DualAxes {...chartConfig} />
		</div>
	);
};

export default Index;
