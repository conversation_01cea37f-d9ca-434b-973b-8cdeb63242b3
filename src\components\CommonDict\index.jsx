import { Button, Table, Popconfirm, Input, Modal, Form, Switch, Tag } from 'antd';
import { SearchOutlined, PlusOutlined } from '@ant-design/icons';
import { useEffect, useRef, useState } from 'react';

const Index = (props = { add, batchDel, pageCategoryValue, update }) => {
	console.log(props);
	const { add, batchDel, pageCategoryValue, update } = props;
	const [name, setName] = useState('');
	const [isModalOpen, setIsModalOpen] = useState(false);
	const [curRow, setCurRow] = useState({});

	const [dataSource, setDataSource] = useState([]);
	const [total, setTotal] = useState(0);

	// 页码数据
	const [pagination, setPagination] = useState({
		pageNum: 1,
		pageSize: 10,
	});

	// 页码页数改变
	const changePage = (pageNum, pageSize) => {
		pagination.pageSize = pageSize;
		pagination.pageNum = pageNum;
		setPagination({ ...pagination });
	};

	const getList = () => {
		pageCategoryValue({
			code: props.categoryCode || '',
			name: name || '',
		}).then((res) => {
			setDataSource(res.data || []);
			setTotal((res.data || []).length);
		});
	};

	useEffect(() => {
		if (isModalOpen) {
			formRef.current.setFieldValue('itemName', curRow.itemName || '');
			formRef.current.setFieldValue('rankingNum', curRow.rankingNum || '');
			formRef.current.setFieldValue('status', curRow.status !== undefined ? curRow.status : 1);
			formRef.current.setFieldValue('remarks', curRow.remarks || '');
		}
	}, [isModalOpen]);

	const [form] = Form.useForm();
	const formRef = useRef();

	const submit = () => {
		form.validateFields().then((values) => {
			const params = {
				id: curRow.id || '',
				itemName: values.itemName || '', // 字典名称
				code: props.categoryCode, // 字典编码
				status: values.status || 0, // 状态:0停用 1启用
				rankingNum: values.rankingNum || '', // 排名序号
				remarks: values.remarks || '',
			};
			if (curRow.id) {
				update(params).then(() => {
					reset();
					getList();
				});
			} else {
				add(params).then(() => {
					reset();
					getList();
				});
			}
		});
	};

	const reset = () => {
		formRef.current && formRef.current.resetFields && formRef.current.resetFields();
		setIsModalOpen(false);
		setCurRow({});
	};

	useEffect(() => {
		getList();
	}, []);
	return (
		<div className={`flex-sub flex flex-direction-column ${props.tabItem ? '' : 'padding-20'}`}>
			<div className={`flex-sub bg-color-ffffff border-radius-4  ${props.tabItem ? '' : 'padding-20'}`}>
				<div className="font-size-16 font-weight-500 color-1d2129 line-height-24 margin-bottom-20">{`${props.valueName}配置管理`}</div>
				<div className="flex justify-between align-center margin-bottom-20">
					<div className="flex-sub">
						<Button
							type="primary"
							icon={<PlusOutlined />}
							onClick={() => {
								setCurRow({});
								setIsModalOpen(true);
							}}
						>
							新建
						</Button>
					</div>
					{!props.tabItem && (
						<>
							<Input
								placeholder="请输入关键词"
								className="width-280 margin-right-14"
								suffix={<SearchOutlined />}
								onInput={(e) => {
									setName(e.target.value || '');
								}}
								onPressEnter={() => {
									changePage(1, 10);
								}}
							/>
							<Button type="primary" icon={<SearchOutlined />} onClick={() => getList()}>
								查询
							</Button>
						</>
					)}
				</div>
				<Table
					size="small"
					rowKey="id"
					dataSource={dataSource}
					changePage={changePage}
					pagination={total >= pagination.pageSize ? { ...pagination, total } : false}
				>
					<Table.Column
						title="序号"
						key="index"
						dataIndex="index"
						width={110}
						render={(text, record, index) => {
							return index + 1 + pagination.pageSize * (pagination.pageNum - 1);
						}}
					/>
					<Table.Column title={`${props.valueName}名称`} key="itemName" dataIndex="itemName" />
					<Table.Column
						title="启用状态"
						key="status"
						dataIndex="status"
						align="center"
						render={(status) => {
							return <Tag color={status == 1 ? 'success' : 'default'}>{status == 1 ? '开启' : '关闭'}</Tag>;
						}}
					/>
					<Table.Column title="排序" key="rankingNum" dataIndex="rankingNum" />
					<Table.Column
						title="操作"
						key="option"
						dataIndex="option"
						width={220}
						render={(_, record) => {
							return (
								<>
									<Button
										type="link"
										size="small"
										onClick={() => {
											setCurRow(record);
											setIsModalOpen(true);
										}}
									>
										编辑
									</Button>

									<Popconfirm
										title="提示"
										description="确定删除吗？"
										onConfirm={() => {
											batchDel({ ids: [record.id], dictItemIds: [record.id] }).then(() => {
												reset();
												if (dataSource.length > 1) {
													getList();
												} else {
													if (pagination.pageNum > 1) {
														changePage(pagination.pageNum - 1, pagination.pageSize);
													} else {
														getList();
													}
												}
											});
										}}
										okText="确定"
										cancelText="取消"
									>
										<Button type="link" danger size="small">
											删除
										</Button>
									</Popconfirm>
								</>
							);
						}}
					/>
				</Table>
				<Modal
					title={`${props.valueName}${form.id ? '修改' : '新增'}`}
					open={isModalOpen}
					maskClosable={false}
					onOk={() => {
						submit();
					}}
					onCancel={() => {
						reset();
					}}
				>
					<Form
						labelCol={{
							span: 6,
						}}
						wrapperCol={{
							span: 18,
						}}
						autoComplete="off"
						ref={formRef}
						form={form}
					>
						<Form.Item
							label={`${props.valueName}名称`}
							name="itemName"
							prop="itemName"
							rules={[
								{
									required: true,
									message: `请输入${props.valueName}名称`,
								},
							]}
						>
							<Input placeholder={`请输入${props.valueName}名称`} />
						</Form.Item>
						<Form.Item label="启用状态" prop="status" name="status">
							<CustomSwitch />
						</Form.Item>
						<Form.Item label="排序" prop="rankingNum" name="rankingNum">
							<Input />
						</Form.Item>
					</Form>
				</Modal>
			</div>
		</div>
	);
};

// 自定义
const CustomSwitch = (props = {}) => {
	return (
		<Switch
			checked={props.value === 1}
			onChange={(e) => {
				props.onChange(e ? 1 : 0);
			}}
		/>
	);
};

export default Index;
