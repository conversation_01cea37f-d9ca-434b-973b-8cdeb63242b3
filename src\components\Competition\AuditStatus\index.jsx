import { useEffect, forwardRef, useImperativeHandle, useState, useRef } from 'react';
import { Modal, Radio, Form, Input, message } from 'antd';

const Index = forwardRef((props = {}, ref) => {
	const [visible, setVisible] = useState(false);
	const formRef = useRef();
	const [form] = Form.useForm();
	const auditStatus = Form.useWatch('auditStatus', form);
	const [row, setRow] = useState({});
	// 表单提交
	const submit = () => {
		form.validateFields().then((values) => {
			setVisible(false);
			props.onSubmit && props.onSubmit(row, values);
		});
	};

	// 撤销审核
	const revokeAuditConfirm = (data) => {
		Modal.confirm({
			title: '提示',
			content: `是否确定撤销审核该数据？`,
			okText: '确定',
			cancelText: '取消',
			onOk: () => {
				props.onRevoke && props.onRevoke(data);
			},
		});
	};

	useImperativeHandle(ref, () => {
		return {
			open: (data = {}, { auditStatus = 2, auditReason = '' }) => {
				form.setFieldValue('auditStatus', auditStatus);
				form.setFieldValue('auditReason', auditReason || '');
				setVisible(true);
				setRow(data);
			},
			revoke: (data = {}) => {
				revokeAuditConfirm(data);
				setRow(data);
			},
		};
	});

	return (
		<>
			<Modal open={visible} onCancel={() => setVisible(false)} onOk={submit} title="审核" centered>
				<div className="margin-top-20">
					<Form form={form} ref={formRef}>
						<Form.Item label="审核结果" name="auditStatus">
							<Radio.Group
								options={[
									{
										label: '通过',
										value: 2,
									},
									{
										label: '不通过',
										value: 1,
									},
								]}
							/>
						</Form.Item>
						{auditStatus === 1 && (
							<Form.Item label="拒绝原因" name="auditReason">
								<Input.TextArea rows={4} placeholder="请输入拒绝原因" />
							</Form.Item>
						)}
					</Form>
				</div>
			</Modal>
		</>
	);
});

export default Index;
