import { Button, Table, Popconfirm, Input, Modal, Form, Switch, Tag, Select, Image } from 'antd';
import { SearchOutlined, PlusOutlined, CloseCircleOutlined } from '@ant-design/icons';
import { useEffect, useRef, useState } from 'react';

import UploadImg from '@/components/UploadImg';
const Index = (props = {}) => {
	const [isModalOpen, setIsModalOpen] = useState(false);
	const [curRow, setCurRow] = useState({});

	const [imgUrl, setImgUrl] = useState('');

	useEffect(() => {}, [props]);

	useEffect(() => {
		if (isModalOpen) {
			formRef.current.setFieldValue('name', curRow.name || '');
			formRef.current.setFieldValue('imgUrl', curRow.imgUrl || '');
			formRef.current.setFieldValue('jumpUrl', curRow.jumpUrl || '');
			formRef.current.setFieldValue('status', curRow.status === false ? false : true);
			formRef.current.setFieldValue('rankingNum', curRow.rankingNum || '');
			setImgUrl(curRow.imgUrl || '');
		}
	}, [isModalOpen]);

	const [form] = Form.useForm();
	const formRef = useRef();

	const submit = () => {
		form.validateFields().then((values) => {
			const params = {
				id: curRow.id || `${new Date().valueOf()}`,
				name: values.name || '', //
				imgUrl: values.imgUrl || '', //
				jumpUrl: values.jumpUrl || '', //
				status: values.status, // 状态
				rankingNum: values.rankingNum || '', // 排名序号
			};
			console.log('🚀 ~ form.validateFields ~ params:', params);
			const result = props.dataSource;

			if (curRow.id) {
				const findIndex = props.dataSource.findIndex((ov) => {
					return ov.id == curRow.id;
				});
				if (findIndex == -1) return;
				result[findIndex] = params;
			} else {
				result.push(params);
			}

			// 排序
			result.sort((a, b) => a.rankingNum - b.rankingNum);
			props.setDataSource(result);
			reset();
		});
	};

	const reset = () => {
		formRef.current && formRef.current.resetFields && formRef.current.resetFields();
		setIsModalOpen(false);
		setCurRow({});
	};
	return (
		<div>
			<div className="flex align-center justify-between line-height-24 padding-tb-16">
				<div className="flex align-center justify-start">
					<div className="width-6 height-16 border-radius-4 bg-color-165dff"></div>
					<div className="font-size-16 font-weight-500 margin-left-8">{props.title || '轮播图管理'}</div>
				</div>
				<Button
					type="primary"
					icon={<PlusOutlined />}
					onClick={() => {
						setCurRow({});
						setIsModalOpen(true);
					}}
				>
					新建
				</Button>
			</div>
			<Table size="small" rowKey="id" dataSource={[...props.dataSource].sort((a, b) => a.rankingNum - b.rankingNum)} pagination={false}>
				<Table.Column
					title="序号"
					key="index"
					dataIndex="index"
					width={100}
					render={(text, record, index) => {
						return index + 1;
					}}
				/>
				<Table.Column title={`名称`} key="name" dataIndex="name" />
				<Table.Column
					title={`图片`}
					key="imgUrl"
					dataIndex="imgUrl"
					width={120}
					align="center"
					render={(url) => {
						return <Image src={url} width={80} />;
					}}
				/>
				<Table.Column title="排序" key="rankingNum" dataIndex="rankingNum" width={100} align="center" />
				<Table.Column
					title="启用状态"
					key="status"
					dataIndex="status"
					align="center"
					width={160}
					render={(status) => {
						return <Tag color={status ? 'success' : 'default'}>{status ? '开启' : '关闭'}</Tag>;
					}}
				/>
				<Table.Column
					title="操作"
					key="option"
					dataIndex="option"
					align="center"
					width={220}
					render={(_, record) => {
						return (
							<>
								<Button
									type="link"
									size="small"
									onClick={() => {
										setCurRow(JSON.parse(JSON.stringify(record)));
										setIsModalOpen(true);
									}}
								>
									编辑
								</Button>

								<Popconfirm
									title="提示"
									description="确定删除吗？"
									onConfirm={() => {
										props.setDataSource(
											props.dataSource.filter((ov) => {
												return ov.id != record.id;
											})
										);
									}}
									okText="确定"
									cancelText="取消"
								>
									<Button type="link" danger size="small">
										删除
									</Button>
								</Popconfirm>
							</>
						);
					}}
				/>
			</Table>
			<Modal
				title={`编辑`}
				open={isModalOpen}
				maskClosable={false}
				onOk={() => {
					submit();
				}}
				onCancel={() => {
					reset();
				}}
			>
				<Form
					labelCol={{
						span: 6,
					}}
					wrapperCol={{
						span: 18,
					}}
					autoComplete="off"
					ref={formRef}
					form={form}
					initialValues={{
						status: true,
					}}
				>
					<Form.Item
						label={`名称`}
						name="name"
						prop="name"
						rules={[
							{
								required: true,
								message: `请输入名称`,
							},
						]}
					>
						<Input />
					</Form.Item>
					<Form.Item
						label={`跳转地址`}
						name="jumpUrl"
						prop="jumpUrl"
						rules={[
							{
								required: !true,
								message: `请输入跳转地址`,
							},
						]}
					>
						<Input.TextArea />
					</Form.Item>

					<Form.Item
						label="图片上传"
						rules={[
							{
								required: true,
							},
						]}
						name="imgUrl"
					>
						<UploadImg size={10} width={340} height={120} tips="建议尺寸：1920px*430px" direction="vertical" />
					</Form.Item>

					<Form.Item label="启用状态" prop="status" name="status">
						<Switch />
					</Form.Item>

					<Form.Item label="排序" prop="rankingNum" name="rankingNum">
						<Input />
					</Form.Item>
				</Form>
			</Modal>
		</div>
	);
};

export default Index;
