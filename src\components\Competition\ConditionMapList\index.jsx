import { getImageSrc } from '@/assets/images/index';
import { memo } from 'react';

const Index = (props = { list: [], clickItem: () => {} }) => {
	return (
		<>
			{props.list.map((ov) => {
				return (
					<div
						className={`a margin-right-30 margin-bottom-20 font-weight-500 ${ov.active ? 'color-165dff' : ''}`}
						key={ov.label}
						onClick={() => props.clickItem && props.clickItem(ov)}
					>
						{ov.label}
					</div>
				);
			})}
		</>
	);
};
export default Index;
