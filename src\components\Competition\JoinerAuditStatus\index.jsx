import { useEffect, useState } from 'react';
import { Modal, Radio, Form, Input, message } from 'antd';
import { batchAuditStatus } from '@/api/Competition/UserManage/joiner';

const Index = (props = {}) => {
	const [visible, setVisible] = useState(false);
	const [form] = Form.useForm();
	const auditStatus = Form.useWatch('auditStatus', form);

	// 撤销审核
	const revokeAuditConfirm = (id) => {
		Modal.confirm({
			title: '提示',
			content: `是否确定撤销审核该数据？`,
			okText: '确定',
			cancelText: '取消',
			onOk: () => {
				changeAuditStatus(0);
			},
		});
	};

	// 表单提交
	const submit = () => {
		form.validateFields().then((values) => {
			changeAuditStatus(values.auditStatus, values.auditReason);
			setVisible(false);
		});
	};

	// 审核通过/不通过/撤销
	const changeAuditStatus = (auditStatus, auditReason = '') => {
		batchAuditStatus({ ids: props.ids, auditStatus, auditReason }).then(() => {
			message.success('操作成功');
			props.onCallback && props.onCallback();
		});
	};
	return (
		<>
			<div
				onClick={() => {
					if (props.isRevoke) {
						revokeAuditConfirm();
					} else {
						setVisible(true);
					}
				}}
			>
				{props.children}
			</div>
			<Modal open={visible} onCancel={() => setVisible(false)} onOk={submit} title="是否确定对已选数据进行审核？" centered>
				<div className="margin-top-20">
					<Form form={form}>
						<Form.Item label="审核结果" name="auditStatus">
							<Radio.Group
								options={[
									{
										label: '通过',
										value: 2,
									},
									{
										label: '不通过',
										value: 1,
									},
								]}
							/>
						</Form.Item>
						{auditStatus === 1 && (
							<Form.Item label="拒绝原因" name="auditReason">
								<Input.TextArea rows={4} placeholder="请输入拒绝原因" />
							</Form.Item>
						)}
					</Form>
				</div>
			</Modal>
		</>
	);
};

export default Index;
