import { useEffect, forwardRef, useImperativeHandle, useState, useRef } from 'react';
import { Modal, Radio, Form, Input, Button } from 'antd';

const Index = forwardRef((props = {}, ref) => {
	const [visible, setVisible] = useState(false);
	const [src, setSrc] = useState({});
	const [type, setType] = useState('');

	useImperativeHandle(ref, () => {
		return {
			open: (url = '', typeKey = '') => {
				console.log('🚀 ~ useImperativeHandle ~ url:', url);
				if (!url) {
					return;
				}
				setVisible(true);
				setType(typeKey);
				// const fileType = ['mp4', 'pdf', 'ppt/pptx', 'mov', 'avi']
				if (['ppt', 'pptx'].includes(typeKey)) {
					setSrc(`https://view.officeapps.live.com/op/view.aspx?src=${url}`);
				} else {
					setSrc(`${url}`);
				}
			},
		};
	});

	return (
		<Modal
			open={visible}
			onCancel={() => setVisible(false)}
			cancelText={null}
			onOk={() => setVisible(false)}
			title="预览"
			centered
			afterClose={() => setSrc('')}
			width={'70%'}
			footer={null}
		>
			{src && <iframe src={src} frameBorder="0" className={`${!props.class ? 'width-100per height-80vh' : props.class}`}></iframe>}
		</Modal>
	);
});

export default Index;
