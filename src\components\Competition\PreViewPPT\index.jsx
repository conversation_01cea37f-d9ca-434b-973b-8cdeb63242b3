
import { useEffect, forwardRef, useImperativeHandle, useState, useRef } from 'react';
import { Modal, Radio, Form, Input, message } from 'antd';

const Index = forwardRef((props = {}, ref) => {
    const [visible, setVisible] = useState(false);
    const [src, setSrc] = useState({})

    useImperativeHandle(ref, () => {
        return {
            open: (url = '') => {
                if (!url) {
                    return
                }
                setVisible(true);
                setSrc(`https://view.officeapps.live.com/op/view.aspx?src=${url}`);
            },
        };
    });

    return (
        <Modal
            open={visible}
            onCancel={() => setVisible(false)}
            onOk={() => setVisible(false)}
            title='预览'
            centered
            afterClose={() => setSrc('')}
            width={'70%'}
        >
            {src && <iframe src={src} frameBorder="0" className={`${!props.class ? 'width-100per height-80vh' : props.class}`}></iframe>}

        </Modal>
    );
});

export default Index;
