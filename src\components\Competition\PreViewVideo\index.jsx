
import { useEffect, forwardRef, useImperativeHandle, useState, useRef } from 'react';
import { Modal, Radio, Form, Input, message } from 'antd';

const Index = forwardRef((props = {}, ref) => {
    const [visible, setVisible] = useState(false);
    const [src, setSrc] = useState({})

    useImperativeHandle(ref, () => {
        return {
            open: (url = '') => {
                if (!url) {
                    return
                }
                setVisible(true);
                setSrc(`${url}`);
            },
        };
    });

    return (
        <Modal
            open={visible}
            onCancel={() => setVisible(false)}
            onOk={() => setVisible(false)}
            title='预览'
            centered
            destroyOnClose={true}
            afterClose={() => setSrc('')}
            width={'70%'}
        >
            {(visible && src) ? (<iframe src={src} allowfullscreen frameBorder="0" className={`${!props.class ? 'width-100per height-80vh' : props.class}`}></iframe>) : null}

        </Modal>
    );
});

export default Index;
