import { useEffect, useState, useRef } from 'react';

import { Button, Space } from 'antd';

import PreViewFile from '@/components/Competition/PreViewFile/index';

import { getFormTemplateDetail } from '@/api/Competition/CompetitionManage';

const Index = (props = {}) => {
	const { templateId = '', attachedList = [] } = props;
	const [formTemplaData, setFormTemplaData] = useState([]);
	const [signUpFormData, setSignUpFormData] = useState({});

	// 获取报名表单模版
	const getFormTemplaData = () => {
		getFormTemplateDetail({ id: templateId }).then((res) => {
			const list = res.data?.pieces || []
			list.forEach(ov=>{
				ov.fields = [...(ov.fields || [])].sort((a, b) => a.sort - b.sort);
			})
			setFormTemplaData(list);
		});
	};

	// 获取报名信息格式化
	const getSignFormData = () => {
		const result = {};
		attachedList.forEach((ov) => {
			try {
				let fieldValue = JSON.parse(ov.fieldValue);

				if (fieldValue !== null && typeof fieldValue === 'object' && !Array.isArray(fieldValue)) {
					fieldValue = [fieldValue];
				}

				result[ov.enName] = fieldValue.map((ov) => (ov && ov.label) || ov).filter((ov) => ov);
			} catch (error) {
				result[ov.enName] = ov.fieldValue;
			}
		});
		setSignUpFormData(result);
	};

	useEffect(() => {
		if (templateId && formTemplaData.length === 0) {
			getFormTemplaData();
		}
	}, [templateId]);

	useEffect(() => {
		if (attachedList) {
			getSignFormData();
		}
	}, [attachedList]);

	const PreViewFileRef = useRef();
	const previewBtn = (url = '') => {
		const fieldValueList = url.split('.');
		PreViewFileRef.current.open(url, fieldValueList.pop());
	};
	return (
		<div>
			{formTemplaData.map((ov) => {
				return (
					<div key={ov.id}>
						<div className="flex align-center justify-start line-height-24 padding-tb-12">
							<div className="width-6 height-16 border-radius-4 bg-color-165dff"></div>
							<div className="font-size-16 font-weight-500 margin-left-8">{ov.name}</div>
						</div>
						{ov.fields.map((oov) => {
							return (
								<div key={oov.id} className="flex align-start justify-start line-height-24 padding-tb-12 padding-lr-24">
									<div className="flex justify-end min-width-168 font-size-16 color-4e5969">{oov.cnName}：</div>
									<div className="flex-sub font-size-16 font-weight-500">
										{/* fieldType 1 单选 2多选 3单行 4多行 5上传 6 日期 */}
										{[3, 4].includes(oov.fieldType) && (
											<div className="padding-lr-10 padding-tb-4 line-height-22 min-height-22 border-radius-4 bg-color-f2f3f5">
												{signUpFormData[oov.fieldName] || ''}
											</div>
										)}
										{[1, 2].includes(oov.fieldType) && (
											<Space size={12}>
												{(signUpFormData[oov.fieldName] || []).map((label, index) => {
													return (
														<Button key={index} type="primary" size="small" ghost>
															{label}
														</Button>
													);
												})}
											</Space>
										)}

										{oov.fieldType === 5 && (
											<Space size={16}>
												<div
													className="cursor-pointer color-165dff"
													onClick={() => previewBtn(signUpFormData[oov.fieldName])}
													href={signUpFormData[oov.fieldName]}
													target="_blank"
												>
													{(signUpFormData[oov.fieldName] || '').split('/').pop()}
												</div>
												<Button ghost type="primary" size="small" onClick={() => window.open(signUpFormData[oov.fieldName])}>
													下载
												</Button>
											</Space>
										)}
										{oov.fieldType === 6 && (
											<div className="padding-lr-10 padding-tb-4 line-height-22 min-height-22 border-radius-4 bg-color-f2f3f5">
												{(signUpFormData[oov.fieldName] || '').substr(0, 10)}
											</div>
										)}
										{oov.fieldType === 7 && (
											<div className="padding-lr-10 padding-tb-4 line-height-22 min-height-22 border-radius-4 bg-color-f2f3f5">
												{(signUpFormData[oov.fieldName] || []).join(' / ')}
											</div>
										)}
									</div>
								</div>
							);
						})}
					</div>
				);
			})}
			<PreViewFile ref={PreViewFileRef} />
		</div>
	);
};

export default Index;
