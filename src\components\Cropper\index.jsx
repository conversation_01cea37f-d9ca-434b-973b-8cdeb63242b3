/**
 * @description index.jsx - 文件描述
 * <AUTHOR>
 *
 * Created on 2024/9/27 13:56
 */
import { forwardRef, useEffect, useImperativeHandle, useRef, useState } from 'react';
import Cropper from 'react-cropper';
import 'cropperjs/dist/cropper.css';
import { Al<PERSON>, Button, Modal } from 'antd';
import { debounce } from '@/utils/common';
import { PlusOutlined, MinusOutlined, ReloadOutlined } from '@ant-design/icons';

const CropperImage = forwardRef((props, ref) => {
	const [open, setOpen] = useState(false);
	/* 截取后的图片 */
	const [cropImg, setCropImg] = useState('');
	const [file, setFile] = useState(null);
	const cropperRef = useRef(null);

	/* 关闭弹框 */
	const handleCancel = () => {
		setOpen(false);
		props.promise?.reject();
	};
	const handleOk = () => {
		setOpen(false);
		props.promise?.resolve(cropImg);
	};
	const onCrop = () => {
		const cropper = cropperRef.current?.cropper;
		// const dataURL = cropper.getCroppedCanvas().toDataURL();
		cropper.getCroppedCanvas().toBlob(
			(blob) => {
				if (blob) {
					setCropImg(blob);
				}
			},
			undefined,
			1
		);
		console.log('~cropper');
	};
	useImperativeHandle(ref, () => {
		return {
			open: () => {
				console.log('open', props, file);
				setOpen(true);
			},
		};
	});
	/* 操作按钮 */
	const handleOption = (type) => {
		switch (type) {
			case 'zoomIn':
				cropperRef.current?.cropper.zoom(0.1);
				break;
			case 'zoomOut':
				cropperRef.current?.cropper.zoom(-0.1);
				break;
			case 'rotateLeft':
				cropperRef.current?.cropper.rotate(-45);
				break;
			case 'rotateRight':
				cropperRef.current?.cropper.rotate(45);
				break;
			default:
		}
	};
	return (
		<Modal open={open} title={'图片截取'} width={980} onCancel={handleCancel} onOk={handleOk} maskClosable={false}>
			<div className={'margin-tb-12'}>
				<Alert showIcon message={'您可以通过双击裁剪器来切换“裁剪”和“移动”模式。'} type="info" />
			</div>
			<Cropper
				src={props.src}
				style={{ height: 500, width: '100%' }}
				aspectRatio={props.width / props.height}
				guides={false}
				fixedBox={true}
				cropBoxMovable={true}
				cropBoxResizable={false}
				dragMode={'crop'}
				viewMode={1}
				crop={debounce(onCrop, 200)}
				ref={cropperRef}
				autoCrop={true}
			/>
			<div className={'flex justify-center gap-12 margin-top-12'}>
				<Button shape="circle" icon={<PlusOutlined />} onClick={() => handleOption('zoomIn')} />
				<Button shape="circle" icon={<MinusOutlined />} onClick={() => handleOption('zoomOut')} />
				<Button shape="circle" icon={<ReloadOutlined />} onClick={() => handleOption('rotateRight')} />
				<Button shape="circle" icon={<ReloadOutlined className={'scaleX-1'} />} onClick={() => handleOption('rotateLeft')} />
			</div>
		</Modal>
	);
});
export default CropperImage;
