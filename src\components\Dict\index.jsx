import { Button, Table, Popconfirm, Input, Modal, Form, Switch, Tag } from 'antd';
import { SearchOutlined, PlusOutlined } from '@ant-design/icons';
import { useEffect, useRef, useState } from 'react';

const Index = (props = { add, batchDel, pageCategoryValue, update }) => {
	const { add, batchDel, pageCategoryValue, update } = props;
	const [name, setName] = useState('');
	const [isModalOpen, setIsModalOpen] = useState(false);
	const [curRow, setCurRow] = useState({});

	const [dataSource, setDataSource] = useState([]);
	const [total, setTotal] = useState(0);

	// 页码数据
	const [pagination, setPagination] = useState({
		pageNum: 1,
		pageSize: 1000,
	});

	// 页码页数改变
	const changePage = (pageNum, pageSize) => {
		pagination.pageSize = pageSize;
		pagination.pageNum = pageNum;
		setPagination({ ...pagination });
	};

	const getList = () => {
		pageCategoryValue({
			categoryCode: props.categoryCode || '',
			name: name || '',
			pageNum: pagination.pageNum,
			pageSize: pagination.pageSize,
		}).then((res) => {
			setDataSource(res.data.records || []);
			setTotal(res.data.total - 0);
		});
	};

	useEffect(() => {
		getList();
	}, [pagination]);

	useEffect(() => {
		if (isModalOpen) {
			formRef.current.setFieldValue('value', curRow.value || '');
			formRef.current.setFieldValue('serialNo', curRow.serialNo || '');
			formRef.current.setFieldValue('remarks', curRow.remarks || '');
		}
	}, [isModalOpen]);

	const [form] = Form.useForm();
	const formRef = useRef();

	const submit = () => {
		form.validateFields().then((values) => {
			console.log('🚀 ~ form.validateFields ~ values:', values);
			const params = {
				categoryCode: props.categoryCode || '',
				id: curRow.id || '',
				value: values.value || '',
				serialNo: values.serialNo || '',
				remarks: values.remarks || '',
			};
			if (curRow.id) {
				update(params).then(() => {
					reset();
					getList();
				});
			} else {
				add(params).then(() => {
					reset();
					getList();
				});
			}
		});
	};

	const reset = () => {
		formRef.current && formRef.current.resetFields && formRef.current.resetFields();
		setIsModalOpen(false);
		setCurRow({});
	};
	return (
		<div className="flex-sub flex flex-direction-column padding-20">
			<div className="padding-20 flex-sub bg-color-ffffff border-radius-4">
				<div className="font-size-16 font-weight-500 color-1d2129 line-height-24 margin-bottom-20">{`${props.valueName}配置管理`}</div>
				<div className="flex justify-between align-center margin-bottom-20">
					<div className="flex-sub">
						<Button
							type="primary"
							icon={<PlusOutlined />}
							onClick={() => {
								setCurRow({});
								setIsModalOpen(true);
							}}
						>
							新建
						</Button>
					</div>
					<Input
						placeholder="请输入关键词"
						className="width-280 margin-right-14"
						suffix={<SearchOutlined />}
						onInput={(e) => {
							setName(e.target.value || '');
						}}
						onPressEnter={() => {
							changePage(1, 10);
						}}
					/>
					<Button type="primary" icon={<SearchOutlined />} onClick={() => getList()}>
						查询
					</Button>
				</div>
				{/* pagination={{ ...pagination, total }} */}
				<Table size="small" rowKey="id" dataSource={dataSource} changePage={changePage} pagination={false}>
					<Table.Column
						title="序号"
						key="index"
						dataIndex="index"
						width={110}
						render={(text, record, index) => {
							return index + 1 + pagination.pageSize * (pagination.pageNum - 1);
						}}
					/>
					<Table.Column title={`${props.valueName}名称`} key="value" dataIndex="value" />
					{/* 商机阶段特有 */}
					{props.categoryCode === 'opportunity_stage' && (
						<Table.Column
							title="进度"
							key="remarks"
							dataIndex="remarks"
							render={(remarks) => {
								return `${remarks}%`;
							}}
						/>
					)}

					{/* 项目阶段特有 */}
					{props.categoryCode === 'project_stage' && (
						<Table.Column
							title="过会标识"
							key="remarks"
							dataIndex="remarks"
							align="center"
							render={(remarks) => {
								return <Tag color={remarks === 'RMSign' ? 'success' : 'red'}>{remarks === 'RMSign' ? '是' : '否'}</Tag>;
							}}
						/>
					)}
					{(props.categoryCode === 'customer_type' || props.categoryCode === 'reliability_type') && (
						<Table.Column title="简称" key="remarks" dataIndex="remarks" />
					)}

					<Table.Column title="排序" key="serialNo" dataIndex="serialNo" />
					<Table.Column
						title="操作"
						key="option"
						dataIndex="option"
						width={220}
						render={(_, record) => {
							return (
								<>
									<Button
										type="link"
										size="small"
										onClick={() => {
											setCurRow(record);
											setIsModalOpen(true);
										}}
									>
										编辑
									</Button>

									<Popconfirm
										title="提示"
										description="确定删除吗？"
										onConfirm={() => {
											batchDel({ ids: [record.id] }).then(() => {
												reset();
												if (dataSource.length > 1) {
													getList();
												} else {
													if (pagination.pageNum > 1) {
														changePage(pagination.pageNum - 1, pagination.pageSize);
													} else {
														getList();
													}
												}
											});
										}}
										okText="确定"
										cancelText="取消"
									>
										<Button type="link" danger size="small">
											删除
										</Button>
									</Popconfirm>
								</>
							);
						}}
					/>
				</Table>
				<Modal
					title={`${props.valueName}${form.id ? '修改' : '新增'}`}
					open={isModalOpen}
					maskClosable={false}
					onOk={() => {
						submit();
					}}
					onCancel={() => {
						reset();
					}}
				>
					<Form
						labelCol={{
							span: 6,
						}}
						wrapperCol={{
							span: 18,
						}}
						autoComplete="off"
						ref={formRef}
						form={form}
					>
						<Form.Item
							label={`${props.valueName}名称`}
							name="value"
							prop="value"
							rules={[
								{
									required: true,
									message: `请输入${props.valueName}名称`,
								},
							]}
						>
							<Input />
						</Form.Item>

						{/* 商机阶段特有 */}
						{props.categoryCode === 'opportunity_stage' && (
							<Form.Item
								label="进度"
								prop="remarks"
								name="remarks"
								rules={[
									{
										required: true,
										message: `请输入进度`,
									},
								]}
							>
								<Input suffix="%" />
							</Form.Item>
						)}
						{/* 项目阶段特有 */}
						{props.categoryCode === 'project_stage' && (
							<Form.Item label="过会标识" prop="remarks" name="remarks">
								<Switch
									checked={curRow.remarks === 'RMSign'}
									onChange={(e) => {
										curRow.remarks = e ? 'RMSign' : '';
										formRef.current.setFieldValue('remarks', curRow.remarks);
										setCurRow({ ...curRow });
									}}
								/>
							</Form.Item>
						)}

						{/* 客户类型 商机自评 特有 */}
						{(props.categoryCode === 'customer_type' || props.categoryCode === 'reliability_type') && (
							<Form.Item
								label={`简称`}
								name="remarks"
								prop="remarks"
								rules={[
									{
										required: true,
										message: `请输入简称`,
									},
								]}
							>
								<Input />
							</Form.Item>
						)}

						<Form.Item label="排序" prop="serialNo" name="serialNo">
							<Input />
						</Form.Item>
					</Form>
				</Modal>
			</div>
		</div>
	);
};

export default Index;
