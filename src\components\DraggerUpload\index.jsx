import { message, Upload } from 'antd';

import { fileUpload } from '@/api/common';

const Index = (props = {}) => {
	const beforeUpload = (file) => {
		console.log('🚀 ~ beforeUpload ~ file:', file);
		props.setLoading?.(true);
		if (props.justGetFile) {
			props.onChange && props.onChange(file);
			props.setLoading?.(false);
		} else {
			const formData = new FormData();
			formData.append('files', file);
			formData.append('fileName', props.customName ? props.fileName || `${new Date().getTime().toString()}_${file.name}` : '');
			const options = {};
			if (props.timeout) {
				options.timeout = props.timeout;
			}
			fileUpload(formData, options)
				.then((res) => {
					props.onChange && props.onChange(res[0] || '', file);
					props.setLoading?.(false);
				})
				.catch((err) => {
					message.error('上传失败');
					props.setLoading?.(false);
				});
		}
		return false;
	};

	return (
		<Upload.Dragger accept={props.accept || null} showUploadList={false} beforeUpload={(e) => beforeUpload(e)}>
			{props.children}
		</Upload.Dragger>
	);
};

export default Index;
