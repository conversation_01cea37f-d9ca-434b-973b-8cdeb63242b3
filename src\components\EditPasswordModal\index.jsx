import { useState } from "react";
import { useSelector, useDispatch } from "react-redux";
import { logout } from "@/utils/common";

import { Button, Space, Modal, message, Form, Input } from "antd";

import { updatePassword } from "@/api/login";

const Index = (props = {}) => {
    const [form] = Form.useForm();
    const [open, setOpen] = useState(false);

    const dispatch = useDispatch();
    // 登录凭证
    const userInfo = useSelector((state) => {
        return state.user.userInfo;
    });

    // 验证密码是否一致
    const validPassword = (_, value) => {
        if (value === form.getFieldValue("password")) {
            return Promise.resolve();
        }
        return Promise.reject("两次密码不一致");
    };

    // 提交保存
    const submit = () => {
        form.validateFields().then((values) => {
            updatePassword({
                userId: userInfo.id,
                password: values.password,
            }).then(() => {
                logout().then(() => {
                    message.success({
                        content: "重置成功，请重新登录",
                        duration: 1,
                        onClose() {
                            window.location.href =
                                import.meta.env.VITE_BASE_PATH +
                                (import.meta.env.VITE_BASE_PATH
                                    ? "/login".slice(1)
                                    : "/login");
                        },
                    });
                    setOpen(false);
                });
            });
        });
    };

    return (
        <>
            <div className={props.className} onClick={() => setOpen(true)}>
                {props.children}
            </div>
            <Modal
                zIndex={1051}
                title="重置账号密码"
                open={open}
                styles={{
                    content: {
                        paddingBottom: "1px",
                    },
                }}
                footer={false}
                width={500}
                centered
                onCancel={() => {
                    setOpen(false);
                }}
            >
                <Form
                    form={form}
                    className="margin-top-24"
                    labelCol={{
                        style: {
                            width: "140px",
                        },
                    }}
                >
                    <Form.Item
                        label="id"
                        name="id"
                        required={true}
                        style={{ display: "none" }}
                    >
                        <Input placeholder="id" />
                    </Form.Item>
                    <Form.Item
                        label="登录密码"
                        name="password"
                        rules={[
                            {
                                required: true,
                                message: "请输入登录密码",
                            },
                        ]}
                    >
                        <Input.Password placeholder="请输入登录密码" />
                    </Form.Item>
                    <Form.Item
                        label="确认密码"
                        name="password2"
                        rules={[
                            {
                                required: true,
                                message: "请输入确认密码",
                            },
                            { validator: validPassword, trigger: "blur" },
                        ]}
                    >
                        <Input.Password placeholder="请输入登录密码" />
                    </Form.Item>
                    <Form.Item label=" " colon={false}>
                        <Space size={18}>
                            <Button type="primary" onClick={submit}>
                                保存
                            </Button>
                            <Button
                                type="primary"
                                ghost
                                onClick={() => setOpen(false)}
                            >
                                取消
                            </Button>
                        </Space>
                    </Form.Item>
                </Form>
            </Modal>
        </>
    );
};

export default Index;
