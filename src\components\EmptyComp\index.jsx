import { memo } from 'react'
import { getImageSrc } from '@/assets/images/index'
const Index = (props = {}) => {
	return (
		<div className='flex flex-direction-column align-center padding-40'>
			<img
				className='width-360 height-360'
				src={getImageSrc(
					'@/assets/images/Enterprise/Emphasize/list-empty.png'
				)}
			/>
			<div className='font-size-16'>{props.tips || '没有找到相关数据'}</div>
		</div>
	)
}
export default memo(Index)
