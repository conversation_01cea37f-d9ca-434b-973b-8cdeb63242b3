import { useState, useEffect } from 'react';
import { DatePicker as AntdDatePicker } from 'antd';
import dayjs from 'dayjs';

const Index = (props = {}) => {
	return (
		<AntdDatePicker
			{...props}
			value={props.value ? dayjs(props.value) : null}
			onChange={(e) => {
				props.onChange && props.onChange(e?.format(props.valueFormat || 'YYYY-MM-DD HH:mm:ss'));
				props.onCb && props.onCb(e?.format(props.valueFormat || 'YYYY-MM-DD HH:mm:ss'));
			}}
		/>
	);
};

const RangePicker = (props = {}) => {
	const [timeList, setTimeList] = useState([null, null]);
	useEffect(() => {
		if (props.value) {
			try {
				const data = typeof props.value === 'object' ? props.value : JSON.parse(props.value);
				setTimeList(data.map((ov) => (ov ? dayjs(ov) : null)));
			} catch (error) {}
		}
	}, [props.value]);
	return (
		<AntdDatePicker.RangePicker
			{...props}
			value={timeList}
			onChange={(e) => {
				if (e) {
					props.onChange &&
						props.onChange([
							e[0] ? e[0].format(props.valueFormat || 'YYYY-MM-DD') : undefined,
							e[1] ? e[1].format(props.valueFormat || 'YYYY-MM-DD') : undefined,
						]);
				} else {
					props.onChange && props.onChange([undefined, undefined]);
				}
			}}
		/>
	);
};

Index.RangePicker = RangePicker;

export default Index;
