import { useState, forwardRef, useImperativeHandle, memo } from 'react';

import { Form, Input, message, Button, Space, Upload } from 'antd';
import { downloadFileByUrl } from '@/utils/common';

import './index.scss';

const Index = forwardRef((props, ref) => {
	const [fileList, setFileList] = useState([]);

	// 提交
	const handleSubmit = () => {
		return new Promise((resolve, reject) => {
			if (fileList.length === 0) {
				message.error('请选择文件');
				reject();
			} else {
				const formData = new FormData();
				formData.append(props.fileName, fileList[0]);
				if(props.customParams){
					Object.keys(props.customParams).forEach(key=>{
						formData.append(key, props.customParams[key]);
					})
				}
				setFileList([]);
				resolve(formData);
			}
		});
	};

	// 取消
	const handleCancel = () => {
		return new Promise((resolve) => {
			setFileList([]);
			resolve();
		});
	};

	useImperativeHandle(ref, () => {
		return {
			onCancel: handleCancel,
			onOk: handleSubmit,
		};
	});
	return (
		<div className='import-form-box'>
			<Form
				labelCol={{
					style: { width: '80px' },
				}}
				autoComplete='off'
				labelAlign='right'
			>
				<Form.Item label='选择文件'>
					<Space className='width-100per' direction='vertical'>
						<Upload
							maxCount={1}
							accept='.xls, .xlsx, application/vnd.openxmlformats-officedocument.spreadsheetml.sheet, application/vnd.ms-excel'
							className='width-100per'
							fileList={fileList}
							beforeUpload={(file) => {
								setFileList([file]);
								return false;
							}}
							onRemove={() => {
								setFileList([]);
							}}
						>
							<Input
								placeholder='请选择文件'
								readOnly={true}
								suffix={
									<Button type='primary'>选择文件</Button>
								}
							/>
						</Upload>
						<Space>
							<div className='font-size-12'>
								请上传 大小不超过
								<span className='padding-lr-6 color-165dff'>
									10MB
								</span>
								格式为
								<span className='padding-lr-6 color-165dff'>
									xls/xlsx
								</span>
								的文件
							</div>
							{props.tplUrl && (
								<div
									className='font-size-12 color-165dff cursor-pointer'
									onClick={() => {
										downloadFileByUrl(
											props.tplUrl,
											props.tplName || '下载模版'
										);
									}}
								>
									下载模板
								</div>
							)}
						</Space>
					</Space>
				</Form.Item>
			</Form>
		</div>
	);
});

export default memo(Index);
