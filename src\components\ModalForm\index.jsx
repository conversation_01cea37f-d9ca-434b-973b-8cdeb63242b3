import { useEffect, useState, forwardRef, useImperativeHandle, memo, useRef } from 'react';
import { Space } from 'antd';
import { Modal } from 'antd';

const Index = forwardRef((props = {}, ref) => {
	const { FormComp, footerExtra = null } = props;
	const FormCompRef = useRef();
	const [open, setOpen] = useState(false);
	const [title, setTitle] = useState('');
	const [config, setConfig] = useState({});

	const onCancel = (res) => {
		setOpen(false);
		props.onCancel && props.onCancel(res);
	};

	const onOk = (res) => {
		setOpen(false);
		props.onOk && props.onOk(res);
	};

	useEffect(() => {
		const { modelConfig } = props;
		setConfig({
			...modelConfig,
			styles: {
				content: {
					padding: 0,
					...(modelConfig?.styles?.content || {}),
				},
				header: {
					padding: '20px 24px 8px',
					borderBottom: 'solid 1px #e5e6eb',
					...(modelConfig?.styles?.header || {}),
				},
				body: {
					padding: '16px 24px',
					minHeight: '150px',
					maxHeight: '70vh',
					overflow: 'auto',
					...(modelConfig?.styles?.body || {}),
				},
				footer: {
					padding: '8px 24px 20px',
					borderTop: 'solid 1px #e5e6eb',
					...(modelConfig?.styles?.footer || {}),
				},
			},
			width: modelConfig?.width || '600px',
			style: {
				...(modelConfig?.style || {}),
			},
		});
	}, [props.modelConfig]);

	useImperativeHandle(ref, () => {
		return {
			setOpen,
			setTitle,
		};
	});

	return (
		<Modal
			{...config}
			centered
			open={open}
			title={title}
			destroyOnClose
			onCancel={() => {
				FormCompRef.current &&
					FormCompRef.current.onCancel &&
					FormCompRef.current
						.onCancel()
						.then((res) => {
							onCancel(res);
						})
						.catch((err) => {
							console.log('FormCompRef.current.onCancel err:', err);
						});
			}}
			onOk={() => {
				FormCompRef.current &&
					FormCompRef.current.onOk &&
					FormCompRef.current
						.onOk()
						.then((res) => {
							onOk(res);
						})
						.catch((err) => {
							console.log('FormCompRef.current.onOk err:', err);
						});
			}}
			footer={(Btns) => {
				return (
					<div className="flex align-center justify-between">
						<div>{footerExtra}</div>
						<Space size={8}>{Btns}</Space>
					</div>
				);
			}}
		>
			<FormComp open={open} FormCompRef={FormCompRef} />
		</Modal>
	);
});

export default memo(Index);
