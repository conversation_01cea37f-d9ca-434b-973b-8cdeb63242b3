import { useEffect, useState } from 'react';
import { useSelector } from 'react-redux';
import { useRouterLink } from '@/hook/useRouter';
import { getAccessToken } from '@/utils/common';
const Index = (props = {}) => {
	const { location, openNewTab } = useRouterLink();
	const [pathname, setPathname] = useState([]);

	// 系统列表
	const routerList = useSelector((state) => {
		return state.user.routerList || [];
	}).filter((item) => item.perms && item.perms !== 'system');

	useEffect(() => {
		setPathname(routerList.length === 1 ? location.pathname.replace(`/${routerList[0].perms}`, '') : location.pathname);
		/*  openNewTab(
            `https://operate-smp.dwq360.com${
                location.pathname
            }?token=${getAccessToken()}`
        ); */
	}, [location.pathname]);

	return (
		<div className="flex-sub flex flex-direction-column padding-20 width-100per height-100per font-size-26 border-box">
			{/* <div className="flex-sub flex align-center justify-center bg-color-ffffff">
                跳转中...
            </div> */}
			{pathname && <iframe className="flex-sub" src={`${import.meta.env.VITE_IFRAME_HOST}${pathname}?token=${getAccessToken()}`}></iframe>}
		</div>
	);
};

export default Index;
