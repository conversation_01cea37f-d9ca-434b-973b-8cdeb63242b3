import { useSelector } from 'react-redux';

export const isPermi = (hasPermi) => {
	const permi = useSelector((state) => {
		return ((state.user.userInfo && state.user.userInfo.permissionList) || []).some((ov) => {
			if (Array.isArray(hasPermi)) {
				return hasPermi.includes(ov.perms);
			}
			if (typeof hasPermi == 'string') {
				return hasPermi == ov.perms;
			}
		});
	});
	return permi;
};

const Index = ({ children, hasPermi = [], empty }) => {
	return <>{(isPermi(hasPermi) && children) || empty}</>;
};

export default Index;
