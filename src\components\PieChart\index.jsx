import { useEffect, memo } from 'react';
import { Chart } from '@antv/g2';

/* const data1 = [
	{ name: '事例一', opportunityCount: 40 },
	{ name: '事例二', opportunityCount: 21 },
	{ name: '事例三', opportunityCount: 17 },
	{ name: '事例四', opportunityCount: 13 },
	{ name: '事例五', opportunityCount: 9 },
];
 */
const initPie = (props) => {
	const el = props.id;
	const {data = [], y = 'opportunityCount', text, fill, unit = '', name = 'name', nameUnit = '', count} = props;
	const chart = new Chart({
		container: el,
		autoFit: true,
	});
	chart.coordinate({
		type: 'theta',
		outerRadius: 0.8,
		innerRadius: 0.5,
	});
	chart
		.interval()
		.data(data)
		.transform({ type: 'stackY' })
		.encode('y', y)
		.encode('color', name)
		// .style('stroke', 'white')
		// .style('inset', 1)
		// .style('radius', 4)
		// .scale('color', {
		// 	palette: 'spectral',
		// 	offset: (t) => t * 0.8 + 0.1,
		// })
		.legend('color', {
			position: 'right', layout: { justifyContent: 'center' }, labelFormatter: (datum, index) => {
				return `${datum || ''}`.split(' ')[0]
			},
		})
		// .label({
		// 	position: 'outside',
		// 	rotate: true,
		// 	text: (data) =>
		// 		`${data.nameRemarks || data[name]} ${nameUnit}:${data[y]} ${unit}`,
		// })
		.tooltip((data) => ({
			name: `${data[name]} ${nameUnit}`,
			value: `${data[y]} ${unit}`,
		}));
	if (count) {
		chart
			.text()
			.style('text', `${+count.toFixed(2)}`)
			// Relative position
			.style('x', '50%')
			.style('y', '50%')
			.style('fontSize', 16)
			.style('fontWeight', 'bold')
			.style('fill', fill || '#333')
			.style('textAlign', 'center');
	}
	if (text) {
		chart
			.text()
			.style('text', `${text}`)
			// Absolute position
			.style('x', '50%')
			.style('y', '60%')
			.style('fontSize', 14)
			.style('fill', '#86909c')
			.style('textAlign', 'center');
	}

	chart.render();
};

/* props
* @param {String} y 图表y轴参数
* @param {String} text 图表内容
* @param {String} count 统计内容
* @param {String} fill 文本颜色
* @param {String} unit 单位
* @param {String} name 名称
* @param {String} nameUnit 名称单位
*   */
const Index = (props) => {
	useEffect(() => {
		if (props.id && props.data && props.data.length) {
			initPie(props);
		}
	}, [props.data]);

	return <div className='width-100per height-100per' id={props.id}></div>;
};

export default memo(Index);
