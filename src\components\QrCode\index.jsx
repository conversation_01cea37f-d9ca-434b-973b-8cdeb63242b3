import { Button } from 'antd';
import { DownloadOutlined } from '@ant-design/icons';
import { download, downloadFileByUrl } from '@/utils/common';

const Index = () => {
	return <></>;
};

const QRCodebase64 = (props = {}) => {
	const onDownload = (base64data, fileName) => {
		download.markBase64(base64data, fileName);
	};

	return <QrCode {...props} download={onDownload} />;
};

const QRCodeUrl = (props = {}) => {
	const onDownload = (fileUrl, fileName) => {
		downloadFileByUrl(fileUrl, fileName);
	};

	return <QrCode {...props} download={onDownload} />;
};

const QrCode = (props) => {
	return (
		<div className="qr-code-bg flex justify-center padding-tb-14">
			{(props.qrCodeList || [])
				.filter((ov) => ov.qrcodeUrl)
				.map((ov) => {
					return (
						<div className="flex flex-direction-column justify-center padding-lr-12" key={ov.qrcodeUrl}>
							<div className="margin-bottom-8 line-height-22 font-weight-500 text-align-center color-1d2129">{ov.qrcodeName}</div>
							<img className={`margin-bottom-24 ${props.qrcodeClassName || 'width-140 height-140'}`} src={ov.qrcodeUrl} />
							<Button type="primary" icon={<DownloadOutlined />} onClick={() => props.download(ov.qrcodeUrl, ov.qrcodeName)}>
								下载
							</Button>
						</div>
					);
				})}
		</div>
	);
};

Index.QRCodebase64 = QRCodebase64;
Index.QRCodeUrl = QRCodeUrl;
export default Index;
