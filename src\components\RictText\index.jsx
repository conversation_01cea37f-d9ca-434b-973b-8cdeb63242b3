import { useState } from 'react';
import { message, Upload } from 'antd';

import ReactQuill from 'react-quill';
import 'react-quill/dist/quill.snow.css'; //富文本样式文件

import './index.scss';

const modules = {
	toolbar: {
		container: [
			['bold', 'italic', 'underline', 'strike'], // 加粗 斜体 下划线 删除线
			['blockquote', 'code-block'], // 引用  代码块
			[{ list: 'ordered' }, { list: 'bullet' }], // 有序、无序列表
			[{ indent: '-1' }, { indent: '+1' }], // 缩进
			[{ size: ['small', false, 'large', 'huge'] }], // 字体大小
			[{ header: [1, 2, 3, 4, 5, 6, false] }], // 标题
			[{ color: [] }, { background: [] }], // 字体颜色、字体背景颜色
			[{ align: [] }], // 对齐方式
			['clean'], // 清除文本格式
			['image'],
			//["link", "image", "video"]
		],
	},
};

const RictText = (props) => {
	const onChange = (e) => {
		props.onChange(e);
	};
	return (
		<div className="rict-text">
			<ReactQuill
				value={props.value}
				className={`quill-box ${props.className || 'height-250'}`}
				placeholder={props.placeholder || '请输入文章内容'}
				theme="snow"
				readOnly={props.disabled}
				modules={modules}
				onChange={onChange}
			/>
		</div>
	);
};

export default RictText;
