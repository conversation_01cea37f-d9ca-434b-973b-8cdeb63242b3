import { useState, useEffect } from 'react';
import { Table } from 'antd';

const Index = (props = {}) => {
	const [columns, setColumns] = useState([]);
	const [dataSource, setDataSource] = useState([]);

	useEffect(() => {
		setColumns(
			[
				...(props.hideSort
					? []
					: [
							{
								title: '序号',
								dataIndex: 'sort',
								align: 'center',
								width: 68,
								render: (text, record, index) => index + 1,
							},
						]),
				...props.columns,
			].map((item) => {
				return {
					...item,
					className: item.className + ' vertical-align-middle',
				};
			})
		);
	}, [props.hideSort, props.columns]);

	useEffect(() => {
		if (props.dataSource.length && props.dataSource[0].key === undefined) {
			setDataSource(
				props.dataSource.map((item) => {
					return {
						...item,
						key: item.id || Math.ceil(Math.random() * 10000000),
					};
				})
			);
		} else {
			setDataSource(props.dataSource);
		}
	}, [props.dataSource]);
	return (
		<Table
			size="small"
			scroll={{ x: 'max-content' }}
			className={props.className}
			columns={columns}
			dataSource={dataSource}
			pagination={
				props.pagination && props.pagination.total
					? {
							current: props.pagination.pageNum,
							pageSize: props.pagination.pageSize,
							total: props.pagination.total,
							onChange: props.pagination.onChange,
							showSizeChanger: true,
							showQuickJumper: true,
						}
					: false
			}
			rowSelection={props.rowSelection}
		></Table>
	);
};

export default Index;
