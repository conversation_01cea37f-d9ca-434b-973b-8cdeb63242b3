import { useEffect, useState, useRef } from 'react';
import RcUeditor from 'react-ueditor-wrap';

import { fileUpload } from '@/api/common';

import { EDITOR_TOOLBAR_CONFIG } from './const';

import { appOrigin } from '@/config/env';

import './index.scss';

const UEditor = (props) => {
	const [value, setValue] = useState('');
	const [init, setInit] = useState(false);

	const hanldeChage = (e) => {
		if (init) {
			props.onChange(e);
		} else {
			setInit(true);
		}
	};

	useEffect(() => {
		if (value === '' && props.value) {
			setValue(props.value);
		}
	}, [props.value]);
	return (
		<>
			<RcUeditor
				className={`ueditor_component ${props.className || ''}`}
				value={value}
				ueditorUrl={`${appOrigin}/static/UEditorPlus/ueditor.all.js`}
				ueditorConfigUrl={`${appOrigin}/static/UEditorPlus/ueditor.config.js`}
				editorConfig={{
					zIndex: 1,
					serverUrl: `${appOrigin}/static/UEditorPlus/config.json`,
					toolbars: [props.toolbars || EDITOR_TOOLBAR_CONFIG],
					uploadServiceEnable: true,
					uploadServiceUpload: (_, file, callback, __) => {
						const currentFile = file?.blob?.source || new File([file], file.name);
						const formData = new FormData();
						formData.append('files', currentFile);
						fileUpload(formData)
							.then((res) => {
								callback.success({
									state: 'SUCCESS',
									url: res[0] || '',
								});
							})
							.catch(() => {
								callback.error({
									state: 'Error',
								});
							});
					},
					topOffset: 90,
					initialFrameWidth: '100%',
					initialFrameHeight: props.height || 320,
				}}
				onChange={hanldeChage}
			/>
		</>
	);
};
export default UEditor;
