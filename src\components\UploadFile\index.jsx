import { Button, Input, message, Space, Upload } from 'antd';
import { DeleteOutlined } from '@ant-design/icons';

import { fileUpload } from '@/api/common';
import { getImageSrc } from '@/assets/images/index';

const Index = (props = {}) => {
	const beforeUpload = (file) => {
		console.log('🚀 ~ beforeUpload ~ file:', file);
		props.setLoading?.(true);
		if (props.justGetFile) {
			props.onChange && props.onChange(file);
			props.setLoading?.(false);
		} else {
			const formData = new FormData();
			formData.append('files', file);
			formData.append('fileName', props.customName ? props.fileName || `${new Date().getTime().toString()}_${file.name}` : '');
			const options = {};
			if (props.timeout) {
				options.timeout = props.timeout;
			}
			fileUpload(formData, options)
				.then((res) => {
					props.onChange && props.onChange(res[0] || '', file);
					props.setLoading?.(false);
				})
				.catch((err) => {
					message.error('上传失败');
					props.setLoading?.(false);
				});
		}
		return false;
	};

	return !props.children && props.value ? (
		<Input
			className="width-100per"
			value={props.value.split('/').pop()}
			suffix={
				<Button type="link" size="small" onClick={() => props.onChange && props.onChange('')}>
					删除
				</Button>
			}
			readOnly
		/>
	) : (
		<Space>
			<Upload accept={props.accept || null} showUploadList={false} beforeUpload={(e) => beforeUpload(e)} className="width-100per">
				{props.children ? (
					props.children
				) : (
					<div
						className="position-relative border-radius-4 overflow-hidden"
						style={{ width: (props.width || 120) + 'px', height: (props.height || 120) + 'px', border: '1px dashed rgb(22, 93, 255)' }}
					>
						<div className="position-absolute inset-0 flex flex-direction-column align-center justify-center cursor-pointer">
							<img className="width-60 height-60" src={getImageSrc('@/assets/images/Public/fileup-icon.png')} />
							<div>上传文件</div>
						</div>
					</div>
				)}
			</Upload>
			{!props.value && props.tips && (
				<div className="flex-sub flex flex-direction-column justify-center align-start line-height-20 font-size-12 color-86909c">
					{props.tips}
				</div>
			)}
		</Space>
	);
};

export default Index;
