import { useEffect, useRef, useState, forwardRef } from 'react';
import { Image, message, Space, Upload } from 'antd';
import { DeleteOutlined } from '@ant-design/icons';

import { fileUpload } from '@/api/common';

import { getImageSrc } from '@/assets/images/index';
import CropperImage from '@/components/Cropper';

import { useRouterLink } from '@/hook/useRouter';

export const CustomUpload = forwardRef((props, ref) => {
	const [fileList, setFileList] = useState([]);
	const cropperRef = useRef(null);
	const [promise, setPromise] = useState(null);
	const [cropperImg, setCropperImg] = useState(null);
	const uploadFile = useRef(null);
	const formData = useRef(new FormData());

	// 上传前校验
	const beforeUpload = (file, fileList) => {
		const size = props.size || 3;
		const isJpgOrPng = file.type === 'image/jpg' || file.type === 'image/jpeg' || file.type === 'image/png';
		if (!isJpgOrPng) {
			message.error('支持上传的图片格式：jpg、 jpeg、 png');
			return Upload.LIST_IGNORE;
		}
		const limitSize = file.size / 1024 / 1024 < size;
		if (!limitSize) {
			message.error(`图片大小不超过${size}M`);
			return Upload.LIST_IGNORE;
		}
		if (props.maxCount && fileList.length > props.maxCount) {
			file.uid === fileList[0].uid && message.error(`最多上传${props.maxCount}张图片`);
			return Upload.LIST_IGNORE;
		}
		if (!props.cropperProps) {
			return true;
		}
		const p = new Promise((res, rej) => {
			console.log('🚀 ~ Promise ~ limitSize:', limitSize);
			setPromise({ resolve: res, reject: rej });
			/* 图片文件转成base64 */
			const reader = new FileReader();
			reader.onload = function (event) {
				setCropperImg(event.target.result);
				cropperRef.current && cropperRef.current.open();
			};
			reader.readAsDataURL(file);
		});
		p.then(
			(res) => {
				console.log('🚀 ~ Promise ~ res:', res);
				/* 将base64对象转成图片文件 */
				const dataURLtoFile = (dataurl, filename) => {
					const arr = dataurl.split(',');
					const mime = arr[0].match(/:(.*?);/)[1];
					const bstr = atob(arr[1]);
					let n = bstr.length;
					const u8arr = new Uint8Array(n);
					while (n--) {
						u8arr[n] = bstr.charCodeAt(n);
					}
					return new File([u8arr], filename, { type: mime });
				};
				uploadFile.current = dataURLtoFile(res, file.name);
			},
			(err) => {
				console.log('🚀 ~ Promise ~ err:', err);
			}
		);
		return p;
	};

	// 自定义上传
	const customRequest = (option) => {
		const file = uploadFile.current || option.file;
		console.log(file);

		formData.current.append('files', file);
		const uploadKey = 'upload';
		message.loading({
			key: uploadKey,
			content: '正在上传',
			duration: 0,
		});

		const fileLen = formData.current.getAll('files').length;
		if (fileList.length !== fileLen) {
			return;
		}

		fileUpload(formData.current)
			.then((res) => {
				message.success({
					key: uploadKey,
					content: '上传成功',
					duration: 2,
				});
				props.onChange(props.multiple ? res : res[0]);
				setFileList([]);
				formData.current.delete('files');
			})
			.catch((err) => {
				console.log('🚀 ~ fileUpload ~ err:', err);
				message.error('上传失败');
				message.destroy(uploadKey);
			});
	};

	return (
		<div className="upload-file">
			<Upload
				fileList={fileList}
				showUploadList={false}
				customRequest={customRequest}
				beforeUpload={beforeUpload}
				multiple={props.multiple || false}
				accept={props.accept || '.jpg,.jpeg,.png,.gif'}
				onChange={(e) => {
					setFileList(e.fileList);
				}}
				maxCount={props.maxCount || undefined}
			>
				{props.children}
			</Upload>
			<CropperImage ref={cropperRef} {...props.cropperProps} promise={promise} src={cropperImg} />
		</div>
	);
});

// 图片上传
const Index = (props = {}) => {
	const { openNewTab } = useRouterLink();
	return (
		<Space className="width-100per" direction={props.direction || 'horizontal'} size={16}>
			<div
				className="position-relative flex align-center justify-center border-radius-4 overflow-hidden"
				style={{
					border: 'dashed 1px #165DFF',
					width: `${props.width || 188}px`,
					height: `${props.height || 336}px`,
					backgroundImage: `url(${props.value || ''})`,
					backgroundSize: 'contain',
					backgroundRepeat: 'no-repeat',
					backgroundPosition: 'center',
				}}
			>
				{props.value ? (
					<>
						<Image className="opacity-0" src={props.value} width={props.width || 188} height={props.height || 336} />
						<div className="position-absolute top-2 right-2 flex align-center justify-center padding-4 font-size-16 border-radius-36 bg-color-00000040 color-ffffff ">
							<DeleteOutlined className="cursor-pointer" onClick={() => props.onChange('')} />
						</div>
					</>
				) : (
					<CustomUpload size={props.size || 3} onChange={(url) => props.onChange(url)} cropperProps={props.cropperProps}>
						<div className="position-absolute inset-0 flex flex-direction-column align-center justify-center cursor-pointer">
							<img className="width-60 height-60" src={getImageSrc('@/assets/images/Public/fileup-icon.png')} />
							<div>上传图片</div>
						</div>
					</CustomUpload>
				)}
			</div>
			{!props.hideTip && (
				<div className="flex-sub flex flex-direction-column justify-center align-start line-height-20 font-size-12 color-86909c">
					<div>请上传大小不超过{props.size || 3}MB格式为jpg、 jpeg、png、gif的文件</div>
					<div>{props.tips}</div>
					<div
						className="a color-165dff"
						onClick={() => {
							openNewTab('https://tinypng.com');
						}}
					>
						图片压缩地址 可压缩图片 提升页面打开速度
					</div>
				</div>
			)}
		</Space>
	);
};

// 多图上传
const MultipleUpload = (props = {}) => {
	const { openNewTab } = useRouterLink();
	return (
		<Space className="width-100per" direction={props.direction || 'horizontal'} size={16}>
			<Space wrap size={16}>
				{props.value?.map((ov, oi) => (
					<div
						key={oi}
						className="position-relative flex align-center justify-center border-radius-4 overflow-hidden"
						style={{
							border: 'dashed 1px #165DFF',
							width: `${props.width || 188}px`,
							height: `${props.height || 336}px`,
							backgroundImage: `url(${ov || ''})`,
							backgroundSize: 'contain',
							backgroundRepeat: 'no-repeat',
							backgroundPosition: 'center',
						}}
					>
						<Image className="opacity-0" src={ov} width={props.width || 188} height={props.height || 336} />
						<div className="position-absolute top-2 right-2 flex align-center justify-center padding-4 font-size-16 border-radius-36 bg-color-00000040 color-ffffff ">
							<DeleteOutlined
								className="cursor-pointer"
								onClick={() => {
									props.onChange(props.value.filter((oov) => oov !== ov));
								}}
							/>
						</div>
					</div>
				))}
				{(props.maxCount === undefined || props.maxCount - (props.value?.length || 0) > 0) && (
					<div
						className="position-relative flex align-center justify-center border-radius-4 overflow-hidden"
						style={{
							border: 'dashed 1px #165DFF',
							width: `${props.width || 188}px`,
							height: `${props.height || 336}px`,
						}}
					>
						<CustomUpload
							size={props.size || 3}
							onChange={(url) => {
								props.onChange([...(props.value || []), ...url]);
							}}
							multiple
							maxCount={props.maxCount - (props.value?.length || 0)}
							cropperProps={props.cropperProps}
						>
							<div className="position-absolute inset-0 flex flex-direction-column align-center justify-center cursor-pointer">
								<img className="width-60 height-60" src={getImageSrc('@/assets/images/Public/fileup-icon.png')} />
								<div>上传图片</div>
							</div>
						</CustomUpload>
					</div>
				)}
			</Space>
			{!props.hideTip && (
				<div className="flex-sub flex flex-direction-column justify-center align-start line-height-20 font-size-12 color-86909c">
					<div>请上传大小不超过{props.size || 3}MB格式为jpg、 jpeg、png、gif的文件</div>
					<div>{props.tips}</div>
					<div
						className="a color-165dff"
						onClick={() => {
							openNewTab('https://tinypng.com');
						}}
					>
						图片压缩地址 可压缩图片 提升页面打开速度
					</div>
				</div>
			)}
		</Space>
	);
};

Index.MultipleUpload = MultipleUpload;
export default Index;
