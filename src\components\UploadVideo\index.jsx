import { useEffect, useState } from 'react';
import { Button, message, Space, Upload, Progress } from 'antd';
import { DeleteTwoTone } from '@ant-design/icons';

import { getOssClient } from '@/utils/common';
import dayjs from 'dayjs';

/**
 * 视频上传
 * @param {object} props
 * @param {object} props.size 限制大小 默认3G
 * @param {object} props.direction 横向 horizontal 竖向 vertical 默认horizontal
 * @param {object} props.tips 提示 false 不显示
 * @param {object} props.progress 进度条 不显示
 * @param {object} props.fileName 自定义上传名称
 * @returns
 */
export const Index = (props = {}) => {
	const [client, setClient] = useState(null);
	const [status, setStatus] = useState('');
	const [percent, setPercent] = useState(0);
	const size = props.size || 3;
	const partSize = 3 * 1024 * 1024; // 每个分片大小(byte)
	const parallel = 4; // 同时上传的分片数
	const MODENAME = import.meta.env.MODE == 'production' ? 'prod' : 'test';

	// 上传前校验
	const beforeUpload = (file) => {
		const limitSize = size * 1024 * 1024 * 1024; // 单位 Gb
		const isVideo = file.type === 'video/mp4' || file.type === 'video/mov' || file.type === 'video/avi';
		if (!isVideo) {
			message.error('支持上传的视频格式：mp4、 mov、 avi');
			return false;
		}

		if (file.size > limitSize) {
			message.error(`视频大小不超过${getSizeText(size)}`);
			return false;
		}
		return true;
	};

	// 自定义上传
	const customRequest = async ({ file }) => {
		console.log('🚀 ~ customRequest ~ file:', file);
		if (status === 'loading') {
			message.warning('上传中，请勿重复上传');
			return;
		}
		getOssClient().then((client) => {
			setClient(client);
			setStatus('loading');
			setPercent(0);
			const fileName = props.fileName ? `${props.fileName}.${file.name.split('.').pop()}` : `${file.name}`;
			const fileUpName = `${MODENAME}/video/${dayjs().format('YYYY-MM-DD')}/${fileName}`;
			client.current
				.multipartUpload(fileUpName, file, {
					parallel,
					partSize,
					progress: (progress) => {
						setPercent((progress * 100).toFixed(2));
					},
				})
				.then(() => {
					setStatus('done');
					props.onChange && props.onChange(`${client.ossUrl}${fileUpName}`, file);
					message.success('上传成功');
				})
				.catch((err) => {
					console.log(props.value);
					if (err.name === 'cancel') {
						setStatus('done');
						message.warning('取消上传');
					} else {
						setStatus('error');
						message.error('上传失败');
					}
				});
		});
	};

	// 取消上传
	const cancelFileup = () => {
		client.current.abortMultipartUpload();
	};

	// 状态
	useEffect(() => {
		props.onChangeStatus && props.onChangeStatus(status);
	}, [status]);
	useEffect(() => {
		console.log('percent:', typeof percent, percent);
		props.onPercent && props.onPercent(percent);
	}, [percent]);

	useEffect(() => {
		return () => {
			console.log('关闭了');
			client && client.current.abortMultipartUpload();
		};
	}, []);

	return (
		<Space direction="vertical" className="width-100per">
			<Space direction={props.direction || 'horizontal'}>
				<Upload
					accept=".mp4, .avi, .wmv, .rmvb, .mov, .mkv, .flv, .m4v, .webm, .ogg, .mpg, .mpeg, .3gp, .ts, .m3u8"
					customRequest={customRequest}
					beforeUpload={beforeUpload}
					fileList={[]}
				>
					{props.children || <Button type="primary">上传视频</Button>}
				</Upload>
				{props.tips !== false && (
					<div className="line-height-20 font-size-12 color-86909c">
						{props.tips || `支持上传mp4/mov/avi格式的文件，单个文件最大不超过${getSizeText(size)}`}
					</div>
				)}
			</Space>
			{props.progress !== false && status === 'loading' && (
				<div className="flex">
					<Progress className="flex-sub" percent={percent} type="line" />
					<div className="a margin-top-6 margin-left-8 font-size-12 color-165dff" onClick={cancelFileup}>
						取消
					</div>
				</div>
			)}
			{(status === '' || status === 'done') && props.value && (
				<div
					className="a flex align-center justify-between margin-replacetop-10 padding-4 bg-color-f2f3f5 hover-color-165dff"
					title={props.value}
				>
					<div className="world-break-all">
						{props.showTitle
							? `${props.showTitle}.${props.value.split('.').pop()}`
							: props.value.replace(`${client?.ossUrl}${MODENAME}/video/`, '')}
					</div>
					<DeleteTwoTone
						title="删除"
						className="margin-left-12 font-size-16 color-165dff a"
						onClick={() => {
							props.onChange && props.onChange('');
						}}
					/>
				</div>
			)}
		</Space>
	);
};

// 获取大小文字输出
// 大于 1G 输出G 小于 输出 M
// size 单位为G
function getSizeText(size) {
	return size > 1 ? `${size}G` : `${size * 1024}M`;
}

export default Index;
