import { useEffect, useRef, useState } from 'react';
import { message } from 'antd';
import { verifyCodeGet } from '@/api/login';

const VerifyCode = ({ getMobile = () => '', seconds = 60 }) => {
	const [countdown, setCountdown] = useState(0); // 倒计时秒数
	const [isDisabled, setIsDisabled] = useState(false); // 控制按钮是否禁用
	const countdownTimer = useRef(null);

	// 获取验证码 并 开启倒计时
	const getCode = () => {
		const mobile = getMobile() || '';
		if (isDisabled || mobile === '') return;

		// 校验手机号码格式
		if (!/^(1[3-9]\d{9})$/.test(mobile)) {
			return message.error('请输入正确格式手机号码');
		}

		verifyCodeGet({
			verifyCodeType: 'VERIFY_CODE_TYPE_SMS',
			sceneType: 'SCENE_TYPE_UPDATEPWD',
			mobile,
		}).then(() => {
			setIsDisabled(true);
			setCountdown(seconds);
			message.success('发送成功');
		});
	};

	// 倒计时
	const startCountDown = () => {
		countdownTimer.current = setInterval(() => {
			if (countdown > 0) {
				setCountdown((countdown) => countdown - 1);
			} else if (countdown <= 0) {
				endCountDown();
			}
		}, 1000);
	};

	// 结束倒计时 清除定时器
	const endCountDown = () => {
		setIsDisabled(false);
		countdownTimer.current && clearInterval(countdownTimer.current);
	};

	useEffect(() => {
		if (countdown === seconds) {
			startCountDown();
		}
	}, [countdown]);

	// 开始离开 清空定时器
	useEffect(() => {
		endCountDown();
		return () => endCountDown();
	}, []);

	return (
		<div
			className='a color-86909c font-size-12 margin-right-8'
			onClick={getCode}
		>
			{countdown ? `${countdown}秒后获取` : '获取验证码'}
		</div>
	);
};
export default VerifyCode;
