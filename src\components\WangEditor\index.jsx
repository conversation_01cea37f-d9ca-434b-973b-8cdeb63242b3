/**
 * @description index.jsx - 文件描述
 * <AUTHOR>
 *
 * Created on 2024/10/9 15:54
 */
import React, { useEffect, useState } from 'react';
import '@wangeditor/editor/dist/css/style.css'; // 引入 css
import { Editor, Toolbar } from '@wangeditor/editor-for-react';
import store from '@/store';
// import { IDomEditor, IEditorConfig, IToolbarConfig } from '@wangeditor/editor'

const WangEditor = (props) => {
	const [editor, setEditor] = useState(null);
	// 编辑器内容
	const [html, setHtml] = useState('');

	// 工具栏配置
	const toolbarConfig = {};

	// 编辑器配置
	const editorConfig = {
		placeholder: '请输入内容...',
		MENU_CONF: {
			uploadImage: {
				server: '/api/system/file/upload',
				fieldName: 'files',
				headers: {
					session_token: store.getState().user.token,
				},
				allowedFileTypes: ['image/*'],
				customInsert: (urlList, insertFn) => {
					console.log(urlList);
					insertFn(urlList[0]);
				},
			},
			uploadVideo: {
				server: '/api/system/file/upload',
				fieldName: 'files',
				headers: {
					session_token: store.getState().user.token,
				},
				// 单个文件的最大体积限制，默认为 20M
				maxFileSize: 20 * 1024 * 1024, // 5M
				allowedFileTypes: ['video/*'],
				customInsert: (urlList, insertFn) => {
					console.log(urlList);
					insertFn(urlList[0]);
				},
			},
		},
	};

	// 及时销毁 editor ，重要！
	useEffect(() => {
		return () => {
			if (editor == null) return;
			editor.destroy();
			setEditor(null);
		};
	}, [editor]);

	useEffect(() => {
		if (editor == null) return;
		console.log('editor', props.value);
		editor.setHtml(props.value);
	}, [props.value]);

	/* 修改数据 */
	const handleChange = (editor) => {
		const value = editor.getHtml();
		setHtml(value);
		props.onChange(value);
	};

	return (
		<>
			<div style={{ border: '1px solid #ccc', zIndex: 100 }}>
				<Toolbar editor={editor} defaultConfig={toolbarConfig} mode="default" style={{ borderBottom: '1px solid #ccc' }} />
				<Editor
					defaultConfig={editorConfig}
					value={html}
					onCreated={setEditor}
					onChange={handleChange}
					mode="default"
					style={{ height: '300px', overflowY: 'hidden' }}
				/>
			</div>
		</>
	);
};
export default WangEditor;
