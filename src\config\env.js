export const mode = process.env.NODE_ENV || import.meta.env.MODE;
// # 项目名称 标题
export const title = import.meta.env.VITE_TITLE || '';
// # 登录地址
export const loginUrl =
	import.meta.env.VITE_BASE_PATH +
	(import.meta.env.VITE_BASE_PATH ? '/login'.slice(1) : '/login');

export const appOrigin = `${window.location.origin
	}${import.meta.env.VITE_BASE_PATH.replace(/\/$/, '')}`;

// 设置 页面 tab 的 标题
export function setDocumentTitle(newTitle) {
	document.title = `${newTitle || ''} - ${import.meta.env.VITE_TITLE}`;
}
// 设置 页面 tab 的 标题
export function metaTitle(newTitle) {
	const VITE_TITLE = import.meta.env.VITE_TITLE;
	return `${newTitle || ''}${VITE_TITLE ? ` - ${VITE_TITLE}` : ''}`;
}


export const qxbEnv = {
	'development': {
		tokenDomain: 'https://b-plugin.qixin.com',
		tenant: 'dg_dwq_test',
		account: 'sh_eitc_jyc' + '***********',
		key: 'CDEA571C9AC94E66',
		iv: '4453C43D46724004',
		srcDomain: 'https://jzzs-plugin-web.qixin.com',
	},
	'production': {
		tokenDomain: 'https://b-plugin.qixin.com',
		tenant: 'dg_dwq_test',
		account: 'sh_eitc_jyc' + '***********',
		key: 'CDEA571C9AC94E66',
		iv: '4453C43D46724004',
		srcDomain: 'https://jzzs-plugin-web.qixin.com',
	}
}[mode == 'development' ? 'development' : 'production']

export default {
	loginUrl: loginUrl,
};
