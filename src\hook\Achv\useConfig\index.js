import { useEffect, useState } from 'react';

import { getConfig } from '@/api/Achv/common';

export function useConfig() {
	// 总配置
	const [aboutUsConfig, setAboutUsConfig] = useState({});
	const [collaborativeContentConfig, setCollaborativeContentConfig] = useState({});
	const [dataDisplayConfig, setDataDisplayConfig] = useState({});
	const [mpConfig, setMpConfig] = useState({});
	const [webPageConfig, setWebPageConfig] = useState({});

	useEffect(() => {
		// 读取配置
		getConfig().then((res) => {
			const { aboutUsConfig, collaborativeContentConfig, dataDisplayConfig, mpConfig, webPageConfig } = res.data || {};
			try {
				setAboutUsConfig(aboutUsConfig);
				setCollaborativeContentConfig(JSON.parse(collaborativeContentConfig));
				setDataDisplayConfig(JSON.parse(dataDisplayConfig));
				setMpConfig(JSON.parse(mpConfig));
				setWebPageConfig(JSON.parse(webPageConfig));
			} catch (error) {
				console.log(error);
			}
		});
	}, []);

	return {
		aboutUsConfig,
		collaborativeContentConfig,
		dataDisplayConfig,
		mpConfig,
		webPageConfig,
	};
}
