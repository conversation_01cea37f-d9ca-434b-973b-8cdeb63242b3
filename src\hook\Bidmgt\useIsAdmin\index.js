import { useRef ,useEffect } from 'react';
import { useSelector } from 'react-redux';

import {
	getInvestmentResponsibleDept,
  } from "@/utils/bigmt";

export function useIsAdmin() {
	// 是否管理员
	let isAdmin = false;
	// 是否招商单位
	let isInvestment = useRef(false);

	// 用户信息
	const userInfo = useSelector((state) => {
		return state.user.userInfo;
	});

	// 部门列表
	const deptList = userInfo.deptList || [];

	// 是否区商务局管理员
	const findData = (userInfo.deptList || []).find(
		(ov) => ov.deptCode === 'DistrictCommerceBureau' || ov.name === 'admin'
	);

	useEffect(() => {
		const ids = deptList.map(ov => ov.id)
		getInvestmentResponsibleDept().then(list => {
			isInvestment.current = list.map(ov => ov.value).some(ov => ids.includes(ov))
		})
	}, [])

	if (findData) {
		isAdmin = true;
	}
	return {
		isAdmin,
		isInvestment,
		deptId: deptList[0]?.id || '',
		deptIds: deptList.map((ov) => ov.id),
		deptList: deptList.map((ov) => {
			return {
				label: ov.name,
				value: ov.id || '',
			};
		}),
	};
}
