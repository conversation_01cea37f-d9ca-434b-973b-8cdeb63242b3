import { useSelector, useDispatch } from 'react-redux';
import { menuHide, menuShow } from '@/store/menuSlice';
export function stateMenu() {
	const dispatch = useDispatch();
	const menuClass = useSelector((state) => {
		return state.menu.menuClass;
	});
	const dispatchMenuHide = () => {
		dispatch(menuHide());
	};
	const dispatchMenuShow = () => {
		dispatch(menuShow());
	};
	return { menuClass, dispatchMenuHide, dispatchMenuShow };
}
