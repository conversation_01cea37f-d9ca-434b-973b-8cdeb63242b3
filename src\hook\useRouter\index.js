import { useNavigate, useLocation, useSearchParams } from 'react-router-dom';
import { useSelector } from 'react-redux';

export function useRouterLink() {
	const navigate = useNavigate();
	const location = useLocation();
	const [searchParams, setSearchParams] = useSearchParams();

	// 登录凭证
	const token = useSelector((state) => {
		return state.user.token;
	});

	const linkTo = (url, option = {}) => {
		navigate(url, { ...option });
	};
	const checkLoginLinkTo = (url, option = {}) => {
		if (token) {
			navigate(url, { ...option });
		} else {
			navigate('/login', { replace: true });
		}
	};

	const openNewTab = (url = '', outWeb = false, downloadFileName = '') => {
		const a = document.createElement('a');
		a.target = '_blank';
		if (downloadFileName) {
			a.download = downloadFileName;
		}
		if (outWeb) {
			a.href = url.indexOf('http') > -1 ? url : `http://${url}`;
		} else {
			a.href = url.indexOf('http') > -1 ? url : import.meta.env.VITE_BASE_PATH + (import.meta.env.VITE_BASE_PATH ? url.slice(1) : url);
		}
		document.body.appendChild(a);
		a.click();
		a.remove();
	};

	return { linkTo, checkLoginLinkTo, openNewTab, location, searchParams, setSearchParams };
}
