import { useEffect, useState, useRef } from 'react';
import { Form, InputNumber, message } from 'antd';
import { useRouterLink } from '@/hook/useRouter';

import { download } from '@/utils/common';
import dayjs from 'dayjs';

import ModalForm from '@/components/ModalForm';
import ImportForm from '@/components/ImportForm';

export function useTableData(options = {}) {
	const ModalFormImportRef = useRef();
	const {
		params = {},
		getTablePageData,
		delTableItemData,
		exportTableData,
		importTableData,
		batchUpdateSort,
		pageCallback = () => {},
		getPageResult = (e) => Promise.resolve(e),
		closeFilter = false, // 关闭筛选
		disabledReplace = false, // 禁止改变路由参数
		firstActiveLoad = false, // 忽略第一次请求
	} = options;
	const { searchParams, setSearchParams } = useRouterLink();
	const [form] = Form.useForm();

	const [dataSource, setDataSource] = useState([]);

	// 是否第一次请求
	const [isFirstLoad, setIsFirstLoad] = useState(false);

	// 页码数据
	const [pagination, setPagination] = useState({
		total: 0,
		current: searchParams.get('pageNum') - 0 || 1,
		pageSize: searchParams.get('pageSize') - 0 || 10,
		showTotal: (total) => `共 ${total} 条`,
		showSizeChanger: true,
	});

	// 页码页数改变
	const changePage = ({ current, pageSize }) => {
		pagination.current = current;
		pagination.pageSize = pageSize;
		setPagination({ ...pagination });
	};

	// 获取表格数据
	const getTableData = () => {
		if (!getTablePageData) {
			return;
		}
		const queryParams = (!closeFilter && form && form.getFieldsValue && form.getFieldsValue()) || {};
		const paramsData = {
			...queryParams,
			pageNum: pagination.current,
			pageSize: pagination.pageSize,
			...params,
		};
		getTablePageData(paramsData).then((res) => {
			getPageResult(res.data || {}).then((resData) => {
				pagination.total = resData.total - 0;
				setDataSource(resData.records || []);
				setPagination({ ...pagination });
				setIsFirstLoad(true);

				if (disabledReplace) {
					return;
				}

				for (let i in paramsData) {
					if (paramsData[i] === undefined) {
						delete paramsData[i];
					}
					if (typeof paramsData[i] === 'object') {
						paramsData[i] = JSON.stringify(paramsData[i]);
					}
				}
				setSearchParams(paramsData, { replace: true });
			});
		});

		pageCallback(paramsData);
	};

	// 删除表格数据
	const delTableData = (id, params = {}) => {
		if (!delTableItemData) {
			return;
		}
		const ids = Array.isArray(id) ? id : [id];
		delTableItemData({
			ids,
			...params,
		}).then(() => {
			const { current, pageSize, total } = pagination;
			if (current > 1 && current > Math.ceil((total - ids.length) / pageSize)) {
				pagination.current = current - 1;
				setPagination({ ...pagination });
			} else {
				getTableData();
			}
			message.success('删除成功');
			pageCallback();
		});
	};

	// 导出表格数据
	const exportData = (exportParams = {}) => {
		if (!exportTableData) {
			return;
		}
		const queryParams = (!closeFilter && form && form.getFieldsValue && form.getFieldsValue()) || {};
		const paramsData = {
			...queryParams,
			...params,
		};
		exportTableData(Object.keys(exportParams).length === 0 || exportParams.type ? paramsData : exportParams).then((res) => {
			download.excel(res, `导出文件-${dayjs().format('YYYYMMDD_HH:mm')}`);
		});
	};

	// 导入表格数据
	const importData = (title) => {
		if (ModalFormImportRef.current) {
			ModalFormImportRef.current.setOpen(true);
			ModalFormImportRef.current.setTitle(typeof title === 'string' ? title : '表格导入');
		}
	};

	// 导入弹窗
	const ImportModal = (props = {}) => {
		return (
			<ModalForm
				ref={ModalFormImportRef}
				modelConfig={{
					styles: {
						body: {
							minHeight: 'unset',
						},
					},
				}}
				onOk={(formData) => {
					if (!formData || !importTableData) {
						return;
					}

					importTableData(formData).then(() => {
						message.success('导入成功');
						onSearch();
					});
				}}
				FormComp={({ FormCompRef }) => (
					<ImportForm ref={FormCompRef} fileName="file" tplUrl={props.tplUrl} tplName={props.tplName} customParams={props.customParams} />
				)}
			/>
		);
	};

	// 更新排序
	const onUpdateSort = (params) => {
		if (!batchUpdateSort || !params) {
			return;
		}
		let data = {};

		if (params.length > 0) {
			data.saveDtoList = params;
		} else {
			data = { ...params };
		}

		batchUpdateSort(data).then(() => {
			message.success('排序已更改');
			getTableData();
		});
	};

	// 更新排序输入框
	const SortInput = (props = {}) => {
		const record = props.record;
		const rankingKey = props.rankingKey || 'rankingNum';
		const rankingNum = record[rankingKey];
		return (
			<InputNumber
				className="text-align-center"
				defaultValue={rankingNum}
				min={1}
				precision={0}
				controls={false}
				onBlur={(e) => {
					const curRankingNum = e.target.value - 0 || null;
					if (curRankingNum !== rankingNum) {
						const params = { id: record.id, rankingNum: curRankingNum };
						params[rankingKey] = curRankingNum;
						console.log(props.isSingle);

						onUpdateSort(props.isSingle ? params : [params]);
					}
				}}
				placeholder="请输入"
			></InputNumber>
		);
	};

	// 搜索
	const onSearch = () => {
		if (pagination.current === 1) {
			getTableData();
		} else {
			pagination.current = 1;
			setPagination({ ...pagination });
		}
	};

	// 重置
	const onReset = (fields = {}) => {
		form && form.resetFields && form.resetFields();
		onSearch();
	};

	useEffect(() => {
		if (isFirstLoad) {
			getTableData();
		} else {
			if (!firstActiveLoad) {
				// 第一次延迟执行 等待读取参数
				setTimeout(() => {
					getTableData();
				}, 500);
			}
		}
	}, [pagination.current, pagination.pageSize]);

	useEffect(() => {
		if (isFirstLoad) {
			if (pagination.current === 1) {
				getTableData();
			} else {
				setTimeout(() => {
					pagination.current = 1;
					setPagination({ ...pagination });
				}, 50);
			}
		}
	}, [JSON.stringify(params)]);

	useEffect(() => {
		if (!closeFilter) {
			form && form.setFieldsValue(arrayToObj(searchParams));
		}
	}, []);

	return {
		form,
		dataSource,
		setDataSource,
		pagination,
		changePage,
		getTableData,
		delTableData,
		exportData,
		onSearch,
		onReset,
		onUpdateSort,
		// 导入
		importData,
		ImportModal,
		SortInput,
	};
}

const arrayToObj = (searchParams) => {
	const obj = {};
	searchParams.keys().forEach &&
		searchParams.keys().forEach((ov) => {
			try {
				obj[ov] = JSON.parse(searchParams.get(ov));
			} catch (error) {
				obj[ov] = searchParams.get(ov);
			}
		});

	return obj;
};
