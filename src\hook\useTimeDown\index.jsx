import { useState, useEffect, useRef } from 'react';
/**
 * 获取剩余时间
 */
//millisLong 从提交时间到现在的毫秒数
export default function useTimeDown(millisLong) {
	const getLeftTime = (millisLong) => {
		let leftTime = 0;
		if (millisLong && millisLong >= 0) {
			const thirtyMinute = 30 * 60 * 1000;
			leftTime = thirtyMinute > millisLong ? parseInt((thirtyMinute - millisLong) / 1000) : 0;
		}
		return leftTime;
	};
	const leftTime = getLeftTime(millisLong);
	const [timeDown, setCountDown] = useState(leftTime);
	const timerRef = useRef();
	useEffect(() => {
		if (timeDown) {
			timerRef.current = setInterval(() => {
				setCountDown((c) => c - 1);
			}, 1000);
		}
		//清除副作用
		return () => {
			clearInterval(timerRef.current);
		};
	}, [timeDown]);

	const setTimeDown = (millisLong) => {
		setCountDown(getLeftTime(millisLong));
	};
	return [timeDown, setTimeDown];
}
