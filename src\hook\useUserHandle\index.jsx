import { useSelector, useDispatch } from "react-redux";
import { Modal } from "antd";

import { setSwitchSystemOpen } from "@/store/userSlice";

import { logout } from "@/utils/common";

export function useUserHandle() {
    const dispatch = useDispatch();

    // 系统列表
    const routerList = useSelector((state) => {
        return state.user.routerList || [];
    }).filter((item) => item.perms && item.perms !== "system");

    // 用户信息
    const userInfo = useSelector((state) => {
        return state.user.userInfo;
    });

    // 当前选中系统信息
    const curSystemInfo = useSelector((state) => {
        return state.user.curSystemInfo;
    });

    // 退出登录
    const loginOut = () => {
        Modal.confirm({
            title: "提示",
            content: "确定退出登录？",
            okText: "确定",
            cancelText: "取消",
            onOk() {
                logout().then(() => {
                    window.location.href =
                        import.meta.env.VITE_BASE_PATH +
                        (import.meta.env.VITE_BASE_PATH
                            ? "/login".slice(1)
                            : "/login");
                });
            },
        });
    };

    // 切换系统
    const switchSystem = () => {
        dispatch(setSwitchSystemOpen(true));
    };

    return {
        userInfo,
        curSystemInfo,
        loginOut,
        switchSystem,
        routerList,
    };
}
