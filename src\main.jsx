import React from 'react';
import ReactDOM from 'react-dom/client';
import { ConfigProvider } from 'antd';
import { Provider } from 'react-redux';
import store from '@/store';
import App from '@/App';

import zhCN from 'antd/locale/zh_CN';
import dayjs from 'dayjs';
import 'dayjs/locale/zh-cn';
import 'reset-css';
import './main.scss';

import { toggleFullScreen } from '@/utils/common';
console.log('test change');

dayjs.locale('zh-cn');
// import RScaleScreen from 'r-scale-screen'

// 监听 F11 按键
document.addEventListener('keydown', (e) => {
	if (e && (e.code == 'F11' || e.keyCode == 122)) {
		//捕捉F11键盘动作
		e.preventDefault(); //阻止F11默认动作
		toggleFullScreen();
	}
});

const initReactDOM = () => {
	ReactDOM.createRoot(document.getElementById('root')).render(
		<ConfigProvider
			locale={zhCN}
			theme={{
				components: {
					Menu: {
						itemSelectedBg: '#ffffff',
						subMenuItemBg: 'transparent',
						iconSize: 18,
					},
				},
			}}
		>
			<Provider store={store}>
				<App />
			</Provider>
		</ConfigProvider>
	);
};
initReactDOM();
