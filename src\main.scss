body {
	font-size: 14px;
	background: #f7f8fa;
}

.flex {
	display: flex;
}

.inline-flex {
	display: inline-flex;
}

.flex-sub {
	flex: 1;
}

.justify-start {
	justify-content: flex-start;
}

.justify-center {
	justify-content: center;
}

.justify-between {
	justify-content: space-between;
}

.justify-around {
	justify-content: space-around;
}

.justify-end {
	justify-content: flex-end;
}

.align-start {
	align-items: flex-start;
}

.align-center {
	align-items: center;
}

.align-end {
	align-items: flex-end;
}

.align-stretch {
	align-items: stretch;
}

.align-content-start {
	align-content: flex-start;
}

.flex-direction-column {
	flex-direction: column;
}

.flex-direction-row {
	flex-direction: row;
}

.flex-direction-column-reverse {
	flex-direction: column-reverse;
}

.flex-direction-row-reverse {
	flex-direction: row-reverse;
}

.flex-wrap {
	flex-wrap: wrap;
}

.flex-nowrap {
	flex-wrap: nowrap;
}

.flex-shrink {
	flex-shrink: 0;
}

.grid {
	display: grid;
}

/* text-align start */
.text-align-left {
	text-align: left;
}

.text-align-center {
	text-align: center;
}

.text-align-right {
	text-align: right;
}

.text-align-justify {
	text-align: justify;
	text-align-last: justify;
}

.vertical-align-middle {
	vertical-align: middle;
}

.vertical-align-top {
	vertical-align: top;
}

/* text-align end */
.font-bold {
	font-weight: bolder;
}

.hover-font-bold:hover {
	font-weight: bolder;
}

.display-none {
	display: none;
}

.display-block {
	display: block;
}

.display-inline-block {
	display: inline-block;
}

.display-inline {
	display: inline;
}

a,
a:link,
a:visited,
a:hover,
a:active {
	cursor: pointer;
	text-decoration: none;
}

.a {
	cursor: pointer;
	user-select: none;
}

.user-select-none {
	user-select: none;
}

.position-fixed {
	position: fixed;
}

.position-relative {
	position: relative;
}

.position-absolute {
	position: absolute;
}

.position-sticky {
	position: sticky;
}

.border-box {
	box-sizing: border-box;
}

.content-box {
	box-sizing: content-box;
}

.overflow-visible {
	overflow: visible;
}

.overflow-hidden {
	overflow: hidden;
}

.overflowY-scroll {
	overflow-y: scroll;
}

.overflowY-auto {
	overflow-y: auto;
}

.overflowY-hidden {
	overflow-y: hidden;
}

.overflowX-scroll {
	overflow-x: scroll;
}

.overflowX-auto {
	overflow-x: auto;
}

.overflowX-hidden {
	overflow-x: hidden;
}

.world-break-all {
	word-break: break-all;
}

.nowrap {
	white-space: nowrap;
}

.pre-line {
	white-space: pre-line;
}

.pre-wrap {
	white-space: pre-wrap;
}

// 一行 溢出隐藏
.ellipsis {
	overflow: hidden;
	text-overflow: ellipsis;
	white-space: nowrap;
}

.margin-auto {
	margin: auto;
}

.margin-0-auto {
	margin: 0 auto;
}

.border-0 {
	border: 0 !important;
}

.border-0:focus {
	outline: none !important;
	border: 0 !important;
}

.outline-none {
	outline: none !important;
	outline-style: none !important;
}

.outline-none:focus {
	outline: none !important;
	outline-style: none !important;
}

// 平滑滚动
.scroll-smooth {
	scroll-behavior: smooth;
}

.box-shadow {
	box-shadow: rgba(0, 0, 0, 0.15) 0px 2px 4px 2px;
}

.box-shadow-30 {
	box-shadow: 0px 2px 30px 0px rgba(0, 0, 0, 0.15);
}

.text-cut {
	text-overflow: -o-ellipsis-lastline;
	overflow: hidden;
	text-overflow: ellipsis;
	display: -webkit-box;
	-webkit-line-clamp: 1;
	-webkit-box-orient: vertical;
	word-break: break-all;
}

.text-cut-2 {
	text-overflow: -o-ellipsis-lastline;
	overflow: hidden;
	text-overflow: ellipsis;
	display: -webkit-box;
	-webkit-line-clamp: 2;
	-webkit-box-orient: vertical;
	word-break: break-all;
}

.text-cut-3 {
	text-overflow: -o-ellipsis-lastline;
	overflow: hidden;
	text-overflow: ellipsis;
	display: -webkit-box;
	-webkit-line-clamp: 3;
	-webkit-box-orient: vertical;
	word-break: break-all;
}

.transparent {
	background: transparent !important;
}

/* ==================
         模态窗口 开始
 ==================== */
.cu-modal {
	position: fixed;
	top: 0;
	right: 0;
	bottom: 0;
	left: 0;
	z-index: 1110;
	opacity: 0;
	outline: 0;
	text-align: center;
	-ms-transform: scale(1.185);
	transform: scale(1.185);
	backface-visibility: hidden;
	perspective: 2000px;
	background: rgba(0, 0, 0, 0.6);
	transition: all 0.3s ease-in-out 0s;
	pointer-events: none;
}

.cu-modal::before {
	content: '\200B';
	display: inline-block;
	height: 100%;
	vertical-align: middle;
}

.cu-modal.show {
	opacity: 1;
	transition-duration: 0.3s;
	-ms-transform: scale(1);
	transform: scale(1);
	overflow-x: hidden;
	overflow-y: auto;
	pointer-events: auto;
}

.cu-dialog {
	position: relative;
	display: inline-block;
	vertical-align: middle;
	margin-left: auto;
	margin-right: auto;
	width: 680px;
	max-width: 100%;
	// background-color: #f8f8f8;
	border-radius: 10px;
	overflow: hidden;
}

.cu-modal.bottom-modal::before {
	vertical-align: bottom;
}

.cu-modal.bottom-modal .cu-dialog {
	width: 100%;
	border-radius: 0;
}

.cu-modal.bottom-modal {
	margin-bottom: -1000px;
}

.cu-modal.bottom-modal.show {
	margin-bottom: 0;
}

.cu-modal.drawer-modal {
	transform: scale(1);
	display: flex;
}

.cu-modal.drawer-modal .cu-dialog {
	height: 100%;
	min-width: 200px;
	border-radius: 0;
	margin: initial;
	transition-duration: 0.3s;
}

.cu-modal.drawer-modal.justify-start .cu-dialog {
	transform: translateX(-100%);
}

.cu-modal.drawer-modal.justify-end .cu-dialog {
	transform: translateX(100%);
}

.cu-modal.drawer-modal.show .cu-dialog {
	transform: translateX(0%);
}

.cu-modal .cu-dialog > .cu-bar:first-child .action {
	min-width: 100px;
	margin-right: 0;
	min-height: 100px;
}

/* ==================
         模态窗口 结束
 ==================== */

@for $value from 1 through 6 {
	$hundredValue: $value * 100;
	$doubleValue: $value * 2;

	.font-weight-#{$hundredValue} {
		font-weight: ($hundredValue);
	}

	.hover-font-weight-#{$hundredValue}:hover {
		font-weight: ($hundredValue);
	}

	.z-index-#{$doubleValue} {
		z-index: $doubleValue;
	}

	.letter-spacing-#{$doubleValue} {
		letter-spacing: $doubleValue * 1px;
	}
}

@for $value from 0 through 50 {
	$doubleValue: $value * 2;

	.width-#{$doubleValue}vw {
		width: ($doubleValue * 1vw);
	}

	.width-#{$doubleValue}per {
		width: ($doubleValue * 1%);
	}

	.height-#{$doubleValue}vh {
		height: ($doubleValue * 1vh);
	}

	.height-#{$doubleValue}per {
		height: ($doubleValue * 1%);
	}

	.max-width-#{$doubleValue}vw {
		max-width: ($doubleValue * 1vw);
	}

	.max-height-#{$doubleValue}vh {
		max-height: ($doubleValue * 1vh);
	}

	.min-height-#{$doubleValue}vh {
		min-height: ($doubleValue * 1vh);
	}

	.opacity-#{$doubleValue} {
		opacity: calc($doubleValue/100);
	}

	/* border-radius start */

	.border-radius_#{$doubleValue} {
		border-radius: 1% * $doubleValue;
	}

	.border-radius-#{$doubleValue} {
		border-radius: $doubleValue * 1px;
	}

	.border-radius-t-#{$doubleValue} {
		border-top-left-radius: $doubleValue * 1px;
		border-top-right-radius: $doubleValue * 1px;
	}

	.border-radius-tl-#{$doubleValue} {
		border-top-left-radius: $doubleValue * 1px;
	}

	.border-radius-tr-#{$doubleValue} {
		border-top-right-radius: $doubleValue * 1px;
	}

	.border-radius-b-#{$doubleValue} {
		border-bottom-right-radius: $doubleValue * 1px;
		border-bottom-left-radius: $doubleValue * 1px;
	}

	.border-radius-br-#{$doubleValue} {
		border-bottom-right-radius: $doubleValue * 1px;
	}

	.border-radius-bl-#{$doubleValue} {
		border-bottom-left-radius: $doubleValue * 1px;
	}

	/* border-radius end */

	.top-#{$doubleValue} {
		top: $doubleValue * 1px;
	}

	.bottom-#{$doubleValue} {
		bottom: $doubleValue * 1px;
	}

	.left-#{$doubleValue} {
		left: $doubleValue * 1px;
	}

	.right-#{$doubleValue} {
		right: $doubleValue * 1px;
	}

	.top-#{$doubleValue}per {
		top: ($doubleValue * 1%);
	}

	.bottom-#{$doubleValue}per {
		bottom: ($doubleValue * 1%);
	}

	.left-#{$doubleValue}per {
		left: ($doubleValue * 1%);
	}

	.right-#{$doubleValue}per {
		right: ($doubleValue * 1%);
	}
	.margin-left-#{$doubleValue}per {
		margin-left: ($doubleValue * 1%);
	}
	.margin-right-#{$doubleValue}per {
		margin-right: ($doubleValue * 1%);
	}
	.margin-top-#{$doubleValue}per {
		margin-top: ($doubleValue * 1%);
	}
	.margin-bottom-#{$doubleValue}per {
		margin-bottom: ($doubleValue * 1%);
	}
	.padding-left-#{$doubleValue}per {
		padding-left: ($doubleValue * 1%);
	}
	.padding-right-#{$doubleValue}per {
		padding-right: ($doubleValue * 1%);
	}
	.padding-bottom-#{$doubleValue}per {
		padding-bottom: ($doubleValue * 1%);
	}
	.padding-top-#{$doubleValue}per {
		padding-top: ($doubleValue * 1%);
	}
}

@for $value from 5 through 50 {
	$doubleValue: $value * 2;

	/* font-size start */
	.font-size-#{$doubleValue} {
		font-size: $doubleValue * 1px;
	}

	/* font-size end */

	.line-height-#{$doubleValue} {
		line-height: ($doubleValue * 1px);
	}
}

@for $value from 0 through 200 {
	$doubleValue: $value * 2;

	.gap-#{$doubleValue} {
		gap: ($doubleValue * 1px);
	}
	.max-height-#{$doubleValue} {
		max-height: ($doubleValue * 1px);
	}

	.max-width-#{$doubleValue} {
		max-width: ($doubleValue * 1px);
	}

	.width-#{$doubleValue} {
		width: ($doubleValue * 1px);
	}

	.height-#{$doubleValue} {
		height: ($doubleValue * 1px);
	}

	.min-height-#{$doubleValue} {
		min-height: ($doubleValue * 1px);
	}

	.min-width-#{$doubleValue} {
		min-width: ($doubleValue * 1px);
	}

	/* padding start */
	.padding-#{$doubleValue} {
		padding: ($doubleValue * 1px);
	}

	.padding-left-#{$doubleValue} {
		padding-left: ($doubleValue * 1px);
	}

	.padding-right-#{$doubleValue} {
		padding-right: ($doubleValue * 1px);
	}

	.padding-top-#{$doubleValue} {
		padding-top: ($doubleValue * 1px);
	}

	.padding-bottom-#{$doubleValue} {
		padding-bottom: ($doubleValue * 1px);
	}

	.padding-tb-#{$doubleValue} {
		padding-top: ($doubleValue * 1px);
		padding-bottom: ($doubleValue * 1px);
	}

	.padding-lr-#{$doubleValue} {
		padding-left: ($doubleValue * 1px);
		padding-right: ($doubleValue * 1px);
	}

	/* padding end */

	/* margin start */
	.margin-#{$doubleValue} {
		margin: ($doubleValue * 1px);
	}

	.margin-left-#{$doubleValue} {
		margin-left: ($doubleValue * 1px);
	}

	.margin-right-#{$doubleValue} {
		margin-right: ($doubleValue * 1px);
	}

	.margin-top-#{$doubleValue} {
		margin-top: ($doubleValue * 1px);
	}

	.margin-bottom-#{$doubleValue} {
		margin-bottom: ($doubleValue * 1px);
	}

	.margin-tb-#{$doubleValue} {
		margin-top: ($doubleValue * 1px);
		margin-bottom: ($doubleValue * 1px);
	}

	.margin-lr-#{$doubleValue} {
		margin-left: ($doubleValue * 1px);
		margin-right: ($doubleValue * 1px);
	}

	/* margin end */
}
.background-no-repeat {
	background-repeat: no-repeat;
}
.background-position-top-center {
	background-position: center center;
}
.background-position-center-top {
	background-position: center top;
}
.background-position-center-center {
	background-position: center center;
}
.background-position-bottom-right {
	background-position: bottom right;
}
.background-size-100-100 {
	background-size: 100% 100%;
}
.width-auto {
	width: auto;
}

.width-0 {
	width: 0;
}

.width-468 {
	width: 468px;
}

.width-436 {
	width: 436px;
}

.width-448 {
	width: 448px;
}

.height-auto {
	height: auto;
}

.height-full-main {
	height: calc(100vh - 60px);
}

.full-page-width {
	margin: auto;
	max-width: 1080px;
	min-width: 900px;
}

$colors: (
	'000000': #000000,
	'ffffff': #ffffff,
	// 默认值
	'1d2129': #1d2129,
	// 常规
	'165dff': #165dff,
	'4e5969': #4e5969,
	'e8f3ff': #e8f3ff,
	'e5e6e8': #e5e6e8,
	'86909c': #86909c,
	'f6f9ff': #f6f9ff,
	'f4f7fb': #f4f7fb,
	'f2f3f5': #f2f3f5,
	'ff7d00': #ff7d00,
	'f8f8f8': #f8f8f8,
	'fff7e8': #fff7e8,
	'434b6d': #434b6d,
	'c9cdd4': #c9cdd4,
	'1577d8': #1577d8,
	'4080ff': #4080ff,
	'ebf2ff': #ebf2ff,
	'f76560': #f76560,
	'e6e6e6': #e6e6e6,
	'f53f3f': #f53f3f,
	'fdcdc5': #fdcdc5,
	'fdddc3': #fdddc3,
	'00b42a': #00b42a,
	'aff0b5': #aff0b5,
	'e8f7ff': #e8f7ff,
	'206ccf': #206ccf,

	'333333': #333333,
	'131313': #131313,
	'aaaaaa': #aaaaaa,
	'f2f2f2': #f2f2f2,
	'f7f7f7': #f7f7f7,
	'777777': #777777,
	'f5f5f5': #f5f5f5,
	'ff0000': #ff0000,
	'ff9535': #ff9535,
	'1c8dfe': #1c8dfe,
	'e8fffb': #e8fffb,
	'0aa5a8': #0aa5a8,
	'f6f8fa': #f6f8fa,
	'e5e6eb': #e5e6eb,
	'e8ffea': #e8ffea,
	'ceead1': #ceead1,
	'2e974a': #2e974a,
	'f8fbff': #f8fbff,
	'4c6cc4': #4c6cc4,
	'ffece8': #ffece8,
	'cd4c57': #cd4c57,
	'c7eee8': #c7eee8,
	'41adaf': #41adaf,
	'f4f8ff': #f4f8ff,
	'fff7f6': #fff7f6,
	'f7f8fa': #f7f8fa,
	'6aa1ff': #6aa1ff,
	'd25f00': #d25f00,
	'fff6e8': #fff6e8,
	'ffe4ba': #ffe4ba,
	'f8fafe': #f8fafe,
	'57a9fb': #57a9fb,
	'0987e4': #0987e4,
	'f77234': #f77234,
	'edf4ff': #edf4ff,
	'f7f9fc': #f7f9fc,
	'cbddff': #cbddff,
	'8d4eda': #8d4eda,
	'56fefe': #56fefe,
	'0feef0': #0feef0,
	'14aef3': #14aef3,
	'ffd07d': #ffd07d,
	'e3f4fc': #e3f4fc,
	'08a4ff': #08a4ff,
	'91fffd': #91fffd,
	'66ffff': #66ffff,
	'0041b7': #0041b7,
	'6aa1ff1a': rgba(106, 161, 255, 0.1),
	'165dff1a': rgba(22, 93, 255, 0.1),
	'0505050f': rgba(5, 5, 5, 0.06),
	'00000040': rgba(0, 0, 0, 0.25),
	'ffffff33': rgba(255, 255, 255, 0.2),
	'3491fa': #3491fa,
	'6e2e0c': #6e2e0c,
	'0c196e': #0c196e,
	'0c6e4d': #0c6e4d,
);
@each $key, $color in $colors {
	.bg-color-#{$key} {
		background-color: $color !important;
	}

	.hover-bg-color-#{$key}:hover {
		background-color: $color !important;
	}

	.color-#{$key} {
		color: $color !important;
	}

	.hover-color-#{$key}:hover {
		color: $color !important;
	}

	.hover:hover .hover-color-#{$key} {
		color: $color !important;
	}

	.border-#{$key} {
		box-shadow: 0px 0px 0px 1px $color;
	}

	.hover-border-#{$key}:hover {
		box-shadow: 0px 0px 0px 1px $color;
	}

	.border-bottom-#{$key} {
		box-shadow: 0px 2px 2px -1px $color;
	}

	.border-top-#{$key} {
		box-shadow: 0px -2px 2px -1px $color;
	}

	.border-left-#{$key} {
		box-shadow: -2px 0px 2px -1px $color;
	}

	.border-right-#{$key} {
		box-shadow: 2px 0px 2px -1px $color;
	}

	.border-dashed-#{$key} {
		border: 1px dashed $color;
	}
	.border-solid-#{$key} {
		border: 1px solid $color;
	}

	.hover-border-solid-#{$key}:hover {
		border: 1px solid $color;
	}

	.border-solid-bottom-#{$key} {
		border-bottom: 1px solid $color;
	}

	.border-solid-top-#{$key} {
		border-top: 1px solid $color;
	}

	.border-solid-left-#{$key} {
		border-left: 1px solid $color;
	}

	.border-solid-right-#{$key} {
		border-right: 1px solid $color;
	}
}

.pic-filter-24-0-165dff {
	filter: drop-shadow(24px 0px #165dff);
}

.scrollbar::-webkit-scrollbar {
	/*滚动条整体样式*/
	width: 6px;
	/*高宽分别对应横竖滚动条的尺寸*/
	height: 1px;
}

.scrollbar::-webkit-scrollbar-thumb {
	/*滚动条里面小方块*/
	border-radius: 10px;
	box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.2);
	background: #ccc;
	// #535353;
}

.scrollbar::-webkit-scrollbar-track {
	/*滚动条里面轨道*/
	box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.2);
	border-radius: 10px;
	background: rgb(241, 242, 246);
	// background: #ededed;
}

*::-webkit-scrollbar {
	/*滚动条整体样式*/
	width: 6px;
	/*高宽分别对应横竖滚动条的尺寸*/
	height: 6px;
}

*::-webkit-scrollbar-thumb {
	/*滚动条里面小方块*/
	border-radius: 10px;
	box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.2);
	background: #ccc;
	// #535353;
}

*::-webkit-scrollbar-track {
	/*滚动条里面轨道*/
	box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.2);
	border-radius: 10px;
	background: rgb(241, 242, 246);
	// background: #ededed;
}

.hide-scrollbar {
	-webkit-overflow-scrolling: touch;

	&::-webkit-scrollbar {
		width: 0 !important;
		height: 0 !important;
		border: 0 !important;
		background-clip: content-box;
		display: none;
		color: transparent;
	}
}

.bg-icon {
	background-position: right center;
	background-repeat: no-repeat;
	background-size: 100% 100%;
}

.bg-icon-size-width-auto {
	background-position: right center;
	background-repeat: no-repeat;
	background-size: auto 100%;
}

.ant-menu {
	border-inline-end: none !important;
}

.space-line {
	height: 1px;
	background: rgba(14, 66, 210, 0.1);
}

.reach-text {
	img {
		max-width: 100%;
	}
}

.ant-pagination .ant-pagination-item-active {
	background-color: #4080ff !important;
}

.ant-pagination .ant-pagination-item {
	background-color: #f7f8fa;
}

.ant-pagination .ant-pagination-item-active a {
	color: #ffffff !important;
}

.width-1 {
	width: 1px;
}

.width-580 {
	width: 580px;
}

.min-width-1000 {
	min-width: 1000px;
}

.width-1200 {
	width: 1200px;
}

.width-1200 {
	width: 1200px;
}

.min-width-1200 {
	min-width: 1200px;
}

.windows-width {
	max-width: 1440px;
}

.box-shadow-rgba00004 {
	box-shadow: 0px -5px 7px 0px rgba(0, 0, 0, 0.04);
}

.height-1 {
	height: 1px;
}

.height-444 {
	height: 444px;
}

.height-500 {
	height: 500px;
}

.height-532 {
	height: 532px;
}
.height-550 {
	height: 550px;
}
.height-560 {
	height: 560px;
}

.height-auto {
	height: auto !important;
}

.min-height-600 {
	min-height: 600px;
}

.height-620 {
	height: 620px;
}

.height-650 {
	height: 650px;
}

.rotate-180deg {
	transform: rotate(180deg);
}

.transform-x-100per {
	transform: translateX(-100%);
}
.transform-x-50per {
	transform: translateX(-50%);
}

.transform-y-100per {
	transform: translateY(-100%);
}
.transform-y-50per {
	transform: translateY(-50%);
}
.transform-y--4px {
	transform: translateY(4px);
}

.transform-xy-100per {
	transform: translate(-100%, -100%);
}
.transform-xy-50per {
	transform: translate(-50%, -50%);
}

.transition {
	transition: all 0.3s ease-in-out 0s;
}

.transition-03 {
	transition: all 0.3s;
}

.cursor-pointer {
	cursor: pointer;
}
.cursor-not-allowed {
	cursor: not-allowed;
}
.catch-hand-grab {
	cursor: grab;
}
.text-decoration-underline {
	text-decoration: underline;
}

.inset-0 {
	inset: 0;
}
.menu-fixed {
	left: 0;
}
.content-width {
	width: calc(100vw - 280px);
}

.multiple-check .ant-cascader-menu {
	max-height: 180px !important;
	height: auto !important;
}
.preview-seat-image {
	.ant-image-preview-mask {
		background-color: #fff !important;
	}
}
.form-filter {
	.ant-form-item {
		margin-inline-end: 0;
		margin: 0;
	}
}
/* 很小的设备（手机等，小于 600px） */
@media only screen and (max-width: 600px) {
}

/* 比较小的设备（竖屏的平板，屏幕较大的手机等, 大于 600px） */
@media only screen and (min-width: 600px) {
}

/* 中型大小设备（横屏的平板, 大于 768px） */
@media only screen and (min-width: 768px) {
}

/* 大型设备（电脑, 大于 992px） */
@media only screen and (min-width: 992px) {
}

/* 超大型设备（大尺寸电脑屏幕, 大于 1200px） */
@media only screen and (min-width: 1200px) {
}

/* 超大型设备（大尺寸电脑屏幕, 大于 1440px） */
@media only screen and (min-width: 1440px) {
	.menu-fixed {
		left: 50%;
		transform: translate(-720px, 0);
	}
	.content-width {
		width: 1160px;
	}
}

/* 超大型设备（大尺寸电脑屏幕, 大于 1600px） */
@media only screen and (min-width: 1600px) {
}

// layout Class
$headerHeight: 60px;

.min-content-height {
	min-height: calc(100vh - $headerHeight);
}
.max-content-height {
	max-height: calc(100vh - $headerHeight);
}

// 表格属性
th.ant-table-cell,
td.ant-table-cell {
	vertical-align: middle !important;
}

.ant-table-wrapper .ant-table {
	scrollbar-color: unset !important;
}

@function colorFn($color) {
	@return rgba($color, 0.1);
}

// $status defalut 默认 warning 警告 error 错误
@mixin btn-status($color, $lineHeight, $fontSize) {
	display: inline-flex;
	align-items: center;
	justify-content: center;
	padding: 0 8px;
	line-height: $lineHeight;
	border-radius: 4px;
	font-size: $fontSize;
	color: $color;
}

$btnType: (
	'primary': #3491fa,
	'success': #00b42a,
	'error': #f53f3f,
	'warning': #ff7d00,
	'default': #86909c,
	'700eb2': #700eb2,
	'f8bb35': #f8bb35,
	'0aa5a8': #f8bb35,
	'cd4c57': #cd4c57,
);

@each $type, $color in $btnType {
	.tag-status-#{$type} {
		@include btn-status($color, 22px, 14px);
		background-color: colorFn($color);
	}
	.tag-status-small-#{$type} {
		@include btn-status($color, 20px, 12px);
		background-color: colorFn($color);
	}
}

.custom-anchor-box {
	&::before {
		display: none;
	}
	.ant-anchor-ink {
		display: none;
	}
}

.anchor-header-tabBar-box {
	&::before {
		display: none;
	}
	.ant-anchor-ink {
		display: none;
	}
}

.antd-form-box {
	.select-box,
	.cascader-box,
	.input-box {
		max-width: 670px;
	}

	.input-number-box {
		width: 375px;
	}
	.ant-checkbox-group-item,
	.ant-radio-wrapper-in-form-item {
		line-height: 32px;
	}
}

#section1,
#section2,
#section3,
#section4,
#section5 {
	scroll-margin: 80px;
}

.rich-box {
	img {
		max-width: 100%;
	}
}
