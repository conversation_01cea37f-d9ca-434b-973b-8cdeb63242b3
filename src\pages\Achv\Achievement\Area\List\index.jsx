import { useEffect, useState } from 'react';
import { Table, Button, Popconfirm, Space, Form, Input, Cascader, Row, Col, Affix, message } from 'antd';
import { useTableData } from '@/hook/useTableData';
import { useRouterLink } from '@/hook/useRouter';
import { pageDemand as getTablePageData, delDemand as delTableItemData, exportDemand as exportTableData } from '@/api/Achv/Demand/DemandManage/index';
import { addRecommend, pageRecommend } from '@/api/Achv/DailyPush/index';
import { getThreeLevelData } from '@/api/common';

import { auditStatusData, auditStatusTextList, authTextList } from '@/pages/Achv/config';

const Index = () => {
	const { linkTo, searchParams } = useRouterLink();

	const [auditStatus, setAuditStatus] = useState(searchParams.get('auditStatus') - 0 || 3);
	const { form, dataSource, pagination, changePage, delTableData, exportData, onSearch, onReset } = useTableData({
		params: { auditStatus },
		getTablePageData,
		delTableItemData,
		exportTableData,
	});

	// 获取选项数据
	const [areaOptions, setAreaOptions] = useState([]);
	const getOptionsData = () => {
		getThreeLevelData({ level: 2 }).then((res) => {
			setAreaOptions(res.data || []);
		});
	};

	// 获取推荐列表
	const [recommendList, setRecommendList] = useState([]);

	const getRecommendList = () => {
		pageRecommend({ pageSize: 100, pageNum: 1 }).then((res) => {
			setRecommendList((res?.data?.records || []).filter((ov) => ov.recommendType === 1).map((ov) => ov.sourceId));
		});
	};

	// 推荐
	const recommend = (id) => {
		addRecommend({
			recommendType: 1,
			sourceId: id,
		}).then(() => {
			message.success('推荐成功');
			getRecommendList();
		});
	};

	useEffect(() => {
		getOptionsData();
		getRecommendList();
	}, []);

	return (
		<div className="">
			<div className="flex flex-shrink align-center margin-bottom-4 height-48 font-size-22 font-weight-500 color-1d2129">需求管理</div>
			{/* Tabs & 功能按钮 开始 */}
			<Affix offsetTop={0}>
				<div className="flex align-center justify-between padding-24 border-radius-8 bg-color-ffffff border-solid-bottom-f2f3f5">
					<div className="flex align-center">
						{auditStatusData.map((ov) => (
							<div
								key={ov.value}
								className={`a margin-right-40 font-size-18 font-weight-500 ${auditStatus === ov.value ? 'color-165dff' : ''}`}
								onClick={() => setAuditStatus(ov.value)}
							>
								{ov.label}
							</div>
						))}
					</div>
					<Space size={16}>
						<Button onClick={exportData}>批量导出</Button>
						<Button
							type="primary"
							onClick={() => {
								linkTo(`/newAchv/demand/curd`);
							}}
						>
							新建需求
						</Button>
					</Space>
				</div>
			</Affix>
			{/* Tabs & 功能按钮 结束 */}
			<div className="flex-sub margin-top-18 padding-24 border-radius-8 bg-color-ffffff">
				{/* 筛选条件 开始 */}
				<div className="flex  justify-between margin-bottom-18">
					<div className="flex-sub">
						<Form
							form={form}
							labelAlign="right"
							layout="inline"
							initialValues={{
								provinceCodes: undefined,
								cityCodes: undefined,
								tempArea: [],
							}}
						>
							<Form.Item hidden name="provinceCodes">
								<Input />
							</Form.Item>
							<Form.Item hidden name="cityCodes">
								<Input />
							</Form.Item>
							<Row className="width-100per" gutter={[12, 12]}>
								<Col span={8}>
									<Form.Item name="keywords" label="需求名称">
										<Input placeholder="请输入需求名称" />
									</Form.Item>
								</Col>
								<Col span={8}>
									<Form.Item name="tempArea" label="区域">
										<Cascader
											options={areaOptions}
											placeholder="请选择区域"
											changeOnSelect
											displayRender={(label) => label.join('-')}
											onChange={(e = [undefined, undefined]) => {
												form.setFieldValue('provinceCodes', e[0] ? [e[0]] : undefined);
												form.setFieldValue('cityCodes', e[1] ? [e[1]] : undefined);
											}}
										/>
									</Form.Item>
								</Col>
							</Row>
						</Form>
					</div>
					<div className="margin-left-12 padding-left-12 border-solid-left-e5e6eb">
						<Space size={16}>
							<Button onClick={onReset}>重置</Button>
							<Button type="primary" onClick={onSearch}>
								查询
							</Button>
						</Space>
					</div>
				</div>
				{/* 筛选条件 结束 */}

				{/* 表格 开始 */}
				<Table rowKey="id" dataSource={dataSource} pagination={pagination} onChange={changePage} scroll={{ x: 'max-content' }}>
					<Table.Column
						title="序号"
						key="index"
						width={60}
						render={(_, __, index) => {
							return index + 1;
						}}
					/>

					<Table.Column title="需求名称" dataIndex="name" render={(text) => <div className="max-width-240">{text}</div>} />
					<Table.Column
						title="技术领域"
						dataIndex="areaCategoryListName"
						render={(text) => (text && text.length ? text.join('、') : '--')}
					/>
					<Table.Column
						title="发布状态"
						dataIndex="auditStatus"
						align="center"
						render={(text) => {
							return (
								<div className={`tag-status-small-${['default', 'warning', 'error', 'primary'][text]}  `}>
									{auditStatusTextList[text || ''] || '--'}
								</div>
							);
						}}
					/>
					<Table.Column
						title="认证状态"
						dataIndex="isAuth"
						align="center"
						render={(text) => {
							return (
								<div
									style={{
										color: text === 1 ? '#1890ff' : '',
									}}
								>
									{authTextList[text || 0]}
								</div>
							);
						}}
					/>
					<Table.Column
						title="负责人"
						dataIndex="responsiblePersonnelList"
						align="center"
						render={(text) => {
							if (text && Array.isArray(text)) {
								return text.map((ov) => ov.userName).join('、') || '--';
							} else {
								return text || '--';
							}
						}}
					/>
					<Table.Column
						title="揭榜状态"
						dataIndex="proclamationStatus"
						align="center"
						render={(text) => {
							return <div className={`proclamation-status-${text}`}>{['', '可揭榜', '揭榜中', '已揭榜'][text]}</div>;
						}}
					/>
					<Table.Column title="合作意向" dataIndex="cooperateNum" />
					<Table.Column
						title="所属区域"
						dataIndex="provinceCode"
						render={(_, record) => {
							return [record.provinceCodeName, record.cityCodeName, record.areaCodeName].filter((ov) => ov).join('-');
						}}
					/>
					<Table.Column
						title="操作"
						key="option"
						dataIndex="option"
						align="center"
						fixed="right"
						render={(_, record) => {
							return (
								<>
									<Button type="link" size="small" onClick={() => linkTo(`/newAchv/demand/detail?id=${record.id}`)}>
										编辑/审核
									</Button>
									<Popconfirm
										title="提示"
										description="确定删除吗？"
										onConfirm={() => {
											delTableData(record.id);
										}}
										okText="确定"
										cancelText="取消"
									>
										<Button type="link" size="small">
											删除
										</Button>
									</Popconfirm>
									<Button
										type="link"
										size="small"
										disabled={recommendList.includes(record.id)}
										onClick={() => recommend(record.id)}
									>
										推荐
									</Button>
								</>
							);
						}}
					/>
				</Table>
				{/* 表格 结束 */}
			</div>
		</div>
	);
};

export default Index;
