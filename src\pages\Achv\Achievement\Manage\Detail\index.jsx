import { useEffect, useState, useRef, forwardRef, useImperativeHandle } from 'react';
import { Table, Button, Input, Affix, Anchor, message, Modal, Form, Image, Space, Switch, Tag } from 'antd';
import { EditOutlined } from '@ant-design/icons';

import ModalForm from '@/components/ModalForm';
import Cooperate from '@/components/Achv/Cooperate';

import { getAchievementDetail as getTableItemData, auditAchievement } from '@/api/Achv/Achievement/Manage/index';
import { detailByUserId } from '@/api/Achv/Competition/Signup';
import { getOrderSupplierBySourceId } from '@/api/Achv/Demand/DockingManage/index';

import { releaseTypeTextList, releasePlatformTextList, competitionGroupTextList, dockingStageStatusDataTextList } from '@/pages/Achv/config';
import { useRouterLink } from '@/hook/useRouter';

import dayjs from 'dayjs';

const Index = (props = {}) => {
	const ModalFormRef = useRef();
	const { linkTo, openNewTab, searchParams } = useRouterLink();
	const id = searchParams.get('id') || '';
	const fromList = !!searchParams.get('fromList');
	const [detail, setDetail] = useState({});
	const [dataSource, setDataSource] = useState([]);

	// 是否可以编辑基本资料
	// 赛事成果详情入口 只能是赛事成果才能编辑
	const [isEditBase, setIsEditBase] = useState(true);

	// 跳转地址
	const linkToPath = props.linkToPath || `/newAchv/achievement/manage`;

	const getDetail = () => {
		getTableItemData({ id, isUpdate: 1 }).then((res) => {
			const resData = res.data || {};
			const { releaseType, createBy } = resData;

			setDetail(res.data || {});
			setIsEditBase(props.releaseType !== 3 || releaseType === 3);

			// 赛事成果请求报名信息 显示组别 姓名
			if (releaseType === 3 && createBy) {
				detailByUserId({ userId: createBy }).then((res) => {
					const { id: signUpId, name: signUpName = '', competitionGroup = '' } = res.data || {};
					setDetail({
						signUpId,
						signUpName,
						competitionGroup,
						...resData,
					});
				});
			}
		});

		getOrderSupplierBySourceId({ sourceId: id, sourceType: 2 }).then((res) => {
			setDataSource(res.data || []);
		});
	};

	// 通过审核
	const handlePass = () => {
		Modal.confirm({
			title: '成果审核',
			content: `是否通过【${detail.name}】的审核？`,
			onOk() {
				handleAudit({ status: 3 });
			},
		});
	};

	// 撤销审核
	const handleRevoke = () => {
		Modal.confirm({
			title: '撤销审核',
			content: `是否撤销【${detail.name}】的审核结果？`,
			onOk() {
				handleAudit({ status: 1 });
			},
		});
	};

	// 审核操作
	const handleAudit = (values) => {
		auditAchievement({ ...values, id }).then(() => {
			message.success('操作成功');

			setTimeout(() => {
				linkTo(-1);
			}, 500);
		});
	};

	useEffect(() => {
		getDetail();

		// 跳转到指定锚点
		const hash = window.location.hash.replace('#', '');
		if (hash) {
			setTimeout(() => {
				const element = document.getElementById(hash);
				if (element) {
					element.scrollIntoView({ behavior: 'smooth' });
				}
			}, 500);
		}
	}, []);

	return (
		<div>
			<div className="flex flex-shrink align-center margin-bottom-4 height-48 font-size-22 font-weight-500">
				<div className="a color-86909c" onClick={() => linkTo(fromList ? -1 : linkToPath)}>
					成果管理
				</div>
				<div className="margin-lr-10 color-86909c">/</div>
				<div className="color-1d2129">成果详情</div>
			</div>
			<div id="section1"></div>
			<Affix>
				<div className="height-66 flex align-center justify-between bg-color-ffffff border-radius-8 padding-lr-24 border-solid-bottom-f2f3f5">
					<div className="flex align-center justify-between color-1d2129 line-height-26 font-size-18 font-weight-500">
						<Anchor
							affix={false}
							rootClassName="anchor-header-tabBar-box"
							direction="horizontal"
							replace
							targetOffset={80}
							items={[
								{
									key: 'section1',
									href: '#section1',
									title: <div className="margin-right-40 font-size-16 font-weight-500">成果信息</div>,
								},
								{
									key: 'section2',
									href: '#section2',
									title: <div className="margin-right-40 font-size-16 font-weight-500">所属机构信息</div>,
								},
								{
									key: 'section3',
									href: '#section3',
									title: <div className="margin-right-40 font-size-16 font-weight-500">成果描述</div>,
								},
								{
									key: 'section4',
									href: '#section4',
									title: <div className="margin-right-40 font-size-16 font-weight-500">相关参与人</div>,
								},
								{
									key: 'section5',
									href: '#section5',
									title: <div className="margin-right-40 font-size-16 font-weight-500">合作方动态</div>,
								},
								// 赛事不显示 外部合作意向
								{
									key: 'section6',
									href: '#section6',
									title: <div className="margin-right-40 font-size-16 font-weight-500">外部合作意向</div>,
								},
							].filter(({ key }) => key !== 'section6' || props.releaseType !== 3)}
						/>
					</div>
					<div className="flex align-center justify-between color-1d2129 line-height-26 font-size-18 font-weight-500">
						{detail.auditStatus === 1 ? (
							<>
								<Button
									type="primary"
									danger
									ghost
									onClick={() => {
										ModalFormRef.current.setOpen(true);
										ModalFormRef.current.setTitle('不通过原因');
									}}
									className="height-40 width-104"
								>
									不通过
								</Button>
								<Button type="primary" onClick={handlePass} className="margin-left-20 height-40 width-104">
									通过审核
								</Button>
							</>
						) : (
							<Button onClick={handleRevoke} className="margin-left-20 height-40 width-104">
								撤销审核
							</Button>
						)}
					</div>
				</div>
			</Affix>

			<div className="bg-color-ffffff border-radius-8 padding-lr-24 padding-tb-20 margin-tb-16">
				<div className="flex align-center justify-between margin-bottom-20">
					<div className="font-size-18 line-height-26 font-weight-500">成果信息</div>
					<Button
						type="link"
						icon={<EditOutlined />}
						disabled={!isEditBase}
						title={!isEditBase ? '当前成果来源不是“参赛提交”请到成果管理进行编辑' : ''}
						onClick={() => {
							linkTo(`${linkToPath}/curd?id=${id}#section1`);
						}}
					>
						编辑信息
					</Button>
				</div>

				<div className="flex align-center justify-start font-size-14 line-height-22 padding-left-16 margin-bottom-20">
					<div className="color-86909c margin-right-4">审核状态：</div>
					<div className="">
						<div className={`tag-status-${['default', 'warning', 'error', 'primary'][detail.auditStatus]}`}>
							{['', '审核中', '审核不通过', '审核通过'][detail.auditStatus] || ''}
						</div>
					</div>
				</div>
				{detail.auditStatus === 2 && (
					<div className="flex align-center justify-start font-size-14 line-height-22 padding-left-16 margin-bottom-20">
						<div className="color-86909c margin-right-4">拒绝原因：</div>
						<div className="">{detail.reason || '无'}</div>
					</div>
				)}
				{detail.competitionGroup && (
					<div className="flex align-center justify-start font-size-14 line-height-22 padding-left-16 margin-bottom-20">
						<div className="color-86909c margin-right-4">参赛组别：</div>
						<div className="">
							<div className={`tag-status-${['', 'primary', 'warning', '700eb2'][detail.competitionGroup]}`}>
								{competitionGroupTextList[detail.competitionGroup] || '--'}
							</div>
						</div>
					</div>
				)}
				{detail.signUpName && (
					<div className="flex align-center justify-start font-size-14 line-height-22 padding-left-16 margin-bottom-20">
						<div className="color-86909c margin-right-4">参赛者姓名：</div>
						<div className="">
							<span
								className="a color-165dff"
								onClick={() => {
									linkTo(`/newAchv/competition/signup/detail?id=${detail.signUpId}`);
								}}
							>
								{detail.signUpName || '--'}
							</span>
						</div>
					</div>
				)}
				<div className="flex align-start justify-start font-size-14 line-height-22 padding-left-16 margin-bottom-20">
					<div className="color-86909c margin-right-4">成果名称：</div>
					<div className="">{detail.name || '--'}</div>
				</div>
				<div className="flex align-center justify-start font-size-14 line-height-22 padding-left-16 margin-bottom-20">
					<div className="color-86909c margin-right-4">成果状态：</div>
					<div className="">{['', '可揭榜', '揭榜中', '已揭榜'][detail.proclamationStatus]}</div>
				</div>
				<div className="flex align-start justify-start font-size-14 line-height-22 padding-left-16 margin-bottom-20">
					<div className="color-86909c margin-right-4">成果封面：</div>
					<div className="">{(detail.achvtImgUrl && <Image width={120} src={detail.achvtImgUrl} />) || null}</div>
				</div>

				{detail.achvtImgList && (
					<div className="flex align-start justify-start font-size-14 line-height-22 padding-left-16 margin-bottom-20">
						<div className="color-86909c margin-right-4">成果图片：</div>
						<div className="flex-sub">
							<Space wrap={true}>
								{(detail.achvtImgList || '').split(',').map((ov, oi) => {
									return <Image key={oi} width={120} src={ov} />;
								})}
							</Space>
						</div>
					</div>
				)}
				<div className="flex align-start justify-start font-size-14 line-height-22 padding-left-16 margin-bottom-20">
					<div className="color-86909c margin-right-4">所属领域：</div>
					<div className="">{(detail.areaCategoryListName || []).join('、') || '--'}</div>
				</div>
				<div className="flex align-start justify-start font-size-14 line-height-22 padding-left-16 margin-bottom-20">
					<div className="color-86909c margin-right-4">所处阶段：</div>
					<div className="">{detail.stageIdName || '--'}</div>
				</div>
				<div className="flex align-center justify-start font-size-14 line-height-22 padding-left-16 margin-bottom-20">
					<div className="color-86909c margin-right-4">转化方式：</div>
					<div className="">{(detail.transformListName || []).join('、') || '--'}</div>
				</div>
				<div className="flex align-center justify-start font-size-14 line-height-22 padding-left-16 margin-bottom-20">
					<div className="color-86909c margin-right-4">成果转化落地的期望时限：</div>
					<div className="">{['--', '半年内完成转化', '一年内完成转化', '不限'][detail.expirationType || 0]}</div>
				</div>

				{detail.releaseType === 2 && (
					<>
						<div className="flex align-center justify-start font-size-14 line-height-22 padding-left-16 margin-bottom-20">
							<div className="color-86909c margin-right-4">甄选成果：</div>
							<div className="">
								<Switch checked={detail.preferredStatus === 1} disabled />
							</div>
						</div>
						<div className="flex align-center justify-start font-size-14 line-height-22 padding-left-16 margin-bottom-20">
							<div className="color-86909c margin-right-4">甄选理由：</div>
							<div className="">{detail.preferredReason || '--'}</div>
						</div>
					</>
				)}

				<div className="flex align-center justify-start font-size-14 line-height-22 padding-left-16 margin-bottom-20">
					<div className="color-86909c margin-right-4">发布类型：</div>
					<div className="">
						<div className={`tag-status-${['', 'primary', 'warning', '700eb2', 'success', 'error'][detail.releaseType]}`}>
							{releaseTypeTextList[detail.releaseType] || '--'}
						</div>
					</div>
				</div>
				<div className="flex align-center justify-start font-size-14 line-height-22 padding-left-16 margin-bottom-20">
					<div className="color-86909c margin-right-4">发布平台：</div>
					<div className="">
						<div className={`tag-status-${['', 'primary', 'success', '700eb2'][detail.releasePlatform]}`}>
							{releasePlatformTextList[detail.releasePlatform] || '--'}
						</div>
					</div>
				</div>
				<div className="flex justify-start font-size-14 line-height-22 padding-left-16 margin-bottom-20">
					<div className="flex-shrink color-86909c margin-right-4">AI检索标签：</div>
					<div className="">
						<Space wrap={true} size={8}>
							{detail.tagDesc
								? detail.tagDesc
										.split('、')
										.filter((ov) => ov)
										.map((ov, oi) => {
											return (
												<Tag className="margin-right-0" key={oi} color="processing">
													{ov}
												</Tag>
											);
										})
								: '--'}
						</Space>
					</div>
				</div>
			</div>

			<div id="section2"></div>
			<div className="bg-color-ffffff border-radius-8 padding-lr-24 padding-tb-20 margin-tb-16">
				<div className="flex align-center justify-between margin-bottom-20">
					<div className="font-size-18 line-height-26 font-weight-500">所属机构信息</div>
					<Button
						type="link"
						icon={<EditOutlined />}
						disabled={!isEditBase}
						title={!isEditBase ? '当前成果来源不是“参赛提交”请到成果管理进行编辑' : ''}
						onClick={() => {
							linkTo(`${linkToPath}/curd?id=${id}#section1`);
						}}
					>
						编辑信息
					</Button>
				</div>

				<div className="flex align-center justify-start font-size-14 line-height-22 padding-left-16 margin-bottom-20">
					<div className="color-86909c margin-right-4">所属机构名称：</div>
					<div className="">{detail.orgName || ''}</div>
				</div>
				<div className="flex align-center justify-start font-size-14 line-height-22 padding-left-16 margin-bottom-20">
					<div className="color-86909c margin-right-4">所属区域：</div>
					<div className="">{[detail.provinceCodeName, detail.cityCodeName, detail.areaCodeName].filter((ov) => ov).join('-') || '--'}</div>
				</div>
				<div className="flex align-center justify-start font-size-14 line-height-22 padding-left-16 margin-bottom-20">
					<div className="color-86909c margin-right-4">联系人：</div>
					<div className="">{detail.orgContacts || '--'}</div>
				</div>
				<div className="flex align-center justify-start font-size-14 line-height-22 padding-left-16 margin-bottom-20">
					<div className="color-86909c margin-right-4">联系人职位：</div>
					<div className="">{detail.orgContactsPosition || '--'}</div>
				</div>
				<div className="flex align-center justify-start font-size-14 line-height-22 padding-left-16 margin-bottom-20">
					<div className="color-86909c margin-right-4">联系人电话：</div>
					<div className="">{detail.orgContactsPhone || '--'}</div>
				</div>
			</div>

			<div id="section3"></div>
			<div className="bg-color-ffffff border-radius-8 padding-lr-24 padding-tb-20 margin-tb-16">
				<div className="flex align-center justify-between margin-bottom-20">
					<div className="font-size-18 line-height-26 font-weight-500">成果描述</div>
					<Button
						type="link"
						icon={<EditOutlined />}
						disabled={!isEditBase}
						title={!isEditBase ? '当前成果来源不是“参赛提交”请到成果管理进行编辑' : ''}
						onClick={() => {
							linkTo(`${linkToPath}/curd?id=${id}#section1`);
						}}
					>
						编辑信息
					</Button>
				</div>

				<div className="flex justify-start font-size-14 line-height-22 padding-left-16 margin-bottom-20">
					<div className="color-86909c margin-right-4 flex-shrink">成果描述：</div>
					<div className="flex-sub overflow-hidden">
						<div
							className="font-size-14 line-height-24 pre-wrap rich-box"
							dangerouslySetInnerHTML={{
								__html: detail.detailsContent || '--',
							}}
						></div>
					</div>
				</div>

				{detail.attachment && (
					<div className="flex justify-start font-size-14 line-height-22 padding-left-16 margin-bottom-20">
						<div className="color-86909c margin-right-4">成果附件：</div>
						<div className="flex-sub">
							<div
								className="a color-165dff"
								onClick={() => {
									const attachmentList = (detail.attachment || '').split('/');
									const fileName = encodeURIComponent(attachmentList.pop());
									openNewTab(attachmentList.join('/') + '/' + fileName);
								}}
							>
								{(detail.attachment || '').split('/').pop()}
							</div>
						</div>
					</div>
				)}
			</div>

			<div id="section4"></div>
			<div className="bg-color-ffffff border-radius-8 padding-lr-24 padding-tb-20 margin-tb-16">
				<div className="flex justify-between margin-bottom-20">
					<div className="font-size-18 line-height-26 font-weight-500">相关参与人</div>
					<Button
						type="link"
						icon={<EditOutlined />}
						onClick={() => {
							linkTo(`${linkToPath}/curd?id=${id}#section4`);
						}}
					>
						编辑参与人
					</Button>
				</div>
				<div className="flex align-center justify-start font-size-14 line-height-22 padding-left-16 margin-bottom-20">
					<div className="color-86909c margin-right-24">关联科转号</div>
					<div className="flex align-start justify-start flex-wrap">
						<Space wrap>
							{(detail.ttChannelsList || []).map((item) => {
								return (
									<div key={item.id} className="flex align-center justify-start margin-right-20">
										<img src={item.avatarUrl} className="width-20 height-20 border-radius-10 bg-color-e5e6eb" />
										<div className="font-size-14 color-165dff margin-left-6 line-height-20">{item.accountName || ''}</div>
									</div>
								);
							})}
						</Space>
						{(detail.ttChannelsList || []).length === 0 && '--'}
					</div>
				</div>
				<div className="flex justify-start font-size-14 line-height-22 padding-left-16 margin-bottom-20">
					<div className="color-86909c margin-right-24">成果挖掘人</div>
					<div className="flex-sub flex align-start justify-start flex-wrap">
						<Space wrap>
							{(detail.excavatePersonnelTtChannelsList || []).map((item) => {
								return (
									<div key={item.id} className="flex align-center justify-start margin-right-20">
										<img src={item.avatarUrl} className="width-20 height-20 border-radius-10 bg-color-e5e6eb" />
										<div className="font-size-14 color-165dff margin-left-6 line-height-20">{item.accountName || ''}</div>
									</div>
								);
							})}
						</Space>
						{(detail.excavatePersonnelTtChannelsList || []).length === 0 && '--'}
					</div>
				</div>
				<div className="flex justify-start font-size-14 line-height-22 padding-left-16 margin-bottom-20">
					<div className="color-86909c margin-right-24">成果认证人</div>
					<div className="flex-sub flex align-start justify-start flex-wrap">
						<Space wrap>
							{(detail.authPersonnelTtChannelsList || []).map((item) => {
								return (
									<div key={item.id} className="flex align-center justify-start margin-right-20">
										<img src={item.avatarUrl} className="width-20 height-20 border-radius-10 bg-color-e5e6eb" />
										<div className="font-size-14 color-165dff margin-left-6 line-height-20">{item.accountName || ''}</div>
									</div>
								);
							})}
						</Space>
						{(detail.authPersonnelTtChannelsList || []).length === 0 && '--'}
					</div>
				</div>
				<div className="flex justify-start font-size-14 line-height-22 padding-left-16 margin-bottom-20">
					<div className="color-86909c margin-right-24">成果负责人</div>
					<div className="flex-sub flex align-start justify-start flex-wrap">
						<Space wrap>
							{(detail.responsiblePersonnelTtChannelsList || []).map((item) => {
								return (
									<div key={item.id} className="flex align-center justify-start margin-right-20">
										<img src={item.avatarUrl} className="width-20 height-20 border-radius-10 bg-color-e5e6eb" />
										<div className="font-size-14 color-165dff margin-left-6 line-height-20">{item.accountName || ''}</div>
									</div>
								);
							})}
						</Space>
						{(detail.responsiblePersonnelTtChannelsList || []).length === 0 && '--'}
					</div>
				</div>
				<div className="flex justify-start font-size-14 line-height-22 padding-left-16 margin-bottom-20">
					<div className="color-86909c margin-right-24">成果跟进人</div>
					<div className="flex-sub flex align-start justify-start flex-wrap">
						<Space wrap>
							{(detail.brokerTtChannelsList || []).map((item) => {
								return (
									<div key={item.id} className="flex align-center justify-start margin-right-20">
										<img src={item.avatarUrl} className="width-20 height-20 border-radius-10 bg-color-e5e6eb" />
										<div className="font-size-14 color-165dff margin-left-6 line-height-20">{item.accountName || ''}</div>
									</div>
								);
							})}
						</Space>
						{(detail.brokerTtChannelsList || []).length === 0 && '--'}
					</div>
				</div>
			</div>

			<div id="section5"></div>
			<div className="bg-color-ffffff border-radius-8 padding-lr-24 padding-tb-20 margin-tb-16">
				<div className="flex align-center justify-between margin-bottom-20">
					<div className="font-size-18 line-height-26 font-weight-500">合作方动态</div>
					<Button
						type="link"
						icon={<EditOutlined />}
						onClick={() => {
							linkTo(`${linkToPath}/docking/curd?sourceId=${id}`);
						}}
					>
						添加合作方
					</Button>
				</div>
				<div className="ffont-size-14 line-height-22 padding-left-16 margin-bottom-20">
					{/* 表格 开始 */}
					<Table rowKey="id" dataSource={dataSource} pagination={false} scroll={{ x: 'max-content' }}>
						<Table.Column title="合作方" key="supplierName" dataIndex="supplierName" />

						<Table.Column
							title="当前状态"
							dataIndex="stageStatus"
							align="center"
							width={160}
							render={(text) => {
								return (
									<div
										className={`tag-status-${
											['error', 'warning', 'primary', 'success', 'default', 'primary', 'primary'][text || 0]
										}  `}
									>
										{dockingStageStatusDataTextList[text || 0]}
									</div>
								);
							}}
						/>
						<Table.Column title="跟进人" dataIndex="brokerName" align="center" />
						<Table.Column
							title="更新时间"
							dataIndex="createTime"
							align="center"
							render={(text) => {
								return (text && dayjs(text).format('YYYY/MM/DD HH:mm')) || '';
							}}
						/>
						<Table.Column
							title="操作"
							key="option"
							dataIndex="option"
							align="center"
							width={160}
							render={(_, record) => {
								return (
									<Button
										type="link"
										size="small"
										onClick={() => linkTo(`${linkToPath}/docking/detail?id=${record.id}&fromDetail=1`)}
									>
										详情
									</Button>
								);
							}}
						/>
					</Table>
					{/* 表格 结束 */}
				</div>
			</div>

			{/* 赛事不显示 外部合作意向  */}
			{props.releaseType !== 3 && (
				<>
					<div id="section6"></div>
					<div style={{ minHeight: 'calc(100vh - 100px)' }}>
						<div className="bg-color-ffffff border-radius-8 padding-lr-24 padding-tb-20 margin-tb-16">
							<div className="flex justify-between margin-bottom-20">
								<div className="font-size-18 line-height-26 font-weight-500">外部合作意向</div>
							</div>
							<Cooperate.Table cooperateProjectId={id} cooperateType={1} linkToPath={linkToPath} />
						</div>
					</div>
				</>
			)}

			{/* 询问弹窗 开始 */}
			<ModalForm ref={ModalFormRef} onOk={handleAudit} FormComp={(props) => <ConrirmForm ref={props.FormCompRef} />} />
			{/* 询问弹窗 结束 */}
		</div>
	);
};

// 询问表单
const ConrirmForm = forwardRef((props = {}, ref) => {
	const [form] = Form.useForm();

	useImperativeHandle(ref, () => {
		return {
			onCancel: () => Promise.resolve(),
			onOk: () => {
				return new Promise((resolve) => {
					form.validateFields().then((values) => {
						resolve(values);
					});
				});
			},
		};
	});

	return (
		<Form form={form} labelCol={{ style: { width: '100px' } }} initialValues={{ status: 2 }}>
			<Form.Item hidden name="status">
				<Input />
			</Form.Item>
			<Form.Item label="原因" name="reason" rules={[{ required: true, message: '请输入不通过原因' }]}>
				<Input.TextArea rows={4} placeholder="请输入不通过原因" />
			</Form.Item>
		</Form>
	);
});

export default Index;
