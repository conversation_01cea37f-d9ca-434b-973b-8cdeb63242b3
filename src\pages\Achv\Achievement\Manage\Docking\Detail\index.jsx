import Docking from '@/components/Achv/DockingManage/index';

import { competitionGroupTextList } from '@/pages/Achv/config';

const Index = (props = {}) => {
	// 跳转地址
	const linkToPath = props.linkToPath || `/newAchv/achievement/manage`;

	return <Docking.Detail type={2} BaseData={BaseData} linkToPath={linkToPath} releaseType={props.releaseType} />;
};

// 基础数据
export const BaseData = (props = {}) => {
	const detailData = props.detailData;
	return (
		<>
			<div className="flex margin-top-20">
				<div className="margin-right-4 line-height-22 color-86909c">合作方：</div>
				<div className="flex-sub line-height-22 color-1d2129">{detailData.supplierName}</div>
			</div>
			<div className="flex margin-top-20">
				<div className="margin-right-4 line-height-22 color-86909c">联系人：</div>
				<div className="flex-sub line-height-22 color-1d2129">
					{[detailData.supplierContacts, detailData.supplierContactsPhone].filter((ov) => ov).join(' / ') || '暂无'}
				</div>
			</div>
			{detailData.competitionGroup && (
				<div className="flex margin-top-20">
					<div className="margin-right-4 line-height-22 color-86909c">联系人组别：</div>
					<div className="flex-sub">
						<div className={`tag-status-${['', 'primary', 'warning', '700eb2'][detailData.competitionGroup]}`}>
							{competitionGroupTextList[detailData.competitionGroup] || '--'}
						</div>
					</div>
				</div>
			)}
			<div className="flex margin-top-20">
				<div className="margin-right-4 line-height-22 color-86909c">联系人职位：</div>
				<div className="flex-sub line-height-22 color-1d2129">{detailData.supplierContactsPosition || '--'}</div>
			</div>
			<div className="flex margin-top-20">
				<div className="margin-right-4 line-height-22 color-86909c">意向转化方式：</div>
				<div className="flex-sub line-height-22 color-1d2129">{detailData.cooperateMode || '--'}</div>
			</div>
			<div className="flex margin-top-20">
				<div className="margin-right-4 line-height-22 color-86909c">合作需求描述：</div>
				<div className="flex-sub line-height-22 color-1d2129">{detailData.supplierIntroduction || '暂无'}</div>
			</div>
		</>
	);
};

export default Index;
