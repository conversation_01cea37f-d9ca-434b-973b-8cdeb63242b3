import { useEffect, useState } from 'react';
import { Table, Button, Popconfirm, Space, Form, Input, Cascader, Row, Col, Affix, message, Image, Select, InputNumber } from 'antd';
import Permission from '@/components/Permission';
import { useTableData } from '@/hook/useTableData';
import { useRouterLink } from '@/hook/useRouter';

import {
	pageAchievement as getTablePageData,
	achievementDel as delTableItemData,
	exportList as exportTableData,
	updateRankingNum as batchUpdateSort,
	queryAchievementStatistics,
} from '@/api/Achv/Achievement/Manage/index';
import { addRecommend, pageRecommend } from '@/api/Achv/DailyPush/index';
import { getThreeLevelData } from '@/api/common';

import { auditStatusData, releaseTypeData, releaseTypeTextList } from '@/pages/Achv/config';

import './index.scss';
const Index = () => {
	const { linkTo, searchParams } = useRouterLink();

	const status = searchParams.get('auditStatus');
	const [auditStatus, setAuditStatus] = useState(status === null ? 3 : status - 0 || '');
	const { form, dataSource, pagination, changePage, delTableData, exportData, onSearch, onReset, SortInput } = useTableData({
		params: { auditStatus },
		getTablePageData,
		delTableItemData,
		exportTableData,
		batchUpdateSort,
		pageCallback: (paramsData = {}) => {
			getStatisticsData({ ...paramsData });
		},
	});

	// 获取统计数据
	const [statistics, setStatistics] = useState({});
	const getStatisticsData = (paramsData) => {
		delete paramsData.auditStatus;
		delete paramsData.pageSize;
		delete paramsData.pageNum;
		delete paramsData.tempArea;
		queryAchievementStatistics(paramsData).then((res) => {
			const resData = res.data || {};
			setStatistics({
				...resData,
				total: Object.values(resData).reduce((pre, cur) => pre + (cur - 0), 0),
			});
		});
	};

	// 获取推荐列表
	const [recommendList, setRecommendList] = useState([]);

	const getRecommendList = () => {
		pageRecommend({ pageSize: 100, pageNum: 1 }).then((res) => {
			setRecommendList((res?.data?.records || []).filter((ov) => ov.recommendType === 2).map((ov) => ov.sourceId));
		});
	};

	// 推荐
	const recommend = (id) => {
		addRecommend({
			recommendType: 2,
			sourceId: id,
		}).then(() => {
			message.success('推荐成功');
			getRecommendList();
		});
	};

	useEffect(() => {
		getRecommendList();
	}, []);

	return (
		<div className="">
			<div className="flex flex-shrink align-center margin-bottom-4 height-48 font-size-22 font-weight-500 color-1d2129">成果管理</div>
			{/* Tabs & 功能按钮 开始 */}
			<Affix offsetTop={0}>
				<div className="flex align-center justify-between padding-24 border-radius-8 bg-color-ffffff border-solid-bottom-f2f3f5">
					<div className="flex align-center">
						{auditStatusData.map((ov) => (
							<div
								key={ov.value}
								className={`a margin-right-40 font-size-18 font-weight-500 ${auditStatus === ov.value ? 'color-165dff' : ''}`}
								onClick={() => setAuditStatus(ov.value)}
							>
								{ov.label}
								{statistics[ov.countName] > 0 ? `（${statistics[ov.countName]}）` : ''}
							</div>
						))}
					</div>
					<Space size={16}>
						<Permission hasPermi={['newAchv:achievement:manage:list:export']}>
							<Button onClick={exportData}>批量导出</Button>
						</Permission>
						<Button
							type="primary"
							onClick={() => {
								linkTo(`/newAchv/achievement/manage/curd`);
							}}
						>
							新建成果
						</Button>
					</Space>
				</div>
			</Affix>
			{/* Tabs & 功能按钮 结束 */}
			<div className="flex-sub margin-top-18 padding-24 border-radius-8 bg-color-ffffff">
				{/* 筛选条件 开始 */}
				<FilerForm form={form} onReset={onReset} onSearch={onSearch} />
				{/* 筛选条件 结束 */}

				{/* 表格 开始 */}
				<ListTable
					dataSource={dataSource}
					pagination={pagination}
					changePage={changePage}
					delTableData={delTableData}
					recommend={recommend}
					recommendList={recommendList}
					SortInput={SortInput}
				/>
				{/* 表格 结束 */}
			</div>
		</div>
	);
};

export const FilerForm = (props = {}) => {
	const { form, onReset, onSearch } = props;

	// 获取选项数据
	const [areaOptions, setAreaOptions] = useState([]);
	const getOptionsData = () => {
		getThreeLevelData({ level: 2 }).then((res) => {
			setAreaOptions(res.data || []);
		});
	};

	useEffect(() => {
		getOptionsData();
	}, []);

	return (
		<div className="flex  justify-between margin-bottom-18">
			<div className="flex-sub">
				<Form
					form={form}
					labelAlign="right"
					layout="inline"
					initialValues={{
						provinceCodes: undefined,
						cityCodes: undefined,
						tempArea: [],
					}}
				>
					<Form.Item hidden name="provinceCodes">
						<Input />
					</Form.Item>
					<Form.Item hidden name="cityCodes">
						<Input />
					</Form.Item>
					<Row className="width-100per" gutter={[12, 12]}>
						<Col span={8}>
							<Form.Item name="keywords" label="成果名称">
								<Input placeholder="请输入成果名称" />
							</Form.Item>
						</Col>
						<Col span={8}>
							<Form.Item name="tempArea" label="区域">
								<Cascader
									options={areaOptions}
									placeholder="请选择区域"
									changeOnSelect
									displayRender={(label) => label.join('-')}
									onChange={(e = [undefined, undefined]) => {
										form.setFieldValue('provinceCodes', e[0] ? [e[0]] : undefined);
										form.setFieldValue('cityCodes', e[1] ? [e[1]] : undefined);
									}}
								/>
							</Form.Item>
						</Col>
						{!props.hideReleaseType && (
							<Col span={8}>
								<Form.Item name="releaseType" label="来源">
									<Select options={releaseTypeData} allowClear placeholder="请选择来源" />
								</Form.Item>
							</Col>
						)}
					</Row>
				</Form>
			</div>
			<div className="margin-left-12 padding-left-12 border-solid-left-e5e6eb">
				<Space size={16}>
					<Button onClick={onReset}>重置</Button>
					<Button type="primary" onClick={onSearch}>
						查询
					</Button>
				</Space>
			</div>
		</div>
	);
};

export const ListTable = (props = {}) => {
	const { linkTo } = useRouterLink();
	const { dataSource, pagination, changePage, delTableData } = props;
	return (
		<Table rowKey="id" dataSource={dataSource} pagination={pagination} onChange={changePage} scroll={{ x: 'max-content' }}>
			<Table.Column
				title="序号"
				key="index"
				width={60}
				render={(_, __, index) => {
					return index + 1;
				}}
			/>
			<Table.Column
				title="图片"
				key="achvtImgUrl"
				width={100}
				render={(_, record) => {
					return (
						(record.achvtImgUrl && (
							<div className="flex align-center justify-center width-100 height-100 overflow-hidden">
								<Image
									width={100}
									src={record.achvtImgUrl}
									fallback="https://gbac-produce.oss-cn-shenzhen.aliyuncs.com/static-assets/gbac-achv-applet/Public/default-achv-cover.png"
								/>
							</div>
						)) ||
						null
					);
				}}
			/>

			<Table.Column
				title="成果名称"
				dataIndex="name"
				render={(text) => {
					return <div className="max-width-240">{text}</div>;
				}}
			/>
			<Table.Column
				title="成果状态"
				dataIndex="proclamationStatus"
				align="center"
				render={(text) => {
					return <div className={`proclamation-status-${text}`}>{['', '可揭榜', '揭榜中', '已揭榜'][text]}</div>;
				}}
			/>
			<Table.Column title="技术领域" dataIndex="areaCategoryListName" render={(text) => (text && text.length ? text.join('、') : '--')} />
			<Table.Column title="所属机构" dataIndex="orgName" />
			<Table.Column title="所处阶段" dataIndex="stageIdName" />
			<Table.Column title="转化方式" dataIndex="transformListName" />
			<Table.Column
				title="所属区域"
				dataIndex="provinceCode"
				render={(_, record) => {
					return [record.provinceCodeName, record.cityCodeName, record.areaCodeName].filter((ov) => ov).join('-');
				}}
			/>
			<Table.Column
				title="审核状态"
				dataIndex="auditStatus"
				align="center"
				render={(text) => {
					return (
						<div className={`tag-status-small-${['default', 'warning', 'error', 'primary'][text]}`}>
							{['', '审核中', '审核不通过', '审核通过'][text] || ''}
						</div>
					);
				}}
			/>
			<Table.Column
				title="合作意向"
				dataIndex="cooperateNum"
				align="center"
				render={(text, record) => {
					return text > 0 ? (
						<div
							className="a color-165dff"
							onClick={() => linkTo(`${props.linkToPath || '/newAchv/achievement/manage'}/detail?id=${record.id}#section6`)}
						>
							{text}
						</div>
					) : (
						0
					);
				}}
			/>
			<Table.Column
				title="来源"
				dataIndex="releaseType"
				render={(text) => {
					return (
						<div className={`tag-status-small-${['', 'primary', 'warning', '700eb2', 'success', 'error'][text]}`}>
							{releaseTypeTextList[text] || '--'}
						</div>
					);
				}}
			/>
			{props.SortInput && (
				<Table.Column
					title="排序"
					align="center"
					dataIndex="rankingNum"
					render={(_, record) => {
						return <props.SortInput record={record} />;
					}}
				/>
			)}
			<Table.Column
				title="提交时间"
				dataIndex="createTime"
				render={(text) => {
					return (text || '--').slice(0, 16);
				}}
			/>
			<Table.Column
				title="操作"
				key="option"
				dataIndex="option"
				align="center"
				fixed="right"
				render={(_, record) => {
					return (
						<>
							<Button
								type="link"
								size="small"
								onClick={() => linkTo(`${props.linkToPath || '/newAchv/achievement/manage'}/detail?id=${record.id}&fromList=1`)}
							>
								编辑/审核
							</Button>
							<Popconfirm
								title="提示"
								description="确定删除吗？"
								onConfirm={() => {
									delTableData(record.id);
								}}
								okText="确定"
								cancelText="取消"
							>
								<Button type="link" size="small">
									删除
								</Button>
							</Popconfirm>
							{props.recommend && (
								<Button
									type="link"
									size="small"
									disabled={props.recommendList.includes(record.id)}
									onClick={() => props.recommend(record.id)}
								>
									推荐
								</Button>
							)}
						</>
					);
				}}
			/>
		</Table>
	);
};

export default Index;
