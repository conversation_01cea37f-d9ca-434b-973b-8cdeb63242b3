import { useEffect, useState, useRef, forwardRef, useImperativeHandle } from 'react';
import { useRouterLink } from '@/hook/useRouter';

import {
	Button,
	Space,
	Form,
	Input,
	Cascader,
	Radio,
	Checkbox,
	InputNumber,
	message,
	Affix,
	Anchor,
	Select,
	DatePicker,
	Table,
	Popconfirm,
	Modal,
	Image,
} from 'antd';

import UploadImg from '@/components/UploadImg';
import UploadFile from '@/components/UploadFile';
import { addActivity, updateActivity, getActivityDetail, detailConfig, saveConfig, delConfig } from '@/api/Achv/Activity/index';
import dayjs from 'dayjs';
import { DeleteOutlined } from '@ant-design/icons';

const pathTypeList = [
	{
		label: '内部',
		value: 'inner',
	},
	// {
	// 	label: 'h5',
	// 	value: 'h5',
	// },
	// {
	// 	label: '外部小程序',
	// 	value: 'otherMp',
	// },
];

const initLabelCol = { style: { width: 150 } };

const GridItemForm = forwardRef((props = {}, ref) => {
	const [form] = Form.useForm();

	const [isModalOpen, setIsModalOpen] = useState(false);

	useImperativeHandle(ref, () => {
		return {
			showModal: (fields = {}) => {
				form.setFieldsValue({ ...fields });
				setIsModalOpen(true);
			},
		};
	});

	const handleOk = () => {
		setIsModalOpen(false);
		form.validateFields().then((values) => {
			props.onChange(values);
			handleCancel();
		});
	};
	const handleCancel = () => {
		setIsModalOpen(false);
		form.resetFields();
	};

	return (
		<>
			<Modal title="格子配置" open={isModalOpen} onOk={handleOk} onCancel={handleCancel} width={800}>
				<Form
					form={form}
					className="antd-form-box"
					labelAlign="right"
					labelCol={initLabelCol}
					initialValues={{
						id: '',
						name: '',
						icon: '',
						detailImageUrl: '',
						pathType: '',
						path: '',
						sort: '',
					}}
				>
					<Form.Item hidden name="id">
						<Input />
					</Form.Item>

					<Form.Item label="名称" name="name">
						<Input className="input-box" placeholder="请输入名称" />
					</Form.Item>
					<Form.Item label="icon图标" name="icon" required>
						<UploadImg size={1} width={150} height={150} tips={'建议尺寸：100*100px'} />
					</Form.Item>
					<Form.Item label="详情图片" name="detailImageUrl">
						<UploadImg size={1} width={150} height={150} />
					</Form.Item>

					<Form.Item label="跳转方式" name="pathType">
						<Select options={pathTypeList} allowClear placeholder="请输入跳转方式" />
					</Form.Item>
					<Form.Item label="跳转地址" name="path">
						<Input className="input-box" placeholder="请输入跳转地址" />
					</Form.Item>
					<Form.Item label="排序" name="sort">
						<Input className="input-box" placeholder="请输入排序" />
					</Form.Item>
				</Form>
			</Modal>
		</>
	);
});

// 九宫格列表
const GridTable = (props) => {
	const ModalFormRef = useRef();
	const [dataSource, setDataSource] = useState([]);
	useEffect(() => {
		setDataSource((props.value || []).sort((a, b) => (a.sort || Infinity) - (b.sort || Infinity)));
	}, [props]);
	return (
		<>
			<div className="flex justify-start align-center margin-bottom-10">
				<Button
					type="primary"
					onClick={() => {
						ModalFormRef.current.showModal({
							id: dayjs().valueOf(),
							icon: '',
							name: '',
							pathType: '',
							path: '',
							sort: '',
							status: 1,
						});
					}}
				>
					新增
				</Button>
			</div>
			<Table rowKey="id" dataSource={dataSource} pagination={false} scroll={{ x: 'max-content' }}>
				<Table.Column
					title="序号"
					key="index"
					width={60}
					render={(_, __, index) => {
						return index + 1;
					}}
				/>
				<Table.Column
					title="icon图标"
					dataIndex="icon"
					render={(text) => <Image src={text} alt="" width={60} className="width-60 border-radius-8" />}
				/>
				<Table.Column title="名称" dataIndex="name" render={(text) => <div className="max-width-240">{text}</div>} />
				<Table.Column
					title="跳转方式"
					dataIndex="pathType"
					render={(text) => <div className="max-width-240">{(pathTypeList.find((ov) => ov.value == text) || {}).label || ''}</div>}
				/>
				<Table.Column title="跳转地址" dataIndex="path" render={(text) => <div className="max-width-240">{text}</div>} />
				<Table.Column
					title="详情图片"
					dataIndex="detailImageUrl"
					render={(text) => <Image src={text} alt="" width={60} className="width-60 border-radius-8" />}
				/>
				<Table.Column title="排序" dataIndex="sort" render={(text) => <div className="max-width-240">{text}</div>} />

				<Table.Column
					title="操作"
					key="option"
					dataIndex="option"
					align="center"
					fixed="right"
					render={(_, record) => {
						return (
							<>
								<Button
									type="link"
									size="small"
									onClick={() => {
										ModalFormRef.current.showModal({ ...record });
									}}
								>
									编辑
								</Button>
								<Popconfirm
									title="提示"
									description="确定删除吗？"
									onConfirm={() => {
										props.onChange(dataSource.filter((ov) => ov.id != record.id));
									}}
									okText="确定"
									cancelText="取消"
								>
									<Button type="link" size="small">
										删除
									</Button>
								</Popconfirm>
							</>
						);
					}}
				/>
			</Table>
			<GridItemForm
				ref={ModalFormRef}
				onChange={(data = {}) => {
					const find = dataSource.find((ov) => ov.id == data.id);
					if (find) {
						props.onChange(
							dataSource.map((ov) => {
								if (ov.id == find.id) {
									return data;
								} else {
									return ov;
								}
							})
						);
					} else {
						props.onChange([...dataSource, data]);
					}
				}}
			/>
		</>
	);
};

// 九宫格配置
const ActivityConfig2 = () => {
	return (
		<div className="margin-top-20 padding-20 border-radius-8 bg-color-ffffff">
			<div className="padding-bottom-30 line-height-26 font-size-18 font-weight-500 color-1d2129">九宫格配置</div>
			<Form.Item name={['introduce', 'bgImageUrl']} label="背景图" rules={[{ required: true, message: '请上传背景图' }]}>
				<UploadImg size={5} width={200} height={300} tips={'建议尺寸：375*812px'} />
			</Form.Item>
			<Form.Item name={['introduce', 'kvImageUrl']} label="顶部KV" rules={[{ required: true, message: '请上传顶部KV' }]}>
				<UploadImg size={2} width={300} height={184} tips={'建议尺寸：750*460px'} />
			</Form.Item>
			<Form.Item name={['introduce', 'centerImageUrl']} label="中间图" rules={[{ required: true, message: '请上传中间图' }]}>
				<UploadImg size={2} width={300} height={120} tips={'建议尺寸：630*230px'} />
			</Form.Item>
			<Form.Item name={['introduce', 'shareTitle']} label="微信分享标题">
				<Input className="input-box" placeholder="请输入微信分享标题" />
			</Form.Item>
			<Form.Item name={['introduce', 'shareImageUrl']} label="微信分享封面">
				<UploadImg size={2} width={200} height={160} tips={'建议尺寸：200*160px'} cropperProps={{ width: 500, height: 400 }} />
			</Form.Item>
			<Form.Item name={['introduce', 'shareTimelineTitle']} label="朋友圈分享标题">
				<Input className="input-box" placeholder="请输入朋友圈分享标题" />
			</Form.Item>
			<Form.Item name={['introduce', 'shareTimelineImageUrl']} label="朋友圈分享封面">
				<UploadImg size={2} width={200} height={200} tips={'建议尺寸：100*100px'} cropperProps={{ width: 200, height: 200 }} />
			</Form.Item>
			<Form.Item name={['introduce', 'list']} label="格子配置">
				<GridTable />
			</Form.Item>
		</div>
	);
};

/* 证书配置 */
export const CertificateConfig = (props = {}) => {
	const form = Form.useFormInstance();
	const isOpenCertificate = Form.useWatch(['extend', 'isOpenCertificate'], form);
	const resultNeedShare = Form.useWatch(['extend', 'resultConfig', 'resultNeedShare'], form) || [];

	return (
		<div className="margin-top-20 padding-20 border-radius-8 bg-color-ffffff">
			<div className="padding-bottom-30 line-height-26 font-size-18 font-weight-500 color-1d2129">证书配置</div>
			{/* 是否开启证书配置 */}
			<Form.Item name={['extend', 'isOpenCertificate']} label="是否配置证书" rules={[{ required: true, message: '请选择是否需要证书配置' }]}>
				<Radio.Group
					options={[
						{ label: '是', value: 1 },
						{ label: '否', value: 0 },
					]}
				/>
			</Form.Item>
			{isOpenCertificate ? (
				<>
					{/* 证书查询页面配置 */}
					<Form.Item label="证书查询页">
						<Form.Item
							name={['extend', 'findConfig', 'findCondition']}
							label="查询条件"
							labelCol={initLabelCol}
							rules={[{ required: true, message: '请选择查询条件' }]}
						>
							<Checkbox.Group
								options={[
									{ label: '单位名称', value: 1 },
									{ label: '统一社会信用代码', value: 2 },
								]}
							/>
						</Form.Item>
						<Form.Item
							name={['extend', 'findConfig', 'backgroundImg']}
							label="背景图"
							labelCol={initLabelCol}
							rules={[{ required: true, message: '请上传背景图' }]}
						>
							<UploadImg size={2} width={80} height={80} tips={'建议尺寸：393*762px'} cropperProps={{ width: 393, height: 762 }} />
						</Form.Item>
						<Form.Item name={['extend', 'findConfig', 'shareTitle']} label="微信分享标题" labelCol={initLabelCol}>
							<Input placeholder="请输入微信分享标题" />
						</Form.Item>
						<Form.Item name={['extend', 'findConfig', 'shareCoverImg']} label="微信分享封面" labelCol={initLabelCol}>
							<UploadImg size={2} width={80} height={80} tips={'建议尺寸：200*160px'} cropperProps={{ width: 200, height: 160 }} />
						</Form.Item>
						<Form.Item name={['extend', 'findConfig', 'friendShareTitle']} label="朋友圈分享标题" labelCol={initLabelCol}>
							<Input placeholder="请输入朋友圈分享标题" />
						</Form.Item>
						<Form.Item name={['extend', 'findConfig', 'friendShareCoverImg']} label="朋友圈分享封面" labelCol={initLabelCol}>
							<UploadImg size={2} width={80} height={80} tips={'建议尺寸：200*200px'} cropperProps={{ width: 200, height: 200 }} />
						</Form.Item>
					</Form.Item>
					{/* 证书结果页配置 */}
					<Form.Item label="证书结果页">
						<Form.Item
							name={['extend', 'resultConfig', 'backgroundImg']}
							label="证书背景图"
							labelCol={initLabelCol}
							rules={[{ required: true, message: '请上传背景图' }]}
						>
							<UploadImg size={6} width={80} height={80} tips={'建议尺寸：248*350px'} />
						</Form.Item>
						<Form.Item
							name={['extend', 'resultConfig', 'showCols']}
							label="显示数据"
							labelCol={initLabelCol}
							rules={[{ required: true, message: '请配置显示数据' }]}
						>
							<Checkbox.Group
								options={[
									{ label: '单位名称', value: '单位名称' },
									{ label: '统一社会信用代码', value: '统一社会信用代码' },
									{ label: '行业', value: '行业' },
									{ label: '组别', value: '组别' },
									{ label: '奖项', value: '奖项' },
								]}
							/>
						</Form.Item>
						<Form.Item
							name={['extend', 'resultConfig', 'nameListFile']}
							label="导入数据"
							labelCol={initLabelCol}
							rules={[{ required: true, message: '请上传导入数据' }]}
						>
							<FileUpload />
						</Form.Item>
						<Form.Item
							name={['extend', 'resultConfig', 'resultNeedShare']}
							label="是否需要分享/下载"
							labelCol={initLabelCol}
							rules={[{ required: true, message: '请选择是否需要分享/下载' }]}
						>
							<Checkbox.Group
								options={[
									{ label: '分享', value: 'shareStatus' },
									{ label: '下载', value: 'downloadStatus' },
								]}
							/>
						</Form.Item>
						{resultNeedShare?.includes('shareStatus') && (
							<>
								<Form.Item name={['extend', 'resultConfig', 'shareTitle']} label="微信分享标题" labelCol={initLabelCol}>
									<Input placeholder="请输入微信分享标题" />
								</Form.Item>
								<Form.Item name={['extend', 'resultConfig', 'shareCoverImg']} label="微信分享封面" labelCol={initLabelCol}>
									<UploadImg
										size={2}
										width={80}
										height={80}
										tips={'建议尺寸：200*160px'}
										cropperProps={{ width: 200, height: 160 }}
									/>
								</Form.Item>
								<Form.Item name={['extend', 'resultConfig', 'friendShareTitle']} label="朋友圈分享标题" labelCol={initLabelCol}>
									<Input placeholder="请输入朋友圈分享标题" />
								</Form.Item>
								<Form.Item name={['extend', 'resultConfig', 'friendShareCoverImg']} label="朋友圈分享封面" labelCol={initLabelCol}>
									<UploadImg
										size={2}
										width={80}
										height={80}
										tips={'建议尺寸：200*200px'}
										cropperProps={{ width: 200, height: 200 }}
									/>
								</Form.Item>
							</>
						)}
					</Form.Item>
				</>
			) : null}
		</div>
	);
};

/* 文件上传 */
export const FileUpload = (props = {}) => {
	return (
		<Space direction="vertical">
			{props.value && (
				<Space className="line-height-32 font-size-16">
					<div>{(props.value || '').split('/').pop()}</div>
					<DeleteOutlined onClick={() => props.onChange('')} className="color-165dff" />
				</Space>
			)}
			<UploadFile accept={'.xls,.xlsx'} onChange={props.onChange} customName>
				<div className={'flex align-center gap-16'}>
					<Button type="primary">上传文件</Button>
					<span className={'color-86909c font-size-12'}>请上传xls/xlsx格式的文件</span>
				</div>
			</UploadFile>
		</Space>
	);
};

// 普通活动配置
const ActivityConfig1 = () => {
	return (
		<div className="margin-top-20 padding-20 border-radius-8 bg-color-ffffff">
			<div className="padding-bottom-30 line-height-26 font-size-18 font-weight-500 color-1d2129">普通活动配置</div>
			<Form.Item name={['introduce', 'xx']} label="待开发">
				<Input placeholder="待设计开发" disabled />
			</Form.Item>
		</div>
	);
};

// 跳转指定配置
const ActivityConfig3 = () => {
	return (
		<div className="margin-top-20 padding-20 border-radius-8 bg-color-ffffff">
			<div className="padding-bottom-30 line-height-26 font-size-18 font-weight-500 color-1d2129">跳转指定配置</div>
			<Form.Item name={['introduce', 'path']} label="跳转地址" rules={[{ required: true, message: '请输入跳转地址' }]}>
				<Input placeholder="请输入跳转地址 例：/pages/Home/index?id=123" />
			</Form.Item>
		</div>
	);
};

const Index = () => {
	const { linkTo, searchParams } = useRouterLink();
	const [form] = Form.useForm();
	const activityType = Form.useWatch('activityType', form);
	const [eventData, setEventData] = useState(null);

	// 获取详情
	const getDetail = () => {
		const id = searchParams.get('id');
		if (id) {
			getActivityDetail({ id, isUpdate: 1 }).then((res) => {
				const resData = res.data || {};

				if (resData.introduce && typeof resData.introduce === 'string') {
					try {
						resData.introduce = JSON.parse(resData.introduce || '{}');

						if (resData.introduce.path) {
							resData.activityType = 3;
						}
						if (resData.introduce.xx) {
							resData.activityType = 1;
						}
					} catch (error) {}
				}

				form.setFieldsValue(resData);
			});
		}
	};

	// 活动配置详情
	const getConfigDetail = () => {
		const id = searchParams.get('id');
		const params = {
			eventId: id,
			type: 1,
		};
		if (id) {
			detailConfig(params).then((res) => {
				console.log('🚀 ~ getConfigDetail ~ res:', res.data || {});
				setEventData(res.data || {});
				const { id, findConfig } = res.data || {};
				const extend = {
					isOpenCertificate: id ? 1 : 0,
				};
				if (id) {
					const { shareStatus, downloadStatus, ...resultConfig } = res.data.resultConfig;
					extend.findConfig = findConfig;
					const resultNeedShare = [];
					if (shareStatus === 1) {
						resultNeedShare.push('shareStatus');
					}
					if (downloadStatus === 1) {
						resultNeedShare.push('downloadStatus');
					}
					resultConfig.resultNeedShare = resultNeedShare;
					extend.resultConfig = resultConfig;
				}
				form.setFieldsValue({
					extend,
				});
			});
		}
	};
	// 保存活动配置
	const submitConfig = async (extend) => {
		const { id } = eventData;
		const params = { ...extend };
		const { resultNeedShare, ...resultConfig } = extend.resultConfig;
		if (id) {
			params.id = id;
		}
		params.type = 1;
		resultConfig.shareStatus = resultNeedShare.includes('shareStatus') ? 1 : 0;
		resultConfig.downloadStatus = resultNeedShare.includes('downloadStatus') ? 1 : 0;
		params.resultConfig = resultConfig;
		console.log('🚀 ~ submitConfig ~ params:', params);
		return await saveConfig(params);
	};
	// 删除活动配置
	const optionDelConfig = async () => {
		const { id } = eventData || {};
		if (id) {
			await delConfig(id);
		}
	};
	useEffect(() => {
		getDetail();
		getConfigDetail();
	}, []);

	// 取消
	const onCancel = () => {
		linkTo(-1);
	};

	// 提交
	const onSubmit = () => {
		form.validateFields()
			.then((values) => {
				const { ...params } = values;

				if (params.introduce) {
					params.introduce = JSON.stringify(params.introduce);
				}

				(params.id ? updateActivity : addActivity)({ ...params, activityType: 2 }).then(async (res) => {
					/* 判断是否更新配置 */
					const { isOpenCertificate, ...extend } = values.extend;
					if (isOpenCertificate === 1) {
						extend.eventId = params.id || res.data;
						await submitConfig(extend);
					} else {
						await optionDelConfig();
					}
					message.success(params.id ? '修改成功' : '添加成功');
					setTimeout(() => {
						linkTo(-1);
					}, 500);
				});
			})
			.catch((error) => {
				console.log();
				const { errors } = error?.errorFields[0] || {};
				if (errors[0]) {
					message.error(errors[0]);
				}
			});
	};

	return (
		<div className="">
			<div className="flex flex-shrink align-center margin-bottom-4 height-48 font-size-22 font-weight-500 color-1d2129">
				<Space size={10}>
					<div className="a color-86909c" onClick={() => linkTo('/newAchv/activity/list')}>
						活动管理
					</div>
					<div className="color-86909c">/</div>
					<div>活动编辑</div>
				</Space>
			</div>
			<div id="section1"></div>
			{/* Tabs & 功能按钮 开始 */}
			<Affix>
				<div className="height-66 flex align-center justify-between bg-color-ffffff border-radius-8 padding-lr-24 border-solid-bottom-f2f3f5">
					<div className="flex align-center justify-between color-1d2129 line-height-26 font-size-18 font-weight-500">
						<Anchor
							affix={false}
							rootClassName="anchor-header-tabBar-box"
							direction="horizontal"
							replace
							targetOffset={80}
							items={[
								{
									key: 'section1',
									href: '#section1',
									title: <div className="margin-right-40 font-size-16 font-weight-500">活动信息</div>,
								},
								{
									key: 'section2',
									href: '#section2',
									title: <div className="margin-right-40 font-size-16 font-weight-500">活动配置</div>,
								},
								{
									key: 'section3',
									href: '#section3',
									title: <div className="margin-right-40 font-size-16 font-weight-500">证书配置</div>,
								},
							]}
						/>
					</div>
					<Space size={16}>
						<Button onClick={onCancel}>取消</Button>
						<Button type="primary" onClick={onSubmit}>
							保存
						</Button>
					</Space>
				</div>
			</Affix>
			{/* Tabs & 功能按钮 结束 */}

			<Form
				form={form}
				className="antd-form-box"
				labelAlign="right"
				labelCol={initLabelCol}
				initialValues={{
					recommendStatus: 0,
					activityType: 2,
					startTime_endTime: [],
					introduce: {
						bgImageUrl: '',
						kvImageUrl: '',
						centerImageUrl: '',
						shareTitle: '',
						list: [],
					},
					extend: {
						isOpenCertificate: 0,
					},
				}}
			>
				<Form.Item hidden name="id">
					<Input />
				</Form.Item>

				{/* 基本信息 开始 */}
				<div className="margin-top-20 padding-20 border-radius-8 bg-color-ffffff">
					<div className="padding-bottom-30 line-height-26 font-size-18 font-weight-500 color-1d2129">基本信息</div>
					<Form.Item label="活动名称" name="title" rules={[{ required: true, message: '请输入活动名称' }]}>
						<Input className="input-box" placeholder="请输入活动名称" />
					</Form.Item>

					<Form.Item label="封面" name="coverImageUrl" rules={[{ required: true, message: '请上传封面' }]}>
						<UploadImg size={1} width={280} height={104} tips={'建议尺寸：700*260px'} cropperProps={{ width: 700, height: 260 }} />
					</Form.Item>

					<Form.Item label="推荐状态" name="recommendStatus" required>
						<Radio.Group
							options={[
								{ label: '是', value: 1 },
								{ label: '否 ', value: 0 },
							]}
						/>
					</Form.Item>

					<Form.Item label="活动类型" name="activityType" required>
						<Radio.Group
							options={[
								{ label: '九宫格活动', value: 2 },
								{ label: '普通活动', value: 1 },
								{ label: '跳转指定地址', value: 3 },
							]}
						/>
					</Form.Item>
					<Form.Item label="排序" name="rankingNum">
						<InputNumber placeholder="请输入" />
					</Form.Item>
				</div>
				{/* 基本信息 结束 */}

				<div id="section2"></div>
				{/* 普通活动 开始 */}
				{activityType === 1 && <ActivityConfig1 />}
				{/* 普通活动 结束 */}

				{/* 九宫格配置 开始 */}
				{activityType === 2 && <ActivityConfig2 />}
				{/* 九宫格配置 结束 */}

				{/* 跳转指定 开始 */}
				{activityType === 3 && <ActivityConfig3 />}
				{/* 跳转指定 结束 */}

				{/* 证书配置 */}
				<div id={'section3'} />
				<CertificateConfig />
			</Form>
		</div>
	);
};

export default Index;
