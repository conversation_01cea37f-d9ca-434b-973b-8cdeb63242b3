import { useEffect, useState } from 'react';
import { Table, Button, Affix, Anchor, Image, Tabs, Space } from 'antd';
import { EditOutlined } from '@ant-design/icons';

import { detailConfig, getActivityDetail } from '@/api/Achv/Activity/index';

import { useRouterLink } from '@/hook/useRouter';

const pathTypeList = [
	{
		label: '内部',
		value: 1,
	},
	{
		label: 'h5',
		value: 2,
	},
	{
		label: '外部小程序',
		value: 3,
	},
];
const Index = () => {
	const { linkTo, searchParams } = useRouterLink();

	const id = searchParams.get('id') || '';
	const [eventData, setEventData] = useState({});
	const [detail, setDetail] = useState({});
	const getDetail = () => {
		getActivityDetail({ id }).then((res) => {
			const resData = res.data || {};

			try {
				resData.introduce = JSON.parse(resData.introduce || '{}');

				if (resData.introduce.path) {
					resData.activityType = 3;
				}
				if (resData.introduce.xx) {
					resData.activityType = 1;
				}
			} catch (error) {
				setDetail(resData);
			}

			setDetail(resData);
		});
	};
	// 活动配置详情
	const getConfigDetail = () => {
		const params = {
			eventId: id,
			type: 1,
		};
		if (id) {
			detailConfig(params).then((res) => {
				const eventId = res.data.eventId || id;
				const extend = {
					...res.data,
					isOpenCertificate: id ? 1 : 0,
					eventId,
				};
				setEventData(extend);
			});
		}
	};
	useEffect(() => {
		getDetail();
		getConfigDetail();
	}, []);

	return (
		<div>
			<div className="flex flex-shrink align-center margin-bottom-4 height-48 font-size-22 font-weight-500">
				<div className="a color-86909c" onClick={() => linkTo(-1)}>
					活动管理
				</div>
				<div className="margin-lr-10 color-86909c">/</div>
				<div className="color-1d2129">活动详情</div>
			</div>

			<div id="section1"></div>
			<Affix>
				<div className="height-66 flex align-center justify-between bg-color-ffffff border-radius-8 padding-lr-24 border-solid-bottom-f2f3f5">
					<div className="flex align-center justify-between color-1d2129 line-height-26 font-size-18 font-weight-500">
						<Anchor
							affix={false}
							rootClassName="anchor-header-tabBar-box"
							direction="horizontal"
							replace
							targetOffset={80}
							items={[
								{
									key: 'section1',
									href: '#section1',
									title: <div className="margin-right-40 font-size-16 font-weight-500">活动信息</div>,
								},
								{
									key: 'section2',
									href: '#section2',
									title: <div className="margin-right-40 font-size-16 font-weight-500">活动配置</div>,
								},
								{
									key: 'section3',
									href: '#section3',
									title: <div className="margin-right-40 font-size-16 font-weight-500">证书配置</div>,
								},
							]}
						/>
					</div>
				</div>
			</Affix>

			<div className="bg-color-ffffff border-radius-8 padding-lr-24 padding-tb-20 margin-tb-16">
				<div className="flex align-center justify-between margin-bottom-20">
					<div className="font-size-18 line-height-26 font-weight-500">活动信息</div>
					<Button
						type="link"
						icon={<EditOutlined />}
						onClick={() => {
							linkTo(`/newAchv/activity/curd?id=${id}&tabKey=1`);
						}}
					>
						编辑信息
					</Button>
				</div>

				<div className="flex align-center justify-start font-size-14 line-height-22 padding-left-16 margin-bottom-20">
					<div className="color-86909c margin-right-4">活动名称：</div>
					<div className="">{detail.title || '--'}</div>
				</div>
				<div className="flex justify-start font-size-14 line-height-22 padding-left-16 margin-bottom-20">
					<div className="color-86909c margin-right-4">活动封面：</div>
					<div className="">
						<Image src={detail.coverImageUrl} alt="" width={200} className="width-80 border-radius-8" />
					</div>
				</div>
				<div className="flex align-center justify-start font-size-14 line-height-22 padding-left-16 margin-bottom-20">
					<div className="color-86909c margin-right-4">推荐状态：</div>
					<div className="">
						<div className={`tag-status-${['warning', 'primary'][detail.recommendStatus]}`}>
							{detail.recommendStatus === 0 ? '非推荐' : '推荐'}
						</div>
					</div>
				</div>
				<div className="flex align-center justify-start font-size-14 line-height-22 padding-left-16 margin-bottom-20">
					<div className="color-86909c margin-right-4">活动类型：</div>
					<div className="">
						<div className={`tag-status-${['', 'primary', 'warning', '700eb2'][detail.activityType]}`}>
							{['--', '普通活动', '九宫格活动', '跳转指定地址'][detail.activityType || 0]}
						</div>
					</div>
				</div>
				<div className="flex align-center justify-start font-size-14 line-height-22 padding-left-16 margin-bottom-20">
					<div className="color-86909c margin-right-4">排序：</div>
					<div className="">{detail.rankingNum || '--'}</div>
				</div>
			</div>

			<div id="section2"></div>
			{detail.activityType === 1 && <ActivityConfig1 id={id} detail={detail.introduce} />}
			{detail.activityType === 2 && <ActivityConfig2 id={id} detail={detail.introduce} />}
			{detail.activityType === 3 && <ActivityConfig3 id={id} detail={detail.introduce} />}
			<div id="section3" />
			<ActivityConfig id={id} detail={eventData} />
		</div>
	);
};

// 普通活动配置
const ActivityConfig1 = (props = {}) => {
	const { linkTo } = useRouterLink();

	const detail = props.detail || {};
	return (
		<div className="bg-color-ffffff border-radius-8 padding-lr-24 padding-tb-20 margin-tb-16">
			<div className="flex align-center justify-between margin-bottom-20">
				<div className="font-size-18 line-height-26 font-weight-500">普通活动配置</div>
				<Button
					type="link"
					icon={<EditOutlined />}
					onClick={() => {
						linkTo(`/newAchv/activity/curd?id=${props.id}`);
					}}
				>
					编辑信息
				</Button>
			</div>

			<div className="flex justify-start font-size-14 line-height-22 padding-left-16 margin-bottom-20">
				<div className="color-86909c margin-right-4">待设计开发：</div>
				<div className="">--</div>
			</div>
		</div>
	);
};

// 九宫格配置
const ActivityConfig2 = (props = {}) => {
	const { linkTo } = useRouterLink();

	const detail = props.detail || {};
	return (
		<div className="bg-color-ffffff border-radius-8 padding-lr-24 padding-tb-20 margin-tb-16">
			<div className="flex align-center justify-between margin-bottom-20">
				<div className="font-size-18 line-height-26 font-weight-500">九宫格配置</div>
				<Button
					type="link"
					icon={<EditOutlined />}
					onClick={() => {
						linkTo(`/newAchv/activity/curd?id=${props.id}`);
					}}
				>
					编辑信息
				</Button>
			</div>

			<div className="flex justify-start font-size-14 line-height-22 padding-left-16 margin-bottom-20">
				<div className="color-86909c margin-right-4">背景图：</div>
				<div className="">
					<Image src={detail.bgImageUrl} alt="" width={200} className="width-60 border-radius-8" />
				</div>
			</div>
			<div className="flex justify-start font-size-14 line-height-22 padding-left-16 margin-bottom-20">
				<div className="color-86909c margin-right-4">顶部KV：</div>
				<div className="">
					<Image src={detail.kvImageUrl} alt="" width={200} className="width-60 border-radius-8" />
				</div>
			</div>
			<div className="flex justify-start font-size-14 line-height-22 padding-left-16 margin-bottom-20">
				<div className="color-86909c margin-right-4">中间图：</div>
				<div className="">
					<Image src={detail.centerImageUrl} alt="" width={200} className="width-60 border-radius-8" />
				</div>
			</div>
			<div className="flex justify-start font-size-14 line-height-22 padding-left-16 margin-bottom-20">
				<div className="color-86909c margin-right-4">微信分享标题：</div>
				<div className="">{detail.shareTitle || '--'}</div>
			</div>
			<div className="flex justify-start font-size-14 line-height-22 padding-left-16 margin-bottom-20">
				<div className="color-86909c margin-right-4">微信分享图片：</div>
				<div className="">
					{detail.shareImageUrl ? <Image src={detail.shareImageUrl} alt="" width={120} className="width-60 border-radius-8" /> : '--'}
				</div>
			</div>
			<div className="flex justify-start font-size-14 line-height-22 padding-left-16 margin-bottom-20">
				<div className="color-86909c margin-right-4">朋友圈分享标题：</div>
				<div className="">{detail.shareTimelineTitle || '--'}</div>
			</div>
			<div className="flex justify-start font-size-14 line-height-22 padding-left-16 margin-bottom-20">
				<div className="color-86909c margin-right-4">朋友圈分享图片：</div>
				<div className="">
					{detail.shareTimelineImageUrl ? (
						<Image src={detail.shareTimelineImageUrl} alt="" width={120} className="width-60 border-radius-8" />
					) : (
						'--'
					)}
				</div>
			</div>
			<div className="flex align-start justify-start font-size-14 line-height-22 padding-left-16 margin-bottom-20">
				<div className="color-86909c margin-right-4">格子配置：</div>
				<div className="flex-sub">
					<Table rowKey="id" dataSource={detail.list || []} pagination={false} scroll={{ x: 'max-content' }}>
						<Table.Column
							title="序号"
							key="index"
							width={60}
							render={(_, __, index) => {
								return index + 1;
							}}
						/>
						<Table.Column
							title="icon图标"
							dataIndex="icon"
							render={(text) => <Image src={text} alt="" width={60} className="width-60 border-radius-8" />}
						/>
						<Table.Column title="名称" dataIndex="name" render={(text) => <div className="max-width-240">{text}</div>} />
						<Table.Column
							title="跳转方式"
							dataIndex="pathType"
							render={(text) => <div className="max-width-240">{(pathTypeList.find((ov) => ov.value == text) || {}).label || ''}</div>}
						/>
						<Table.Column title="跳转地址" dataIndex="path" render={(text) => <div className="max-width-240">{text}</div>} />
						<Table.Column
							title="详情图片"
							dataIndex="detailImageUrl"
							render={(text) => <Image src={text} alt="" width={60} className="width-60 border-radius-8" />}
						/>
						<Table.Column title="排序" dataIndex="sort" render={(text) => <div className="max-width-240">{text}</div>} />
					</Table>
				</div>
			</div>
		</div>
	);
};

// 跳转指定配置
const ActivityConfig3 = (props = {}) => {
	const { linkTo } = useRouterLink();

	const detail = props.detail || {};
	return (
		<div className="bg-color-ffffff border-radius-8 padding-lr-24 padding-tb-20 margin-tb-16">
			<div className="flex align-center justify-between margin-bottom-20">
				<div className="font-size-18 line-height-26 font-weight-500">跳转指定配置</div>
				<Button
					type="link"
					icon={<EditOutlined />}
					onClick={() => {
						linkTo(`/newAchv/activity/curd?id=${props.id}`);
					}}
				>
					编辑信息
				</Button>
			</div>

			<div className="flex justify-start font-size-14 line-height-22 padding-left-16 margin-bottom-20">
				<div className="color-86909c margin-right-4">跳转指定配置：</div>
				<div className="">{detail.path || '--'}</div>
			</div>
			<div className="flex align-start justify-start font-size-14 line-height-22 padding-left-16 margin-bottom-20">
				<div className="color-86909c margin-right-4">格子配置：</div>
				<div className="flex-sub">
					<Table rowKey="id" dataSource={detail.list || []} pagination={false} scroll={{ x: 'max-content' }}>
						<Table.Column
							title="序号"
							key="index"
							width={60}
							render={(_, __, index) => {
								return index + 1;
							}}
						/>
						<Table.Column
							title="icon图标"
							dataIndex="icon"
							render={(text) => <Image src={text} alt="" width={60} className="width-60 border-radius-8" />}
						/>
						<Table.Column title="名称" dataIndex="name" render={(text) => <div className="max-width-240">{text}</div>} />
						<Table.Column
							title="跳转方式"
							dataIndex="pathType"
							render={(text) => <div className="max-width-240">{(pathTypeList.find((ov) => ov.value == text) || {}).label || ''}</div>}
						/>
						<Table.Column title="跳转地址" dataIndex="path" render={(text) => <div className="max-width-240">{text}</div>} />
						<Table.Column
							title="详情图片"
							dataIndex="detailImageUrl"
							render={(text) => <Image src={text} alt="" width={60} className="width-60 border-radius-8" />}
						/>
						<Table.Column title="排序" dataIndex="sort" render={(text) => <div className="max-width-240">{text}</div>} />
					</Table>
				</div>
			</div>
		</div>
	);
};

/* 配置详情 */
const ActivityConfig = (props = {}) => {
	const { detail, id } = props;
	const { linkTo } = useRouterLink();

	const keyToValue = (keys) => {
		const res = [];
		const keyMap = {
			1: '单位名称',
			2: '统一社会信用代码',
			industry: '行业',
			groupName: '组别',
			level: '奖项',
			share: '分享',
			download: '下载',
		};
		if (typeof keys === 'string') {
			return keyMap[keys];
		}
		keys.forEach((key) => {
			if (keyMap[key]) {
				res.push(keyMap[key]);
			}
		});
		return res.join('、');
	};
	return (
		<div className="bg-color-ffffff border-radius-8 padding-lr-24 padding-tb-20 margin-tb-16">
			<div className="flex align-center justify-between margin-bottom-20">
				<div className="font-size-18 line-height-26 font-weight-500">证书配置</div>
				<Button
					type="link"
					icon={<EditOutlined />}
					onClick={() => {
						linkTo(`/newAchv/activity/curd?id=${id}`);
					}}
				>
					编辑信息
				</Button>
			</div>
			{/* 活动证书配置 开始 */}
			<div className="flex justify-start font-size-14 line-height-22 padding-left-16 margin-bottom-20">
				<div className="color-86909c margin-right-4 line-height-46">证书配置：</div>
				<div className="flex-sub line-height-46">
					{detail.id ? (
						<Tabs
							defaultValue={'1'}
							className={'width-100per'}
							items={[
								{
									label: '证书查询页',
									key: '1',
									children: (
										<>
											<div className="flex justify-start font-size-14 line-height-22 padding-left-16 margin-bottom-20">
												<div className="color-86909c margin-right-4">查询条件：</div>
												<div className="">{keyToValue(detail.findConfig.findCondition)}</div>
											</div>
											<div className="flex justify-start font-size-14 line-height-22 padding-left-16 margin-bottom-20">
												<div className="color-86909c margin-right-4">背景图：</div>
												<div className="">
													{detail.findConfig.backgroundImg ? (
														<Image
															src={detail.findConfig.backgroundImg}
															alt=""
															width={120}
															className="width-60 border-radius-8"
														/>
													) : (
														'--'
													)}
												</div>
											</div>
											<div className="flex justify-start font-size-14 line-height-22 padding-left-16 margin-bottom-20">
												<div className="color-86909c margin-right-4">微信分享标题：</div>
												<div className="">{detail.findConfig.shareTitle}</div>
											</div>
											<div className="flex justify-start font-size-14 line-height-22 padding-left-16 margin-bottom-20">
												<div className="color-86909c margin-right-4">微信分享封面：</div>
												<div className="">
													{detail.findConfig.shareCoverImg ? (
														<Image
															src={detail.findConfig.shareCoverImg}
															alt=""
															width={120}
															className="width-60 border-radius-8"
														/>
													) : (
														'--'
													)}
												</div>
											</div>
											<div className="flex justify-start font-size-14 line-height-22 padding-left-16 margin-bottom-20">
												<div className="color-86909c margin-right-4">朋友圈分享标题：</div>
												<div className="">{detail.findConfig.friendShareTitle}</div>
											</div>
											<div className="flex justify-start font-size-14 line-height-22 padding-left-16 margin-bottom-20">
												<div className="color-86909c margin-right-4">朋友圈分享封面：</div>
												<div className="">
													{detail.findConfig.friendShareCoverImg ? (
														<Image
															src={detail.findConfig.friendShareCoverImg}
															alt=""
															width={120}
															className="width-60 border-radius-8"
														/>
													) : (
														'--'
													)}
												</div>
											</div>
										</>
									),
								},
								{
									label: '证书结果页',
									key: '2',
									children: (
										<>
											<div className="flex justify-start font-size-14 line-height-22 padding-left-16 margin-bottom-20">
												<div className="color-86909c margin-right-4">证书背景图：</div>
												<div className="">
													{detail.resultConfig.backgroundImg ? (
														<Image
															src={detail.resultConfig.backgroundImg}
															alt=""
															width={120}
															className="width-60 border-radius-8"
														/>
													) : (
														'--'
													)}
												</div>
											</div>
											<div className="flex justify-start font-size-14 line-height-22 padding-left-16 margin-bottom-20">
												<div className="color-86909c margin-right-4">显示数据：</div>
												<div className="">{(detail.resultConfig.showCols || [])?.join('、')}</div>
											</div>
											<div className="flex justify-start font-size-14 line-height-22 padding-left-16 margin-bottom-20">
												<div className="color-86909c margin-right-4">导入数据：</div>
												<a className="flex-sub" href={detail.resultConfig.nameListFile}>
													{detail.resultConfig.nameListFile}
												</a>
											</div>
											<div className="flex justify-start font-size-14 line-height-22 padding-left-16 margin-bottom-20">
												<div className="color-86909c margin-right-4">是否需要分享/下载：</div>
												<Space split={'、'} size={0} className="">
													{detail.resultConfig.shareStatus === 1 ? '分享' : null}
													{detail.resultConfig.downloadStatus === 1 ? '下载' : null}
												</Space>
											</div>
											{detail.resultConfig.downloadStatus === 1 && (
												<>
													<div className="flex justify-start font-size-14 line-height-22 padding-left-16 margin-bottom-20">
														<div className="color-86909c margin-right-4">朋友圈分享标题：</div>
														<div className="">{detail.resultConfig.shareTitle}</div>
													</div>
													<div className="flex justify-start font-size-14 line-height-22 padding-left-16 margin-bottom-20">
														<div className="color-86909c margin-right-4">朋友圈分享封面：</div>
														<div className="">
															{detail.resultConfig.shareCoverImg ? (
																<Image
																	src={detail.resultConfig.shareCoverImg}
																	alt=""
																	width={120}
																	className="width-60 border-radius-8"
																/>
															) : (
																'--'
															)}
														</div>
													</div>
													<div className="flex justify-start font-size-14 line-height-22 padding-left-16 margin-bottom-20">
														<div className="color-86909c margin-right-4">朋友圈分享标题：</div>
														<div className="">{detail.resultConfig.friendShareTitle}</div>
													</div>
													<div className="flex justify-start font-size-14 line-height-22 padding-left-16 margin-bottom-20">
														<div className="color-86909c margin-right-4">朋友圈分享封面：</div>
														<div className="">
															{detail.resultConfig.friendShareCoverImg ? (
																<Image
																	src={detail.resultConfig.friendShareCoverImg}
																	alt=""
																	width={120}
																	className="width-60 border-radius-8"
																/>
															) : (
																'--'
															)}
														</div>
													</div>
												</>
											)}
										</>
									),
								},
							]}
						/>
					) : (
						'未配置'
					)}
				</div>
			</div>
			{/* 活动证书配置 结束 */}
		</div>
	);
};
export default Index;
