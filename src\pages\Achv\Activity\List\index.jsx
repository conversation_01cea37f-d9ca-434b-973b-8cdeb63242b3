import { useState } from 'react';
import { Table, Button, Popconfirm, Space, Form, Input, Row, Col, Affix, message, Select, Image, Upload } from 'antd';
import { useTableData } from '@/hook/useTableData';
import { useRouterLink } from '@/hook/useRouter';

import { gridPage as getTablePageData, delActivity as delTableItemData, activityEnterpriseImport } from '@/api/Achv/Activity/index';

// 推荐状态
export const recommendStatusData = [
	{
		label: '全部',
		value: '',
	},
	{
		label: '已推荐',
		value: 1,
	},
	{
		label: '未推荐',
		value: 0,
	},
];

const Index = () => {
	const { linkTo, searchParams } = useRouterLink();
	const id = searchParams.get('id') || undefined;
	const [recommendStatus, setRecommendStatus] = useState(searchParams.get('recommendStatus') - 0 || '');

	const { form, dataSource, pagination, changePage, delTableData, onSearch, onReset } = useTableData({
		params: { recommendStatus, id },
		getTablePageData,
		delTableItemData,
	});

	return (
		<div className="">
			<div className="flex flex-shrink align-center margin-bottom-4 height-48 font-size-22 font-weight-500 color-1d2129">活动管理</div>
			{/* Tabs & 功能按钮 开始 */}
			<Affix offsetTop={0}>
				<div className="flex align-center justify-between padding-24 border-radius-8 bg-color-ffffff border-solid-bottom-f2f3f5">
					<div className="flex align-center">
						{recommendStatusData.map((ov) => (
							<div
								key={ov.value}
								className={`a margin-right-40 font-size-18 font-weight-500 ${recommendStatus === ov.value ? 'color-165dff' : ''}`}
								onClick={() => setRecommendStatus(ov.value)}
							>
								{ov.label}
							</div>
						))}
					</div>
					<Space size={16}>
						<Button
							type="primary"
							onClick={() => {
								linkTo(`/newAchv/activity/curd`);
							}}
						>
							新建活动
						</Button>

						<Upload
							fileList={[]}
							showUploadList={false}
							beforeUpload={(file, fileList) => {
								const formData = new FormData();
								formData.append('file', file);
								activityEnterpriseImport(formData).then((res) => {
									message.success('上传成功');
								});
								return false;
							}}
							multiple={false}
							maxCount={undefined}
						>
							<Button type="primary">赛事分数上传</Button>
						</Upload>
					</Space>
				</div>
			</Affix>
			{/* Tabs & 功能按钮 结束 */}

			<div className="flex-sub margin-top-18 padding-24 border-radius-8 bg-color-ffffff">
				{/* 筛选条件 开始 */}
				<div className="flex justify-between margin-bottom-18">
					<div className="flex-sub">
						<Form form={form} labelAlign="right" layout="inline" initialValues={{}}>
							<Row className="width-100per" gutter={[12, 12]}>
								<Col span={8}>
									<Form.Item name="keywords" label="活动名称">
										<Input placeholder="请输入活动名称" />
									</Form.Item>
								</Col>
								{/* <Col span={8}>
									<Form.Item name="releaseType" label="活动类型">
										<Select
											options={[
												{ label: '九宫格活动', value: 2 },
												{ label: '普通活动', value: 1 },
												{ label: '跳转指定地址', value: 3 },
											]}
											allowClear
											placeholder="请选择活动类型"
										/>
									</Form.Item>
								</Col> */}
							</Row>
						</Form>
					</div>
					<div className="margin-left-12 padding-left-12 border-solid-left-e5e6eb">
						<Space size={16}>
							<Button onClick={onReset}>重置</Button>
							<Button type="primary" onClick={onSearch}>
								查询
							</Button>
						</Space>
					</div>
				</div>
				{/* 筛选条件 结束 */}

				{/* 表格 开始 */}
				<Table rowKey="id" dataSource={dataSource} pagination={pagination} onChange={changePage} scroll={{ x: 'max-content' }}>
					<Table.Column
						title="序号"
						key="index"
						width={60}
						fixed="left"
						render={(_, __, index) => {
							return index + 1;
						}}
					/>

					<Table.Column title="活动名称" dataIndex="title" render={(text) => <div className="max-width-240">{text}</div>} />

					<Table.Column
						title="活动封面"
						dataIndex="coverImageUrl"
						render={(text) => <Image src={text} alt="" width={114} className="width-80 border-radius-8" />}
					/>
					<Table.Column
						title="活动类型"
						dataIndex="activityType"
						align="center"
						render={(_, record) => {
							let activityType = 2;
							try {
								const introduce = JSON.parse(record.introduce);
								console.log(introduce);

								if (introduce.path) {
									activityType = 3;
								}
								if (introduce.xx) {
									activityType = 1;
								}
							} catch (error) {}
							return (
								<div className={`tag-status-small-${['', 'primary', 'warning', '700eb2'][activityType]}`}>
									{['--', '普通活动', '九宫格活动', '跳转指定地址'][activityType || 0]}
								</div>
							);
						}}
					/>
					<Table.Column
						title="是否推荐"
						dataIndex="recommendStatus"
						align="center"
						render={(text) => {
							return <div className={`tag-status-small-${['warning', 'primary'][text]}`}>{text === 0 ? '否' : '是'}</div>;
						}}
					/>
					<Table.Column
						title="流量统计"
						dataIndex="viewNum"
						align="center"
						render={(text) => {
							return text || '-';
						}}
					/>
					<Table.Column
						title="操作"
						key="option"
						dataIndex="option"
						align="center"
						fixed="right"
						width={200}
						render={(_, record) => {
							return (
								<>
									<Button type="link" size="small" onClick={() => linkTo(`/newAchv/activity/detail?id=${record.id}`)}>
										详情/编辑
									</Button>
									<Popconfirm
										title="提示"
										description="确定删除吗？"
										onConfirm={() => {
											delTableData(record.id);
										}}
										okText="确定"
										cancelText="取消"
									>
										<Button type="link" size="small">
											删除
										</Button>
									</Popconfirm>
								</>
							);
						}}
					/>
				</Table>
				{/* 表格 结束 */}
			</div>
		</div>
	);
};

export default Index;
