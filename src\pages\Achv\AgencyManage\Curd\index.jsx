import { useEffect, useState } from 'react';
import { useRouterLink } from '@/hook/useRouter';

import { Button, Space, Form, Input, Cascader, message, Affix, Anchor, Select } from 'antd';

import UploadImg from '@/components/UploadImg';

import { getThreeLevelData } from '@/api/common';
import { addAgency as addTableItemData, updateAgency as updateTableItemData, getAgency as getTableItemData } from '@/api/Achv/AgencyManage/index';

import { getCategoryValueList } from '@/utils/achv';

const Index = () => {
	const { linkTo, searchParams } = useRouterLink();
	const id = searchParams.get('id');
	const [form] = Form.useForm();

	// 取消
	const onCancel = () => {
		linkTo(-1);
	};

	// 提交
	const onSubmit = () => {
		form.validateFields().then((values) => {
			const params = { ...values };
			delete params.tempArea;
			if (id) {
				updateTableItemData(params).then(() => {
					message.success('修改成功');
					setTimeout(() => {
						linkTo(-1);
					}, 500);
				});
			} else {
				addTableItemData(params).then(() => {
					message.success('添加成功');
					setTimeout(() => {
						linkTo(-1);
					}, 500);
				});
			}
		});
	};

	// 获取详情
	const getDetail = () => {
		if (id) {
			getTableItemData({ id, isUpdate: 1 }).then((res) => {
				const resData = res.data || {};

				// 处理区域
				resData.tempArea = [resData.provinceCode, resData.cityCode];

				form.setFieldsValue(resData);
			});
		}
	};

	useEffect(() => {
		getDetail();
	}, []);

	return (
		<div className="">
			<div className="flex flex-shrink align-center margin-bottom-4 height-48 font-size-22 font-weight-500 color-1d2129">
				<Space size={10}>
					<div className="a color-86909c" onClick={() => linkTo('/newAchv/agencyManage')}>
						机构管理
					</div>
					<div className="color-86909c">/</div>
					<div>机构编辑</div>
				</Space>
			</div>

			<div id="section1"></div>
			{/* Tabs & 功能按钮 开始 */}
			<Affix style={{ zIndex: 1000, position: 'relative' }}>
				<div className="flex align-center justify-between padding-24 border-radius-8 border-solid-bottom-f2f2f2 bg-color-ffffff">
					<div className="flex align-center justify-between color-1d2129 line-height-26 font-size-18 font-weight-500">
						<Anchor
							affix={false}
							rootClassName="custom-anchor-box"
							direction="horizontal"
							replace
							targetOffset={80}
							items={[
								{
									key: 'section1',
									href: '#section1',
									title: <div className="margin-right-40 font-size-16 font-weight-500">基本信息</div>,
								},
								{
									key: 'section2',
									href: '#section2',
									title: <div className="margin-right-40 font-size-16 font-weight-500">联系信息</div>,
								},
							]}
						/>
					</div>
					<Space size={16}>
						<Button onClick={onCancel}>取消</Button>
						<Button type="primary" onClick={onSubmit}>
							保存
						</Button>
					</Space>
				</div>
			</Affix>
			{/* Tabs & 功能按钮 结束 */}

			<Form
				form={form}
				className="antd-form-box"
				labelAlign="right"
				labelCol={{ style: { width: '160px' } }}
				initialValues={{
					id: '',
					brokerTypeData: 1,
					brokerCertificateStatus: 1,
					brokerCertificateLevel: 1,
					recommendStatus: 1,
				}}
			>
				<Form.Item hidden name="id">
					<Input />
				</Form.Item>

				{/* 基本信息 开始 */}
				<div className="margin-top-20 padding-20 border-radius-8 bg-color-ffffff">
					<div className="padding-bottom-30 line-height-26 font-size-18 font-weight-500 color-1d2129">基本信息</div>
					<BrokerForm1 form={form} />
				</div>
				{/* 基本信息 结束 */}

				{/* 联系信息 开始 */}
				<div id="section2"></div>
				<div className="margin-top-20 padding-20 border-radius-8 bg-color-ffffff">
					<div className="padding-bottom-30 line-height-26 font-size-18 font-weight-500 color-1d2129">联系信息</div>
					<BrokerForm2 form={form} />
				</div>
				{/* 联系信息 结束 */}
			</Form>
		</div>
	);
};

// 基本信息
const BrokerForm1 = (props = {}) => {
	const [areaOptions, setAreaOptions] = useState([]);
	const [agencyTypeOptions, setAgencyTypeOptions] = useState([]);
	const [studyDirectionOptions, setStudyDirectionOptions] = useState([]);

	useEffect(() => {
		getThreeLevelData({ level: 2 }).then((res) => {
			setAreaOptions(res.data || []);
		});

		getCategoryValueList('agency_type').then((res) => {
			setAgencyTypeOptions(res);
		});

		getCategoryValueList('ttchannels_area').then((res) => {
			setStudyDirectionOptions(res);
		});
	}, []);

	return (
		<>
			<Form.Item label="机构logo" name="logo">
				<UploadImg size={3} width={140} height={140} tips="建议尺寸：200px*200px" cropperProps={{ width: 140, height: 140 }} />
			</Form.Item>
			<Form.Item label="机构名称" name="name" rules={[{ required: true, message: '请输入机构名称' }]}>
				<Input className="input-box" placeholder="请输入机构名称" />
			</Form.Item>
			<Form.Item label="机构简介" name="introduction">
				<Input.TextArea rows={4} placeholder="请输入机构简介" />
			</Form.Item>
			<Form.Item label="机构类型" name="agencyTypeList">
				<Select className="Select-box" mode="multiple" options={agencyTypeOptions} placeholder="请选择机构类型" />
			</Form.Item>
			<Form.Item label="研究领域" name="studyDirectionList">
				<Select className="input-box" mode="multiple" options={studyDirectionOptions} placeholder="请选择研究领域" />
			</Form.Item>
			<Form.Item hidden name="provinceCode">
				<Input />
			</Form.Item>
			<Form.Item hidden name="cityCode">
				<Input />
			</Form.Item>
			<Form.Item label="所属区域" name="tempArea">
				<Cascader
					className="cascader-box"
					options={areaOptions}
					placeholder="请选择所属区域"
					displayRender={(label) => label.filter((ov) => ov).join('-')}
					onChange={(e = [undefined, undefined]) => {
						props.form.setFieldValue('provinceCode', e[0]);
						props.form.setFieldValue('cityCode', e[1]);
					}}
				/>
			</Form.Item>
			<Form.Item label="机构地址" name="address">
				<Input className="input-box" placeholder="请输入机构地址" />
			</Form.Item>
			<Form.Item label="主营业务" name="mainBusiness" help="如果有多个 请用 | 隔开">
				<Input.TextArea rows={4} placeholder="请输入主营业务" />
			</Form.Item>
			<Form.Item label="机构营业执照" name="businessLicense">
				<UploadImg size={3} width={200} height={300} tips="建议尺寸：200px*300px" />
			</Form.Item>
			<Form.Item label="AI检索标签" name="tagDesc">
				<Input className="input-box" placeholder="请输入AI检索标签，中文逗号分割 例：技术、项目" />
			</Form.Item>
		</>
	);
};

// 联系信息
const BrokerForm2 = (props = {}) => {
	return (
		<>
			<Form.Item label="联系人" name="contacts">
				<Input className="input-box" placeholder="请输入联系人" />
			</Form.Item>
			<Form.Item label="联系电话" name="phoneNo">
				<Input className="input-box" placeholder="请输入联系电话" />
			</Form.Item>
			<Form.Item label="联系人职位" name="contactsPosition">
				<Input className="input-box" placeholder="请输入联系人职位" />
			</Form.Item>
		</>
	);
};

export default Index;
