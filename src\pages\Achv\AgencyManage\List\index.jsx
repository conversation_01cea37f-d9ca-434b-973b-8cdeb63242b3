import { useState, useEffect } from 'react';
import { Table, Button, Popconfirm, Space, Form, Input, Row, Col, Affix, Image, Cascader, Switch, message } from 'antd';
import { useTableData } from '@/hook/useTableData';
import { useRouterLink } from '@/hook/useRouter';
import { useConfig } from '@/hook/Achv/useConfig';

import {
	pageAgency as getTablePageData,
	batchDelAgency as delTableItemData,
	exportAgency as exportTableData,
	updateRankingNum as batchUpdateSort,
	auditDataStatistics,
	updateHideStatusChange,
	updateRecommendStatusChange,
} from '@/api/Achv/AgencyManage/index';
import { getThreeLevelData } from '@/api/common';

import { brokerAuditStatusData, brokerAuditStatusTextList } from '@/pages/Achv/config';

const Index = () => {
	const { linkTo, searchParams } = useRouterLink();
	const config = useConfig();

	const status = searchParams.get('auditStatus');
	const [auditStatus, setAuditStatus] = useState(status === null ? 3 : status - 0 || '');
	const { form, dataSource, pagination, changePage, getTableData, delTableData, exportData, onSearch, onReset, SortInput } = useTableData({
		params: { auditStatus },
		getTablePageData,
		delTableItemData,
		exportTableData,
		batchUpdateSort,
		pageCallback: (paramsData = {}) => {
			getStatisticsData({ ...paramsData });
		},
	});

	// 获取统计数据
	const [statistics, setStatistics] = useState({});
	const getStatisticsData = (paramsData) => {
		delete paramsData.auditStatus;
		delete paramsData.pageSize;
		delete paramsData.pageNum;
		delete paramsData.tempArea;
		auditDataStatistics(paramsData).then((res) => {
			setStatistics(res.data || {});
		});
	};

	// 获取地区选项数据
	const [areaOptions, setAreaOptions] = useState([]);
	const getAreaOptions = () => {
		getThreeLevelData({
			level: 2,
			defaultArea: config.mpConfig.defaultArea || {},
		}).then((res) => {
			setAreaOptions(res.data || []);
		});
	};

	// 修改推荐状态
	const updateRecommend = (record) => {
		updateRecommendStatusChange({ id: record.id, recommendShowStatus: record.recommendShowStatus == 1 ? 0 : 1 }).then(() => {
			message.success('操作成功');
			getTableData();
		});
	};

	// 修改显示状态
	const hideStatusChange = (record) => {
		updateHideStatusChange({ id: record.id, hideStatus: record.hideStatus == 1 ? 0 : 1 }).then(() => {
			message.success('操作成功');
			getTableData();
		});
	};

	useEffect(() => {
		if (config.mpConfig) {
			getAreaOptions();
		}
	}, [config.mpConfig]);

	return (
		<div className="">
			<div className="flex flex-shrink align-center margin-bottom-4 height-48 font-size-22 font-weight-500 color-1d2129">机构管理</div>
			{/* Tabs & 功能按钮 开始 */}
			<Affix offsetTop={0}>
				<div className="flex align-center justify-between padding-24 border-radius-8 bg-color-ffffff border-solid-bottom-f2f3f5">
					<div className="flex align-center">
						{brokerAuditStatusData.map((ov) => (
							<div
								key={ov.value}
								className={`a margin-right-40 font-size-18 font-weight-500 ${auditStatus === ov.value ? 'color-165dff' : ''}`}
								onClick={() => setAuditStatus(ov.value)}
							>
								{ov.label}
								{statistics[ov.countName] > 0 ? `（${statistics[ov.countName]}）` : ''}
							</div>
						))}
					</div>
					<Space size={16}>
						<Button onClick={exportData}>批量导出</Button>
						<Button
							type="primary"
							onClick={() => {
								linkTo(`/newAchv/agencyManage/curd`);
							}}
						>
							新建机构
						</Button>
					</Space>
				</div>
			</Affix>
			{/* Tabs & 功能按钮 结束 */}
			<div className="flex-sub margin-top-18 padding-24 border-radius-8 bg-color-ffffff">
				{/* 筛选条件 开始 */}
				<div className="flex  justify-between margin-bottom-18">
					<div className="flex-sub">
						<Form
							form={form}
							labelAlign="right"
							layout="inline"
							initialValues={{
								provinceCodes: undefined,
								cityCodes: undefined,
								tempArea: [],
							}}
						>
							<Form.Item hidden name="provinceCodes">
								<Input />
							</Form.Item>
							<Form.Item hidden name="cityCodes">
								<Input />
							</Form.Item>
							<Row className="width-100per" gutter={[12, 12]}>
								<Col span={8}>
									<Form.Item name="keywords" label="机构名称">
										<Input placeholder="请输入机构名称" />
									</Form.Item>
								</Col>
								<Col span={8}>
									<Form.Item name="tempArea" label="所属区域">
										<Cascader
											options={areaOptions}
											placeholder="请选择所属区域"
											changeOnSelect
											displayRender={(label) => (label || []).filter((ov) => ov).join('-')}
											onChange={(e = [undefined, undefined]) => {
												form.setFieldValue('provinceCodes', e[0] ? [e[0]] : undefined);
												form.setFieldValue('cityCodes', e[1] ? [e[1]] : undefined);
											}}
										/>
									</Form.Item>
								</Col>
							</Row>
						</Form>
					</div>
					<div className="margin-left-12 padding-left-12 border-solid-left-e5e6eb">
						<Space size={16}>
							<Button onClick={onReset}>重置</Button>
							<Button type="primary" onClick={onSearch}>
								查询
							</Button>
						</Space>
					</div>
				</div>
				{/* 筛选条件 结束 */}
				{/* 表格 开始 */}
				<Table rowKey="id" dataSource={dataSource} pagination={pagination} onChange={changePage} scroll={{ x: 'max-content' }}>
					<Table.Column
						title="序号"
						key="index"
						width={60}
						fixed="left"
						render={(_, __, index) => {
							return index + 1;
						}}
					/>
					<Table.Column
						title="logo"
						key="logo"
						width={100}
						render={(_, record) => {
							return (
								(record.logo && (
									<div className="flex align-center justify-center width-100 height-100 overflow-hidden">
										<Image
											width={100}
											src={record.logo}
											fallback="https://gbac-produce.oss-cn-shenzhen.aliyuncs.com/static-assets/gbac-achv-applet/Public/default-achv-cover.png"
										/>
									</div>
								)) ||
								null
							);
						}}
					/>
					<Table.Column title="机构名称" dataIndex="name" />
					<Table.Column title="机构类型" dataIndex="agencyTypeList" render={(text) => (text || []).join('、') || '--'} />
					<Table.Column title="研究领域" dataIndex="studyDirectionList" render={(text) => (text || []).join('、') || '--'} />
					<Table.Column
						title="所属区域"
						dataIndex="provinceName"
						render={(_, record) => {
							return [record.provinceCodeName, record.cityCodeName, record.areaCodeName].filter((ov) => ov).join('-');
						}}
					/>
					<Table.Column title="地址" dataIndex="address" render={(text) => text || '--'} />
					<Table.Column
						title="排序"
						align="center"
						dataIndex="rankingNum"
						render={(_, record) => {
							return <SortInput record={record} />;
						}}
					/>
					<Table.Column
						title="审核状态"
						dataIndex="auditStatus"
						align="center"
						render={(text) => {
							return (
								<div className={`tag-status-small-${['default', 'warning', 'error', 'primary'][text]}`}>
									{brokerAuditStatusTextList[text] || ''}
								</div>
							);
						}}
					/>
					<Table.Column
						title="推荐状态"
						dataIndex="recommendShowStatus"
						align="center"
						render={(_, record) => {
							return (
								<Switch
									checked={record.recommendShowStatus == 1}
									disabled={record.auditStatus !== 3}
									title={record.auditStatus !== 3 ? '仅审核通过后才能操作' : ''}
									onChange={() => {
										updateRecommend(record);
									}}
								/>
							);
						}}
					/>
					<Table.Column
						title="隐藏状态"
						dataIndex="hideStatus"
						align="center"
						render={(_, record) => {
							return (
								<Switch
									checked={record.hideStatus == 1}
									disabled={record.auditStatus !== 3}
									title={record.auditStatus !== 3 ? '仅审核通过后才能操作' : ''}
									onChange={() => {
										hideStatusChange(record);
									}}
								/>
							);
						}}
					/>
					<Table.Column
						title="操作"
						key="option"
						dataIndex="option"
						align="center"
						fixed="right"
						render={(_, record) => {
							return (
								<>
									<Button
										type="link"
										size="small"
										onClick={() => linkTo(`/newAchv/agencyManage/detail?id=${record.id}&fromList=1`)}
									>
										编辑/审核
									</Button>
									<Popconfirm
										title="提示"
										description="确定删除吗？"
										onConfirm={() => {
											delTableData(record.id);
										}}
										okText="确定"
										cancelText="取消"
									>
										<Button type="link" size="small">
											删除
										</Button>
									</Popconfirm>
								</>
							);
						}}
					/>
				</Table>
				{/* 表格 结束 */}
			</div>
		</div>
	);
};

export default Index;
