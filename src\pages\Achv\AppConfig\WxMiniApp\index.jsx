import { useEffect } from 'react';
import { Button, Card, Input, Form, Space } from 'antd';

import { getConfig, saveConfig } from '@/api/Achv/common';

const Index = () => {
	const [form] = Form.useForm();

	const [otherConfig, setOtherConfig] = useState({});

	useEffect(() => {
		getConfig().then((res) => {
			setOtherConfig(res.data);
			try {
				const wxMiniApp = JSON.parse(res.data.wxMiniApp || '{}');
				form.setFieldValue(wxMiniApp);
			} catch (error) {
				console.log(error);
			}
		});
	}, []);
	return (
		<div className="flex-sub flex flex-direction-column">
			<div className="flex flex-shrink align-center margin-bottom-4 height-48 font-size-22 font-weight-500 color-1d2129">运营数据管理</div>

			<Space direction="vertical" size={18}>
				<Config1 form={form} />
			</Space>
		</div>
	);
};

const Config1 = (props = {}) => {
	return (
		<Card
			title="小程序首页导航"
			extra={
				<Button type="primary" onClick={() => {}}>
					保存
				</Button>
			}
		>
			<Form form={props.form}>
				<Form.Item label="" name="tempDate"></Form.Item>
			</Form>
		</Card>
	);
};

export default Index;
