import { useEffect, useState } from 'react';
import { useRouterLink } from '@/hook/useRouter';
import { Button, Space, Form, Input, message, Affix, InputNumber, Radio } from 'antd';

import UploadImg from '@/components/UploadImg';

import { addBanner, updateBanner, getBanner } from '@/api/Achv/Banner';

const Index = () => {
	const { linkTo, searchParams } = useRouterLink();
	const id = searchParams.get('id') || '';
	const [form] = Form.useForm();
	const type = Form.useWatch('type', form);
	// 跳转地址
	const linkToPath = '/newAchv/banner/course';

	// 禁止编辑发布类型
	const [hash, setHash] = useState('');

	// 取消
	const onCancel = () => {
		linkTo(-1);
	};

	// 提交
	const onSubmit = () => {
		form.validateFields()
			.then((values) => {
				(values.id ? updateBanner : addBanner)(values).then(() => {
					message.success(values.id ? '修改成功' : '添加成功');
					setTimeout(() => {
						linkTo(-1);
					}, 500);
				});
			})
			.catch((error) => {
				console.log(error);
				form.scrollToField(error.errorFields[0].name, {
					block: 'center',
				});
			});
	};

	// 获取详情
	const getDetail = () => {
		if (id) {
			getBanner({ id }).then((res) => {
				const resData = res.data || {};
				form.setFieldsValue(resData);
			});
		}
	};

	useEffect(() => {
		const hash = window.location.hash.replace('#', '');
		setHash(hash || 'section1');

		getDetail();
	}, []);

	// 跳转到指定锚点
	useEffect(() => {
		if (hash) {
			setTimeout(() => {
				const element = document.getElementById(hash);
				if (element) {
					element.scrollIntoView({ behavior: 'smooth' });
				}
			}, 500);
		}
	}, [hash]);

	return (
		<div className="">
			<div className="flex flex-shrink align-center margin-bottom-4 height-48 font-size-22 font-weight-500 color-1d2129">
				<Space size={10}>
					<div className="a color-86909c" onClick={() => linkTo(linkToPath)}>
						Banner管理
					</div>
					<div className="color-86909c">/</div>
					<div>{id ? 'Banner编辑' : '新增Banner'}</div>
				</Space>
			</div>

			{/* Tabs & 功能按钮 开始 */}
			<div id="section1"></div>
			<Affix offsetTop={0}>
				<div className="flex align-center justify-between padding-24 border-radius-8 border-solid-bottom-f2f2f2 bg-color-ffffff">
					<div className="flex align-center">
						{['基本信息', '跳转配置'].map((ov, oi) => {
							const currentHash = `section${oi + 1}`;
							return (
								<div
									key={oi}
									className={`a margin-right-40 font-size-18 font-weight-500 ${hash === currentHash ? 'color-165dff' : ''}`}
									onClick={() => {
										setHash(currentHash);
									}}
								>
									{ov}
								</div>
							);
						})}
					</div>
					<Space size={16}>
						<Button onClick={onCancel}>取消</Button>
						<Button type="primary" onClick={onSubmit}>
							保存
						</Button>
					</Space>
				</div>
			</Affix>
			{/* Tabs & 功能按钮 结束 */}

			<Form
				form={form}
				className="antd-form-box"
				labelAlign="right"
				labelCol={{ style: { width: '120px' } }}
				initialValues={{
					id: '',
					staffList: [],
					serviceList: [],
					managerList: [],
				}}
			>
				<Form.Item hidden name="id">
					<Input />
				</Form.Item>
				<Form.Item hidden name="rankingNum">
					<Input />
				</Form.Item>
				<Form.Item hidden name="showStatus">
					<Input />
				</Form.Item>
				{/* 基本信息 开始 */}
				<div className="margin-top-20 padding-20 border-radius-8 bg-color-ffffff">
					<div className="padding-bottom-30 line-height-26 font-size-18 font-weight-500 color-1d2129">基本信息</div>
					<Form.Item label="Banner名称" name="title" rules={[{ required: true, message: '请输入Banner名称' }]}>
						<Input className="input-box" placeholder="请输入Banner名称" />
					</Form.Item>
					<Form.Item label="Banner简介" name="introduce">
						<Input className="input-box" placeholder="请输入Banner简介" />
					</Form.Item>
					<Form.Item label="Banner封面" name="bannerUrl" rules={[{ required: true, message: '请上传Banner封面' }]}>
						<UploadImg size={5} width={122} height={170} cropperProps={{ width: 244, height: 340 }} />
					</Form.Item>
					<Form.Item label="按钮图片" name="btnUrl" rules={[{ required: true, message: '请上传按钮图片' }]}>
						<UploadImg size={5} width={122} height={122} />
					</Form.Item>
				</div>
				{/* 基本信息 结束 */}

				{/* 跳转配置 开始 */}
				<div id="section2"></div>
				<div className="margin-top-20 padding-20 border-radius-8 bg-color-ffffff">
					<div className="padding-bottom-30 line-height-26 font-size-18 font-weight-500 color-1d2129">跳转配置</div>
					<Form.Item label="跳转类型" name="type" rules={[{ required: true, message: '请选择跳转类型' }]} initialValue={1}>
						<Radio.Group
							options={[
								{ label: '普通跳转', value: 1 },
								{ label: '重定向跳转', value: 2 },
								{ label: '跳转小程序', value: 3 },
								{ label: '只展示', value: 4 },
							]}
						></Radio.Group>
					</Form.Item>
					{type !== 4 && (
						<Form.Item label="登录状态" name="loginStatus" rules={[{ required: true, message: '请选择登录状态' }]} initialValue={0}>
							<Radio.Group
								options={[
									{ label: '不登录', value: 0 },
									{ label: '登录', value: 1 },
								]}
							></Radio.Group>
						</Form.Item>
					)}
					{type === 3 && (
						<Form.Item label="APPID" name="title" rules={[{ required: true, message: '请输入第三方微信小程序APPID' }]}>
							<Input className="input-box" placeholder="请输入第三方微信小程序APPID" />
						</Form.Item>
					)}
				</div>
				{/* 跳转配置 结束 */}
			</Form>
		</div>
	);
};

export default Index;
