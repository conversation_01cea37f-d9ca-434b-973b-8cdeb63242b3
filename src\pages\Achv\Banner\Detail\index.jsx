import { useEffect, useState } from 'react';
import { Button, Affix, Anchor, Image } from 'antd';
import { EditOutlined } from '@ant-design/icons';

import { getBanner } from '@/api/Achv/Banner/index';

import { useRouterLink } from '@/hook/useRouter';

const Index = () => {
	const { linkTo, searchParams } = useRouterLink();

	const id = searchParams.get('id') || '';
	const [detail, setDetail] = useState({});
	const getDetail = () => {
		getBanner({ id }).then((res) => {
			const resData = res.data || {};

			setDetail(resData);
		});
	};

	useEffect(() => {
		getDetail();
	}, []);

	return (
		<div>
			<div className="flex flex-shrink align-center margin-bottom-4 height-48 font-size-22 font-weight-500">
				<div className="a color-86909c" onClick={() => linkTo(-1)}>
					Banner管理
				</div>
				<div className="margin-lr-10 color-86909c">/</div>
				<div className="color-1d2129">Banner详情</div>
			</div>

			<div id="section1"></div>
			<Affix>
				<div className="height-66 flex align-center justify-between bg-color-ffffff border-radius-8 padding-lr-24 border-solid-bottom-f2f3f5">
					<div className="flex align-center justify-between color-1d2129 line-height-26 font-size-18 font-weight-500">
						<Anchor
							affix={false}
							rootClassName="anchor-header-tabBar-box"
							direction="horizontal"
							replace
							targetOffset={80}
							items={[
								{
									key: 'section1',
									href: '#section1',
									title: <div className="margin-right-40 font-size-16 font-weight-500">基本信息</div>,
								},
								{
									key: 'section2',
									href: '#section2',
									title: <div className="margin-right-40 font-size-16 font-weight-500">跳转配置</div>,
								},
							]}
						/>
					</div>
				</div>
			</Affix>

			<div className="bg-color-ffffff border-radius-8 padding-lr-24 padding-tb-20 margin-tb-16">
				<div className="flex align-center justify-between margin-bottom-20">
					<div className="font-size-18 line-height-26 font-weight-500">基本信息</div>
					<Button
						type="link"
						icon={<EditOutlined />}
						onClick={() => {
							linkTo(`/newAchv/banner/curd?id=${id}&tabKey=1`);
						}}
					>
						编辑信息
					</Button>
				</div>

				<div className="flex align-center justify-start font-size-14 line-height-22 padding-left-16 margin-bottom-20">
					<div className="color-86909c margin-right-4">活动名称：</div>
					<div className="">{detail.title || '--'}</div>
				</div>
				<div className="flex justify-start font-size-14 line-height-22 padding-left-16 margin-bottom-20">
					<div className="color-86909c margin-right-4">活动封面：</div>
					<div className="">
						<Image src={detail.coverImageUrl} alt="" width={200} className="width-80 border-radius-8" />
					</div>
				</div>
				<div className="flex align-center justify-start font-size-14 line-height-22 padding-left-16 margin-bottom-20">
					<div className="color-86909c margin-right-4">推荐状态：</div>
					<div className="">
						<div className={`tag-status-${['warning', 'primary'][detail.recommendStatus]}`}>
							{detail.recommendStatus === 0 ? '非推荐' : '推荐'}
						</div>
					</div>
				</div>
				<div className="flex align-center justify-start font-size-14 line-height-22 padding-left-16 margin-bottom-20">
					<div className="color-86909c margin-right-4">活动类型：</div>
					<div className="">
						<div className={`tag-status-${['', 'primary', 'warning', '700eb2'][detail.activityType]}`}>
							{['--', '普通活动', '九宫格活动', '跳转指定地址'][detail.activityType || 0]}
						</div>
					</div>
				</div>
				<div className="flex align-center justify-start font-size-14 line-height-22 padding-left-16 margin-bottom-20">
					<div className="color-86909c margin-right-4">排序：</div>
					<div className="">{detail.rankingNum || '--'}</div>
				</div>
			</div>

			<div id="section2"></div>
			<div className="bg-color-ffffff border-radius-8 padding-lr-24 padding-tb-20 margin-tb-16">
				<div className="flex align-center justify-between margin-bottom-20">
					<div className="font-size-18 line-height-26 font-weight-500">跳转配置</div>
					<Button
						type="link"
						icon={<EditOutlined />}
						onClick={() => {
							linkTo(`/newAchv/activity/curd?id=${id}&tabKey=1`);
						}}
					>
						编辑信息
					</Button>
				</div>

				<div className="flex align-center justify-start font-size-14 line-height-22 padding-left-16 margin-bottom-20">
					<div className="color-86909c margin-right-4">活动名称：</div>
					<div className="">{detail.title || '--'}</div>
				</div>
				<div className="flex justify-start font-size-14 line-height-22 padding-left-16 margin-bottom-20">
					<div className="color-86909c margin-right-4">活动封面：</div>
					<div className="">
						<Image src={detail.coverImageUrl} alt="" width={200} className="width-80 border-radius-8" />
					</div>
				</div>
				<div className="flex align-center justify-start font-size-14 line-height-22 padding-left-16 margin-bottom-20">
					<div className="color-86909c margin-right-4">推荐状态：</div>
					<div className="">
						<div className={`tag-status-${['warning', 'primary'][detail.recommendStatus]}`}>
							{detail.recommendStatus === 0 ? '非推荐' : '推荐'}
						</div>
					</div>
				</div>
				<div className="flex align-center justify-start font-size-14 line-height-22 padding-left-16 margin-bottom-20">
					<div className="color-86909c margin-right-4">活动类型：</div>
					<div className="">
						<div className={`tag-status-${['', 'primary', 'warning', '700eb2'][detail.activityType]}`}>
							{['--', '普通活动', '九宫格活动', '跳转指定地址'][detail.activityType || 0]}
						</div>
					</div>
				</div>
				<div className="flex align-center justify-start font-size-14 line-height-22 padding-left-16 margin-bottom-20">
					<div className="color-86909c margin-right-4">排序：</div>
					<div className="">{detail.rankingNum || '--'}</div>
				</div>
			</div>
		</div>
	);
};
export default Index;
