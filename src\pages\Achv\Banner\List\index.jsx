import { Table, Button, Popconfirm, Space, Form, Input, Row, Col, Affix, message, Select, Image, Switch } from 'antd';
import { useTableData } from '@/hook/useTableData';
import { useRouterLink } from '@/hook/useRouter';

import {
	pageBanner as getTablePageData,
	batchDel as delTableItemData,
	updateRankingNum as batchUpdateSort,
	updateShowStatus,
} from '@/api/Achv/Banner/index';

const Index = () => {
	const { linkTo } = useRouterLink();

	const { form, dataSource, pagination, changePage, delTableData, onSearch, onReset, SortInput } = useTableData({
		getTablePageData,
		batchUpdateSort,
		delTableItemData,
	});

	// 修改显示状态
	const showStatusChange = (record) => {
		updateShowStatus({ id: record.id, showStatus: record.showStatus == 1 ? 0 : 1 }).then(() => {
			message.success('操作成功');
			getTableData();
		});
	};

	return (
		<div className="">
			<div className="flex flex-shrink align-center margin-bottom-4 height-48 font-size-22 font-weight-500 color-1d2129">Banner管理</div>
			{/* Tabs & 功能按钮 开始 */}
			<Affix offsetTop={0}>
				<div className="flex align-center justify-between padding-24 border-radius-8 bg-color-ffffff border-solid-bottom-f2f3f5">
					<div className="flex align-center"></div>
					<Space size={16}>
						<Button
							type="primary"
							onClick={() => {
								linkTo(`/newAchv/banner/curd`);
							}}
						>
							新建Banner
						</Button>
					</Space>
				</div>
			</Affix>
			{/* Tabs & 功能按钮 结束 */}

			<div className="flex-sub margin-top-18 padding-24 border-radius-8 bg-color-ffffff">
				{/* 筛选条件 开始 */}
				<div className="flex justify-between margin-bottom-18">
					<div className="flex-sub">
						<Form form={form} labelAlign="right" layout="inline" initialValues={{}}>
							<Row className="width-100per" gutter={[12, 12]}>
								<Col span={8}>
									<Form.Item name="title" label="Banner名称">
										<Input placeholder="请输入Banner名称" />
									</Form.Item>
								</Col>
								<Col span={8}>
									<Form.Item name="showStatus" label="显示状态">
										<Select
											options={[
												{ label: '隐藏', value: 0 },
												{ label: '显示', value: 1 },
											]}
											allowClear
											placeholder="请选择显示状态"
										/>
									</Form.Item>
								</Col>
							</Row>
						</Form>
					</div>
					<div className="margin-left-12 padding-left-12 border-solid-left-e5e6eb">
						<Space size={16}>
							<Button onClick={onReset}>重置</Button>
							<Button type="primary" onClick={onSearch}>
								查询
							</Button>
						</Space>
					</div>
				</div>
				{/* 筛选条件 结束 */}

				{/* 表格 开始 */}
				<Table rowKey="id" dataSource={dataSource} pagination={pagination} onChange={changePage} scroll={{ x: 'max-content' }}>
					<Table.Column
						title="序号"
						key="index"
						width={60}
						fixed="left"
						render={(_, __, index) => {
							return index + 1;
						}}
					/>

					<Table.Column title="Banner名称" dataIndex="title" render={(text) => <div className="max-width-240">{text}</div>} />

					<Table.Column
						title="Banner封面"
						dataIndex="bannerUrl"
						render={(text) => <Image src={text} alt="" width={114} className="width-80 border-radius-8" />}
					/>
					<Table.Column
						title="Banner类型"
						dataIndex="type"
						align="center"
						render={(text) => {
							return (
								<div className={`tag-status-small-${['', 'primary', 'warning', '700eb2', 'error'][text]}`}>
									{['--', '普通跳转', '重定向跳转', '跳转小程序', '只展示'][text || 0]}
								</div>
							);
						}}
					/>
					<Table.Column
						title="是否显示"
						dataIndex="showStatus"
						align="center"
						render={(_, record) => {
							return (
								<Switch
									checked={record.showStatus == 1}
									onChange={() => {
										showStatusChange(record);
									}}
								/>
							);
						}}
					/>
					<Table.Column
						title="排序"
						align="center"
						dataIndex="rankingNum"
						render={(_, record) => {
							return <SortInput record={record} />;
						}}
					/>
					<Table.Column
						title="操作"
						key="option"
						dataIndex="option"
						align="center"
						fixed="right"
						width={200}
						render={(_, record) => {
							return (
								<>
									<Button type="link" size="small" onClick={() => linkTo(`/newAchv/banner/detail?id=${record.id}`)}>
										详情/编辑
									</Button>
									<Popconfirm
										title="提示"
										description="确定删除吗？"
										onConfirm={() => {
											delTableData(record.id);
										}}
										okText="确定"
										cancelText="取消"
									>
										<Button type="link" size="small">
											删除
										</Button>
									</Popconfirm>
								</>
							);
						}}
					/>
				</Table>
				{/* 表格 结束 */}
			</div>
		</div>
	);
};

export default Index;
