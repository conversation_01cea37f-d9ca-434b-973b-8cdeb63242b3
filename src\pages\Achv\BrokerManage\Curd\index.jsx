import { useEffect, useState } from 'react';
import { useRouterLink } from '@/hook/useRouter';

import { Button, Space, Form, Input, Cascader, Radio, DatePicker, message, Affix, Anchor, Select, InputNumber, Card } from 'antd';
import { DeleteOutlined } from '@ant-design/icons';

import UploadImg from '@/components/UploadImg';
import FormComp from '@/components/FormComp';

import { getThreeLevelData } from '@/api/common';
import {
	brokerAdd as addTableItemData,
	brokerUpdate as updateTableItemData,
	getBrokerDetail as getTableItemData,
} from '@/api/Achv/BrokerManage/index';

import { getCategoryValueList } from '@/utils/achv';
import { getDictData } from '@/utils/dictionary';
import { brokerTypeData, brokerCertificateLevelData } from '@/pages/Achv/config';

import { useConfig } from '@/hook/Achv/useConfig';
import dayjs from 'dayjs';
const Index = () => {
	const { linkTo, searchParams } = useRouterLink();
	const [form] = Form.useForm();

	// 取消
	const onCancel = () => {
		linkTo(-1);
	};

	// 提交
	const onSubmit = () => {
		form.validateFields().then((values) => {
			const params = { ...values };
			delete params.tempArea;
			if (id) {
				updateTableItemData(params).then(() => {
					message.success('修改成功');
					setTimeout(() => {
						linkTo(-1);
					}, 500);
				});
			} else {
				addTableItemData(params).then(() => {
					message.success('添加成功');
					setTimeout(() => {
						linkTo(-1);
					}, 500);
				});
			}
		});
	};

	// 获取详情
	const getDetail = () => {
		const id = searchParams.get('id');
		if (id) {
			getTableItemData({ id, isUpdate: 1 }).then((res) => {
				const resData = res.data || {};

				// 处理区域
				resData.tempArea = [resData.provinceCode, resData.cityCode];

				form.setFieldsValue(resData);
			});
		}
	};

	useEffect(() => {
		getDetail();
	}, []);

	return (
		<div className="">
			<div className="flex flex-shrink align-center margin-bottom-4 height-48 font-size-22 font-weight-500 color-1d2129">
				<Space size={10}>
					<div className="a color-86909c" onClick={() => linkTo('/newAchv/brokerManage')}>
						经理人管理
					</div>
					<div className="color-86909c">/</div>
					<div>经理人编辑</div>
				</Space>
			</div>

			<div id="section1"></div>
			{/* Tabs & 功能按钮 开始 */}
			<Affix style={{ zIndex: 1000, position: 'relative' }}>
				<div className="flex align-center justify-between padding-24 border-radius-8 border-solid-bottom-f2f2f2 bg-color-ffffff">
					<div className="flex align-center justify-between color-1d2129 line-height-26 font-size-18 font-weight-500">
						<Anchor
							affix={false}
							rootClassName="custom-anchor-box"
							direction="horizontal"
							replace
							targetOffset={80}
							items={[
								{
									key: 'section1',
									href: '#section1',
									title: <div className="margin-right-40 font-size-16 font-weight-500">基本信息</div>,
								},
								{
									key: 'section2',
									href: '#section2',
									title: <div className="margin-right-40 font-size-16 font-weight-500">从业经验</div>,
								},
								{
									key: 'section3',
									href: '#section3',
									title: <div className="margin-right-40 font-size-16 font-weight-500">工作经验</div>,
								},
							]}
						/>
					</div>
					<Space size={16}>
						<Button onClick={onCancel}>取消</Button>
						<Button type="primary" onClick={onSubmit}>
							保存
						</Button>
					</Space>
				</div>
			</Affix>
			{/* Tabs & 功能按钮 结束 */}

			<Form
				form={form}
				className="antd-form-box"
				labelAlign="right"
				labelCol={{ style: { width: '180px' } }}
				initialValues={{
					id: '',
					brokerTypeData: 1,
					brokerCertificateStatus: 1,
					brokerCertificateLevel: 1,
					recommendStatus: 1,
					isWorkStatus: 1,
					areaCategoryList: [],
				}}
			>
				<Form.Item hidden name="id">
					<Input />
				</Form.Item>

				{/* 基本信息 开始 */}
				<div className="margin-top-20 padding-20 border-radius-8 bg-color-ffffff">
					<div className="padding-bottom-30 line-height-26 font-size-18 font-weight-500 color-1d2129">基本信息</div>
					<BrokerForm1 form={form} />
				</div>
				{/* 基本信息 结束 */}

				{/* 从业经验 开始 */}
				<div id="section2"></div>
				<div className="margin-top-20 padding-20 border-radius-8 bg-color-ffffff">
					<div className="padding-bottom-30 line-height-26 font-size-18 font-weight-500 color-1d2129">从业经验</div>
					<BrokerForm2 form={form} />
				</div>
				{/* 从业经验 结束 */}

				{/* 工作经验 开始 */}
				<div id="section3"></div>
				<div className="margin-top-20 padding-20 border-radius-8 bg-color-ffffff">
					<div className="padding-bottom-30 line-height-26 font-size-18 font-weight-500 color-1d2129">工作经验</div>
					<BrokerForm3 form={form} />
				</div>
				{/* 工作经验 结束 */}
			</Form>
		</div>
	);
};

// 基本信息
const BrokerForm1 = (props = {}) => {
	const config = useConfig();

	const [areaOptions, setAreaOptions] = useState([]);
	const [degreeOptions, setDegreeOptions] = useState([]);

	useEffect(() => {
		getThreeLevelData({ level: 2 }).then((res) => {
			setAreaOptions(res.data || []);
		});

		getCategoryValueList('degree').then((res) => {
			setDegreeOptions(res);
		});
	}, []);

	useEffect(() => {
		console.log(config);
	}, [config]);
	return (
		<>
			<Form.Item label="头像" name="wxAvatarUrl" rules={[{ required: true, message: '请上传头像' }]}>
				<UploadImg size={3} width={140} height={140} tips="建议尺寸：200px*200px" cropperProps={{ width: 140, height: 140 }} />
			</Form.Item>
			<Form.Item label="姓名" name="userName" rules={[{ required: true, message: '请输入姓名' }]}>
				<Input className="input-box" placeholder="请输入姓名" />
			</Form.Item>
			<Form.Item label="性别" name="gender" rules={[{ required: true, message: '请选择性别' }]}>
				<Radio.Group options={getDictData('gender')} />
			</Form.Item>
			<Form.Item label="出生年月" name="birthDay" rules={[{ required: true, message: '请选择出生年月' }]}>
				<FormComp.DatePicker picker="month" valueFormat="YYYY-MM" placeholder="请选择出生年月" />
			</Form.Item>
			<Form.Item hidden name="provinceCode">
				<Input />
			</Form.Item>
			<Form.Item hidden name="cityCode">
				<Input />
			</Form.Item>
			<Form.Item label="常住城市" name="tempArea" rules={[{ required: true, type: 'array', message: '请选择常住城市' }]}>
				<Cascader
					className="cascader-box"
					options={areaOptions}
					placeholder="请选择区域"
					displayRender={(label) => label.filter((ov) => ov).join('-')}
					onChange={(e = [undefined, undefined]) => {
						props.form.setFieldValue('provinceCode', e[0]);
						props.form.setFieldValue('cityCode', e[1]);
					}}
				/>
			</Form.Item>
			<Form.Item label="联系电话" name="phone" rules={[{ required: true, message: '请输入联系电话' }]}>
				<Input className="input-box" placeholder="请输入联系电话" />
			</Form.Item>
			<Form.Item label="学历" name="degreeId" rules={[{ required: true, message: '请选择学历' }]}>
				<Select className="input-box" options={degreeOptions} placeholder="请选择学历" />
			</Form.Item>
			<Form.Item
				label="用户类型"
				hidden={config && config.mpConfig && config.mpConfig.brokerForm && config.mpConfig.brokerForm.userType !== 1}
				name="userType"
				rules={[{ required: true, message: '请选择用户类型' }]}
			>
				<Radio.Group options={brokerTypeData} />
			</Form.Item>
		</>
	);
};

// 从业经验
const BrokerForm2 = (props = {}) => {
	const config = useConfig();
	const brokerCertificateStatus = Form.useWatch('brokerCertificateStatus', props.form);
	const [companyTypeOptions, setCompanyTypeOptions] = useState([]);
	const [areaCategoryListOptions, setAreaCategoryListOptions] = useState([]);

	useEffect(() => {
		getCategoryValueList('company_type').then((res) => {
			setCompanyTypeOptions(res);
		});
		getCategoryValueList('ttchannels_area').then((res) => {
			setAreaCategoryListOptions(res);
		});
	}, []);

	return (
		<>
			<Form.Item label="就职单位" name="companyName" rules={[{ required: true, message: '请输入就职单位' }]}>
				<Input className="input-box" placeholder="请输入就职单位" />
			</Form.Item>
			<Form.Item label="单位类型" name="companyTypeList" rules={[{ required: true, message: '请输入单位类型' }]}>
				<Select className="input-box" options={companyTypeOptions} placeholder="请输入单位类型" />
			</Form.Item>
			<Form.Item label="工作职务" name="positionName" rules={[{ required: true, message: '请输入工作职务' }]}>
				<Input className="input-box" placeholder="请输入工作职务" />
			</Form.Item>
			<Form.Item label="职称" name="professionalCertificateName">
				<Input className="input-box" placeholder="请输入职称" />
			</Form.Item>
			<Form.Item label="是否持有证书" name="brokerCertificateStatus" required>
				<Radio.Group
					options={[
						{ label: '是', value: 1 },
						{ label: '否', value: 0 },
					]}
				/>
			</Form.Item>
			{brokerCertificateStatus === 1 && (
				<>
					<Form.Item label="证书类型" name="brokerCertificateLevel" rules={[{ required: true, message: '请输入证书类型' }]}>
						<Radio.Group options={brokerCertificateLevelData} />
					</Form.Item>
					<Form.Item label="证件扫描件" name="brokerCertificateUrl" rules={[{ required: true, message: '请输入证件扫描件' }]}>
						<UploadImg size={5} width={200} height={300} />
					</Form.Item>
				</>
			)}
			<Form.Item label="擅长领域" name="areaCategoryList" rules={[{ required: true, type: 'array', message: '请输入擅长领域' }]}>
				<Select className="input-box" mode="multiple" options={areaCategoryListOptions} placeholder="请输入擅长领域" />
			</Form.Item>
			<Form.Item label="从事技术转移工作时间" name="workingExperience" rules={[{ required: true, message: '请输入从事技术转移工作时间' }]}>
				<InputNumber className="input-number-box" min={1} precision={0} placeholder="请输入从事技术转移工作时间" suffix="年" />
			</Form.Item>
			{config && config.mpConfig && config.mpConfig.brokerForm && config.mpConfig.brokerForm.recommendStatus === 1 && (
				<Form.Item label="加入湖南协会会员" name="recommendStatus" required>
					<Radio.Group
						options={[
							{ label: '同意', value: 1 },
							{ label: '不同意', value: 0 },
						]}
					/>
				</Form.Item>
			)}

			<Form.Item label="是否在职" name="isWorkStatus" required>
				<Radio.Group
					options={[
						{ label: '是', value: 1 },
						{ label: '否', value: 0 },
					]}
				/>
			</Form.Item>
		</>
	);
};

// 工作经验
const BrokerForm3 = (props = {}) => {
	return (
		<>
			<Form.Item label="获得荣誉" name="mainHonorsProjects">
				<Input.TextArea rows={4} placeholder="请输入获得荣誉" />
			</Form.Item>
			<Form.Item label="社会兼职" name="partTimeDesc">
				<Input.TextArea rows={4} placeholder="请输入社会兼职" />
			</Form.Item>
			<Form.Item label="个人履历">
				<Form.List name="workExperiencesList">
					{(fields, { add, remove }) => {
						return (
							<Space direction="vertical" className="width-100per">
								<Button
									type="primary"
									onClick={() =>
										add({
											companyName: '',
											positionName: '',
											deptName: '',
											startDate: '',
											endDate: '',
										})
									}
								>
									新增履历
								</Button>
								{fields.map((field, index) => {
									return (
										<Card
											title={`履历${index + 1}`}
											key={index}
											styles={{
												body: { padding: '12px 12px 0' },
												header: { padding: '0 12px' },
											}}
											extra={
												<DeleteOutlined className="a font-size-16 hover-color-165dff" onClick={() => remove(field.name)} />
											}
										>
											<WorkExperiencesForm field={field} form={props.form} />
										</Card>
									);
								})}
							</Space>
						);
					}}
				</Form.List>
			</Form.Item>
			<Form.Item label="个人简介" name="introduction">
				<Input.TextArea rows={4} placeholder="请输入个人简介" />
			</Form.Item>
		</>
	);
};

const WorkExperiencesForm = (props = {}) => {
	return (
		<Space direction="vertical" className="width-100per">
			<Form.Item
				labelAlign="right"
				labelCol={{ style: { width: '100px' } }}
				label="单位名称"
				name={[props.field.name, 'companyName']}
				rules={[{ required: true, message: '请输入单位名称' }]}
			>
				<Input placeholder="请输入单位名称" className="input-box" />
			</Form.Item>
			<Form.Item
				labelAlign="right"
				labelCol={{ style: { width: '100px' } }}
				label="职位"
				name={[props.field.name, 'positionName']}
				rules={[{ required: true, message: '请输入职位' }]}
			>
				<Input placeholder="请输入职位" className="input-box" />
			</Form.Item>
			<Form.Item labelAlign="right" labelCol={{ style: { width: '100px' } }} label="所在部门" name={[props.field.name, 'deptName']}>
				<Input placeholder="请输入所在部门" className="input-box" />
			</Form.Item>
			<Form.Item
				labelAlign="right"
				labelCol={{ style: { width: '100px' } }}
				label="在职时间"
				name={[props.field.name]}
				rules={[{ required: true, message: '请输入在职时间' }]}
			>
				<DatePickerComp index={props.field.name} form={props.form} />
			</Form.Item>
		</Space>
	);
};

const DatePickerComp = (props = {}) => {
	const [tempDate, setTempDate] = useState([null, null]);
	useEffect(() => {
		setTempDate(dayjs(new Date()), dayjs(new Date()));
		const startDate = (props.value && props.value.startDate) || null;
		const endDate = (props.value && props.value.endDate) || null;
		setTempDate([startDate && startDate !== '至今' ? dayjs(startDate) : null, endDate && endDate !== '至今' ? dayjs(endDate) : null]);
	}, [props.value]);
	return (
		<DatePicker.RangePicker
			value={tempDate}
			placeholder={['开始时间', '至今']}
			allowEmpty={[false, true]}
			format="YYYY-MM"
			picker="month"
			onChange={(value) => {
				const workExperiencesList = props.form.getFieldValue('workExperiencesList');
				const currentData = workExperiencesList[props.index] || {};

				currentData.startDate = value[0] ? dayjs(value[0]).format('YYYY-MM') : null;
				currentData.endDate = value[1] ? dayjs(value[1]).format('YYYY-MM') : '至今';

				if (!workExperiencesList[props.index]) {
					workExperiencesList[props.index] = currentData;
				}
				props.form.setFieldsValue({ workExperiencesList });
			}}
		/>
	);
};

export default Index;
