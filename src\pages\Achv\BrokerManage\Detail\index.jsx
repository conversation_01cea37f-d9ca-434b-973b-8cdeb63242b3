import { useEffect, useState, useRef, forwardRef, useImperativeHandle } from 'react';
import { Button, Input, Affix, Anchor, message, Modal, Form, Image, Space } from 'antd';
import { EditOutlined } from '@ant-design/icons';

import ModalForm from '@/components/ModalForm';
import { useRouterLink } from '@/hook/useRouter';
import { useConfig } from '@/hook/Achv/useConfig';

import { getBrokerDetail, auditBroker } from '@/api/Achv/BrokerManage/index';
import { brokerAuditStatusTextList, brokerTypeTextList, brokerCertificateLevelTextList } from '@/pages/Achv/config';

const Index = () => {
	const ModalFormRef = useRef();
	const { linkTo, searchParams } = useRouterLink();
	const config = useConfig();

	const id = searchParams.get('id') || '';
	const fromList = !!searchParams.get('fromList');

	const [detail, setDetail] = useState({});

	// 获取详情
	const getDetail = () => {
		if (id) {
			getBrokerDetail({ id }).then((res) => {
				const resData = res.data || {};

				setDetail(resData);
			});
		}
	};

	// 通过审核
	const handlePass = () => {
		Modal.confirm({
			title: '经理人审核',
			content: `是否通过【${detail.userName}】的审核？`,
			onOk() {
				handleAudit({ status: 3 });
			},
		});
	};

	// 撤销审核
	const handleRevoke = () => {
		Modal.confirm({
			title: '撤销审核',
			content: `是否撤销【${detail.userName}】的审核结果？`,
			onOk() {
				handleAudit({ status: 1 });
			},
		});
	};

	// 审核操作
	const handleAudit = (values) => {
		auditBroker({ ...values, id }).then(() => {
			message.success('操作成功');

			setTimeout(() => {
				linkTo(-1);
			}, 500);
		});
	};

	useEffect(() => {
		getDetail();
	}, []);

	return (
		<div className="">
			<div className="flex flex-shrink align-center margin-bottom-4 height-48 font-size-22 font-weight-500 color-1d2129">
				<Space size={10}>
					<div className="a color-86909c" onClick={() => linkTo(fromList ? -1 : '/newAchv/brokerManage')}>
						经理人管理
					</div>
					<div className="color-86909c">/</div>
					<div>经理人详情</div>
				</Space>
			</div>
			<div id="section1"></div>
			{/* Tabs & 功能按钮 开始 */}
			<Affix>
				<div className="height-66 flex align-center justify-between bg-color-ffffff border-radius-8 padding-lr-24 border-solid-bottom-f2f3f5">
					<div className="flex align-center justify-between color-1d2129 line-height-26 font-size-18 font-weight-500">
						<Anchor
							affix={false}
							rootClassName="custom-anchor-box"
							direction="horizontal"
							replace
							targetOffset={80}
							items={[
								{
									key: 'section1',
									href: '#section1',
									title: <div className="margin-right-40 font-size-16 font-weight-500">基本信息</div>,
								},
								{
									key: 'section2',
									href: '#section2',
									title: <div className="margin-right-40 font-size-16 font-weight-500">从业经验</div>,
								},
								{
									key: 'section3',
									href: '#section3',
									title: <div className="margin-right-40 font-size-16 font-weight-500">工作经验</div>,
								},
							]}
						/>
					</div>
					<div className="flex align-center justify-between color-1d2129 line-height-26 font-size-18 font-weight-500">
						{detail.auditStatus === 1 ? (
							<>
								<Button
									type="primary"
									danger
									ghost
									onClick={() => {
										ModalFormRef.current.setOpen(true);
										ModalFormRef.current.setTitle('不通过原因');
									}}
									className="height-40 width-104"
								>
									不通过
								</Button>
								<Button type="primary" onClick={handlePass} className="margin-left-20 height-40 width-104">
									通过审核
								</Button>
							</>
						) : (
							<Button onClick={handleRevoke} className="margin-left-20 height-40 width-104">
								撤销审核
							</Button>
						)}
					</div>
				</div>
			</Affix>
			{/* Tabs & 功能按钮 结束 */}

			{/* 基本信息 开始 */}
			<div className="margin-top-20 padding-20 border-radius-8 bg-color-ffffff">
				<div className="flex align-center justify-between margin-bottom-20">
					<div className="font-size-18 line-height-26 font-weight-500">基本信息</div>
					<Button
						type="link"
						icon={<EditOutlined />}
						onClick={() => {
							linkTo(`/newAchv/brokerManage/curd?id=${id}`);
						}}
					>
						编辑信息
					</Button>
				</div>
				<div className="flex align-center justify-start font-size-14 line-height-22 padding-left-16 margin-bottom-20">
					<div className="color-86909c margin-right-4">审核状态：</div>
					<div className="">
						<div className={`tag-status-${['', 'primary', 'warning', 'success'][detail.auditStatus]}`}>
							{brokerAuditStatusTextList[detail.auditStatus] || '--'}
						</div>
					</div>
				</div>
				{detail.auditStatus === 2 && (
					<div className="flex align-center justify-start font-size-14 line-height-22 padding-left-16 margin-bottom-20">
						<div className="color-86909c margin-right-4">拒绝原因：</div>
						<div className="">{detail.reason || '无'}</div>
					</div>
				)}
				{detail.inviteBrokerName && (
					<div className="flex align-center justify-start font-size-14 line-height-22 padding-left-16 margin-bottom-20">
						<div className="color-86909c margin-right-4">邀请人：</div>
						<div className="">{detail.inviteBrokerName}</div>
					</div>
				)}
				<div className="flex align-start justify-start font-size-14 line-height-22 padding-left-16 margin-bottom-20">
					<div className="color-86909c margin-right-4">头像：</div>
					<div className="">{(detail.wxAvatarUrl && <Image width={120} src={detail.wxAvatarUrl} />) || '--'}</div>
				</div>
				<div className="flex align-center justify-start font-size-14 line-height-22 padding-left-16 margin-bottom-20">
					<div className="color-86909c margin-right-4">姓名：</div>
					<div className="">{detail.userName || '--'}</div>
				</div>
				<div className="flex align-center justify-start font-size-14 line-height-22 padding-left-16 margin-bottom-20">
					<div className="color-86909c margin-right-4">性别：</div>
					<div className="">{['--', '男', '女'][detail.gender || 0]}</div>
				</div>
				<div className="flex align-center justify-start font-size-14 line-height-22 padding-left-16 margin-bottom-20">
					<div className="color-86909c margin-right-4">出生年月：</div>
					<div className="">{detail.birthDay || '--'}</div>
				</div>
				<div className="flex align-center justify-start font-size-14 line-height-22 padding-left-16 margin-bottom-20">
					<div className="color-86909c margin-right-4">常住城市：</div>
					<div className="">{[detail.provinceName, detail.cityName, detail.areaName].filter((ov) => ov).join('-') || '--'}</div>
				</div>
				<div className="flex align-center justify-start font-size-14 line-height-22 padding-left-16 margin-bottom-20">
					<div className="color-86909c margin-right-4">联系电话：</div>
					<div className="">{detail.phone || '--'}</div>
				</div>
				<div className="flex align-center justify-start font-size-14 line-height-22 padding-left-16 margin-bottom-20">
					<div className="color-86909c margin-right-4">学历：</div>
					<div className="">{detail.degreeName || '--'}</div>
				</div>
				{config.mpConfig.userType === 1 && (
					<div className="flex align-center justify-start font-size-14 line-height-22 padding-left-16 margin-bottom-20">
						<div className="color-86909c margin-right-4">用户类型：</div>
						<div className="">{brokerTypeTextList[detail.userType] || '--'}</div>
					</div>
				)}
			</div>
			{/* 基本信息 结束 */}

			{/* 从业经验 开始 */}
			<div id="section2"></div>
			<div className="margin-top-20 padding-20 border-radius-8 bg-color-ffffff">
				<div className="flex align-center justify-between margin-bottom-20">
					<div className="font-size-18 line-height-26 font-weight-500">从业经验</div>
					<Button
						type="link"
						icon={<EditOutlined />}
						onClick={() => {
							linkTo(`/newAchv/brokerManage/curd?id=${id}`);
						}}
					>
						编辑信息
					</Button>
				</div>
				<div className="flex align-center justify-start font-size-14 line-height-22 padding-left-16 margin-bottom-20">
					<div className="color-86909c margin-right-4">就职单位：</div>
					<div className="">{detail.companyName || '--'}</div>
				</div>

				<div className="flex align-center justify-start font-size-14 line-height-22 padding-left-16 margin-bottom-20">
					<div className="color-86909c margin-right-4">单位类型：</div>
					<div className="">{(detail.companyTypeListName || []).join('、') || '--'}</div>
				</div>

				<div className="flex align-center justify-start font-size-14 line-height-22 padding-left-16 margin-bottom-20">
					<div className="color-86909c margin-right-4">工作职务：</div>
					<div className="">{detail.positionName || '--'}</div>
				</div>

				<div className="flex align-center justify-start font-size-14 line-height-22 padding-left-16 margin-bottom-20">
					<div className="color-86909c margin-right-4">职称：</div>
					<div className="">{detail.professionalCertificateName || '--'}</div>
				</div>

				<div className="flex align-center justify-start font-size-14 line-height-22 padding-left-16 margin-bottom-20">
					<div className="color-86909c margin-right-4">是否持有技术经理（经纪）人证书：</div>
					<div className="">{detail.brokerCertificateStatus === 1 ? '是' : '否'}</div>
				</div>
				{detail.brokerCertificateStatus === 1 && (
					<>
						<div className="flex align-center justify-start font-size-14 line-height-22 padding-left-16 margin-bottom-20">
							<div className="color-86909c margin-right-4">证书类型：</div>
							<div className="">{brokerCertificateLevelTextList[detail.brokerCertificateLevel] || '--'}</div>
						</div>

						<div className="flex justify-start font-size-14 line-height-22 padding-left-16 margin-bottom-20">
							<div className="color-86909c margin-right-4">证件扫描件：</div>
							<div className="">{(detail.brokerCertificateUrl && <Image width={120} src={detail.brokerCertificateUrl} />) || null}</div>
						</div>
					</>
				)}

				<div className="flex align-center justify-start font-size-14 line-height-22 padding-left-16 margin-bottom-20">
					<div className="color-86909c margin-right-4">擅长领域：</div>
					<div className="">{(detail.areaCategoryListName || []).join('、') || '--'}</div>
				</div>

				<div className="flex align-center justify-start font-size-14 line-height-22 padding-left-16 margin-bottom-20">
					<div className="color-86909c margin-right-4">从事技术转移工作时间（单位: 年）：</div>
					<div className="">{detail.workingExperience || '--'}</div>
				</div>
				{config.mpConfig.recommendStatus === 1 && (
					<div className="flex align-center justify-start font-size-14 line-height-22 padding-left-16 margin-bottom-20">
						<div className="color-86909c margin-right-4">加入湖南协会会员：</div>
						<div className="">{detail.recommendStatus === 1 ? '同意' : '不同意'}</div>
					</div>
				)}
				<div className="flex align-center justify-start font-size-14 line-height-22 padding-left-16 margin-bottom-20">
					<div className="color-86909c margin-right-4">是否在职：</div>
					<div className="">{detail.isWorkStatus === 1 ? '是' : '否'}</div>
				</div>
			</div>
			{/* 从业经验 结束 */}

			{/* 工作经验 开始 */}
			<div id="section3"></div>
			<div className="margin-top-20 padding-20 border-radius-8 bg-color-ffffff">
				<div className="flex align-center justify-between margin-bottom-20">
					<div className="font-size-18 line-height-26 font-weight-500">工作经验</div>
					<Button
						type="link"
						icon={<EditOutlined />}
						onClick={() => {
							linkTo(`/newAchv/brokerManage/curd?id=${id}`);
						}}
					>
						编辑信息
					</Button>
				</div>
				<div className="flex justify-start font-size-14 line-height-22 padding-left-16 margin-bottom-20">
					<div className="color-86909c margin-right-4">获得荣誉：</div>
					<div className="flex-sub">{detail.mainHonorsProjects || '--'}</div>
				</div>
				<div className="flex justify-start font-size-14 line-height-22 padding-left-16 margin-bottom-20">
					<div className="color-86909c margin-right-4">社会兼职：</div>
					<div className="flex-sub">{detail.partTimeDesc || '--'}</div>
				</div>
				<div className="flex justify-start font-size-14 line-height-22 padding-left-16 margin-bottom-20">
					<div className="color-86909c margin-right-4">个人履历：</div>
					<div className="flex-sub">{detail.workExperiencesList ? <ExperiencesList data={detail.workExperiencesList} /> : '--'}</div>
				</div>
				<div className="flex justify-start font-size-14 line-height-22 padding-left-16 margin-bottom-20">
					<div className="color-86909c margin-right-4">个人简介：</div>
					<div className="flex-sub">{detail.introduction || '--'}</div>
				</div>
			</div>
			{/* 工作经验 结束 */}

			{/* 询问弹窗 开始 */}
			<ModalForm ref={ModalFormRef} onOk={handleAudit} FormComp={(props) => <ConrirmForm ref={props.FormCompRef} />} />
			{/* 询问弹窗 结束 */}
		</div>
	);
};

// 询问表单
const ConrirmForm = forwardRef((props = {}, ref) => {
	const [form] = Form.useForm();

	useImperativeHandle(ref, () => {
		return {
			onCancel: () => Promise.resolve(),
			onOk: () => {
				return new Promise((resolve) => {
					form.validateFields().then((values) => {
						resolve(values);
					});
				});
			},
		};
	});

	return (
		<Form form={form} labelCol={{ style: { width: '100px' } }} initialValues={{ status: 2 }}>
			<Form.Item hidden name="status">
				<Input />
			</Form.Item>
			<Form.Item label="原因" name="reason" rules={[{ required: true, message: '请输入不通过原因' }]}>
				<Input.TextArea rows={4} placeholder="请输入不通过原因" />
			</Form.Item>
		</Form>
	);
});

// 经历展示
const ExperiencesList = (props = {}) => {
	return (
		<Space direction="vertical">
			{(props.data || []).map((ov, oi) => {
				return (
					<div key={oi} className="">
						<div className="line-height-22">
							{ov.startDate} - {ov.endDate} {ov.deptName}
						</div>
						<div className="margin-top-4 color-86909c">
							{ov.companyName} {ov.positionName}
						</div>
					</div>
				);
			})}
		</Space>
	);
};

export default Index;
