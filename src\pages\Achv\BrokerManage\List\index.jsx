import { useState } from 'react';
import { Table, Button, Popconfirm, Space, Form, Input, Row, Col, Affix, Image, Select, InputNumber, DatePicker } from 'antd';
import { useTableData } from '@/hook/useTableData';
import { useRouterLink } from '@/hook/useRouter';

import FormComp from '@/components/FormComp';

import {
	pageBroker as getTablePageData,
	brokerDel as delTableItemData,
	exportList as exportTableData,
	updateRankingNum as batchUpdateSort,
	getAuditDataStatistics,
} from '@/api/Achv/BrokerManage/index';

import { brokerAuditStatusData, brokerAuditStatusTextList, brokerTypeTextList } from '@/pages/Achv/config';

const Index = () => {
	const { linkTo, searchParams } = useRouterLink();
	const status = searchParams.get('auditStatus');
	const [auditStatus, setAuditStatus] = useState(status === null ? 3 : status - 0 || '');
	const { form, dataSource, pagination, changePage, delTableData, exportData, onSearch, onReset, SortInput } = useTableData({
		params: { auditStatus },
		getTablePageData,
		delTableItemData,
		exportTableData,
		batchUpdateSort,
		pageCallback: (paramsData = {}) => {
			getStatisticsData({ ...paramsData });
		},
	});

	const sortKey = Form.useWatch('sortKey', form);

	// 获取统计数据
	const [statistics, setStatistics] = useState({});
	const getStatisticsData = (paramsData) => {
		delete paramsData.auditStatus;
		delete paramsData.pageSize;
		delete paramsData.pageNum;
		delete paramsData.sortValue;
		delete paramsData.tempDate;
		getAuditDataStatistics(paramsData).then((res) => {
			setStatistics(res.data || {});
		});
	};

	return (
		<div className="">
			<div className="flex flex-shrink align-center margin-bottom-4 height-48 font-size-22 font-weight-500 color-1d2129">经理人管理</div>
			{/* Tabs & 功能按钮 开始 */}
			<Affix offsetTop={0}>
				<div className="flex align-center justify-between padding-24 border-radius-8 bg-color-ffffff border-solid-bottom-f2f3f5">
					<div className="flex align-center">
						{brokerAuditStatusData.map((ov) => (
							<div
								key={ov.value}
								className={`a margin-right-40 font-size-18 font-weight-500 ${auditStatus === ov.value ? 'color-165dff' : ''}`}
								onClick={() => setAuditStatus(ov.value)}
							>
								{ov.label}
								{statistics[ov.countName] > 0 ? `（${statistics[ov.countName]}）` : ''}
							</div>
						))}
					</div>
					<Space size={16}>
						<Button onClick={exportData}>批量导出</Button>
						<Button
							type="primary"
							onClick={() => {
								linkTo(`/newAchv/brokerManage/curd`);
							}}
						>
							新建经理人
						</Button>
					</Space>
				</div>
			</Affix>
			{/* Tabs & 功能按钮 结束 */}
			<div className="flex-sub margin-top-18 padding-24 border-radius-8 bg-color-ffffff">
				{/* 筛选条件 开始 */}
				<div className="flex  justify-between margin-bottom-18">
					<div className="flex-sub">
						<Form
							form={form}
							labelAlign="right"
							layout="inline"
							initialValues={{
								sortValue: 'desc',
								auditStartTime: undefined,
								auditEndTime: undefined,
								tempDate: ['', ''],
							}}
						>
							<Form.Item hidden name="auditStartTime">
								<Input placeholder="" />
							</Form.Item>
							<Form.Item hidden name="auditEndTime">
								<Input placeholder="" />
							</Form.Item>
							<Row className="width-100per" gutter={[12, 12]}>
								<Col span={8}>
									<Form.Item name="name" label="经理人名称">
										<Input placeholder="请输入经理人名称" />
									</Form.Item>
								</Col>
								<Col span={8}>
									<Form.Item name="sortKey" label="排序">
										<Select
											options={[
												{ label: '申请时间', value: 'createTime' },
												{ label: '审核时间', value: 'auditTime' },
												{ label: '排序号', value: 'rankingNum' },
											]}
											allowClear
											placeholder="请选择排序"
										/>
									</Form.Item>
								</Col>
								<Col span={8}>
									<Form.Item name="sortValue" label="排序方式">
										<Select
											disabled={!sortKey}
											options={[
												{ label: '降序', value: 'desc' },
												{ label: '升序', value: 'asc' },
											]}
											allowClear
											placeholder="请选择排序方式"
										/>
									</Form.Item>
								</Col>

								{auditStatus === 3 && (
									<Col span={12}>
										<Form.Item name="tempDate" label="审核时间">
											<FormComp.DatePicker.RangePicker
												placeholder={['开始时间', '结束时间']}
												onChange={(date) => {
													form.setFieldsValue({
														auditStartTime: date[0],
														auditEndTime: date[1],
													});
												}}
											/>
										</Form.Item>
									</Col>
								)}
							</Row>
						</Form>
					</div>
					<div className="margin-left-12 padding-left-12 border-solid-left-e5e6eb">
						<Space size={16}>
							<Button onClick={onReset}>重置</Button>
							<Button type="primary" onClick={onSearch}>
								查询
							</Button>
						</Space>
					</div>
				</div>
				{/* 筛选条件 结束 */}
				{/* 表格 开始 */}
				<Table rowKey="id" dataSource={dataSource} pagination={pagination} onChange={changePage} scroll={{ x: 'max-content' }}>
					<Table.Column
						title="序号"
						key="index"
						width={60}
						render={(_, __, index) => {
							return index + 1;
						}}
					/>
					<Table.Column title="名称" dataIndex="name" />
					<Table.Column title="所属单位" dataIndex="companyName" render={(text) => text || '--'} />
					<Table.Column
						title="单位区域"
						dataIndex="provinceName"
						render={(_, record) => {
							return [record.provinceName, record.cityName, record.areaName].filter((ov) => ov).join('-');
						}}
					/>
					<Table.Column title="职位" dataIndex="positionName" render={(text) => text || '--'} />
					<Table.Column title="职称" dataIndex="professionalCertificateName" render={(text) => text || '--'} />
					<Table.Column
						title="擅长领域"
						dataIndex="areaCategoryNameList"
						render={(text) => (text && text.length ? text.join('、') : '--')}
					/>

					<Table.Column
						title="类型"
						dataIndex="userType"
						align="center"
						render={(text) => {
							return (
								<div className={`tag-status-small-${['default', 'warning', 'primary'][text]}`}>
									{brokerTypeTextList[text] || '--'}
								</div>
							);
						}}
					/>
					<Table.Column
						title="审核状态"
						dataIndex="auditStatus"
						align="center"
						render={(text) => {
							return (
								<div className={`tag-status-small-${['default', 'warning', 'error', 'primary'][text]}`}>
									{brokerAuditStatusTextList[text] || ''}
								</div>
							);
						}}
					/>
					<Table.Column
						title="排序"
						align="center"
						dataIndex="rankingNum"
						render={(_, record) => {
							return <SortInput record={record} isSingle />;
						}}
					/>
					<Table.Column
						title="申请时间"
						dataIndex="createTime"
						render={(text) => {
							return (text || '--').slice(0, 16);
						}}
					/>
					<Table.Column
						title="审核时间"
						dataIndex="auditTime"
						render={(text) => {
							return (text || '--').slice(0, 16);
						}}
					/>
					<Table.Column
						title="操作"
						key="option"
						dataIndex="option"
						align="center"
						fixed="right"
						render={(_, record) => {
							return (
								<>
									<Button
										type="link"
										size="small"
										onClick={() => linkTo(`/newAchv/brokerManage/detail?id=${record.id}&fromList=1`)}
									>
										编辑/审核
									</Button>
									<Popconfirm
										title="提示"
										description="确定删除吗？"
										onConfirm={() => {
											delTableData(record.id);
										}}
										okText="确定"
										cancelText="取消"
									>
										<Button type="link" size="small">
											删除
										</Button>
									</Popconfirm>
								</>
							);
						}}
					/>
				</Table>
				{/* 表格 结束 */}
			</div>
		</div>
	);
};

export default Index;
