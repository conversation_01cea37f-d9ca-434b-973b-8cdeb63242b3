import { useState } from 'react';
import { Table, Button, Popconfirm, Space, Form, Input, Row, Col, Affix, message, Switch, Select } from 'antd';
import { useTableData } from '@/hook/useTableData';
import { useRouterLink } from '@/hook/useRouter';
import { pageComment as getTablePageData, batchDelComment as delTableItemData, getStatistics } from '@/api/Achv/Comment';

import { auditStatusData, auditStatusTextList } from '@/pages/Achv/config';

const Index = () => {
	const { linkTo, searchParams } = useRouterLink();
	const status = searchParams.get('auditStatus');
	const [auditStatus, setAuditStatus] = useState(status === null ? 3 : status - 0 || '');
	const { form, dataSource, pagination, changePage, delTableData, onSearch, onReset } = useTableData({
		params: { auditStatus },
		getTablePageData,
		delTableItemData,
		pageCallback: (paramsData = {}) => {
			getStatisticsData({ ...paramsData });
		},
	});

	// 获取统计数据
	const [statistics, setStatistics] = useState({});
	const getStatisticsData = (paramsData) => {
		delete paramsData.auditStatus;
		delete paramsData.pageSize;
		delete paramsData.pageNum;
		getStatistics(paramsData).then((res) => {
			setStatistics(res.data || {});
		});
	};

	return (
		<div>
			<div className="flex flex-shrink align-center margin-bottom-4 height-48 font-size-22 font-weight-500 color-1d2129">评论管理</div>
			{/* Tabs & 功能按钮 开始 */}
			<Affix offsetTop={0}>
				<div className="flex align-center justify-between padding-24 border-radius-8 bg-color-ffffff border-solid-bottom-f2f3f5">
					<div className="flex align-center">
						{auditStatusData.map((ov) => (
							<div
								key={ov.value}
								className={`a margin-right-40 font-size-18 font-weight-500 ${auditStatus === ov.value ? 'color-165dff' : ''}`}
								onClick={() => setAuditStatus(ov.value)}
							>
								{ov.label}
								{statistics[ov.countName] > 0 ? `（${statistics[ov.countName]}）` : ''}
							</div>
						))}
					</div>
					<Space size={16}></Space>
				</div>
			</Affix>
			{/* Tabs & 功能按钮 结束 */}

			{/* Tabs & 功能按钮 结束 */}
			<div className="flex-sub margin-top-18 padding-24 border-radius-8 bg-color-ffffff">
				{/* 筛选条件 开始 */}
				<div className="flex justify-between margin-bottom-18">
					<div className="flex-sub">
						<Form form={form} labelAlign="right" layout="inline">
							<Row className="width-100per" gutter={[12, 12]}>
								<Col span={12}>
									<Form.Item name="keyword" label="评论内容">
										<Input placeholder="请输入评论内容" />
									</Form.Item>
								</Col>
							</Row>
						</Form>
					</div>
					<div className="margin-left-12 padding-left-12 border-solid-left-e5e6eb">
						<Space size={16}>
							<Button onClick={onReset}>重置</Button>
							<Button type="primary" onClick={onSearch}>
								查询
							</Button>
						</Space>
					</div>
				</div>
				{/* 筛选条件 结束 */}

				{/* 表格 开始 */}
				<Table rowKey="id" dataSource={dataSource} pagination={pagination} onChange={changePage} scroll={{ x: 'max-content' }}>
					<Table.Column
						title="序号"
						key="index"
						width={60}
						render={(_, __, index) => {
							return index + 1;
						}}
					/>
					<Table.Column title="评论内容" dataIndex="content" render={(text) => <div className="max-width-240">{text}</div>} />
					<Table.Column
						title="发布状态"
						dataIndex="auditStatus"
						align="center"
						render={(text) => {
							return (
								<div className={`tag-status-small-${['default', 'warning', 'error', 'primary'][text]}  `}>
									{auditStatusTextList[text || ''] || '--'}
								</div>
							);
						}}
					/>
					<Table.Column
						title="文章标题"
						dataIndex="title"
						render={(text) => {
							return text || '-';
						}}
					/>
					<Table.Column
						title="评论作者"
						dataIndex="userName"
						render={(text) => {
							return text || '-';
						}}
					/>
					<Table.Column
						title="用户类型"
						dataIndex="userType"
						render={(text) => {
							return (
								<div className={`tag-status-small-${['default', 'warning', 'primary'][text]}  `}>
									{['', '科转号', '普通用户'][text || ''] || '--'}
								</div>
							);
						}}
					/>
					<Table.Column
						title="举报数量"
						dataIndex="reportCount"
						align="center"
						render={(text) => {
							return text || '-';
						}}
					/>
					<Table.Column
						title="发布时间"
						dataIndex="createTime"
						render={(text) => {
							return (text || '--').slice(0, 16);
						}}
					/>

					<Table.Column
						title="操作"
						key="option"
						dataIndex="option"
						align="center"
						fixed="right"
						render={(_, record) => {
							return (
								<>
									<Button type="link" size="small" onClick={() => linkTo(`/newAchv/comment/detail?id=${record.id}&fromList=1`)}>
										详情
									</Button>
									<Popconfirm
										title="提示"
										description={`确定删除吗？`}
										onConfirm={() => {
											delTableData(record.id);
										}}
										okText="确定"
										cancelText="取消"
									>
										<Button type="link" size="small">
											删除
										</Button>
									</Popconfirm>
								</>
							);
						}}
					/>
				</Table>
				{/* 表格 结束 */}
			</div>
		</div>
	);
};

export default Index;
