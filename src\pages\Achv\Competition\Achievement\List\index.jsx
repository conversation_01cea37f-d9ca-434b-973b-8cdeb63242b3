import { useState } from 'react';
import { Table, Button, Popconfirm, Space, Form, Input, Row, Col, Affix, InputNumber, Select, Image } from 'antd';
import { useTableData } from '@/hook/useTableData';
import { useRouterLink } from '@/hook/useRouter';
import { deleteAchievementDemand as delTableItemData, updateRankingNum as batchUpdateSort } from '@/api/Achv/Competition/Demand';
import {
	queryAchievementPage as getTablePageData,
	exportAchievement as exportTableData,
	queryAchievementStatistics,
} from '@/api/Achv/Competition/Achievement';

import { auditStatusData, releaseTypeData, releaseTypeTextList, competitionGroupData, competitionGroupTextList } from '@/pages/Achv/config';
import './index.scss';
const Index = () => {
	const { linkTo, searchParams } = useRouterLink();

	const status = searchParams.get('auditStatus');
	const [auditStatus, setAuditStatus] = useState(status === null ? 3 : status - 0 || '');
	const { form, dataSource, pagination, changePage, delTableData, exportData, onSearch, onReset, SortInput } = useTableData({
		params: { auditStatus },
		getTablePageData,
		delTableItemData,
		exportTableData,
		batchUpdateSort,
		pageCallback: (paramsData = {}) => {
			getStatisticsData({ ...paramsData });
		},
	});

	// 获取统计数据
	const [statistics, setStatistics] = useState({});
	const getStatisticsData = (paramsData) => {
		delete paramsData.auditStatus;
		delete paramsData.pageSize;
		delete paramsData.pageNum;
		delete paramsData.tempArea;
		queryAchievementStatistics(paramsData).then((res) => {
			const resData = res.data || {};
			setStatistics({
				...resData,
				total: Object.values(resData).reduce((pre, cur) => pre + (cur - 0), 0),
			});
		});
	};

	return (
		<div className="">
			<div className="flex flex-shrink align-center margin-bottom-4 height-48 font-size-22 font-weight-500 color-1d2129">成果管理</div>
			{/* Tabs & 功能按钮 开始 */}
			<Affix offsetTop={0}>
				<div className="flex align-center justify-between padding-24 border-radius-8 bg-color-ffffff border-solid-bottom-f2f3f5">
					<div className="flex align-center">
						{auditStatusData.map((ov) => (
							<div
								key={ov.value}
								className={`a margin-right-40 font-size-18 font-weight-500 ${auditStatus === ov.value ? 'color-165dff' : ''}`}
								onClick={() => setAuditStatus(ov.value)}
							>
								{ov.label}
								{statistics[ov.countName] > 0 ? `（${statistics[ov.countName]}）` : ''}
							</div>
						))}
					</div>
					<Space size={16}>
						<Button type="primary" onClick={exportData}>
							批量导出
						</Button>
					</Space>
				</div>
			</Affix>
			{/* Tabs & 功能按钮 结束 */}
			<div className="flex-sub margin-top-18 padding-24 border-radius-8 bg-color-ffffff">
				{/* 筛选条件 开始 */}
				<div className="flex justify-between margin-bottom-18">
					<div className="flex-sub">
						<Form form={form} labelAlign="right" layout="inline">
							<Row className="width-100per" gutter={[12, 12]}>
								<Col span={8}>
									<Form.Item name="keywords" label="成果名称">
										<Input placeholder="请输入成果名称" />
									</Form.Item>
								</Col>
								<Col span={8}>
									<Form.Item name="releaseType" label="来源">
										<Select options={releaseTypeData} allowClear placeholder="请选择来源" />
									</Form.Item>
								</Col>
								<Col span={8}>
									<Form.Item name="signUpName" label="参赛者姓名">
										<Input placeholder="请输入参赛者姓名" />
									</Form.Item>
								</Col>
								<Col span={8}>
									<Form.Item name="competitionGroup" label="参赛组别">
										<Select options={competitionGroupData} allowClear placeholder="请选择参赛组别" />
									</Form.Item>
								</Col>
							</Row>
						</Form>
					</div>
					<div className="margin-left-12 padding-left-12 border-solid-left-e5e6eb">
						<Space size={16}>
							<Button onClick={onReset}>重置</Button>
							<Button type="primary" onClick={onSearch}>
								查询
							</Button>
						</Space>
					</div>
				</div>
				{/* 筛选条件 结束 */}

				{/* 表格 开始 */}
				<Table rowKey="id" dataSource={dataSource} pagination={pagination} onChange={changePage} scroll={{ x: 'max-content' }}>
					<Table.Column
						title="序号"
						key="index"
						width={60}
						fixed="left"
						render={(_, __, index) => {
							return index + 1;
						}}
					/>

					<Table.Column
						title="图片"
						key="achvtImgUrl"
						align="center"
						width={100}
						fixed="left"
						render={(_, record) => {
							return (
								<div className="flex align-center justify-center width-100 height-100 overflow-hidden">
									<Image
										width={100}
										src={record.achvtImgUrl}
										fallback="https://gbac-produce.oss-cn-shenzhen.aliyuncs.com/static-assets/gbac-achv-applet/Public/default-achv-cover.png"
									/>
								</div>
							);
						}}
					/>

					<Table.Column
						title="成果名称"
						dataIndex="name"
						render={(text) => {
							return <div className="max-width-240">{text}</div>;
						}}
					/>
					<Table.Column
						title="成果状态"
						dataIndex="proclamationStatus"
						align="center"
						render={(text) => {
							return <div className={`proclamation-status-${text}`}>{['', '可揭榜', '揭榜中', '已揭榜'][text]}</div>;
						}}
					/>
					<Table.Column title="所属机构" dataIndex="orgName" align="center" />
					<Table.Column
						title="技术领域"
						dataIndex="areaCategoryListName"
						render={(text) => (text && text.length ? text.join('、') : '--')}
					/>
					<Table.Column title="所处阶段" dataIndex="stageIdName" align="center" />
					<Table.Column title="转化方式" dataIndex="transformListName" align="center" />
					<Table.Column
						title="审核状态"
						dataIndex="auditStatus"
						align="center"
						render={(text) => {
							return (
								<div className={`tag-status-small-${['default', 'warning', 'error', 'primary'][text]}`}>
									{['', '审核中', '审核不通过', '审核通过'][text] || ''}
								</div>
							);
						}}
					/>
					<Table.Column title="合作意向" dataIndex="cooperateNum" />
					<Table.Column title="参赛者姓名" dataIndex="signUpName" render={(text) => text || '--'} />
					<Table.Column
						title="参赛组别"
						dataIndex="competitionGroup"
						render={(text) => {
							return (
								<div className={`tag-status-small-${['', 'primary', 'warning', '700eb2'][text]}`}>
									{competitionGroupTextList[text] || '--'}
								</div>
							);
						}}
					/>
					<Table.Column
						title="来源"
						dataIndex="releaseType"
						render={(text) => {
							return (
								<div className={`tag-status-small-${['', 'primary', 'warning', '700eb2', 'success', 'error'][text]}`}>
									{releaseTypeTextList[text] || '--'}
								</div>
							);
						}}
					/>
					<Table.Column
						title="排序"
						align="center"
						dataIndex="rankingNum"
						render={(_, record) => {
							return <SortInput record={record} />;
						}}
					/>
					<Table.Column
						title="提交时间"
						dataIndex="createTime"
						render={(text) => {
							return (text || '--').slice(0, 16);
						}}
					/>
					<Table.Column
						title="操作"
						key="option"
						dataIndex="option"
						align="center"
						fixed="right"
						render={(_, record) => {
							const delText = record.releaseType === 3 ? '删除' : '移出';
							return (
								<>
									<Button
										type="link"
										size="small"
										onClick={() => linkTo(`/newAchv/competition/achievement/detail?id=${record.id}&fromList=1`)}
									>
										编辑/审核
									</Button>
									<Popconfirm
										title="提示"
										description={`确定${delText}吗？`}
										onConfirm={() => {
											delTableData(record.id);
										}}
										okText="确定"
										cancelText="取消"
									>
										<Button type="link" size="small">
											{delText}
										</Button>
									</Popconfirm>
								</>
							);
						}}
					/>
				</Table>
				{/* 表格 结束 */}
			</div>
		</div>
	);
};

export default Index;
