import { useEffect, useState, useRef, forwardRef, useImperativeHandle } from 'react';
import { Button, Input, Affix, Anchor, message, Form, Image, Space, Modal } from 'antd';
import { useRouterLink } from '@/hook/useRouter';

import ModalForm from '@/components/ModalForm';

import { getSignUp, auditSignUp } from '@/api/Achv/Competition/Signup/index';
import { competitionGroupTextList } from '@/pages/Achv/config';

const Index = () => {
	const ModalFormRef = useRef();
	const { linkTo, openNewTab, searchParams } = useRouterLink();
	const id = searchParams.get('id') || '';
	const [detail, setDetail] = useState({});

	const getDetail = () => {
		getSignUp({ id }).then((res) => {
			if (res.data) {
				setDetail(res.data);
			} else {
				message.error('该报名数据不存在');
				setTimeout(() => {
					linkTo(-1);
				}, 500);
			}
		});
	};

	// 通过审核
	const handlePass = () => {
		Modal.confirm({
			title: '报名审核',
			content: `是否通过${competitionGroupTextList[detail.competitionGroup]}-${detail.name}的报名？`,
			onOk() {
				handleAudit({ status: 3 });
			},
		});
	};

	// 撤销审核
	const handleRevoke = () => {
		Modal.confirm({
			title: '撤销审核',
			content: `是否撤销${competitionGroupTextList[detail.competitionGroup]}-${detail.name}的审核结果？`,
			onOk() {
				handleAudit({ status: 1 });
			},
		});
	};

	// 审核操作
	const handleAudit = (values) => {
		auditSignUp({ ...values, id }).then(() => {
			message.success('操作成功');

			setTimeout(() => {
				linkTo(-1);
			}, 500);
		});
	};

	useEffect(() => {
		getDetail();
	}, []);

	return (
		<div>
			<div className="flex flex-shrink align-center margin-bottom-4 height-48 font-size-22 font-weight-500">
				<div className="a color-86909c" onClick={() => linkTo('/newAchv/competition/signup')}>
					报名管理
				</div>
				<div className="margin-lr-10 color-86909c">/</div>
				<div className="color-1d2129">报名详情</div>
			</div>

			<Affix>
				<div className="height-66 flex align-center justify-between bg-color-ffffff border-radius-8 padding-lr-24 border-solid-bottom-f2f3f5">
					<div className="flex align-center justify-between color-1d2129 line-height-26 font-size-18 font-weight-500">
						<Anchor
							affix={false}
							rootClassName="anchor-header-tabBar-box"
							direction="horizontal"
							replace
							targetOffset={80}
							items={[
								{
									key: 'section1',
									href: '#section1',
									title: <div className="margin-right-40 font-size-16 font-weight-500">基本信息</div>,
								},
								{
									key: 'section2',
									href: '#section2',
									title: <div className="margin-right-40 font-size-16 font-weight-500">简历信息</div>,
								},
							]}
						/>
					</div>
					<div className="flex align-center justify-between color-1d2129 line-height-26 font-size-18 font-weight-500">
						{detail.auditStatus === 1 ? (
							<>
								<Button
									type="primary"
									danger
									ghost
									onClick={() => {
										ModalFormRef.current.setOpen(true);
										ModalFormRef.current.setTitle('不通过原因');
									}}
									className="height-40 width-104"
								>
									不通过
								</Button>
								<Button type="primary" onClick={handlePass} className="margin-left-20 height-40 width-104">
									通过审核
								</Button>
							</>
						) : (
							<Button onClick={handleRevoke} className="margin-left-20 height-40 width-104">
								撤销审核
							</Button>
						)}
					</div>
				</div>
			</Affix>

			<div id="section1"></div>
			<div className="bg-color-ffffff border-radius-8 padding-lr-24 padding-tb-20 margin-tb-16">
				<div className="flex align-center justify-between margin-bottom-20">
					<div className="font-size-18 line-height-26 font-weight-500">基本信息</div>
					{/* <Button
						type="link"
						icon={<EditOutlined />}
						onClick={() => {
							linkTo(`/newAchv/competition/signup/curd?id=${id}&tabKey=1`);
						}}
					>
						编辑信息
					</Button> */}
				</div>
				<div className="flex align-center justify-start font-size-14 line-height-22 padding-left-16 margin-bottom-20">
					<div className="color-86909c margin-right-4">审核状态：</div>
					<div className="">
						<div className={`tag-status-${['default', 'warning', 'error', 'primary'][detail.auditStatus]}`}>
							{['', '审核中', '审核不通过', '审核通过'][detail.auditStatus] || ''}
						</div>
					</div>
				</div>
				{detail.auditStatus === 2 && (
					<div className="flex align-center justify-start font-size-14 line-height-22 padding-left-16 margin-bottom-20">
						<div className="color-86909c margin-right-4">拒绝原因：</div>
						<div className="">{detail.reason || '无'}</div>
					</div>
				)}
				<div className="flex align-center justify-start font-size-14 line-height-22 padding-left-16 margin-bottom-20">
					<div className="color-86909c margin-right-4">参赛组别：</div>
					<div className="">
						<div className={`tag-status-${['', 'primary', 'warning', '700eb2'][detail.competitionGroup]}`}>
							{competitionGroupTextList[detail.competitionGroup]}
						</div>
					</div>
				</div>
				<div className="flex justify-start font-size-14 line-height-22 padding-left-16 margin-bottom-20">
					<div className="color-86909c margin-right-4">个人照片：</div>
					<div className="">
						<Image width={120} src={detail.wxAvatarUrl} />
					</div>
				</div>
				<div className="flex align-center justify-start font-size-14 line-height-22 padding-left-16 margin-bottom-20">
					<div className="color-86909c margin-right-4">姓名：</div>
					<div className="">{detail.name}</div>
				</div>
				<div className="flex align-center justify-start font-size-14 line-height-22 padding-left-16 margin-bottom-20">
					<div className="color-86909c margin-right-4">性别：</div>
					<div className="">{['--', '男', '女'][detail.gender]}</div>
				</div>
				<div className="flex align-center justify-start font-size-14 line-height-22 padding-left-16 margin-bottom-20">
					<div className="color-86909c margin-right-4">出生年月：</div>
					<div className="">{detail.birthDay || '--'}</div>
				</div>
				<div className="flex align-center justify-start font-size-14 line-height-22 padding-left-16 margin-bottom-20">
					<div className="color-86909c margin-right-4">政治面貌：</div>
					<div className="">{detail.politicalStatus || '--'}</div>
				</div>
				{/* 经理人特有 开始 */}
				{detail.competitionGroup !== 3 && (
					<div className="flex align-center justify-start font-size-14 line-height-22 padding-left-16 margin-bottom-20">
						<div className="color-86909c margin-right-4">工作城市：</div>
						<div className="">
							{[detail.provinceName || '', detail.cityName || '', detail.areaName || ''].filter((ov) => ov).join('/')}
						</div>
					</div>
				)}
				{/* 经理人特有 结束 */}
				<div className="flex align-center justify-start font-size-14 line-height-22 padding-left-16 margin-bottom-20">
					<div className="color-86909c margin-right-4">联系电话：</div>
					<div className="">{detail.contactPhone || '--'}</div>
				</div>
				<div className="flex align-center justify-start font-size-14 line-height-22 padding-left-16 margin-bottom-20">
					<div className="color-86909c margin-right-4">联系邮箱：</div>
					<div className="">{detail.contactEmail || '--'}</div>
				</div>

				{/* 经理人 开始 */}
				{detail.competitionGroup !== 3 && (
					<>
						<div className="flex align-center justify-start font-size-14 line-height-22 padding-left-16 margin-bottom-20">
							<div className="color-86909c margin-right-4">所在单位/推荐单位：</div>
							<div className="">{detail.companyName || '--'}</div>
						</div>
						<div className="flex align-center justify-start font-size-14 line-height-22 padding-left-16 margin-bottom-20">
							<div className="color-86909c margin-right-4">工作职务：</div>
							<div className="">{detail.positionName || '--'}</div>
						</div>
						<div className="flex align-center justify-start font-size-14 line-height-22 padding-left-16 margin-bottom-20">
							<div className="color-86909c margin-right-4">职称/资格证书：</div>
							<div className="">{detail.professionalCertificateName || '--'}</div>
						</div>
						<div className="flex align-center justify-start font-size-14 line-height-22 padding-left-16 margin-bottom-20">
							<div className="color-86909c margin-right-4">从事技术转移年限（单位：年）：</div>
							<div className="">{detail.workingExperience || '--'}</div>
						</div>
						<div className="flex align-center justify-start font-size-14 line-height-22 padding-left-16 margin-bottom-20">
							<div className="color-86909c margin-right-4">促成技术转移项目数（单位：个）：</div>
							<div className="">{detail.projectsNumber || '--'}</div>
						</div>
						<div className="flex align-center justify-start font-size-14 line-height-22 padding-left-16 margin-bottom-20">
							<div className="color-86909c margin-right-4">促成合同成交额（单位：万元）：</div>
							<div className="">{detail.contractAmount || '--'}</div>
						</div>
					</>
				)}
				{/* 经理人 结束 */}

				{/* 大学生 开始 */}
				{detail.competitionGroup === 3 && (
					<>
						<div className="flex align-center justify-start font-size-14 line-height-22 padding-left-16 margin-bottom-20">
							<div className="color-86909c margin-right-4">在读/毕业学校：</div>
							<div className="">{detail.schoolName || '--'}</div>
						</div>
						<div className="flex align-center justify-start font-size-14 line-height-22 padding-left-16 margin-bottom-20">
							<div className="color-86909c margin-right-4">专业：</div>
							<div className="">{detail.majorName || '--'}</div>
						</div>
						<div className="flex align-center justify-start font-size-14 line-height-22 padding-left-16 margin-bottom-20">
							<div className="color-86909c margin-right-4">学历：</div>
							<div className="">{detail.degreeName || '--'}</div>
						</div>
						<div className="flex align-center justify-start font-size-14 line-height-22 padding-left-16 margin-bottom-20">
							<div className="color-86909c margin-right-4">指导老师：</div>
							<div className="">{(detail.instructor || '').split(',').join('、') || '--'}</div>
						</div>
						<div className="flex align-center justify-start font-size-14 line-height-22 padding-left-16 margin-bottom-20">
							<div className="color-86909c margin-right-4">现任年级：</div>
							<div className="">{detail.currentGrade || '--'}</div>
						</div>
						<div className="flex align-center justify-start font-size-14 line-height-22 padding-left-16 margin-bottom-20">
							<div className="color-86909c margin-right-4">毕业时间：</div>
							<div className="">{detail.graduationYear || '--'}</div>
						</div>
					</>
				)}
				{/* 大学生 结束 */}

				{[2, 3].includes(detail.competitionGroup) && (
					<div className="flex align-center justify-start font-size-14 line-height-22 padding-left-16 margin-bottom-20">
						<div className="color-86909c margin-right-4">报名附件：</div>
						<div className="flex-sub">
							{detail.attachment ? (
								<div
									className="a color-165dff"
									onClick={() => {
										const attachmentList = (detail.attachment || '').split('/');
										const fileName = encodeURIComponent(attachmentList.pop());
										openNewTab(attachmentList.join('/') + '/' + fileName);
									}}
								>
									{(detail.attachment || '').split('/').pop()}
								</div>
							) : (
								'--'
							)}
						</div>
					</div>
				)}
			</div>

			<div id="section2"></div>

			<div className="bg-color-ffffff border-radius-8 padding-lr-24 padding-tb-20 margin-tb-16">
				<div className="flex align-center justify-between margin-bottom-20">
					<div className="font-size-18 line-height-26 font-weight-500">简历信息</div>
					{/* <Button
						type="link"
						icon={<EditOutlined />}
						onClick={() => {
							linkTo(`/newAchv/demand/demandManage/curd?id=${id}&tabKey=1`);
						}}
					>
						编辑信息
					</Button> */}
				</div>

				<div className="flex justify-start font-size-14 line-height-22 padding-left-16 margin-bottom-20">
					<div className="color-86909c margin-right-4">教育经历：</div>
					<div className="flex-sub">
						{detail.educationExperiencesList ? <ExperiencesList data={detail.educationExperiencesList} /> : '--'}
					</div>
				</div>
				<div className="flex justify-start font-size-14 line-height-22 padding-left-16 margin-bottom-20">
					<div className="color-86909c margin-right-4">工作经历：</div>
					<div className="flex-sub">{detail.workExperiencesList ? <ExperiencesList data={detail.workExperiencesList} /> : '--'}</div>
				</div>
				<div className="flex justify-start font-size-14 line-height-22 padding-left-16 margin-bottom-20">
					<div className="color-86909c margin-right-4">
						从事成果转化
						<br />
						工作重点业绩：
					</div>
					<div className="flex-sub">{detail.mainHonorsProjects || '--'}</div>
				</div>
			</div>

			{/* 询问弹窗 开始 */}
			<ModalForm ref={ModalFormRef} onOk={handleAudit} FormComp={(props) => <ConrirmForm ref={props.FormCompRef} />} />
			{/* 询问弹窗 结束 */}
		</div>
	);
};

// 经历展示
const ExperiencesList = (props = {}) => {
	return (
		<Space direction="vertical">
			{(props.data || []).map((ov, oi) => {
				return (
					<div key={oi} className="">
						<div className="line-height-22">
							{ov.startDate} - {ov.endDate} {ov.unitName}
						</div>
						<div className="margin-top-4 color-86909c">
							{ov.deptOrDegree} {ov.positionName}
						</div>
					</div>
				);
			})}
		</Space>
	);
};

// 询问表单
const ConrirmForm = forwardRef((props = {}, ref) => {
	const [form] = Form.useForm();

	useImperativeHandle(ref, () => {
		return {
			onCancel: () => Promise.resolve(),
			onOk: () => {
				return new Promise((resolve) => {
					form.validateFields().then((values) => {
						resolve(values);
					});
				});
			},
		};
	});

	return (
		<Form form={form} labelCol={{ style: { width: '100px' } }} initialValues={{ status: 2 }}>
			<Form.Item hidden name="status">
				<Input />
			</Form.Item>
			<Form.Item label="原因" name="reason" rules={[{ required: true, message: '请输入不通过原因' }]}>
				<Input.TextArea rows={4} placeholder="请输入不通过原因" />
			</Form.Item>
		</Form>
	);
});

export default Index;
