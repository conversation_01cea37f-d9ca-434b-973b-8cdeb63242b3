import { useEffect, useState } from 'react';
import { Table, Button, Popconfirm, Space, Form, Input, Cascader, Row, Col, Affix, Select } from 'antd';
import { useTableData } from '@/hook/useTableData';
import { useRouterLink } from '@/hook/useRouter';
import {
	pageSignUp as getTablePageData,
	delSignUp as delTableItemData,
	exportSignUp as exportTableData,
	dataStatistics,
} from '@/api/Achv/Competition/Signup/index';
import { getThreeLevelData } from '@/api/common';
import { brokerAuditStatusData, competitionGroupData, competitionGroupTextList } from '@/pages/Achv/config';

const Index = () => {
	const { linkTo, searchParams } = useRouterLink();

	const status = searchParams.get('auditStatus');
	const [auditStatus, setAuditStatus] = useState(status === null ? '' : status - 0 || '');

	const { form, dataSource, pagination, changePage, delTableData, exportData, onReset, onSearch } = useTableData({
		params: { auditStatus },
		getTablePageData,
		delTableItemData,
		exportTableData,
		pageCallback: (paramsData) => {
			getStatisticsData({ ...paramsData });
		},
	});

	// 获取统计数据
	const [statistics, setStatistics] = useState({});
	const getStatisticsData = (paramsData) => {
		delete paramsData.auditStatus;
		delete paramsData.pageSize;
		delete paramsData.pageNum;
		delete paramsData.tempArea;
		dataStatistics(paramsData).then((res) => {
			setStatistics(res.data || {});
		});
	};

	// 获取选项数据
	const [areaOptions, setAreaOptions] = useState([]);
	const getOptionsData = () => {
		getThreeLevelData({ level: 3 }).then((res) => {
			setAreaOptions(res.data || []);
		});
	};

	useEffect(() => {
		getOptionsData();
	}, []);

	return (
		<div className="">
			<div className="flex flex-shrink align-center margin-bottom-4 height-48 font-size-22 font-weight-500 color-1d2129">报名管理</div>
			{/* Tabs 开始 */}
			<Affix offsetTop={0}>
				<div className="flex align-center justify-between padding-24 border-radius-8 bg-color-ffffff border-solid-bottom-f2f3f5">
					<div className="flex align-center">
						{brokerAuditStatusData.map((ov, oi) => (
							<div
								key={oi}
								className={`a margin-right-40 font-size-18 font-weight-500 ${auditStatus === ov.value ? 'color-165dff' : ''}`}
								onClick={() => setAuditStatus(ov.value)}
							>
								{ov.label}
								{statistics[ov.countName] > 0 ? `（${statistics[ov.countName]}）` : ''}
							</div>
						))}
					</div>
					<Space size={16}>
						<Button type="primary" onClick={exportData}>
							批量导出
						</Button>
					</Space>
				</div>
			</Affix>
			{/* Tabs 结束 */}
			<div className="flex-sub margin-top-18 padding-24 border-radius-8 bg-color-ffffff">
				{/* 筛选条件 开始 */}
				<div className="flex justify-between margin-bottom-18">
					<div className="flex-sub">
						<Form
							form={form}
							labelAlign="right"
							layout="inline"
							initialValues={{
								provinceCodes: undefined,
								cityCodes: undefined,
								areaCode: undefined,
								tempArea: [],
							}}
						>
							<Form.Item hidden name="provinceCodes">
								<Input />
							</Form.Item>
							<Form.Item hidden name="cityCodes">
								<Input />
							</Form.Item>
							<Form.Item hidden name="areaCode">
								<Input />
							</Form.Item>
							<Row className="width-100per" gutter={[12, 12]}>
								<Col span={8}>
									<Form.Item name="name" label="姓名">
										<Input placeholder="请输入姓名" />
									</Form.Item>
								</Col>
								<Col span={8}>
									<Form.Item name="competitionGroup" label="参赛组别">
										<Select options={competitionGroupData} placeholder="请选择参赛组别" allowClear />
									</Form.Item>
								</Col>
								<Col span={8}>
									<Form.Item name="tempArea" label="区域">
										<Cascader
											options={areaOptions}
											placeholder="请选择区域"
											displayRender={(label) => label.join('-')}
											onChange={(e = [undefined, undefined, undefined]) => {
												form.setFieldValue('provinceCodes', e[0] || undefined);
												form.setFieldValue('cityCodes', e[1] || undefined);
												form.setFieldValue('areaCode', e[2] || undefined);
											}}
										/>
									</Form.Item>
								</Col>
							</Row>
						</Form>
					</div>
					<div className="margin-left-12 padding-left-12 border-solid-left-e5e6eb">
						<Space size={16}>
							<Button onClick={onReset}>重置</Button>
							<Button type="primary" onClick={onSearch}>
								查询
							</Button>
						</Space>
					</div>
				</div>
				{/* 筛选条件 结束 */}
				{/* 表格 开始 */}
				<Table rowKey="id" dataSource={dataSource} pagination={pagination} onChange={changePage} scroll={{ x: 'max-content' }}>
					<Table.Column
						title="序号"
						key="index"
						fixed="left"
						width={60}
						align="center"
						render={(_, __, index) => {
							return index + 1;
						}}
					/>
					<Table.Column title="姓名" dataIndex="name" />
					<Table.Column
						title="组别"
						dataIndex="competitionGroup"
						render={(text) => {
							return (
								<div className={`tag-status-small-${['', 'primary', 'warning', '700eb2'][text]}`}>
									{competitionGroupTextList[text]}
								</div>
							);
						}}
					/>
					<Table.Column
						title="审核状态"
						dataIndex="auditStatus"
						align="center"
						render={(text) => {
							return (
								<div className={`tag-status-small-${['', 'primary', 'warning', 'success'][text]}`}>
									{['', '待审核', '不通过', '已通过'][text]}
								</div>
							);
						}}
					/>
					<Table.Column
						title="工作地区"
						dataIndex="name"
						render={(_, record) => {
							return [record.provinceName || '', record.cityName || '', record.areaName || ''].filter((ov) => ov).join('/') || '--';
						}}
					/>
					<Table.Column title="邀约人" align="center" dataIndex="inviterUserName" render={(text) => text || '--'} />
					<Table.Column title="报名时间" dataIndex="createTime" align="center" />
					<Table.Column
						title="操作"
						key="option"
						dataIndex="option"
						align="center"
						fixed="right"
						render={(_, record) => {
							return (
								<>
									<Button type="link" size="small" onClick={() => linkTo(`/newAchv/competition/signup/detail?id=${record.id}`)}>
										编辑/审核
									</Button>
									<Popconfirm
										title="提示"
										description="确定删除吗？"
										onConfirm={() => {
											delTableData(record.id);
										}}
										okText="确定"
										cancelText="取消"
									>
										<Button type="link" size="small">
											删除
										</Button>
									</Popconfirm>
								</>
							);
						}}
					/>
				</Table>
				{/* 表格 结束 */}
			</div>
		</div>
	);
};

export default Index;
