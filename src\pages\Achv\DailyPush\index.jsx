import { useEffect, useState } from 'react';
import { Table, Button, Popconfirm, Space, Affix, message, InputNumber } from 'antd';
import { useTableData } from '@/hook/useTableData';
import { useRouterLink } from '@/hook/useRouter';
import {
	pageRecommend as getTablePageData,
	removeRecommend as delTableItemData,
	getLastPublishTime,
	publish,
	batchUpdateSort,
} from '@/api/Achv/DailyPush/index';

import { authTextList } from '@/pages/Achv/config';

import dayjs from 'dayjs';

import './index.scss';

const TODAY = dayjs().format('YYYY-MM-DD');

const statusList = [
	{
		label: '今日主推',
		value: TODAY,
	},
	{
		label: '昨日主推',
		value: dayjs().subtract(1, 'day').format('YYYY-MM-DD'),
	},
	{
		label: '前日主推',
		value: dayjs().subtract(2, 'day').format('YYYY-MM-DD'),
	},
];

const Index = () => {
	const { searchParams } = useRouterLink();

	const [recommendData, setRecommendData] = useState(searchParams.get('recommendData') || TODAY);
	const { dataSource, pagination, changePage, delTableData, onUpdateSort } = useTableData({
		params: {
			recommendData,
		},
		getTablePageData,
		delTableItemData,
		batchUpdateSort,
		closeFilter: true,
	});

	// 获取更新时间
	const [lastPublishTime, setLastPublishTime] = useState('');
	const getUpdateTime = () => {
		if (recommendData === TODAY) {
			getLastPublishTime().then((res) => {
				console.log(res);

				setLastPublishTime(res?.data || '');
			});
		} else {
			setLastPublishTime('');
		}
	};

	// 更新数据
	const updateRecommendData = () => {
		publish().then(() => {
			message.success('更新成功');
			getUpdateTime();
		});
	};

	useEffect(() => {
		getUpdateTime();
	}, [recommendData]);

	return (
		<div className="">
			{/* Tabs & 功能按钮 开始 */}
			<Affix offsetTop={0}>
				<div className="flex align-center justify-between padding-24 border-radius-8 bg-color-ffffff border-solid-bottom-f2f3f5">
					<div className="flex align-center">
						{statusList.map((ov) => (
							<div
								key={ov.value}
								className={`a margin-right-40 font-size-18 font-weight-500 ${recommendData === ov.value ? 'color-165dff' : ''}`}
								onClick={() => setRecommendData(ov.value)}
							>
								{ov.label}
							</div>
						))}
					</div>
					<Space size={16}>
						{TODAY === recommendData && (
							<>
								<div>{lastPublishTime}</div>
								<Button type="primary" onClick={updateRecommendData}>
									更新
								</Button>
							</>
						)}
					</Space>
				</div>
			</Affix>
			{/* Tabs & 功能按钮 结束 */}
			<div className="flex-sub margin-top-18 padding-24 border-radius-8 bg-color-ffffff">
				{/* 表格 开始 */}
				<Table rowKey="id" dataSource={dataSource} pagination={pagination} onChange={changePage} scroll={{ x: 'max-content' }}>
					<Table.Column
						title="序号"
						key="index"
						width={60}
						render={(_, __, index) => {
							return index + 1;
						}}
					/>
					<Table.Column title="需求/成果名称" dataIndex="name" render={(text) => <div className="max-width-240">{text}</div>} />
					<Table.Column
						title="类型"
						dataIndex="recommendType"
						align="center"
						render={(text) => {
							return <div className={`tag-status-${['default', 'success', 'primary'][text]}  `}>{['', '需求', '成果'][text]}</div>;
						}}
					/>
					<Table.Column
						title="认证状态"
						dataIndex="isAuth"
						align="center"
						render={(text) => {
							return (
								<div
									style={{
										color: text === 1 ? '#1890ff' : '',
									}}
								>
									{authTextList[text || 0]}
								</div>
							);
						}}
					/>
					<Table.Column
						title="揭榜状态"
						dataIndex="proclamationStatus"
						align="center"
						render={(text) => {
							return <div className={`proclamation-status-${text}`}>{['--', '可揭榜', '揭榜中', '已揭榜'][text || 0]}</div>;
						}}
					/>
					<Table.Column
						title="所属区域"
						dataIndex="provinceCode"
						render={(_, record) => {
							return [record.provinceCodeName, record.cityCodeName, record.areaCodeName].filter((ov) => ov).join('-');
						}}
					/>
					<Table.Column
						title="排序"
						align="center"
						dataIndex="rankingNum"
						render={(_, record) => {
							return (
								<InputNumber
									className="text-align-center"
									defaultValue={record.rankingNum}
									min={1}
									precision={0}
									controls={false}
									onBlur={(e) => {
										const rankingNum = e.target.value - 0 || null;
										if (rankingNum !== record.rankingNum) {
											onUpdateSort([
												{
													id: record.id,
													rankingNum,
												},
											]);
										}
									}}
									placeholder="请输入"
								></InputNumber>
							);
						}}
					/>
					<Table.Column
						title="操作"
						key="option"
						dataIndex="option"
						align="center"
						fixed="right"
						render={(_, record) => {
							return (
								<>
									<Popconfirm
										title="提示"
										description="确定删除吗？"
										onConfirm={() => {
											delTableData(record.id);
										}}
										okText="确定"
										cancelText="取消"
									>
										<Button type="link" size="small">
											删除
										</Button>
									</Popconfirm>
								</>
							);
						}}
					/>
				</Table>
				{/* 表格 结束 */}
			</div>
		</div>
	);
};

export default Index;
