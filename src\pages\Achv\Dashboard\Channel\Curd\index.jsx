import { useEffect } from 'react';
import { useRouterLink } from '@/hook/useRouter';

import { Button, Space, Form, Input, message, Affix, Anchor } from 'antd';

import { getChannel, addChannel, updateChannel } from '@/api/Achv/Dashboard/Channel';

const Index = () => {
	const { linkTo, searchParams } = useRouterLink();
	const [form] = Form.useForm();

	// 取消
	const onCancel = () => {
		linkTo(-1);
	};

	// 提交
	const onSubmit = () => {
		form.validateFields().then((values) => {
			if (values.id) {
				updateChannel(values).then(() => {
					message.success('修改成功');
					setTimeout(() => {
						linkTo(-1);
					}, 500);
				});
			} else {
				addChannel(values).then(() => {
					message.success('添加成功');
					setTimeout(() => {
						linkTo(-1);
					}, 500);
				});
			}
		});
	};

	// 获取详情
	const getDetail = () => {
		const id = searchParams.get('id');
		if (id) {
			getChannel({ id }).then((res) => {
				const resData = res.data || {};
				form.setFieldsValue(resData);
			});
		}
	};

	useEffect(() => {
		getDetail();
	}, []);

	return (
		<div className="">
			<div className="flex flex-shrink align-center margin-bottom-4 height-48 font-size-22 font-weight-500 color-1d2129">
				<Space size={10}>
					<div className="a color-86909c" onClick={() => linkTo(`/newAchv/dashboard/channel/list`)}>
						渠道管理
					</div>
					<div className="color-86909c">/</div>
					<div>渠道编辑</div>
				</Space>
			</div>

			{/* Tabs & 功能按钮 开始 */}
			<div id="section1"></div>
			<Affix>
				<div className="height-66 flex align-center justify-between bg-color-ffffff border-radius-8 padding-lr-24 border-solid-bottom-f2f3f5">
					<div className="flex align-center justify-between color-1d2129 line-height-26 font-size-18 font-weight-500">
						<Anchor
							affix={false}
							rootClassName="anchor-header-tabBar-box"
							direction="horizontal"
							replace
							targetOffset={80}
							items={[
								{
									key: 'section1',
									href: '#section1',
									title: <div className="margin-right-40 font-size-16 font-weight-500">基本信息</div>,
								},
							]}
						/>
					</div>
					<div className="flex align-center justify-between color-1d2129 line-height-26 font-size-18 font-weight-500">
						<Space size={16}>
							<Button onClick={onCancel}>取消</Button>
							<Button type="primary" onClick={onSubmit}>
								保存
							</Button>
						</Space>
					</div>
				</div>
			</Affix>
			{/* Tabs & 功能按钮 结束 */}

			<Form
				form={form}
				className="antd-form-box"
				labelAlign="right"
				labelCol={{ style: { width: '116px' } }}
				initialValues={{
					id: '',
				}}
			>
				<Form.Item hidden name="id">
					<Input />
				</Form.Item>

				{/* 基本信息 开始 */}
				<div className="margin-top-20 padding-20 border-radius-8 bg-color-ffffff">
					<div className="padding-bottom-30 line-height-26 font-size-18 font-weight-500 color-1d2129">基本信息</div>
					<Form.Item label="渠道名称" name="channelName">
						<Input className="input-box" placeholder="请输入渠道名称" />
					</Form.Item>
				</div>
				{/* 基本信息 结束 */}
			</Form>
		</div>
	);
};

export default Index;
