import { Table, Button, Popconfirm, Space, Form, Input, Row, Col, Affix, Tooltip, Tag, Modal } from 'antd';
import QrCode from '@/components/QrCode';
import { useTableData } from '@/hook/useTableData';
import { useRouterLink } from '@/hook/useRouter';
import { pageChannel as getTablePageData, batchDelChannel as delTableItemData } from '@/api/Achv/Dashboard/Channel';

const Index = () => {
	const { linkTo } = useRouterLink();

	const { form, dataSource, pagination, changePage, delTableData, onSearch, onReset } = useTableData({
		getTablePageData,
		delTableItemData,
	});

	return (
		<div>
			<div className="flex flex-shrink align-center margin-bottom-4 height-48 font-size-22 font-weight-500 color-1d2129">渠道管理</div>
			{/* Tabs & 功能按钮 开始 */}
			<Affix offsetTop={0}>
				<div className="flex align-center justify-between padding-24 border-radius-8 bg-color-ffffff border-solid-bottom-f2f3f5">
					<div></div>
					<Space size={16}>
						<Button
							type="primary"
							onClick={() => {
								linkTo('/newAchv/dashboard/channel/curd');
							}}
						>
							新增渠道
						</Button>
					</Space>
				</div>
			</Affix>

			<div className="flex-sub margin-top-18 padding-24 border-radius-8 bg-color-ffffff">
				{/* 筛选条件 开始 */}
				<div className="flex justify-between margin-bottom-18">
					<div className="flex-sub">
						<Form form={form} labelAlign="right" layout="inline">
							<Row className="width-100per" gutter={[12, 12]}>
								<Col span={8}>
									<Form.Item name="channelName" label="渠道名称">
										<Input placeholder="请输入渠道名称" />
									</Form.Item>
								</Col>
							</Row>
						</Form>
					</div>
					<div className="margin-left-12 padding-left-12 border-solid-left-e5e6eb">
						<Space size={16}>
							<Button onClick={onReset}>重置</Button>
							<Button type="primary" onClick={onSearch}>
								查询
							</Button>
						</Space>
					</div>
				</div>
				{/* 筛选条件 结束 */}

				{/* 表格 开始 */}
				<Table rowKey="id" dataSource={dataSource} pagination={pagination} onChange={changePage} scroll={{ x: 'max-content' }}>
					<Table.Column
						title="序号"
						key="index"
						width={60}
						render={(_, __, index) => {
							return index + 1;
						}}
					/>
					<Table.Column title="渠道名称" dataIndex="channelName" render={(text) => text || '--'} />
					<Table.Column
						title="二维码"
						dataIndex="channelQrCodeUrl"
						render={(text) => {
							return (
								<Tooltip
									className="tool-tip"
									title={
										<QrCode.QRCodeUrl
											qrCodeList={[
												{
													qrcodeUrl: text,
													qrcodeName: '二维码',
												},
											]}
										/>
									}
									color="#ffffff"
								>
									<Tag className="cursor-pointer" color="blue">
										查看
									</Tag>
								</Tooltip>
							);
						}}
					/>
					<Table.Column
						title="创建时间"
						dataIndex="createTime"
						render={(text) => {
							return (text || '--').slice(0, 16);
						}}
					/>
					<Table.Column
						title="创建人"
						dataIndex="createByName"
						render={(text) => {
							return text || '--';
						}}
					/>
					<Table.Column
						title="流量统计"
						dataIndex="viewNum"
						align="center"
						render={(text) => {
							return text || '-';
						}}
					/>
					<Table.Column
						title="操作"
						key="option"
						dataIndex="option"
						align="center"
						fixed="right"
						render={(_, record) => {
							return (
								<>
									<Button
										type="link"
										size="small"
										onClick={() => linkTo(`/newAchv/dashboard/channel/detail?id=${record.id}&fromList=1`)}
									>
										编辑
									</Button>
									<Popconfirm
										title="提示"
										description="确定删除吗？"
										onConfirm={() => {
											delTableData(record.id);
										}}
										okText="确定"
										cancelText="取消"
									>
										<Button type="link" size="small">
											删除
										</Button>
									</Popconfirm>
								</>
							);
						}}
					/>
				</Table>
				{/* 表格 结束 */}
			</div>
		</div>
	);
};

export default Index;
