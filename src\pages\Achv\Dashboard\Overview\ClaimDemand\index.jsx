import { FilerForm, ListTable } from '@/pages/Achv/Demand/DemandManage/List';

import { useTableData } from '@/hook/useTableData';
import { useRouterLink } from '@/hook/useRouter';

import { pageToClaim as getTablePageData } from '@/api/Achv/Dashboard/Overview/ClaimDemand';
import { updateRankingNum as batchUpdateSort } from '@/api/Achv/Demand/DemandManage/index';

const Index = () => {
	const { linkTo } = useRouterLink();
	const { form, dataSource, pagination, changePage, onSearch, onReset, delTableData } = useTableData({
		params: {},
		getTablePageData,
		batchUpdateSort,
	});

	return (
		<div className="">
			<div className="flex flex-shrink align-center margin-bottom-4 height-48 font-size-22 font-weight-500 color-1d2129">认领需求</div>

			<div className="flex-sub margin-top-18 padding-24 border-radius-8 bg-color-ffffff">
				{/* 筛选条件 开始 */}
				<FilerForm form={form} onReset={onReset} onSearch={onSearch} openClaimTime />
				{/* 筛选条件 结束 */}
				{/* 表格 开始 */}
				<ListTable dataSource={dataSource} pagination={pagination} changePage={changePage} delTableData={delTableData} />
				{/* 表格 结束 */}
			</div>
		</div>
	);
};

export default Index;
