import DockingList from '@/pages/Achv/Dashboard/Overview/components/DockingList';
import { querySupplierMeetingVoPage as getTablePageData } from '@/api/Achv/Dashboard/Overview/DockingMeeting';
import { dockingStageStatusData } from '@/pages/Achv/config';
const Index = () => {
	return (
		<DockingList
			title="供需对接会"
			getTablePageData={getTablePageData}
			dockingStageStatusData={dockingStageStatusData.filter((ov) => [2, 5, 6].includes(ov.value))}
		/>
	);
};
export default Index;
