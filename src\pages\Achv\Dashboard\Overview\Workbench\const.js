/**
 * @description const - 常量定义
 * <AUTHOR>
 *
 * Created on 2024/9/26 15:54
 */
/* 	数据请求查询字段： 数据类型-1今日实时数据 2数据概况 3对接数据 */
export const QUERY_TYPE = {
	TODAY: 1,
	OVERVIEW: 2,
	DOCKING: 3,
};

/* 	需求分布查询字段： 需求类型-1：全部需求 2：认证需求 */
export const REQUIREMENT_TYPE = {
	ALL: '1',
	AUTH: '2',
};

/* 	需求分布查询字段： 分布条件-1：按揭榜状态 2：按行业领域 3：按揭榜方式 */
export const REQUIREMENT_CONDITION = {
	STATUS: 1,
	INDUSTRY: 2,
	TYPE: 3,
};

/* 		技术经理人活跃度查询字段： 排名条件-1:按挖掘需求 2:按认证需求 3:按负责需求 4:按认领需求 5:按提供线索 */
export const ACTIVITY_CONDITION = {
	CLAIMED: 1,
	AUTH: 2,
	RESPONSIBLE: 3,
	PROVIDE: 4,
	CLAIM: 5,
};

/*
 * 	数据类型code:
 * * 认证需求：auth_demand\n
 * * 认证技术经理人：auth_broker\n
 * * 需求认领：claim_demand\n
 * * 组织三方会议：third_party_meeting\n
 * * 需求总数：total_demand\n
 * * 认证需求总数：total_auth_demand\n
 * * 技术经理人总数：total_broker\n
 * * 认证技术经理人总数：total_auth_broker\n
 * * 成果总数：total_achievement\n
 * * 专家总数：total_expert\n
 * * 机构总数：total_agency\n
 * * 名片总数：total_business_card\n
 * * 需求认领总数：total_claim_demand\n
 * * 活跃技术经理人总数：total_active_broker\n
 * * 三方会议总数：total_third_party_meeting\n
 * * 线索总数：total_send_order_supplier
 * */
export const DATA_UNIT = {
	/* 认证需求 */
	AUTH_DEMAND: '个',
	/* 认证技术经理人 */
	AUTH_BROKER: '人',
	/* 需求认领 */
	CLAIM_DEMAND: '次',
	/* 组织三方会议 */
	THIRD_PARTY_MEETING: '场',
	/* 需求总数 */
	TOTAL_DEMAND: '个',
	/* 认证需求总数 */
	TOTAL_AUTH_DEMAND: '个',
	/* 技术经理人总数 */
	TOTAL_BROKER: '人',
	/* 认证技术经理人总数 */
	TOTAL_AUTH_BROKER: '人',
	/* 成果总数 */
	TOTAL_ACHIEVEMENT: '个',
	/* 专家总数 */
	TOTAL_EXPERT: '人',
	/* 机构总数 */
	TOTAL_AGENCY: '家',
	/* 名片总数 */
	TOTAL_BUSINESS_CARD: '人',
	/* 需求认领总数 */
	TOTAL_CLAIM_DEMAND: '次',
	/* 活跃技术经理人总数 */
	TOTAL_ACTIVE_BROKER: '人',
	/* 三方会议总数 */
	TOTAL_THIRD_PARTY_MEETING: '场',
	/* 线索总数 */
	TOTAL_SEND_ORDER_SUPPLIER: '个',
};

export const DATA_TIPS = {
	TOTAL_ACTIVE_BROKER: '有认领过技术需求的技术经理人',
};

export const DATA_PATH = {
	AUTH_DEMAND: `/newAchv/demand/demandManage/list?isAuth=1&auditStatus=`,
	AUTH_BROKER: (date) => {
		const tempDate = JSON.stringify([date, date]);
		return `/newAchv/brokerManage/list?tempDate=${tempDate}&auditStartTime=${date}&auditEndTime=${date}&auditStatus=3`;
	},
	CLAIM_DEMAND: (date) => {
		const tempDate = JSON.stringify([date, date]);
		return `/newAchv/dashboard/overview/claimDemand?tempDate=${tempDate}&claimStartTime=${date}&claimEndTime=${date}`;
	},
	THIRD_PARTY_MEETING: (date) => {
		const tempDate = JSON.stringify([date, date]);
		return `/newAchv/dashboard/overview/dockingMeeting?tempDate=${tempDate}&startTime=${date}&endTime=${date}`;
	},
	TOTAL_DEMAND: '/newAchv/demand/demandManage/list?auditStatus=',
	TOTAL_AUTH_DEMAND: '/newAchv/demand/demandManage/list?isAuth=1&auditStatus=',
	TOTAL_BROKER: '/newAchv/brokerManage/list?auditStatus=',
	TOTAL_AUTH_BROKER: '/newAchv/brokerManage/list?auditStatus=3',
	TOTAL_ACHIEVEMENT: '/newAchv/achievement/manage/list?auditStatus=',
	TOTAL_EXPERT: '/newAchv/expertManage/list?auditStatus=',
	TOTAL_AGENCY: '/newAchv/agencyManage/list?auditStatus=',
	TOTAL_BUSINESS_CARD: '',
	TOTAL_CLAIM_DEMAND: '/newAchv/dashboard/overview/claimDemand',
	TOTAL_ACTIVE_BROKER: '/newAchv/dashboard/shareStatistics',
	TOTAL_THIRD_PARTY_MEETING: '/newAchv/dashboard/overview/dockingMeeting',
	TOTAL_SEND_ORDER_SUPPLIER: '/newAchv/dashboard/overview/clueStatistics',
};

// 预定义的明亮颜色数组
export const highContrastColors = [
	'#0E42D2',
	'#3491FA',
	'#86E8DD',
	'#8D4EDA',
	'#165DFF',
	'#D91AD9',
	'#FF7D00',
	'#C396ED',
	'#6AA1FF',
	'#F08EE6',
	'#FF9A2E',
	'#009A29',
	'#20CCFF',
	'#0AA5A8',
	'#FFCF8B',
	'#23C343',
	'#9FD4FD',
	'#33D1C9',
	'#551DB0',
	'#7BE188',
];
