/**
 * @description index.jsx - 文件描述
 * <AUTHOR>
 *
 * Created on 2024/9/26 11:43
 */
import React, { useEffect, useState } from 'react';
import dayjs from 'dayjs';
import { <PERSON><PERSON>, Card, Col, Divider, Drawer, Dropdown, Row, Space } from 'antd';
import { LeftCircleOutlined, RightCircleOutlined, LeftOutlined, RightOutlined, DownOutlined } from '@ant-design/icons';
import DataItem from '@/pages/Achv/Dashboard/Overview/components/DataItem';
import { REQUIREMENT_CONDITION, REQUIREMENT_TYPE, ACTIVITY_CONDITION, QUERY_TYPE, DATA_UNIT, DATA_TIPS, DATA_PATH } from './const';
import PieChart from '@/pages/Achv/Dashboard/Overview/components/PieChart';
import LineChart from '@/pages/Achv/Dashboard/Overview/components/LineChart';
import { queryAchievementData, brokerRanking, demandDistribution } from '@/api/Achv/Dashboard/Overview/Workbench';

const now = dayjs();
/* 需求分布-下拉项 */
const requirementDistribution = [
	{ label: '全部需求', key: REQUIREMENT_TYPE.ALL },
	{ label: '认证需求', key: REQUIREMENT_TYPE.AUTH },
];
/* 需求分布-分布条件选项 */
const requirementDistributionOptions = [
	{ label: '按揭榜状态', value: REQUIREMENT_CONDITION.STATUS },
	{ label: '按行业领域', value: REQUIREMENT_CONDITION.INDUSTRY },
	{ label: '按揭榜方式', value: REQUIREMENT_CONDITION.TYPE },
];
/* 技术经理人活跃度Top10-排名条件选项 排名条件-1:按挖掘需求 2:按认证需求 3:按负责需求 4:按认领需求 5:按提供线索 */
const activeOptions = [
	{ label: '按认领需求', value: ACTIVITY_CONDITION.PROVIDE },
	{ label: '按挖掘需求', value: ACTIVITY_CONDITION.CLAIMED },
	{ label: '按负责需求', value: ACTIVITY_CONDITION.RESPONSIBLE },
	{ label: '按提供线索', value: ACTIVITY_CONDITION.CLAIM },
	{ label: '按认证需求', value: ACTIVITY_CONDITION.AUTH },
];

const Workbench = () => {
	/* 小程序活跃概况 */
	const [miniActiveData, setMiniActiveData] = useState([]);
	/* 小程序数据情况 */
	const [miniData, setMiniData] = useState([]);
	/* 小程序对接数据 */
	const [increaseData, setIncreaseData] = useState([]);
	/* 活跃度时间 */
	const [activeTime, setActiveTime] = useState(now);
	/* 数据情况时间 */
	const [dataTime, setDataTime] = useState(now);
	/* 对接数据时间 */
	const [increaseTime, setIncreaseTime] = useState(now);
	/* 切换需求分布下拉项 */
	const [requirementSelectValue, setRequirementSelectValue] = useState(requirementDistribution[1]);
	/* 需求分布-选中项 */
	const [requirementActive, setRequirementActive] = useState(REQUIREMENT_CONDITION.STATUS);
	/* 技术经理人活跃度Top10-选中项 */
	const [conditionActive, setConditionActive] = useState(ACTIVITY_CONDITION.PROVIDE);
	/* 需求分布数据 */
	const [requireData, setRequireData] = useState([]);
	/* 技术经理人活跃度数据 */
	const [activeData, setActiveData] = useState([]);
	useEffect(() => {
		getMiniActiveData();
		getMiniData();
		getIncreaseData();
		getRequireData();
		getActiveData();
	}, []);

	/* 获取小程序活跃概况 */
	const getMiniActiveData = async (time) => {
		const params = {
			dataType: QUERY_TYPE.TODAY,
			queryDate: (time || activeTime).format('YYYY-MM-DD'),
		};
		const res = await queryAchievementData(params);
		if (res.data) {
			console.log('获取小程序活跃概况', res.data);
			const data = res.data.dataOverviewList.map((item) => {
				const path = DATA_PATH[item.code.toUpperCase()];
				return {
					...item,
					title: item.name,
					total: item.currentCount,
					unit: DATA_UNIT[item.code.toUpperCase()],
					tips: DATA_TIPS[item.code.toUpperCase()],
					path: typeof path === 'function' ? path((time || activeTime).format('YYYY-MM-DD')) : path,
					/* 较昨日新增 */
					increase: item.yesterdayCount,
					/* 较7日前新增 */
					increase7: item.sevenDaysCount,
				};
			});
			setMiniActiveData(data);
		}
	};
	/* 获取数据情况 */
	const getMiniData = async (time) => {
		const params = {
			dataType: QUERY_TYPE.OVERVIEW,
			queryDate: (time || dataTime).format('YYYY-MM-DD'),
		};
		const res = await queryAchievementData(params);
		if (res.data) {
			console.log('获取数据情况', res.data);
			const data = res.data.dataOverviewList.map((item) => {
				return {
					...item,
					title: item.name,
					total: item.currentCount,
					unit: DATA_UNIT[item.code.toUpperCase()],
					tips: DATA_TIPS[item.code.toUpperCase()],
					path: DATA_PATH[item.code.toUpperCase()],
					/* 较昨日新增 */
					increase: item.yesterdayCount,
					/* 较7日前新增 */
					increase7: item.sevenDaysCount,
				};
			});
			setMiniData(data);
		}
	};
	/* 获取对接数据 */
	const getIncreaseData = async (time) => {
		const params = {
			dataType: QUERY_TYPE.DOCKING,
			queryDate: (time || increaseTime).format('YYYY-MM-DD'),
		};
		const res = await queryAchievementData(params);
		if (res.data) {
			console.log('获取对接数据', res.data);
			const data = res.data.dataOverviewList.map((item) => {
				return {
					...item,
					title: item.name,
					total: item.currentCount,
					unit: DATA_UNIT[item.code.toUpperCase()],
					tips: DATA_TIPS[item.code.toUpperCase()],
					path: DATA_PATH[item.code.toUpperCase()],
					/* 较昨日新增 */
					increase: item.yesterdayCount,
					/* 较7日前新增 */
					increase7: item.sevenDaysCount,
				};
			});
			setIncreaseData(data);
		}
	};
	/* 修改时间 */
	const changeTime = (type, value) => {
		/* option为增加或减少 */
		// const time = preTime.add(option, 'day');
		switch (type) {
			case 'active':
				setActiveTime(value);
				getMiniActiveData(value);
				break;
			case 'data':
				setDataTime(value);
				getMiniData(value);
				break;
			case 'increase':
				setIncreaseTime(value);
				getIncreaseData(value);
				break;
		}
	};

	/* 获取需求分布 */
	const getRequireData = async (args) => {
		const params = {
			demandType: requirementSelectValue.key,
			demandDistributionStatus: requirementActive,
			...args,
		};
		const res = await demandDistribution(params);
		if (res.data) {
			console.log('获取需求分布', res.data);
			setRequireData(res.data);
		}
	};

	/* 获取技术经理人活跃度Top10数据 */
	const getActiveData = async (args) => {
		const params = {
			brokerRankingCondition: conditionActive,
			...args,
		};
		const res = await brokerRanking(params);
		if (res.data) {
			console.log('获取技术经理人活跃度Top10数据', res.data);
			setActiveData(res.data);
		}
	};

	/* 切换切换需求分布下拉项 */
	const handleChangeRequirementType = ({ key }) => {
		console.log(key);
		const selectItem = requirementDistribution.find((item) => item.key === key);
		setRequirementSelectValue(selectItem);
		getRequireData({ demandType: selectItem.key });
	};
	/* 切换分布条件选项 */
	const handleChangeRequirementActive = (value) => {
		setRequirementActive(value);
		getRequireData({ demandDistributionStatus: value });
	};
	/* 切换排名条件选项 */
	const handleChangeConditionActive = (value) => {
		setConditionActive(value);
		getActiveData({ brokerRankingCondition: value });
	};

	return (
		<div className={`position-absolute inset-0 margin-auto flex flex-direction-column margin-lr-32`}>
			<div className="flex flex-shrink align-center margin-bottom-18 height-48 font-size-22 font-weight-500 color-1d2129">数据总览</div>
			{/* 小程序活跃概况 */}
			<Card bordered={false} styles={{ body: { paddingBottom: 0 } }}>
				<div className={'flex flex-direction-column'}>
					<div className={'font-weight-500 font-size-16 color-1d2129 line-height-24'}>小程序活跃概况</div>
					<Space className={'margin-top-6'}>
						<Button
							size={'small'}
							shape="circle"
							type={'text'}
							icon={<LeftCircleOutlined />}
							onClick={() => changeTime('active', activeTime.subtract(1, 'day'))}
						/>
						<span>{activeTime?.format('YYYY-MM-DD')}</span>
						<Button
							size={'small'}
							shape="circle"
							type={'text'}
							icon={<RightCircleOutlined />}
							disabled={dayjs().isSame(activeTime, 'day')}
							onClick={() => changeTime('active', activeTime.add(1, 'day'))}
						/>
					</Space>
					<Row gutter={48}>
						{miniActiveData?.map((item, index) => {
							return (
								<Col
									key={index}
									span={6}
									className={'margin-tb-24'}
									style={{ borderRight: (index !== miniActiveData.length - 1 || (index + 1) % 4 !== 0) && '1px solid #f2f3f5' }}
								>
									<DataItem key={index} {...item} />
								</Col>
							);
						})}
					</Row>
				</div>
			</Card>
			{/* 整体数据 */}
			<Card bordered={false} className={'margin-top-24'} styles={{ body: { paddingBottom: 0 } }}>
				<div className={'flex flex-direction-column'}>
					<div className={'font-weight-500 font-size-16 color-1d2129 line-height-24'}>整体数据</div>
					<Space className={'margin-top-6'}>
						<Button
							size={'small'}
							shape="circle"
							type={'text'}
							icon={<LeftCircleOutlined />}
							onClick={() => changeTime('data', dataTime.subtract(1, 'day'))}
						/>
						<span>{dataTime?.format('YYYY-MM-DD')}</span>
						<Button
							size={'small'}
							shape="circle"
							type={'text'}
							icon={<RightCircleOutlined />}
							disabled={dayjs().isSame(dataTime, 'day')}
							onClick={() => changeTime('data', dataTime.add(1, 'day'))}
						/>
					</Space>
					<Row gutter={48}>
						{miniData?.map((item, index) => {
							return (
								<Col
									key={index}
									span={6}
									className={'margin-tb-24'}
									style={{ borderRight: (index !== miniData.length - 1 || (index + 1) % 4 !== 0) && '1px solid #f2f3f5' }}
								>
									<DataItem key={index} {...item} />
								</Col>
							);
						})}
					</Row>
				</div>
			</Card>
			{/* 对接总览情况 */}
			<Card bordered={false} className={'margin-top-24'} styles={{ body: { paddingBottom: 0 } }}>
				<div className={'flex flex-direction-column'}>
					<div className={'font-weight-500 font-size-16 color-1d2129 line-height-24'}>对接总览</div>
					<Space className={'margin-top-6'}>
						<Button
							size={'small'}
							shape="circle"
							type={'text'}
							icon={<LeftCircleOutlined />}
							onClick={() => changeTime('increase', increaseTime.subtract(1, 'day'))}
						/>
						<span>{increaseTime?.format('YYYY-MM-DD')}</span>
						<Button
							size={'small'}
							shape="circle"
							type={'text'}
							icon={<RightCircleOutlined />}
							disabled={dayjs().isSame(increaseTime, 'day')}
							onClick={() => changeTime('increase', increaseTime.add(1, 'day'))}
						/>
					</Space>
					<Row gutter={48}>
						{increaseData?.map((item, index) => {
							return (
								<Col
									key={index}
									span={6}
									className={'margin-tb-24'}
									style={{ borderRight: (index !== increaseData.length - 1 || (index + 1) % 4 !== 0) && '1px solid #f2f3f5' }}
								>
									<DataItem key={index} {...item} />
								</Col>
							);
						})}
					</Row>
				</div>
			</Card>
			{/* 图表统计 */}
			<Row gutter={20}>
				<Col span={12}>
					<Card bordered={false} className={'margin-top-24 height-100per'} styles={{ body: { height: '100%' } }}>
						<div className={'flex justify-between align-center'}>
							<div className={'font-weight-500 font-size-16 color-1d2129 line-height-24'}>需求分布</div>
							<div className={'font-weight-400 font-size-14 color-1d2129'}>
								<Dropdown menu={{ items: requirementDistribution, onClick: handleChangeRequirementType }}>
									<Space>
										<span className={'cursor-pointer'}>{requirementSelectValue?.label}</span>
										<DownOutlined />
									</Space>
								</Dropdown>
							</div>
						</div>
						<div className={'flex align-center gap-12 font-weight-400 font-size-14 margin-top-20'}>
							<div className={'color-1d2129'}>分布条件</div>
							{requirementDistributionOptions.map((item) => {
								return (
									<div
										key={item.value}
										onClick={() => handleChangeRequirementActive(item.value)}
										className={`padding-lr-16 padding-tb-4 border-solid-f2f3f5 border-radius-16 cursor-pointer ${
											requirementActive === item.value ? 'font-weight-500 color-165dff bg-color-f2f3f5' : 'color-4e5969'
										}`}
									>
										{item.label}
									</div>
								);
							})}
						</div>
						<div className={'margin-top-24 flex-sub'}>
							<PieChart data={requireData} />
						</div>
					</Card>
				</Col>
				<Col span={12}>
					<Card bordered={false} className={'margin-top-24 height-100per'} styles={{ body: { height: '100%' } }}>
						<div className={'flex justify-between align-center'}>
							<div className={'font-weight-500 font-size-16 color-1d2129 line-height-24'}>技术经理人活跃度Top10</div>
						</div>
						<div className={'flex align-start gap-12 font-weight-400 font-size-14 margin-top-20'}>
							<div className={'color-1d2129 padding-tb-4'}>排名条件</div>
							<div className={'flex gap-12 flex-sub flex-wrap'}>
								{activeOptions.map((item) => {
									return (
										<div
											key={item.value}
											onClick={() => handleChangeConditionActive(item.value)}
											className={`padding-lr-16 padding-tb-4 margin-bottom-6 border-solid-f2f3f5 border-radius-16 cursor-pointer ${
												conditionActive === item.value ? 'font-weight-500 color-165dff bg-color-f2f3f5' : 'color-4e5969'
											}`}
										>
											{item.label}
										</div>
									);
								})}
							</div>
						</div>
						<div className={'margin-top-24'}>
							<LineChart data={activeData} />
						</div>
					</Card>
				</Col>
			</Row>
		</div>
	);
};
export default Workbench;
