/**
 * @description DataItem - 文件描述
 * <AUTHOR>
 *
 * Created on 2024/9/26 13:42
 */
import React from 'react';
import { Space, Tooltip } from 'antd';
import { InfoCircleOutlined, RightOutlined } from '@ant-design/icons';
import { useRouterLink } from '@/hook/useRouter';
const DataItem = ({ title, total, unit, leftTitle = '较昨日', increase = 0, rightTitle = '较7日前', increase7 = 0, tips, path }) => {
	const { linkTo } = useRouterLink();
	return (
		<div
			className={`${path ? 'a' : ''} flex flex-direction-column gap-6`}
			onClick={() => {
				path && linkTo(path);
			}}
		>
			<div className={'flex align-center font-weight-500 font-size-14 color-1d2129'}>
				{title}
				{tips && (
					<Tooltip title={tips} color={'rgba(0,0,0,0.75)'}>
						<InfoCircleOutlined className={'margin-left-6'} />
					</Tooltip>
				)}
			</div>
			<div className={'font-size-24 color-1d2129 font-weight-600 line-height-30'}>
				{total}
				<span className={'font-weight-400 font-size-14 line-height-24 margin-left-6'}>{unit}</span>
			</div>
			<Space size={24} className={'flex align-center font-weight-400 color-86909c line-height-24'}>
				<div>
					{leftTitle}
					<span className={`margin-left-6 ${increase > 0 ? 'color-00b42a' : 'color-f53f3f'}`}>
						{increase > 0 ? '+' : ''}
						{`${increase}` === '0' ? '-' : increase}
					</span>
				</div>
				<div>
					{rightTitle}
					<span className={`margin-left-6 ${increase7 > 0 ? 'color-00b42a' : 'color-f53f3f'}`}>
						{increase7 > 0 ? '+' : ''}
						{`${increase7}` === '0' ? '-' : increase7}
					</span>
				</div>
			</Space>
		</div>
	);
};
export default DataItem;
