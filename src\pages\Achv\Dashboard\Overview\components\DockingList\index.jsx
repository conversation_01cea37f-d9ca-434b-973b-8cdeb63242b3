import { useState, useEffect } from 'react';
import { Table, Button, Space, Form, Input, Row, Col, Affix, Select } from 'antd';

import FormComp from '@/components/FormComp';

import { useTableData } from '@/hook/useTableData';
import { useRouterLink } from '@/hook/useRouter';

import { dockingStageStatusDataTextList } from '@/pages/Achv/config';

const tabsData = [
	{
		label: '需求对接',
		value: 1,
		countName: 'demandTotal',
	},
	{
		label: '成果对接',
		value: 2,
		countName: 'achievementTotal',
	},
];

const Index = (props = {}) => {
	const { getTablePageData = () => {}, title = '' } = props;
	const { linkTo, searchParams } = useRouterLink();

	const [sourceType, setSourceType] = useState(searchParams.get('sourceType') - 0 || 1);
	const { form, dataSource, pagination, changePage, onSearch, onReset } = useTableData({
		params: { sourceType },
		getTablePageData,
		pageCallback: (paramsData = {}) => {
			getStatisticsData({ ...paramsData });
		},
	});

	// 获取统计数据
	const [statistics, setStatistics] = useState({});
	const getStatisticsData = (paramsData) => {
		delete paramsData.sourceType;

		Promise.all([getTablePageData({ ...paramsData, sourceType: 1 }), getTablePageData({ ...paramsData, sourceType: 2 })]).then((resList) => {
			console.log(resList);
			setStatistics({
				demandTotal: resList[0]?.data?.total || 0,
				achievementTotal: resList[1]?.data?.total || 0,
			});
		});
	};

	// 跳转路径
	const [path, setPath] = useState('');
	useEffect(() => {
		const path = sourceType == 1 ? '/newAchv/demand/demandManage' : '/newAchv/achievement/manage';
		setPath(path);
	}, [sourceType]);

	return (
		<div className="">
			<div className="flex flex-shrink align-center margin-bottom-4 height-48 font-size-22 font-weight-500 color-1d2129">{title}</div>
			{/* Tabs & 功能按钮 开始 */}
			<Affix offsetTop={0}>
				<div className="flex align-center justify-between padding-24 border-radius-8 bg-color-ffffff border-solid-bottom-f2f3f5">
					<div className="flex align-center">
						{tabsData.map((ov) => (
							<div
								key={ov.value}
								className={`a margin-right-40 font-size-18 font-weight-500 ${sourceType === ov.value ? 'color-165dff' : ''}`}
								onClick={() => setSourceType(ov.value)}
							>
								{ov.label}
								{statistics[ov.countName] > 0 ? `（${statistics[ov.countName]}）` : ''}
							</div>
						))}
					</div>
					<Space size={16}></Space>
				</div>
			</Affix>
			{/* Tabs & 功能按钮 结束 */}
			<div className="flex-sub margin-top-18 padding-24 border-radius-8 bg-color-ffffff">
				{/* 筛选条件 开始 */}
				<div className="flex justify-between margin-bottom-18">
					<div className="flex-sub">
						<Form
							form={form}
							labelAlign="right"
							layout="inline"
							initialValues={{
								startTime: undefined,
								endTime: undefined,
								tempDate: ['', ''],
							}}
						>
							<Form.Item hidden name="startTime">
								<Input placeholder="" />
							</Form.Item>
							<Form.Item hidden name="endTime">
								<Input placeholder="" />
							</Form.Item>
							<Row className="width-100per" gutter={[12, 12]}>
								<Col span={12}>
									<Form.Item name="tempDate" label="线索时间">
										<FormComp.DatePicker.RangePicker
											placeholder={['开始时间', '结束时间']}
											onChange={(date) => {
												form.setFieldsValue({
													startTime: date[0],
													endTime: date[1],
												});
											}}
										/>
									</Form.Item>
								</Col>
								<Col span={8}>
									<Form.Item name="stageStatus" label="线索状态">
										<Select options={props.dockingStageStatusData} allowClear placeholder="请选择线索状态" />
									</Form.Item>
								</Col>
							</Row>
						</Form>
					</div>
					<div className="margin-left-12 padding-left-12 border-solid-left-e5e6eb">
						<Space size={16}>
							<Button onClick={onReset}>重置</Button>
							<Button type="primary" onClick={onSearch}>
								查询
							</Button>
						</Space>
					</div>
				</div>
				{/* 筛选条件 结束 */}
				{/* 表格 开始 */}
				<Table rowKey="id" dataSource={dataSource} pagination={pagination} onChange={changePage} scroll={{ x: 'max-content' }}>
					<Table.Column
						title="序号"
						key="index"
						width={60}
						render={(_, __, index) => {
							return index + 1;
						}}
					/>
					<Table.Column
						title={sourceType === 1 ? '需求名称' : '成果名称'}
						dataIndex="sourceName"
						render={(text, record) => {
							return text ? (
								<div
									className={`${record.sourceId ? 'a color-165dff' : ''} max-width-400`}
									onClick={() => {
										if (record.sourceId) {
											linkTo(`${path}/detail?id=${record.sourceId}`);
										}
									}}
								>
									{text}
								</div>
							) : (
								'--'
							);
						}}
					/>
					<Table.Column
						title="供给方"
						dataIndex="supplierName"
						render={(text) => {
							return text || '--';
						}}
					/>
					<Table.Column
						title="线索状态"
						dataIndex="stageStatus"
						render={(text) => {
							return (
								<div
									className={` tag-status-${
										['error', 'warning', 'primary', 'success', 'default', 'primary', 'primary'][text || 0]
									}`}
								>
									{dockingStageStatusDataTextList[text || 0]}
								</div>
							);
						}}
					/>
					<Table.Column
						title="跟进人"
						dataIndex="brokerName"
						render={(text) => {
							return text || '--';
						}}
					/>
					<Table.Column
						title="更新时间"
						dataIndex="meetingTime"
						render={(text) => {
							return (text || '').slice(0, 16);
						}}
					/>
					<Table.Column
						title="操作"
						key="option"
						dataIndex="option"
						align="center"
						fixed="right"
						render={(_, record) => {
							return (
								<>
									<Button type="link" size="small" onClick={() => linkTo(`${path}/docking/detail?id=${record.id}`)}>
										详情
									</Button>
								</>
							);
						}}
					/>
				</Table>
				{/* 表格 结束 */}
			</div>
		</div>
	);
};

export default Index;
