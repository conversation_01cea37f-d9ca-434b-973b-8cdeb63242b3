/**
 * @description LineChart - 文件描述
 * <AUTHOR>
 *
 * Created on 2024/9/26 17:02
 */
import React, {useEffect, useMemo} from 'react';
import * as echarts from 'echarts';

const LineChart = (props) => {
    const chartRef = React.createRef();

    const [computedData, computedLabel] = useMemo(() => {
        const data = [];
        const label = [];
        props.data.forEach((item) => {
            data.push(+item.brokerCount);
            label.push(item.brokerName);
        });
        return [data, label];
    }, [props.data]);
    useEffect(() => {
        if (props.data && props.data.length > 0 && chartRef.current) {
            const option = {
                xAxis: {
                    // max: 'dataMax'，
                    type: 'value',
                    axisTick: {
                        show: false
                    },
                    splitLine: {
                        show: false
                    },
                    axisLine: {
                        show: false
                    },
                    show: false,
                    label: {
                        show: false,
                        fontWeight: 600
                    }
                },
                yAxis: {
                    type: 'category',
                    data: computedLabel,
                    inverse: true,
                    animationDuration: 300,
                    animationDurationUpdate: 300,
                    axisLine: {
                        show: true
                    },
                    axisTick: {
                        show: false
                    },
                    // max: 2 // only the largest 3 bars will be displayed
                },
                series: [
                    {
                        realtimeSort: true,
                        name: 'X',
                        type: 'bar',
                        data: computedData,
                        barCategoryGap: 40,
                        label: {
                            show: true,
                            position: 'right',
                            valueAnimation: true,
                        },
                        itemStyle: {
                            borderRadius: [0, 5, 5, 0],
                            color: '#4080ff'
                        },
                        barWidth: 10,
                    },
                ],
                legend: {
                    show: false,
                },
                grid: { // 添加 grid 配置
                    top: '5%', // 设置顶部间距
                    bottom: '5%' // 设置底部间距
                },
                tooltip: {
                    trigger: 'item',
                    formatter: '{b0}: {c0}'
                },
                animationDuration: 0,
                animationDurationUpdate: 1000,
                animationEasing: 'linear',
                animationEasingUpdate: 'linear'
            };
            const chart = echarts.init(chartRef.current);
            chart.setOption({
                ...option,
            });
        }
    }, [chartRef.current, computedData, computedLabel]);
    return <div ref={chartRef} style={{ height: 400, maxWidth: '100%' }} />;
}
export default LineChart;