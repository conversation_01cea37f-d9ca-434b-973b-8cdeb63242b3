/**
 * @description PieChart.jsx - 文件描述
 * <AUTHOR>
 *
 * Created on 2024/9/26 17:00
 */
import { Pie } from '@ant-design/plots';
import * as echarts from 'echarts';
// echarts

import React, { useMemo } from 'react';
import { highContrastColors } from '@/pages/Achv/Dashboard/Overview/Workbench/const';

const PieChart = (props) => {
	const chartRef = React.useRef(null);
	const computedData = useMemo(() => {
		return props.data
			.map((item) => ({
				value: Number(item.demandCount),
				name: item.demandTypeName,
				...item,
			}))
			.filter((item) => item.name);
	}, [props.data]);
	React.useEffect(() => {
		if (chartRef.current && computedData) {
			const chart = echarts.init(chartRef.current);
			const options = {
				color: highContrastColors,
				tooltip: {
					trigger: 'item',
					formatter: (params, ticket, callback) => {
						return `<div style="display: flex;flex-direction: column;gap: 8px;">
                               <div style="color: ${params.color};display: flex;align-items: center;">
                                   <span style="width:6px;height: 6px;background: ${params.color};margin-right: 6px;border-radius: 50%;display: inline;"></span>
                                   <div style="font-weight: bold">${params.name}</div>
                               </div>
                               <div>数量: ${params.value}</div>
                               <div>占比: ${params.percent}%</div>
                           </div>`;
					},
				},
				legend: {
					bottom: '5%',
					left: 'center',
					icon: 'circle',
				},
				series: [
					{
						name: '需求分布',
						type: 'pie',
						radius: ['40%', '60%'],
						center: ['50%', '40%'],
						avoidLabelOverlap: false,
						label: {
							show: false,
							position: 'center',
						},
						emphasis: {
							label: {
								show: true,
								fontSize: 20,
								fontWeight: 'bold',
							},
						},
						labelLine: {
							show: false,
						},
						data: computedData,
					},
				],
			};
			chart.setOption(options);
		}
	}, [computedData]);

	return <div ref={chartRef} style={{ width: '100%', height: 400 }} />;
};
export default PieChart;
