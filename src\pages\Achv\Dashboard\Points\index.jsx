import { useState } from 'react';
import { Table, Button, Space, Form, Row, Col, Select } from 'antd';

import FormComp from '@/components/FormComp';

import { useTableData } from '@/hook/useTableData';
import { allPoints as getTablePageData, exportPoints as exportTableData } from '@/api/Achv/Dashboard/Points';
import dayjs from 'dayjs';

const fixedNameList = [
	{
		title: '用户名称',
		dataIndex: '用户名称',
		fixed: 'left',
		width: 100,
	},
	{
		title: '任务完成情况',
		children: [
			{
				title: '100分有效积分',
				dataIndex: '100分有效积分任务',
				align: 'center',
				width: 140,
			},
			{
				title: '内容创作',
				dataIndex: '内容创作任务',
				align: 'center',
				width: 110,
			},
			{
				title: '名片分享',
				dataIndex: '名片分享任务',
				align: 'center',
				width: 110,
			},
			{
				title: '小程序分享',
				dataIndex: '小程序分享任务',
				align: 'center',
				width: 110,
			},
		],
	},
	{
		title: '积分分类统计',
		children: [
			{
				title: '名片分享',
				dataIndex: '名片分享',
				align: 'center',
				width: 90,
			},
			{
				title: '三方会议',
				dataIndex: '三方会议',
				align: 'center',
				width: 90,
			},
			{
				title: '二维码导流',
				dataIndex: '二维码导流',
				align: 'center',
				width: 110,
			},
			{
				title: '企业走访',
				dataIndex: '企业走访',
				align: 'center',
				width: 90,
			},
			{
				title: '入驻邀约',
				dataIndex: '入驻邀约',
				align: 'center',
				width: 90,
			},
			{
				title: '内容被评论',
				dataIndex: '内容被评论',
				align: 'center',
				width: 100,
			},
			{
				title: '分享导流',
				dataIndex: '分享导流',
				align: 'center',
				width: 90,
			},
			{
				title: '动态创作',
				dataIndex: '动态创作',
				align: 'center',
				width: 90,
			},
			{
				title: '岗位挖掘',
				dataIndex: '岗位挖掘',
				align: 'center',
				width: 90,
			},
			{
				title: '成果挖掘',
				dataIndex: '成果挖掘',
				align: 'center',
				width: 90,
			},
			{
				title: '报名邀约',
				dataIndex: '报名邀约',
				align: 'center',
				width: 90,
			},
			{
				title: '拼团创建',
				dataIndex: '拼团创建',
				align: 'center',
				width: 90,
			},
			{
				title: '活动创建',
				dataIndex: '活动创建',
				align: 'center',
				width: 90,
			},
			{
				title: '特殊奖励',
				dataIndex: '特殊奖励',
				align: 'center',
				width: 90,
			},
			{
				title: '评论创作',
				dataIndex: '评论创作',
				align: 'center',
				width: 90,
			},
			{
				title: '邀约创作',
				dataIndex: '邀约创作',
				align: 'center',
				width: 90,
			},
			{
				title: '需求挖掘',
				dataIndex: '需求挖掘',
				align: 'center',
				width: 90,
			},
		],
	},
	{
		title: '总计',
		children: [
			{
				title: '累计积分',
				dataIndex: '累计积分',
				fixed: 'right',
				align: 'center',
				width: 90,
			},
			{
				title: '考核积分',
				dataIndex: '考核积分',
				fixed: 'right',
				align: 'center',
				width: 90,
			},
		],
	},
];

const Index = () => {
	const [dataSource, setDataSource] = useState([]);
	const { form, exportData, onSearch, onReset } = useTableData({
		getTablePageData,
		exportTableData,
		getPageResult: (resData) => {
			setDataSource(resData);
			return new Promise((resolve) => {
				resolve(resData);
			});
		},
	});
	return (
		<div>
			<div className="flex flex-shrink align-center justify-between margin-bottom-4 height-48">
				<div className="font-size-22 font-weight-500 color-1d2129">分享统计</div>

				<Button type="primary" onClick={exportData}>
					导出文件
				</Button>
			</div>
			{/* Tabs & 功能按钮 开始 */}

			<div className="flex-sub margin-top-18 padding-24 border-radius-8 bg-color-ffffff">
				{/* 筛选条件 开始 */}
				<div className="flex justify-between margin-bottom-18">
					<div className="flex-sub">
						<Form
							form={form}
							labelAlign="left"
							layout="inline"
							initialValues={{
								month: dayjs().format('YYYY-MM'),
								sortType: 0,
							}}
						>
							<Row className="width-100per" gutter={[12, 12]}>
								<Col span={121}>
									<Form.Item name="month" label="统计月份">
										<FormComp.DatePicker picker="month" valueFormat="YYYY-MM" placeholder="请输入统计月份" />
									</Form.Item>
								</Col>
								<Col span={121}>
									<Form.Item name="sortType" label="排序">
										<Select
											placeholder="请选择排序"
											allowClear
											options={[
												{ value: 0, label: '按累计积分倒序' },
												{ value: 1, label: '按考核积分倒序' },
											]}
										/>
									</Form.Item>
								</Col>
							</Row>
						</Form>
					</div>
					<div className="margin-left-12 padding-left-12 border-solid-left-e5e6eb">
						<Space size={16}>
							<Button onClick={onReset}>重置</Button>
							<Button type="primary" onClick={onSearch}>
								查询
							</Button>
						</Space>
					</div>
				</div>
				{/* 筛选条件 结束 */}

				{/* 表格 开始 */}
				<Table
					rowKey="name"
					dataSource={dataSource}
					pagination={false}
					bordered
					scroll={{ x: 'max-content', y: 55 * 10 }}
					columns={[
						{
							title: '序号',
							dataIndex: 'index',
							fixed: 'left',
							width: 80,
							render: (_, __, index) => {
								return index + 1;
							},
						},
						...fixedNameList,
					]}
				></Table>
				{/* 表格 结束 */}
			</div>
		</div>
	);
};

export default Index;
