import { useEffect } from 'react';
import { Table, Button, Space, Form, Input, Row, Col, Select } from 'antd';

import FormComp from '@/components/FormComp';

import { useTableData } from '@/hook/useTableData';
import { useRouterLink } from '@/hook/useRouter';
import { pageTechnicalBrokerActive as getTablePageData, exportBrokerData as exportTableData } from '@/api/Achv/Dashboard/ShareStatistics';
import dayjs from 'dayjs';

const workStatusOptions = [
	{ value: 1, label: '在职' },
	{ value: 0, label: '离职' },
];

const companyOptions = [
	{ value: '大湾区科技创新服务中心（广州）股份有限公司', label: '大湾区科技创新服务中心（广州）股份有限公司' },
	{ value: '汤逊湖科技创新中心（武汉）有限公司', label: '汤逊湖科技创新中心（武汉）有限公司' },
	{ value: '知创知识产权（广州）有限公司', label: '知创知识产权（广州）有限公司' },
	{ value: '湾创策能科技服务（广州）有限公司', label: '湾创策能科技服务（广州）有限公司' },
	{ value: '湾创智策人才服务（广州）有限公司', label: '湾创智策人才服务（广州）有限公司' },
	{ value: '湾创企赋（广州）信息技术有限公司', label: '湾创企赋（广州）信息技术有限公司' },
	{ value: '广州蒂湾创业人才服务有限公司', label: '广州蒂湾创业人才服务有限公司' },
	{ value: '广州凤科湾创文化传播有限公司', label: '广州凤科湾创文化传播有限公司' },
	{ value: '广州湾顶创业服务有限公司', label: '广州湾顶创业服务有限公司' },
	{ value: '广州金凯长清信息科技有限公司', label: '广州金凯长清信息科技有限公司' },
	{ value: '广州市黄埔区粤港澳大湾区青年创新创业促进会', label: '广州市黄埔区粤港澳大湾区青年创新创业促进会' },
];

const sortTypeOption = [
	{ value: 1, label: '需求挖掘', dataIndex: 'excavatePersonnelCount' },
	{ value: 2, label: '需求认证', dataIndex: 'authPersonnelCount' },
	{ value: 3, label: '需求负责', dataIndex: 'responsiblePersonnelCount' },
	{ value: 4, label: '需求跟进', dataIndex: 'brokerCount' },
	{ value: 5, label: '需求分享', dataIndex: 'demandShareCount' },
	{ value: 6, label: '成果分享', dataIndex: 'achievementShareCount' },
	{ value: 7, label: '资讯分享', dataIndex: 'infoShareCount' },
	{ value: 8, label: '实时简报分享', dataIndex: 'realInfoShareCount' },
	{ value: 10, label: '活动分享', dataIndex: 'activityShareCount' },
	{ value: 11, label: '大赛海报分享', dataIndex: 'competitionShareCount' },
	{ value: 12, label: '拼团海报分享', dataIndex: 'groupShareCount' },
	{ value: 13, label: '需求预判分享', dataIndex: 'predictShareCount' },
	{ value: 14, label: '直播海报分享', dataIndex: 'liveShareCount' },
	{ value: 9, label: '分享总数' },
];

const Index = () => {
	const { searchParams } = useRouterLink();

	const { form, dataSource, pagination, changePage, exportData, onSearch, onReset } = useTableData({
		getTablePageData,
		exportTableData,
	});

	const scoreType = Form.useWatch('scoreType', form);

	useEffect(() => {
		/* if (!searchParams.get('tempDate')) {
			const startDate = dayjs().startOf('month').format('YYYY-MM-DD');
			const endDate = dayjs().endOf('month').format('YYYY-MM-DD');
			form.setFieldsValue({
				tempDate: [startDate, endDate],
				startTime: `${startDate} 00:00:00`,
				endTime: `${endDate} 23:56:56`,
			});
		} */
	}, []);
	return (
		<div>
			<div className="flex flex-shrink align-center justify-between margin-bottom-4 height-48">
				<div className="font-size-22 font-weight-500 color-1d2129">分享统计</div>

				<Button type="primary" onClick={exportData}>
					导出文件
				</Button>
			</div>
			{/* Tabs & 功能按钮 开始 */}

			<div className="flex-sub margin-top-18 padding-24 border-radius-8 bg-color-ffffff">
				{/* 筛选条件 开始 */}
				<div className="flex justify-between margin-bottom-18">
					<div className="flex-sub">
						<Form
							form={form}
							labelAlign="left"
							layout="inline"
							initialValues={{
								companyNames: [],
								sortStr: 'desc',
								startTime: undefined,
								endTime: undefined,
								tempDate: ['', ''],
							}}
						>
							<Form.Item hidden name="startTime">
								<Input placeholder="" />
							</Form.Item>
							<Form.Item hidden name="endTime">
								<Input placeholder="" />
							</Form.Item>
							<Row className="width-100per" gutter={[12, 12]}>
								<Col span={8}>
									<Form.Item name="isWorkStatus" label="就职状态">
										<Select options={workStatusOptions} allowClear placeholder="请选择就职状态" />
									</Form.Item>
								</Col>
								<Col span={8}>
									<Form.Item name="companyNames" label="所属单位">
										<Select mode="multiple" options={companyOptions} allowClear placeholder="请选择所属单位" />
									</Form.Item>
								</Col>
								<Col span={8}>
									<Form.Item name="scoreType" label="排序类型">
										<Select options={sortTypeOption} allowClear placeholder="请选择排序类型" />
									</Form.Item>
								</Col>
								{scoreType ? (
									<Col span={8}>
										<Form.Item name="sortStr" label="排序方式">
											<Select
												options={[
													{ label: '降序', value: 'desc' },
													{ label: '升序', value: 'asc' },
												]}
												placeholder="请选择排序方式"
											/>
										</Form.Item>
									</Col>
								) : (
									<Form.Item name="sortStr" hidden>
										<Input />
									</Form.Item>
								)}
								<Col span={121}>
									<Form.Item name="tempDate" label="统计时间">
										<FormComp.DatePicker.RangePicker
											placeholder={['开始时间', '结束时间']}
											onChange={(date) => {
												form.setFieldsValue({
													startTime: date[0] ? `${date[0]} 00:00:00` : null,
													endTime: date[1] ? `${date[1]} 23:56:56` : null,
												});
											}}
										/>
									</Form.Item>
								</Col>
							</Row>
						</Form>
					</div>
					<div className="margin-left-12 padding-left-12 border-solid-left-e5e6eb">
						<Space size={16}>
							<Button onClick={onReset}>重置</Button>
							<Button type="primary" onClick={onSearch}>
								查询
							</Button>
						</Space>
					</div>
				</div>
				{/* 筛选条件 结束 */}

				{/* 表格 开始 */}
				<Table rowKey="name" dataSource={dataSource} pagination={pagination} onChange={changePage} scroll={{ x: 'max-content' }}>
					<Table.Column
						title="序号"
						key="index"
						fixed="left"
						width={60}
						render={(_, __, index) => {
							return index + 1;
						}}
					/>
					<Table.Column title="姓名" dataIndex="name" fixed="left" render={(text) => text || '--'} />
					<Table.Column title="所属单位" dataIndex="companyName" render={(text) => <div className="max-width-220">{text || '--'}</div>} />
					<Table.Column title="是否在职" dataIndex="isWorkStatus" align="center" render={(text) => (text === 1 ? '是' : '否')} />
					{sortTypeOption
						.filter((ov) => ov.dataIndex)
						.map((ov) => {
							return (
								<Table.Column
									key={ov.dataIndex}
									title={<div className={scoreType === ov.value ? 'font-bold color-165dff' : ''}>{ov.label}</div>}
									dataIndex={ov.dataIndex}
									align="center"
									render={(text) => <div className={scoreType === ov.value ? 'font-bold color-165dff' : ''}>{text || '--'}</div>}
								/>
							);
						})}
					<Table.Column title="分享总数" dataIndex="shareTotal" align="center" fixed="right" render={(text) => text || '--'} />
				</Table>
				{/* 表格 结束 */}
			</div>
		</div>
	);
};

export default Index;
