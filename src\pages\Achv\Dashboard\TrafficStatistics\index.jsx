import { useEffect, useState } from 'react';
import { DatePicker, Select, Space, Card, Statistic, Row, Col, Table, Button, Modal } from 'antd';
import { RedoOutlined } from '@ant-design/icons';
import QrCode from '@/components/QrCode';

import { useTableData } from '@/hook/useTableData';
import { useRouterLink } from '@/hook/useRouter';

import { dataPointStatistics, dataPointPage } from '@/api/Achv/Dashboard/TtrafficStatistics';
import { getMiniProgramCode } from '@/api/common';

import dayjs from 'dayjs';

const businessOptions = [
	{ label: '活动', value: 'eventDetail' },
	{ label: '拼团', value: 'groupDetail' },
	{ label: '图文', value: 'infoDetail' },
	{ label: '视频', value: 'videoDetail' },
	{ label: '主题活动', value: 'activityDetail' },
	{ label: '自定义渠道', value: 'channel' },
	{ label: '小程序页面', value: 'pages' },
];

const Index = () => {
	const { openNewTab } = useRouterLink();
	const [type, setType] = useState('day');

	const [specifiedDate, setSpecifiedDate] = useState(dayjs(new Date()).format('YYYY-MM-DD'));
	const [startTime, setStartTime] = useState('');
	const [endTime, setEndTime] = useState('');
	// 访问类型 eventDetail 会务活动详情  infoDetail 资讯详情 groupDetail 拼团详情 activityDetail 活动详情
	const [businessCode, setBusinessCode] = useState('eventDetail');
	const [sortProject, setSortProject] = useState('totalVisitCount');

	// 生成二维码数据
	const [infoData, setInfoData] = useState({});

	const { dataSource, pagination, changePage, onSearch } = useTableData({
		params: {
			startTime: type === 'day' ? undefined : startTime,
			endTime: type === 'day' ? undefined : endTime,
			specifiedDate: type === 'day' ? specifiedDate : undefined,
			businessCode,
			sortProject,
			sortType: 'desc',
		},
		getTablePageData: dataPointPage,
		closeFilter: true,
		disabledReplace: true,
	});

	// 统计数据
	const [statisticsData, setStatisticsData] = useState([]);
	const getStatisticsData = () => {
		dataPointStatistics({
			startTime: type === 'day' ? undefined : startTime,
			endTime: type === 'day' ? undefined : endTime,
			specifiedDate: type === 'day' ? specifiedDate : undefined,
		}).then((res) => {
			const list = res?.data?.dataPointStatisticsList || [];
			list.forEach((ov) => {
				const index = businessOptions.findIndex((oov) => oov.value === ov.businessCode);
				if (index > -1) {
					ov.businessName = businessOptions[index].label;
					ov.sort = index;
				}
			});
			setStatisticsData(
				businessOptions.map((ov) => {
					const {
						currentCount,
						sevenDaysCount = 0,
						yesterdayCount = 0,
						totalCount = 0,
					} = list.find((oov) => oov.businessCode === ov.value) || {};
					return {
						businessName: ov.label,
						businessCode: ov.value,
						currentCount,
						sevenDaysCount,
						yesterdayCount,
						totalCount,
					};
				})
			);
		});
	};

	// 跳转详情
	const linkToDetail = (record) => {
		const { businessCode, businessId, businessTitle } = record;

		if (businessId) {
			switch (businessCode) {
				case 'groupDetail':
					openNewTab(`/newAchv/eventManage/groupManage/list?id=${businessId}`);
					break;
				case 'eventDetail':
					openNewTab(`${import.meta.env.VITE_EVENT_URL}/eventManage/event?id=${businessId}`);
					break;
				case 'infoDetail':
					openNewTab(`/newAchv/informationManage/informationRelease/list?keywords=${businessTitle}`);
					break;
				case 'activityDetail':
					openNewTab(`/newAchv/activity/list?keywords=${businessTitle}`);
					break;
				case 'channel':
					openNewTab(`/newAchv/dashboard/channel/list?channelName=${businessTitle}`);
					break;
			}
		}
	};

	// 统计请求
	useEffect(() => {
		if (startTime && endTime && specifiedDate) {
			getStatisticsData();
		}
	}, [startTime, endTime, specifiedDate, type]);

	return (
		<div>
			<Space size={16} direction="vertical" className="width-100per">
				<div className="flex justify-between align-center margin-bottom-4 height-48 font-size-22 font-weight-500 color-1d2129">
					<div>流量统计</div>
					<Space>
						<DateSelect
							type={type}
							setType={setType}
							onChange={(e, date) => {
								setStartTime(dayjs(e[0]).format('YYYY-MM-DD'));
								setEndTime(dayjs(e[1]).format('YYYY-MM-DD'));
								setSpecifiedDate(dayjs(date).format('YYYY-MM-DD'));
							}}
						/>
						<Button
							type="primary"
							icon={<RedoOutlined />}
							onClick={() => {
								getStatisticsData();
								onSearch();
							}}
						>
							刷新
						</Button>
					</Space>
				</div>
				<Card title="小程序流量统计">
					<Row gutter={[64, 32]}>
						{statisticsData.map((ov, index) => {
							return (
								<Col key={index} xs={6} sm={6} md={6} lg={6} className={index === 0 ? '' : 'border-left-f2f3f5'}>
									<StatisticItem data={ov} type={type} onClick={() => setBusinessCode(ov.businessCode)} />
								</Col>
							);
						})}
					</Row>
				</Card>
				<Card
					title="统计明细"
					extra={
						<Space>
							<Select
								value={sortProject}
								placeholder="请选择排序"
								className="width-140"
								options={[
									{ label: '按访问热度排序', value: 'heatValue' },
									{ label: '按访问人数降序', value: 'totalVisitCount' },
									{ label: '按访问流量降序', value: 'total' },
								].filter((ov) => {
									return ov.value !== 'heatValue' || ['infoDetail', 'videoDetail'].includes(businessCode);
								})}
								onChange={setSortProject}
							/>
							<Select
								value={businessCode}
								className="width-140"
								options={businessOptions}
								onChange={(e) => {
									if (['infoDetail', 'videoDetail'].includes(e)) {
										setSortProject('heatValue');
									} else {
										setSortProject('totalVisitCount');
									}
									setBusinessCode(e);
								}}
							/>
						</Space>
					}
				>
					<Table rowKey="businessId" dataSource={dataSource} pagination={pagination} onChange={changePage} scroll={{ x: 'max-content' }}>
						<Table.Column
							title="序号"
							dataIndex="index"
							width={110}
							render={(text, record, index) => {
								return index + 1;
							}}
						/>
						<Table.Column
							title="统计目标"
							dataIndex="businessTitle"
							render={(text) => {
								return (
									<div style={{ maxWidth: '600px' }} title={text}>
										{text || '-'}
									</div>
								);
							}}
						/>
						{['infoDetail', 'videoDetail'].includes(businessCode) && (
							<Table.Column
								title="热度值"
								dataIndex="heatValue"
								align="center"
								render={(text) => {
									return text || '-';
								}}
							/>
						)}
						<Table.Column
							title="访问人数"
							dataIndex="totalVisitCount"
							align="center"
							render={(text) => {
								return text || '-';
							}}
						/>
						<Table.Column
							title="流量统计"
							dataIndex="currentCount"
							align="center"
							render={(text) => {
								return text || '-';
							}}
						/>
						<Table.Column
							title="操作"
							dataIndex="option"
							align="center"
							fixed="right"
							render={(_, record) => {
								return (
									<Space>
										<Button
											type="link"
											size="small"
											onClick={() =>
												setInfoData({
													...record,
													index: infoData?.index - 0 + 1,
												})
											}
										>
											扫码查看
										</Button>
									</Space>
								);
							}}
						/>
					</Table>
				</Card>
			</Space>
			<ShareCode infoData={infoData} />
		</div>
	);
};

// 日期选择
const DateSelect = (props = {}) => {
	const [specifiedDate, setSpecifiedDate] = useState('');
	const [startTime, setStartTime] = useState('');
	const [endTime, setEndTime] = useState('');

	useEffect(() => {
		setSpecifiedDate(dayjs(new Date()));
		// 当月1号
		setStartTime(dayjs(new Date(new Date().getFullYear(), new Date().getMonth(), 1)));
		// 当月最后一天
		setEndTime(dayjs(new Date(new Date().getFullYear(), new Date().getMonth() + 1, 0)));
	}, []);

	useEffect(() => {
		if (startTime && endTime && specifiedDate) {
			props.onChange && props.onChange([startTime, endTime], specifiedDate);
		}
	}, [startTime, endTime, specifiedDate]);
	return (
		<Space>
			<Select
				className="width-160"
				value={props.type}
				popupMatchSelectWidth
				options={[
					{ label: '按日期', value: 'day' },
					{ label: '按时间范围', value: 'range' },
				]}
				onChange={(e) => {
					props.setType(e);
				}}
			/>

			{props.type === 'range' ? (
				<DatePicker.RangePicker
					value={[startTime, endTime]}
					format="YYYY-MM-DD"
					onChange={(date) => {
						setStartTime(date[0]);
						setEndTime(date[1]);
					}}
				/>
			) : (
				<DatePicker
					value={specifiedDate}
					onChange={(date) => {
						setSpecifiedDate(date);
					}}
				/>
			)}
		</Space>
	);
};

// 统计
const StatisticItem = (props = {}) => {
	const { type, data } = props;
	return (
		<div className="a" onClick={props.onClick}>
			<Statistic
				title={data.businessName}
				value={data.currentCount}
				valueStyle={{ fontWeight: 'bold', fontSize: '32px' }}
				suffix={
					<Space>
						<div className="font-size-14 color-86909c">次</div>
					</Space>
				}
				onClick={() => {
					props.changeBusinessCode(data.businessCode);
				}}
			/>
			{type === 'day' && (
				<div className="flex align-center justify-between color-86909c">
					<div className="">
						较昨日 <StatisticNum value={data.yesterdayCount} />
					</div>
					<div className="">
						较7日前 <StatisticNum value={data.sevenDaysCount} />
					</div>
					<div className="">
						总数 <span className="">{data.totalCount || '--'}</span>
					</div>
				</div>
			)}
		</div>
	);
};

// 统计数字
const StatisticNum = (props = {}) => {
	return (
		<span className={props.value ? (props.value > 0 ? 'color-f53f3f' : 'color-00b42a') : ''}>
			{props.value > 0 && '+'}
			{props.value || '--'}
		</span>
	);
};

// 二维码弹窗
const ShareCode = (props = {}) => {
	const [shareUrlObje, setShareUrlObje] = useState({});
	const [showShare, setShowShare] = useState('');

	const onShare = (infoData) => {
		const { businessId, businessCode } = infoData;
		if (shareUrlObje[businessId]) {
			setShowShare(shareUrlObje[businessId]);
			return;
		}

		const pathName =
			{
				eventDetail: `event/ActivityDetail/index`,
				infoDetail: `pages/PolicyInterpretationDetail/index`,
				groupDetail: `groupEvent/Detail/index`,
				activityDetail: `activity/GridDetail/index`,
				channel: `pages/Home/index`,
				pages: businessId,
			}[businessCode] || `pages/Home/index`;

		const params =
			{
				eventDetail: `id=${businessId}`,
				infoDetail: `id=${businessId}`,
				groupDetail: `id=${businessId}`,
				activityDetail: `id=${businessId}`,
				channel: `pointId=${businessId}`,
			}[businessCode] || 'temp=12';

		getMiniProgramCode({
			appId: 'dwq1690976562914136064',
			pathName: pathName,
			params: params,
			envVersion: import.meta.env.PROD ? 'release' : 'trial',
		}).then((res) => {
			const miniUrl = `data:image/png;base64,${res.data}`;
			shareUrlObje[businessId] = miniUrl;
			setShareUrlObje({
				...shareUrlObje,
			});
			setShowShare(miniUrl);
		});
	};

	useEffect(() => {
		if (props.infoData.businessId) {
			onShare(props.infoData);
		}
	}, [props.infoData]);

	return (
		<Modal
			title="二维码"
			open={showShare != ''}
			onCancel={() => {
				setShowShare('');
			}}
			centered
			footer={null}
			width={300}
			className="qr-code-modal"
		>
			<QrCode.QRCodebase64
				qrCodeList={[
					{
						qrcodeUrl: showShare,
						qrcodeName: '二维码',
					},
				]}
			/>
		</Modal>
	);
};

export default Index;
