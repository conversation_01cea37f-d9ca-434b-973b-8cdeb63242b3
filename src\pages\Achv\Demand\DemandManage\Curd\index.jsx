import { useEffect, useState } from 'react';
import { useRouterLink } from '@/hook/useRouter';

import { Button, Space, Form, Input, Cascader, Radio, Checkbox, InputNumber, message, Affix, Select } from 'antd';
import { DeleteOutlined } from '@ant-design/icons';

import FormComp from '@/components/FormComp';
import UEditor from '@/components/UEditor';
import UploadFile from '@/components/UploadFile';
import SelectTTChannels from '@/components/Achv/SelectTTChannels';

import { getDemand, addDemand, updateDemand } from '@/api/Achv/Demand/DemandManage/index';
import { getThreeLevelData } from '@/api/common';

import { getCategoryValueList } from '@/utils/achv';
import { handleEditor } from '@/utils/common';

import { releaseTypeData } from '@/pages/Achv/config';

import './index.scss';

// 基本信息
const BaseInfoForm = (props = {}) => {
	const [demandTypeList, setDemandTypeList] = useState([]);
	const [areaCategoryList, setAreaCategoryList] = useState([]);
	const [transformList, setTransformList] = useState([]);
	const [expirationTypeOptions, setExpirationTypeOptions] = useState([]);

	useEffect(() => {
		getCategoryValueList('demand_type').then((res) => {
			setDemandTypeList(res);
		});
		getCategoryValueList('ttchannels_area').then((res) => {
			setAreaCategoryList(res);
		});
		getCategoryValueList('achv_transform').then((res) => {
			setTransformList(res);
		});

		setExpirationTypeOptions(
			['', '半年内解决', '一年内解决', '不限']
				.map((ov, oi) => {
					return {
						label: ov,
						value: oi,
					};
				})
				.filter((ov) => ov.value)
		);
	}, []);
	return (
		<>
			<Form.Item label="需求名称" name="name" rules={[{ required: true, message: '请输入需求名称' }]}>
				<Input className="input-box" placeholder="请输入需求名称" />
			</Form.Item>
			<Form.Item label="开启认证" name="isAuth" required>
				<FormComp.Switch />
			</Form.Item>
			<Form.Item label="需求状态" name="proclamationStatus" required>
				<Radio.Group
					options={[
						{ label: '可揭榜', value: 1 },
						{ label: '揭榜中', value: 2 },
						{ label: '已揭榜', value: 3 },
					]}
				/>
			</Form.Item>
			<Form.Item label="需求类型" name="demandTypeId" rules={[{ required: true, message: '请选择需求类型' }]}>
				<Radio.Group options={demandTypeList} />
			</Form.Item>
			<Form.Item
				label="技术领域"
				name="areaCategoryList"
				rules={[
					{
						required: true,
						type: 'array',
						message: '请选择技术领域',
					},
				]}
			>
				<Checkbox.Group options={areaCategoryList} />
			</Form.Item>
			<Form.Item
				label="合作方式"
				name="transformList"
				rules={[
					{
						required: true,
						type: 'array',
						message: '请选择合作方式',
					},
				]}
			>
				<Checkbox.Group options={transformList} />
			</Form.Item>
			<Form.Item hidden name="provinceCode">
				<Input />
			</Form.Item>
			<Form.Item hidden name="cityCode">
				<Input />
			</Form.Item>
			<Form.Item label="项目预算" name="budgetDesc" required>
				<BudgetDesc />
			</Form.Item>
			<Form.Item label="是否公开需求" name="isOpen" required>
				<Radio.Group
					options={[
						{ label: '公开', value: 1 },
						{ label: '不公开', value: 0 },
					]}
				/>
			</Form.Item>
			<Form.Item label="需求时限" name="expirationType">
				<Select className="select-box" options={expirationTypeOptions} placeholder="请选择需求时限" />
			</Form.Item>
			<Form.Item label="发布类型" name="releaseType" required>
				<Radio.Group options={releaseTypeData} disabled={props.disabledReleaseType} />
			</Form.Item>
			<Form.Item label="AI检索标签" name="tagDesc">
				<Input className="input-box" placeholder="请输入AI检索标签，中文逗号分割 例：技术、项目" />
			</Form.Item>
		</>
	);
};

// 需求方信息
const CompanyForm = (props = {}) => {
	const [areaOptions, setAreaOptions] = useState([]);

	useEffect(() => {
		getThreeLevelData({ level: 2 }).then((res) => {
			setAreaOptions(res.data || []);
		});
	}, []);
	return (
		<>
			<Form.Item label="需求方" name="companyName">
				<Input className="input-box" placeholder="请输入需求方" />
			</Form.Item>
			<Form.Item label="是否公开需求方" name="publicType">
				<Radio.Group
					options={[
						// { label: "全公开", value: 2 },
						{ label: '半公开', value: 1 },
						{ label: '隐藏', value: 0 },
					]}
				/>
			</Form.Item>
			<Form.Item
				label="所属区域"
				name="tempArea"
				rules={[
					{
						required: true,
						type: 'array',
						message: '请选择所属区域',
					},
				]}
			>
				<Cascader
					className="cascader-box"
					options={areaOptions}
					placeholder="请选择区域"
					displayRender={(label) => label.join('-')}
					onChange={(e = [undefined, undefined]) => {
						props.form.setFieldValue('provinceCode', e[0]);
						props.form.setFieldValue('cityCode', e[1]);
					}}
				/>
			</Form.Item>
			<Form.Item label="联系人" name="contacts">
				<Input className="input-box" placeholder="请输入联系人" />
			</Form.Item>
			<Form.Item label="联系人职位" name="contactsPosition">
				<Input className="input-box" placeholder="请输入联系人职位" />
			</Form.Item>
			<Form.Item label="联系人电话" name="contactsPhone">
				<Input className="input-box" placeholder="请输入联系人电话" />
			</Form.Item>
		</>
	);
};

// 项目预算
const BudgetDesc = (props = {}) => {
	const [value, setValue] = useState(null);

	useEffect(() => {
		setValue(props.value === '面议' ? null : props.value);
	}, [props.value]);
	return (
		<Space size={8}>
			<InputNumber
				value={value}
				className="input-number-box"
				placeholder="请输入"
				min={0}
				precision={2}
				controls={false}
				onChange={(e) => {
					props.onChange(e === null ? '面议' : e);
				}}
			/>
			<div className="margin-right-16">万元</div>
			<Checkbox
				checked={props.value === '面议'}
				onClick={() => {
					if (props.value === '面议') {
						message.warning('请输入项目预算');
					} else {
						props.onChange('面议');
					}
				}}
			>
				面议
			</Checkbox>
		</Space>
	);
};

// 上传附件
const FileUpload = (props = {}) => {
	return (
		<Space direction="vertical">
			{props.value && (
				<Space className="line-height-32 font-size-16">
					<div>{(props.value || '').split('/').pop()}</div>
					<DeleteOutlined onClick={() => props.onChange('')} className="color-165dff" />
				</Space>
			)}
			<UploadFile accept=".pdf" onChange={props.onChange} customName>
				<Button type="primary">上传</Button>
			</UploadFile>
		</Space>
	);
};

const Index = (props = {}) => {
	const { linkTo, searchParams } = useRouterLink();
	// 禁止编辑发布类型
	const [disabledReleaseType, setDisabledReleaseType] = useState(false);
	const [hash, setHash] = useState('');
	const [form] = Form.useForm();
	const releaseType = Form.useWatch('releaseType', form);

	// 是否可以编辑基本资料
	// 赛事需求详情入口 只能是赛事需求才能编辑
	const [isEditBase, setIsEditBase] = useState(true);

	// 取消
	const onCancel = () => {
		linkTo(-1);
	};

	// 提交
	const onSubmit = () => {
		form.validateFields()
			.then((values) => {
				handleEditor(values.requirement).then((requirement) => {
					delete values.tempArea;

					(values.id ? updateDemand : addDemand)({
						requirement,
						...values,
						ttChannelsIds: values.ttChannelsIds ? values.ttChannelsIds.map((ov) => ov.id) : undefined,
						excavatePersonnelTtChannelIds: values.excavatePersonnelTtChannelIds
							? values.excavatePersonnelTtChannelIds.map((ov) => ov.id)
							: undefined,
						authPersonnelTtChannelIds: values.authPersonnelTtChannelIds ? values.authPersonnelTtChannelIds.map((ov) => ov.id) : undefined,
						responsiblePersonnelTtChannelIds: values.responsiblePersonnelTtChannelIds
							? values.responsiblePersonnelTtChannelIds.map((ov) => ov.id)
							: undefined,
					}).then(() => {
						message.success(id ? '修改成功' : '添加成功');
						setTimeout(() => {
							linkTo(-1);
						}, 500);
					});
				});
			})
			.catch((error) => {
				console.log(error);
				const errorName = error.errorFields[0].name[0];
				if (['ttChannelsIds'].includes(errorName)) {
					setHash('section4');
				} else {
					setHash('section1');
				}
			});
	};

	// 获取详情
	const getDetail = () => {
		const id = searchParams.get('id');
		if (id) {
			getDemand({ id, isUpdate: 1 }).then((res) => {
				const resData = res.data || {};

				// 处理区域
				resData.tempArea = [resData.provinceCode, resData.cityCode];

				// 处理 关联科转号
				resData.ttChannelsIds = resData.ttChannelsList || [];
				resData.excavatePersonnelTtChannelIds = resData.excavatePersonnelTtChannelsList || [];
				resData.authPersonnelTtChannelIds = resData.authPersonnelTtChannelsList;
				resData.responsiblePersonnelTtChannelIds = resData.responsiblePersonnelTtChannelsList || [];

				form.setFieldsValue(resData);

				// 赛事不能编辑 非赛事的需求
				setIsEditBase(props.releaseType !== 3 || resData.releaseType === 3);

				// 编辑 与固定类型 一致时不可以编辑
				setDisabledReleaseType(resData.releaseType === props.releaseType);
			});
		} else if (props.releaseType) {
			// 新建时 如果是固定类型入口就默认类型
			form.setFieldValue('releaseType', props.releaseType);
			// 新建时 固定类型不可编辑
			setDisabledReleaseType(true);
		}
	};

	useEffect(() => {
		getDetail();

		const hash = window.location.hash.replace('#', '');
		setHash(hash || 'section1');
	}, []);

	// 跳转到指定锚点
	useEffect(() => {
		if (hash) {
			setTimeout(() => {
				const element = document.getElementById(hash);
				if (element) {
					element.scrollIntoView({ behavior: 'smooth' });
				}
			}, 500);
		}
	}, [hash]);

	return (
		<div className="">
			<div className="flex flex-shrink align-center margin-bottom-4 height-48 font-size-22 font-weight-500 color-1d2129">
				<Space size={10}>
					<div className="a color-86909c" onClick={() => linkTo(props.linkToPath || `/newAchv/demand/demandManage`)}>
						需求管理
					</div>
					<div className="color-86909c">/</div>
					<div>需求编辑</div>
				</Space>
			</div>

			{/* Tabs & 功能按钮 开始 */}
			<div id="section1"></div>
			<Affix offsetTop={0}>
				<div className="flex align-center justify-between padding-24 border-radius-8 border-solid-bottom-f2f2f2 bg-color-ffffff">
					<div className="flex align-center">
						{['需求信息', '需求方信息', '需求描述', '相关参与人']
							.map((ov, oi) => {
								const currentHash = `section${oi + 1}`;
								return {
									el: (
										<div
											key={oi}
											className={`a margin-right-40 font-size-18 font-weight-500 ${
												hash === currentHash ? 'color-165dff' : ''
											} ${oi !== 3 && !isEditBase ? 'cursor-not-allowed color-86909c' : ''}`}
											onClick={() => {
												if (oi !== 3 && !isEditBase) {
													return;
												}
												if (currentHash === 'section4') {
													form.validateFields()
														.then(() => {
															setHash(`section${oi + 1}`);
														})
														.catch((error) => {
															const errorName = error.errorFields[0].name[0];
															if (['ttChannelsIds'].includes(errorName)) {
																setHash('section4');
															} else {
																setHash('section1');
															}
														});
												} else {
													setHash(`section${oi + 1}`);
												}
											}}
											title={!isEditBase ? '当前需求来源不是“参赛提交”请到需求管理进行编辑' : ''}
										>
											{ov}
										</div>
									),
									label: ov,
								};
							})
							.filter(({ label }) => label)
							.map((ov) => ov.el)}
					</div>
					<Space size={16}>
						<Button onClick={onCancel}>取消</Button>
						<Button type="primary" onClick={onSubmit}>
							保存
						</Button>
					</Space>
				</div>
			</Affix>
			{/* Tabs & 功能按钮 结束 */}

			<Form
				form={form}
				className="antd-form-box"
				labelAlign="right"
				labelCol={{ style: { width: '116px' } }}
				initialValues={{
					id: '',
					isAuth: 0,
					proclamationStatus: 1,
					areaCategoryList: [],
					transformList: [],
					publicType: 1,
					budgetDesc: '面议',
					isOpen: 1,
					releaseType: 1,
					releasePlatform: 1,
					tempArea: [],
					ttChannelsIds: [],
					delTTChannelsIds: [],
					excavatePersonnelTtChannelIds: [],
					authPersonnelTtChannelIds: [],
					responsiblePersonnelTtChannelIds: [],
					brokerList: [],
				}}
			>
				<Form.Item hidden name="id">
					<Input />
				</Form.Item>
				<Form.Item hidden name="delTTChannelsIds">
					<Input />
				</Form.Item>
				<Form.Item hidden name="rankingNum">
					<Input />
				</Form.Item>

				{/* 兼容赛事数据 开始 */}
				<Form.Item hidden name="contacts">
					<Input />
				</Form.Item>
				<Form.Item hidden name="contactsPosition">
					<Input />
				</Form.Item>
				<Form.Item hidden name="contactsPhone">
					<Input />
				</Form.Item>
				<Form.Item hidden name="releasePlatform">
					<Input />
				</Form.Item>
				{/* 兼容赛事数据 结束 */}

				<div style={{ display: hash !== 'section4' ? 'block' : 'none' }}>
					{/* 基本信息 开始 */}
					<div className="margin-top-20 padding-20 border-radius-8 bg-color-ffffff">
						<div className="padding-bottom-30 line-height-26 font-size-18 font-weight-500 color-1d2129">基本信息</div>
						<BaseInfoForm form={form} disabledReleaseType={disabledReleaseType} />
					</div>
					{/* 基本信息 结束 */}

					{/* 需求方信息 开始 */}
					<div id="section2"></div>
					<div className="margin-top-20 padding-20 border-radius-8 bg-color-ffffff">
						<div className="padding-bottom-30 line-height-26 font-size-18 font-weight-500 color-1d2129">需求方信息</div>
						<CompanyForm form={form} />
					</div>
					{/* 需求方信息 结束 */}

					{/* 需求描述 开始 */}
					<div id="section3"></div>
					<div className="margin-top-20 padding-20 border-radius-8 bg-color-ffffff">
						<div className="padding-bottom-30 line-height-26 font-size-18 font-weight-500 color-1d2129">需求描述</div>
						<Form.Item name="requirement" label="需求描述" rules={[{ required: true, message: '请输入需求描述' }]}>
							<UEditor placeholder="请输入需求描述" height={300} />
						</Form.Item>

						<Form.Item name="attachment" label="需求附件">
							<FileUpload />
						</Form.Item>
					</div>
					{/* 需求描述 结束 */}
				</div>

				{/* 相关参与人 开始 */}
				<div style={{ display: hash === 'section4' ? 'block' : 'none' }}>
					{releaseType === 2 && (
						<SelectTTChannels
							form={form}
							name="ttChannelsIds"
							label="关联科转号"
							rules={[{ required: true, type: 'array', message: '请选择关联科转号' }]}
							collectDelIds={true}
						/>
					)}

					<SelectTTChannels form={form} name="excavatePersonnelTtChannelIds" label="需求挖掘人" />
					<SelectTTChannels form={form} name="authPersonnelTtChannelIds" label="需求认证人" />
					<SelectTTChannels form={form} name="responsiblePersonnelTtChannelIds" label="需求负责人" />
					<SelectTTChannels form={form} name="brokerTtChannelsList" label="需求跟进人" hideDel hideAdd />
				</div>
				{/* 相关参与人 结束 */}
			</Form>
		</div>
	);
};

export default Index;
