import { useEffect, useState } from 'react';
import { useRouterLink } from '@/hook/useRouter';

import { Button, Form, Input, Radio, Space, message, Modal } from 'antd';
import { DeleteOutlined } from '@ant-design/icons';

import Docking from '@/components/Achv/DockingManage/index';

import { getDemand } from '@/api/Achv/Demand/DemandManage/index';
import { getSupplier, addSupplier, updateSupplier, delSupplier } from '@/api/Achv/Demand/DockingManage/index';

const Index = (props = {}) => {
	const { linkTo, searchParams } = useRouterLink();
	const id = searchParams.get('id') || '';
	const sourceId = searchParams.get('sourceId') || '';
	const [demandName, setDemandName] = useState('');
	const [form] = Form.useForm();

	// 跳转地址
	const linkToPath = props.linkToPath || `/newAchv/demand/demandManage`;

	// 删除供给方
	const onDel = () => {
		Modal.confirm({
			title: '确定删除该供给方？',
			onOk: () => {
				delSupplier({ id }).then((res) => {
					message.success('删除成功');

					setTimeout(() => {
						linkTo(-1);
					}, 500);
				});
			},
		});
	};

	// 提交
	const onSubmit = () => {
		form.validateFields().then((values) => {
			const { brokerId } = values;

			// 经理人特殊处理
			if (brokerId) {
				const [id] = brokerId.split('|');
				values.brokerId = id;
			}

			(id ? updateSupplier : addSupplier)({
				...values,
				createSource: id ? undefined : props.releaseType || 1,
			}).then(() => {
				message.success('保存成功');

				setTimeout(() => {
					linkTo(-1);
				}, 500);
			});
		});
	};

	// 获取供给方详情
	const getDetail = () => {
		getSupplier({ id }).then((res) => {
			form.setFieldsValue(res.data || {});
		});
	};

	// 获取需求详情
	const getSourceDetail = (id) => {
		getDemand({ id }).then((res) => {
			setDemandName(res?.data?.name || '');
		});
	};

	useEffect(() => {
		if (sourceId === '') {
			message.error('参数错误');
			setTimeout(() => {
				linkTo(-1);
			}, 500);
			return;
		}
		form.setFieldValue('sourceId', sourceId);
		getSourceDetail(sourceId);
		if (id) {
			getDetail();
		}
	}, []);

	return (
		<div className="">
			<div className="flex flex-shrink align-center margin-bottom-4 height-48 font-size-22 font-weight-500 color-1d2129">
				<Space size={10}>
					<div className="a color-86909c" onClick={() => linkTo(linkToPath)}>
						需求管理
					</div>
					<div className="color-86909c">/</div>
					<div className="a color-86909c" onClick={() => linkTo(`${linkToPath}/detail?id=${sourceId}`)}>
						需求详情
					</div>
					<div className="color-86909c">/</div>
					<div>{id ? '供给方编辑' : '新增供给方'}</div>
				</Space>
			</div>

			{/* 供给方详情 开始 */}
			<div className="margin-top-20 padding-20 border-radius-8 bg-color-ffffff">
				<div className="flex align-center justify-between">
					<div className="line-height-26 font-size-18 font-weight-500 color-1d2129">供给方</div>
					{id && (
						<div className="a flex align-center color-f53f3f" onClick={onDel}>
							<DeleteOutlined />
							<div className="margin-left-8 line-height-24 font-size-16 font-weight-500">删除</div>
						</div>
					)}
				</div>
				{/* 表单 开始 */}
				<Form
					className="antd-form-box margin-top-20"
					form={form}
					labelCol={{ span: 5 }}
					wrapperCol={{ span: 16 }}
					initialValues={{
						sourceType: 1,
						supplierType: 1,
						stageStatus: 1,
					}}
				>
					<Form.Item name="id" hidden>
						<Input />
					</Form.Item>
					<Form.Item name="sourceType" hidden>
						<Input />
					</Form.Item>
					<Form.Item name="sourceId" hidden>
						<Input />
					</Form.Item>
					<Form.Item label="需求名称" required>
						<div>{demandName}</div>
					</Form.Item>
					<Form.Item name="supplierName" label="供给方名称" rules={[{ required: true, message: '请输入供给方名称' }]}>
						<Input placeholder="请输入供给方名称" />
					</Form.Item>
					<Form.Item name="supplierType" label="供给方类型" required>
						<Radio.Group
							options={[
								{ label: '专家团队', value: 1 },
								{ label: '科研机构', value: 2 },
								{ label: '企业', value: 3 },
							]}
						/>
					</Form.Item>
					<Form.Item name="supplierContacts" label="联系人" rules={[{ required: true, message: '请输入联系人' }]}>
						<Input placeholder="请输入联系人" />
					</Form.Item>
					<Form.Item name="supplierContactsPosition" label="联系人职位">
						<Input placeholder="请输入联系人职位" />
					</Form.Item>
					<Form.Item name="supplierContactsPhone" label="联系方式" rules={[{ required: true, message: '请输入联系方式' }]}>
						<Input placeholder="请输入联系方式" />
					</Form.Item>
					<Form.Item name="supplierIntroduction" label="供给方介绍" rules={[{ required: true, message: '请输入供给方介绍' }]}>
						<Input.TextArea maxLength={500} rows={4} placeholder="请输入供给方介绍" />
					</Form.Item>
					<Docking.Curd form={form} releaseType={props.releaseType} />
					<Form.Item colon={false} label=" ">
						<div className="flex align-center justify-between">
							<div className="flex align-center"></div>
							<Space size={16}>
								<Button onClick={() => linkTo(-1)}>取消</Button>
								<Button type="primary" onClick={onSubmit}>
									保存
								</Button>
							</Space>
						</div>
					</Form.Item>
				</Form>
				{/* 表单 结束 */}
			</div>
			{/* 供给方详情 结束 */}
		</div>
	);
};
export default Index;
