import Docking from '@/components/Achv/DockingManage/index';

import { competitionGroupTextList } from '@/pages/Achv/config';

const Index = (props = {}) => {
	// 跳转地址
	const linkToPath = props.linkToPath || `/newAchv/demand/demandManage`;
	return <Docking.Detail type={1} BaseData={BaseData} linkToPath={linkToPath} releaseType={props.releaseType} />;
};

// 基础数据
export const BaseData = (props = {}) => {
	const detailData = props.detailData;

	return (
		<>
			<div className="flex margin-top-20">
				<div className="margin-right-4 line-height-22 color-86909c">供给方：</div>
				<div className="flex-sub line-height-22 color-1d2129">{detailData.supplierName}</div>
			</div>
			<div className="flex margin-top-20">
				<div className="margin-right-4 line-height-22 color-86909c">供给方类型：</div>
				<div className="flex-sub line-height-22 color-1d2129">{['--', '专家团队', '科研机构', '企业'][detailData.supplierType]}</div>
			</div>
			<div className="flex margin-top-20">
				<div className="margin-right-4 line-height-22 color-86909c">联系人：</div>
				<div className="flex-sub line-height-22 color-1d2129">
					{[detailData.supplierContacts, detailData.supplierContactsPhone].filter((ov) => ov).join(' / ') || '暂无'}
				</div>
			</div>
			{detailData.competitionGroup && (
				<div className="flex margin-top-20">
					<div className="margin-right-4 line-height-22 color-86909c">联系人组别：</div>
					<div className="flex-sub">
						<div className={`tag-status-${['', 'primary', 'warning', '700eb2'][detailData.competitionGroup]}`}>
							{competitionGroupTextList[detailData.competitionGroup] || '--'}
						</div>
					</div>
				</div>
			)}
			<div className="flex margin-top-20">
				<div className="margin-right-4 line-height-22 color-86909c">联系人职位：</div>
				<div className="flex-sub line-height-22 color-1d2129">{detailData.supplierContactsPosition || '--'}</div>
			</div>
			<div className="flex margin-top-20">
				<div className="margin-right-4 line-height-22 color-86909c">供给方介绍：</div>
				<div className="flex-sub line-height-22 color-1d2129">{detailData.supplierIntroduction || '暂无'}</div>
			</div>
		</>
	);
};

export default Index;
