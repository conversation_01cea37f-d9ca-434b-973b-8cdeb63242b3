import { useEffect, useState } from 'react';
import { Table, Button, Popconfirm, Space, Form, Input, Cascader, Row, Col, Affix, message, Select, InputNumber, Switch } from 'antd';
import Permission from '@/components/Permission';
import FormComp from '@/components/FormComp';
import PredictionList from '@/pages/Achv/Demand/Prediction/List';

import { useTableData } from '@/hook/useTableData';
import { useRouterLink } from '@/hook/useRouter';
import {
	pageDemand as getTablePageData,
	delDemand as delTableItemData,
	exportDemand as exportTableData,
	updateRankingNum as batchUpdateSort,
	queryDemandStatistics,
	batchUpdateContentDisplayStatus,
} from '@/api/Achv/Demand/DemandManage/index';
import { addRecommend, pageRecommend } from '@/api/Achv/DailyPush/index';
import { pageEnterprise } from '@/api/Achv/Demand/Prediction/index';
import { getThreeLevelData } from '@/api/common';

import { auditStatusData, auditStatusTextList, releaseTypeData, releaseTypeTextList, authData, authTextList } from '@/pages/Achv/config';

import './index.scss';

const Index = () => {
	const { linkTo, searchParams } = useRouterLink();
	const status = searchParams.get('auditStatus');
	const [auditStatus, setAuditStatus] = useState(status === null ? 3 : status - 0 || '');
	const { form, dataSource, pagination, changePage, delTableData, exportData, onSearch, onReset, SortInput, setDataSource } = useTableData({
		params: { auditStatus },
		getTablePageData,
		delTableItemData,
		exportTableData,
		batchUpdateSort,
		pageCallback: (paramsData = {}) => {
			getStatisticsData({ ...paramsData });
		},
	});

	// 获取统计数据
	const [statistics, setStatistics] = useState({});
	const getStatisticsData = (paramsData) => {
		delete paramsData.auditStatus;
		delete paramsData.pageSize;
		delete paramsData.pageNum;
		delete paramsData.tempArea;
		delete paramsData.tempDate;
		Promise.all([
			queryDemandStatistics(paramsData),
			pageEnterprise({
				pageNum: 1,
				pageSize: 1,
				recommendFlag: 1,
				confirmStatus: 1,
			}),
		]).then((resList) => {
			const resData = resList[0]?.data || {};
			setStatistics({
				...resData,
				total: Object.values(resData).reduce((pre, cur) => pre + (cur - 0), 0),
				preDemandCount: resList[1]?.data?.total || 0,
			});
		});
	};

	// 获取推荐列表
	const [recommendList, setRecommendList] = useState([]);

	const getRecommendList = () => {
		pageRecommend({ pageSize: 100, pageNum: 1 }).then((res) => {
			setRecommendList((res?.data?.records || []).filter((ov) => ov.recommendType === 1).map((ov) => ov.sourceId));
		});
	};

	// 推荐
	const recommend = (id) => {
		addRecommend({
			recommendType: 1,
			sourceId: id,
		}).then(() => {
			message.success('推荐成功');
			getRecommendList();
		});
	};

	useEffect(() => {
		getRecommendList();
	}, []);

	const contentDisplayStatusChange = (val, row) => {
		const params = {
			ids: [row.id],
			contentDisplayStatus: val,
		};
		batchUpdateContentDisplayStatus(params).then((res) => {
			const list = dataSource.map((ov) => {
				if (row.id == ov.id) {
					return {
						...ov,
						contentDisplayStatus: val,
					};
				} else {
					return ov;
				}
			});
			setDataSource(list);
		});
	};

	return (
		<div className="">
			<div className="flex flex-shrink align-center margin-bottom-4 height-48 font-size-22 font-weight-500 color-1d2129">需求管理</div>
			{/* Tabs & 功能按钮 开始 */}
			<Affix offsetTop={0}>
				<div className="flex align-center justify-between padding-24 border-radius-8 bg-color-ffffff border-solid-bottom-f2f3f5">
					<div className="flex align-center">
						{[
							...auditStatusData,
							{
								label: '预判需求',
								value: 4,
								countName: 'preDemandCount',
							},
						].map((ov) => (
							<div
								key={ov.value}
								className={`a margin-right-40 font-size-18 font-weight-500 ${auditStatus === ov.value ? 'color-165dff' : ''}`}
								onClick={() => setAuditStatus(ov.value)}
							>
								{ov.label}
								{statistics[ov.countName] > 0 ? `（${statistics[ov.countName]}）` : ''}
							</div>
						))}
					</div>
					<Space size={16}>
						<Permission hasPermi={['newAchv:demand:demandManage:list:export']}>
							<Button onClick={exportData}>批量导出</Button>
						</Permission>
						<Button
							type="primary"
							onClick={() => {
								linkTo(`/newAchv/demand/demandManage/curd`);
							}}
						>
							新建需求
						</Button>
					</Space>
				</div>
			</Affix>
			{/* Tabs & 功能按钮 结束 */}
			{auditStatus !== 4 ? (
				<div className="flex-sub margin-top-18 padding-24 border-radius-8 bg-color-ffffff">
					{/* 筛选条件 开始 */}
					<FilerForm form={form} onReset={onReset} onSearch={onSearch} />
					{/* 筛选条件 结束 */}

					{/* 表格 开始 */}
					<ListTable
						dataSource={dataSource}
						pagination={pagination}
						changePage={changePage}
						delTableData={delTableData}
						recommend={recommend}
						recommendList={recommendList}
						contentDisplayStatusChange={contentDisplayStatusChange}
						SortInput={SortInput}
					/>
					{/* 表格 结束 */}
				</div>
			) : (
				<PredictionList recommendFlag={1} />
			)}
		</div>
	);
};

export const FilerForm = (props = {}) => {
	const { form, onReset, onSearch } = props;

	// 获取选项数据
	const [areaOptions, setAreaOptions] = useState([]);
	const getOptionsData = () => {
		getThreeLevelData({ level: 2 }).then((res) => {
			setAreaOptions(res.data || []);
		});
	};

	useEffect(() => {
		getOptionsData();
	}, []);

	return (
		<div className="flex justify-between margin-bottom-18">
			<div className="flex-sub">
				<Form
					form={form}
					labelAlign="right"
					layout="inline"
					initialValues={{
						provinceCodes: undefined,
						cityCodes: undefined,
						tempArea: [],
						claimStartTime: undefined,
						authEndTime: undefined,
						tempDate: [],
					}}
				>
					<Form.Item hidden name="provinceCodes">
						<Input />
					</Form.Item>
					<Form.Item hidden name="cityCodes">
						<Input />
					</Form.Item>
					<Form.Item hidden name="claimStartTime">
						<Input />
					</Form.Item>
					<Form.Item hidden name="authEndTime">
						<Input />
					</Form.Item>
					<Row className="width-100per" gutter={[12, 12]}>
						<Col span={8}>
							<Form.Item name="keywords" label="需求名称">
								<Input placeholder="请输入需求名称" />
							</Form.Item>
						</Col>
						<Col span={8}>
							<Form.Item name="tempArea" label="区域">
								<Cascader
									options={areaOptions}
									placeholder="请选择区域"
									changeOnSelect
									displayRender={(label) => label.join('-')}
									onChange={(e = [undefined, undefined]) => {
										form.setFieldValue('provinceCodes', e[0] ? [e[0]] : undefined);
										form.setFieldValue('cityCodes', e[1] ? [e[1]] : undefined);
									}}
								/>
							</Form.Item>
						</Col>
						{!props.hideReleaseType && (
							<Col span={8}>
								<Form.Item name="releaseType" label="来源">
									<Select options={releaseTypeData} allowClear placeholder="请选择来源" />
								</Form.Item>
							</Col>
						)}
						<Col span={8}>
							<Form.Item name="isAuth" label="认证状态">
								<Select options={authData} allowClear placeholder="请选择认证状态" />
							</Form.Item>
						</Col>
						{props.openClaimTime && (
							<Col span={12}>
								<Form.Item name="tempDate" label="认领时间">
									<FormComp.DatePicker.RangePicker
										placeholder={['开始时间', '结束时间']}
										onChange={(date) => {
											form.setFieldsValue({
												claimStartTime: date[0],
												authEndTime: date[1],
											});
										}}
									/>
								</Form.Item>
							</Col>
						)}
					</Row>
				</Form>
			</div>
			<div className="margin-left-12 padding-left-12 border-solid-left-e5e6eb">
				<Space size={16}>
					<Button onClick={onReset}>重置</Button>
					<Button type="primary" onClick={onSearch}>
						查询
					</Button>
				</Space>
			</div>
		</div>
	);
};

export const ListTable = (props = {}) => {
	const { linkTo } = useRouterLink();
	const { dataSource, pagination, changePage, delTableData } = props;
	return (
		<Table rowKey="id" dataSource={dataSource} pagination={pagination} onChange={changePage} scroll={{ x: 'max-content' }}>
			<Table.Column
				title="序号"
				key="index"
				width={60}
				render={(_, __, index) => {
					return index + 1;
				}}
			/>

			<Table.Column title="需求名称" dataIndex="name" render={(text) => <div className="max-width-240">{text}</div>} />
			<Table.Column title="技术领域" dataIndex="areaCategoryListName" render={(text) => (text && text.length ? text.join('、') : '--')} />
			<Table.Column
				title="发布状态"
				dataIndex="auditStatus"
				align="center"
				render={(text) => {
					return (
						<div className={`tag-status-small-${['default', 'warning', 'error', 'primary'][text]}  `}>
							{auditStatusTextList[text || ''] || '--'}
						</div>
					);
				}}
			/>
			<Table.Column
				title="认证状态"
				dataIndex="isAuth"
				align="center"
				render={(text) => {
					return (
						<div
							style={{
								color: text === 1 ? '#1890ff' : '',
							}}
						>
							{authTextList[text || 0]}
						</div>
					);
				}}
			/>
			<Table.Column
				title="负责人"
				dataIndex="responsiblePersonnelList"
				align="center"
				render={(text) => {
					if (text && Array.isArray(text)) {
						return text.map((ov) => ov.userName).join('、') || '--';
					} else {
						return text || '--';
					}
				}}
			/>
			<Table.Column
				title="揭榜状态"
				dataIndex="proclamationStatus"
				align="center"
				render={(text) => {
					return <div className={`proclamation-status-${text}`}>{['', '可揭榜', '揭榜中', '已揭榜'][text]}</div>;
				}}
			/>
			<Table.Column
				title="合作意向"
				dataIndex="cooperateNum"
				align="center"
				render={(text, record) => {
					return text > 0 ? (
						<div
							className="a color-165dff"
							onClick={() => linkTo(`${props.linkToPath || '/newAchv/demand/demandManage'}/detail?id=${record.id}#section6`)}
						>
							{text}
						</div>
					) : (
						0
					);
				}}
			/>
			{props.contentDisplayStatusChange && (
				<Table.Column
					title="详情开放"
					dataIndex="contentDisplayStatus"
					render={(contentDisplayStatus, record) => {
						// 	状态:1 公开 0 不公开
						return (
							<Switch
								checked={contentDisplayStatus == 1}
								onChange={(val) => props.contentDisplayStatusChange((val && 1) || 0, record)}
							/>
						);
					}}
				/>
			)}
			<Table.Column
				title="所属区域"
				dataIndex="provinceCode"
				render={(_, record) => {
					return [record.provinceCodeName, record.cityCodeName, record.areaCodeName].filter((ov) => ov).join('-');
				}}
			/>
			<Table.Column
				title="来源"
				dataIndex="releaseType"
				render={(text) => {
					return (
						<div className={`tag-status-small-${['', 'primary', 'warning', '700eb2', 'success', 'error'][text]}`}>
							{releaseTypeTextList[text] || '--'}
						</div>
					);
				}}
			/>
			{props.SortInput && (
				<Table.Column
					title="排序"
					align="center"
					dataIndex="rankingNum"
					render={(_, record) => {
						return <props.SortInput record={record} />;
					}}
				/>
			)}
			<Table.Column
				title="提交时间"
				dataIndex="createTime"
				render={(text) => {
					return (text || '--').slice(0, 16);
				}}
			/>
			<Table.Column
				title="操作"
				key="option"
				dataIndex="option"
				align="center"
				fixed="right"
				render={(_, record) => {
					return (
						<>
							<Button
								type="link"
								size="small"
								onClick={() => linkTo(`${props.linkToPath || '/newAchv/demand/demandManage'}/detail?id=${record.id}&fromList=1`)}
							>
								编辑/审核
							</Button>
							<Popconfirm
								title="提示"
								description="确定删除吗？"
								onConfirm={() => {
									delTableData(record.id);
								}}
								okText="确定"
								cancelText="取消"
							>
								<Button type="link" size="small">
									删除
								</Button>
							</Popconfirm>
							{props.recommend && (
								<Button
									type="link"
									size="small"
									disabled={props.recommendList.includes(record.id)}
									onClick={() => props.recommend(record.id)}
								>
									推荐
								</Button>
							)}
						</>
					);
				}}
			/>
		</Table>
	);
};

export default Index;
