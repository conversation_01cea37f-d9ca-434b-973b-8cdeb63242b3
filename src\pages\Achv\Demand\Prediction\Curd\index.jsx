import { useEffect, useState } from 'react';
import { useRouterLink } from '@/hook/useRouter';

import { Button, Space, Form, Input, Radio, Checkbox, message, Affix, Select, Anchor, Cascader } from 'antd';

import { detailEnterprise, addEnterprise, updateEnterprise } from '@/api/Achv/Demand/Prediction/index';

import { getThreeLevelData } from '@/api/common';

import { getCategoryValueList } from '@/utils/achv';

import { releaseTypeData } from '@/pages/Achv/config';

import './index.scss';

// 基本信息
const BaseInfoForm = (props = {}) => {
	const [demandTypeList, setDemandTypeList] = useState([]);
	const [areaCategoryList, setAreaCategoryList] = useState([]);
	const [areaOptions, setAreaOptions] = useState([]);

	const confirmStatus = Form.useWatch('confirmStatus', props.form);

	useEffect(() => {
		getCategoryValueList('demand_type').then((res) => {
			setDemandTypeList(res);
		});

		getCategoryValueList('ttchannels_area').then((res) => {
			setAreaCategoryList(res);
		});

		getThreeLevelData({ level: 3 }).then((res) => {
			setAreaOptions(res.data || []);
		});
	}, []);

	return (
		<>
			<Form.Item label="企业名称" name="name">
				<Input className="input-box" style={{ border: '0 none', outline: 'none' }} readOnly />
			</Form.Item>
			<Form.Item label="需求名称" name="demandName">
				<Input className="input-box" placeholder="请输入需求名称" />
			</Form.Item>
			<Form.Item label="确认状态" name="confirmStatus" required>
				<Radio.Group
					options={[
						{ label: '未确认', value: 1 },
						{ label: '需求驳回', value: 2 },
						{ label: '已确认', value: 3 },
					]}
				/>
			</Form.Item>
			{confirmStatus === 2 && (
				<>
					<Form.Item label="驳回原因" name="reason">
						<Input.TextArea className="input-box" rows={2} placeholder="请输入驳回原因" />
					</Form.Item>
					<Form.Item label="反馈联系方式" name="feedbackPhone">
						<Input className="input-box" placeholder="请输入反馈联系方式" />
					</Form.Item>
				</>
			)}
			<Form.Item label="需求来源" name="demandSource" rules={[{ required: true, message: '请选择需求来源' }]}>
				<Radio.Group options={releaseTypeData} />
			</Form.Item>
			<Form.Item label="需求类型" name="demandTypeId" rules={[{ required: true, message: '请选择需求类型' }]}>
				<Radio.Group options={demandTypeList} />
			</Form.Item>

			<Form.Item
				label="技术领域"
				name="technicalFieldIds"
				rules={[
					{
						required: true,
						type: 'array',
						message: '请选择技术领域',
					},
				]}
			>
				<Checkbox.Group options={areaCategoryList} />
			</Form.Item>
			<Form.Item label="企业资质" name="aptitude">
				<Input className="input-box" placeholder="请输入企业资质" />
			</Form.Item>
			<Form.Item hidden name="provinceCode">
				<Input />
			</Form.Item>
			<Form.Item hidden name="cityCode">
				<Input />
			</Form.Item>
			<Form.Item label="所属区域" name="tempArea">
				<Cascader
					className="cascader-box"
					options={areaOptions}
					placeholder="请选择区域"
					displayRender={(label) => label.join('-')}
					onChange={(e = [undefined, undefined]) => {
						props.form.setFieldValue('provinceCode', e[0]);
						props.form.setFieldValue('cityCode', e[1]);
						props.form.setFieldValue('areaCode', e[2]);
					}}
				/>
			</Form.Item>
			<Form.Item label="企业联系电话" name="phone">
				<Input className="input-box" placeholder="请输入企业联系电话" />
			</Form.Item>
		</>
	);
};

const Index = () => {
	const { linkTo, searchParams } = useRouterLink();
	const [form] = Form.useForm();

	// 取消
	const onCancel = () => {
		linkTo(-1);
	};

	// 提交
	const onSubmit = () => {
		form.validateFields().then((values) => {
			delete values.tempArea;
			if (values.id) {
				updateEnterprise(values).then(() => {
					message.success('修改成功');
					setTimeout(() => {
						linkTo(-1);
					}, 500);
				});
			} else {
				addEnterprise(values).then(() => {
					message.success('添加成功');
					setTimeout(() => {
						linkTo(-1);
					}, 500);
				});
			}
		});
	};

	// 获取详情
	const getDetail = () => {
		const id = searchParams.get('id');
		if (id) {
			detailEnterprise({ id }).then((res) => {
				const resData = res.data || {};

				// 处理区域
				resData.tempArea = [resData.provinceCode, resData.cityCode, resData.areaCode].filter((ov) => ov);

				form.setFieldsValue(resData);
			});
		}
	};

	useEffect(() => {
		getDetail();
	}, []);

	return (
		<div className="">
			<div className="flex flex-shrink align-center margin-bottom-4 height-48 font-size-22 font-weight-500 color-1d2129">
				<Space size={10}>
					<div className="a color-86909c" onClick={() => linkTo(`/newAchv/demand/prediction/list`)}>
						需求管理
					</div>
					<div className="color-86909c">/</div>
					<div>企业需求预判编辑</div>
				</Space>
			</div>

			{/* Tabs & 功能按钮 开始 */}
			<Affix>
				<div className="height-66 flex align-center justify-between bg-color-ffffff border-radius-8 padding-lr-24 border-solid-bottom-f2f3f5">
					<div className="flex align-center justify-between color-1d2129 line-height-26 font-size-18 font-weight-500">
						<Anchor
							affix={false}
							rootClassName="anchor-header-tabBar-box"
							direction="horizontal"
							replace
							targetOffset={80}
							items={[
								{
									key: 'section1',
									href: '#section1',
									title: <div className="margin-right-40 font-size-16 font-weight-500">基本信息</div>,
								},
								{
									key: 'section2',
									href: '#section2',
									title: <div className="margin-right-40 font-size-16 font-weight-500">预判技术需求</div>,
								},
							]}
						/>
					</div>
					<div className="flex align-center justify-between color-1d2129 line-height-26 font-size-18 font-weight-500">
						<Space size={16}>
							<Button onClick={onCancel}>取消</Button>
							<Button type="primary" onClick={onSubmit}>
								保存
							</Button>
						</Space>
					</div>
				</div>
			</Affix>
			{/* Tabs & 功能按钮 结束 */}

			<Form
				form={form}
				className="antd-form-box"
				labelAlign="right"
				labelCol={{ style: { width: '116px' } }}
				initialValues={{
					id: '',
					confirmStatus: 1,
					demandSource: 1,
					technicalFieldIds: [],
					tempArea: [],
				}}
			>
				<Form.Item hidden name="id">
					<Input />
				</Form.Item>

				{/* 基本信息 开始 */}
				<div id="section1"></div>
				<div className="margin-top-20 padding-20 border-radius-8 bg-color-ffffff">
					<div className="padding-bottom-30 line-height-26 font-size-18 font-weight-500 color-1d2129">基本信息</div>
					<BaseInfoForm form={form} />
				</div>
				{/* 基本信息 结束 */}

				{/* 预判技术需求  开始 */}
				<div id="section2"></div>
				<div className="margin-top-20 padding-20 border-radius-8 bg-color-ffffff">
					<div className="padding-bottom-30 line-height-26 font-size-18 font-weight-500 color-1d2129">预判技术需求</div>
					<Form.Item
						label=" "
						labelCol={{ span: 0 }}
						name="technologicalRequirement"
						rules={[{ required: true, message: '请输入预判技术需求' }]}
					>
						<Input.TextArea rows={4} placeholder="请输入预判技术需求" />
					</Form.Item>
				</div>
				{/* 预判技术需求  结束 */}
			</Form>
		</div>
	);
};

export default Index;
