import { useEffect, useState } from 'react';
import { Button, Affix, Anchor, Space } from 'antd';
import { EditOutlined } from '@ant-design/icons';

import { detailEnterprise } from '@/api/Achv/Demand/Prediction/index';

import { useRouterLink } from '@/hook/useRouter';
import { releaseTypeTextList } from '@/pages/Achv/config';

const Index = () => {
	const { linkTo, searchParams } = useRouterLink();

	const id = searchParams.get('id') || '';
	const fromList = !!searchParams.get('fromList');

	const [detail, setDetail] = useState({});

	const getDetail = () => {
		detailEnterprise({ id }).then((res) => {
			const resData = res.data || {};
			setDetail(resData);
		});
	};

	useEffect(() => {
		getDetail();
	}, []);

	return (
		<div>
			<div className="flex flex-shrink align-center margin-bottom-4 height-48 font-size-22 font-weight-500">
				<div className="a color-86909c" onClick={() => linkTo(fromList ? linkTo(-1) : '/newAchv/demand/prediction/list')}>
					企业需求预判
				</div>
				<div className="margin-lr-10 color-86909c">/</div>
				<div className="color-1d2129">预判详情</div>
			</div>

			<div id="section1"></div>
			<Affix>
				<div className="height-66 flex align-center justify-between bg-color-ffffff border-radius-8 padding-lr-24 border-solid-bottom-f2f3f5">
					<div className="flex align-center justify-between color-1d2129 line-height-26 font-size-18 font-weight-500">
						<Anchor
							affix={false}
							rootClassName="anchor-header-tabBar-box"
							direction="horizontal"
							replace
							targetOffset={80}
							items={[
								{
									key: 'section1',
									href: '#section1',
									title: <div className="margin-right-40 font-size-16 font-weight-500">基本信息</div>,
								},
								{
									key: 'section2',
									href: '#section2',
									title: <div className="margin-right-40 font-size-16 font-weight-500">预判技术需求</div>,
								},
								{
									key: 'section3',
									href: '#section3',
									title: <div className="margin-right-40 font-size-16 font-weight-500">操作信息</div>,
								},
							]}
						/>
					</div>
					<div className="flex align-center justify-between color-1d2129 line-height-26 font-size-18 font-weight-500"></div>
				</div>
			</Affix>

			<div className="bg-color-ffffff border-radius-8 padding-lr-24 padding-tb-20 margin-tb-16">
				<div className="flex align-center justify-between margin-bottom-20">
					<div className="font-size-18 line-height-26 font-weight-500">需求信息</div>
					<Button
						type="link"
						icon={<EditOutlined />}
						onClick={() => {
							linkTo(`/newAchv/demand/${detail.demandId ? 'demandManage/detail' : 'prediction/curd'}?id=${detail.demandId || id}`);
						}}
					>
						编辑信息
					</Button>
				</div>
				<div className="flex align-center justify-start font-size-14 line-height-22 padding-left-16 margin-bottom-20">
					<div className="color-86909c margin-right-4">确认状态：</div>
					<div className="">
						<div className={`tag-status-${['default', 'warning', 'error', 'primary'][detail.confirmStatus]}`}>
							{['', '未确认', '需求驳回', '已确认'][detail.confirmStatus] || ''}
						</div>
					</div>
				</div>
				{detail.confirmStatus === 2 && (
					<>
						<div className="flex align-center justify-start font-size-14 line-height-22 padding-left-16 margin-bottom-20">
							<div className="color-86909c margin-right-4">驳回原因：</div>
							<div className="">{detail.reason || '无'}</div>
						</div>
						<div className="flex align-center justify-start font-size-14 line-height-22 padding-left-16 margin-bottom-20">
							<div className="color-86909c margin-right-4">反馈联系方式：</div>
							<div className="">{detail.feedbackPhone || '无'}</div>
						</div>
					</>
				)}
				<div className="flex align-center justify-start font-size-14 line-height-22 padding-left-16 margin-bottom-20">
					<div className="color-86909c margin-right-4">需求来源：</div>
					<div className="">
						<div className={`tag-status-${['', 'primary', 'warning', '700eb2', 'success', 'error'][detail.demandSource]}`}>
							{releaseTypeTextList[detail.demandSource] || '--'}
						</div>
					</div>
				</div>
				<div className="flex justify-start font-size-14 line-height-22 padding-left-16 margin-bottom-20">
					<div className="color-86909c margin-right-4">需求名称：</div>
					<div className="flex-sub">{detail.demandName || '--'}</div>
				</div>
				<div className="flex align-center justify-start font-size-14 line-height-22 padding-left-16 margin-bottom-20">
					<div className="color-86909c margin-right-4">企业名称：</div>
					<div className="">{detail.name || '--'}</div>
				</div>
				<div className="flex align-center justify-start font-size-14 line-height-22 padding-left-16 margin-bottom-20">
					<div className="color-86909c margin-right-4">需求类型：</div>
					<div className="">{detail.demandTypeName || '--'}</div>
				</div>
				<div className="flex align-center justify-start font-size-14 line-height-22 padding-left-16 margin-bottom-20">
					<div className="color-86909c margin-right-4">技术领域：</div>
					<div className="">{(detail.technicalFieldNames || []).join('、') || '--'}</div>
				</div>
				<div className="flex align-center justify-start font-size-14 line-height-22 padding-left-16 margin-bottom-20">
					<div className="color-86909c margin-right-4">企业资质：</div>
					<div className="">{detail.aptitude || '--'}</div>
				</div>
				<div className="flex align-center justify-start font-size-14 line-height-22 padding-left-16 margin-bottom-20">
					<div className="color-86909c margin-right-4">所属区域：</div>
					<div className="">{[detail.provinceName, detail.cityName, detail.areaName].filter((ov) => ov).join('-') || '--'}</div>
				</div>
				<div className="flex align-center justify-start font-size-14 line-height-22 padding-left-16 margin-bottom-20">
					<div className="color-86909c margin-right-4">企业联系电话：</div>
					<div className="">{detail.phone || '--'}</div>
				</div>
			</div>

			<div id="section2"></div>
			<div className="bg-color-ffffff border-radius-8 padding-lr-24 padding-tb-20 margin-tb-16">
				<div className="flex align-center justify-between margin-bottom-20">
					<div className="font-size-18 line-height-26 font-weight-500">预判技术需求</div>
					<Button
						type="link"
						icon={<EditOutlined />}
						onClick={() => {
							linkTo(`/newAchv/demand/${detail.demandId ? 'demandManage/detail' : 'prediction/curd'}?id=${detail.demandId || id}`);
						}}
					>
						编辑信息
					</Button>
				</div>
				<div
					className="font-size-14 padding-left-16 line-height-24 pre-wrap rich-box"
					dangerouslySetInnerHTML={{
						__html: detail.technologicalRequirement || '',
					}}
				></div>
			</div>

			<div id="section3"></div>
			<div className="bg-color-ffffff border-radius-8 padding-lr-24 padding-tb-20 margin-tb-16">
				<div className="flex align-center justify-between margin-bottom-20">
					<div className="font-size-18 line-height-26 font-weight-500">操作信息</div>
				</div>
				<div className="flex justify-start font-size-14 line-height-22 padding-left-16 margin-bottom-20">
					<div className="color-86909c margin-right-4">最终操作人账号：</div>
					<div className="flex align-center">{detail.statusUsername || '--'}</div>
				</div>
				<div className="flex align-center justify-start font-size-14 line-height-22 padding-left-16 margin-bottom-20">
					<div className="color-86909c margin-right-4">手机号：</div>
					<div className="">{detail.statusUserPhone || '--'}</div>
				</div>
			</div>
		</div>
	);
};

export default Index;
