import { useEffect, useState } from 'react';
import { Table, Button, Popconfirm, Space, Form, Input, Cascader, Row, Col, Affix, message, Modal, DatePicker } from 'antd';
import QrCode from '@/components/QrCode';

import { useTableData } from '@/hook/useTableData';
import { useRouterLink } from '@/hook/useRouter';
import {
	pageEnterprise as getTablePageData,
	deleteEnterprise as delTableItemData,
	exportEnterprise as exportTableData,
	importEnterprise as importTableData,
	statusStatistic,
} from '@/api/Achv/Demand/Prediction/index';

import { getThreeLevelData, getMiniProgramCode } from '@/api/common';
import dayjs from 'dayjs';

// 审核状态
export const auditStatusData = [
	{
		label: '未确认',
		value: 1,
		countName: 'waitReleaseNum',
	},
	{
		label: '需求驳回',
		value: 2,
		countName: 'notPassNum',
	},
	{
		label: '已确认',
		value: 3,
		countName: 'releaseNum',
	},
];

const Index = (props = {}) => {
	const { linkTo, searchParams } = useRouterLink();

	const [confirmStatus, setConfirmStatus] = useState(searchParams.get('confirmStatus') - 0 || 1);
	const { form, dataSource, pagination, changePage, delTableData, exportData, onSearch, onReset, importData, ImportModal } = useTableData({
		params: { confirmStatus, recommendFlag: props.recommendFlag, hiddenFlag: 1 },
		getTablePageData,
		delTableItemData,
		exportTableData,
		importTableData,
		pageCallback: (paramsData = {}) => {
			getStatisticsData({ ...paramsData });
		},
	});

	// 获取选项数据
	const [areaOptions, setAreaOptions] = useState([]);
	const getOptionsData = () => {
		getThreeLevelData({ level: 3 }).then((res) => {
			setAreaOptions(res.data || []);
		});
	};

	// 获取统计数据
	const [statistics, setStatistics] = useState({});
	const getStatisticsData = (paramsData) => {
		delete paramsData.confirmStatus;
		delete paramsData.pageSize;
		delete paramsData.pageNum;
		delete paramsData.tempArea;
		statusStatistic(paramsData).then((res) => {
			setStatistics(res.data || {});
		});
	};

	// 分享
	const [shareUrlObje, setShareUrlObje] = useState({});
	const [showShare, setShowShare] = useState('');
	const onShare = (id) => {
		if (shareUrlObje[id]) {
			setShowShare(shareUrlObje[id]);
			return;
		}

		getMiniProgramCode({
			appId: 'dwq1690976562914136064',
			pathName: 'ai/EnterpriseDemand/index',
			params: `id=${id}`,
		}).then((res) => {
			const miniUrl = `data:image/png;base64,${res.data}`;
			shareUrlObje[id] = miniUrl;
			setShareUrlObje({
				...shareUrlObje,
			});
			setShowShare(miniUrl);
		});
	};

	useEffect(() => {
		getOptionsData();
	}, []);

	return (
		<div className="">
			{props.recommendFlag === undefined && (
				<>
					<div className="flex flex-shrink align-center margin-bottom-4 height-48 font-size-22 font-weight-500 color-1d2129">
						企业需求预判
					</div>
					{/* Tabs & 功能按钮 开始 */}
					<Affix offsetTop={0}>
						<div className="flex align-center justify-between padding-24 border-radius-8 bg-color-ffffff border-solid-bottom-f2f3f5">
							<div className="flex align-center">
								{auditStatusData.map((ov) => (
									<div
										key={ov.value}
										className={`a margin-right-40 font-size-18 font-weight-500 ${
											confirmStatus === ov.value ? 'color-165dff' : ''
										}`}
										onClick={() => setConfirmStatus(ov.value)}
									>
										{ov.label}
										{statistics[ov.countName] > 0 ? `（${statistics[ov.countName]}）` : ''}
									</div>
								))}
							</div>
							<Space size={16}>
								<Button
									onClick={() => {
										message.info('暂未开放');
										return;
										importData();
									}}
								>
									批量导入
								</Button>
								<Button onClick={exportData}>批量导出</Button>
							</Space>
						</div>
					</Affix>
				</>
			)}
			{/* Tabs & 功能按钮 结束 */}
			<div className="flex-sub margin-top-18 padding-24 border-radius-8 bg-color-ffffff">
				{/* 筛选条件 开始 */}
				<div className="flex justify-between margin-bottom-18">
					<div className="flex-sub">
						<Form
							form={form}
							labelAlign="right"
							layout="inline"
							initialValues={{
								provinceCodes: undefined,
								cityCodes: undefined,
								areaCodes: undefined,
								tempArea: [],
								createStart: undefined,
								createEnd: undefined,
							}}
						>
							<Form.Item hidden name="provinceCodes">
								<Input />
							</Form.Item>
							<Form.Item hidden name="cityCodes">
								<Input />
							</Form.Item>
							<Form.Item hidden name="areaCodes">
								<Input />
							</Form.Item>
							<Row className="width-100per" gutter={[12, 12]}>
								<Col span={8}>
									<Form.Item name="name" label="企业名称">
										<Input placeholder="请输入企业名称" />
									</Form.Item>
								</Col>
								<Col span={8}>
									<Form.Item name="aptitude" label="企业资质">
										<Input placeholder="请输入企业资质" />
									</Form.Item>
								</Col>
								<Col span={8}>
									<Form.Item name="tempArea" label="所属区域">
										<Cascader
											options={areaOptions}
											placeholder="请选择区域"
											displayRender={(label) => label.join('-')}
											onChange={(e = [undefined, undefined, undefined]) => {
												form.setFieldValue('provinceCodes', e[0] ? e[0] : undefined);
												form.setFieldValue('cityCodes', e[1] ? e[1] : undefined);
												form.setFieldValue('areaCodes', e[2] ? e[2] : undefined);
											}}
										/>
									</Form.Item>
								</Col>

								<Form.Item hidden name="createStart">
									<Input />
								</Form.Item>
								<Form.Item hidden name="createEnd">
									<Input />
								</Form.Item>
								<Form.Item name="createStart-createEnd" label="创建时间">
									<DatePicker.RangePicker
										format="YYYY-MM-DD"
										onChange={(value) => {
											if (value) {
												form.setFieldValue('createStart', `${dayjs(value[0]).format('YYYY-MM-DD')} 00:00:01`);
												form.setFieldValue('createEnd', `${dayjs(value[1]).format('YYYY-MM-DD')} 23:59:59`);
											} else {
												form.setFieldValue('createStart', undefined);
												form.setFieldValue('createEnd', undefined);
											}
										}}
									/>
								</Form.Item>
							</Row>
						</Form>
					</div>
					<div className="margin-left-12 padding-left-12 border-solid-left-e5e6eb">
						<Space size={16}>
							<Button onClick={onReset}>重置</Button>
							<Button type="primary" onClick={onSearch}>
								查询
							</Button>
						</Space>
					</div>
				</div>
				{/* 筛选条件 结束 */}

				{/* 表格 开始 */}
				<Table rowKey="id" dataSource={dataSource} pagination={pagination} onChange={changePage} scroll={{ x: 'max-content' }}>
					<Table.Column
						title="序号"
						key="index"
						width={60}
						render={(_, __, index) => {
							return index + 1;
						}}
					/>
					<Table.Column title="企业名称" dataIndex="name" render={(text) => text || '--'} />
					<Table.Column
						title="预判技术需求"
						dataIndex="technologicalRequirement"
						render={(text) => {
							return (
								<div className="max-width-400 text-cut-3" title={text}>
									{text || '--'}
								</div>
							);
						}}
					/>
					<Table.Column
						title="确认状态"
						dataIndex="confirmStatus"
						render={(text) => {
							return (
								<div className={`tag-status-small-${['default', 'warning', 'error', 'primary'][text]}  `}>
									{['', '未确认', '需求驳回', '已确认'][text]}
								</div>
							);
						}}
					/>
					<Table.Column title="企业资质" dataIndex="aptitude" render={(text) => text || '--'} />
					<Table.Column
						title="所属区域"
						dataIndex="provinceCode"
						render={(_, record) => {
							return [record.provinceCodeName, record.cityCodeName, record.areaCodeName].filter((ov) => ov).join('-');
						}}
					/>
					<Table.Column title="联系电话" dataIndex="phone" render={(text) => text || '--'} />
					<Table.Column
						title="提交时间"
						dataIndex="createTime"
						render={(text) => {
							return (text || '--').slice(0, 16);
						}}
					/>
					<Table.Column
						title="创建人"
						dataIndex="createName"
						render={(text) => {
							return text || '--';
						}}
					/>
					<Table.Column
						title="操作"
						key="option"
						dataIndex="option"
						align="center"
						fixed="right"
						render={(_, record, index) => {
							return (
								<>
									<Button type="link" size="small" disabled={record.confirmStatus !== 1} onClick={() => onShare(record.id)}>
										分享
									</Button>
									<Button
										type="link"
										size="small"
										onClick={() => linkTo(`/newAchv/demand/prediction/detail?id=${record.id}&fromList=1`)}
									>
										编辑
									</Button>
									<Popconfirm
										title="提示"
										description="确定删除吗？"
										onConfirm={() => {
											delTableData(record.id);
										}}
										okText="确定"
										cancelText="取消"
									>
										<Button type="link" size="small">
											删除
										</Button>
									</Popconfirm>
								</>
							);
						}}
					/>
				</Table>
				{/* 表格 结束 */}

				{/* 导入 开始 */}
				<ImportModal tplUrl="https://gbac-produce.oss-cn-shenzhen.aliyuncs.com/tip/competition/%E8%B5%9B%E4%BA%8B%E6%8A%A5%E5%90%8D%E5%AF%BC%E5%85%A5%E6%A8%A1%E7%89%88.xlsx" />
				{/* 导入 结束 */}
			</div>

			<Modal
				title="二维码"
				open={showShare !== ''}
				onCancel={() => {
					setShowShare('');
				}}
				centered
				footer={null}
				width={300}
				className="qr-code-modal"
			>
				<QrCode.QRCodebase64
					qrCodeList={[
						{
							qrcodeUrl: showShare,
							qrcodeName: '二维码',
						},
					]}
				/>
			</Modal>
		</div>
	);
};

export default Index;
