import { useEffect, useState, useRef, forwardRef, useImperativeHandle } from 'react';
import { useRouterLink } from '@/hook/useRouter';
import { Button, Space, Form, Input, InputNumber, message, Affix, Radio, Checkbox, DatePicker, Modal, TimePicker, Popconfirm } from 'antd';
import {
	MenuFoldOutlined,
	DeleteOutlined,
	PlusCircleOutlined,
	CloseOutlined,
	PlusCircleFilled,
	PlusOutlined,
	MinusCircleOutlined,
	ClockCircleOutlined,
	QuestionCircleOutlined,
} from '@ant-design/icons';
import { DragDropContext, Droppable, Draggable } from 'react-beautiful-dnd';

import UploadImg from '@/components/UploadImg';
import UEditor from '@/components/UEditor';
import SelectTTChannels from '@/components/Achv/SelectTTChannels';

import { getEvent, addEvent, updateEvent } from '@/api/Achv/Event/index';

import { releaseTypeData } from '@/pages/Achv/config';

import dayjs from 'dayjs';

const Index = (props = {}) => {
	const { linkTo, searchParams } = useRouterLink();
	const id = searchParams.get('id') || '';
	const [form] = Form.useForm();
	const releaseType = Form.useWatch('releaseType', form);

	// 跳转地址
	const linkToPath = props.linkToPath || `/newAchv/event`;

	// 禁止编辑发布类型
	const [disabledReleaseType, setDisabledReleaseType] = useState(false);
	const [hash, setHash] = useState('');

	// 议程添加方式
	const [agendaType, setAgendaType] = useState(2);

	// 取消
	const onCancel = () => {
		linkTo(-1);
	};

	// 提交
	const onSubmit = () => {
		form.validateFields()
			.then((values) => {
				const params = { ...values };
				(values.id ? updateEvent : addEvent)({
					...params,
					enrollDesignJson: JSON.stringify(params.enrollDesignJson),
					agendaJson: JSON.stringify(params.agendaJson),
					agendaUrl: agendaType === 1 ? params.agendaUrl : '',
					ttChannelsIds: params.ttChannelsIds ? params.ttChannelsIds.map((ov) => ov.id) : undefined,
					adminStaffList: params.adminStaffList
						? params.adminStaffList.map((ov) => {
								return {
									adminUserId: ov.adminUserId || ov.userId,
									adminUserName: ov.adminUserName || ov.accountName,
								};
						  })
						: undefined,
				}).then(() => {
					message.success(values.id ? '修改成功' : '添加成功');
					setTimeout(() => {
						linkTo(-1);
					}, 500);
				});
			})
			.catch((error) => {
				console.log(error);

				const errorName = error.errorFields[0].name[0];
				if (['ttChannelsIds', 'adminStaffList'].includes(errorName)) {
					setHash('section5');
				} else if (['introduce'].includes(errorName)) {
					setHash('section2');
				} else {
					setHash('section1');
				}
			});
	};

	// 获取详情
	const getDetail = () => {
		if (id) {
			getEvent({ id }).then((res) => {
				const resData = res.data || {};

				// 处理 报名表单配置
				if (resData.enrollDesignJson) {
					try {
						resData.enrollDesignJson = JSON.parse(resData.enrollDesignJson).filter((ov) => ov);
					} catch (error) {
						resData.enrollDesignJson = [];
					}
				}

				// 处理 议程
				if (resData.agendaJson) {
					try {
						resData.agendaJson = JSON.parse(resData.agendaJson);
					} catch (error) {
						resData.agendaJson = [];
					}
				}

				if (resData.agendaUrl) {
					setAgendaType(1);
				}

				// 处理 关联科转号
				resData.ttChannelsIds = resData.ttChannelsList || [];

				// 处理 管理人
				resData.adminStaffList = resData.adminStaffTTChannelsList || [];

				form.setFieldsValue(resData);
				// 编辑 与固定类型 一致时不可以编辑
				setDisabledReleaseType(resData.releaseType === props.releaseType);
			});
		} else if (props.releaseType) {
			// 新建时 如果是固定类型入口就默认类型
			form.setFieldValue('releaseType', props.releaseType);
			// 新建时 固定类型不可编辑
			setDisabledReleaseType(true);
		}
	};

	useEffect(() => {
		const hash = window.location.hash.replace('#', '');
		setHash(hash || 'section1');

		getDetail();
	}, []);

	// 跳转到指定锚点
	useEffect(() => {
		if (hash) {
			setTimeout(() => {
				const element = document.getElementById(hash);
				if (element) {
					element.scrollIntoView({ behavior: 'smooth' });
				}
			}, 500);
		}
	}, [hash]);

	return (
		<div className="">
			<div className="flex flex-shrink align-center margin-bottom-4 height-48 font-size-22 font-weight-500 color-1d2129">
				<Space size={10}>
					<div className="a color-86909c" onClick={() => linkTo(linkToPath)}>
						活动管理
					</div>
					<div className="color-86909c">/</div>
					<div>{id ? '活动编辑' : '新增活动'}</div>
				</Space>
			</div>

			{/* Tabs & 功能按钮 开始 */}
			<div id="section1"></div>
			<Affix offsetTop={0}>
				<div className="flex align-center justify-between padding-24 border-radius-8 border-solid-bottom-f2f2f2 bg-color-ffffff">
					<div className="flex align-center">
						{['基本信息', '活动描述', '报名表单', '活动议程', '关联账号'].map((ov, oi) => {
							const currentHash = `section${oi + 1}`;
							return (
								<div
									key={oi}
									className={`a margin-right-40 font-size-18 font-weight-500 ${hash === currentHash ? 'color-165dff' : ''}`}
									onClick={() => {
										if (currentHash === 'section4' || currentHash === 'section5') {
											form.validateFields()
												.then(() => {
													setHash(`section${oi + 1}`);
												})
												.catch((error) => {
													const errorName = error.errorFields[0].name[0];
													if (['ttChannelsIds', 'adminStaffList'].includes(errorName)) {
														setHash('section5');
													} else if (['introduce'].includes(errorName)) {
														setHash('section2');
													} else {
														setHash('section1');
													}
												});
										} else {
											setHash(`section${oi + 1}`);
										}
									}}
								>
									{ov}
								</div>
							);
						})}
					</div>
					<Space size={16}>
						<Button onClick={onCancel}>取消</Button>
						<Button type="primary" onClick={onSubmit}>
							保存
						</Button>
					</Space>
				</div>
			</Affix>
			{/* Tabs & 功能按钮 结束 */}

			<Form
				form={form}
				className="antd-form-box"
				labelAlign="right"
				labelCol={{ style: { width: '120px' } }}
				initialValues={{
					enrollDesignJson: [
						{
							type: 1,
							isBase: true,
							required: true,
							lable: '姓名',
							placeholder: '请输入姓名',
							keyName: 'name',
						},
						{
							type: 1,
							isBase: true,
							required: true,
							lable: '手机',
							placeholder: '请输入手机',
							keyName: 'phone',
						},
						{
							type: 1,
							isBase: true,
							required: true,
							lable: '公司',
							placeholder: '请输入公司',
							keyName: 'company',
						},
						{
							type: 1,
							isBase: true,
							required: true,
							lable: '职务',
							placeholder: '请输入职务',
							keyName: 'position',
						},
					],
					agendaJson: [],
					applicationAuditStatus: 1,
					maximumApplicationNumber: null,
					profilePictureShowStatus: 0,
					releaseType: 1,
					releasePlatform: 1,
					adminStaffList: [],
					ttChannelsIds: [],
					delTTChannelsIds: [],
				}}
			>
				<Form.Item hidden name="id">
					<Input />
				</Form.Item>
				<Form.Item hidden name="releasePlatform">
					<Input />
				</Form.Item>
				<Form.Item hidden name="delTTChannelsIds">
					<Input />
				</Form.Item>
				<Form.Item hidden name="rankingNum">
					<Input />
				</Form.Item>

				<div style={{ display: ['section1', 'section2', 'section3'].includes(hash) ? 'block' : 'none' }}>
					{/* 基本信息 开始 */}
					<div className="margin-top-20 padding-20 border-radius-8 bg-color-ffffff">
						<div className="padding-bottom-30 line-height-26 font-size-18 font-weight-500 color-1d2129">基本信息</div>
						<Form.Item label="活动名称" name="title" rules={[{ required: true, message: '请输入活动名称' }]}>
							<Input className="input-box" placeholder="请输入活动名称" />
						</Form.Item>
						<Form.Item hidden name="endTime">
							<Input />
						</Form.Item>
						<Form.Item label="活动时间" name="startTime" rules={[{ required: true, message: '请选择活动时间' }]}>
							<TimeFormItem form={form} />
						</Form.Item>
						<Form.Item label="活动地址" name="resolveAddress" rules={[{ required: true, message: '请输入活动举办地址' }]}>
							<Input className="input-box" placeholder="请输入活动举办地址" />
						</Form.Item>
						<Form.Item label="显示地址" name="address">
							<Input className="input-box" placeholder="请输入活动显示地址（前端优先展示该地址）" />
						</Form.Item>
						<Form.Item label="报名人数" name="maximumApplicationNumber">
							<ApplicationNumber />
						</Form.Item>
						<Form.Item label="报名审核" name="applicationAuditStatus">
							<Radio.Group
								options={[
									{ label: '需要审核', value: 1 },
									{ label: '不需要审核', value: 0 },
								]}
							/>
						</Form.Item>
						<Form.Item label="最近报名" name="profilePictureShowStatus">
							<Radio.Group
								options={[
									{ label: '使用加热数据', value: 1 },
									{ label: '使用真实数据', value: 0 },
								]}
							/>
						</Form.Item>
						<Form.Item label="活动封面" name="coverImageUrl" rules={[{ required: true, message: '请上传活动封面' }]}>
							<UploadImg size={5} width={213} height={120} tips={'建议尺寸：640*360px'} cropperProps={{ width: 320, height: 180 }} />
						</Form.Item>
						<Form.Item label="微信二维码" name="qrCode">
							<UploadImg size={5} width={120} height={120} tips={'建议尺寸：200*200px'} cropperProps={{ width: 200, height: 200 }} />
						</Form.Item>
						<Form.Item label="二维码说明" name="qrCodeTips">
							<Input className="input-box" placeholder="同上，于报名提交后展示" />
						</Form.Item>
						<Form.Item label="分享封面" name="shareImage">
							<UploadImg size={5} width={150} height={120} tips={'建议尺寸：150*120px'} />
						</Form.Item>
						<Form.Item label="发布类型" name="releaseType" required>
							<Radio.Group options={releaseTypeData.filter((ov) => ov.value < 3)} disabled={disabledReleaseType} />
						</Form.Item>
					</div>
					{/* 基本信息 结束 */}

					{/* 活动描述 开始 */}
					<div id="section2"></div>
					<div className="margin-top-20 padding-20 border-radius-8 bg-color-ffffff">
						<div className="padding-bottom-30 line-height-26 font-size-18 font-weight-500 color-1d2129">活动描述</div>
						<Form.Item
							label="活动描述"
							name="introduce"
							wrapperCol={{ span: 24 }}
							rules={[{ required: true, message: '请输入活动描述' }]}
						>
							<UEditor />
						</Form.Item>
						<Form.Item label="展示图片" name="showPictures">
							<ShowPictures />
						</Form.Item>
					</div>
					{/* 活动描述 结束 */}

					{/* 报名表单 开始 */}
					<div id="section3"></div>
					<div className="margin-top-20 padding-20 border-radius-8 bg-color-ffffff">
						<div className="padding-bottom-30 line-height-26 font-size-18 font-weight-500 color-1d2129">报名表单</div>
						<Form.Item name="enrollDesignJson">
							<ApplyFormConfig />
						</Form.Item>
					</div>
					{/* 报名表单 结束 */}
				</div>

				{/* 活动议程 开始 */}
				<div style={{ display: hash === 'section4' ? 'block' : 'none' }}>
					<div id="section4"></div>
					<div className="margin-top-20 padding-20 border-radius-8 bg-color-ffffff">
						<div className="padding-bottom-30 line-height-26 font-size-18 font-weight-500 color-1d2129">活动议程</div>
						<Form.Item label="议程类型">
							<Radio.Group
								value={agendaType}
								options={[
									{ label: '自定义', value: 2 },
									{ label: '上传图片', value: 1 },
								]}
								onChange={(e) => {
									setAgendaType(e.target.value);
								}}
							/>
						</Form.Item>
						{agendaType === 1 && (
							<Form.Item label="议程图片" name="agendaUrl">
								<UploadImg size={5} width={213} height={120} tips={'上传后将优先用该图片作为议程展示'} />
							</Form.Item>
						)}
						{agendaType === 2 && (
							<Form.Item name="agendaJson">
								<Agenda />
							</Form.Item>
						)}
					</div>
				</div>
				{/* 活动议程 结束 */}

				{/* 关联账号 开始 */}
				<div style={{ display: hash === 'section5' ? 'block' : 'none' }}>
					<div id="section3"></div>
					<SelectTTChannels
						form={form}
						name="ttChannelsIds"
						label="关联科转号"
						rules={[{ required: releaseType === 2, type: 'array', message: '请选择关联科转号' }]}
						collectDelIds={true}
					/>
					<SelectTTChannels
						form={form}
						name="adminStaffList"
						label="管理人员"
						rules={[{ required: true, type: 'array', message: '请选择管理人员' }]}
					/>
				</div>
				{/* 关联账号 结束 */}
			</Form>
		</div>
	);
};

// 活动时间
const TimeFormItem = (props = {}) => {
	const startTime = Form.useWatch('startTime', props.form);
	const endTime = Form.useWatch('endTime', props.form);

	return (
		<DatePicker.RangePicker
			value={[dayjs(startTime), dayjs(endTime)]}
			showTime
			format={'YYYY-MM-DD HH:mm'}
			onChange={(_, e) => {
				props.form.setFieldsValue({
					startTime: e[0] ? e[0].padEnd(19, ':00') : null,
					endTime: e[1] ? e[1].padEnd(19, ':59') : null,
				});
			}}
		/>
	);
};

// 报名人数
const ApplicationNumber = (props = {}) => {
	return (
		<Space>
			<InputNumber
				value={props.value}
				min={1}
				precision={0}
				className="width-160"
				placeholder="请输入报名人数"
				onChange={(e) => props.onChange(e || null)}
			/>
			<Checkbox
				checked={props.value === null}
				onChange={(e) => {
					props.onChange(null);
				}}
			>
				不限人数
			</Checkbox>
		</Space>
	);
};

// 展示图片
const ShowPictures = (props = {}) => {
	console.log(props);

	const value = props.value ? props.value.split() : [];

	return <UploadImg.MultipleUpload value={value} maxCount={9} size={5} width={120} height={120} onChange={(e) => props.onChange(e.join())} />;
};

// 报名表单配置
const ApplyFormConfig = (props = {}) => {
	const infoList = props.value || [];

	const setInfoList = (list) => {
		props.onChange(list);
	};

	// 添加信息项
	const addInfoItem = (type, isBase = false, keyName = '', lable = '', placeholder = '') => {
		const infoItem = {
			type,
			isBase,
			required: isBase,
			lable: lable || ['', '单行文本框', '多行文本框', '单项按钮框', '多项按钮框'][type],
			placeholder,
			keyName: keyName || 'field-' + new Date().valueOf().toString(),
		};

		if (type === 3 || type === 4) {
			infoItem.options = ['', ''];
		}

		infoList.push(infoItem);
		setInfoList([...infoList]);
	};

	// 更新信息项
	const changeInfoItem = (keyName, key, value) => {
		const findData = infoList.find((item) => item.keyName === keyName);

		if (findData) {
			findData[key] = value;
			setInfoList([...infoList]);
		}
	};

	// 删除信息项
	const delInfoItem = (keyName) => {
		const index = infoList.findIndex((item) => item.keyName === keyName);
		if (index > -1) {
			infoList.splice(index, 1);
			setInfoList([...infoList]);
		}
	};

	// 排序列表
	const sortList = (keyName, targetKeyName) => {
		const index = infoList.findIndex((item) => item.keyName === keyName);
		const targetIndex = infoList.findIndex((item) => item.keyName === targetKeyName);

		const tempData = infoList[targetIndex];
		infoList[targetIndex] = infoList[index];
		infoList[index] = tempData;

		setInfoList([...infoList]);
	};

	return (
		<div style={{ padding: '0 32px', width: '600px' }}>
			<div className="margin-bottom-4">
				<div className="margin-bottom-16 line-height-22 font-size-16 font-weight-500">默认表单项</div>
				{infoList
					.filter(({ isBase }) => isBase)
					.map((item) => {
						return <InfoItem key={item.keyName} infoData={item} onChange={changeInfoItem} onDel={delInfoItem} hideDrag />;
					})}
			</div>
			<div className="margin-bottom-4">
				<div className="margin-bottom-16 line-height-22 font-size-16 font-weight-500">自定义表单项</div>
				<DragList list={infoList.filter(({ isBase }) => !isBase)} onChange={changeInfoItem} onDel={delInfoItem} onSort={sortList} />
			</div>

			<div className="margin-bottom-12 line-height-22 font-size-14 font-weight-500">添加自定义选项：</div>
			<div className="flex align-center margin-bottom-20">
				{['单行文本框', '多行文本框', '单选按钮框', '多选按钮框'].map((ov, oi) => {
					return (
						<div
							key={oi}
							className="flex align-center margin-right-30 padding-lr-12 height-32 border-e5e6e8 border-radius-2 color-86909c cursor-pointer hover-color-165dff hover-border-165dff"
							onClick={() => addInfoItem(oi + 1)}
						>
							{ov}
						</div>
					);
				})}
			</div>
		</div>
	);
};

// 拖拉列表
const DragList = (props) => {
	const onDragEnd = (result) => {
		const { destination, draggableId } = result;
		if (!destination) {
			return;
		}
		props.onSort(draggableId, props.list[destination.index].keyName);
	};
	return (
		<DragDropContext onDragEnd={onDragEnd}>
			<Droppable droppableId="id">
				{(provided = {}) => (
					<div ref={provided.innerRef} {...provided.droppableProps}>
						{props.list.map((item, index) => {
							return (
								<Draggable draggableId={item.keyName} index={index} key={item.keyName}>
									{(provided) => (
										<div className="overflow-hidden" ref={provided.innerRef} {...provided.draggableProps}>
											<InfoItem
												infoData={item}
												onChange={props.onChange}
												onDel={props.onDel}
												dragHandleProps={provided.dragHandleProps}
											/>
										</div>
									)}
								</Draggable>
							);
						})}
						{provided.placeholder}
					</div>
				)}
			</Droppable>
		</DragDropContext>
	);
};

const InfoItem = (props) => {
	const infoData = props.infoData;
	const isDefault = ['name', 'phone'].includes(infoData.keyName);

	return (
		<div key={infoData.keyName}>
			<div className={`flex align-start ${infoData.type === 1 || infoData.type === 2 ? 'margin-bottom-20' : ''}`}>
				<Checkbox
					disabled={isDefault}
					className="flex align-center margin-right-12 line-height-32"
					checked={infoData.required}
					onChange={(e) => {
						props.onChange(infoData.keyName, 'required', e.target.checked);
					}}
				>
					必填
				</Checkbox>
				<Input
					className="width-160 height-32 bg-color-f2f3f5"
					defaultValue={infoData.lable}
					placeholder="请填写自定义名称"
					onBlur={(e) => {
						props.onChange(infoData.keyName, 'lable', e.target.value);
					}}
				></Input>
				{infoData.type === 1 || infoData.type === 2 ? (
					<>
						<div className="padding-lr-8 line-height-32">-</div>
						{infoData.type === 1 ? (
							<Input
								className="flex-sub bg-color-f2f3f5"
								defaultValue={infoData.placeholder}
								placeholder="请填写提示信息"
								onBlur={(e) => {
									props.onChange(infoData.keyName, 'placeholder', e.target.value);
								}}
							></Input>
						) : (
							<Input.TextArea
								className="flex-sub bg-color-f2f3f5"
								rows={3}
								defaultValue={infoData.placeholder}
								placeholder="请填写提示信息"
								onBlur={(e) => {
									props.onChange(infoData.keyName, 'placeholder', e.target.value);
								}}
							></Input.TextArea>
						)}
					</>
				) : (
					<div className="flex-sub"></div>
				)}
				<Space size={12} className="width-56 margin-left-8 line-height-32 font-size-18 color-86909c">
					{!props.hideDrag && <MenuFoldOutlined {...props.dragHandleProps} className="hover-color-165dff cursor-pointer" />}
					{!isDefault ? (
						<DeleteOutlined
							className="hover-color-165dff cursor-pointer"
							onClick={() => {
								props.onDel(infoData.keyName);
							}}
						/>
					) : null}
				</Space>
			</div>
			{infoData.type === 3 || infoData.type === 4 ? (
				<div className="margin-left-72 margin-bottom-20">
					<div className="margin-top-8 margin-bottom-4 line-height-22">选项列表：</div>
					<div className="flex">
						<Space size={8} direction="vertical" className="flex-sub">
							{infoData.options.map((option, key) => {
								return (
									<Input
										className="width-100per flex-shrink bg-color-f2f3f5"
										defaultValue={option}
										key={new Date() + key}
										placeholder={`选项${key + 1}`}
										suffix={
											key <= 1 ? null : (
												<CloseOutlined
													onClick={() => {
														infoData.options.splice(key, 1);
														props.onChange(infoData.keyName, 'options', infoData.options);
													}}
													style={{ fontSize: '12px' }}
												/>
											)
										}
										onBlur={(e) => {
											infoData.options[key] = e.target.value;
											props.onChange(infoData.keyName, 'options', infoData.options);
										}}
									></Input>
								);
							})}
						</Space>
						<div className="min-width-56 margin-left-8 line-height-32 font-size-18 color-86909c hover-color-165dff cursor-pointer">
							<PlusCircleOutlined
								onClick={() => {
									infoData.options.push('');
									props.onChange(infoData.keyName, 'options', infoData.options);
								}}
							/>
						</div>
					</div>
				</div>
			) : null}
		</div>
	);
};

// 议程
const Agenda = (props = {}) => {
	const AgendaModalRef = useRef(null);
	const ahemdaList = props.value || [];

	const setAhemdaList = (list) => {
		props.onChange(list);
	};

	// 显示弹窗
	const showAgendaModal = (id = '', index = -1) => {
		AgendaModalRef.current.showModal(
			id,
			index > -1
				? {
						index,
						...ahemdaList[index],
				  }
				: {}
		);
	};

	// 议程变化
	const onAgendaChange = (data = {}) => {
		if (data.type === 'add') {
			ahemdaList.push(data.params);
		} else if (data.type === 'edit') {
			ahemdaList[data.index] = data.params;
		} else if (data.type === 'del') {
			ahemdaList.splice(data.index);
		}
		setAhemdaList([...ahemdaList]);
	};

	return (
		<>
			{/* 添加按钮 开始 */}
			<div className="flex align-center justify-center height-32 border-radius-2 border-165dff color-165dff" onClick={() => showAgendaModal()}>
				<PlusCircleFilled style={{ fontSize: '16px' }} />
				<div className="margin-left-8">添加议程</div>
			</div>
			{/* 添加按钮 结束 */}

			{/* 议程列表 开始 */}
			{ahemdaList.length ? (
				<div className="margin-top-12 padding-8 border-radius-2 bg-color-f7f9fc">
					{ahemdaList.map((ahemda, index) => {
						return (
							<div key={index} className="flex padding-tb-8">
								<div className="flex align-center margin-lr-8 padding-lr-12 height-32 border-radius-2 border-e5e6eb bg-color-ffffff">
									<div>{ahemda.startTime}</div>
									<div className="margin-lr-20 color-c9cdd4">-</div>
									<div>{ahemda.endTime}</div>
									<ClockCircleOutlined
										style={{
											marginLeft: '30px',
											fontSize: '16px',
											color: '#C9CDD4',
										}}
									/>
								</div>
								<div className="flex-sub">
									<div className="flex align-center justify-between margin-lr-8 padding-lr-12 height-32 border-radius-2 border-e5e6eb bg-color-ffffff">
										<div>{ahemda.agendaTheme}</div>
										<div
											className="padding-left-10 color-165dff cursor-pointer"
											onClick={() => showAgendaModal(ahemda.id, index)}
										>
											编辑
										</div>
									</div>
									{ahemda.eventAgendaGuestsList.map((item, key) => {
										return (
											<div
												key={key}
												className="margin-top-8 flex align-center justify-between margin-lr-8 padding-lr-12 height-32 border-radius-2 border-e5e6eb bg-color-ffffff color-86909c"
											>
												{[item.name, item.enterpriseName, item.positionTitle].filter((item) => item).join(' / ')}
											</div>
										);
									})}
								</div>
							</div>
						);
					})}
				</div>
			) : null}
			{/* 议程列表 结束 */}

			{/* 弹窗 开始 */}
			<AgendaModal ref={AgendaModalRef} eventId={props.eventId} onChange={onAgendaChange} />
			{/* 弹窗 结束 */}
		</>
	);
};

// 弹窗
const AgendaModal = forwardRef((props, ref) => {
	const [open, setOpen] = useState(false);
	const [agendaId, setAgendaId] = useState('');
	const [index, setIndex] = useState(-1);
	const [form] = Form.useForm();

	// 显示弹窗
	const showModal = (id = '', detailData = {}) => {
		if (Object.values(detailData).length) {
			setIndex(detailData.index);
			setDetail(detailData);
		}
		setOpen(true);
	};

	// 设置详情
	const setDetail = (detailData = {}) => {
		const { agendaTheme, eventAgendaGuestsList, startTime, endTime } = detailData;
		form.setFieldsValue({
			timeList: [dayjs(startTime, 'HH:mm'), dayjs(endTime, 'HH:mm')],
			agendaTheme,
			eventAgendaGuestsList: eventAgendaGuestsList.map((item) => {
				return {
					name: item.name,
					enterpriseName: item.enterpriseName,
					positionTitle: item.positionTitle,
				};
			}),
		});
	};

	// 删除
	const onDel = () => {
		props.onChange({
			type: 'del',
			index,
		});
		close();
	};

	// 提交
	const submit = () => {
		form.validateFields().then((res) => {
			const params = {
				agendaTheme: res.agendaTheme,
				startTime: dayjs(res.timeList[0]).format('HH:mm'),
				endTime: dayjs(res.timeList[1]).format('HH:mm'),
				eventAgendaGuestsList: res.eventAgendaGuestsList.filter((item) => Object.values(item).some((sItem) => sItem)),
			};
			props.onChange({
				type: index > -1 ? 'edit' : 'add',
				index,
				params,
			});
			close();
		});
	};

	// 关闭重置数据
	const close = () => {
		form.resetFields();
		setAgendaId('');
		setIndex(-1);
		setOpen(false);
	};

	useImperativeHandle(ref, () => {
		return {
			showModal,
		};
	});

	return (
		<Modal
			open={open}
			title="议程内容"
			centered
			onCancel={() => setOpen(false)}
			onOk={submit}
			width={620}
			footer={(Btn) => {
				return !agendaId && index < 0 ? (
					Btn
				) : (
					<div className="flex align-center justify-between">
						<Popconfirm
							title="提示"
							description="是否确认删除该议程"
							icon={
								<QuestionCircleOutlined
									style={{
										color: 'red',
									}}
								/>
							}
							onConfirm={onDel}
						>
							<Space size={8} className="flex align-center padding-0 height-22 color-165dff cursor-pointer">
								<DeleteOutlined />
								刪除议程
							</Space>
						</Popconfirm>

						<Space size={12}>{Btn}</Space>
					</div>
				);
			}}
		>
			<div className="padding-12 max-height-320 overflowY-auto scrollbar">
				<Form
					labelCol={{ span: 4 }}
					wrapperCol={{ span: 20 }}
					form={form}
					autoComplete="off"
					initialValues={{
						agendaTheme: null,
						timeList: [],
						eventAgendaGuestsList: [],
					}}
				>
					<Form.Item
						name="timeList"
						label="议程时间"
						rules={[
							{
								type: 'array',
								required: true,
								message: '请选择议程时间',
							},
						]}
					>
						<TimePicker.RangePicker format="HH:mm" changeOnBlur />
					</Form.Item>
					<Form.Item
						name="agendaTheme"
						label="主题"
						rules={[
							{
								required: true,
								message: '请输入主题',
							},
						]}
					>
						<Input placeholder="请输入主题" />
					</Form.Item>
					<Form.Item label="分享嘉宾">
						<Form.List name="eventAgendaGuestsList">
							{(fields, { add, remove }) => (
								<>
									{fields.map(({ key, name, ...restField }) => (
										<Space
											key={key}
											style={{
												display: 'flex',
												marginBottom: 8,
											}}
											align="baseline"
										>
											<Form.Item noStyle {...restField} name={[name, 'name']}>
												<Input placeholder="输入嘉宾名字" />
											</Form.Item>
											<Form.Item noStyle {...restField} name={[name, 'enterpriseName']}>
												<Input placeholder="所在企业/机构" />
											</Form.Item>
											<Form.Item noStyle {...restField} name={[name, 'positionTitle']}>
												<Input placeholder="嘉宾Title/职位" />
											</Form.Item>
											<MinusCircleOutlined onClick={() => remove(name)} />
										</Space>
									))}
									<Form.Item>
										<Button ghost type="primary" onClick={() => add()} icon={<PlusOutlined />}>
											添加嘉宾
										</Button>
									</Form.Item>
								</>
							)}
						</Form.List>
					</Form.Item>
				</Form>
			</div>
		</Modal>
	);
});

export default Index;
