import { useEffect, useState } from 'react';
import { useRouterLink } from '@/hook/useRouter';

import { Button, Descriptions, Affix, Anchor, Image, message, Modal, Space } from 'antd';

import { workPostSubmissionDetail, workPostSubmissionEdit } from '@/api/Achv/Employment/Delivery';

import { submissionStatusTextList, contactStatusTextList } from '@/pages/Achv/config';

const Index = () => {
	const { linkTo, searchParams, openNewTab } = useRouterLink();
	const id = searchParams.get('id') || '';
	const fromList = !!searchParams.get('fromList');

	const [detail, setDetail] = useState({});

	// 跳转地址
	const linkToPath = '/newAchv/employment/delivery/list';

	// 获取详情
	const getDetail = () => {
		if (id) {
			workPostSubmissionDetail({ id }).then((res) => {
				const resData = res.data || {};

				setDetail(resData);
			});
		}
	};

	// 撤销审核
	const onHanlelContactStatus = (contactStatus) => {
		Modal.confirm({
			title: contactStatus === 2 ? '标记为已联系' : '撤销已联系状态',
			content: contactStatus === 2 ? '是否标记为已联系状态' : '是否撤销已联系状态？',
			onOk() {
				workPostSubmissionEdit({ id, contactStatus }).then(() => {
					message.success('操作成功');

					setTimeout(() => {
						linkTo(-1);
					}, 500);
				});
			},
		});
	};

	useEffect(() => {
		getDetail();
	}, []);

	const [modalInfo, setModalInfo] = useState({});

	return (
		<div className="">
			<div className="flex flex-shrink align-center margin-bottom-4 height-48 font-size-22 font-weight-500 color-1d2129">
				<Space size={10}>
					<div className="a color-86909c" onClick={() => linkTo(fromList ? -1 : linkToPath)}>
						投递管理
					</div>
					<div className="color-86909c">/</div>
					<div>投递详情</div>
				</Space>
			</div>
			<div id="section1"></div>
			{/* Tabs & 功能按钮 开始 */}
			<Affix>
				<div className="height-66 flex align-center justify-between bg-color-ffffff border-radius-8 padding-lr-24 border-solid-bottom-f2f3f5">
					<div className="flex align-center justify-between color-1d2129 line-height-26 font-size-18 font-weight-500">
						<Anchor
							affix={false}
							rootClassName="custom-anchor-box"
							direction="horizontal"
							replace
							targetOffset={80}
							items={[
								{
									key: 'section1',
									href: '#section1',
									title: <div className="margin-right-40 font-size-16 font-weight-500">投递信息</div>,
								},
								{
									key: 'section2',
									href: '#section2',
									title: <div className="margin-right-40 font-size-16 font-weight-500">简历信息</div>,
								},
								{
									key: 'section3',
									href: '#section3',
									title: <div className="margin-right-40 font-size-16 font-weight-500">推荐人信息</div>,
								},
							]}
						/>
					</div>
					<div className="flex align-center justify-between color-1d2129 line-height-26 font-size-18 font-weight-500">
						{detail.submissionStatus === 2 && detail.contactStatus === 1 && (
							<Button type="primary" ghost onClick={() => onHanlelContactStatus(2)} className="height-40 width-104">
								修改联系状态
							</Button>
						)}
						{detail.submissionStatus === 2 && detail.contactStatus === 2 && (
							<Button type="default" onClick={() => onHanlelContactStatus(1)} className="height-40 width-104">
								撤销联系状态
							</Button>
						)}
					</div>
				</div>
			</Affix>
			{/* Tabs & 功能按钮 结束 */}

			{/* 投递信息 开始 */}
			<div className="margin-top-20 padding-20 border-radius-8 bg-color-ffffff">
				<div className="flex align-center justify-between margin-bottom-20">
					<div className="font-size-18 line-height-26 font-weight-500">投递信息</div>
				</div>
				<div className="flex align-center justify-start font-size-14 line-height-22 padding-left-16 margin-bottom-20">
					<div className="color-86909c margin-right-4">投递人：</div>
					<div className="">{detail.workResumeVo?.name || detail.submitterName || '--'}</div>
				</div>
				<div className="flex align-center justify-start font-size-14 line-height-22 padding-left-16 margin-bottom-20">
					<div className="color-86909c margin-right-4">投递岗位：</div>
					<div className="flex">
						<div
							className={detail.postId ? 'a color-165dff' : ''}
							onClick={() => detail.postId && linkTo(`/newAchv/employment/post/detail?id=${detail.postId}`)}
						>
							{detail.postName || '--'}
						</div>
					</div>
				</div>
				<div className="flex align-center justify-start font-size-14 line-height-22 padding-left-16 margin-bottom-20">
					<div className="color-86909c margin-right-4">岗位来源：</div>
					<div className="">
						{detail.releaseType === 2 ? detail.ttChannelsList[0] && detail.ttChannelsList[0].accountName : detail.sourceUnitName || '--'}
					</div>
				</div>
				<div className="flex align-center justify-start font-size-14 line-height-22 padding-left-16 margin-bottom-20">
					<div className="color-86909c margin-right-4">联系状态：</div>
					<div className="">
						<div className={`tag-status-small-${['default', 'warning', 'primary'][detail.submissionStatus]}  `}>
							{submissionStatusTextList[detail.submissionStatus || ''] || '--'}
						</div>
					</div>
				</div>
				<div className="flex align-center justify-start font-size-14 line-height-22 padding-left-16 margin-bottom-20">
					<div className="color-86909c margin-right-4">投递状态：</div>
					<div className="">
						<div className={`tag-status-small-${['default', 'warning', 'primary'][detail.contactStatus]}  `}>
							{contactStatusTextList[detail.contactStatus || ''] || '--'}
						</div>
					</div>
				</div>
				<div className="flex align-center justify-start font-size-14 line-height-22 padding-left-16 margin-bottom-20">
					<div className="color-86909c margin-right-4">投递时间：</div>
					<div className="">{(detail.createTime || '--').slice(0, 16)}</div>
				</div>
			</div>
			{/* 投递信息 结束 */}

			{/* 简历信息 开始 */}
			<div id="section2"></div>
			<div className="bg-color-ffffff border-radius-8 padding-lr-24 padding-tb-20 margin-tb-16">
				<div className="flex align-center justify-between margin-bottom-20">
					<div className="font-size-18 line-height-26 font-weight-500">简历信息</div>
				</div>
				<div className="flex align-center justify-start font-size-14 line-height-22 padding-left-16 margin-bottom-20">
					<div className="color-86909c margin-right-4">姓名：</div>
					<div className="">{detail.workResumeVo?.name || '--'}</div>
				</div>
				<div className="flex align-center justify-start font-size-14 line-height-22 padding-left-16 margin-bottom-20">
					<div className="color-86909c margin-right-4">性别：</div>
					<div className="">{['--', '男', '女'][detail.workResumeVo?.gender]}</div>
				</div>
				<div className="flex align-center justify-start font-size-14 line-height-22 padding-left-16 margin-bottom-20">
					<div className="color-86909c margin-right-4">出生年月：</div>
					<div className="">{detail.workResumeVo?.birthDay || '--'}</div>
				</div>
				<div className="flex align-center justify-start font-size-14 line-height-22 padding-left-16 margin-bottom-20">
					<div className="color-86909c margin-right-4">联系电话：</div>
					<div className="">{detail.workResumeVo?.contactPhone || '--'}</div>
				</div>
				<div className="flex align-center justify-start font-size-14 line-height-22 padding-left-16 margin-bottom-20">
					<div className="color-86909c margin-right-4">最⾼学历：</div>
					<div className="">{['博士', '硕士', '本科', '专科', '中专及以下'][detail.workResumeVo?.degree || 0]}</div>
				</div>
				<div className="flex align-center justify-start font-size-14 line-height-22 padding-left-16 margin-bottom-20">
					<div className="color-86909c margin-right-4">毕业学校：</div>
					<div className="">{detail.workResumeVo?.graduationSchool || '--'}</div>
				</div>
				<div className="flex align-center justify-start font-size-14 line-height-22 padding-left-16 margin-bottom-20">
					<div className="color-86909c margin-right-4">毕业年份：</div>
					<div className="">{detail.workResumeVo?.graduationYear || '--'}年</div>
				</div>
				<div className="flex align-center justify-start font-size-14 line-height-22 padding-left-16 margin-bottom-20">
					<div className="color-86909c margin-right-4">附件简历：</div>
					<div className="">
						{(detail?.workResumeVo?.attachmentUrlList || []).map((ov, oi) => {
							return (
								<div
									key={oi}
									className="a color-165dff"
									onClick={() => {
										openNewTab(ov.url);
									}}
								>
									{ov.fileName}
								</div>
							);
						})}
					</div>
				</div>
			</div>
			{/* 简历信息 开始 */}

			{/* 推荐人信息 开始 */}
			<div id="section3"></div>
			<div className="bg-color-ffffff border-radius-8 padding-lr-24 padding-tb-20 margin-tb-16">
				<div className="flex align-center justify-between margin-bottom-20">
					<div className="font-size-18 line-height-26 font-weight-500">推荐人信息</div>
				</div>
				{(detail.referenceList || []).map((ov, oi) => {
					return (
						<div key={oi} className="flex align-center justify-start font-size-14 line-height-22 padding-left-16 margin-bottom-20">
							<div className="margin-right-24 width-90 text-align-right color-86909c">推荐⼈{oi + 1}：</div>
							<div className="a flex align-start justify-start flex-wrap color-165dff" onClick={() => setModalInfo(ov)}>
								{ov.referenceName}
							</div>
						</div>
					);
				})}
			</div>
			{/* 推荐人信息 结束 */}

			{/* 推荐人弹窗 开始 */}
			<Modal title="推荐人信息" open={!!modalInfo.id} onCancel={() => setModalInfo({})} onOk={() => setModalInfo({})} width={600} centered>
				<Descriptions
					className="margin-top-20"
					column={1}
					items={[
						{
							label: '推荐人',
							children: (
								<div className="flex align-center">
									<div>{modalInfo.referenceName}</div>
									{modalInfo.ttChannelsIsAuth && (
										<img
											className="margin-left-8"
											height={20}
											src={`https://gbac-produce.oss-cn-shenzhen.aliyuncs.com/static-assets/gbac-achv-applet/ttChannels/channel-title-${modalInfo.ttChannelsType}.png`}
										/>
									)}
								</div>
							),
						},
						{
							label: '联系电话',
							children: modalInfo.referencePhone,
						},
						{
							label: '任职单位',
							children: modalInfo.referenceUnit,
						},
						{
							label: '工作职务',
							children: modalInfo.referenceDuty,
						},
						{
							label: '推荐理由',
							children: modalInfo.referenceReason || '--',
						},
						{
							label: '附件',
							children: modalInfo.referenceAttachment ? <Image width={100} src={modalInfo.referenceAttachment} /> : '--',
						},
					]}
				/>
			</Modal>
			{/* 推荐人弹窗 结束 */}
		</div>
	);
};

export default Index;
