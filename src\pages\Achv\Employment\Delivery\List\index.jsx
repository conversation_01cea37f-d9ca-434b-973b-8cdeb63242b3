import { useEffect, useState } from 'react';
import { Table, Button, Popconfirm, Space, Affix, Form, Select, Input, Row, Col } from 'antd';
import { useTableData } from '@/hook/useTableData';
import { useRouterLink } from '@/hook/useRouter';
import {
	workPostSubmissionPage as getTablePageData,
	workPostSubmissionBatchDel as delTableItemData,
	workPostSubmissionStatusStatistics,
} from '@/api/Achv/Employment/Delivery/index';

import { submissionStatusData, submissionStatusTextList, contactStatusData, contactStatusTextList } from '@/pages/Achv/config';

const tabBarData = [
	{
		label: '全部',
		value: null,
		countName: 'total',
	},
	{
		label: '已投递成功',
		value: 1,
		countName: 'releaseNum',
	},
	{
		label: '待助力',
		value: 2,
		countName: 'waitReleaseNum',
	},
	{
		label: '待联系',
		value: 3,
		countName: 'contactNum',
	},
	{
		label: '已联系',
		value: 4,
		countName: 'contactSuccessNum',
	},
];

const Index = () => {
	const { linkTo, searchParams } = useRouterLink();
	const linkToPath = '/newAchv/employment/delivery';

	const [tabBarStatus, setTabBarStatus] = useState(null);
	const [params, setParams] = useState({});
	const { form, dataSource, pagination, changePage, delTableData, onReset, onSearch } = useTableData({
		params: params,
		getTablePageData,
		delTableItemData,
	});

	// 获取统计数据
	const [statistics, setStatistics] = useState({});
	const getStatisticsData = () => {
		workPostSubmissionStatusStatistics({
			postId: searchParams.get('postId') || undefined,
		}).then((res) => {
			setStatistics(res.data || {});
		});
	};

	useEffect(() => {
		getStatisticsData();
	}, []);

	useEffect(() => {
		const params = {
			postId: searchParams.get('postId') || undefined,
		};
		if (tabBarStatus === 1 || tabBarStatus === 2) {
			params.submissionStatus = tabBarStatus === 2 ? 1 : 2;
		} else if (tabBarStatus === 3 || tabBarStatus === 4) {
			params.contactStatus = tabBarStatus === 3 ? 1 : 2;
		} else {
			form.resetFields();
		}
		setParams(params);
	}, [tabBarStatus]);

	return (
		<div className="">
			<div className="flex flex-shrink align-center margin-bottom-4 height-48 font-size-22 font-weight-500 color-1d2129">投递管理</div>

			{/* Tabs & 功能按钮 开始 */}
			<Affix offsetTop={0}>
				<div className="flex align-center justify-between padding-24 border-radius-8 bg-color-ffffff border-solid-bottom-f2f3f5">
					<div className="flex align-center">
						{tabBarData.map((ov) => (
							<div
								key={ov.value}
								className={`a margin-right-40 font-size-18 font-weight-500 ${tabBarStatus === ov.value ? 'color-165dff' : ''}`}
								onClick={() => setTabBarStatus(ov.value)}
							>
								{ov.label}
								{statistics[ov.countName] > 0 ? `（${statistics[ov.countName]}）` : ''}
							</div>
						))}
					</div>
					<Space size={16}></Space>
				</div>
			</Affix>
			{/* Tabs & 功能按钮 结束 */}

			<div className="flex-sub margin-top-18 padding-24 border-radius-8 bg-color-ffffff">
				{/* 筛选条件 开始 */}
				<div className="flex justify-between margin-bottom-18">
					<div className="flex-sub">
						<Form form={form} labelAlign="right" layout="inline">
							<Row className="width-100per" gutter={[12, 12]}>
								<Col span={8}>
									<Form.Item name="submitterName" label="人员名称">
										<Input placeholder="请输入人员名称" />
									</Form.Item>
								</Col>

								{tabBarStatus !== 1 && tabBarStatus !== 2 && (
									<Col span={8}>
										<Form.Item name="submissionStatus" label="投递状态">
											<Select options={submissionStatusData} placeholder="请选择投递状态" allowClear />
										</Form.Item>
									</Col>
								)}
								{tabBarStatus !== 3 && tabBarStatus !== 4 && (
									<Col span={8}>
										<Form.Item name="contactStatus" label="联系状态">
											<Select options={contactStatusData} placeholder="请选择联系状态" allowClear />
										</Form.Item>
									</Col>
								)}
							</Row>
						</Form>
					</div>
					<div className="margin-left-12 padding-left-12 border-solid-left-e5e6eb">
						<Space size={16}>
							<Button onClick={onReset}>重置</Button>
							<Button type="primary" onClick={onSearch}>
								查询
							</Button>
						</Space>
					</div>
				</div>
				{/* 筛选条件 结束 */}

				{/* 表格 开始 */}
				<Table rowKey="id" dataSource={dataSource} pagination={pagination} onChange={changePage} scroll={{ x: 'max-content' }}>
					<Table.Column
						title="序号"
						key="index"
						width={60}
						align="center"
						render={(_, __, index) => {
							return index + 1;
						}}
					/>
					<Table.Column title="姓名" dataIndex="submitterName" render={(text) => <div className="">{text}</div>} />
					<Table.Column title="投递岗位" dataIndex="postName" render={(text) => <div className="">{text}</div>} />
					<Table.Column
						title="投递状态"
						dataIndex="submissionStatus"
						align="center"
						render={(text) => {
							return (
								<div className={`tag-status-small-${['default', 'warning', 'primary'][text]}  `}>
									{submissionStatusTextList[text || ''] || '--'}
								</div>
							);
						}}
					/>
					<Table.Column
						title="联系状态"
						dataIndex="contactStatus"
						align="center"
						render={(text) => {
							return (
								<div className={`tag-status-small-${['default', 'warning', 'primary'][text]}  `}>
									{contactStatusTextList[text || ''] || '--'}
								</div>
							);
						}}
					/>
					<Table.Column
						title="已助力/助力要求"
						align="center"
						dataIndex="submissionNum"
						render={(_, { submissionNum, helpNum, id }) => {
							return submissionNum ? (
								<div
									className="cursor-pointer"
									onClick={() => {
										linkTo(`${linkToPath}/detail?id=${id}`);
									}}
								>
									<span className="color-165dff">{helpNum || 0}</span>
									<span>/</span>
									<span>{submissionNum || 0}</span>
								</div>
							) : (
								'无需助力'
							);
						}}
					/>
					<Table.Column
						title="投递时间"
						dataIndex="createTime"
						align="center"
						render={(text) => {
							return <div>{(text || '--').slice(0, 16)}</div>;
						}}
					/>
					<Table.Column
						title="操作"
						key="option"
						dataIndex="option"
						align="center"
						fixed="right"
						width="160px"
						render={(_, record) => {
							return (
								<>
									<Button type="link" size="small" onClick={() => linkTo(`${linkToPath}/detail?id=${record.id}&fromList=1`)}>
										详情/编辑
									</Button>
									<Popconfirm
										title="提示"
										description="确定删除吗？"
										onConfirm={() => {
											delTableData(record.id);
										}}
										okText="确定"
										cancelText="取消"
									>
										<Button type="link" size="small">
											删除
										</Button>
									</Popconfirm>
								</>
							);
						}}
					/>
				</Table>
				{/* 表格 结束 */}
			</div>
		</div>
	);
};

export default Index;
