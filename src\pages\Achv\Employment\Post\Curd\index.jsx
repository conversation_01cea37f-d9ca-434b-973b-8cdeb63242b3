import { useEffect, useState } from 'react';
import { useRouterLink } from '@/hook/useRouter';
import { Button, Space, Form, Input, message, Affix, Radio, Checkbox, Select, Cascader } from 'antd';

import UploadImg from '@/components/UploadImg';
import SelectTTChannels from '@/components/Achv/SelectTTChannels';
import { addPost, updatePost, detailPost } from '@/api/Achv/Employment/Post/index';
import { getThreeLevelData } from '@/api/common';

import { releaseTypeData, degreeData } from '@/pages/Achv/config';

const experienceData = new Array(20).fill(0).map((_, oi) => {
	return {
		label: `${oi + 1}年`,
		value: oi + 1,
	};
});

const salaryData = new Array(98).fill(0).map((_, oi) => {
	return {
		label: `${oi + 3}K`,
		value: oi + 3,
	};
});

const Index = () => {
	const { linkTo, searchParams } = useRouterLink();
	const id = searchParams.get('id') || '';
	const [form] = Form.useForm();
	const releaseType = Form.useWatch('releaseType', form);
	const minimumYears = Form.useWatch('minimumYears', form);
	const maximumYears = Form.useWatch('maximumYears', form);
	const minimumMonthlySalary = Form.useWatch('minimumMonthlySalary', form);
	const highestMonthlySalary = Form.useWatch('highestMonthlySalary', form);

	// 跳转地址
	const linkToPath = `/newAchv/employment/post`;

	// 禁止编辑发布类型
	const [hash, setHash] = useState('');

	// 取消
	const onCancel = () => {
		linkTo(-1);
	};

	// 提交
	const onSubmit = () => {
		form.validateFields()
			.then((values) => {
				const params = { ...values };

				delete params.tempArea;
				(values.id ? updatePost : addPost)({
					...params,
					ttChannelsIds: params.ttChannelsIds ? params.ttChannelsIds.map((ov) => ov.id) : undefined,
					excavatePersonnelTtChannelIds: params.excavatePersonnelTtChannelIds
						? params.excavatePersonnelTtChannelIds.map((ov) => ov.id)
						: undefined,
				}).then(() => {
					message.success(values.id ? '修改成功' : '添加成功');
					setTimeout(() => {
						linkTo(-1);
					}, 500);
				});
			})
			.catch((error) => {
				console.log(error);
				const errorName = error.errorFields[0].name[0];
				if (['ttChannelsIds'].includes(errorName)) {
					setHash('section2');
				} else {
					setHash('section1');
				}
			});
	};

	// 获取详情
	const getDetail = () => {
		if (id) {
			detailPost({ id }).then((res) => {
				const resData = res.data || {};

				// 处理区域
				resData.tempArea = [resData.provinceCode, resData.cityCode];
				// 处理 关联科转号
				resData.ttChannelsIds = resData.ttChannelsList || [];
				// 处理 关联科转号
				resData.excavatePersonnelTtChannelIds = resData.excavatePersonnelTtChannelsList || [];
				form.setFieldsValue(resData);
			});
		}
	};

	useEffect(() => {
		const hash = window.location.hash.replace('#', '');
		setHash(hash || 'section1');

		getDetail();
	}, []);

	// 跳转到指定锚点
	useEffect(() => {
		if (hash) {
			setTimeout(() => {
				const element = document.getElementById(hash);
				if (element) {
					element.scrollIntoView({ behavior: 'smooth' });
				}
			}, 500);
		}
	}, [hash]);

	const [areaOptions, setAreaOptions] = useState([]);

	useEffect(() => {
		getThreeLevelData({ level: 2 }).then((res) => {
			setAreaOptions(res.data || []);
		});
	}, []);

	return (
		<div className="">
			<div className="flex flex-shrink align-center margin-bottom-4 height-48 font-size-22 font-weight-500 color-1d2129">
				<Space size={10}>
					<div className="a color-86909c" onClick={() => linkTo(linkToPath)}>
						岗位管理
					</div>
					<div className="color-86909c">/</div>
					<div>{id ? '岗位编辑' : '新增岗位'}</div>
				</Space>
			</div>

			{/* Tabs & 功能按钮 开始 */}
			<div id="section1"></div>
			<Affix offsetTop={0}>
				<div className="flex align-center justify-between padding-24 border-radius-8 border-solid-bottom-f2f2f2 bg-color-ffffff">
					<div className="flex align-center">
						{['基本信息', '相关参与人'].map((ov, oi) => {
							const currentHash = `section${oi + 1}`;
							return (
								<div
									key={oi}
									className={`a margin-right-40 font-size-18 font-weight-500 ${hash === currentHash ? 'color-165dff' : ''}`}
									onClick={() => {
										if (currentHash === 'section2') {
											form.validateFields()
												.then(() => {
													setHash(`section${oi + 1}`);
												})
												.catch((error) => {
													console.log(error);
													const errorName = error.errorFields[0].name[0];
													if (['ttChannelsIds'].includes(errorName)) {
														setHash('section2');
													} else {
														setHash('section1');
													}
												});
										} else {
											setHash(`section${oi + 1}`);
										}
									}}
								>
									{ov}
								</div>
							);
						})}
					</div>
					<Space size={16}>
						<Button onClick={onCancel}>取消</Button>
						<Button type="primary" onClick={onSubmit}>
							保存
						</Button>
					</Space>
				</div>
			</Affix>
			{/* Tabs & 功能按钮 结束 */}

			<Form
				form={form}
				className="antd-form-box"
				labelAlign="right"
				labelCol={{ style: { width: '120px' } }}
				initialValues={{
					minimumYears: null,
					maximumYears: null,
					minimumMonthlySalary: null,
					highestMonthlySalary: null,
					annualSalary: 12,
					releaseType: 1,
					releasePlatform: 1,
					ttChannelsIds: [],
					excavatePersonnelIds: [],
				}}
			>
				<Form.Item hidden name="id">
					<Input />
				</Form.Item>
				<Form.Item hidden name="releasePlatform">
					<Input />
				</Form.Item>
				<Form.Item hidden name="rankingNum">
					<Input />
				</Form.Item>
				<div style={{ display: hash === 'section1' ? 'block' : 'none' }}>
					{/* 基本信息 开始 */}
					<div className="margin-top-20 padding-20 border-radius-8 bg-color-ffffff">
						<div className="padding-bottom-30 line-height-26 font-size-18 font-weight-500 color-1d2129">基本信息</div>
						<Form.Item
							label="岗位名称"
							name="postName"
							rules={[
								{ required: true, message: '请输入岗位名称' },
								{ max: 12, message: '最多12个字' },
							]}
						>
							<Input className="input-box" placeholder="请输入岗位名称" />
						</Form.Item>
						<Form.Item label="经验要求" required>
							<Space>
								<Form.Item name="minimumYears" noStyle rules={[{ required: !!maximumYears, message: '请选择最低年限' }]}>
									<Select
										style={{ width: '172px' }}
										options={experienceData.filter((ov) => !maximumYears || ov.value <= maximumYears)}
										placeholder="最低年限"
										allowClear
									/>
								</Form.Item>
								至
								<Form.Item name="maximumYears" noStyle>
									<Select
										style={{ width: '172px' }}
										options={experienceData.filter((ov) => !minimumYears || ov.value >= minimumYears)}
										placeholder="最高年限"
										allowClear
									/>
								</Form.Item>
								<Checkbox
									checked={minimumYears === null && maximumYears === null}
									onChange={(e) => {
										if (e.target.checked) {
											form.setFieldValue('minimumYears', null);
											form.setFieldValue('maximumYears', null);
										} else {
											message.error('请选择经验要求');
										}
									}}
								>
									不限
								</Checkbox>
							</Space>
						</Form.Item>
						<Form.Item label="学历要求" name="degree" rules={[{ required: true, message: '请选择学历要求' }]}>
							<Select className="input-number-box" placeholder="请选择学历要求" options={degreeData} />
						</Form.Item>
						<Form.Item label="薪酬范围" required>
							<Space direction="vertical">
								<Space>
									<Form.Item
										name="minimumMonthlySalary"
										noStyle
										rules={[{ required: !!highestMonthlySalary, message: '请选择最低月薪' }]}
									>
										<Select
											style={{ width: '172px' }}
											options={salaryData.filter((ov) => !highestMonthlySalary || ov.value <= highestMonthlySalary)}
											placeholder="最低月薪"
											allowClear
										/>
									</Form.Item>
									至
									<Form.Item name="highestMonthlySalary" noStyle>
										<Select
											style={{ width: '172px' }}
											options={salaryData.filter((ov) => !minimumMonthlySalary || ov.value >= minimumMonthlySalary)}
											placeholder="最高月薪"
											allowClear
										/>
									</Form.Item>
									<Checkbox
										checked={minimumMonthlySalary === null && highestMonthlySalary === null}
										onChange={(e) => {
											if (e.target.checked) {
												form.setFieldValue('minimumMonthlySalary', null);
												form.setFieldValue('highestMonthlySalary', null);
											} else {
												message.error('请选择薪酬范围');
											}
										}}
									>
										面议
									</Checkbox>
								</Space>
								{minimumMonthlySalary && (
									<Form.Item name="annualSalary" noStyle>
										<Select
											style={{ width: '172px' }}
											options={new Array(9).fill(0).map((_, index) => {
												return {
													label: `${index + 12}薪`,
													value: index + 12,
												};
											})}
										/>
									</Form.Item>
								)}
							</Space>
						</Form.Item>
						<Form.Item hidden name="provinceCode">
							<Input />
						</Form.Item>
						<Form.Item hidden name="cityCode">
							<Input />
						</Form.Item>
						<Form.Item label="工作城市" name="tempArea" rules={[{ required: true, type: 'array', message: '请选择工作城市' }]}>
							<Cascader
								className="cascader-box"
								options={areaOptions}
								placeholder="请选择工作城市"
								displayRender={(label) => label.filter((ov) => ov).join('-')}
								onChange={(e = [undefined, undefined]) => {
									form.setFieldValue('provinceCode', e[0]);
									form.setFieldValue('cityCode', e[1]);
								}}
							/>
						</Form.Item>
						<Form.Item label="推荐人数要求" name="recommendNumberPeople" required initialValue={1}>
							<Select
								className="input-number-box"
								placeholder="请选择推荐人数要求"
								options={[
									{ label: '无需推荐', value: 0 },
									{ label: '1人', value: 1 },
									{ label: '2人', value: 2 },
									{ label: '3人', value: 3 },
									{ label: '4人', value: 4 },
								]}
							/>
						</Form.Item>
						<Form.Item label="岗位职责" name="postDuty" rules={[{ required: true, message: '请输入岗位职责' }]}>
							<Input.TextArea className="input-box" rows={4} placeholder="请输入岗位职责" />
						</Form.Item>
						<Form.Item label="岗位要求" name="postRequire" rules={[{ required: true, message: '请输入岗位要求' }]}>
							<Input.TextArea className="input-box" rows={4} placeholder="请输入岗位要求" />
						</Form.Item>
						<Form.Item label="发布类型" name="releaseType" required>
							<Radio.Group options={releaseTypeData.filter((ov) => ov.value < 3)} />
						</Form.Item>
						{releaseType === 1 && (
							<>
								<Form.Item label="来源单位名称" name="sourceUnitName" rules={[{ required: true, message: '请输入来源单位名称' }]}>
									<Input className="input-box" placeholder="请输入来源单位名称" />
								</Form.Item>
								<Form.Item
									label="来源单位头像"
									name="sourceUnitAvatarUrl"
									rules={[{ required: true, message: '请上传来源单位头像' }]}
								>
									<UploadImg
										size={5}
										width={150}
										height={150}
										tips={'建议尺寸：150*150px'}
										cropperProps={{ width: 150, height: 150 }}
									/>
								</Form.Item>
							</>
						)}
						<Form.Item label="联系人" name="postContactName">
							<Input className="input-box" placeholder="请输入联系人" />
						</Form.Item>
						<Form.Item label="联系电话" name="postContactPhone">
							<Input className="input-box" placeholder="请输入联系电话" />
						</Form.Item>
					</div>
					{/* 基本信息 结束 */}
				</div>

				{/* 相关参与人 开始 */}
				<div style={{ display: hash === 'section2' ? 'block' : 'none' }}>
					<div id="section2"></div>
					{releaseType === 2 && (
						<SelectTTChannels
							form={form}
							rules={[{ required: releaseType === 2, type: 'array', message: '请选择关联科转号' }]}
							name="ttChannelsIds"
							label="关联科转号"
							collectDelIds={true}
						/>
					)}

					<SelectTTChannels form={form} name="excavatePersonnelTtChannelIds" label="岗位挖掘人" />
				</div>
				{/* 相关参与人 结束 */}
			</Form>
		</div>
	);
};
export default Index;
