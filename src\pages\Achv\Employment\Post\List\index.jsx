import { useState, useEffect } from 'react';
import { Table, Button, Popconfirm, Space, Switch, Affix, message, Form, Cascader, Input, Row, Col, Tooltip } from 'antd';
import { QuestionCircleOutlined } from '@ant-design/icons';
import { useTableData } from '@/hook/useTableData';
import { useRouterLink } from '@/hook/useRouter';
import {
	pagePost as getTablePageData,
	batchDelPost as delTableItemData,
	updateRankingNum as batchUpdateSort,
	updateShowStatus,
	updateRecommendStatus,
	getAuditDataStatistics,
} from '@/api/Achv/Employment/Post/index';
import { getThreeLevelData } from '@/api/common';

import { auditStatusData, auditStatusTextList, degreeTextList } from '@/pages/Achv/config';

const Index = () => {
	const { linkTo, searchParams } = useRouterLink();
	const linkToPath = '/newAchv/employment/post';

	const status = searchParams.get('auditStatus');
	const [auditStatus, setAuditStatus] = useState(status === null ? 3 : status - 0 || '');

	const { form, dataSource, pagination, changePage, delTableData, getTableData, onReset, onSearch, SortInput } = useTableData({
		params: { auditStatus },
		getTablePageData,
		delTableItemData,
		batchUpdateSort,
		pageCallback: (paramsData = {}) => {
			getStatisticsData({ ...paramsData });
		},
	});

	// 获取统计数据
	const [statistics, setStatistics] = useState({});
	const getStatisticsData = (paramsData) => {
		delete paramsData.auditStatus;
		delete paramsData.pageSize;
		delete paramsData.pageNum;
		delete paramsData.tempArea;
		getAuditDataStatistics(paramsData).then((res) => {
			setStatistics(res.data || {});
		});
	};

	// 修改显示状态
	const showStatusChange = (record) => {
		updateShowStatus({ id: record.id, showStatus: record.showStatus == 1 ? 0 : 1 }).then(() => {
			message.success('操作成功');
			getTableData();
		});
	};

	// 修改推荐状态
	const recommendStatusChange = (record) => {
		updateRecommendStatus({ id: record.id, recommendStatus: record.recommendStatus == 1 ? 0 : 1 }).then(() => {
			message.success('操作成功');
			getTableData();
		});
	};

	// 获取选项数据
	const [areaOptions, setAreaOptions] = useState([]);
	const getOptionsData = () => {
		getThreeLevelData({ level: 2 }).then((res) => {
			setAreaOptions(res.data || []);
		});
	};

	useEffect(() => {
		getOptionsData();
	}, []);

	return (
		<div className="">
			<div className="flex flex-shrink align-center margin-bottom-4 height-48 font-size-22 font-weight-500 color-1d2129">岗位管理</div>

			{/* Tabs & 功能按钮 开始 */}
			<Affix offsetTop={0}>
				<div className="flex align-center justify-between padding-24 border-radius-8 bg-color-ffffff border-solid-bottom-f2f3f5">
					<div className="flex align-center">
						{auditStatusData.map((ov) => (
							<div
								key={ov.value}
								className={`a margin-right-40 font-size-18 font-weight-500 ${auditStatus === ov.value ? 'color-165dff' : ''}`}
								onClick={() => setAuditStatus(ov.value)}
							>
								{ov.label}
								{statistics[ov.countName] > 0 ? `（${statistics[ov.countName]}）` : ''}
							</div>
						))}
					</div>
					<Space size={16}>
						<Button
							type="primary"
							onClick={() => {
								linkTo(`${linkToPath}/curd`);
							}}
						>
							创建岗位
						</Button>
					</Space>
				</div>
			</Affix>
			{/* Tabs & 功能按钮 结束 */}

			<div className="flex-sub margin-top-18 padding-24 border-radius-8 bg-color-ffffff">
				{/* 筛选条件 开始 */}
				<div className="flex justify-between margin-bottom-18">
					<div className="flex-sub">
						<Form form={form} labelAlign="right" layout="inline">
							<Row className="width-100per" gutter={[12, 12]}>
								<Col span={8}>
									<Form.Item name="postName" label="岗位名称">
										<Input placeholder="请输入岗位名称" />
									</Form.Item>
								</Col>
								<Col span={8}>
									<Form.Item hidden name="provinceCodes">
										<Input />
									</Form.Item>
									<Form.Item hidden name="cityCodes">
										<Input />
									</Form.Item>
									<Form.Item name="tempArea" label="城市" initialValue={[]}>
										<Cascader
											options={areaOptions}
											placeholder="请输入城市"
											displayRender={(label) => label.join('-')}
											onChange={(e = [undefined, undefined]) => {
												form.setFieldValue('provinceCodes', e[0] ? [e[0]] : undefined);
												form.setFieldValue('cityCodes', e[1] ? [e[1]] : undefined);
											}}
										/>
									</Form.Item>
								</Col>
							</Row>
						</Form>
					</div>
					<div className="margin-left-12 padding-left-12 border-solid-left-e5e6eb">
						<Space size={16}>
							<Button onClick={onReset}>重置</Button>
							<Button type="primary" onClick={onSearch}>
								查询
							</Button>
						</Space>
					</div>
				</div>
				{/* 筛选条件 结束 */}

				{/* 表格 开始 */}
				<Table rowKey="id" dataSource={dataSource} pagination={pagination} onChange={changePage} scroll={{ x: 'max-content' }}>
					<Table.Column
						title="序号"
						key="index"
						width={60}
						align="center"
						render={(_, __, index) => {
							return index + 1;
						}}
					/>
					<Table.Column title="岗位名称" dataIndex="postName" render={(text) => <div className="">{text}</div>} />
					<Table.Column
						title="发布单位"
						dataIndex="sourceUnitName"
						render={(_, { releaseType, ttChannelsList, sourceUnitName }) => (
							<div className="">{releaseType === 2 ? ttChannelsList[0] && ttChannelsList[0].accountName : sourceUnitName || '--'}</div>
						)}
					/>
					<Table.Column
						title="发布状态"
						dataIndex="auditStatus"
						align="center"
						render={(text) => {
							return (
								<div className={`tag-status-small-${['default', 'warning', 'error', 'primary'][text]}  `}>
									{auditStatusTextList[text || ''] || '--'}
								</div>
							);
						}}
					/>

					<Table.Column
						title="经验要求"
						dataIndex="minimumYears"
						align="center"
						render={(_, { minimumYears, maximumYears }) => (
							<div className="">
								{minimumYears && maximumYears
									? `${minimumYears}年至${maximumYears}年`
									: minimumYears
									? `${minimumYears}年以上`
									: '不限'}
							</div>
						)}
					/>
					<Table.Column
						title="学历要求"
						dataIndex="degree"
						align="center"
						render={(text) => <div className="">{degreeTextList[text] || '--'}</div>}
					/>
					<Table.Column
						title="城市"
						dataIndex="provinceCode"
						render={(_, record) => {
							return [record.provinceName, record.cityName, record.areaName].filter((ov) => ov).join('-');
						}}
					/>
					<Table.Column
						title="显示/隐藏"
						dataIndex="showStatus"
						align="center"
						render={(_, record) => {
							return (
								<Switch
									checked={record.showStatus == 1}
									disabled={record.auditStatus !== 3}
									title={record.auditStatus !== 3 ? '仅审核通过后才能操作' : ''}
									onChange={() => {
										showStatusChange(record);
									}}
								/>
							);
						}}
					/>
					{/* <Table.Column
						title="推荐"
						dataIndex="recommendStatus"
						align="center"
						render={(_, record) => {
							return (
								<Switch
									checked={record.recommendStatus == 1}
									disabled={record.auditStatus !== 3}
									title={record.auditStatus !== 3 ? '仅审核通过后才能操作' : ''}
									onChange={() => {
										recommendStatusChange(record);
									}}
								/>
							);
						}}
					/> */}
					<Table.Column
						title={
							<Space>
								<div>投递人数</div>
								<Tooltip title={() => <span className="color-4e5969">总数/成功</span>} color="#ffffff">
									<QuestionCircleOutlined />
								</Tooltip>
							</Space>
						}
						align="center"
						dataIndex="auditNumber"
						render={(_, record) => {
							return (
								<div
									className="cursor-pointer"
									onClick={() => {
										linkTo(`/newAchv/employment/delivery/list?postId=${record.id}`);
									}}
								>
									<span className="color-165dff">{record.deliveryTotal || 0}</span>
									<span>/</span>
									<span>{record.deliverySuccess || 0}</span>
								</div>
							);
						}}
					/>
					<Table.Column
						title="排序"
						align="center"
						dataIndex="rankingNum"
						render={(_, record) => {
							return <SortInput record={record} />;
						}}
					/>
					<Table.Column
						title="操作"
						key="option"
						dataIndex="option"
						align="center"
						fixed="right"
						width="160px"
						render={(_, record) => {
							return (
								<>
									<Button type="link" size="small" onClick={() => linkTo(`${linkToPath}/detail?id=${record.id}&fromList=1`)}>
										详情/编辑
									</Button>
									<Popconfirm
										title="提示"
										description="确定删除吗？"
										onConfirm={() => {
											delTableData(record.id);
										}}
										okText="确定"
										cancelText="取消"
									>
										<Button type="link" size="small">
											删除
										</Button>
									</Popconfirm>
								</>
							);
						}}
					/>
				</Table>
				{/* 表格 结束 */}
			</div>
		</div>
	);
};

export default Index;
