/**
 * @description Curd - 文件描述
 * <AUTHOR>
 *
 * Created on 2024/10/28 16:36
 */
import { useEffect, useState, forwardRef, useImperativeHandle, memo, useRef } from 'react';
import { Space, Form, Card, Input, DatePicker, Cascader, message, Radio, Button, Affix, Anchor, Image, Avatar, Tag, Checkbox } from 'antd';
import { getThreeLevelData } from '@/api/common';
import UploadImg from '@/components/UploadImg';
import { useRouterLink } from '@/hook/useRouter';
import CommanderModal from '../components/CommanderModal';
import JoinMembers from '../components/JoinMembers';
import UEditor from '@/components/UEditor';
import { addGroupEvent, groupEventDetail, updateAuditStatus, updateGroupEvent } from '@/api/Achv/EventManage';
import dayjs from 'dayjs';
import { EditOutlined } from '@ant-design/icons';
import ModalForm from '@/components/ModalForm';

const { RangePicker } = DatePicker;
/* 审核选项 */
const auditOptions = [
	{
		label: '审核中',
		value: 1,
	},
	{
		label: '审核不通过',
		value: 2,
	},
	{
		label: '审核通过',
		value: 3,
	},
];
const StepOne = ({ areaList, visible }) => {
	// const ruleJoinMembers = async ({getFieldValue}) => {
	//     return {
	//         validator: async (_, value) => {
	//             const joinMembersSetupList = value;
	//             if (joinMembersSetupList?.length === 0) {
	//                 return Promise.reject(new Error('请设置拼团席位'));
	//             } else {
	//                 joinMembersSetupList?.forEach(item => {
	//                     if (!item.joinNum && !error) {
	//                         return Promise.reject(new Error('请设置席位数量'));
	//                     }
	//                 });
	//             }
	//         },
	//     };
	// };
	return (
		<div>
			<Form.Item label={'拼团标题'} name={'title'} hidden={!visible} required rules={[{ required: true, message: '请输入拼团标题' }]}>
				<Input placeholder={'请输入'} />
			</Form.Item>
			<Form.Item label={'拼团时间'} name={'joinDate'} hidden={!visible} required rules={[{ required: true, message: '请选择开团时间' }]}>
				<RangePicker showTime={{ minuteStep: 30 }} format="YYYY-MM-DD HH:mm" className={'width-370'} />
			</Form.Item>

			<Form.Item label={'活动时间'} required>
				<div className="flex align-center">
					<Form.Item name={'rangeDate'} hidden={!visible} required rules={[{ required: true, message: '请选择活动时间' }]} noStyle>
						<RangePicker showTime={{ minuteStep: 30 }} format="YYYY-MM-DD HH:mm" className={'width-370'} />
					</Form.Item>
					<Form.Item name={'bePendingDesc'} hidden={!visible} noStyle valuePropName="checked">
						<Checkbox className="margin-left-20">待定</Checkbox>
					</Form.Item>
				</div>
			</Form.Item>
			<Form.Item label={'开团地址'} name={'addressCode'} hidden={!visible} required rules={[{ required: true, message: '请选择所属区域' }]}>
				<Cascader options={areaList} placeholder="请选择所属区域" style={{ width: '370px' }} />
			</Form.Item>
			<Form.Item name={'address'} hidden={!visible} wrapperCol={{ span: 16, offset: 4 }}>
				<Input placeholder={'请输入详细地址'} />
			</Form.Item>
			<Form.Item name={'coverImageUrl'} label={'拼团封面'} hidden={!visible} required rules={[{ required: true, message: '请上传拼团封面' }]}>
				<UploadImg size={5} width={225} height={126} tips={'建议尺寸：750*420px,大小不超过5M'} cropperProps={{ width: 750, height: 420 }} />
			</Form.Item>
			<Form.Item name={'commander'} label={'团长'} hidden={!visible} required rules={[{ required: true, message: '请输入团长信息' }]}>
				<CommanderModal />
			</Form.Item>
			<Form.Item name={'joinMembersSetupList'} label={'可拼团席位'} hidden={!visible}>
				<JoinMembers />
			</Form.Item>

			<Form.Item label={'分享标题'} name={'shareDesc'} required hidden={!visible} rules={[{ required: true, message: '请输入分享标题' }]}>
				<Input placeholder={'请输入'} />
			</Form.Item>
			<Form.Item name={'shareImageUrl'} label={'分享封面'} hidden={!visible}>
				<UploadImg size={5} width={200} height={160} tips={'建议尺寸：200*160px,大小不超过1M'} cropperProps={{ width: 200, height: 160 }} />
			</Form.Item>

			{/*<Form.Item name={'adminStaffs'} label={'管理人员'} hidden={!visible} required rules={[{required: true, message: '请选择审核状态'}]}>*/}
			{/*</Form.Item>*/}
		</div>
	);
};

const StepTwo = ({ visible }) => {
	return (
		<>
			<Form.Item label={'广告单位名称'} name={'adsName'} hidden={!visible}>
				<Input placeholder={'请输入'} />
			</Form.Item>
			<Form.Item name={'adsImageUrl'} label={'广告贴图'} hidden={!visible}>
				<UploadImg size={3} width={200} height={160} tips={'建议尺寸：710*140px,大小不超过3M'} cropperProps={{ width: 710, height: 140 }} />
			</Form.Item>
		</>
	);
};

const StepThree = ({ visible }) => {
	return (
		<Form.Item
			noStyle
			name={'introduce'}
			hidden={!visible}
			wrapperCol={{ span: 14, offset: 4 }}
			required
			rules={[{ required: true, message: '请输入拼团详情' }]}
		>
			<UEditor />
		</Form.Item>
	);
};

const Index = () => {
	const { linkTo, searchParams } = useRouterLink();

	const [detail, setDetail] = useState({});
	const [step, setStep] = useState(1);
	const [form] = Form.useForm();
	const [areaList, setAreaList] = useState([]);
	/* 编辑状态 */
	const [editing, setEditing] = useState(false);

	useEffect(() => {
		// 获取区域接口
		getArea();
		const searchId = searchParams.get('id');
		if (searchId) {
			getGroupEventDetail(searchId);
		} else {
			setEditing(true);
		}
	}, []);

	/* 查询拼团详情 */
	const getGroupEventDetail = async (id) => {
		const res = await groupEventDetail({ id });
		if (res.data) {
			setDetail(res.data);
			const {
				title,
				startTime,
				endTime,
				joinStartTime,
				joinEndTime,
				cityCode,
				provinceCode,
				address,
				coverImageUrl,
				groupLeaderName,
				groupLeaderLogo,
				joinMembersSetupList,
				introduce,
				shareDesc,
				shareImageUrl,
				adsName,
				adsImageUrl,
				bePendingDesc = '',
			} = res.data;
			const values = {
				title,
				address,
				coverImageUrl,
				joinMembersSetupList,
				introduce,
				bePendingDesc: !!bePendingDesc,
				rangeDate: startTime ? [dayjs(startTime), dayjs(endTime)] : [],
				joinDate: joinStartTime ? [dayjs(joinStartTime), dayjs(joinEndTime)] : [],
				addressCode: cityCode ? [provinceCode, cityCode] : [],
				commander: { name: groupLeaderName, logo: groupLeaderLogo },
				shareDesc,
				shareImageUrl,
				adsName,
				adsImageUrl,
			};
			console.log('init setFieldsValue', values);
			form.setFieldsValue(values);
			setEditing(false);
		}
	};

	// 获取区域接口
	const getArea = async () => {
		console.log('获取区域接口');
		const res = await getThreeLevelData({ level: 2 }, { showLoading: false });
		if (res.data) {
			console.log('获取区域接口', res.data);
			setAreaList(res.data);
		} else {
			message.error('获取区域失败');
		}
	};
	/* 保存提交数据 */
	const handleSubmit = async () => {
		try {
			const { rangeDate, joinDate, addressCode, commander, ...values } = await form.validateFields();
			console.log('🚀 ~ handleSubmit ~ rangeDate:', rangeDate);
			const id = detail.id;
			const params = {
				...values,
				startTime: (rangeDate && rangeDate[0] && rangeDate[0].format('YYYY-MM-DD HH:mm:ss')) || '',
				endTime: (rangeDate && rangeDate[1] && rangeDate[1].format('YYYY-MM-DD HH:mm:ss')) || '',
				joinStartTime: joinDate[0].format('YYYY-MM-DD HH:mm:ss'),
				joinEndTime: joinDate[1].format('YYYY-MM-DD HH:mm:ss'),
				provinceCode: addressCode[0],
				cityCode: addressCode[1],
				groupLeaderName: commander.name,
				groupLeaderLogo: commander.logo,
				adminStaffs: [{ adminUserId: 1, adminUserName: 1, id: 1, eventId: 1 }],
				auditStatus: detail.auditStatus, // 3, // 审核状态：直接审核发布-审核通过
				bePendingDesc: (values.bePendingDesc && '待定') || '', //
			};
			let res;
			if (id) {
				res = await updateGroupEvent({ id, ...params });
			} else {
				res = await addGroupEvent(params);
			}
			if (res.data) {
				message.success('保存成功');
				// linkTo('/newAchv/eventManage/groupManage/list');
				getGroupEventDetail(detail.id);
				setEditing(false);
			}
		} catch (e) {
			console.error(e);
			if (e.errorFields?.length) {
				message.error(e.errorFields[0].errors[0]);
				return;
			}
			message.error('保存失败');
		}
	};

	/* 取消 */
	const handleCancel = () => {
		setEditing(false);
	};
	const handleBack = () => {
		window.history.back();
	};
	const setEditModal = () => {
		setEditing(true);
		// form.setFieldsValue(detail);
	};

	/* 通过审核 */
	const handleAudit = async (auditStatus, reason = '') => {
		const res = await updateAuditStatus({ id: detail.id, auditStatus, reason });
		if (res.data) {
			let str = '审核通过';
			if (auditStatus === 1) {
				str = '撤销审核';
			}
			if (auditStatus === 2) {
				str = '审核不通过';
			}
			if (auditStatus === 3) {
				str = '审核通过';
			}
			message.success(str);
			// handleBack();
			getGroupEventDetail(detail.id);
		}
	};

	const ModalFormRef = useRef();

	return (
		<div>
			<div className="flex flex-shrink align-center margin-bottom-4 height-48 font-size-22 font-weight-500 color-1d2129">
				<Space size={10}>
					<div className="a color-86909c" onClick={() => linkTo('/newAchv/eventManage/groupManage/list')}>
						拼团管理
					</div>
					<div className="color-86909c">/</div>
					<div>{detail?.id ? '修改拼团' : '创建拼团'}</div>
				</Space>
			</div>
			{/* Tabs & 功能按钮 开始 */}
			<Affix>
				<div className="height-66 flex align-center justify-between bg-color-ffffff border-radius-8 padding-lr-24 border-solid-bottom-f2f3f5">
					<Anchor
						affix={false}
						rootClassName="custom-anchor-box"
						direction="horizontal"
						replace
						targetOffset={80}
						items={[
							{
								key: 'section1',
								href: '#section1',
								title: <div className="margin-right-40 font-size-16 font-weight-500">基本信息</div>,
							},
							{
								key: 'section2',
								href: '#section2',
								title: <div className="margin-right-40 font-size-16 font-weight-500">广告位</div>,
							},
							{
								key: 'section3',
								href: '#section3',
								title: <div className="margin-right-40 font-size-16 font-weight-500">拼团详情</div>,
							},
						]}
					/>
					{detail.id && (
						<div className="flex align-center justify-between color-1d2129 line-height-26 font-size-18 font-weight-500">
							<Space size={16}>
								{editing ? (
									<>
										<Button onClick={handleCancel}>取消</Button>
										<Button type="primary" onClick={handleSubmit}>
											保存
										</Button>
									</>
								) : (
									<>
										{detail.auditStatus === 1 && (
											<div>
												<Button
													onClick={() => {
														ModalFormRef.current.setOpen(true);
														ModalFormRef.current.setTitle('不通过原因');
													}}
													danger
												>
													审核不通过
												</Button>
												<Button className="margin-left-16" type="primary" onClick={() => handleAudit(3)}>
													审核通过
												</Button>
											</div>
										)}
										{detail.auditStatus === 2 && (
											<div>
												<Button className="margin-left-16" onClick={() => handleAudit(1)} danger>
													撤销审核
												</Button>
											</div>
										)}
										{detail.auditStatus === 3 && (
											<Button onClick={() => handleAudit(1)} danger>
												撤销审核
											</Button>
										)}
									</>
								)}
							</Space>
						</div>
					)}
					{!detail.id && (
						<Space size={16}>
							<Button onClick={handleBack}>取消</Button>
							<Button type="primary" onClick={handleSubmit}>
								保存
							</Button>
						</Space>
					)}
				</div>
			</Affix>
			{/* Tabs & 功能按钮 结束 */}

			{!editing && (
				<>
					<div id="section1" />
					<div className="bg-color-ffffff border-radius-8 padding-lr-24 padding-tb-20 margin-tb-16">
						<div className="flex align-center justify-between margin-bottom-20">
							<div className="font-size-18 line-height-26 font-weight-500">基本信息</div>
							<Button type="link" icon={<EditOutlined />} onClick={setEditModal}>
								编辑信息
							</Button>
						</div>
						<div className="flex align-center justify-start font-size-14 line-height-22 padding-left-16 margin-bottom-20">
							<div className="color-86909c margin-right-4">拼团标题：</div>
							<div className="">{detail.title || '--'}</div>
						</div>
						<div className="flex align-center justify-start font-size-14 line-height-22 padding-left-16 margin-bottom-20">
							<div className="color-86909c margin-right-4">拼团时间：</div>
							<div className="">
								{detail.joinStartTime && detail.joinEndTime ? `${detail.joinStartTime} - ${detail.joinEndTime}` : '--'}
							</div>
						</div>
						<div className="flex align-center justify-start font-size-14 line-height-22 padding-left-16 margin-bottom-20">
							<div className="color-86909c margin-right-4">活动时间：</div>
							{detail.bePendingDesc ? (
								<div>待定</div>
							) : (
								<div className="">{detail.startTime && detail.endTime ? `${detail.startTime} - ${detail.endTime}` : '--'}</div>
							)}
						</div>
						<div className="flex align-center justify-start font-size-14 line-height-22 padding-left-16 margin-bottom-20">
							<div className="color-86909c margin-right-4">开团地址：</div>
							<div className="">{detail.provinceName && detail.cityName ? `${detail.provinceName} - ${detail.cityName}` : '--'}</div>
						</div>
						<div className="flex align-center justify-start font-size-14 line-height-22 padding-left-16 margin-bottom-20">
							<div className="color-86909c margin-right-4">详细地址：</div>
							<div className="">{detail.address || '--'}</div>
						</div>
						<div className="flex align-center justify-start font-size-14 line-height-22 padding-left-16 margin-bottom-20">
							<div className="color-86909c margin-right-4">拼团封面：</div>
							<div className="">
								<Image src={detail.coverImageUrl} alt="" width={120} />
							</div>
						</div>
						<div className="flex align-center justify-start font-size-14 line-height-22 padding-left-16 margin-bottom-20">
							<div className="color-86909c margin-right-4">团长：</div>
							<div className="flex align-center gap-12">
								<Avatar width={30} src={detail?.groupLeaderLogo} className={'border-solid-e5e6eb'} />
								<div>{detail.groupLeaderName}</div>
							</div>
						</div>
						<div className="flex align-center justify-start font-size-14 line-height-22 padding-left-16 margin-bottom-20">
							<div className="color-86909c margin-right-4">可拼席位：</div>
							<div className="flex gap-12">
								{detail.joinMembersSetupList?.map((item) => {
									return <div key={item.joinTypeName}>{`${item.joinTypeName}：${item.joinNum}`}</div>;
								})}
								<>{!detail.joinMembersSetupList || detail.joinMembersSetupList?.length === 0 ? '--' : ''}</>
							</div>
						</div>

						<div className="flex align-center justify-start font-size-14 line-height-22 padding-left-16 margin-bottom-20">
							<div className="color-86909c margin-right-4">分享标题：</div>
							<div className="">{detail.shareDesc || '--'}</div>
						</div>
						<div className="flex align-center justify-start font-size-14 line-height-22 padding-left-16 margin-bottom-20">
							<div className="color-86909c margin-right-4">分享封面：</div>
							<div className="">{detail.shareImageUrl && <Image src={detail.shareImageUrl} alt="" width={120} />}</div>
						</div>
					</div>
					<div id="section2" />
					<div className="bg-color-ffffff border-radius-8 padding-lr-24 padding-tb-20 margin-tb-16">
						<div className="flex align-center justify-between margin-bottom-20">
							<div className="font-size-18 line-height-26 font-weight-500">广告位</div>
						</div>
						<div className="flex align-center justify-start font-size-14 line-height-22 padding-left-16 margin-bottom-20">
							<div className="color-86909c margin-right-4">广告单位名称：</div>
							<div className="">{detail.adsName || '--'}</div>
						</div>
						<div className="flex align-center justify-start font-size-14 line-height-22 padding-left-16 margin-bottom-20">
							<div className="color-86909c margin-right-4">广告贴图：</div>
							<div className="">{detail.adsImageUrl && <Image src={detail.adsImageUrl} alt="" width={120} />}</div>
						</div>
					</div>
					<div id="section3" />
					<div className="bg-color-ffffff border-radius-8 padding-lr-24 padding-tb-20 margin-tb-16">
						<div className="flex align-center justify-between margin-bottom-20">
							<div className="font-size-18 line-height-26 font-weight-500">拼团详情</div>
						</div>
						<div className="flex align-center justify-start font-size-14 line-height-22 padding-left-16 margin-bottom-20">
							<div className="world-break-all" dangerouslySetInnerHTML={{ __html: detail.introduce || '' }} />
						</div>
					</div>
				</>
			)}

			<Form
				form={form}
				initialValues={{
					shareDesc: '【资源拼团】快来一起拼团链接资源!',
				}}
				labelCol={{ span: 4 }}
				wrapperCol={{ span: 16 }}
				hidden={!editing}
			>
				<div id="section1" />
				<Card title={<div className="font-size-18 line-height-26 font-weight-500">基本信息</div>} className={'margin-top-20'}>
					<StepOne areaList={areaList} visible />
				</Card>
				<div id="section2" />
				<Card title={<div className="font-size-18 line-height-26 font-weight-500">广告位</div>} className={'margin-top-20'}>
					<StepTwo areaList={areaList} visible />
				</Card>
				<div id="section3" />
				<Card title={<div className="font-size-18 line-height-26 font-weight-500">拼团详情</div>} className={'margin-top-20'}>
					<StepThree areaList={areaList} visible />
				</Card>
			</Form>

			{/* 询问弹窗 开始 */}
			<ModalForm
				ref={ModalFormRef}
				onOk={(e) => {
					handleAudit(2, e.reason);
				}}
				FormComp={(props) => <ConrirmForm ref={props.FormCompRef} />}
			/>
			{/* 询问弹窗 结束 */}
		</div>
	);
};

export default Index;

// 询问表单
const ConrirmForm = forwardRef((props = {}, ref) => {
	const [form] = Form.useForm();

	useImperativeHandle(ref, () => {
		return {
			onCancel: () => Promise.resolve(),
			onOk: () => {
				return new Promise((resolve) => {
					form.validateFields().then((values) => {
						resolve(values);
					});
				});
			},
		};
	});

	return (
		<Form form={form} labelCol={{ style: { width: '100px' } }} initialValues={{}}>
			<Form.Item label="原因" name="reason" rules={[{ required: true, message: '请输入不通过原因' }]}>
				<Input.TextArea rows={4} placeholder="请输入不通过原因" />
			</Form.Item>
		</Form>
	);
});
