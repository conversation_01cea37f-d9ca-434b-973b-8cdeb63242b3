/**
 * @description index - 文件描述
 * <AUTHOR>
 *
 * Created on 2024/10/28 16:34
 */
import { useEffect, useState } from 'react';
import { Button, Tooltip, Tag, Table, Space, Modal, message, Form, Switch, Affix, Image, Popover, Select, InputNumber } from 'antd';
import { DownloadOutlined, QuestionCircleOutlined } from '@ant-design/icons';

import { useRouterLink } from '@/hook/useRouter';

import { useTableData } from '@/hook/useTableData';
import {
	pageGroupEvent as getTablePageData,
	deleteGroupEvent,
	updateAuditStatus,
	getGroupEventAuditStatistics,
	updateRankingNum as batchUpdateSort,
	updateGroupEventRecommendStatus,
} from '@/api/Achv/EventManage';
import './index.scss';

const tabsList = [
	{ label: '全部', typeCode: '', count: 0, key: 'total', value: '' },
	{ label: '已发布', typeCode: 3, count: 0, key: 'passNum', value: 3 },
	{ label: '待审核', typeCode: 1, count: 0, key: 'waitNum', value: 1 },
	{ label: '不通过', typeCode: 2, count: 0, key: 'notPassNum', value: 2 },
];
const Index = () => {
	const { linkTo, searchParams } = useRouterLink();
	const id = searchParams.get('id') || undefined;

	const [typeCode, setTypeCode] = useState(searchParams.get('typeCode') || '');

	const [tabsListData, setTabsListData] = useState(tabsList);

	const { form, dataSource, pagination, changePage, onSearch, getTableData, onUpdateSort } = useTableData({
		params: { id },
		getTablePageData,
		batchUpdateSort,
	});

	// 表格数据
	const columns = [
		{
			title: '序号',
			dataIndex: 'index',
			key: 'index',
			render: (_, __, index) => index + 1 + (pagination.current - 1) * 10,
		},
		{
			title: '拼团封面',
			dataIndex: 'coverImageUrl',
			key: 'coverImageUrl',
			className: 'max-width-200',
			render: (coverImageUrl, { eventStatus }) => {
				/* 活动状态：1 未开始 2 进行中 3已结束 4已成团  */
				return (
					<div className={'position-relative border-radius-8 status-btn'}>
						<Image src={coverImageUrl} alt="" width={114} className="width-80 border-radius-8" />
						<div className={`position-absolute top-0 left-0 color-ffffff status-item status-${eventStatus}`}>
							{['未开始', '拼团中', '已结束', '已成团'][eventStatus - 1] || ''}
						</div>
					</div>
				);
			},
		},
		{
			title: '拼团名称',
			dataIndex: 'title',
			key: 'title',
			className: 'max-width-200',
		},
		{
			title: '发布状态',
			dataIndex: 'auditStatus',
			key: 'auditStatus',
			align: 'center',
			render: (auditStatus, record) => {
				/* 	审核状态:1 审核中 2 审核不通过 3 审核通过 */
				return (
					<div className={`tag-status-small-${['default', 'warning', 'error', 'primary'][auditStatus]}`}>
						{['', '审核中', '不通过', '已通过'][auditStatus] || ''}
					</div>
				);
			},
		},
		{
			title: '拼团二维码',
			dataIndex: 'qrCodeUrl',
			key: 'qrCodeUrl',
			align: 'center',
			render: (qrCodeUrl, record) => {
				return (
					<Popover
						trigger={'hover'}
						content={
							<div className={'flex flex-direction-column justify-center padding-lr-12'}>
								<div className={'margin-bottom-8 line-height-22 font-weight-500 text-align-center color-1d2129'}>二维码</div>
								<img className={'margin-bottom-12 width-90 height-90'} src={qrCodeUrl} alt={'二维码'} />
								<Button type="primary" icon={<DownloadOutlined />} onClick={() => downloadQrCode(qrCodeUrl, '拼团二维码')}>
									下载
								</Button>
							</div>
						}
					>
						<Tag className="cursor-pointer" color="blue">
							拼团二维码
						</Tag>
					</Popover>
				);
			},
		},
		{
			title: '开团时间',
			dataIndex: 'joinStartTime',
			key: 'joinStartTime',
			align: 'center',
			render: (joinStartTime) => {
				return joinStartTime;
			},
		},
		{
			title: () => {
				return (
					<Space>
						<div>拼团人数</div>
						<Tooltip title={() => <span className="color-4e5969">已报名/已通过</span>} color="#ffffff">
							<QuestionCircleOutlined />
						</Tooltip>
					</Space>
				);
			},
			dataIndex: 'number',
			key: 'number',
			align: 'center',
			render: (number, record) => {
				return (
					<div className="a" onClick={() => handleStatistics(record)}>
						<span>{(record.joinMembersList || []).filter((ov) => ov.joinType !== 6).length}</span>/<a>{record.passNum || 0}</a>
					</div>
				);
			},
		},
		{
			title: '成团条件',
			dataIndex: 'joinMembersJson',
			render: (joinMembersJson) => {
				const joinMembers = JSON.parse(joinMembersJson || '[]');
				return (
					<div className={'flex flex-wrap width-180 '}>
						{joinMembers
							?.map((item, index) => {
								return `${item.joinTypeName}x${item.joinNum}`;
							})
							.join('、')}
					</div>
				);
			},
		},
		{
			title: '推荐首页',
			dataIndex: 'recommendStatus',
			align: 'center',
			render: (text, record) => {
				return (
					<Switch
						checked={text === 1}
						onChange={() => {
							changeStatus(record);
						}}
					/>
				);
			},
		},
		{
			title: '排序',
			dataIndex: 'rankingNum',
			align: 'center',
			render: (_, record) => {
				return (
					<InputNumber
						className="text-align-center"
						defaultValue={record.rankingNum}
						min={1}
						precision={0}
						controls={false}
						onBlur={(e) => {
							const rankingNum = e.target.value - 0 || null;
							if (rankingNum !== record.rankingNum) {
								onUpdateSort([
									{
										id: record.id,
										rankingNum,
									},
								]);
							}
						}}
						placeholder="请输入"
					></InputNumber>
				);
			},
		},
		{
			title: '流量统计',
			dataIndex: 'viewNum',
			align: 'center',
			render: (text) => {
				return text || '-';
			},
		},
		{
			title: '操作',
			dataIndex: 'operate',
			key: 'operate',
			fixed: 'right',
			align: 'center',
			render: (text, record) => {
				return (
					<Space size={0}>
						<Button size={'small'} type={'link'} onClick={() => handleCurd(record.id)}>
							编辑/审核
						</Button>
						<Button size={'small'} type={'link'} danger onClick={() => handleDel(record.id)}>
							刪除
						</Button>
					</Space>
				);
			},
		},
	];

	/* 点击下载二维码 */
	const downloadQrCode = (url, name) => {
		let aLink = document.createElement('a');
		aLink.href = url;
		aLink.download = name || '';
		aLink.click();
		aLink.remove();
	};

	// 改变状态
	const changeStatus = (row) => {
		const recommendStatus = row.recommendStatus === 0 ? 1 : 0;
		updateGroupEventRecommendStatus({
			id: row.id,
			recommendStatus,
		}).then(() => {
			message.success('操作成功');
			row.recommendStatus = recommendStatus;
			getTableData();
		});
	};

	useEffect(() => {
		getGroupEventJoinAuditStatistics({});
	}, []);

	/* 查询拼团统计数据 */
	const getGroupEventJoinAuditStatistics = async (params) => {
		const res = await getGroupEventAuditStatistics(params);
		if (res.data) {
			console.log(res.data);
			setTabsListData(tabsList.map((item) => ({ ...item, count: res.data[item.key] })));
		}
	};
	/* 切换tabs */
	const handleTabsChange = (typeCode) => {
		setTypeCode(typeCode);
		form.setFieldsValue({ auditStatus: typeCode });
		onSearch();
	};

	// 跳到统计页面
	const handleStatistics = (row) => {
		linkTo(`/newAchv/eventManage/groupManage/Statistics?id=${row.id}&title=${row.title}`);
	};

	// 新增/编辑
	const handleCurd = (id = '') => {
		linkTo(`/newAchv/eventManage/groupManage/curd${id ? `?id=${id}` : ''}`);
	};

	// 删除
	const handleDel = (id) => {
		Modal.confirm({
			title: '提示',
			content: '确认删除选中数据？此操作不可以撤回请谨慎操作！',
			async onOk() {
				const res = await deleteGroupEvent({ ids: [id] });
				if (res.data) {
					message.success('删除成功');
					onSearch();
				}
			},
		});
	};

	return (
		<div className="">
			<div className="flex flex-shrink align-center margin-bottom-4 height-48 font-size-22 font-weight-500 color-1d2129">拼团管理</div>
			{/* Tabs 开始 */}
			<Affix offsetTop={0}>
				<div className="flex align-center justify-between padding-24 border-radius-8 bg-color-ffffff border-solid-bottom-f2f3f5">
					<div className="flex align-center">
						{tabsListData.map((ov, oi) => (
							<div
								key={oi}
								className={`a margin-right-40 font-size-18 font-weight-500 ${typeCode === ov.typeCode ? 'color-165dff' : ''}`}
								onClick={() => handleTabsChange(ov.typeCode)}
							>
								{ov.label}
								{ov.count - 0 > 0 ? `（${ov.count}）` : ''}
							</div>
						))}
					</div>
					<Space>
						<Button type={'primary'} onClick={() => handleCurd()}>
							新建拼团
						</Button>
					</Space>
				</div>
			</Affix>
			{/* Tabs 结束 */}
			<div className="flex-sub margin-top-18 padding-24 border-radius-8 bg-color-ffffff">
				<div className={'flex justify-end'}>
					<Form form={form} hidden>
						<Form.Item name={'auditStatus'}>
							<Select placeholder={'请选择筛选类型'} options={tabsListData} />
						</Form.Item>
					</Form>
				</div>
				{/* 表格 开始 */}
				<Table
					rowKey="id"
					dataSource={dataSource}
					pagination={pagination}
					onChange={changePage}
					scroll={{ x: 'max-content' }}
					columns={columns}
				/>
				{/* 表格 结束 */}
			</div>
			{/*<DetailModal ref={detailRef} />*/}
		</div>
	);
};

export default Index;
