import { useEffect, useState, forwardRef, useImperativeHandle, memo, useRef } from 'react';

import { Affix, Anchor, Button, message, Form, Image, Input, Radio, Space, Checkbox } from 'antd';
import { useRouterLink } from '@/hook/useRouter';
import { EditOutlined } from '@ant-design/icons';
import { detailGroupEventJoin, addGroupEventJoin, updateGroupEventJoin, updateJoinAuditStatus } from '@/api/Achv/EventManage';
import { demandTypeOptions, joinTypeColor, joinTypeOptions as joinTypeData } from '@/pages/Achv/EventManage/GroupManage/const';
import UploadImg from '@/components/UploadImg';
import ModalForm from '@/components/ModalForm';

const joinTypeOptions = [...joinTypeData, { label: '气氛组', value: 6 }];

const detailSchema = [
	{
		label: '活动名称',
		key: 'company',
		visibleType: [1, 2, 3, 4, 6],
	},
	{
		label: '姓名',
		key: 'name',
		visibleType: [5],
	},
	{
		label: 'LOGO',
		key: 'companyLogo',
		visibleType: [1, 2, 3, 4],
		type: 'image',
	},
	{
		label: '头像',
		key: 'companyLogo',
		visibleType: [5, 6],
		type: 'image',
	},
	{
		label: '类型',
		key: 'joinTypeName',
		visibleType: [1, 2, 3, 4],
		type: 'tag',
		color: joinTypeColor,
		colorValue: 'joinType',
	},
	{
		label: '联系人',
		key: 'name',
		visibleType: [1, 2, 3, 4],
	},
	{
		label: '联系电话',
		key: 'phone',
		visibleType: [1, 2, 3, 4, 5],
	},
	{
		label: '类型',
		key: 'joinTypeName',
		visibleType: [5, 6],
		type: 'tag',
		color: joinTypeColor,
		colorValue: 'joinType',
	},
	{
		label: '资源需求',
		key: 'demandTypeName',
		visibleType: [1, 2, 3, 4],
	},
	{
		label: '资源描述',
		key: 'demandDesc',
		visibleType: [1, 2, 3, 4],
	},
	{
		label: '弹幕展示',
		key: 'displayStatus',
		visibleType: [1, 2, 3, 4, 5, 6],
		component: (val) => {
			return val == 0 ? '不展示' : '展示';
		},
	},
];

const StatisticsCurd = () => {
	const { linkTo, searchParams } = useRouterLink();
	const [detail, setDetail] = useState({});
	const [editing, setEditing] = useState(!!searchParams.get('editing') || false);
	const [form] = Form.useForm();
	const joinType = Form.useWatch('joinType', form);

	useEffect(() => {
		if (searchParams.get('id')) {
			getGroupEventDetail();
		}
	}, []);
	/* 查询详情信息 */
	const getGroupEventDetail = async (id) => {
		const res = await detailGroupEventJoin({ id: searchParams.get('id') || id });
		if (res.data) {
			const data = res.data;
			data.joinTypeName = joinTypeOptions.find((item) => item.value === data.joinType)?.label;
			const typeList = data.demandType?.split(',');
			data.demandTypeName = typeList?.map((type) => demandTypeOptions.find((it) => `${it.value}` === `${type}`)?.label).join('、');
			data.demandType = typeList?.map((item) => Number(item));
			setDetail(res.data);
		}
	};
	/* 跳到参团统计 */
	const linkToStatistics = () => {
		history.back();
	};
	/* 跳到拼团管理 */
	const jumpToGroupManage = () => {
		linkTo('/newAchv/eventManage/groupManage/list');
	};
	/* 保存 */
	const handleSubmit = async () => {
		const values = await form.validateFields();
		const params = {
			...values,
			id: detail.id,
			eventId: detail.eventId || searchParams.get('eventId'),
			// 气氛组直接审核不通过
			auditStatus: joinType === 6 ? 2 : detail.auditStatus,
			demandType: values.demandType ? values.demandType.join(',') : '',
		};
		const res = await (params.id ? updateGroupEventJoin : addGroupEventJoin)(params);
		if (res.data) {
			message.success('保存成功');
			getGroupEventDetail(res.data);
			setEditing(false);
		}
	};
	/* 审核提交 */
	const handleApprove = async (auditStatus, reason = '') => {
		const res = await updateJoinAuditStatus({ ids: [detail.id], auditStatus, eventId: detail.eventId, reason });
		if (res.data) {
			message.success('审核成功');
			getGroupEventDetail();
		}
	};
	/* 取消 */
	const handleCancel = () => {
		form.resetFields();
		setEditing(false);
	};
	const handleBack = () => {
		window.history.back();
	};
	const setEditModal = () => {
		setEditing(true);
		form.setFieldsValue(detail);
	};

	const ModalFormRef = useRef();

	return (
		<div>
			<div className="flex flex-shrink align-center margin-bottom-4 height-48 font-size-22 font-weight-500 color-1d2129">
				<Space size={10}>
					<div className="a color-86909c" onClick={jumpToGroupManage}>
						拼团管理
					</div>
					<div className="color-86909c">/</div>
					<div className="a color-86909c" onClick={linkToStatistics}>
						参团统计
					</div>
					<div className="color-86909c">/</div>
					<div>参团信息</div>
				</Space>
			</div>
			{/* Tabs & 功能按钮 开始 */}
			<Affix>
				<div className="height-66 flex align-center justify-between bg-color-ffffff border-radius-8 padding-lr-24 border-solid-bottom-f2f3f5">
					<Anchor
						affix={false}
						rootClassName="custom-anchor-box"
						direction="horizontal"
						replace
						targetOffset={80}
						items={[
							{
								key: 'section1',
								href: '#section1',
								title: <div className="margin-right-40 font-size-16 font-weight-500">基本信息</div>,
							},
						]}
					/>
					<div className="flex align-center justify-between color-1d2129 line-height-26 font-size-18 font-weight-500">
						{editing ? (
							<Space size={16}>
								<Button onClick={handleCancel}>取消</Button>
								<Button type={'primary'} onClick={handleSubmit}>
									保存
								</Button>
							</Space>
						) : (
							<>
								{detail.auditStatus === 1 ? (
									<Space size={16}>
										<Button
											onClick={() => {
												ModalFormRef.current.setOpen(true);
												ModalFormRef.current.setTitle('不通过原因');
											}}
											danger
										>
											不通过
										</Button>
										<Button type={'primary'} onClick={() => handleApprove(3)}>
											通过
										</Button>
									</Space>
								) : (
									<Button onClick={() => handleApprove(1)} danger>
										撤销审核
									</Button>
								)}
							</>
						)}
					</div>
				</div>
			</Affix>
			{/* Tabs & 功能按钮 结束 */}
			{/* 基本信息 开始 */}
			<div id="section1" />

			<Form
				form={form}
				labelCol={{ span: 6 }}
				hidden={!editing}
				wrapperCol={{ span: 14 }}
				initialValues={{
					joinType: joinTypeOptions[0].value,
					displayStatus: 1,
				}}
			>
				<div className="margin-top-20 padding-20 border-radius-8 bg-color-ffffff">
					<Form.Item label="名称" name="company" rules={[{ required: true, message: '请输入名称' }]}>
						<Input />
					</Form.Item>
					<Form.Item label="LOGO" name="companyLogo">
						<UploadImg size={5} width={150} height={150} tips={'建议尺寸：500*500px'} cropperProps={{ width: 500, height: 500 }} />
					</Form.Item>
					<Form.Item label="类型" name="joinType" required>
						<Radio.Group
							options={joinTypeOptions}
							onChange={(e) => {
								const value = e.target.value;
								// 气氛组必须显示弹幕
								if (value === 6) {
									form.setFieldValue('displayStatus', 1);
								}
							}}
						/>
					</Form.Item>
					<Form.Item label="联系人" name="name">
						<Input />
					</Form.Item>
					<Form.Item label="联系电话" name="phone">
						<Input />
					</Form.Item>
					<Form.Item label="资源需求" name="demandType">
						<Checkbox.Group options={demandTypeOptions} />
					</Form.Item>
					<Form.Item label="需求描述" name="demandDesc">
						<Input.TextArea rows={4} />
					</Form.Item>
					{/* 气氛组不给编辑 */}
					<Form.Item label="弹幕展示" name="displayStatus">
						<Radio.Group
							disabled={joinType === 6}
							options={[
								{
									label: '不展示',
									value: 0,
								},
								{
									label: '展示',
									value: 1,
								},
							]}
						/>
					</Form.Item>
				</div>
			</Form>

			{!editing && (
				<div className="margin-top-20 padding-20 border-radius-8 bg-color-ffffff">
					<div className={'flex align-center justify-between margin-bottom-20'}>
						<div className=" line-height-26 font-size-18 font-weight-500 color-1d2129">基本信息</div>
						{/* {detail.joinType !== 5 && (
						)} */}
						<Button type="link" icon={<EditOutlined />} onClick={setEditModal}>
							编辑信息
						</Button>
					</div>
					{detailSchema.map((item) => {
						if (!item.visibleType.includes(detail.joinType)) return null;
						let detailDom = detail[item.key];
						if (item.type === 'image') {
							detailDom = detailDom ? <Image src={detailDom} width={100} height={100} /> : '--';
						}
						if (item.key === 'displayStatus') {
							detailDom = (item.component && item.component(detail[item.key])) || '';
						}

						return (
							<div
								key={item.key}
								className="flex align-center justify-start font-size-14 line-height-22 padding-left-16 margin-bottom-20"
							>
								<div className="color-86909c margin-right-4">{item.label}：</div>
								<div className="">{detailDom || '--'}</div>
							</div>
						);
					})}
				</div>
			)}

			{/* 询问弹窗 开始 */}
			<ModalForm
				ref={ModalFormRef}
				onOk={(e) => {
					handleApprove(2, e.reason);
				}}
				FormComp={(props) => <ConrirmForm ref={props.FormCompRef} />}
			/>
			{/* 询问弹窗 结束 */}
		</div>
	);
};
export default StatisticsCurd;

// 询问表单
const ConrirmForm = forwardRef((props = {}, ref) => {
	const [form] = Form.useForm();

	useImperativeHandle(ref, () => {
		return {
			onCancel: () => Promise.resolve(),
			onOk: () => {
				return new Promise((resolve) => {
					form.validateFields().then((values) => {
						resolve(values);
					});
				});
			},
		};
	});

	return (
		<Form form={form} labelCol={{ style: { width: '100px' } }} initialValues={{}}>
			<Form.Item label="原因" name="reason" rules={[{ required: true, message: '请输入不通过原因' }]}>
				<Input.TextArea rows={4} placeholder="请输入不通过原因" />
			</Form.Item>
		</Form>
	);
});
