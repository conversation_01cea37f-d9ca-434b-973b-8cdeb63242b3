/**
 * @description Statistics - 文件描述
 * <AUTHOR>
 *
 * Created on 2024/10/28 16:39
 */
import { useEffect, useRef, useState } from 'react';
import { Affix, Button, Image, Select, Space, Table, Tag, Typography, InputNumber, message, Switch } from 'antd';
import { useRouterLink } from '@/hook/useRouter';
import {
	detailGroupEventJoin,
	getGroupEventJoinAuditStatistics,
	pageGroupEventJoin,
	exportGroupEventJoin,
	updateJoinRankingNum,
	updateGroupEventJoin,
} from '@/api/Achv/EventManage';
import JoinInfoModal from '@/pages/Achv/EventManage/GroupManage/components/JoinInfoModal';
import { demandTypeOptions, joinTypeColor, joinTypeOptions } from '../const';
import { download } from '@/utils/common';
import dayjs from 'dayjs';

const joinTypeList = joinTypeOptions.map((ov) => ov.value);

const { Title } = Typography;

const tabsList = [
	{
		typeCode: '',
		label: '全部',
		count: 0,
		key: 'total',
	},
	{
		typeCode: '1',
		label: '待审核',
		count: 0,
		key: 'waitNum',
	},
	{
		typeCode: '3',
		label: ' 通过',
		count: 0,
		key: 'passNum',
	},
	{
		typeCode: '2',
		label: '不通过',
		count: 0,
		key: 'notPassNum',
	},
	{
		typeCode: '4',
		label: '气氛组',
		count: 0,
		key: 'count',
	},
];

const Statistics = () => {
	const joinRef = useRef();
	const { linkTo, searchParams } = useRouterLink();
	const [tabsListData, setTabsListData] = useState(tabsList);
	const [auditStatus, setTypeCode] = useState(searchParams.get('typeCode') || '');
	const [pagination, setPagination] = useState({
		current: 1,
		pageSize: 10,
	});
	const [total, setTotal] = useState(0);
	const [dataSource, setDataSource] = useState([]);
	const [joinType, setJoinType] = useState(searchParams.get('joinType') || null);
	const [title, setTitle] = useState('');

	useEffect(() => {
		const searchId = searchParams.get('id');
		setTitle(searchParams.get('title'));
		if (searchId) {
			getGroupEventStatistics(searchId);
		}
	}, []);

	useEffect(() => {
		getGroupEventList();
	}, [auditStatus, pagination]);

	/* 拼团统计 */
	const getGroupEventStatistics = async (id) => {
		const res = await getGroupEventJoinAuditStatistics({ eventId: id, joinTypeList });
		const res1 = await pageGroupEventJoin({
			pageNum: 1,
			pageSize: 1,
			joinType: 6,
			eventId: searchParams.get('id'),
		});
		res.data.count = res1.data.total || 0;

		setTabsListData(tabsList.map((item) => ({ ...item, count: res.data[item.key] })));
	};
	/* 拼团列表 */
	const getGroupEventList = async () => {
		const params = {
			eventId: searchParams.get('id'),
			auditStatus: auditStatus != 4 ? auditStatus : undefined,
			joinTypeList: auditStatus != 4 ? (joinType ? [joinType] : joinTypeList) : [6],
			pageNum: pagination.current,
			pageSize: pagination.pageSize,
		};
		const res = await pageGroupEventJoin(params);
		if (res.data) {
			setDataSource(res.data.records);
			setTotal(res.data.total);
		}
	};
	/* 导出数据 */
	const exportData = async () => {
		searchParams.get('id') &&
			exportGroupEventJoin({ eventId: searchParams.get('id') }).then((res) => {
				download.excel(res, `${searchParams.get('title')}-${dayjs().format('YYYYMMDD_HH:mm')}`);
			});
	};
	const changePage = ({ current, pageSize }) => {
		setPagination({
			current,
			pageSize: pageSize,
		});
	};
	/* 操作 */
	const optionsData = async (record) => {
		console.log(record);
		const res = await detailGroupEventJoin({ id: record.id });
		// joinRef.current.showModal(res.data);
		linkTo(`/newAchv/eventManage/groupManage/statisticsCurd?id=${record.id}&eventId=${searchParams.get('id')}`);
	};

	/* 切换审核类型 */
	const handleChangeTypeCode = (value) => {
		setTypeCode(value);
	};

	// 排序
	const onUpdateSort = (saveDtoList = []) => {
		updateJoinRankingNum({
			saveDtoList,
		}).then(() => {
			message.success('排序已更改');
			getGroupEventList();
		});
	};
	const onChangeDisplayStatus = (val, row) => {
		const params = {
			id: row.id,
			displayStatus: val,
		};
		updateGroupEventJoin(params).then((res) => {
			setDataSource(
				dataSource.map((ov) => {
					if (params.id == ov.id) {
						return {
							...ov,
							displayStatus: val,
						};
					} else {
						return ov;
					}
				})
			);
		});
	};
	const columns = [
		{
			title: '序号',
			dataIndex: 'id',
			key: 'id',
			width: 80,
			align: 'center',
			render: (text, record, index) => {
				return index + 1 + (pagination.current - 1) * pagination.pageSize;
			},
		},
		{
			title: '名称',
			dataIndex: 'company',
			key: 'company',
			width: 150,
			render: (company, record) => {
				if (record.joinType == 5) {
					return record.name || '';
				}
				return company || '--';
			},
		},
		{
			title: 'logo/头像',
			dataIndex: 'companyLogo',
			key: 'companyLogo',
			width: 150,
			render: (companyLogo, record) => {
				return companyLogo ? <Image src={companyLogo} alt="" style={{ width: 80, height: 80 }} /> : '--';
			},
		},
		{
			title: '参团类型',
			dataIndex: 'joinType',
			key: 'joinType',
			width: 150,
			render: (joinType, record) => {
				const joinTypeName = joinTypeOptions.find((item) => item.value === joinType)?.label;
				return <Tag color={joinTypeColor[joinType - 1]}>{joinTypeName || '气氛组'}</Tag>;
			},
		},
		{
			title: '审核状态',
			dataIndex: 'auditStatus',
			key: 'auditStatus',
			width: 150,
			render: (auditStatus, record) => {
				// 	审核状态:1 审核中 2 审核不通过 3 审核通过
				const color = ['', 'processing', 'error', 'success'];
				const text = ['未审核', '审核中', '不通过', '已通过'];
				return (
					<div className={`tag-status-small-${['default', 'warning', 'error', 'primary'][auditStatus]}`}>
						{['', '审核中', '不通过', '已通过'][auditStatus] || ''}
					</div>
				);
			},
		},
		{
			title: '弹幕展示',
			dataIndex: 'displayStatus',
			key: 'displayStatus',
			width: 150,
			render: (displayStatus, record) => {
				// 	状态:1 展示 0 不展示
				return <Switch checked={displayStatus == 1} onChange={(val) => onChangeDisplayStatus((val && 1) || 0, record)} />;
			},
		},
		{
			title: '联系人',
			dataIndex: 'name',
			key: 'name',
			width: 150,
		},
		{
			title: '联系电话',
			dataIndex: 'phone',
			key: 'phone',
		},
		{
			title: '资源需求',
			dataIndex: 'demandType',
			key: 'demandType',
			width: 150,
			render: (demandType, record) => {
				const typeList = demandType?.split(',') || [];
				const demandTypeName = typeList?.map((type) => {
					return demandTypeOptions.find((item) => `${item.value}` === `${type}`)?.label;
				});
				return (typeList?.length > 0 && demandTypeName?.join(',')) || '--';
			},
		},
		{
			title: '需求描述',
			dataIndex: 'demandDesc',
			key: 'demandDesc',
			width: 150,
			render: (demandDesc, record) => {
				return demandDesc || '--';
			},
		},
		{
			title: '排序',
			dataIndex: 'rankingNum',
			align: 'center',
			render: (_, record) => {
				return (
					<InputNumber
						className="text-align-center"
						defaultValue={record.rankingNum}
						min={1}
						precision={0}
						controls={false}
						onBlur={(e) => {
							const rankingNum = e.target.value - 0 || null;
							if (rankingNum !== record.rankingNum) {
								onUpdateSort([
									{
										id: record.id,
										rankingNum,
									},
								]);
							}
						}}
						placeholder="请输入"
					></InputNumber>
				);
			},
		},
		{
			title: '操作',
			dataIndex: '',
			key: '',
			width: 150,
			fixed: 'right',
			render: (text, record) => {
				return (
					<Button type={'link'} size={'small'} onClick={() => optionsData(record)}>
						编辑/审核
					</Button>
				);
			},
		},
	];
	return (
		<div>
			<div className="flex flex-shrink align-center margin-bottom-4 height-48 font-size-22 font-weight-500 color-1d2129">
				<Space size={10}>
					<div className="a color-86909c" onClick={() => linkTo('/newAchv/eventManage/groupManage/list')}>
						拼团管理
					</div>
					<div className="color-86909c">/</div>
					<div>参团统计</div>
				</Space>
			</div>
			<div className="flex-sub flex flex-direction-column margin-top-18 padding-24 border-radius-8 bg-color-ffffff">
				<div className={'flex align-center justify-between'}>
					<Title level={4} className={''}>
						参团统计
					</Title>
					<Space>
						<Button type={'primary'} onClick={exportData}>
							导出数据
						</Button>
						<Button
							type={'primary'}
							onClick={() => {
								linkTo(`/newAchv/eventManage/groupManage/statisticsCurd?eventId=${searchParams.get('id')}&editing=1`);
							}}
						>
							新增报名
						</Button>
					</Space>
				</div>
				<Title level={5} className={'margin-top-24'}>
					{title}
				</Title>

				{/* Tabs 开始 */}
				<Affix offsetTop={0}>
					<div className="flex align-center justify-between padding-24 border-radius-8 bg-color-ffffff border-solid-bottom-f2f3f5">
						<div className="flex align-center">
							{tabsListData.map((ov, oi) => (
								<div
									key={oi}
									className={`a margin-right-40 font-size-18 font-weight-500 ${auditStatus === ov.typeCode ? 'color-165dff' : ''}`}
									onClick={() => handleChangeTypeCode(ov.typeCode)}
								>
									{ov.label}
									{ov.count - 0 > 0 ? `（${ov.count}）` : ''}
								</div>
							))}
						</div>
						<Space>
							<Select
								className={'width-200'}
								allowClear
								options={joinTypeOptions}
								value={joinType}
								onChange={setJoinType}
								placeholder={'请选择筛选类型'}
							/>
							<Button type={'primary'} onClick={() => getGroupEventList()}>
								搜索
							</Button>
						</Space>
					</div>
				</Affix>
				{/* Tabs 结束 */}
				<div className="flex-sub padding-24 border-radius-8 bg-color-ffffff">
					{/* 表格 */}
					{/* 表格 开始 */}
					<Table
						rowKey="id"
						dataSource={dataSource}
						pagination={{
							...pagination,
							total,
							showTotal: (total) => `共 ${total} 条数据`,
						}}
						onChange={changePage}
						scroll={{ x: 'max-content' }}
						columns={columns.filter((ov) => {
							return (ov.dataIndex || ov.key) !== 'rankingNum' || auditStatus == 3;
						})}
					/>
					{/* 表格 结束 */}
				</div>
				<JoinInfoModal ref={joinRef} />
			</div>
		</div>
	);
};

export default Statistics;
