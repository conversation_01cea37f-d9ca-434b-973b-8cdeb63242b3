/**
 * @description CommanderModal.jsx - 文件描述
 * <AUTHOR>
 *
 * Created on 2024/10/28 18:39
 */
import React, { forwardRef, useEffect } from 'react';
import { Avatar, Button, Col, Form, Image, Input, Modal } from 'antd';
import UploadImg from '@/components/UploadImg';
import { DeleteOutlined } from '@ant-design/icons';

const initialValues = {
	name: '大湾区科技创新服务中心（广州）股份有限公司',
	logo: 'https://gbac-src.dwq360.com/static-assets/gbac-achv-applet/BusinessCard/company-1-logo.png',
	// address: '广州市天河区天河北路886号 广州（国际）科技成果转化天河基地C栋6楼'
};
const CommanderModal = ({ onChange, value }) => {
	const [open, setOpen] = React.useState(false);
	const [form] = Form.useForm();
	const [info, setInfo] = React.useState(null);
	/* 打开设置团长 */
	const handleOpen = () => {
		setOpen(true);
		setInfo(value);
	};
	const handleCancel = () => {
		setOpen(false);
	};
	/* 提交更新团长信息 */
	const handleSubmit = async () => {
		const values = await form.validateFields();
		console.log(values);
		setOpen(false);
		setInfo(values);
		onChange(values);
	};
	/* 删除团长信息 */
	const handleDelete = () => {
		setInfo(null);
		onChange(null);
		form.resetFields();
	};
	useEffect(() => {
		if (value?.logo) {
			form.setFieldsValue(value);
			setInfo(value);
		} else {
			form.resetFields();
		}
	}, [value]);
	/* 自动填写信息 */
	const handleAutoFill = () => {
		form.setFieldsValue(initialValues);
	};
	return (
		<div>
			{info ? (
				<div className={'flex align-center gap-12'}>
					<Avatar width={30} src={info?.logo} className={'border-solid-e5e6eb'} />
					<div>{info.name}</div>
					<Button type={'link'} onClick={handleOpen}>
						编辑
					</Button>
				</div>
			) : (
				<Button type={'primary'} size={'small'} onClick={handleOpen}>
					添加
				</Button>
			)}
			<Modal open={open} title={'团长信息'} onCancel={handleCancel} onOk={handleSubmit} width={800}>
				<Form form={form} labelCol={{ span: 6 }} wrapperCol={{ span: 14 }}>
					<Form.Item label={'团长名称'} name={'name'} rules={[{ required: true, message: '请输入团长名称' }]}>
						<Input placeholder={'请输入团长名称'} />
					</Form.Item>
					<Form.Item noStyle>
						<Col offset={6} className={'margin-bottom-20'}>
							<Button type={'link'} size={'small'} onClick={handleAutoFill}>
								自动填写
							</Button>
						</Col>
					</Form.Item>
					<Form.Item label={'单位logo'} name={'logo'} rules={[{ required: true, message: '请上传团长logo' }]}>
						<UploadImg size={5} width={100} height={100} tips={'建议尺寸：500*500px'} cropperProps={{ width: 500, height: 500 }} />
					</Form.Item>
					<Form.Item noStyle wrapperCol={{ span: 14, offset: 6 }} hidden={!value}>
						<Col offset={6}>
							<Button type={'link'} size={'small'} icon={<DeleteOutlined />} onClick={handleDelete}>
								删除团长信息
							</Button>
						</Col>
					</Form.Item>
				</Form>
			</Modal>
		</div>
	);
};
export default CommanderModal;
