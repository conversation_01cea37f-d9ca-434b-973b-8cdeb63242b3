/**
 * @description JoinInfoModal.jsx - 文件描述
 * <AUTHOR>
 *
 * Created on 2024/10/29 11:35
 */
import React, {forwardRef, useImperativeHandle, useRef, useState} from 'react';
import {Button, Divider, Form, Input, Modal, Radio, message} from "antd";
import UploadImg from "@/components/UploadImg";
import {joinTypeOptions, demandTypeOptions} from "../const";
import {updateAuditStatus, updateGroupEventJoin, updateJoinAuditStatus} from "@/api/Achv/EventManage";

const JoinForm = (props, ref) => {
    const [form] = Form.useForm();
    useImperativeHandle(ref, () => ({
        form,
    }));
    return <Form form={form} labelCol={{span: 6}} wrapperCol={{span: 14}}>
        <Form.Item label="名称" name="company">
            <Input/>
        </Form.Item>
        <Form.Item label="LOGO" name="companyLogo">
            <UploadImg size={5} width={150} height={150} tips={'建议尺寸：500*500px'}
                       cropperProps={{width: 500, height: 500}}/>
        </Form.Item>
        <Form.Item label="类型" name="joinType">
            <Radio.Group options={joinTypeOptions}/>
        </Form.Item>
        <Form.Item label="联系人" name="name">
            <Input/>
        </Form.Item>
        <Form.Item label="联系电话" name="phone">
            <Input/>
        </Form.Item>
        <Form.Item label="资源需求" name="demandType">
            <Radio.Group options={demandTypeOptions}/>
        </Form.Item>
        <Form.Item label="需求描述" name="demandDesc">
            <Input.TextArea rows={4}/>
        </Form.Item>
    </Form>;
};

export const JoinFormWarp = forwardRef(JoinForm);
const JoinInfoModal = (props, ref) => {
    const [open, setOpen] = useState(false);
    const [detail, setDetail] = useState({});
    const formRef = useRef();
    const [form] = Form.useForm();
    /* 打开弹框 */
    const handleOpen = (detail) => {
        setOpen(true);
        setDetail(detail);
        form.setFieldsValue(detail);
    };
    /* 关闭弹框 */
    const handleClose = () => {
        setOpen(false);
    };
    /* 审核提交 */
    const handleSubmit = async (auditStatus) => {
        const res = await updateJoinAuditStatus({ids: [detail.id], auditStatus, eventId: detail.eventId});
        if (res.data) {
            message.success('审核成功');
            handleClose();
        }
    }
    /* 拒绝审核 */
    const handleReject = async () => {
        // 审核状态:1 审核中 2 审核不通过 3 审核通过
        handleSubmit(2);
    }
    /* 审核通过 */
    const handlePass = async () => {
        handleSubmit(3);
    }
    /* 保存并通过审核 */
    const handleUpdate = async () => {
        const values = await form.validateFields();
        const params = {
            ...values,
            id: detail.id,
        };
        const res = await updateGroupEventJoin(params);
        if (res.data) {
            // handlePass();
        }
    }
    useImperativeHandle(ref, () => ({
        showModal: handleOpen,
    }), [form]);
    return (<Modal title={'参团信息'} open={open} width={800}
                   footer={<div className={'flex justify-end gap-12'}>
                       <Button onClick={handleReject}>不通过</Button>
                       <Button type={'primary'} onClick={handlePass}>通过</Button>
                   </div>} onCancel={handleClose}>
        <Divider style={{margin: '24px -24px', width: 'auto'}}/>
        <Form form={form} labelCol={{span: 6}} wrapperCol={{span: 14}}>
            <Form.Item label="名称" name="company">
                <Input/>
            </Form.Item>
            <Form.Item label="LOGO" name="companyLogo">
                <UploadImg size={5} width={150} height={150} tips={'建议尺寸：500*500px'}
                           cropperProps={{width: 500, height: 500}}/>
            </Form.Item>
            <Form.Item label="类型" name="joinType">
                <Radio.Group options={joinTypeOptions}/>
            </Form.Item>
            <Form.Item label="联系人" name="name">
                <Input/>
            </Form.Item>
            <Form.Item label="联系电话" name="phone">
                <Input/>
            </Form.Item>
            <Form.Item label="资源需求" name="demandType">
                <Radio.Group options={demandTypeOptions}/>
            </Form.Item>
            <Form.Item label="需求描述" name="demandDesc">
                <Input.TextArea rows={4}/>
            </Form.Item>
        </Form>
    </Modal>)
}
export default forwardRef(JoinInfoModal);
