/**
 * @description JoinMembers - 文件描述
 * <AUTHOR>
 *
 * Created on 2024/10/28 20:00
 */
import React from 'react';
import { Checkbox, Input, InputNumber } from 'antd';
import { joinTypeOptions } from '../const';

const JoinMembers = ({ value, onChange }) => {
	// {joinType, joinNum}
	const [checkValue, setCheckValue] = React.useState({});

	React.useEffect(() => {
		console.log('JoinMembers value: ', value);
		if (value && value.length > 0) {
			const checkValue = {};
			value.forEach((item) => {
				checkValue[item.joinType] = item.joinNum;
			});
			setCheckValue(checkValue);
		}
	}, [value]);

	/* 修改勾选值 */
	const changeCheck = (e) => {
		const target = e.target;
		const value = target.value;
		const prevSelected = checkValue;
		if (target.checked) {
			prevSelected[value] = 1;
		} else {
			delete prevSelected[value];
		}
		setCheckValue(prevSelected);
		const changeValues = joinTypeOptions
			.map((item) => ({
				joinType: item.value,
				joinNum: prevSelected[item.value],
				joinTypeName: item.label,
			}))
			.filter((item) => checkValue.hasOwnProperty(item.joinType));
		onChange && onChange(changeValues);
	};
	/* 修改设置值 */
	const changeSet = (value, id) => {
		const prevSelected = checkValue;
		prevSelected[id] = value;
		setCheckValue(prevSelected);
		const changeValues = joinTypeOptions
			.map((item) => ({
				joinType: item.value,
				joinNum: prevSelected[item.value] || 1,
				joinTypeName: item.label,
			}))
			.filter((item) => checkValue.hasOwnProperty(item.joinType));
		onChange && onChange(changeValues);
	};
	/* 勾选角色 */
	return (
		<div className={'flex flex-direction-column gap-8 align-start'}>
			{joinTypeOptions.map((item) => {
				return (
					<div key={item.label} className={'flex flex-direction-row align-center text-align-left gap-12'}>
						<Checkbox
							size={'small'}
							checked={checkValue.hasOwnProperty(item.value)}
							value={item.value}
							className={'width-120'}
							onChange={changeCheck}
						>
							{item.label}
						</Checkbox>
						<InputNumber
							value={checkValue[item.value]}
							size={'small'}
							min={1}
							step={1}
							disabled={!checkValue.hasOwnProperty(item.value)}
							onChange={(v) => changeSet(v, item.value)}
							placeholder={'参团席位'}
						/>
					</div>
				);
			})}
		</div>
	);
};
export default JoinMembers;
