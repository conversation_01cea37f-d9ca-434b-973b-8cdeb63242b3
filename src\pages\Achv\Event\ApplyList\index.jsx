import { useEffect, useState, useRef, forwardRef, useImperativeHandle } from 'react';
import { Table, Button, Space, Form, Input, Modal, Row, Col, Affix, Select, Descriptions, message, Image } from 'antd';

import ModalForm from '@/components/ModalForm';

import { useTableData } from '@/hook/useTableData';
import { useRouterLink } from '@/hook/useRouter';
import {
	pageEventJoin as getTablePageData,
	exportEventJoin as exportTableData,
	statistics as dataStatistics,
	auditEventJoin,
	importJoin as importTableData,
	updateEventSeatImage,
} from '@/api/Achv/Event/ApplyList/index';
import { getEvent } from '@/api/Achv/Event/index';
import dayjs from 'dayjs';

import UploadFile from '@/components/UploadFile';

// 活动报名审核状态
export const applyAuditStatusData = [
	{
		label: '全部',
		value: null,
		countName: 'allNumber',
	},
	{
		label: '待审核',
		value: 1,
		countName: 'auditNumber',
	},
	{
		label: '已通过',
		value: 3,
		countName: 'passNumber',
	},
	{
		label: '不通过',
		value: 2,
		countName: 'notPassNumber',
	},
];
// 活动报名审核状态文字列表
export const applyAuditStatusTextList = ['', '待审核', '不通过', '已通过'];

const Index = (props = {}) => {
	const ModalFormRef = useRef();
	const linkToPath = props.linkToPath || '/newAchv/event';
	const { linkTo, openNewTab, searchParams } = useRouterLink();

	const eventId = searchParams.get('eventId');
	const status = searchParams.get('auditStatus');
	const [auditStatus, setAuditStatus] = useState(status === 'null' ? null : status - 0 || 1);
	// 图片预览
	const [seatImgVisible, setSeatImgVisible] = useState(false);

	const { form, dataSource, pagination, changePage, exportData, onReset, onSearch, getTableData, importData, ImportModal } = useTableData({
		params: {
			eventId,
			auditStatus,
		},
		getTablePageData,
		exportTableData,
		importTableData,
		getPageResult: (result) => {
			result?.records?.forEach((ov) => {
				try {
					const additionalInfoJson = JSON.parse(ov.additionalInfoJson);
					additionalInfoJson.forEach((oov) => {
						ov[oov.keyName] = oov.type !== 4 ? oov.value : (oov.value || []).join('、');
					});
				} catch (error) {}
			});

			return Promise.resolve(result);
		},
		pageCallback: (paramsData) => {
			getStatisticsData({ ...paramsData });
		},
	});

	// 获取统计数据
	const [statistics, setStatistics] = useState({});
	const getStatisticsData = (paramsData) => {
		delete paramsData.auditStatus;
		delete paramsData.pageSize;
		delete paramsData.pageNum;
		delete paramsData.tempArea;
		dataStatistics(paramsData).then((res) => {
			setStatistics(res.data || {});
		});
	};

	// 活动标题
	const [title, setTitle] = useState('');

	// 动态表格项
	const [columnList, setColumnList] = useState([]);

	const [detail, setDetail] = useState({});

	const getDetail = () => {
		getEvent({ id: eventId }).then((res) => {
			const resData = res.data;
			console.log('🚀 ~ getEvent ~ resData:', resData);
			if (resData) {
				const { title = '', enrollDesignJson } = resData;
				setTitle(title);
				setDetail(resData);
				if (enrollDesignJson) {
					try {
						setColumnList(
							(JSON.parse(enrollDesignJson) || []).map((ov) => {
								return {
									...ov,
									dataIndex: ov.keyName,
									title: ov.lable,
								};
							})
						);
					} catch (error) {
						console.log(error);
					}
				}
			}
		});
	};

	useEffect(() => {
		if (eventId) {
			getDetail();
		}
	}, [eventId]);

	// 多选
	const [checkedList, setCheckedList] = useState([]);

	// 批量审核
	const batchAudit = () => {
		Modal.confirm({
			title: '批量审核',
			content: '确认操作当前选择的数据吗？',
			centered: true,
			closable: true,
			footer: (
				<div className="flex align-center justify-end margin-top-40">
					<Button
						className="margin-left-16"
						color="pink"
						onClick={() => {
							ModalFormRef.current.setOpen(true);
							ModalFormRef.current.setTitle('不通过原因');
							Modal.destroyAll();
						}}
					>
						批量不通过
					</Button>
					<Button
						className="margin-left-16"
						type="primary"
						onClick={() => {
							handelAudit({
								ids: checkedList,
								status: 3,
							});
							Modal.destroyAll();
						}}
					>
						批量通过
					</Button>
				</div>
			),
		});
	};

	// 审核
	const [auditId, setAuditId] = useState('');
	const onAudit = (record) => {
		let infoList = [];

		if (record) {
			setAuditId(record.id);
			infoList = columnList.map((ov) => {
				let children = record[ov.keyName];

				if (children && [5, 7].includes(ov.type)) {
					children = (
						<div
							className="a color-165dff"
							onClick={() => {
								openNewTab(record[ov.keyName]);
							}}
						>
							{children.split('/').pop()}
						</div>
					);
				} else if (children && ov.type === 6) {
					children = <Image src={record[ov.keyName]} width={120} />;
				}
				return {
					key: ov.keyName,
					label: (
						<div className="max-width-300" title={ov.title}>
							{ov.title}
						</div>
					),
					children,
				};
			});
			if (record.auditStatus === 2) {
				infoList.push({
					key: `key-${infoList.length}`,
					label: '备注',
					children: `拒绝原因: ${record.reason || '无'}`,
				});
			}
		}
		Modal.confirm({
			title: '报名资料',
			content: <Descriptions className="margin-top-20" title="" column={1} items={infoList} />,
			centered: true,
			closable: true,
			width: 800,
			footer: (
				<div className="flex align-center justify-end margin-top-40">
					<Button
						type="default"
						onClick={() => {
							ModalFormRef.current.setOpen(true);
							ModalFormRef.current.setTitle('不通过原因');
							Modal.destroyAll();
						}}
					>
						不通过
					</Button>
					<Button
						className="margin-left-16"
						type="primary"
						onClick={() => {
							handelAudit({
								ids: [record.id],
								status: 3,
							});
							Modal.destroyAll();
						}}
					>
						通过
					</Button>
				</div>
			),
			onCancel: () => {
				setAuditId('');
			},
		});
	};

	// 审核操作
	const handelAudit = (params) => {
		auditEventJoin(params).then(() => {
			message.success('操作成功');
			getTableData();
			if (auditId) {
				setAuditId('');
			} else {
				setCheckedList([]);
			}
		});
	};

	return (
		<div className="">
			<div className="flex flex-shrink align-center margin-bottom-4 height-48 font-size-22 font-weight-500 color-1d2129">
				<Space size={10}>
					<div className="a color-86909c" onClick={() => linkTo(linkToPath)}>
						活动管理
					</div>
					<div className="color-86909c">/</div>
					<div className="a max-width-200 color-86909c text-cut" title={title} onClick={() => linkTo(`${linkToPath}/detail?id=${eventId}`)}>
						{title}
					</div>
					<div className="color-86909c">/</div>
					<div>报名管理</div>
				</Space>
			</div>
			{/* Tabs 开始 */}
			<Affix offsetTop={0}>
				<div className="flex align-center justify-between padding-24 border-radius-8 bg-color-ffffff border-solid-bottom-f2f3f5">
					<div className="flex align-center">
						{applyAuditStatusData.map((ov, oi) => (
							<div
								key={oi}
								className={`a margin-right-40 font-size-18 font-weight-500 ${auditStatus === ov.value ? 'color-165dff' : ''}`}
								onClick={() => setAuditStatus(ov.value)}
							>
								{ov.label}
								{statistics[ov.countName] > 0 ? `（${statistics[ov.countName]}）` : ''}
							</div>
						))}
					</div>
					<Space size={16}>
						{detail.eventSeatImageUrl ? (
							<Button
								onClick={() => {
									setSeatImgVisible(true);
								}}
							>
								座位预览
							</Button>
						) : (
							''
						)}

						<UploadFile
							accept={'.png,.jpg,.jpeg'}
							onChange={(url) => {
								updateEventSeatImage({
									id: eventId,
									eventSeatImageUrl: url,
									seatArrangeStatus: 1,
								}).then(() => {
									getDetail();
								});
							}}
						>
							<Button>座位图上传</Button>
						</UploadFile>

						<Button onClick={importData}>批量导入</Button>
						<Button type="primary" onClick={exportData}>
							批量导出
						</Button>
						<Button disabled={checkedList.length === 0} onClick={batchAudit}>
							批量审核{checkedList.length ? `(${checkedList.length})` : ''}
						</Button>
					</Space>
				</div>
			</Affix>
			{/* Tabs 结束 */}
			<div className="flex-sub margin-top-18 padding-24 border-radius-8 bg-color-ffffff">
				{/* 筛选条件 开始 */}
				<div className="flex justify-between margin-bottom-18">
					<div className="flex-sub">
						<Form form={form} labelAlign="right" layout="inline">
							<Row className="width-100per" gutter={[12, 12]}>
								<Col span={8}>
									<Form.Item name="name" label="姓名">
										<Input placeholder="请输入姓名" />
									</Form.Item>
								</Col>
								<Col span={8}>
									<Form.Item name="signInStatus" label="签到状态">
										<Select
											options={[
												{ label: '已签到', value: 1 },
												{ label: '未签到', value: 0 },
											]}
											placeholder="请选择签到状态"
										/>
									</Form.Item>
								</Col>
							</Row>
						</Form>
					</div>
					<div className="margin-left-12 padding-left-12 border-solid-left-e5e6eb">
						<Space size={16}>
							<Button onClick={onReset}>重置</Button>
							<Button type="primary" onClick={onSearch}>
								查询
							</Button>
						</Space>
					</div>
				</div>
				{/* 筛选条件 结束 */}
				{/* 表格 开始 */}
				<Table
					rowKey="id"
					dataSource={dataSource}
					pagination={pagination}
					onChange={changePage}
					scroll={{ x: 'max-content' }}
					rowSelection={{
						selectedRowKeys: checkedList,
						onChange: setCheckedList,
					}}
				>
					<Table.Column
						title="序号"
						key="index"
						fixed="left"
						width={60}
						align="center"
						render={(_, __, index) => {
							return index + 1;
						}}
					/>
					{columnList.map((ov) => {
						return <Table.Column key={ov.dataIndex} {...ov} />;
					})}
					<Table.Column title="邀约人" align="center" dataIndex="inviterUserName" render={(text) => text || '--'} />
					<Table.Column title="报名时间" dataIndex="applyTime" align="center" />

					<Table.Column title="座位" dataIndex="seatDesc" align="center" />
					<Table.Column
						title="审核状态"
						dataIndex="auditStatus"
						align="center"
						fixed="right"
						render={(text) => {
							return (
								<div className={`tag-status-small-${['', 'warning', 'error', 'primary'][text]}`}>
									{applyAuditStatusTextList[text]}
								</div>
							);
						}}
					/>
					<Table.Column
						title="签到状态"
						dataIndex="signInStatus"
						align="center"
						fixed="right"
						render={(text) => {
							return <div className={`tag-status-small-${['warning', 'primary'][text]}`}>{['未签到', '已签到'][text]}</div>;
						}}
					/>
					<Table.Column
						title="操作"
						key="option"
						dataIndex="option"
						align="center"
						fixed="right"
						render={(_, record) => {
							return (
								<>
									<Button type="link" size="small" onClick={() => onAudit(record)}>
										详情/审核
									</Button>
								</>
							);
						}}
					/>
				</Table>
				{/* 表格 结束 */}
			</div>

			{/* 弹窗 开始 */}
			<ModalForm
				ref={ModalFormRef}
				onOk={({ reason }) =>
					handelAudit({
						ids: auditId ? [auditId] : checkedList,
						reason,
						status: 2,
					})
				}
				onCancel={() => {
					console.log(1);
					setAuditId(null);
				}}
				FormComp={(props) => <ConrirmForm ref={props.FormCompRef} />}
			/>

			{/* 弹窗 结束 */}

			{/* 导入 开始 */}
			<ImportModal
				tplUrl={`${window.location.origin}${import.meta.env.VITE_BASE_PATH ? import.meta.env.VITE_BASE_PATH : '/'}活动报名导入模板.xlsx`}
				tplName={`活动报名导入模板${dayjs().format('YYYY_MM_DD_HH_mm_ss')}.xlsx`}
				customParams={{ eventId }}
			/>
			{/* 导入 结束 */}

			{/* 座位图片预览 开始 */}

			<Image
				width={200}
				style={{
					display: 'none',
				}}
				src={detail.eventSeatImageUrl}
				preview={{
					visible: seatImgVisible,
					src: detail.eventSeatImageUrl,
					onVisibleChange: (value) => {
						setSeatImgVisible(value);
					},
				}}
			/>
			{/* 座位图片预览 结束 */}
		</div>
	);
};

// 询问表单
const ConrirmForm = forwardRef((props = {}, ref) => {
	const [form] = Form.useForm();

	useImperativeHandle(ref, () => {
		return {
			onCancel: () => Promise.resolve(),
			onOk: () => {
				return new Promise((resolve) => {
					form.validateFields().then((values) => {
						resolve({ ...values });
					});
				});
			},
		};
	});

	return (
		<Form form={form} labelCol={{ style: { width: '100px' } }}>
			<Form.Item label="原因" name="reason" rules={[{ required: true, message: '请输入不通过原因' }]}>
				<Input.TextArea rows={4} placeholder="请输入不通过原因" />
			</Form.Item>
		</Form>
	);
});

export default Index;
