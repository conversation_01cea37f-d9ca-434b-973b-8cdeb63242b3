import { useEffect, useState, useRef, forwardRef, useImperativeHandle } from 'react';
import { useRouterLink } from '@/hook/useRouter';

import { Button, Input, Affix, Anchor, message, Form, Modal, Image, Space, Checkbox, Radio } from 'antd';
import { EditOutlined, ClockCircleOutlined } from '@ant-design/icons';

import ModalForm from '@/components/ModalForm';

import { getEvent, auditEvent } from '@/api/Achv/Event/index';

import { releaseTypeTextList, releasePlatformTextList, showStatusTextList, auditStatusTextList, eventStatusTextList } from '@/pages/Achv/config';

const Index = (props = {}) => {
	const ModalFormRef = useRef();
	const { linkTo, searchParams } = useRouterLink();
	const id = searchParams.get('id') || '';
	const fromList = !!searchParams.get('fromList');

	const [detail, setDetail] = useState({});

	// 跳转地址
	const linkToPath = props.linkToPath || `/newAchv/event`;

	// 议程添加方式
	const [agendaType, setAgendaType] = useState(2);

	// 获取详情
	const getDetail = () => {
		if (id) {
			getEvent({ id }).then((res) => {
				const resData = res.data || {};

				// 报名表单处理
				if (resData.enrollDesignJson) {
					try {
						resData.enrollDesignJson = JSON.parse(resData.enrollDesignJson);
					} catch (error) {
						resData.enrollDesignJson = [];
					}
				}

				// 议程处理
				if (resData.agendaJson) {
					try {
						resData.agendaJson = JSON.parse(resData.agendaJson);
					} catch (error) {
						resData.agendaJson = [];
					}
				}

				if (resData.agendaUrl) {
					setAgendaType(1);
				}
				setDetail(resData);
			});
		}
	};

	// 通过审核
	const handlePass = () => {
		Modal.confirm({
			title: '需求审核',
			content: `是否通过审核？`,
			onOk() {
				handleAudit({ status: 3 });
			},
		});
	};

	// 撤销审核
	const handleRevoke = () => {
		Modal.confirm({
			title: '撤销审核',
			content: `是否撤销审核结果？`,
			onOk() {
				handleAudit({ status: 1 });
			},
		});
	};

	// 审核操作
	const handleAudit = (values) => {
		auditEvent({ ...values, id }).then(() => {
			message.success('操作成功');

			setTimeout(() => {
				linkTo(-1);
			}, 500);
		});
	};

	useEffect(() => {
		getDetail();
	}, []);

	return (
		<div className="">
			<div className="flex flex-shrink align-center margin-bottom-4 height-48 font-size-22 font-weight-500 color-1d2129">
				<Space size={10}>
					<div className="a color-86909c" onClick={() => linkTo(fromList ? -1 : linkToPath)}>
						活动管理
					</div>
					<div className="color-86909c">/</div>
					<div>活动详情</div>
				</Space>
			</div>
			<div id="section1"></div>
			{/* Tabs & 功能按钮 开始 */}
			<Affix>
				<div className="height-66 flex align-center justify-between bg-color-ffffff border-radius-8 padding-lr-24 border-solid-bottom-f2f3f5">
					<div className="flex align-center justify-between color-1d2129 line-height-26 font-size-18 font-weight-500">
						<Anchor
							affix={false}
							rootClassName="custom-anchor-box"
							direction="horizontal"
							replace
							targetOffset={80}
							items={[
								{
									key: 'section1',
									href: '#section1',
									title: <div className="margin-right-40 font-size-16 font-weight-500">基本信息</div>,
								},
								{
									key: 'section2',
									href: '#section2',
									title: <div className="margin-right-40 font-size-16 font-weight-500">活动描述</div>,
								},
								{
									key: 'section3',
									href: '#section3',
									title: <div className="margin-right-40 font-size-16 font-weight-500">报名表单</div>,
								},
								{
									key: 'section4',
									href: '#section4',
									title: <div className="margin-right-40 font-size-16 font-weight-500">活动议程</div>,
								},
								{
									key: 'section5',
									href: '#section5',
									title: <div className="margin-right-40 font-size-16 font-weight-500">关联账号</div>,
								},
							].filter((ov) => ov.key !== 'section5' || detail.releaseType === 2)}
						/>
					</div>
					<div className="flex align-center justify-between color-1d2129 line-height-26 font-size-18 font-weight-500">
						{detail.auditStatus === 1 ? (
							<>
								<Button
									type="primary"
									danger
									ghost
									onClick={() => {
										ModalFormRef.current.setOpen(true);
										ModalFormRef.current.setTitle('不通过原因');
									}}
									className="height-40 width-104"
								>
									不通过
								</Button>
								<Button type="primary" onClick={handlePass} className="margin-left-20 height-40 width-104">
									通过审核
								</Button>
							</>
						) : (
							<Button onClick={handleRevoke} className="margin-left-20 height-40 width-104">
								撤销审核
							</Button>
						)}
					</div>
				</div>
			</Affix>
			{/* Tabs & 功能按钮 结束 */}

			{/* 基本信息 开始 */}
			<div className="margin-top-20 padding-20 border-radius-8 bg-color-ffffff">
				<div className="flex align-center justify-between margin-bottom-20">
					<div className="font-size-18 line-height-26 font-weight-500">基本信息</div>
					<Button
						type="link"
						icon={<EditOutlined />}
						onClick={() => {
							linkTo(`${linkToPath}/curd?id=${id}#section1`);
						}}
					>
						编辑信息
					</Button>
				</div>
				<div className="flex align-center justify-start font-size-14 line-height-22 padding-left-16 margin-bottom-20">
					<div className="color-86909c margin-right-4">发布状态：</div>
					<div className="">
						<div className={`tag-status-small-${['default', 'warning', 'error', 'primary'][detail.auditStatus]}  `}>
							{auditStatusTextList[detail.auditStatus || ''] || '--'}
						</div>
					</div>
				</div>
				<div className="flex align-center justify-start font-size-14 line-height-22 padding-left-16 margin-bottom-20">
					<div className="color-86909c margin-right-4">活动状态：</div>
					<div className="">
						<div className={`tag-status-small-${['', 'warning', 'primary', 'default'][detail.eventStatus]}  `}>
							{eventStatusTextList[detail.eventStatus || ''] || '--'}
						</div>
					</div>
				</div>
				{detail.auditStatus === 2 && (
					<div className="flex align-center justify-start font-size-14 line-height-22 padding-left-16 margin-bottom-20">
						<div className="color-86909c margin-right-4">拒绝原因：</div>
						<div className="">{detail.reason || '无'}</div>
					</div>
				)}
				<div className="flex align-center justify-start font-size-14 line-height-22 padding-left-16 margin-bottom-20">
					<div className="color-86909c margin-right-4">活动名称：</div>
					<div className="">{detail.title || '--'}</div>
				</div>
				<div className="flex align-center justify-start font-size-14 line-height-22 padding-left-16 margin-bottom-20">
					<div className="color-86909c margin-right-4">活动时间：</div>
					<Space className="">
						<div>{(detail.startTime || '').slice(0, 16)}</div>
						<div>至</div>
						<div>{(detail.endTime || '').slice(0, 16)}</div>
					</Space>
				</div>
				<div className="flex align-center justify-start font-size-14 line-height-22 padding-left-16 margin-bottom-20">
					<div className="color-86909c margin-right-4">活动地址：</div>
					<div className="">{detail.resolveAddress || '--'}</div>
				</div>
				<div className="flex align-center justify-start font-size-14 line-height-22 padding-left-16 margin-bottom-20">
					<div className="color-86909c margin-right-4">显示地址：</div>
					<div className="">{detail.address || '--'}</div>
				</div>
				<div className="flex align-center justify-start font-size-14 line-height-22 padding-left-16 margin-bottom-20">
					<div className="color-86909c margin-right-4">报名人数：</div>
					<div className="">{detail.maximumApplicationNumber === null ? '不限人数' : `限制${detail.maximumApplicationNumber}人报名`}</div>
				</div>
				<div className="flex align-center justify-start font-size-14 line-height-22 padding-left-16 margin-bottom-20">
					<div className="color-86909c margin-right-4">报名审核：</div>
					<div className="">{detail.applicationAuditStatus === 1 ? '需要审核' : '不需要审核'}</div>
				</div>
				<div className="flex align-center justify-start font-size-14 line-height-22 padding-left-16 margin-bottom-20">
					<div className="color-86909c margin-right-4">最近报名：</div>
					<div className="">{detail.profilePictureShowStatus === 1 ? '使用加热数据' : '使用真实数据'}</div>
				</div>
				<div className="flex justify-start font-size-14 line-height-22 padding-left-16 margin-bottom-20">
					<div className="color-86909c margin-right-4">活动封面：</div>
					<div className="">
						{(detail.coverImageUrl && <Image className="border-radius-8 vertical-align-top" height={100} src={detail.coverImageUrl} />) ||
							'--'}
					</div>
				</div>
				<div className="flex align-center justify-start font-size-14 line-height-22 padding-left-16 margin-bottom-20">
					<div className="color-86909c margin-right-4">微信二维码：</div>
					<div className="">
						{(detail.qrCode && <Image className="border-radius-8 vertical-align-top" height={100} src={detail.qrCode} />) || '--'}
					</div>
				</div>
				<div className="flex align-center justify-start font-size-14 line-height-22 padding-left-16 margin-bottom-20">
					<div className="color-86909c margin-right-4">二维码说明：</div>
					<div className="">{detail.qrCodeTips || '--'}</div>
				</div>
				<div className="flex align-center justify-start font-size-14 line-height-22 padding-left-16 margin-bottom-20">
					<div className="color-86909c margin-right-4">分享封面：</div>
					<div className="">
						{(detail.shareImage && (
							<Image
								className="border-radius-8 vertical-align-top"
								height={100}
								src={detail.shareImage}
								fallback="https://gbac-produce.oss-cn-shenzhen.aliyuncs.com/static-assets/gbac-achv-applet/Public/default-achv-cover.png"
							/>
						)) ||
							'--'}
					</div>
				</div>
				<div className="flex align-center justify-start font-size-14 line-height-22 padding-left-16 margin-bottom-20">
					<div className="color-86909c margin-right-4">排序：</div>
					<div className="">{detail.rankingNum || '--'}</div>
				</div>
				<div className="flex align-center justify-start font-size-14 line-height-22 padding-left-16 margin-bottom-20">
					<div className="color-86909c margin-right-4">显示状态：</div>
					<div className="">
						<div className={`tag-status-${['error', 'primary'][detail.showStatus]}`}>{showStatusTextList[detail.showStatus] || '--'}</div>
					</div>
				</div>
				<div className="flex align-center justify-start font-size-14 line-height-22 padding-left-16 margin-bottom-20">
					<div className="color-86909c margin-right-4">发布类型：</div>
					<div className="">
						<div className={`tag-status-${['', 'primary', 'warning', '700eb2', 'success', 'error'][detail.releaseType]}`}>
							{releaseTypeTextList[detail.releaseType] || '--'}
						</div>
					</div>
				</div>
				<div className="flex align-center justify-start font-size-14 line-height-22 padding-left-16 margin-bottom-20">
					<div className="color-86909c margin-right-4">发布平台：</div>
					<div className="">
						<div className={`tag-status-${['', 'primary', 'success', '700eb2'][detail.releasePlatform]}`}>
							{releasePlatformTextList[detail.releasePlatform] || '--'}
						</div>
					</div>
				</div>
			</div>
			{/* 基本信息 结束 */}

			{/* 活动描述 开始 */}
			<div id="section2"></div>
			<div className="margin-top-20 padding-20 border-radius-8 bg-color-ffffff">
				<div className="flex align-center justify-between margin-bottom-20">
					<div className="font-size-18 line-height-26 font-weight-500">活动描述</div>
					<Button
						type="link"
						icon={<EditOutlined />}
						onClick={() => {
							linkTo(`${linkToPath}/curd?id=${id}#section2`);
						}}
					>
						编辑信息
					</Button>
				</div>
				<div className="flex justify-start font-size-14 line-height-22 padding-left-16 margin-bottom-20">
					<div className="color-86909c margin-right-4">活动描述：</div>
					<div className="flex-sub">
						<div
							className="font-size-14 line-height-24 pre-wrap rich-box"
							dangerouslySetInnerHTML={{
								__html: detail.introduce || '--',
							}}
						></div>
					</div>
				</div>
				<div className="flex justify-start font-size-14 line-height-22 padding-left-16 margin-bottom-20">
					<div className="color-86909c margin-right-4">展示图片：</div>
					<div className="flex-sub">
						<Space wrap>
							{(detail.showPictures &&
								detail.showPictures.split(',').map((ov, oi) => {
									return <Image key={oi} width={120} height={120} src={ov} />;
								})) ||
								'--'}
						</Space>
					</div>
				</div>
			</div>
			{/* 活动描述 结束 */}

			{/* 报名表单 开始 */}
			<div id="section3"></div>
			<div className="margin-top-20 padding-20 border-radius-8 bg-color-ffffff">
				<div className="flex align-center justify-between margin-bottom-20">
					<div className="font-size-18 line-height-26 font-weight-500">报名表单</div>
					<Button
						type="link"
						icon={<EditOutlined />}
						onClick={() => {
							linkTo(`${linkToPath}/curd?id=${id}#section3`);
						}}
					>
						编辑信息
					</Button>
				</div>
				<Space direction="vertical">
					{(detail.enrollDesignJson || []).map((ov, oi) => {
						return <FormItem key={oi} {...ov} />;
					})}
				</Space>
			</div>
			{/* 报名表单 结束 */}

			{/* 活动议程 开始 */}
			<div id="section4"></div>
			<div className="margin-top-20 padding-20 border-radius-8 bg-color-ffffff">
				<div className="flex align-center justify-between margin-bottom-20">
					<div className="font-size-18 line-height-26 font-weight-500">活动议程</div>
					<Button
						type="link"
						icon={<EditOutlined />}
						onClick={() => {
							linkTo(`${linkToPath}/curd?id=${id}#section4`);
						}}
					>
						编辑信息
					</Button>
				</div>

				<div className="flex align-center justify-start font-size-14 line-height-22 padding-left-16 margin-bottom-20">
					<div className="color-86909c">议程类型：</div>
					<div className="">{agendaType === 1 ? '上传图片' : '自定义'}</div>
				</div>
				{agendaType === 1 && (
					<div className="flex justify-start font-size-14 line-height-22 padding-left-16 margin-bottom-20">
						<div className="color-86909c">议程图片：</div>
						<div className="">
							{(detail.agendaUrl && <Image className="border-radius-8 vertical-align-top" height={100} src={detail.agendaUrl} />) ||
								'--'}
						</div>
					</div>
				)}
				{/* 议程列表 开始 */}
				{agendaType === 2 && detail.agendaJson && detail.agendaJson.length > 0 && (
					<div className="margin-top-12 padding-8 border-radius-2 bg-color-f7f9fc">
						{detail.agendaJson.map((ahemda, index) => {
							return (
								<div key={index} className="flex padding-tb-8">
									<div className="flex align-center margin-lr-8 padding-lr-12 height-32 border-radius-2 border-e5e6eb bg-color-ffffff">
										<div>{ahemda.startTime}</div>
										<div className="margin-lr-20 color-c9cdd4">-</div>
										<div>{ahemda.endTime}</div>
										<ClockCircleOutlined
											style={{
												marginLeft: '30px',
												fontSize: '16px',
												color: '#C9CDD4',
											}}
										/>
									</div>
									<div className="flex-sub">
										<div className="flex align-center justify-between margin-lr-8 padding-lr-12 height-32 border-radius-2 border-e5e6eb bg-color-ffffff">
											{ahemda.agendaTheme}
										</div>
										{ahemda.eventAgendaGuestsList.map((item, key) => {
											return (
												<div
													key={key}
													className="margin-top-8 flex align-center justify-between margin-lr-8 padding-lr-12 height-32 border-radius-2 border-e5e6eb bg-color-ffffff color-86909c"
												>
													{[item.name, item.enterpriseName, item.positionTitle].filter((item) => item).join(' / ')}
												</div>
											);
										})}
									</div>
								</div>
							);
						})}
					</div>
				)}
				{/* 议程列表 结束 */}
			</div>
			{/* 活动议程 结束 */}

			{/* 关联账号 开始 */}
			<div id="section5"></div>
			<div className="bg-color-ffffff border-radius-8 padding-lr-24 padding-tb-20 margin-tb-16">
				<div className="flex align-center justify-between margin-bottom-20">
					<div className="font-size-18 line-height-26 font-weight-500">关联账号</div>
					<Button
						type="link"
						icon={<EditOutlined />}
						onClick={() => {
							linkTo(`${linkToPath}/curd?id=${id}#section5`);
						}}
					>
						编辑信息
					</Button>
				</div>
				<div className="flex align-center justify-start font-size-14 line-height-22 padding-left-16 margin-bottom-20">
					<div className="margin-right-24 width-90 text-align-right color-86909c">管理人员：</div>
					<div className="flex align-start justify-start flex-wrap color-165dff">
						{(detail.adminStaffList || []).map((ov) => ov.adminUserName).join('、') || '--'}
					</div>
				</div>
				{detail.releaseType === 2 && (
					<div className="flex align-center justify-start font-size-14 line-height-22 padding-left-16 margin-bottom-20">
						<div className="margin-right-24 width-90 text-align-right color-86909c">关联科转号：</div>
						<div className="flex align-start justify-start flex-wrap">
							{(detail.ttChannelsList || []).map((item) => {
								return (
									<div key={item.id} className="flex align-center justify-start margin-right-20">
										<img src={item.avatarUrl} className="width-20 height-20 border-radius-10 bg-color-e5e6eb" />
										<div className="font-size-14 color-165dff margin-left-6 line-height-20">{item.accountName || ''}</div>
									</div>
								);
							})}
							{(detail.ttChannelsList || []).length === 0 && '--'}
						</div>
					</div>
				)}
			</div>
			{/* 关联账号 结束 */}

			{/* 询问弹窗 开始 */}
			<ModalForm ref={ModalFormRef} onOk={handleAudit} FormComp={(props) => <ConrirmForm ref={props.FormCompRef} />} />
			{/* 询问弹窗 结束 */}
		</div>
	);
};

// 询问表单
const ConrirmForm = forwardRef((props = {}, ref) => {
	const [form] = Form.useForm();

	useImperativeHandle(ref, () => {
		return {
			onCancel: () => Promise.resolve(),
			onOk: () => {
				return new Promise((resolve) => {
					form.validateFields().then((values) => {
						resolve(values);
					});
				});
			},
		};
	});

	return (
		<Form form={form} labelCol={{ style: { width: '100px' } }} initialValues={{ status: 2 }}>
			<Form.Item hidden name="status">
				<Input />
			</Form.Item>
			<Form.Item label="原因" name="reason" rules={[{ required: true, message: '请输入不通过原因' }]}>
				<Input.TextArea rows={4} placeholder="请输入不通过原因" />
			</Form.Item>
		</Form>
	);
});

// 表单项
export const FormItem = (props = {}) => {
	return (
		<Space direction="vertical" className="padding-lr-32">
			<Space align="start">
				<Checkbox className="line-height-32" checked={props.required} readOnly={true}>
					必填
				</Checkbox>
				<Input className="width-200" value={props.lable} readOnly={true}></Input>
				{[1, 5, 6, 7].includes(props.type) && (
					<Input className="width-400" value={props.placeholder} placeholder="暂无配置" readOnly={true}></Input>
				)}
				{props.type === 2 && (
					<Input.TextArea
						className="width-400"
						rows={4}
						value={props.placeholder || '请输入' + props.lable}
						readOnly={true}
					></Input.TextArea>
				)}
			</Space>
			{props.type === 3 && <Radio.Group className="margin-left-70" disabled options={props.options} />}
			{props.type === 4 && <Checkbox.Group className="margin-left-70" disabled options={props.options} />}
		</Space>
	);
};

export default Index;
