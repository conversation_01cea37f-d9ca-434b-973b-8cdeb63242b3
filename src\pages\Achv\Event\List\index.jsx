import { useState } from 'react';
import { <PERSON>, Button, Popconfirm, Space, Switch, Affix, message, Form, Select, Input, Row, Col, Image, Tooltip, Tag } from 'antd';
import { QuestionCircleOutlined } from '@ant-design/icons';
import { useTableData } from '@/hook/useTableData';
import { useRouterLink } from '@/hook/useRouter';
import QrCode from '@/components/QrCode';
import {
	pageEvent as getTablePageData,
	delEvent as delTableItemData,
	updateRankingNum as batchUpdateSort,
	updateEventTopUpStatus,
	updateEventValidStatus,
	getEventAuditNum,
} from '@/api/Achv/Event/index';

import { auditStatusData, auditStatusTextList, eventStatusData, eventStatusTextList } from '@/pages/Achv/config';
import './index.scss';

const Index = (props = {}) => {
	const { linkTo, searchParams } = useRouterLink();
	const linkToPath = props.linkToPath || '/newAchv/event';

	const status = searchParams.get('auditStatus');
	const [auditStatus, setAuditStatus] = useState(status === null ? 3 : status - 0 || '');

	const { form, dataSource, pagination, changePage, delTableData, getTableData, onReset, onSearch, SortInput } = useTableData({
		params: { auditStatus, releaseType: props.releaseType || undefined },
		getTablePageData,
		delTableItemData,
		batchUpdateSort,
		pageCallback: (paramsData = {}) => {
			getStatisticsData({ ...paramsData });
		},
	});

	// 获取统计数据
	const [statistics, setStatistics] = useState({});
	const getStatisticsData = (paramsData) => {
		delete paramsData.auditStatus;
		delete paramsData.pageSize;
		delete paramsData.pageNum;
		delete paramsData.tempArea;
		getEventAuditNum(paramsData).then((res) => {
			setStatistics(res.data || {});
		});
	};

	// 修改显示状态
	const showStatusChange = (record) => {
		updateEventValidStatus({ id: record.id, eventValidStatus: record.eventValidStatus == 1 ? 0 : 1 }).then(() => {
			message.success('操作成功');
			getTableData();
		});
	};

	// 修改推荐状态
	const recommendStatusChange = (record) => {
		updateEventTopUpStatus({ id: record.id, topUpStatus: record.topUpStatus == 1 ? 0 : 1 }).then(() => {
			message.success('操作成功');
			getTableData();
		});
	};

	return (
		<div className="">
			<div className="flex flex-shrink align-center margin-bottom-4 height-48 font-size-22 font-weight-500 color-1d2129">活动管理</div>

			{/* Tabs & 功能按钮 开始 */}
			<Affix offsetTop={0}>
				<div className="flex align-center justify-between padding-24 border-radius-8 bg-color-ffffff border-solid-bottom-f2f3f5">
					<div className="flex align-center">
						{auditStatusData.map((ov) => (
							<div
								key={ov.value}
								className={`a margin-right-40 font-size-18 font-weight-500 ${auditStatus === ov.value ? 'color-165dff' : ''}`}
								onClick={() => setAuditStatus(ov.value)}
							>
								{ov.label}
								{statistics[ov.countName] > 0 ? `（${statistics[ov.countName]}）` : ''}
							</div>
						))}
					</div>
					<Space size={16}>
						<Button
							type="primary"
							onClick={() => {
								linkTo(`${linkToPath}/curd`);
							}}
						>
							创建活动
						</Button>
					</Space>
				</div>
			</Affix>
			{/* Tabs & 功能按钮 结束 */}

			<div className="flex-sub margin-top-18 padding-24 border-radius-8 bg-color-ffffff">
				{/* 筛选条件 开始 */}
				<div className="flex justify-between margin-bottom-18">
					<div className="flex-sub">
						<Form form={form} labelAlign="right" layout="inline">
							<Row className="width-100per" gutter={[12, 12]}>
								<Col span={8}>
									<Form.Item name="title" label="活动名称">
										<Input placeholder="请输入活动名称" />
									</Form.Item>
								</Col>
								<Col span={8}>
									<Form.Item name="eventStatus" label="活动状态">
										<Select options={eventStatusData} placeholder="请选择活动状态" allowClear />
									</Form.Item>
								</Col>
							</Row>
						</Form>
					</div>
					<div className="margin-left-12 padding-left-12 border-solid-left-e5e6eb">
						<Space size={16}>
							<Button onClick={onReset}>重置</Button>
							<Button type="primary" onClick={onSearch}>
								查询
							</Button>
						</Space>
					</div>
				</div>
				{/* 筛选条件 结束 */}

				{/* 表格 开始 */}
				<Table rowKey="id" dataSource={dataSource} pagination={pagination} onChange={changePage} scroll={{ x: 'max-content' }}>
					<Table.Column
						title="序号"
						key="index"
						width={60}
						align="center"
						render={(_, __, index) => {
							return index + 1;
						}}
					/>

					<Table.Column
						title="封面"
						key="coverImageUrl"
						width={100}
						render={(_, record) => {
							return (
								<div className="position-relative border-radius-8 overflow-hidden">
									<Image
										className="vertical-align-top"
										width={100}
										src={record.coverImageUrl}
										fallback="https://gbac-produce.oss-cn-shenzhen.aliyuncs.com/static-assets/gbac-achv-applet/Public/default-achv-cover.png"
									/>
									<div
										className={`status-btn status-${record.eventStatus} position-absolute top-0 left-0 padding-lr-4 height-20 line-height-20 font-size-10 font-weight-500 color-ffffff`}
									>
										{eventStatusTextList[record.eventStatus]}
									</div>
								</div>
							);
						}}
					/>
					<Table.Column
						title="活动名称"
						dataIndex="title"
						render={(text) => (
							<div className="max-width-200 text-cut-3" title={text}>
								{text}
							</div>
						)}
					/>
					<Table.Column
						title="发布状态"
						dataIndex="auditStatus"
						align="center"
						render={(text) => {
							return (
								<div className={`tag-status-small-${['default', 'warning', 'error', 'primary'][text]}  `}>
									{auditStatusTextList[text || ''] || '--'}
								</div>
							);
						}}
					/>
					<Table.Column
						title="关联科转号"
						dataIndex="ttChannelsList"
						align="center"
						render={(text) => {
							return (text && text.map((ov) => ov.accountName).join('、')) || '--';
						}}
					/>
					<Table.Column
						title="活动时间"
						dataIndex="time"
						align="center"
						render={(_, record) => {
							return (
								<div>
									<div>{(record.startTime || '').slice(0, 16)}</div>
									<div>至</div>
									<div>{(record.endTime || '').slice(0, 16)}</div>
								</div>
							);
						}}
					/>
					<Table.Column
						title={
							<Space>
								<div>报名人数</div>
								<Tooltip title={() => <span className="color-4e5969">已报名/已审核</span>} color="#ffffff">
									<QuestionCircleOutlined />
								</Tooltip>
							</Space>
						}
						align="center"
						dataIndex="auditNumber"
						render={(_, record) => {
							return (
								<div
									className="cursor-pointer"
									onClick={() => {
										linkTo(`${linkToPath}/applyList?eventId=${record.id}`);
									}}
								>
									<span className="color-165dff">{record.applicationNumber || 0}</span>
									<span>/</span>
									<span>{record.auditNumber || 0}</span>
								</div>
							);
						}}
					/>
					<Table.Column
						title="座位编排"
						dataIndex="seatArrangeStatus"
						align="center"
						render={(text) => {
							return (
								<div
									style={{
										color: text === 1 ? '#1890ff' : '',
									}}
								>
									{['未编排', '已编排'][text]}
								</div>
							);
						}}
					/>
					<Table.Column
						title="活动二维码"
						dataIndex="QrCodeUrl"
						align="center"
						render={(_, record) => {
							return (
								<Tooltip
									overlayClassName="max-width-400"
									className="tool-tip"
									title={
										<QrCode.QRCodeUrl
											qrcodeClassName="width-100 height-100"
											qrCodeList={[
												{
													qrcodeUrl: record.eventQrcodeUrl,
													qrcodeName: '报名小程序码',
												},
												/* {
													qrcodeUrl: record.eventSignInQrcodeUrl,
													qrcodeName: '签到小程序码',
												},
												{
													qrcodeUrl: record.eventSeatQrcodeUrl,
													qrcodeName: '座位查询码',
												}, */
											].filter((ov) => ov.qrcodeUrl)}
										/>
									}
									color="#ffffff"
								>
									<Tag className="cursor-pointer" color="blue">
										查看
									</Tag>
								</Tooltip>
							);
						}}
					/>
					<Table.Column
						title="显示/隐藏"
						dataIndex="eventValidStatus"
						align="center"
						render={(_, record) => {
							return (
								<Switch
									checked={record.eventValidStatus == 1}
									disabled={record.auditStatus !== 3}
									title={record.auditStatus !== 3 ? '仅审核通过后才能操作' : ''}
									onChange={() => {
										showStatusChange(record);
									}}
								/>
							);
						}}
					/>
					<Table.Column
						title="推荐"
						dataIndex="topUpStatus"
						align="center"
						render={(_, record) => {
							return (
								<Switch
									checked={record.topUpStatus == 1}
									disabled={record.auditStatus !== 3}
									title={record.auditStatus !== 3 ? '仅审核通过后才能操作' : ''}
									onChange={() => {
										recommendStatusChange(record);
									}}
								/>
							);
						}}
					/>
					<Table.Column
						title="流量统计"
						dataIndex="viewNum"
						align="center"
						render={(text) => {
							return text || '-';
						}}
					/>
					{!props.hideSort && (
						<Table.Column
							title="排序"
							align="center"
							dataIndex="rankingNum"
							render={(_, record) => {
								return <SortInput record={record} />;
							}}
						/>
					)}
					<Table.Column
						title="操作"
						key="option"
						dataIndex="option"
						align="center"
						fixed="right"
						width="160px"
						render={(_, record) => {
							return (
								<>
									<Button type="link" size="small" onClick={() => linkTo(`${linkToPath}/detail?id=${record.id}&fromList=1`)}>
										详情/编辑
									</Button>
									<Popconfirm
										title="提示"
										description="确定删除吗？"
										onConfirm={() => {
											delTableData(record.id);
										}}
										okText="确定"
										cancelText="取消"
									>
										<Button type="link" size="small">
											删除
										</Button>
									</Popconfirm>
								</>
							);
						}}
					/>
				</Table>
				{/* 表格 结束 */}
			</div>
		</div>
	);
};

export default Index;
