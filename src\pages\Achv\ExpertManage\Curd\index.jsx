import { useEffect, useState } from 'react';
import { useRouterLink } from '@/hook/useRouter';

import { Button, Space, Form, Input, Cascader, Radio, message, Affix, Anchor, TreeSelect } from 'antd';

import UploadImg from '@/components/UploadImg';
import SelectOrg from '@/components/Achv/SelectOrg';

import { getThreeLevelData } from '@/api/common';
import {
	addExpert as addTableItemData,
	updateExpert as updateTableItemData,
	detailExpert as getTableItemData,
	listExpertIndustry,
} from '@/api/Achv/ExpertManage/index';

import { getDictData } from '@/utils/dictionary';

const Index = () => {
	const { linkTo, searchParams } = useRouterLink();
	const [form] = Form.useForm();

	const id = searchParams.get('id') || '';

	// 取消
	const onCancel = () => {
		linkTo(-1);
	};

	// 提交
	const onSubmit = () => {
		form.validateFields().then((values) => {
			const params = { ...values };
			delete params.tempArea;
			if (id) {
				updateTableItemData(params).then(() => {
					message.success('修改成功');
					setTimeout(() => {
						linkTo(-1);
					}, 500);
				});
			} else {
				addTableItemData(params).then(() => {
					message.success('添加成功');
					setTimeout(() => {
						linkTo(-1);
					}, 500);
				});
			}
		});
	};

	// 获取详情
	const getDetail = () => {
		if (id) {
			getTableItemData({ id, isEncryptionName: 0 }).then((res) => {
				const resData = res.data || {};

				// 处理区域
				resData.tempArea = [resData.provinceCode, resData.cityCode];

				// 处理行业领域
				const industryAreaList = resData.industryAreaList;
				resData.industryDisciplineList = industryAreaList.filter((ov) => ov.level === 1).map(({ id, name }) => ({ id, name }));
				resData.industryAreaList = industryAreaList.filter((ov) => ov.level === 2).map(({ id, name }) => ({ id, name }));

				form.setFieldsValue(resData);
			});
		}
	};

	useEffect(() => {
		getDetail();
	}, []);

	return (
		<div className="">
			<div className="flex flex-shrink align-center margin-bottom-4 height-48 font-size-22 font-weight-500 color-1d2129">
				<Space size={10}>
					<div className="a color-86909c" onClick={() => linkTo('/newAchv/expertManage')}>
						专家管理
					</div>
					<div className="color-86909c">/</div>
					<div>专家编辑</div>
				</Space>
			</div>

			<div id="section1"></div>
			{/* Tabs & 功能按钮 开始 */}
			<Affix style={{ zIndex: 1000, position: 'relative' }}>
				<div className="flex align-center justify-between padding-24 border-radius-8 border-solid-bottom-f2f2f2 bg-color-ffffff">
					<div className="flex align-center justify-between color-1d2129 line-height-26 font-size-18 font-weight-500">
						<Anchor
							affix={false}
							rootClassName="custom-anchor-box"
							direction="horizontal"
							replace
							targetOffset={80}
							items={[
								{
									key: 'section1',
									href: '#section1',
									title: <div className="margin-right-40 font-size-16 font-weight-500">基本信息</div>,
								},
								{
									key: 'section2',
									href: '#section2',
									title: <div className="margin-right-40 font-size-16 font-weight-500">从业经验</div>,
								},
								{
									key: 'section3',
									href: '#section3',
									title: <div className="margin-right-40 font-size-16 font-weight-500">其他信息</div>,
								},
							]}
						/>
					</div>
					<Space size={16}>
						<Button onClick={onCancel}>取消</Button>
						<Button type="primary" onClick={onSubmit}>
							保存
						</Button>
					</Space>
				</div>
			</Affix>
			{/* Tabs & 功能按钮 结束 */}

			<Form
				form={form}
				className="antd-form-box"
				labelAlign="right"
				labelCol={{ style: { width: '160px' } }}
				initialValues={{
					id: '',
					industryAreaList: [],
					industryDisciplineList: [],
				}}
			>
				<Form.Item hidden name="id">
					<Input />
				</Form.Item>
				<Form.Item hidden name="industryAreaList">
					<Input />
				</Form.Item>
				<Form.Item hidden name="industryDisciplineList">
					<Input />
				</Form.Item>

				{/* 基本信息 开始 */}
				<div className="margin-top-20 padding-20 border-radius-8 bg-color-ffffff">
					<div className="padding-bottom-30 line-height-26 font-size-18 font-weight-500 color-1d2129">基本信息</div>
					<ExpertForm1 form={form} />
				</div>
				{/* 基本信息 结束 */}

				{/* 从业经验 开始 */}
				<div id="section2"></div>
				<div className="margin-top-20 padding-20 border-radius-8 bg-color-ffffff">
					<div className="padding-bottom-30 line-height-26 font-size-18 font-weight-500 color-1d2129">从业经验</div>
					<ExpertForm2 form={form} />
				</div>
				{/* 从业经验 结束 */}

				{/* 其他信息 开始 */}
				<div id="section3"></div>
				<div className="margin-top-20 padding-20 border-radius-8 bg-color-ffffff">
					<div className="padding-bottom-30 line-height-26 font-size-18 font-weight-500 color-1d2129">其他信息</div>
					<ExpertForm3 form={form} />
				</div>
				{/* 其他信息 结束 */}
			</Form>
		</div>
	);
};

// 基本信息
const ExpertForm1 = (props = {}) => {
	const [areaOptions, setAreaOptions] = useState([]);

	useEffect(() => {
		getThreeLevelData({ level: 2 }).then((res) => {
			setAreaOptions(res.data || []);
		});
	}, []);

	return (
		<>
			<Form.Item label="头像" name="headUrl">
				<UploadImg size={3} width={140} height={140} tips="建议尺寸：200px*200px" cropperProps={{ width: 140, height: 140 }} />
			</Form.Item>
			<Form.Item label="专家姓名" name="expertName" rules={[{ required: true, message: '请输入专家姓名' }]}>
				<Input className="input-box" placeholder="请输入专家姓名" />
			</Form.Item>
			<Form.Item label="性别" name="gender">
				<Radio.Group options={getDictData('gender')} />
			</Form.Item>
			<Form.Item label="联系电话" name="phone">
				<Input className="input-box" placeholder="请输入联系电话" />
			</Form.Item>
			<Form.Item label="邮箱" name="email">
				<Input className="input-box" placeholder="请输入邮箱" />
			</Form.Item>
			<Form.Item hidden name="provinceCode">
				<Input />
			</Form.Item>
			<Form.Item hidden name="cityCode">
				<Input />
			</Form.Item>
			<Form.Item label="所属区域" name="tempArea">
				<Cascader
					className="cascader-box"
					options={areaOptions}
					placeholder="请选择所属区域"
					displayRender={(label) => label.filter((ov) => ov).join('-')}
					onChange={(e = [undefined, undefined]) => {
						props.form.setFieldValue('provinceCode', e[0]);
						props.form.setFieldValue('cityCode', e[1]);
					}}
				/>
			</Form.Item>
			<Form.Item label="AI检索标签" name="tagDesc">
				<Input className="input-box" placeholder="请输入AI检索标签，中文逗号分割 例：技术、项目" />
			</Form.Item>
		</>
	);
};

// 从业经验
const ExpertForm2 = (props = {}) => {
	return (
		<>
			<Form.Item hidden name="agencyId">
				<Input />
			</Form.Item>
			<Form.Item label="所属机构" name="agencyName">
				<SelectOrg form={props.form} keyName="agencyId" placeholder="请输入3个字以上的所属机构关键词" />
			</Form.Item>
			<Form.Item label="在职信息" name="careerInformation">
				<Input className="input-box" placeholder="请输入就职单位" />
			</Form.Item>
			<Form.Item label="职称" name="expertTitle">
				<Input className="input-box" placeholder="请输入就职单位" />
			</Form.Item>
			<Form.Item label="学历" name="education">
				<Input className="input-box" placeholder="请输入就职单位" />
			</Form.Item>
			<Form.Item label="行业学科/细分领域">
				<AreaTreeSelect form={props.form} />
			</Form.Item>
			<Form.Item label="研究方向" name="researchDirection" help="如果有多个 请用 | 隔开">
				<Input className="input-box" placeholder="请输入就职单位" />
			</Form.Item>
		</>
	);
};

// 工作经验
const ExpertForm3 = (props = {}) => {
	return (
		<>
			<Form.Item label="个人简介" name="introduction">
				<Input.TextArea rows={4} placeholder="请输入获得荣誉" />
			</Form.Item>
			<Form.Item label="代表性论文/成果" name="representativePapers">
				<Input.TextArea rows={4} placeholder="请输入社会兼职" />
			</Form.Item>
			<Form.Item label="主要荣誉/项目" name="mainHonors">
				<Input.TextArea rows={4} placeholder="请输入个人简介" />
			</Form.Item>
		</>
	);
};

// 领域树选择
const AreaTreeSelect = (props = {}) => {
	// 获取行业领域选项数据
	const [industryAreaOptions, setIndustryAreaOptions] = useState([]);
	const getIndustryAreaOptions = () => {
		listExpertIndustry().then((res) => {
			const result = res.data || [];
			result.forEach((ov) => {
				ov.label = ov.industryName;
				ov.value = ov.id;
				if (ov.parentId) {
					const find = result.find((oov) => oov.id === ov.parentId);
					if (find) {
						if (!find.children) {
							find.children = [];
						}
						find.children.push(ov);
					}
				}
			});
			setIndustryAreaOptions(result.filter((ov) => ov.parentId === null));
		});
	};

	useEffect(() => {
		getIndustryAreaOptions();
	}, []);

	// 选中的值
	const [value, setValue] = useState([]);
	const industryAreaList = Form.useWatch('industryAreaList', props.form);
	const industryDisciplineList = Form.useWatch('industryDisciplineList', props.form);

	useEffect(() => {
		setValue([...(industryAreaList || []), ...(industryDisciplineList || [])].map((ov) => ov.id));
	}, [industryAreaList, industryDisciplineList]);
	return (
		<TreeSelect
			value={value}
			treeData={industryAreaOptions}
			placeholder="请选择行业领域"
			style={{ width: '100%' }}
			dropdownStyle={{ maxHeight: 800, overflow: 'auto' }}
			allowClear
			multiple
			treeCheckable
			treeCheckStrictly
			onChange={(result) => {
				const pids = industryAreaOptions.map((ov) => ov.value);
				const industryDisciplineList = [];
				const industryAreaList = [];

				result.forEach((ov) => {
					const item = {
						id: ov.value,
						name: ov.label,
					};
					if (pids.includes(ov.value)) {
						industryDisciplineList.push(item);
					} else {
						industryAreaList.push(item);
					}
				});

				props.form.setFieldValue('industryDisciplineList', industryDisciplineList);
				props.form.setFieldValue('industryAreaList', industryAreaList);
			}}
		/>
	);
};

export default Index;
