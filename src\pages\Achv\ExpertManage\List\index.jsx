import { useState, useEffect } from 'react';
import { Table, Button, Popconfirm, Space, Form, Input, Row, Col, Affix, Cascader } from 'antd';
import SelectOrg from '@/components/Achv/SelectOrg';

import { useTableData } from '@/hook/useTableData';
import { useRouterLink } from '@/hook/useRouter';
import { useConfig } from '@/hook/Achv/useConfig';

import {
	pageExpert as getTablePageData,
	batchDelExpert as delTableItemData,
	exportExpert as exportTableData,
	importExpert as importTableData,
	updateRankingNum as batchUpdateSort,
	listExpertIndustry,
} from '@/api/Achv/ExpertManage/index';
import { getThreeLevelData } from '@/api/common';

import { brokerAuditStatusData, brokerAuditStatusTextList } from '@/pages/Achv/config';

const Index = () => {
	const { linkTo, openNewTab, searchParams } = useRouterLink();
	const config = useConfig();

	const status = searchParams.get('auditStatus');
	const [auditStatus, setAuditStatus] = useState(status === null ? 3 : status - 0 || '');
	const { form, dataSource, pagination, changePage, delTableData, exportData, onSearch, onReset, SortInput, importData, ImportModal } =
		useTableData({
			params: { auditStatus, isEncryptionName: 0 },
			getTablePageData,
			delTableItemData,
			exportTableData,
			batchUpdateSort,
			importTableData,
		});

	// 获取地区选项数据
	const [areaOptions, setAreaOptions] = useState([]);
	const getAreaOptions = () => {
		getThreeLevelData({
			level: 2,
			defaultArea: config.mpConfig.defaultArea || {},
		}).then((res) => {
			setAreaOptions(res.data || []);
		});
	};

	// 获取行业领域选项数据
	const [industryAreaOptions, setIndustryAreaOptions] = useState([]);
	const getIndustryAreaOptions = () => {
		listExpertIndustry().then((res) => {
			const result = res.data || [];
			result.forEach((ov) => {
				ov.label = ov.industryName;
				ov.value = ov.id;
				if (ov.parentId) {
					const find = result.find((oov) => oov.id === ov.parentId);
					if (find) {
						if (!find.children) {
							find.children = [];
						}
						find.children.push(ov);
					}
				}
			});
			setIndustryAreaOptions(result.filter((ov) => ov.parentId === null));
		});
	};

	useEffect(() => {
		if (config.mpConfig) {
			getAreaOptions();
		}
	}, [config.mpConfig]);

	useEffect(() => {
		getIndustryAreaOptions();
	}, []);

	return (
		<div className="">
			<div className="flex flex-shrink align-center margin-bottom-4 height-48 font-size-22 font-weight-500 color-1d2129">专家管理</div>
			{/* Tabs & 功能按钮 开始 */}
			<Affix offsetTop={0}>
				<div className="flex align-center justify-between padding-24 border-radius-8 bg-color-ffffff border-solid-bottom-f2f3f5">
					<div className="flex align-center">
						{brokerAuditStatusData.map((ov) => (
							<div
								key={ov.value}
								className={`a margin-right-40 font-size-18 font-weight-500 ${auditStatus === ov.value ? 'color-165dff' : ''}`}
								onClick={() => setAuditStatus(ov.value)}
							>
								{ov.label}
							</div>
						))}
					</div>
					<Space size={16}>
						<Button onClick={exportData}>批量导出</Button>
						<Button onClick={importData}>批量导入</Button>
						<Button
							type="primary"
							onClick={() => {
								linkTo(`/newAchv/expertManage/curd`);
							}}
						>
							新建专家
						</Button>
					</Space>
				</div>
			</Affix>
			{/* Tabs & 功能按钮 结束 */}
			<div className="flex-sub margin-top-18 padding-24 border-radius-8 bg-color-ffffff">
				{/* 筛选条件 开始 */}
				<div className="flex  justify-between margin-bottom-18">
					<div className="flex-sub">
						<Form
							form={form}
							labelAlign="right"
							layout="inline"
							initialValues={{
								provinceCodes: undefined,
								cityCodes: undefined,
								industryAreaIds: undefined,
								industryDisciplineIds: undefined,
								tempArea: [],
								tempArea2: [],
							}}
						>
							<Form.Item hidden name="provinceCodes">
								<Input />
							</Form.Item>
							<Form.Item hidden name="cityCodes">
								<Input />
							</Form.Item>
							<Form.Item hidden name="industryAreaIds">
								<Input />
							</Form.Item>
							<Form.Item hidden name="industryDisciplineIds">
								<Input />
							</Form.Item>
							<Form.Item hidden name="agencyId">
								<Input />
							</Form.Item>
							<Row className="width-100per" gutter={[12, 12]}>
								<Col span={8}>
									<Form.Item name="expertName" label="专家名称">
										<Input placeholder="请输入专家名称" />
									</Form.Item>
								</Col>
								<Col span={8}>
									<Form.Item name="agencyName" label="所属机构">
										<SelectOrg form={form} keyName="agencyId" placeholder="输入3字以上进行搜索选择" />
									</Form.Item>
								</Col>
								<Col span={8}>
									<Form.Item name="tempArea" label="所属区域">
										<Cascader
											options={areaOptions}
											placeholder="请选择所属区域"
											changeOnSelect
											displayRender={(label) => (label || []).filter((ov) => ov).join('-')}
											onChange={(e = [undefined, undefined]) => {
												form.setFieldValue('provinceCodes', e[0] ? [e[0]] : undefined);
												form.setFieldValue('cityCodes', e[1] ? [e[1]] : undefined);
											}}
										/>
									</Form.Item>
								</Col>
								<Col span={8}>
									<Form.Item name="tempArea2" label="行业领域">
										<Cascader
											options={industryAreaOptions}
											placeholder="请选择行业领域"
											changeOnSelect
											displayRender={(label) => (label || []).filter((ov) => ov).join('-')}
											onChange={(e = [undefined, undefined]) => {
												form.setFieldValue('industryDisciplineIds', e[0] ? [e[0]] : undefined);
												form.setFieldValue('industryAreaIds', e[1] ? [e[1]] : undefined);
											}}
										/>
									</Form.Item>
								</Col>
							</Row>
						</Form>
					</div>
					<div className="margin-left-12 padding-left-12 border-solid-left-e5e6eb">
						<Space size={16}>
							<Button onClick={onReset}>重置</Button>
							<Button type="primary" onClick={onSearch}>
								查询
							</Button>
						</Space>
					</div>
				</div>
				{/* 筛选条件 结束 */}
				{/* 表格 开始 */}
				<Table rowKey="id" dataSource={dataSource} pagination={pagination} onChange={changePage} scroll={{ x: 'max-content' }}>
					<Table.Column
						title="序号"
						key="index"
						width={60}
						render={(_, __, index) => {
							return index + 1;
						}}
					/>
					<Table.Column title="专家名称" dataIndex="expertName" />
					<Table.Column
						title="所属区域"
						dataIndex="name"
						render={(_, record) => {
							return [record.provinceCodeName, record.cityCodeName].filter((ov) => ov).join('-') || '--';
						}}
					/>
					<Table.Column
						title="所属机构"
						dataIndex="agencyName"
						render={(_, record) => {
							return (
								<div
									className={record.agencyId ? 'a color-165dff' : ''}
									onClick={() => {
										if (record.agencyId) {
											openNewTab(`/newAchv/agencyManage/detail?id=${record.agencyId}`);
										}
									}}
								>
									{record.agencyName || '--'}
								</div>
							);
						}}
					/>
					<Table.Column
						title="行业学科"
						dataIndex="industryDisciplineNames"
						render={(text) => {
							return (text || []).join('、') || '--';
						}}
					/>
					<Table.Column
						title="细分领域"
						dataIndex="industryAreaNames"
						render={(text) => {
							return (text || []).join('、') || '--';
						}}
					/>
					<Table.Column
						title="审核状态"
						dataIndex="auditStatus"
						align="center"
						render={(text) => {
							return (
								<div className={`tag-status-small-${['default', 'warning', 'error', 'primary'][text]}`}>
									{brokerAuditStatusTextList[text] || ''}
								</div>
							);
						}}
					/>
					<Table.Column
						title="排序"
						align="center"
						dataIndex="rankingNum"
						render={(_, record) => {
							return <SortInput record={record} />;
						}}
					/>
					<Table.Column
						title="操作"
						key="option"
						dataIndex="option"
						align="center"
						fixed="right"
						render={(_, record) => {
							return (
								<>
									<Button
										type="link"
										size="small"
										onClick={() => linkTo(`/newAchv/expertManage/detail?id=${record.id}&fromList=1`)}
									>
										编辑/审核
									</Button>
									<Popconfirm
										title="提示"
										description="确定删除吗？"
										onConfirm={() => {
											delTableData(record.id);
										}}
										okText="确定"
										cancelText="取消"
									>
										<Button type="link" size="small">
											删除
										</Button>
									</Popconfirm>
								</>
							);
						}}
					/>
				</Table>
				{/* 表格 结束 */}
				{/* 导入 开始 */}
				<ImportModal tplUrl="https://gbac-src.dwq360.com/static-assets/gbac-bidmgt-admfrontend/files/%E4%B8%93%E5%AE%B6%E5%AF%BC%E5%85%A5%E6%A8%A1%E6%9D%BF.xlsx" />
				{/* 导入 结束 */}
			</div>
		</div>
	);
};

export default Index;
