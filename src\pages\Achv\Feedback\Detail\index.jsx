import { useEffect, useState, useRef } from 'react';
import { useRouterLink } from '@/hook/useRouter';

import { Button, Space, message, Modal, Popconfirm, Table, Image, Form, Input, Radio } from 'antd';

import { detailFeedback, updateFeedbackStatus, addFeedbackComment, deleteFeedbackComment } from '@/api/Achv/Feedback/index';

const Index = (props = {}) => {
	const { linkTo, searchParams } = useRouterLink();
	const id = searchParams.get('id');
	const [detail, setDetail] = useState({});

	// const [status, setStatus] = useState('');
	const statusRef = useRef()
	// 更新任务状态
	const updateTaskStatus = (status) => {
		if (!status) {
			return;
		}
		updateFeedbackStatus({ id, status }).then(() => {
			message.success('操作成功');
			getTaskDetail();
		});
	};

	// 获取详情
	const getTaskDetail = () => {
		detailFeedback({ id }).then((res) => {
			const resd = res.data || {};
			resd.file = resd.file ? JSON.parse(resd.file) : [];
			setDetail(resd); 
			statusRef.current = resd.status || ''
		});
	};

	useEffect(() => {
		if (id) {
			getTaskDetail();
		} else {
			message.error('参数错误');
			setTimeout(() => {
				linkTo(-1);
			}, 500);
		}
	}, []);

	const formRef = useRef();
	const [form] = Form.useForm();

	const [isModalOpen, setIsModalOpen] = useState(false);
	const modalBtn = () => {
		form.validateFields().then((values) => {
			console.log('🚀 ~ form.validateFields ~ values:', values);
			setIsModalOpen(false);
			addFeedbackComment({
				officialFlag: 1,
				...values,
				feedbackId: id || '',
			}).then(() => {
				form.resetFields();
				getTaskDetail();
			});
		});
	};

	return (
		<div className="">
			<div className="flex flex-shrink align-center margin-bottom-4 height-48 font-size-22 font-weight-500 color-1d2129">
				<Space size={10}>
					<div className="a color-86909c" onClick={() => linkTo(`/newAchv/feedback`)}>
						反馈与建议
					</div>
					<div className="color-86909c">/</div>
					<div>反馈详情</div>
				</Space>
			</div>

			{/* 反馈详情 开始 */}
			<div className="margin-top-20 padding-top-20 padding-lr-20 padding-bottom-30 border-radius-8 bg-color-ffffff">
				<div className="flex align-center justify-between">
					<div className="line-height-26 font-size-18 font-weight-500 color-1d2129">反馈详情</div>
				</div>
				<div className="flex margin-top-20">
					<div className="margin-right-4 line-height-22 color-86909c">状态：</div>
					<div className="flex-sub flex line-height-22 flex">
						<div className="a font-weight-500 color-165dff">
							<div className={`tag-status-${['default', 'success', 'primary', 'default'][detail.status]}`}>
								{['', '已采纳', '已实现', '不合适'][detail.status] || ''}
							</div>
						</div>
						<Button
							size="small"
							type="link"
							onClick={() => {
								Modal.confirm({
									title: '修改反馈状态',
									content: (
										<Radio.Group onChange={(e) => {
											statusRef.current = e.target.value
										}} defaultValue={statusRef.current}>
											<Radio value={1}>已采纳</Radio>
											<Radio value={2}>已实现</Radio>
											<Radio value={3}>不合适</Radio>
										</Radio.Group>
									),
									onOk() {
										updateTaskStatus(statusRef.current);
									},
									onCancel() {
										console.log('Cancel');
									},
								});
							}}
						>
							修改状态
						</Button>
					</div>
				</div>
				<div className="flex margin-top-20">
					<div className="margin-right-4 line-height-22 color-86909c">标题：</div>
					<div className="flex-sub flex line-height-22 ">{detail.title || ''}</div>
				</div>
				<div className="flex margin-top-20">
					<div className="margin-right-4 line-height-22 color-86909c">详细描述:</div>
					<div className="flex-sub flex line-height-22 ">{detail.content || ''}</div>
				</div>
				<div className="flex margin-top-20">
					<div className="margin-right-4 line-height-22 color-86909c">附件图:</div>
					<div className="flex-sub flex line-height-22 flex">
						{(detail.file || []).map((ov) => {
							return <Image key={ov} width={120} src={ov} />;
						})}
					</div>
				</div>
				<div className="flex margin-top-20 margin-bottom-10">
					<div className="flex-sub flex line-height-22 flex align-center justify-between">
						<div className="font-size-16">回答详情({detail.commentCount || 0})</div>
						<Button
							type="primary"
							onClick={() => {
								setIsModalOpen(true);
							}}
						>
							运营人员回答
						</Button>
					</div>
				</div>

				{/* 回答详情 开始 */}
				<Table rowKey="id" dataSource={detail.comments || []} pagination={false}>
					<Table.Column
						title="序号"
						key="index"
						fixed="left"
						width={60}
						render={(_, __, index) => {
							return index + 1;
						}}
					/>
					<Table.Column
						title="来源"
						dataIndex="username"
						render={(username) => {
							return <div className="world-break-all">{username}</div>;
						}}
					/>
					<Table.Column title="回答内容" dataIndex="content" />
					<Table.Column title="回答时间" dataIndex="createTime" align="center" />
					<Table.Column
						title="点赞数"
						dataIndex="voteCount"
						align="center"
						render={(voteCount) => {
							return <div className="world-break-all">{voteCount || 0}</div>;
						}}
					/>
					<Table.Column
						title="操作"
						key="option"
						dataIndex="option"
						align="center"
						fixed="right"
						render={(_, record) => {
							return (
								<>
									<Popconfirm
										title="提示"
										description="确定删除吗？"
										onConfirm={() => {
											deleteFeedbackComment({
												ids: [record.id],
											}).then(() => {
												getTaskDetail();
											});
										}}
										okText="确定"
										cancelText="取消"
									>
										<Button type="link" danger size="small">
											删除
										</Button>
									</Popconfirm>
								</>
							);
						}}
					/>
				</Table>
				{/* 回答详情 结束 */}

				<Modal
					title="以运营人员身份回复"
					open={isModalOpen}
					maskClosable={false}
					onOk={() => {
						modalBtn();
					}}
					onCancel={() => {
						setIsModalOpen(false);
					}}
				>
					<Form autoComplete="off" ref={formRef} form={form}>
						<Form.Item name="content" rules={[{ required: true, message: '请输入内容' }]}>
							<Input.TextArea rows={5} placeholder="请输入内容" />
						</Form.Item>
					</Form>
				</Modal>
			</div>
			{/* 反馈详情 结束 */}
		</div>
	);
};

export default Index;
