import { useEffect, useState } from 'react';
import { Table, Button, Affix, Popconfirm, Form, Input, Select, Space } from 'antd';
import { useTableData } from '@/hook/useTableData';
import { useRouterLink } from '@/hook/useRouter';
import { pageFeedback as getTablePageData, deleteFeedback as delTableItemData } from '@/api/Achv/Feedback/index';

const Index = (props = {}) => {
	const { linkTo, searchParams } = useRouterLink();

	const { form, dataSource, pagination, changePage, onReset, delTableData, onSearch } = useTableData({
		params: {},
		getTablePageData,
		delTableItemData,
		pageCallback: (paramsData = {}) => {
			console.log('🚀 ~ Index ~ paramsData:', paramsData);
		},
	});

	return (
		<div className="">
			<div className="flex flex-shrink align-center margin-bottom-4 height-48 font-size-22 font-weight-500 color-1d2129">反馈与建议</div>
			{/* Tabs 开始 */}
			<Affix offsetTop={0}>
				<div className="flex align-center justify-between padding-24 border-radius-8 bg-color-ffffff border-solid-bottom-f2f3f5">
					<Form
						form={form}
						labelAlign="right"
						layout="inline"
						initialValues={{
							title: '',
							status: '',
						}}
					>
						<Form.Item label={'关键词'} name="title">
							<Input placeholder="请输入关键词" allowClear />
						</Form.Item>
						<Form.Item label={'状态'} name="status">
							<Select
								style={{ width: '120px' }}
								allowClear
								options={[
									{ value: '', label: '全部' },
									{ value: 1, label: '已采纳' },
									{ value: 2, label: '已实现' },
									{ value: 3, label: '不合适' },
								]}
							/>
						</Form.Item>
					</Form>

					<Space size={16}>
						<Button onClick={onReset}>重置</Button>
						<Button type="primary" onClick={onSearch}>
							查询
						</Button>
					</Space>
				</div>
			</Affix>
			{/* Tabs 结束 */}
			<div className="flex-sub margin-top-18 padding-24 border-radius-8 bg-color-ffffff">
				{/* 表格 开始 */}
				<Table rowKey="id" dataSource={dataSource} pagination={pagination} onChange={changePage} scroll={{ x: 'max-content' }}>
					<Table.Column
						title="序号"
						key="index"
						fixed="left"
						width={60}
						render={(_, __, index) => {
							return index + 1;
						}}
					/>
					<Table.Column title="标题" dataIndex="title" />
					<Table.Column
						title="来源"
						dataIndex="username"
						render={(text) => {
							return <div className="world-break-all">{text}</div>;
						}}
					/>
					<Table.Column title="发布时间" dataIndex="createTime" align="center" />
					<Table.Column
						title="状态"
						dataIndex="status"
						align="center"
						render={(status) => {
							return (
								<div className={`tag-status-${['default', 'success', 'primary', 'default'][status]}`}>
									{['', '已采纳', '已实现', '不合适'][status] || ''}
								</div>
							);
						}}
					/>
					<Table.Column title="回答数" dataIndex="commentCount" align="center" />
					<Table.Column title="点赞数" dataIndex="voteCount" align="center" />
					<Table.Column
						title="操作"
						key="option"
						dataIndex="option"
						align="center"
						fixed="right"
						render={(_, record) => {
							return (
								<>
									<Button type="link" size="small" onClick={() => linkTo(`/newAchv/feedback/detail?id=${record.id}`)}>
										查看
									</Button>
									<Popconfirm
										title="提示"
										description="确定删除吗？"
										onConfirm={() => {
											delTableData(record.id);
										}}
										okText="确定"
										cancelText="取消"
									>
										<Button type="link" danger size="small">
											删除
										</Button>
									</Popconfirm>
								</>
							);
						}}
					/>
				</Table>
				{/* 表格 结束 */}
			</div>
		</div>
	);
};

export default Index;
