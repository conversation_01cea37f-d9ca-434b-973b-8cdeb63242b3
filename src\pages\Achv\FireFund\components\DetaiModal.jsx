/**
 * @description DetaiModal - 文件描述
 * <AUTHOR>
 *
 * Created on 2024/9/20 16:15
 */
import React, { forwardRef, useImperativeHandle, useState } from "react";
import { Descriptions, Form, Input, Modal } from "antd";
import { getFundDetail } from "@/api/Achv/fireFund";
const formSchema = [
    {
        name: "name",
        title: "姓名",
        type: "input",
        required: true,
        placeholder: "请输入姓名",
    },
    {
        name: "birthDate",
        title: "出生年月",
        type: "date",
        start: "1900-01-01",
        end: "2050-01-01",
        required: true,
        placeholder: "请选择出生年月",
    },
    {
        name: "gender",
        title: "性别",
        type: "picker",
        required: true,
        placeholder: "请选择性别",
        options: [
            {
                label: "男",
                value: 1,
            },
            {
                label: "女",
                value: 2,
            },
        ],
    },
    {
        title: "工作单位",
        name: "company",
        type: "input",
        required: true,
        placeholder: "请输入工作单位",
    },
    {
        title: "职务",
        name: "position",
        type: "input",
        required: true,
        placeholder: "请输入职务",
    },
    {
        title: "职称",
        name: "title",
        type: "input",
        required: true,
        placeholder: "请输入职称",
    },
    {
        title: "毕业院校",
        name: "universityGraduated",
        type: "input",
        required: true,
        placeholder: "请输入毕业院校",
    },
    {
        title: "学历",
        name: "education",
        type: "picker",
        required: true,
        placeholder: "请选择学历",
        options: [
            {
                label: "博士",
                value: "博士",
            },
            {
                label: "硕士",
                value: "硕士",
            },
            {
                label: "本科",
                value: "本科",
            },
            {
                label: "本科以下",
                value: "本科以下",
            },
        ],
    },
    {
        title: "从事专业",
        name: "engagingProfession",
        type: "input",
        required: true,
        placeholder: "请输入从事专业",
    },
    {
        title: "目前所在城市",
        name: "address",
        type: "AreaSelect",
        required: true,
        placeholder: "请选择目前所在城市",
    },
    {
        title: "联系方式",
        name: "phone",
        type: "input",
        required: true,
        placeholder: "请输入联系方式",
        rules: [
            {
                pattern: "^1[3456789]\\d{9}$",
                message: "请输入正确的手机号",
            },
        ],
        column: 2,
    },
    {
        type: "textarea",
        title: "荣誉、社会兼职等",
        name: "honourDesc",
        placeholder: "请输入荣誉、社会兼职等",
        maxlength: 500,
        column: 2,
    },
    {
        type: "textarea",
        title: "个人简历",
        name: "introduction",
        placeholder: "请输入个人简历",
        maxlength: 500,
        column: 2,
    },
    {
        type: "textarea",
        title: "项目情况",
        name: "projectDesc",
        placeholder: "请输入项目情况",
        maxlength: 500,
        column: 2,
    },
    {
        type: "textarea",
        title: "团队情况",
        name: "teamDesc",
        placeholder: "请输入团队情况",
        maxlength: 500,
        column: 2,
    },
    {
        type: "textarea",
        title: "主要需求",
        name: "mainDemand",
        placeholder: "请输入主要需求",
        maxlength: 500,
        column: 2,
    },
];
const DetailModal = forwardRef((props, ref) => {
    const [open, setOpen] = useState(false);
    const [formData, setFormData] = useState({});
    /* 打开弹框 */
    const showModal = (record) => {
        setOpen(true);
        getDetail(record);
    };
    /* 查询详情 */
    const getDetail = async (params) => {
        const res = await getFundDetail({ id: params.id });
        console.log(res.data);
        if (res.data) {
            const data = res.data;
            data.address = data.address
                ? JSON.parse(data.address)?.provinceAndCityAndAreaName
                : [];
            data.gender =
                data.gender &&
                formSchema
                    .find((item) => item.name === "gender")
                    ?.options.find((item) => item.value === data.gender)?.label;
            setFormData(data);
        }
    };
    const handleOk = () => {};
    const handleCancel = () => {
        setOpen(false);
    };

    useImperativeHandle(ref, () => ({
        showModal,
    }));
    return (
        <Modal
            open={open}
            onCancel={handleCancel}
            title={"查看详情"}
            width={800}
            footer={null}
        >
            <Descriptions
                size={"small"}
                column={2}
                bordered
                className={"margin-top-20"}
                labelStyle={{ width: 160 }}
            >
                {formSchema.map((item, index) => {
                    return (
                        <Descriptions.Item
                            key={index}
                            label={item.title}
                            span={item.column}
                        >
                            {formData[item.name]}
                        </Descriptions.Item>
                    );
                })}
            </Descriptions>
        </Modal>
    );
});
export default DetailModal;
