import { useEffect, useRef } from "react";
import { Table, Button, Space, Form, Input } from "antd";
import { useTableData } from "@/hook/useTableData";

import { pagePolicyToArea as getTablePageData } from "@/api/Achv/fireFund";
import DetailModal from "@/pages/Achv/FireFund/components/DetaiModal";

import "./index.scss";

const Index = () => {
    const detailRef = useRef();

    const { form, dataSource, pagination, changePage, onSearch, onReset } =
        useTableData({
            getTablePageData,
        });

    // 获取选项数据
    const getOptionsData = () => {};

    useEffect(() => {
        getOptionsData();
    }, []);

    const columns = [
        {
            title: "序号",
            dataIndex: "index",
            width: 80,
            render: (_, __, index) => index + 1,
        },
        {
            title: "姓名",
            dataIndex: "name",
        },
        {
            title: "工作单位",
            dataIndex: "company",
        },
        {
            title: "职务",
            dataIndex: "position",
        },
        {
            title: "职称",
            dataIndex: "title",
        },
        {
            title: "从事专业",
            dataIndex: "engagingProfession",
        },
        {
            title: "联系方式",
            dataIndex: "phone",
        },
        {
            title: "申领时间",
            dataIndex: "createTime",
        },
        {
            title: "操作",
            dataIndex: "action",
            width: 80,
            align: "center",
            fixed: "right",
            render: (_, record) => {
                return (
                    <Space size={16}>
                        <Button type="link" onClick={() => onDetail(record)}>
                            详情
                        </Button>
                    </Space>
                );
            },
        },
    ];

    /* 查看详情 */
    const onDetail = (record) => {
        console.log(record);
        detailRef.current?.showModal(record);
    };
    return (
        <div className="">
            <div className="flex flex-shrink align-center margin-bottom-4 height-48 font-size-22 font-weight-500 color-1d2129">
                点燃资金管理
            </div>
            <div className="flex-sub padding-24 border-radius-8 bg-color-ffffff">
                {/* 筛选条件 开始 */}
                <div className="flex  justify-between margin-bottom-18">
                    <div className="flex-sub">
                        <Form
                            form={form}
                            labelAlign="right"
                            layout="inline"
                            initialValues={{
                                areaCodes: undefined,
                                cityCodes: undefined,
                            }}
                        >
                            <Form.Item label={"申领人姓名"} name="name">
                                <Input placeholder="请输入申领人姓名" />
                            </Form.Item>
                        </Form>
                    </div>
                    <div className="margin-left-12 padding-left-12 border-solid-left-e5e6eb">
                        <Space size={16}>
                            <Button onClick={onReset}>重置</Button>
                            <Button type="primary" onClick={onSearch}>
                                查询
                            </Button>
                        </Space>
                    </div>
                </div>
                {/* 筛选条件 结束 */}

                {/* 表格 开始 */}
                <Table
                    rowKey="id"
                    dataSource={dataSource}
                    pagination={pagination}
                    onChange={changePage}
                    scroll={{ x: "max-content" }}
                    columns={columns}
                />
                {/* 表格 结束 */}
            </div>
            <DetailModal ref={detailRef} />
        </div>
    );
};

export default Index;
