@function colorFn($color) {
	@return rgba($color, 0.1);
}

// $status defalut 默认 warning 警告 error 错误
@mixin btn-status($color) {
	display: inline-flex;
	align-items: center;
	justify-content: center;
	padding: 0 8px;
	line-height: 22px;
	font-size: 14;
	color: $color;
}

.audit-status-1 {
	@include btn-status(#ff7d00);
	background-color: colorFn(#ff7d00);
}

.audit-status-2 {
	@include btn-status(#f53f3f);
	background-color: colorFn(#f53f3f);
}

.audit-status-3 {
	@include btn-status(#3491fa);
	background-color: colorFn(#3491fa);
}

@mixin proclamation-status($color) {
	@include btn-status($color);
	&::before {
		content: '';
		margin: 0 6px 0 0;
		width: 6px;
		height: 6px;
		border-radius: 50%;
		background-color: $color;
	}
}

.proclamation-status-1 {
	@include proclamation-status(#00b42a);
}

.proclamation-status-2 {
	@include proclamation-status(#ff7d00);
}

.proclamation-status-3 {
	@include proclamation-status(#3491fa);
}
