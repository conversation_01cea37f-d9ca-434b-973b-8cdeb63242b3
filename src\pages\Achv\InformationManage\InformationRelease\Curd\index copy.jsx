/**
 * @description Curd.jsx - 文件描述
 * <AUTHOR>
 *
 * Created on 2024/10/11 17:21
 */
import React, { useEffect, useRef, useState } from 'react';
import { message, Space } from 'antd';
import { useRouterLink } from '@/hook/useRouter';
import {
	getPolicyDetail as getTableItemData,
	policyAdd as addTableItemData,
	policyUpdate as updateTableItemData,
} from '@/api/Achv/InformationManage/informationRelease';
import InformationModal from '../components/InformationModal';
import { getThreeLevelData } from '@/api/common';

const Curd = () => {
	const { linkTo, searchParams } = useRouterLink();
	const [detail, setDetail] = useState({});
	/* 区域数据 */
	const [areaList, setAreaList] = useState([]);

	const modalRef = useRef();
	useEffect(() => {
		getArea();
	}, []);

	useEffect(() => {
		const urlParamsId = searchParams.get('id');
		const urlParamsIsMp = searchParams.get('isMp');
		if (urlParamsId) {
			getDetail({ id: urlParamsId });
		} else {
			modalRef.current?.openPage({ isMp: !!urlParamsIsMp });
		}
	}, [searchParams]);
	/* 获取详情数据 */
	const getDetail = async (params) => {
		const res = await getTableItemData(params);
		if (res.data) {
			const data = res.data;
			data.isMp = !!data.originalUrl;
			setDetail(data);
			modalRef.current?.openPage(res.data);
		}
	};
	/* 获取区域接口 */
	const getArea = async () => {
		console.log('获取区域接口');
		const res = await getThreeLevelData({ level: 2 }, { showLoading: false });
		if (res.data) {
			console.log('获取区域接口', res.data);
			setAreaList(res.data);
		} else {
			message.error('获取区域失败');
		}
	};

	/* 更新数据 */
	const updateList = async (data) => {};
	return (
		<div>
			<div className="flex flex-shrink align-center margin-bottom-4 height-48 font-size-22 font-weight-500 color-1d2129">
				<Space size={10}>
					<div className="a color-86909c" onClick={() => linkTo('/newAchv/informationManage/informationRelease')}>
						资讯发布
					</div>
					<div className="color-86909c">/</div>
					<div>{detail?.id ? '修改资讯' : '新增资讯'}</div>
				</Space>
			</div>
			<div className={' border-radius-8 '}>
				<InformationModal
					ref={modalRef}
					updateTableItemData={updateTableItemData}
					addTableItemData={addTableItemData}
					getListData={updateList}
					areaList={areaList}
					isPage
				/>
			</div>
		</div>
	);
};
export default Curd;
