import { useEffect, useState } from 'react';
import { useRouterLink } from '@/hook/useRouter';

import { Button, Space, Form, Input, message, Affix, Radio, Tooltip, Popconfirm, Modal, Select } from 'antd';
import { QuestionCircleOutlined, Bar<PERSON><PERSON>Outlined, MinusCircleOutlined, PlusOutlined } from '@ant-design/icons';

import UploadImg from '@/components/UploadImg';
import UEditor from '@/components/UEditor';
import FormComp from '@/components/FormComp';
import SelectTTChannels from '@/components/Achv/SelectTTChannels';

import { getPolicyDetail, policyAdd, policyUpdate } from '@/api/Achv/InformationManage/informationRelease';
import { listHashTag } from '@/api/Achv/TTChannels/HashTag';

import { releaseTypeData } from '@/pages/Achv/config';
import { handleEditor } from '@/utils/common';
import { isPermi } from '@/components/Permission';

const Index = (props = {}) => {
	const { linkTo, searchParams } = useRouterLink();
	const id = searchParams.get('id') || '';
	// type originUrl 引用url文章
	const type = searchParams.get('type') || '';
	const [form] = Form.useForm();
	const releaseType = Form.useWatch('releaseType', form);
	const releaseTime = Form.useWatch('releaseTime', form);

	// 禁止编辑发布类型
	const [disabledReleaseType, setDisabledReleaseType] = useState(false);
	const [hash, setHash] = useState('');

	// 跳转地址
	const linkToPath = props.linkToPath || `/newAchv/informationManage/informationRelease`;

	// 取消
	const onCancel = () => {
		linkTo(-1);
	};

	// 提交
	const onSubmit = () => {
		form.validateFields()
			.then((values) => {
				handleEditor(values.content || '').then((content) => {
					const params = { ...values };
					params.content = content;
					params.ttChannelsIds = params.ttChannelsIds ? params.ttChannelsIds.map((ov) => ov.id) : undefined;
					params.showPictures = (values.showPictures && values.showPictures.join(',')) || '';
					delete params.tempArea;
					(values.id ? policyUpdate : policyAdd)(params).then(() => {
						message.success(values.id ? '修改成功' : '添加成功');
						setTimeout(() => {
							linkTo(-1);
						}, 500);
					});
				});
			})
			.catch((error) => {
				console.log(error);
				const errorName = error.errorFields[0].name[0];
				if (['ttChannelsIds'].includes(errorName)) {
					setHash('section3');
				} else {
					setHash('section1');
				}
			});
	};

	// 话题选项
	const [hashTagOptions, setHashTagOptions] = useState([]);
	const getHashTagOptions = () => {
		listHashTag().then((res) => {
			setHashTagOptions(
				(res.data || []).map((ov) => {
					return {
						label: `#${ov.title}`,
						value: ov.id,
					};
				})
			);
		});
	};

	// 获取详情
	const getDetail = () => {
		if (id) {
			getPolicyDetail({ id }).then((res) => {
				const resData = res.data || {};

				// 处理区域
				resData.tempArea = [resData.provinceCode, resData.cityCode];

				// 处理 关联科转号
				resData.ttChannelsIds = resData.ttChannelsList || [];

				// 处理展示图片
				resData.showPictures = (resData.showPictures || '').split(',').filter((ov) => ov);

				// 处理话题
				resData.ttHashtagList = resData.ttHashtagList.map((ov) => ov.id);

				form.setFieldsValue(resData);

				// 编辑 与固定类型 一致时不可以编辑
				setDisabledReleaseType(resData.releaseType === props.releaseType);
			});
		} else if (props.releaseType) {
			// 新建时 如果是固定类型入口就默认类型
			form.setFieldValue('releaseType', props.releaseType);
			// 新建时 固定类型不可编辑
			setDisabledReleaseType(true);
		}
	};

	useEffect(() => {
		getDetail();
		getHashTagOptions();

		const hash = window.location.hash.replace('#', '');
		setHash(hash || 'section1');
	}, []);

	// 跳转到指定锚点
	useEffect(() => {
		if (hash) {
			setTimeout(() => {
				const element = document.getElementById(hash);
				if (element) {
					element.scrollIntoView({ behavior: 'smooth' });
				}
			}, 500);
		}
	}, [hash]);

	return (
		<div className="">
			<div className="flex flex-shrink align-center margin-bottom-4 height-48 font-size-22 font-weight-500 color-1d2129">
				<Space size={10}>
					<div className="a color-86909c" onClick={() => linkTo(linkToPath)}>
						图文管理
					</div>
					<div className="color-86909c">/</div>
					<div>{id ? '图文编辑' : '新增图文'}</div>
				</Space>
			</div>

			{/* Tabs & 功能按钮 开始 */}
			<div id="section1"></div>
			<Affix offsetTop={0}>
				<div className="flex align-center justify-between padding-24 border-radius-8 border-solid-bottom-f2f2f2 bg-color-ffffff">
					<div className="flex align-center">
						{(releaseType === 2 ? ['基础信息', '图文详情', '关联账号'] : ['基础信息', '图文详情']).map((ov, oi) => {
							const currentHash = `section${oi + 1}`;
							return (
								<div
									key={oi}
									className={`a margin-right-40 font-size-18 font-weight-500 ${hash === currentHash ? 'color-165dff' : ''}`}
									onClick={() => {
										if (currentHash === 'section3') {
											form.validateFields()
												.then(() => {
													setHash(`section${oi + 1}`);
												})
												.catch((error) => {
													const errorName = error.errorFields[0].name[0];
													if (['ttChannelsIds'].includes(errorName)) {
														setHash('section3');
													} else {
														setHash('section1');
													}
												});
										} else {
											setHash(`section${oi + 1}`);
										}
									}}
								>
									{ov}
								</div>
							);
						})}
					</div>
					<Space size={16}>
						<Button onClick={onCancel}>取消</Button>
						<Button type="primary" onClick={onSubmit}>
							保存
						</Button>
					</Space>
				</div>
			</Affix>
			{/* Tabs & 功能按钮 结束 */}

			<Form
				form={form}
				className="antd-form-box"
				labelAlign="right"
				labelCol={{ style: { width: '116px' } }}
				initialValues={{
					isTimingRelease: 0,
					releaseType: 1,
					releasePlatform: 1,
					showPicturesStatus: 1,
					showPictures: [],
					ttChannelsIds: [],
					delTTChannelsIds: [],
				}}
			>
				<Form.Item hidden name="id">
					<Input />
				</Form.Item>
				<Form.Item hidden name="releasePlatform">
					<Input />
				</Form.Item>
				<Form.Item hidden name="delTTChannelsIds">
					<Input />
				</Form.Item>
				<Form.Item hidden name="rankingNum">
					<Input />
				</Form.Item>

				<div style={{ display: hash !== 'section3' ? 'block' : 'none' }}>
					{/* 基础信息 开始 */}
					<div className="margin-top-20 padding-20 border-radius-8 bg-color-ffffff">
						<div className="padding-bottom-30 line-height-26 font-size-18 font-weight-500 color-1d2129">基础信息</div>
						<Form.Item name="title" label="图文标题" rules={[{ required: true, message: '请输入图文标题' }]}>
							<Input placeholder="请输入图文标题" />
						</Form.Item>
						<Form.Item name="logoUrl" label="内容封面" rules={[{ required: true, message: '请上传内容封面' }]}>
							<UploadImg size={1} width={160} height={160} tips={'建议尺寸：200*200px'} cropperProps={{ width: 200, height: 200 }} />
						</Form.Item>
						<Form.Item label="展示图片">
							<Space direction="vertical" className="width-100per">
								<Form.Item name="showPictures" noStyle>
									<UploadImg.MultipleUpload maxCount={9} size={5} width={240} height={120} />
								</Form.Item>
								<Space>
									<Form.Item name="showPicturesStatus" noStyle>
										<FormComp.Switch />
									</Form.Item>
									<div className="font-size-14 color-86909c">在详情中展示</div>
								</Space>
							</Space>
						</Form.Item>
						<Form.Item label="图集模式" name="imageCardModeStatus" initialValue={0}>
							<FormComp.Switch />
						</Form.Item>
						<Form.Item label="投票" name="voteData">
							<VoteConfig />
						</Form.Item>
						{releaseType === 2 && (
							<Form.Item label="关联话题" name="ttHashtagList">
								<Select maxCount={2} mode="multiple" options={hashTagOptions} placeholder="请选择关联话题" allowClear />
							</Form.Item>
						)}

						{/* 科转号 不需要填写来源 来源为 科转号账号名 */}
						{releaseType !== 2 && (
							<Form.Item name="releaseOrg" label="来源" rules={[{ required: true, message: '请输入内容' }]}>
								<Input placeholder="请输入来源" />
							</Form.Item>
						)}
						{type === 'originUrl' && (
							<Form.Item
								name="originalUrl"
								label="文章链接"
								rules={[
									{ required: true, message: '请输入文章链接' },
									{ type: 'url', message: '请输入正确的链接' },
								]}
							>
								<Input placeholder="请输入文章链接" />
							</Form.Item>
						)}
						{isPermi('newAchv:TTChannels:article:recommend') && (
							<Form.Item label="推荐动态" name="ttTimeLineBusinessIds">
								<Input.TextArea placeholder="请输入推荐动态id、英文逗号分隔 例子：20240415457001,20240415457002,20240415457003" />
							</Form.Item>
						)}

						<Form.Item label="发布时间">
							<Space size={0}>
								<Space size={32}>
									<Form.Item noStyle name="releaseTime">
										<FormComp.DatePicker placeholder="请选择发布时间" showTime />
									</Form.Item>
									<Form.Item noStyle name="isTimingRelease">
										<FormComp.Checkbox disabled={!releaseTime}>定时发布</FormComp.Checkbox>
									</Form.Item>
								</Space>
								<Tooltip
									title={
										<>
											<div>1.不选发布时间将以提交时间作为发布时间</div>
											<div>2.发布时间小于当前时间将于10分钟内发布</div>
										</>
									}
								>
									<QuestionCircleOutlined />
								</Tooltip>
							</Space>
						</Form.Item>
						<Form.Item label="发布类型" name="releaseType" required>
							<Radio.Group options={releaseTypeData.filter((ov) => ov.value < 3)} disabled={disabledReleaseType} />
						</Form.Item>
					</div>
					{/* 基础信息 结束 */}

					{/* 图文详情 开始 */}
					{/* url文章不需要填写 图文详情 */}
					{type !== 'originUrl' && (
						<>
							<div id="section2"></div>
							<div className="margin-top-20 padding-20 border-radius-8 bg-color-ffffff">
								<div className="padding-bottom-30 line-height-26 font-size-18 font-weight-500 color-1d2129">图文详情</div>
								<Form.Item name="content" wrapperCol={{ span: 24 }}>
									<UEditor />
								</Form.Item>
							</div>
						</>
					)}
					{/* 图文详情 结束 */}
				</div>

				{/* 关联账号 开始 */}
				<div style={{ display: hash === 'section3' ? 'block' : 'none' }}>
					<div id="section3"></div>
					<SelectTTChannels
						form={form}
						name="ttChannelsIds"
						label="关联科转号"
						rules={[{ required: releaseType === 2, type: 'array', message: '请选择关联科转号' }]}
						collectDelIds={true}
					/>
				</div>
				{/* 关联账号 结束 */}
			</Form>
		</div>
	);
};

// 投票配置
const VoteConfig = (props = {}) => {
	const [open, setOpen] = useState(false);
	const [form] = Form.useForm();
	const multipleChoiceStatus = Form.useWatch('multipleChoiceStatus', form);

	useEffect(() => {
		if (props.value) {
			form.setFieldsValue(props.value);
		} else {
			form.resetFields();
		}
	}, [props.value]);
	return (
		<>
			{props.value ? (
				<Space>
					<Button ghost icon={<BarChartOutlined />} type="primary">
						{props.value.title}
					</Button>
					<div className="a color-165dff" onClick={() => setOpen(true)}>
						编辑
					</div>
					<Popconfirm
						title="提示"
						description={`确定删除吗？`}
						onConfirm={() => {
							props.onChange(null);
						}}
						okText="确定"
						cancelText="取消"
					>
						<div className="a color-165dff">删除</div>
					</Popconfirm>
				</Space>
			) : (
				<Button type="primary" ghost onClick={() => setOpen(true)}>
					点击添加投票
				</Button>
			)}

			<Modal
				title="编辑投票"
				open={open}
				centered
				width={600}
				onCancel={() => setOpen(false)}
				onOk={() => {
					form.validateFields().then((values) => {
						props.onChange(values);
						setOpen(false);
					});
				}}
			>
				<Form
					className="padding-top-12"
					form={form}
					labelCol={{ style: { width: '80px' } }}
					initialValues={{
						multipleChoiceStatus: 0,
						voteOptionList: [
							{
								title: '',
							},
							{
								title: '',
							},
						],
					}}
				>
					<Form.Item name="multipleChoiceStatus" hidden>
						<Input />
					</Form.Item>
					<Form.Item
						label="投票标题"
						name="title"
						rules={[
							{ required: true, message: '请输入投票标题' },
							{ max: 40, message: '投票标题不能超过40个字符' },
						]}
					>
						<Input placeholder="请输入投票标题" />
					</Form.Item>
					<Form.List name="voteOptionList">
						{(fields, { add, remove }) => {
							return fields.map((ov, oi) => {
								return (
									<div key={ov.key}>
										<div className="flex align-start">
											<Form.Item
												className="flex-sub"
												label={`选项${oi + 1}`}
												name={[ov.name, 'title']}
												rules={[
													{ required: true, message: '请输入选项' },
													{ max: 40, message: '选项不能超过40个字符' },
												]}
											>
												<Input placeholder="请输入选项" />
											</Form.Item>
											{oi > 1 && (
												<MinusCircleOutlined
													className="margin-left-12 margin-top-10"
													onClick={() => {
														remove(ov.name);
													}}
												/>
											)}
										</div>
										{oi === fields.length - 1 && (
											<div className="flex align-start justify-between margin-left-80">
												<Button
													icon={<PlusOutlined />}
													type="primary"
													onClick={() => {
														add();
													}}
													ghost
												>
													添加选项
												</Button>
												<div
													className="a flex align-center line-height-28"
													onClick={() => {
														form.setFieldsValue({
															multipleChoiceStatus: multipleChoiceStatus === 1 ? 0 : 1,
														});
													}}
												>
													<Radio checked={multipleChoiceStatus === 1} className="margin-top-2"></Radio>
													可多选
												</div>
											</div>
										)}
									</div>
								);
							});
						}}
					</Form.List>
				</Form>
			</Modal>
		</>
	);
};

export default Index;
