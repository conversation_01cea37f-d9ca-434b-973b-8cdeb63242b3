import { useEffect, useState, useRef, forwardRef, useImperativeHandle } from 'react';
import { Button, Input, Affix, Anchor, message, Form, Modal, Image, Space, Card, Progress } from 'antd';
import { EditOutlined, Bar<PERSON>hartOutlined, CopyOutlined } from '@ant-design/icons';

import ModalForm from '@/components/ModalForm';

import { getPolicyDetail, auditPolicy } from '@/api/Achv/InformationManage/informationRelease';
import { getTTTimeLine } from '@/api/Achv/TTChannels/Dynamic';

import { useRouterLink } from '@/hook/useRouter';
import { releaseTypeTextList, releasePlatformTextList, showStatusTextList } from '@/pages/Achv/config';
import { copeText } from '@/utils/common';

const Index = (props = {}) => {
	const ModalFormRef = useRef();
	const { linkTo, searchParams } = useRouterLink();

	const id = searchParams.get('id') || '';
	const fromList = !!searchParams.get('fromList');

	const [detail, setDetail] = useState({});
	const [open, setOpen] = useState(false);

	// 跳转地址
	const linkToPath = props.linkToPath || `/newAchv/informationManage/informationRelease`;

	const getDetail = () => {
		getPolicyDetail({ id, isUpdate: 1 }).then((res) => {
			const resData = res.data || {};

			setDetail(resData);
		});
	};

	// 通过审核
	const handlePass = () => {
		Modal.confirm({
			title: '需求审核',
			content: `是否通过审核？`,
			onOk() {
				handleAudit({ status: 3 });
			},
		});
	};

	// 撤销审核
	const handleRevoke = () => {
		Modal.confirm({
			title: '撤销审核',
			content: `是否撤销审核结果？`,
			onOk() {
				handleAudit({ status: 1 });
			},
		});
	};

	// 审核操作
	const handleAudit = (values) => {
		auditPolicy({ ...values, id }).then(() => {
			message.success('操作成功');

			setTimeout(() => {
				linkTo(-1);
			}, 500);
		});
	};

	useEffect(() => {
		getDetail();
	}, []);

	// 关联科转圈id
	const [timeLineIds, setTimeLineIds] = useState([]);
	useEffect(() => {
		if (detail.ttChannelsList && detail.ttChannelsList.length > 0) {
			getTTTimeLine({ businessId: id }).then((res) => {
				setTimeLineIds((res.data || []).map((ov) => ov.id));
			});
		}
	}, [detail]);

	return (
		<div>
			<div className="flex flex-shrink align-center margin-bottom-4 height-48 font-size-22 font-weight-500">
				<div className="a color-86909c" onClick={() => linkTo(fromList ? -1 : linkToPath)}>
					图文管理
				</div>
				<div className="margin-lr-10 color-86909c">/</div>
				<div className="color-1d2129">图文详情</div>
			</div>

			<Affix>
				<div className="height-66 flex align-center justify-between bg-color-ffffff border-radius-8 padding-lr-24 border-solid-bottom-f2f3f5">
					<div className="flex align-center justify-between color-1d2129 line-height-26 font-size-18 font-weight-500">
						<Anchor
							affix={false}
							rootClassName="anchor-header-tabBar-box"
							direction="horizontal"
							replace
							targetOffset={80}
							items={[
								{
									key: 'section1',
									href: '#section1',
									title: <div className="margin-right-40 font-size-16 font-weight-500">基本信息</div>,
								},
								{
									key: 'section2',
									href: '#section2',
									title: <div className="margin-right-40 font-size-16 font-weight-500">图文详情</div>,
								},
								{
									key: 'section3',
									href: '#section3',
									title: <div className="margin-right-40 font-size-16 font-weight-500">关联账号</div>,
								},
							].filter((ov) => ov.key !== 'section3' || detail.releaseType === 2)}
						/>
					</div>
					<div className="flex align-center justify-between color-1d2129 line-height-26 font-size-18 font-weight-500">
						{detail.auditStatus === 1 ? (
							<>
								<Button
									type="primary"
									danger
									ghost
									onClick={() => {
										ModalFormRef.current.setOpen(true);
										ModalFormRef.current.setTitle('不通过原因');
									}}
									className="height-40 width-104"
								>
									不通过
								</Button>
								<Button type="primary" onClick={handlePass} className="margin-left-20 height-40 width-104">
									通过审核
								</Button>
							</>
						) : (
							<Button onClick={handleRevoke} className="margin-left-20 height-40 width-104">
								撤销审核
							</Button>
						)}
					</div>
				</div>
			</Affix>

			<div id="section1"></div>
			<div className="bg-color-ffffff border-radius-8 padding-lr-24 padding-tb-20 margin-tb-16">
				<div className="flex align-center justify-between margin-bottom-20">
					<div className="font-size-18 line-height-26 font-weight-500">需求信息</div>
					<Button
						type="link"
						icon={<EditOutlined />}
						onClick={() => {
							linkTo(`${linkToPath}/curd?id=${id}${detail.originalUrl ? '&type=originUrl' : ''}#section1`);
						}}
					>
						编辑信息
					</Button>
				</div>
				<div className="flex align-center justify-start font-size-14 line-height-22 padding-left-16 margin-bottom-20">
					<div className="color-86909c margin-right-4">审核状态：</div>
					<div className="">
						<div className={`tag-status-${['default', 'warning', 'error', 'primary'][detail.auditStatus]}`}>
							{['', '审核中', '审核不通过', '审核通过'][detail.auditStatus] || ''}
						</div>
					</div>
				</div>
				{detail.auditStatus === 2 && (
					<div className="flex align-center justify-start font-size-14 line-height-22 padding-left-16 margin-bottom-20">
						<div className="color-86909c margin-right-4">拒绝原因：</div>
						<div className="">{detail.reason || '无'}</div>
					</div>
				)}
				<div className="flex align-center justify-start font-size-14 line-height-22 padding-left-16 margin-bottom-20">
					<div className="color-86909c margin-right-4">图文标题：</div>
					<div className="">{detail.title || '--'}</div>
				</div>
				<div className="flex justify-start font-size-14 line-height-22 padding-left-16 margin-bottom-20">
					<div className="color-86909c margin-right-4">图文封面：</div>
					<div className="">
						<Image src={detail.logoUrl} width={120} />
					</div>
				</div>
				<div className="flex justify-start font-size-14 line-height-22 padding-left-16 margin-bottom-20">
					<div className="color-86909c margin-right-4">展示图片：</div>
					<div className="flex-sub">
						<Space wrap>
							{(detail.showPictures &&
								detail.showPictures.split(',').map((ov, oi) => {
									return <Image key={oi} width={120} height={120} src={ov} />;
								})) ||
								'--'}
						</Space>
					</div>
				</div>
				<div className="flex justify-start font-size-14 line-height-22 padding-left-16 margin-bottom-20">
					<div className="color-86909c margin-right-4">图集模式：</div>
					<div className="">
						<div className={`tag-status-${['error', 'primary'][detail.imageCardModeStatus || 0]}`}>
							{detail.imageCardModeStatus === 1 ? '是' : '否'}
						</div>
					</div>
				</div>
				<div className="flex justify-start font-size-14 line-height-22 padding-left-16 margin-bottom-20">
					<div className="color-86909c margin-right-4">详情展示：</div>
					<div className="">
						<div className={`tag-status-${['error', 'primary'][detail.showPicturesStatus]}`}>
							{showStatusTextList[detail.showPicturesStatus] || '--'}
						</div>
					</div>
				</div>
				{detail.voteData && (
					<div className="flex align-center justify-start font-size-14 line-height-22 padding-left-16 margin-bottom-20">
						<div className="color-86909c margin-right-4">投票：</div>
						<div className="flex-sub">
							<Space>
								<Button ghost icon={<BarChartOutlined />} type="primary">
									{detail.voteData.title}
								</Button>
								<div className="a color-165dff" onClick={() => setOpen(true)}>
									查看
								</div>
							</Space>
						</div>
					</div>
				)}

				{detail.ttHashtagList && detail.ttHashtagList.length > 0 && (
					<div className="flex align-center justify-start font-size-14 line-height-22 padding-left-16 margin-bottom-20">
						<div className="color-86909c margin-right-4">管理话题：</div>
						<div className="flex-sub">
							<Space size={16}>
								{detail.ttHashtagList.map((ov, oi) => {
									return (
										<div key={oi} className="color-165dff">
											#{ov.title}
										</div>
									);
								})}
							</Space>
						</div>
					</div>
				)}

				{/* 科转号 不需要填写来源 来源为 科转号账号名 */}
				{detail.releaseType !== 2 && (
					<div className="flex align-center justify-start font-size-14 line-height-22 padding-left-16 margin-bottom-20">
						<div className="color-86909c margin-right-4">来源：</div>
						<div className="">{detail.releaseOrg || '--'}</div>
					</div>
				)}
				{/* <div className="flex align-center justify-start font-size-14 line-height-22 padding-left-16 margin-bottom-20">
					<div className="color-86909c margin-right-4">所属区域：</div>
					<div className="">{[detail.provinceCodeName, detail.cityCodeName, detail.areaCodeName].filter((ov) => ov).join('-') || '--'}</div>
				</div> */}
				<div className="flex align-center justify-start font-size-14 line-height-22 padding-left-16 margin-bottom-20">
					<div className="color-86909c margin-right-4">发布时间：</div>
					<div className="">{detail.releaseTime || '--'}</div>
				</div>
				{detail.originalUrl && (
					<div className="flex align-center justify-start font-size-14 line-height-22 padding-left-16 margin-bottom-20">
						<div className="color-86909c margin-right-4">文章链接：</div>
						<div className="">{detail.originalUrl || '--'}</div>
					</div>
				)}
				<div className="flex align-center justify-start font-size-14 line-height-22 padding-left-16 margin-bottom-20">
					<div className="color-86909c margin-right-4">排序：</div>
					<div className="">{detail.rankingNum || '--'}</div>
				</div>
				<div className="flex align-center justify-start font-size-14 line-height-22 padding-left-16 margin-bottom-20">
					<div className="color-86909c margin-right-4">显示状态：</div>
					<div className="">
						<div className={`tag-status-${['error', 'primary'][detail.showStatus]}`}>{showStatusTextList[detail.showStatus] || '--'}</div>
					</div>
				</div>
				<div className="flex align-center justify-start font-size-14 line-height-22 padding-left-16 margin-bottom-20">
					<div className="color-86909c margin-right-4">发布类型：</div>
					<div className="">
						<div className={`tag-status-${['', 'primary', 'warning', '700eb2', 'success', 'error'][detail.releaseType]}`}>
							{releaseTypeTextList[detail.releaseType] || '--'}
						</div>
					</div>
				</div>
				<div className="flex align-center justify-start font-size-14 line-height-22 padding-left-16 margin-bottom-20">
					<div className="color-86909c margin-right-4">发布平台：</div>
					<div className="">
						<div className={`tag-status-${['', 'primary', 'success', '700eb2'][detail.releasePlatform]}`}>
							{releasePlatformTextList[detail.releasePlatform] || '--'}
						</div>
					</div>
				</div>
			</div>

			<div id="section2"></div>
			<div className="bg-color-ffffff border-radius-8 padding-lr-24 padding-tb-20 margin-tb-16">
				<div className="flex align-center justify-between margin-bottom-20">
					<div className="font-size-18 line-height-26 font-weight-500">图文详情</div>
					<Button
						type="link"
						icon={<EditOutlined />}
						onClick={() => {
							linkTo(`${linkToPath}/curd?id=${id}${detail.originalUrl ? '&type=originUrl' : ''}#section2`);
						}}
					>
						编辑信息
					</Button>
				</div>

				<div className="flex justify-start font-size-14 line-height-22 padding-left-16 margin-bottom-20">
					<div className="flex-sub">
						<div
							className="font-size-14 line-height-24 pre-wrap rich-box"
							dangerouslySetInnerHTML={{
								__html: detail.content || '--',
							}}
						></div>
					</div>
				</div>
			</div>

			{detail.releaseType === 2 && (
				<>
					<div id="section3"></div>
					<div className="bg-color-ffffff border-radius-8 padding-lr-24 padding-tb-20 margin-tb-16">
						<div className="flex align-center justify-between margin-bottom-20">
							<div className="font-size-18 line-height-26 font-weight-500">关联账号</div>
							<Button
								type="link"
								icon={<EditOutlined />}
								onClick={() => {
									linkTo(`${linkToPath}/curd?id=${id}${detail.originalUrl ? '&type=originUrl' : ''}#section3`);
								}}
							>
								编辑信息
							</Button>
						</div>
						<div className="flex align-center justify-start font-size-14 line-height-22 padding-left-16 margin-bottom-20">
							<div className="color-86909c margin-right-24">关联科转号</div>
							<div className="flex align-start justify-start flex-wrap">
								{(detail.ttChannelsList || []).map((item) => {
									return (
										<div key={item.id} className="flex align-center justify-start margin-right-20">
											<img src={item.avatarUrl} className="width-20 height-20 border-radius-10 bg-color-e5e6eb" />
											<div className="font-size-14 color-165dff margin-left-6 line-height-20">{item.accountName || ''}</div>
										</div>
									);
								})}
								{(detail.ttChannelsList || []).length === 0 && '--'}
							</div>
						</div>
						<div className="flex justify-start font-size-14 line-height-22 padding-left-16 margin-bottom-20">
							<div className="color-86909c margin-right-24">关联科转圈ID</div>
							<Space direction="vertical">
								{(timeLineIds || []).map((ov) => {
									return (
										<Space key={ov} size={18}>
											<div className="font-size-14 color-165dff margin-left-6 line-height-20">{ov}</div>
											<CopyOutlined
												className="a color-165dff"
												onClick={() => {
													copeText(ov).then(() => {
														message.success('复制成功');
													});
												}}
											/>
										</Space>
									);
								})}
								{(timeLineIds || []).length === 0 && '--'}
							</Space>
						</div>
					</div>
				</>
			)}

			{/* 询问弹窗 开始 */}
			<ModalForm ref={ModalFormRef} onOk={handleAudit} FormComp={(props) => <ConrirmForm ref={props.FormCompRef} />} />
			{/* 询问弹窗 结束 */}

			{/* 投票 开始 */}
			{detail.voteData && <VoteModal open={open} setOpen={setOpen} voteData={detail.voteData} />}
			{/* 投票 结束 */}
		</div>
	);
};

// 询问表单
const ConrirmForm = forwardRef((props = {}, ref) => {
	const [form] = Form.useForm();

	useImperativeHandle(ref, () => {
		return {
			onCancel: () => Promise.resolve(),
			onOk: () => {
				return new Promise((resolve) => {
					form.validateFields().then((values) => {
						resolve(values);
					});
				});
			},
		};
	});

	return (
		<Form form={form} labelCol={{ style: { width: '100px' } }} initialValues={{ status: 2 }}>
			<Form.Item hidden name="status">
				<Input />
			</Form.Item>
			<Form.Item label="原因" name="reason" rules={[{ required: true, message: '请输入不通过原因' }]}>
				<Input.TextArea rows={4} placeholder="请输入不通过原因" />
			</Form.Item>
		</Form>
	);
});

// 投票
const VoteModal = (props = {}) => {
	const voteData = props.voteData;
	return (
		<Modal
			open={props.open}
			title="投票详情"
			centered
			cancelButtonProps={{ hidden: true }}
			onCancel={() => props.setOpen(false)}
			onOk={() => props.setOpen(false)}
			width={700}
		>
			<Card
				title={
					<div style={{ fontWeight: 'normal' }}>
						{voteData.title}
						{voteData.multipleChoiceStatus === 1 ? '（多选）' : ''}
					</div>
				}
				size="small"
				bordered={false}
			>
				<Space direction="vertical">
					{voteData.voteOptionList.map((ov, oi) => {
						return (
							<Space direction="vertical" key={oi}>
								<div>
									选项{oi + 1}：{ov.title}
								</div>
								<Progress
									percent={voteData.voteTotalNum ? ((ov.voteNum / voteData.voteTotalNum) * 100).toFixed(1) : 0}
									percentPosition={{ align: 'start', type: 'inner' }}
									size={[400, 20]}
								/>
							</Space>
						);
					})}
				</Space>
			</Card>
		</Modal>
	);
};

export default Index;
