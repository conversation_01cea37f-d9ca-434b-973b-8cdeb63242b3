import React, { useEffect, useRef, useState } from 'react';
import {
	pagePolicy as getTablePageData,
	policyDel as delTableItemData,
	policyAdd as addTableItemData,
	policyUpdate as updateTableItemData,
	getPolicyDetail as getTableItemData,
} from '@/api/Achv/InformationManage/informationRelease';
import { useTableData } from '@/hook/useTableData';
import { Button, Form, Input, message, Modal, Space, Table, Affix } from 'antd';
import { copeText } from '@/utils/common';
import InformationModal from '../components/InformationModal';
import { getThreeLevelData } from '@/api/common';
import { useRouterLink } from '@/hook/useRouter';

import { releaseStatusData } from '@/pages/Achv/config';

const InformationRelease = () => {
	const { linkTo, searchParams } = useRouterLink();

	const status = searchParams.get('releaseStatus');
	const [releaseStatus, setReleaseStatus] = useState(status === null || status === '' ? '' : status - 0);

	// 区域数据
	const [areaList, setAreaList] = useState([]);
	const modalRef = useRef();

	const { form, dataSource, pagination, changePage, onSearch, onReset } = useTableData({
		params: { releaseStatus },
		getTablePageData,
	});

	useEffect(() => {
		getArea();
	}, []);

	// 获取区域接口
	const getArea = async () => {
		console.log('获取区域接口');
		const res = await getThreeLevelData({ level: 2 }, { showLoading: false });
		if (res.data) {
			console.log('获取区域接口', res.data);
			setAreaList(res.data);
		} else {
			message.error('获取区域失败');
		}
	};

	// 新建资讯
	const onAdd = () => {
		console.log('新建资讯');
		// modalRef.current?.showModal({});
		linkTo('/newAchv/informationManage/informationRelease/curd?type=add');
	};

	// 编辑资讯
	const onUpdate = async (params) => {
		const res = await getTableItemData(params);
		if (res.data) {
			console.log('编辑资讯', res.data);
			const data = res.data;
			data.isMp = !!data.originalUrl;
			// modalRef.current?.showModal(res.data);
			linkTo('/newAchv/informationManage/informationRelease/curd?type=update&id=' + data.id);
		}
	};

	// 删除资讯
	const onDel = (params) => {
		console.log('删除资讯', params);
		Modal.confirm({
			title: params.ids.length === 1 ? '确定删除该条数据？' : '确定删除这些数据？',
			content: '删除后无法恢复，确认删除吗？',
			onOk: async () => {
				const res = await delTableItemData(params);
				if (res?.data) {
					message.success('删除成功');
					onSearch();
					return true;
				}
				return false;
			},
		});
	};

	// 分享资讯
	const onShare = async (params) => {
		console.log('分享资讯', params);
		const copyLink = `/pages/PolicyInterpretationDetail/index?id=${params.id}`;
		await copeText(copyLink);
		message.success('复制小程序地址成功');
	};

	// 公众号导入
	const onImport = () => {
		console.log('公众号导入');
		// modalRef.current?.showModal({isMp: true});
		linkTo('/newAchv/informationManage/informationRelease/curd?type=add&isMp=true');
	};

	const columns = [
		{
			title: '序号',
			dataIndex: 'index',
			width: 80,
			fixed: 'left',
			align: 'center',
			render: (_, __, index) => index + 1 + (pagination.current - 1) * pagination.pageSize,
		},
		{
			title: '标题',
			dataIndex: 'title',
			render: (text) => <div className="max-width-400">{text}</div>,
		},
		{
			title: '来源',
			dataIndex: 'releaseOrg',
		},
		{
			title: '所属区域',
			dataIndex: 'cityCodeName',
			width: 120,
			render: (cityCodeName, { provinceCodeName }) => {
				return `${provinceCodeName} ${cityCodeName}`;
			},
		},
		{
			title: '发布时间',
			dataIndex: 'releaseTime',
		},
		{
			title: '排序',
			dataIndex: 'rankingNum',
		},
		{
			title: '流量统计',
			dataIndex: 'viewNum',
			align: 'center',
			render: (text) => {
				return text || '-';
			},
		},
		{
			title: '操作',
			dataIndex: 'action',
			width: 180,
			fixed: 'right',
			align: 'center',
			render: (_, record) => (
				<Space>
					<Button type="link" size={'small'} onClick={() => onUpdate({ id: record.id })}>
						编辑
					</Button>
					<Button type="link" size={'small'} danger onClick={() => onDel({ ids: [record.id] })}>
						删除
					</Button>
					<Button type="link" size={'small'} onClick={() => onShare(record)}>
						分享
					</Button>
				</Space>
			),
		},
	];

	return (
		<div>
			{/* 标题 开始 */}
			<div className="flex flex-shrink align-center margin-bottom-4 height-48 font-size-22 font-weight-500 color-1d2129">资讯发布</div>
			{/* 标题 结束 */}

			{/* Tabs & 功能按钮 开始 */}
			<Affix offsetTop={0}>
				<div className="flex align-center justify-between padding-24 border-radius-8 bg-color-ffffff border-solid-bottom-f2f3f5">
					<div className="flex align-center">
						{releaseStatusData.map((ov) => (
							<div
								key={ov.value}
								className={`a margin-right-40 font-size-18 font-weight-500 ${releaseStatus === ov.value ? 'color-165dff' : ''}`}
								onClick={() => setReleaseStatus(ov.value)}
							>
								{ov.label}
							</div>
						))}
					</div>
					<Space size={16}>
						<Button onClick={onImport}>公众号导入</Button>
						<Button type="primary" onClick={onAdd}>
							新建资讯
						</Button>
					</Space>
				</div>
			</Affix>
			{/* Tabs & 功能按钮 结束 */}

			<div className="flex-sub margin-top-18 padding-24 border-radius-8 bg-color-ffffff">
				{/* 筛选条件 开始 */}
				<div className="flex justify-between margin-bottom-18">
					<div className="flex-sub">
						<Form form={form} labelAlign="right" layout="inline">
							<Form.Item name="keywords" label={'搜索关键词'}>
								<Input placeholder="请输入搜索关键词" className={'width-200'} allowClear />
							</Form.Item>
						</Form>
					</div>
					<div className="margin-left-12 padding-left-12 border-solid-left-e5e6eb">
						<Space size={16}>
							<Button onClick={onReset}>重置</Button>
							<Button type="primary" onClick={onSearch}>
								查询
							</Button>
						</Space>
					</div>
				</div>
				{/* 筛选条件 结束 */}
				{/* 表格 开始 */}
				<Table
					className={'width-100per'}
					rowKey="id"
					dataSource={dataSource}
					columns={columns}
					pagination={pagination}
					onChange={changePage}
					scroll={{ x: 'max-content' }}
				/>
				{/* 表格 结束 */}
			</div>

			{/* 表单弹窗 开始 */}
			<InformationModal
				ref={modalRef}
				updateTableItemData={updateTableItemData}
				addTableItemData={addTableItemData}
				getListData={onSearch}
				areaList={areaList}
			/>
			{/* 表单弹窗 结束 */}
		</div>
	);
};

export default InformationRelease;
