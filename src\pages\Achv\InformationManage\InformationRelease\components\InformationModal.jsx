/**
 * @description InfomationModal - 文件描述
 * <AUTHOR>
 *
 * Created on 2024/10/9 14:26
 */
import React, { forwardRef, useImperativeHandle, useMemo, useState } from 'react';
import { DatePicker, Divider, Form, Input, InputNumber, Modal, message, Cascader, Button, Affix, Anchor, Space, Image } from 'antd';
import dayjs from 'dayjs';
import UploadImg from '@/components/UploadImg';
// import WangEditor from "@/components/WangEditor";
import UEditor from '@/components/UEditor';
import { EditOutlined } from '@ant-design/icons';
import './styles.scss';
// import ReactUEditor from 'react-ueditor-wrap';

const InformationModal = forwardRef(({ addTableItemData, updateTableItemData, getListData, areaList, isPage = false }, ref) => {
	const [open, setOpen] = useState(false);
	const [detail, setDetail] = useState({});
	const [isMp, setIsMp] = useState(true);
	const [editing, setEditing] = useState(false);
	const [form] = Form.useForm();
	/* 关闭弹窗 */
	const handleCancel = () => {
		if (isPage) {
			history.back();
		} else if (open) {
			setOpen(false);
			form.resetFields();
		}
	};
	const handleBack = () => {
		// history.back();
		setEditing(false);
	};
	const setEditModal = () => {
		setEditing(true);
	};
	/* 提交表单 */
	const handleSubmit = async () => {
		/* 表单验证 */
		const values = await form.validateFields();
		const params = form.getFieldsValue();
		params.id = detail.id;
		params.releaseTime = params.releaseTime ? params.releaseTime.format('YYYY-MM-DD HH:mm:ss') : dayjs(new Date()).format('YYYY-MM-DD HH:mm:ss');
		console.log('params', params);
		const releaseArea = params.releaseArea;
		if (releaseArea) {
			params.provinceCode = releaseArea[0];
			params.cityCode = releaseArea[1];
		}
		let res;
		if (params.id) {
			res = await updateTableItemData(params);
		} else {
			res = await addTableItemData(params);
		}
		if (res.data) {
			message.success('提交成功');
			handleCancel();
			/* 刷新列表数据 */
			getListData();
		} else {
			message.error('提交失败');
		}
	};
	/* 打开弹框 */
	const showModal = (data) => {
		setOpen(true);
		setDetail(data);
		let { releaseTime, isMp, cityCode, provinceCode, ...extra } = data;
		const formData = {
			...extra,
			releaseTime: releaseTime ? dayjs(releaseTime) : undefined,
			releaseArea: cityCode ? [provinceCode, cityCode] : [],
		};
		setIsMp(isMp);
		console.log('detail', formData);
		form.setFieldsValue(formData);
	};
	const openPage = (data) => {
		setDetail(data);
		let { releaseTime, isMp, cityCode, provinceCode, ...extra } = data;
		const formData = {
			...extra,
			releaseTime: releaseTime ? dayjs(releaseTime) : undefined,
			releaseArea: cityCode ? [provinceCode, cityCode] : [],
		};
		setIsMp(isMp);
		console.log('detail', formData);
		if (!data.id) {
			setEditModal();
		}
		form.setFieldsValue(formData);
	};
	useImperativeHandle(ref, () => ({
		showModal,
		openPage,
	}));

	const formDom = useMemo(() => {
		return (
			<Form form={form} labelCol={{ style: { width: '116px' } }}>
				{/* 基本信息 开始 */}
				<div id="section1" />
				<div className="margin-top-20 padding-20 border-radius-8 bg-color-ffffff">
					{!isMp && <div className="padding-bottom-30 line-height-26 font-size-18 font-weight-500 color-1d2129">基本信息</div>}
					<Form.Item name="title" label="标题" rules={[{ required: true, message: '请输入标题' }]}>
						<Input placeholder="请输入标题" />
					</Form.Item>
					<Form.Item name="logoUrl" label="封面" rules={[{ required: true, message: '请上传封面' }]}>
						<UploadImg size={1} width={200} height={200} tips={'建议尺寸：200*200px'} cropperProps={{ width: 200, height: 200 }} />
					</Form.Item>
					<Form.Item name="releaseOrg" label="来源" rules={[{ required: true, message: '请输入内容' }]}>
						<Input placeholder="请输入来源" />
					</Form.Item>
					<Form.Item name="releaseArea" label="所属区域" rules={[{ required: true, message: '请选择所属区域' }]}>
						<Cascader options={areaList} placeholder="请选择所属区域" />
					</Form.Item>
					{isMp && (
						<Form.Item
							name="originalUrl"
							label="文章链接"
							placeholder="请输入文章链接"
							rules={[
								{ required: true, message: '请输入文章链接' },
								{ type: 'url', message: '请输入正确的链接' },
							]}
						>
							<Input />
						</Form.Item>
					)}
					<Form.Item name="releaseTime" label="发布时间">
						<DatePicker showTime format="YYYY-MM-DD HH:mm:ss" />
					</Form.Item>
					<Form.Item name="rankingNum" label="排序">
						<InputNumber placeholder="序号" />
					</Form.Item>
				</div>
				{!isMp && (
					<div>
						{/* 资讯描述 */}
						<div id="section2" />
						<div className="margin-top-20 padding-20 border-radius-8 bg-color-ffffff">
							<div className="padding-bottom-30 line-height-26 font-size-18 font-weight-500 color-1d2129">资讯描述</div>
							<Form.Item name="content" wrapperCol={{ span: 24 }} rules={[{ required: true, message: '请输入正文内容' }]}>
								<UEditor />
							</Form.Item>
						</div>
					</div>
				)}
			</Form>
		);
	}, [form, isMp, areaList]);

	const detailDom = useMemo(() => {
		return (
			<div>
				{/* 基本信息 开始 */}
				<div id="section1" />
				<div className="margin-top-20 padding-20 border-radius-8 bg-color-ffffff">
					<div className={'flex align-center justify-between margin-bottom-20'}>
						<div className=" line-height-26 font-size-18 font-weight-500 color-1d2129">基本信息</div>
						<Button type="link" icon={<EditOutlined />} onClick={setEditModal}>
							编辑信息
						</Button>
					</div>
					<div className="flex align-center justify-start font-size-14 line-height-22 padding-left-16 margin-bottom-20">
						<div className="color-86909c margin-right-4">标题：</div>
						<div className="">{detail.title || '--'}</div>
					</div>
					<div className="flex align-center justify-start font-size-14 line-height-22 padding-left-16 margin-bottom-20">
						<div className="color-86909c margin-right-4">封面：</div>
						<div className="">
							<Image src={detail.logoUrl} width={200} height={200} />
						</div>
					</div>
					<div className="flex align-center justify-start font-size-14 line-height-22 padding-left-16 margin-bottom-20">
						<div className="color-86909c margin-right-4">来源：</div>
						<div className="">{detail.releaseOrg || '--'}</div>
					</div>
					<div className="flex align-center justify-start font-size-14 line-height-22 padding-left-16 margin-bottom-20">
						<div className="color-86909c margin-right-4">所属区域：</div>
						<div className="">{[detail.provinceCodeName, detail.cityCodeName].filter((ov) => ov).join('-') || '--'}</div>
					</div>
					<div className="flex align-center justify-start font-size-14 line-height-22 padding-left-16 margin-bottom-20">
						<div className="color-86909c margin-right-4">发布时间：</div>
						<div className="">{detail.releaseTime || '--'}</div>
					</div>
					{isMp && (
						<div className="flex align-center justify-start font-size-14 line-height-22 padding-left-16 margin-bottom-20">
							<div className="color-86909c margin-right-4">文章链接：</div>
							<div className="">{detail.originalUrl || '--'}</div>
						</div>
					)}
					<div className="flex align-center justify-start font-size-14 line-height-22 padding-left-16 margin-bottom-20">
						<div className="color-86909c margin-right-4">排序：</div>
						<div className="">{detail.rankingNum || '--'}</div>
					</div>
				</div>
				{/* 资讯描述 */}
				{!isMp && (
					<>
						<div id="section2" />
						<div className="margin-top-20 padding-20 border-radius-8 bg-color-ffffff">
							<div className={'flex align-center justify-between margin-bottom-20'}>
								<div className=" line-height-26 font-size-18 font-weight-500 color-1d2129">资讯描述</div>
								<Button type="link" icon={<EditOutlined />} onClick={setEditModal}>
									编辑信息
								</Button>
							</div>
							<div className="flex align-center justify-start font-size-14 line-height-22 padding-left-16 margin-bottom-20">
								<div className="pre-wrap detail-editor-content" dangerouslySetInnerHTML={{ __html: detail.content || '' }} />
							</div>
							·
						</div>
					</>
				)}
			</div>
		);
	}, [isMp, detail]);
	return isPage ? (
		<div className={'margin-top-18'}>
			{/* Tabs & 功能按钮 开始 */}
			<Affix>
				<div className="height-66 flex align-center justify-between bg-color-ffffff border-radius-8 padding-lr-24 border-solid-bottom-f2f3f5">
					<div className="flex align-center justify-between color-1d2129 line-height-26 font-size-18 font-weight-500">
						{!isMp && (
							<Anchor
								affix={false}
								rootClassName="custom-anchor-box"
								direction="horizontal"
								replace
								targetOffset={80}
								items={[
									{
										key: 'section1',
										href: '#section1',
										title: <div className="margin-right-40 font-size-16 font-weight-500">基本信息</div>,
									},
									{
										key: 'section2',
										href: '#section2',
										title: <div className="margin-right-40 font-size-16 font-weight-500">资讯描述</div>,
									},
								]}
							/>
						)}
					</div>
					<div className="flex align-center justify-between color-1d2129 line-height-26 font-size-18 font-weight-500">
						{editing && (
							<Space size={16}>
								{detail?.id ? <Button onClick={handleBack}>取消</Button> : <Button onClick={handleCancel}>取消</Button>}
								<Button type={'primary'} onClick={handleSubmit}>
									保存
								</Button>
							</Space>
						)}
					</div>
				</div>
			</Affix>
			{/* Tabs & 功能按钮 结束 */}
			<div>{editing ? formDom : detailDom}</div>
		</div>
	) : (
		<Modal
			open={open}
			onCancel={handleCancel}
			onOk={handleSubmit}
			title={detail?.id ? '修改资讯内容' : '新增资讯内容'}
			maskClosable={false}
			width={1200}
		>
			<Divider style={{ margin: '24px -24px', width: 'auto' }} />
			{formDom}
		</Modal>
	);
});
export default InformationModal;
