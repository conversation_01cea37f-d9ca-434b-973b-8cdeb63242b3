import { useEffect, useState } from 'react';
import { useRouterLink } from '@/hook/useRouter';

import { Button, Space, Form, Input, InputNumber, message, Affix, Anchor, Select } from 'antd';

import UploadImg from '@/components/UploadImg';
import FormComp from '@/components/FormComp';

import { getLive, addLive, updateLive } from '@/api/Achv/InformationManage/LiveRelease/index';
import dayjs from 'dayjs';
import { originOptions } from '../config';

const Index = () => {
	const { linkTo, searchParams } = useRouterLink();
	const id = searchParams.get('id') || '';
	const [form] = Form.useForm();

	// 取消
	const onCancel = () => {
		linkTo(-1);
	};

	// 提交
	const onSubmit = () => {
		form.validateFields().then((values) => {
			if (values.id) {
				updateLive(values).then(() => {
					message.success('修改成功');
					setTimeout(() => {
						linkTo(-1);
					}, 500);
				});
			} else {
				addLive(values).then(() => {
					message.success('添加成功');
					setTimeout(() => {
						linkTo(-1);
					}, 500);
				});
			}
		});
	};

	// 获取详情
	const getDetail = () => {
		if (id) {
			getLive({ id }).then((res) => {
				const resData = res.data || {};
				form.setFieldsValue(resData);

				// 是否同主体
				if (resData.feedToken) {
					setIsSubject(false);
				}
			});
		}
	};

	useEffect(() => {
		getDetail();
	}, []);

	return (
		<div className="">
			<div className="flex flex-shrink align-center margin-bottom-4 height-48 font-size-22 font-weight-500 color-1d2129">
				<Space size={10}>
					<div className="a color-86909c" onClick={() => linkTo('/newAchv/informationManage/LiveRelease')}>
						直播发布
					</div>
					<div className="color-86909c">/</div>
					<div>{id ? '直播编辑' : '新增直播'}</div>
				</Space>
			</div>

			{/* Tabs & 功能按钮 开始 */}
			<Affix>
				<div className="height-66 flex align-center justify-between bg-color-ffffff border-radius-8 padding-lr-24 border-solid-bottom-f2f3f5">
					<div className="flex align-center justify-between color-1d2129 line-height-26 font-size-18 font-weight-500">
						<Anchor
							affix={false}
							rootClassName="custom-anchor-box"
							direction="horizontal"
							replace
							targetOffset={80}
							items={[
								{
									key: 'section1',
									href: '#section1',
									title: <div className="margin-right-40 font-size-16 font-weight-500">直播信息</div>,
								},
								{
									key: 'section2',
									href: '#section2',
									title: <div className="margin-right-40 font-size-16 font-weight-500">排序备注</div>,
								},
							]}
						/>
					</div>
					<div className="flex align-center justify-between color-1d2129 line-height-26 font-size-18 font-weight-500">
						<Space size={16}>
							<Button onClick={onCancel}>取消</Button>
							<Button type="primary" onClick={onSubmit}>
								保存
							</Button>
						</Space>
					</div>
				</div>
			</Affix>
			{/* Tabs & 功能按钮 结束 */}

			<Form form={form} className="antd-form-box" labelAlign="right" labelCol={{ style: { width: '116px' } }} initialValues={{}}>
				<Form.Item hidden name="id">
					<Input />
				</Form.Item>
				<Form.Item hidden name="liveStatus">
					<Input />
				</Form.Item>
				<Form.Item hidden name="liveSourceLogoUrl">
					<Input />
				</Form.Item>

				{/* 直播信息 开始 */}
				<div id="section1"></div>
				<div className="margin-top-20 padding-20 border-radius-8 bg-color-ffffff">
					<div className="padding-bottom-30 line-height-26 font-size-18 font-weight-500 color-1d2129">直播信息</div>
					<Form.Item label="直播名称" name="name" rules={[{ required: true, message: '请输入标题' }]}>
						<Input className="input-box" placeholder="请输入标题" />
					</Form.Item>
					<Form.Item label="开始时间" name="startTime" rules={[{ required: true, message: '请输入开始时间' }]}>
						<FormComp.DatePicker minDate={dayjs(new Date())} placeholder="请选择开始时间" showTime format="YYYY-MM-DD HH:mm" />
					</Form.Item>
					<Form.Item label="直播地址" name="address" rules={[{ required: true, message: '请输入直播地址' }]}>
						<Input className="input-box" placeholder="请输入直播地址" />
					</Form.Item>
					<Form.Item label="直播来源" name="liveSource" rules={[{ required: true, message: '请选择直播来源' }]}>
						<LiveSourceSelect form={form} />
					</Form.Item>
					<Form.Item label="直播封面" name="coverUrl" rules={[{ required: true, message: '请上传直播封面' }]}>
						<UploadImg size={5} width={154} height={205} tips={'建议尺寸：308*410px'} cropperProps={{ width: 308, height: 410 }} />
					</Form.Item>
					<Form.Item label="分享海报主KV" name="sharePicUrl" rules={[{ required: true, message: '请上传分享海报主KV' }]}>
						<UploadImg size={5} width={200} height={116} tips={'建议尺寸：750*420px'} cropperProps={{ width: 750, height: 420 }} />
					</Form.Item>
				</div>
				{/* 直播信息 结束 */}

				{/* 排序备注 开始 */}
				<div id="section2"></div>
				<div className="margin-top-20 padding-20 border-radius-8 bg-color-ffffff">
					<div className="padding-bottom-30 line-height-26 font-size-18 font-weight-500 color-1d2129">排序备注</div>
					<Form.Item label="备注" name="remarks">
						<Input.TextArea rows={4} placeholder="请输入备注" />
					</Form.Item>
					<Form.Item label="排序" name="rankingNum">
						<InputNumber className="input-number-box" placeholder="请输入" min={1} precision={0} controls={false} />
					</Form.Item>
				</div>
				{/* 排序备注 结束 */}
			</Form>
		</div>
	);
};

// 直播来源
const LiveSourceSelect = (props = {}) => {
	return (
		<Select
			value={props.value}
			options={originOptions.map((ov) => {
				return {
					label: ov.liveSource,
					value: ov.liveSource,
					liveSource: ov.liveSource,
					liveSourceLogoUrl: ov.liveSourceLogoUrl,
				};
			})}
			placeholder="请选择直播来源"
			onChange={(_, { liveSource, liveSourceLogoUrl }) => {
				props.form.setFieldsValue({
					liveSource,
					liveSourceLogoUrl,
				});
			}}
		/>
	);
};

export default Index;
