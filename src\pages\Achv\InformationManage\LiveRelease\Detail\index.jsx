import { useEffect, useState } from 'react';
import { useRouterLink } from '@/hook/useRouter';

import { Button, Space, Affix, Anchor, Image, Modal } from 'antd';
import { EditOutlined } from '@ant-design/icons';

import { getLive, updateLiveStatus } from '@/api/Achv/InformationManage/LiveRelease/index';
import { getLiveStatus } from '../config';

const Index = () => {
	const { linkTo, searchParams } = useRouterLink();
	const id = searchParams.get('id') || '';

	const [detail, setDetail] = useState({});

	// 获取详情
	const getDetail = () => {
		if (id) {
			getLive({ id }).then((res) => {
				const resData = res.data || {};

				setDetail(resData);
			});
		}
	};

	// 改变直播状态 liveStatus 1 直播预告 2 直播中 3 直播回顾 4 结束
	const changeLiveStatus = () => {
		const { liveStatus } = detail;
		Modal.confirm({
			title: liveStatus ? '恢复直播' : '结束直播',
			content: `是否进行${liveStatus ? '恢复直播' : '结束直播'}的操作？`,
			onOk() {
				updateLiveStatus({
					id,
					liveStatus: liveStatus ? '' : 3,
				}).then(() => {
					getDetail();
				});
			},
		});
	};

	useEffect(() => {
		getDetail();
	}, []);

	return (
		<div className="">
			<div className="flex flex-shrink align-center margin-bottom-4 height-48 font-size-22 font-weight-500 color-1d2129">
				<Space size={10}>
					<div className="a color-86909c" onClick={() => linkTo('/newAchv/informationManage/LiveRelease')}>
						直播发布
					</div>
					<div className="color-86909c">/</div>
					<div>直播详情</div>
				</Space>
			</div>
			<div id="section1"></div>
			{/* Tabs & 功能按钮 开始 */}
			<Affix>
				<div className="height-66 flex align-center justify-between bg-color-ffffff border-radius-8 padding-lr-24 border-solid-bottom-f2f3f5">
					<div className="flex align-center justify-between color-1d2129 line-height-26 font-size-18 font-weight-500">
						<Anchor
							affix={false}
							rootClassName="custom-anchor-box"
							direction="horizontal"
							replace
							targetOffset={80}
							items={[
								{
									key: 'section1',
									href: '#section1',
									title: <div className="margin-right-40 font-size-16 font-weight-500">直播信息</div>,
								},
								{
									key: 'section2',
									href: '#section2',
									title: <div className="margin-right-40 font-size-16 font-weight-500">排序备注</div>,
								},
							]}
						/>
					</div>
					<div className="flex align-center justify-between color-1d2129 line-height-26 font-size-18 font-weight-500">
						<Space size={16}>
							<Button type={detail.liveStatus ? '' : 'primary'} onClick={changeLiveStatus}>
								{detail.liveStatus ? '恢复直播' : '结束直播'}
							</Button>
						</Space>
					</div>
				</div>
			</Affix>
			{/* Tabs & 功能按钮 结束 */}

			{/* 直播信息 开始 */}
			<div className="margin-top-20 padding-20 border-radius-8 bg-color-ffffff">
				<div className="flex align-center justify-between margin-bottom-20">
					<div className="font-size-18 line-height-26 font-weight-500">直播信息</div>
					<Button
						type="link"
						icon={<EditOutlined />}
						onClick={() => {
							linkTo(`/newAchv/informationManage/LiveRelease/curd?id=${id}`);
						}}
					>
						编辑信息
					</Button>
				</div>
				<div className="flex align-center justify-start font-size-14 line-height-22 padding-left-16 margin-bottom-20">
					<div className="color-86909c margin-right-4">直播状态：</div>
					<div className="">
						<div
							className={`tag-status-${
								['', 'primary', 'error', 'warning', 'default'][detail.liveStatus || getLiveStatus(detail.startTime) || 0]
							}`}
						>
							{['--', '直播预告', '直播中', '直播回顾', '结束'][detail.liveStatus || getLiveStatus(detail.startTime) || 0]}
						</div>
					</div>
				</div>
				<div className="flex align-center justify-start font-size-14 line-height-22 padding-left-16 margin-bottom-20">
					<div className="color-86909c margin-right-4">发布状态：</div>
					<div className="">
						<div className={`tag-status-${['warning', 'primary'][detail.releaseStatus]}`}>
							{detail.releaseStatus ? '已发布' : '待发布'}
						</div>
					</div>
				</div>
				<div className="flex align-center justify-start font-size-14 line-height-22 padding-left-16 margin-bottom-20">
					<div className="color-86909c margin-right-4">是否推荐：</div>
					<div className="">
						<div className={`tag-status-${['warning', 'primary'][detail.recommendStatus]}`}>{detail.recommendStatus ? '是' : '否'}</div>
					</div>
				</div>
				<div className="flex align-center justify-start font-size-14 line-height-22 padding-left-16 margin-bottom-20">
					<div className="color-86909c margin-right-4">直播名称：</div>
					<div className="">{detail.name || '--'}</div>
				</div>
				<div className="flex align-center justify-start font-size-14 line-height-22 padding-left-16 margin-bottom-20">
					<div className="color-86909c margin-right-4">开始时间：</div>
					<div className="">{(detail.startTime || '--').slice(0, 16)}</div>
				</div>
				<div className="flex align-center justify-start font-size-14 line-height-22 padding-left-16 margin-bottom-20">
					<div className="color-86909c margin-right-4">直播地址：</div>
					<div className="">{detail.address || '--'}</div>
				</div>
				<div className="flex align-center justify-start font-size-14 line-height-22 padding-left-16 margin-bottom-20">
					<div className="color-86909c margin-right-4">直播来源：</div>
					<div className="">{detail.liveSource || '--'}</div>
				</div>
				<div className="flex justify-start font-size-14 line-height-22 padding-left-16 margin-bottom-20">
					<div className="color-86909c margin-right-4">直播封面：</div>
					<div className="">{(detail.coverUrl && <Image width={120} src={detail.coverUrl} />) || null}</div>
				</div>
				<div className="flex justify-start font-size-14 line-height-22 padding-left-16 margin-bottom-20">
					<div className="color-86909c margin-right-4">分享海报主KV：</div>
					<div className="">{(detail.sharePicUrl && <Image width={120} src={detail.sharePicUrl} />) || null}</div>
				</div>
			</div>
			{/* 直播信息 结束 */}

			{/* 排序备注 开始 */}
			<div id="section2"></div>
			<div className="margin-top-20 padding-20 border-radius-8 bg-color-ffffff">
				<div className="flex align-center justify-between margin-bottom-20">
					<div className="font-size-18 line-height-26 font-weight-500">排序备注</div>
					<Button
						type="link"
						icon={<EditOutlined />}
						onClick={() => {
							linkTo(`/newAchv/informationManage/LiveRelease/curd?id=${id}`);
						}}
					>
						编辑信息
					</Button>
				</div>

				<div className="flex align-center justify-start font-size-14 line-height-22 padding-left-16 margin-bottom-20">
					<div className="color-86909c margin-right-4">备注：</div>
					<div className="">{detail.remarks || '--'}</div>
				</div>
				<div className="flex align-center justify-start font-size-14 line-height-22 padding-left-16 margin-bottom-20">
					<div className="color-86909c margin-right-4">排序：</div>
					<div className="">{detail.rankingNum || '--'}</div>
				</div>
			</div>
			{/* 排序备注 结束 */}
		</div>
	);
};

export default Index;
