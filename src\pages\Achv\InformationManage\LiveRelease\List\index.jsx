import { useState } from 'react';
import { <PERSON>, But<PERSON>, Popconfirm, Space, Switch, Affix, message, Form, Select, Row, Col, InputNumber } from 'antd';
import { useTableData } from '@/hook/useTableData';
import { useRouterLink } from '@/hook/useRouter';
import {
	pageLive as getTablePageData,
	delLive as delTableItemData,
	updateRankingNum as batchUpdateSort,
	updateReleaseStatus,
	updateRecommendStatus,
} from '@/api/Achv/InformationManage/LiveRelease/index';

import { releaseStatusData } from '@/pages/Achv/config';
import { originOptions, getLiveStatus } from '../config';

const Index = () => {
	const { linkTo, searchParams } = useRouterLink();

	const status = searchParams.get('releaseStatus');
	console.log('🚀 ~ Index ~ status:', status);
	const [releaseStatus, setReleaseStatus] = useState(status === null || status === '' ? '' : status - 0);

	const { form, dataSource, pagination, changePage, delTableData, getTableData, onReset, onSearch, SortInput } = useTableData({
		params: { releaseStatus },
		getTablePageData,
		delTableItemData,
		batchUpdateSort,
	});

	// 改变状态
	const changeStatus = (row, typeKey, apiFn) => {
		const status = row[typeKey] === 0 ? 1 : 0;
		const params = {
			id: row.id,
		};
		params[typeKey] = status;
		apiFn(params).then(() => {
			message.success('操作成功');
			row[typeKey] = status;
			getTableData();
		});
	};

	return (
		<div className="">
			<div className="flex flex-shrink align-center margin-bottom-4 height-48 font-size-22 font-weight-500 color-1d2129">直播发布</div>

			{/* Tabs & 功能按钮 开始 */}
			<Affix offsetTop={0}>
				<div className="flex align-center justify-between padding-24 border-radius-8 bg-color-ffffff border-solid-bottom-f2f3f5">
					<div className="flex align-center">
						{releaseStatusData.map((ov) => (
							<div
								key={ov.value}
								className={`a margin-right-40 font-size-18 font-weight-500 ${releaseStatus === ov.value ? 'color-165dff' : ''}`}
								onClick={() => setReleaseStatus(ov.value)}
							>
								{ov.label}
							</div>
						))}
					</div>
					<Space size={16}>
						<Button
							type="primary"
							onClick={() => {
								linkTo(`/newAchv/informationManage/LiveRelease/curd`);
							}}
						>
							新增直播
						</Button>
					</Space>
				</div>
			</Affix>
			{/* Tabs & 功能按钮 结束 */}

			<div className="flex-sub margin-top-18 padding-24 border-radius-8 bg-color-ffffff">
				{/* 筛选条件 开始 */}
				<div className="flex justify-between margin-bottom-18">
					<div className="flex-sub">
						<Form form={form} labelAlign="right" layout="inline">
							<Row className="width-100per" gutter={[12, 12]}>
								<Col span={8}>
									<Form.Item label="直播来源" name="liveSource">
										<Select
											placeholder="请选择直播号"
											options={originOptions.map((ov) => {
												return {
													label: ov.liveSource,
													value: ov.liveSource,
												};
											})}
										/>
									</Form.Item>
								</Col>
							</Row>
						</Form>
					</div>
					<div className="margin-left-12 padding-left-12 border-solid-left-e5e6eb">
						<Space size={16}>
							<Button onClick={onReset}>重置</Button>
							<Button type="primary" onClick={onSearch}>
								查询
							</Button>
						</Space>
					</div>
				</div>
				{/* 筛选条件 结束 */}

				{/* 表格 开始 */}
				<Table rowKey="id" dataSource={dataSource} pagination={pagination} onChange={changePage} scroll={{ x: 'max-content' }}>
					<Table.Column
						title="序号"
						key="index"
						width={60}
						align="center"
						render={(_, __, index) => {
							return index + 1;
						}}
					/>

					<Table.Column title="直播名称" dataIndex="name" render={(text) => <div className="max-width-400">{text}</div>} />
					<Table.Column title="直播来源" dataIndex="liveSource" />
					<Table.Column
						title="开始时间"
						dataIndex="startTime"
						render={(text) => <div className="max-width-400">{(text || '').slice(0, 16)}</div>}
					/>
					{/* 1预告 2 直播中 3直播回顾 4 结束 */}
					<Table.Column
						title="直播状态"
						dataIndex="liveStatus"
						align="center"
						render={(_, record) => {
							const { startTime, liveStatus } = record;
							const status = liveStatus || getLiveStatus(startTime) || 0;
							return (
								<div className={`tag-status-small-${['', 'primary', 'error', 'warning', 'default'][status]}`}>
									{['--', '直播预告', '直播中', '直播回顾', '结束'][status]}
								</div>
							);
						}}
					/>

					<Table.Column
						title="发布状态"
						dataIndex="releaseStatus"
						align="center"
						render={(text, record) => {
							return (
								<Switch
									checked={text === 1}
									onChange={() => {
										changeStatus(record, 'releaseStatus', updateReleaseStatus);
									}}
								/>
							);
						}}
					/>
					<Table.Column
						title="推荐状态"
						dataIndex="recommendStatus"
						align="center"
						render={(text, record) => {
							return (
								<Switch
									checked={text === 1}
									onChange={() => {
										changeStatus(record, 'recommendStatus', updateRecommendStatus);
									}}
								/>
							);
						}}
					/>
					<Table.Column
						title="排序"
						align="center"
						dataIndex="rankingNum"
						render={(_, record) => {
							return <SortInput record={record} />;
						}}
					/>
					<Table.Column
						title="操作"
						key="option"
						dataIndex="option"
						align="center"
						fixed="right"
						width="160px"
						render={(_, record) => {
							return (
								<>
									<Button
										type="link"
										size="small"
										onClick={() => linkTo(`/newAchv/informationManage/LiveRelease/detail?id=${record.id}`)}
									>
										详情/编辑
									</Button>
									<Popconfirm
										title="提示"
										description="确定删除吗？"
										onConfirm={() => {
											delTableData(record.id);
										}}
										okText="确定"
										cancelText="取消"
									>
										<Button type="link" size="small">
											删除
										</Button>
									</Popconfirm>
								</>
							);
						}}
					/>
				</Table>
				{/* 表格 结束 */}
			</div>
		</div>
	);
};

export default Index;
