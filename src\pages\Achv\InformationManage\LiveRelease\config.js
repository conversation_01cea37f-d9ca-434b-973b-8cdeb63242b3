// 来源列表  isSubject 是否同主体
export const originOptions = [
	{
		index: 0,
		liveSource: '湾创直播',
		liveSourceLogoUrl: 'https://gbac-produce.oss-cn-shenzhen.aliyuncs.com/static-assets/gbac-achv-applet/Home/origin-logo-wczb.png',
	},
	{
		index: 1,
		liveSource: '大湾区创新',
		liveSourceLogoUrl: 'https://gbac-produce.oss-cn-shenzhen.aliyuncs.com/static-assets/gbac-achv-applet/Home/origin-logo-dwqcx.png',
	},
	{
		index: 2,
		liveSource: '汤逊湖创新',
		liveSourceLogoUrl: 'https://gbac-produce.oss-cn-shenzhen.aliyuncs.com/static-assets/gbac-achv-applet/Home/origin-logo-txhcx.png',
	},
];

// 1 直播预告 2 直播中 3 直播回顾 4 结束
export const getLiveStatus = (startTime) => {
	if (!startTime) {
		return '';
	}
	const startData = new Date(startTime).valueOf();
	const currentData = new Date().valueOf();

	if (currentData > startData) {
		return 2;
	} else {
		return 1;
	}
};
