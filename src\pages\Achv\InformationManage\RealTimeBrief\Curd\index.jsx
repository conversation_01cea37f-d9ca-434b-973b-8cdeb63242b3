/**
 * @description Curd.jsx - 文件描述
 * <AUTHOR>
 *
 * Created on 2024/10/11 17:21
 */
import React, { useEffect, useRef, useState } from 'react';
import { Space } from 'antd';
import { useRouterLink } from '@/hook/useRouter';
import { getNewsletterDetail, newsletterAdd, newsletterUpdate, urlSummary } from '@/api/Achv/InformationManage/realTimeBrief';
import UpdateModal from '@/pages/Achv/InformationManage/RealTimeBrief/components/UpdateModal';

const Curd = () => {
	const { linkTo, searchParams } = useRouterLink();
	const [detail, setDetail] = useState({});
	const modalRef = useRef();
	useEffect(() => {
		const urlParamsId = searchParams.get('id');
		if (urlParamsId) {
			getDetail({ id: urlParamsId });
		} else {
			modalRef.current?.openPage({});
		}
	}, [searchParams]);

	/* 获取详情数据 */
	const getDetail = async (params) => {
		const res = await getNewsletterDetail(params);
		if (res.data) {
			const data = res.data;
			setDetail(data);
			modalRef.current?.openPage(res.data);
		}
	};
	const getListData = () => {};
	return (
		<div>
			<div className="flex flex-shrink align-center margin-bottom-4 height-48 font-size-22 font-weight-500 color-1d2129">
				<Space size={10}>
					<div className="a color-86909c" onClick={() => linkTo('/newAchv/informationManage/realTimeBrief')}>
						实时简报
					</div>
					<div className="color-86909c">/</div>
					<div>{detail?.id ? '修改简报' : '新增简报'}</div>
				</Space>
			</div>
			<div className={'border-radius-8'}>
				<UpdateModal
					ref={modalRef}
					urlSummary={urlSummary}
					addTableItemData={newsletterAdd}
					updateTableItemData={newsletterUpdate}
					getListData={getListData}
					isPage={true}
				/>
			</div>
		</div>
	);
};
export default Curd;
