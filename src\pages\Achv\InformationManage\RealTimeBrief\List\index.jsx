/**
 * @description index.jsx - 文件描述
 * <AUTHOR>
 *
 * Created on 2024/10/8 15:36
 */
import React, { useRef, useState } from 'react';

import {
	pageNewsletter,
	newsletterDel as delTableItemData,
	newsletterAdd,
	newsletterUpdate,
	urlSummary,
} from '@/api/Achv/InformationManage/realTimeBrief';

import { useTableData } from '@/hook/useTableData';
import { Button, Form, Input, Modal, Space, Table, Affix, message } from 'antd';
import UpdateModal from '@/pages/Achv/InformationManage/RealTimeBrief/components/UpdateModal';
import { useRouterLink } from '@/hook/useRouter';

import { releaseStatusData } from '@/pages/Achv/config';

const RealTimeBrief = () => {
	const updateRef = useRef();
	const { linkTo, searchParams } = useRouterLink();
	const status = searchParams.get('releaseStatus');
	const [releaseStatus, setReleaseStatus] = useState(status === null || status === '' ? '' : status - 0);

	const { form, dataSource, pagination, changePage, onSearch, onReset } = useTableData({
		params: { releaseStatus },
		getTablePageData: pageNewsletter,
	});

	const columns = [
		{
			title: '序号',
			dataIndex: 'index',
			width: 80,
			fixed: 'left',
			align: 'center',
			render: (_, __, index) => index + 1 + (pagination.current - 1) * pagination.pageSize,
		},
		{
			title: '标题',
			dataIndex: 'title',
		},
		{
			title: '来源',
			dataIndex: 'source',
		},
		{
			title: '发布时间',
			dataIndex: 'releaseTime',
		},
		{
			title: '操作',
			dataIndex: 'action',
			width: 140,
			fixed: 'right',
			align: 'center',
			render: (_, record) => (
				<Space>
					<Button type="link" size={'small'} onClick={() => onUpdate({ id: record.id })}>
						编辑
					</Button>
					<Button type="link" size={'small'} danger onClick={() => onDel({ ids: [record.id] })}>
						删除
					</Button>
				</Space>
			),
		},
	];

	// 删除数据
	const onDel = (params) => {
		Modal.confirm({
			title: '确认删除吗？',
			content: '删除后无法恢复，确认删除吗？',
			okType: 'danger',
			onOk() {
				delTableItemData(params).then((res) => {
					if (res.data) {
						message.success('删除成功');
						onSearch();
					} else {
						message.error('删除失败');
					}
				});
			},
		});
	};

	// 编辑处理
	const onUpdate = (params) => {
		linkTo('/newAchv/InformationManage/realTimeBrief/curd?type=update&id=' + params.id);
	};

	// 新建处理
	const onAdd = () => {
		linkTo('/newAchv/InformationManage/realTimeBrief/curd?type=add');
	};

	return (
		<div>
			{/* 标题 开始 */}
			<div className="flex flex-shrink align-center margin-bottom-4 height-48 font-size-22 font-weight-500 color-1d2129">实时简报</div>
			{/* 标题 结束 */}

			{/* Tabs & 功能按钮 开始 */}
			<Affix offsetTop={0}>
				<div className="flex align-center justify-between padding-24 border-radius-8 bg-color-ffffff border-solid-bottom-f2f3f5">
					<div className="flex align-center">
						{releaseStatusData.map((ov) => (
							<div
								key={ov.value}
								className={`a margin-right-40 font-size-18 font-weight-500 ${releaseStatus === ov.value ? 'color-165dff' : ''}`}
								onClick={() => setReleaseStatus(ov.value)}
							>
								{ov.label}
							</div>
						))}
					</div>
					<Space size={16}>
						<Button type="primary" onClick={onAdd}>
							新建简报
						</Button>
					</Space>
				</div>
			</Affix>
			{/* Tabs & 功能按钮 结束 */}

			<div className="flex-sub margin-top-18 padding-24 border-radius-8 bg-color-ffffff">
				{/* 筛选条件 开始 */}
				<div className="flex justify-between margin-bottom-18">
					<div className="flex-sub">
						<Form form={form} labelAlign="right" layout="inline">
							<div className={'flex justify-between align-center width-100per'}>
								<div className={'flex-sub'}>
									<Form.Item name="title" label={'标题'}>
										<Input placeholder="请输入标题" className={'width-200'} allowClear />
									</Form.Item>
								</div>
							</div>
						</Form>
					</div>
					<div className="margin-left-12 padding-left-12 border-solid-left-e5e6eb">
						<Space size={16}>
							<Button onClick={onReset}>重置</Button>
							<Button type="primary" onClick={onSearch}>
								查询
							</Button>
						</Space>
					</div>
				</div>
				{/* 筛选条件 结束 */}
				{/* 表格 开始 */}
				<Table
					className={'width-100per'}
					rowKey="id"
					dataSource={dataSource}
					columns={columns}
					pagination={pagination}
					onChange={changePage}
					scroll={{ x: 'max-content' }}
				/>
				{/* 表格 结束 */}
			</div>

			{/* 表单弹窗 开始 */}
			<UpdateModal
				ref={updateRef}
				urlSummary={urlSummary}
				addTableItemData={newsletterAdd}
				updateTableItemData={newsletterUpdate}
				getListData={onSearch}
			/>
			{/* 表单弹窗 结束 */}
		</div>
	);
};
export default RealTimeBrief;
