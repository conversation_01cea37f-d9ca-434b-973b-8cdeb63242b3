/**
 * @description UpdateModal - 文件描述
 * <AUTHOR>
 *
 * Created on 2024/10/8 17:00
 */
import React, { forwardRef, useImperativeHandle, useMemo, useState } from 'react';
import { Button, DatePicker, Divider, Form, Input, message, Modal, Select, Space, Radio, Affix, Anchor, Descriptions } from 'antd';
import dayjs from 'dayjs';
import { EditOutlined } from '@ant-design/icons';

/* 文章内容类型 */
const contentTypeOptions = [
	{ label: '文章链接', value: 'url' },
	{ label: '输入内容', value: 'text' },
];
const UpdateModal = forwardRef(({ urlSummary, addTableItemData, updateTableItemData, getListData, isPage = false }, ref) => {
	const [open, setOpen] = useState(false);
	const [detail, setDetail] = useState({});
	const [loading, setLoading] = useState(false);
	const [editing, setEditing] = useState(false);
	const [form] = Form.useForm();
	const handleCancel = () => {
		if (isPage) {
			history.back();
		} else if (open) {
			setOpen(false);
			form.resetFields();
		}
	};

	const handleBack = () => {
		// history.back();
		setEditing(false);
	};

	/* 打开弹框 */
	const showModal = (data) => {
		setOpen(true);
		setDetail(data);
		let { releaseTime, ...extra } = data;
		const formData = {
			...extra,
			releaseTime: releaseTime ? dayjs(releaseTime) : null,
		};
		console.log('detail', formData);
		form.setFieldsValue(formData);
	};
	const openPage = (data) => {
		setDetail(data);
		let { releaseTime, ...extra } = data;
		const formData = {
			...extra,
			releaseTime: releaseTime ? dayjs(releaseTime) : null,
		};
		console.log('detail', formData);
		if (!data.id) {
			setEditModal();
		}
		form.setFieldsValue(formData);
	};
	/* 提交表单 */
	const handleSubmit = async () => {
		const params = form.getFieldsValue();
		delete params.contentType;
		params.id = detail.id;
		params.releaseTime = params.releaseTime ? params.releaseTime.format('YYYY-MM-DD HH:mm:ss') : null;
		console.log('params', params);
		try {
			let res;
			if (params.id) {
				res = await updateTableItemData(params);
			} else {
				res = await addTableItemData(params);
			}
			if (res.data) {
				message.success('提交成功');
				handleCancel();
				/* 刷新列表数据 */
				getListData();
			} else {
				message.error('提交失败');
			}
		} catch (e) {
			console.log(e);
			message.error('提交失败');
		}
	};
	/* 生成AI简报 */
	const handleGenerate = async () => {
		const params = {};
		const type = form.getFieldValue('contentType');
		params[type] = form.getFieldValue('articleLink');
		if (!params[type]) {
			message.error('请输入文章链接/内容');
			return;
		} else if (type === 'url') {
			/* 校验是否为网址，包含http */
			params.url = params.url.trim();
			if (!/^(?:f|ht)tps?:\/\//i.test(params.url)) {
				message.error('请输入正确的网址');
				return;
			}
		}
		setLoading(true);
		try {
			const res = await urlSummary(params);
			console.log(res.data);
			if (res.data) {
				form.setFieldsValue({ introduction: res?.data });
			} else {
				message.error('简报生成失败');
			}
			setLoading(false);
		} catch (e) {
			console.log(e);
			setLoading(false);
		}
	};

	const setEditModal = () => {
		setEditing(true);
	};

	useImperativeHandle(ref, () => ({
		showModal,
		openPage,
	}));

	const formDom = useMemo(() => {
		return (
			<Form form={form} labelCol={{ style: { width: '116px' } }} initialValues={{ contentType: 'url' }}>
				{/* 基本信息 开始 */}
				<div id="section1" />
				<div className="margin-top-20 padding-20 border-radius-8 bg-color-ffffff">
					<Form.Item label="标题" name="title" rules={[{ required: true, message: '请输入标题' }]}>
						<Input placeholder="请输入标题" />
					</Form.Item>
					<Form.Item label="来源" name="source" rules={[{ required: true, message: '请输入来源' }]}>
						<Input placeholder="请输入来源" />
					</Form.Item>
					<Form.Item label="文章链接" name="articleLink" rules={[{ type: 'url', message: '请输入正确的链接' }]}>
						<Input placeholder="请输入文章链接" />
					</Form.Item>
					<Form.Item label="" wrapperCol={{ span: 20, offset: 4 }} hidden>
						<Space align={'center'} className={'width-100per justify-end'}>
							<Form.Item label="文章类型" name="contentType" noStyle hidden>
								<Radio.Group options={contentTypeOptions} />
							</Form.Item>
							<Button type={'primary'} size={'small'} loading={loading} onClick={handleGenerate}>
								AI 生成简报
							</Button>
						</Space>
					</Form.Item>
					<Form.Item label={'简报内容'} name="introduction" rules={[{ required: true, message: '请输入简报内容' }]}>
						<Input.TextArea placeholder="请输入简报内容" rows={5} />
					</Form.Item>
					<Form.Item label="发布时间" name="releaseTime">
						<DatePicker showTime format="YYYY-MM-DD HH:mm:ss" placeholder="请选择发布时间" />
					</Form.Item>
				</div>
			</Form>
		);
	}, [form, loading, contentTypeOptions]);

	const detailDom = useMemo(() => {
		return (
			<div>
				{/* 基本信息 开始 */}
				<div id="section1" />
				<div className="margin-top-20 padding-20 border-radius-8 bg-color-ffffff">
					<div className={'flex align-center justify-between margin-bottom-20'}>
						<div className=" line-height-26 font-size-18 font-weight-500 color-1d2129">简报</div>
						<Button type="link" icon={<EditOutlined />} onClick={setEditModal}>
							编辑信息
						</Button>
					</div>
					<div className="flex align-center justify-start font-size-14 line-height-22 padding-left-16 margin-bottom-20">
						<div className="color-86909c margin-right-4">标题：</div>
						<div className="">{detail.title || '--'}</div>
					</div>
					<div className="flex align-center justify-start font-size-14 line-height-22 padding-left-16 margin-bottom-20">
						<div className="color-86909c margin-right-4">来源：</div>
						<div className="">{detail.source || '--'}</div>
					</div>
					<div className="flex align-start justify-start font-size-14 line-height-22 padding-left-16 margin-bottom-20">
						<div className="color-86909c margin-right-4">文章链接/内容：</div>
						<div className="flex-sub ">{detail.articleLink || '--'}</div>
					</div>
					<div className="flex align-start justify-start font-size-14 line-height-22 padding-left-16 margin-bottom-20">
						<div className="color-86909c margin-right-4">简报内容：</div>
						<div className="flex-sub pre-wrap">{detail.introduction || '--'}</div>
					</div>
					<div className="flex align-center justify-start font-size-14 line-height-22 padding-left-16 margin-bottom-20">
						<div className="color-86909c margin-right-4">发布时间：</div>
						<div className="">{detail.releaseTime || '--'}</div>
					</div>
				</div>
			</div>
		);
	}, [detail]);
	return isPage ? (
		<div className={'margin-top-18'}>
			{/* Tabs & 功能按钮 开始 */}
			{editing && (
				<Affix>
					<div className="height-66 flex align-center justify-between bg-color-ffffff border-radius-8 padding-lr-24 border-solid-bottom-f2f3f5">
						<div className="flex align-center justify-between color-1d2129 line-height-26 font-size-18 font-weight-500"></div>
						<div className="flex align-center justify-between color-1d2129 line-height-26 font-size-18 font-weight-500">
							<Space size={16}>
								{detail?.id ? <Button onClick={handleBack}>取消</Button> : <Button onClick={handleCancel}>取消</Button>}
								<Button type={'primary'} onClick={handleSubmit}>
									保存
								</Button>
							</Space>
						</div>
					</div>
				</Affix>
			)}
			{/* Tabs & 功能按钮 结束 */}
			<div>{editing ? formDom : detailDom}</div>
		</div>
	) : (
		<Modal
			open={open}
			onCancel={handleCancel}
			onOk={handleSubmit}
			title={detail?.id ? '修改简报内容' : '新增简报内容'}
			maskClosable={false}
			width={800}
		>
			<Divider style={{ margin: '24px -24px', width: 'auto' }} />
			{formDom}
		</Modal>
	);
});
export default UpdateModal;
