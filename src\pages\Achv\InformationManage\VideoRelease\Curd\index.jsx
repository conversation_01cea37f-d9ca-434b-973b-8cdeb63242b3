import { useEffect, useState } from 'react';
import { useRouterLink } from '@/hook/useRouter';

import { Button, Space, Form, Input, InputNumber, message, Affix, Tooltip, Select, Radio } from 'antd';
import { QuestionCircleOutlined } from '@ant-design/icons';

import UploadImg from '@/components/UploadImg';
import UploadVideo from '@/components/UploadVideo';
import FormComp from '@/components/FormComp';
import SelectTTChannels from '@/components/Achv/SelectTTChannels';

import { getWechatChannels, addWechatChannels, updateWechatChannels } from '@/api/Achv/InformationManage/VideoRelease/index';
import { pageAccount } from '@/api/Achv/TTChannels/VideoAccount/index';
import { listHashTag } from '@/api/Achv/TTChannels/HashTag';

import { releaseTypeData } from '@/pages/Achv/config';

const Index = (props = {}) => {
	const { linkTo, openNewTab, searchParams } = useRouterLink();
	const id = searchParams.get('id') || '';
	const [form] = Form.useForm();
	const releaseType = Form.useWatch('releaseType', form);
	const videoType = Form.useWatch('videoType', form);
	const releaseTime = Form.useWatch('releaseTime', form);
	const ttWechatChannelsAccountId = Form.useWatch('ttWechatChannelsAccountId', form);

	// 跳转地址
	const linkToPath = props.linkToPath || `/newAchv/informationManage/videoRelease`;

	// 禁止编辑发布类型
	const [disabledReleaseType, setDisabledReleaseType] = useState(false);
	const [hash, setHash] = useState('');

	// 是否同主体  0 非同主体 1 同主体
	const [mainStatus, setMainStatus] = useState(0);

	// 视频号选项
	const [videoAccountOptions, setVideoAccountOptions] = useState([]);
	// 话题选项
	const [hashTagOptions, setHashTagOptions] = useState([]);

	// 取消
	const onCancel = () => {
		linkTo(-1);
	};

	// 提交
	const onSubmit = () => {
		form.validateFields()
			.then((values) => {
				const params = { ...values };
				(values.id ? updateWechatChannels : addWechatChannels)({
					...params,
					ttChannelsIds: params.ttChannelsIds ? params.ttChannelsIds.map((ov) => ov.id) : undefined,
				}).then(() => {
					message.success(values.id ? '修改成功' : '添加成功');
					setTimeout(() => {
						linkTo(-1);
					}, 500);
				});
			})
			.catch((error) => {
				const errorName = error.errorFields[0].name[0];
				if (['ttChannelsIds'].includes(errorName)) {
					setHash('section3');
				} else {
					setHash('section1');
				}
			});
	};

	// 获取详情
	const getDetail = () => {
		if (id) {
			getWechatChannels({ id }).then((res) => {
				const resData = res.data || {};

				// 是否同主体
				setMainStatus(resData.mainStatus);

				// 处理 关联科转号
				resData.ttChannelsIds = resData.ttChannelsList || [];

				// 处理话题
				resData.ttHashtagList = resData.ttHashtagList.map((ov) => ov.id);

				form.setFieldsValue(resData);
				// 编辑 与固定类型 一致时不可以编辑
				setDisabledReleaseType(resData.releaseType === props.releaseType);
			});
		} else if (props.releaseType) {
			// 新建时 如果是固定类型入口就默认类型
			form.setFieldValue('releaseType', props.releaseType);
			// 新建时 固定类型不可编辑
			setDisabledReleaseType(true);
		}
	};

	// 获取选项数据
	const getOptionsData = () => {
		pageAccount({
			pageSize: 100,
			pageNum: 1,
		}).then((res) => {
			setVideoAccountOptions(
				(res.data.records || []).map((ov) => {
					return {
						label: ov.wechatChannelsAccountName,
						value: ov.id,
						mainStatus: ov.mainStatus,
						ttChannelsIds: ov.ttChannelsList,
					};
				})
			);
		});

		listHashTag().then((res) => {
			setHashTagOptions(
				(res.data || []).map((ov) => {
					return {
						label: ov.title,
						value: ov.id,
					};
				})
			);
		});
	};

	useEffect(() => {
		/* 	const videoType = searchParams.get('videoType') || '';
		if (videoType) {
			form.setFieldValue('videoType', videoType - 0);
		}
 */
		const hash = window.location.hash.replace('#', '');
		setHash(hash || 'section1');

		getDetail();

		getOptionsData();
	}, []);

	// 跳转到指定锚点
	useEffect(() => {
		if (hash) {
			setTimeout(() => {
				const element = document.getElementById(hash);
				if (element) {
					element.scrollIntoView({ behavior: 'smooth' });
				}
			}, 500);
		}
	}, [hash]);

	return (
		<div className="">
			<div className="flex flex-shrink align-center margin-bottom-4 height-48 font-size-22 font-weight-500 color-1d2129">
				<Space size={10}>
					<div className="a color-86909c" onClick={() => linkTo(linkToPath)}>
						{props.releaseType === 2 ? '视频管理' : '视频发布'}
					</div>
					<div className="color-86909c">/</div>
					<div>{id ? '视频编辑' : '新增视频'}</div>
				</Space>
			</div>

			{/* Tabs & 功能按钮 开始 */}
			<div id="section1"></div>
			<Affix offsetTop={0}>
				<div className="flex align-center justify-between padding-24 border-radius-8 border-solid-bottom-f2f2f2 bg-color-ffffff">
					<div className="flex align-center">
						{(releaseType === 2 ? ['视频信息', '视频备注', '关联账号'] : ['视频信息', '视频备注']).map((ov, oi) => {
							const currentHash = `section${oi + 1}`;
							return (
								<div
									key={oi}
									className={`a margin-right-40 font-size-18 font-weight-500 ${hash === currentHash ? 'color-165dff' : ''}`}
									onClick={() => {
										if (currentHash === 'section3') {
											form.validateFields()
												.then(() => {
													setHash(`section${oi + 1}`);
												})
												.catch((error) => {
													const errorName = error.errorFields[0].name[0];
													if (['ttChannelsIds'].includes(errorName)) {
														setHash('section3');
													} else {
														setHash('section1');
													}
												});
										} else {
											setHash(`section${oi + 1}`);
										}
									}}
								>
									{ov}
								</div>
							);
						})}
					</div>
					<Space size={16}>
						<Button onClick={onCancel}>取消</Button>
						<Button type="primary" onClick={onSubmit}>
							保存
						</Button>
					</Space>
				</div>
			</Affix>
			{/* Tabs & 功能按钮 结束 */}

			<Form
				form={form}
				className="antd-form-box"
				labelAlign="right"
				labelCol={{ style: { width: '120px' } }}
				initialValues={{
					videoType: 2,
					releaseType: 2,
					releasePlatform: 1,
					ttChannelsIds: [],
					delTTChannelsIds: [],
				}}
			>
				<Form.Item hidden name="id">
					<Input />
				</Form.Item>
				<Form.Item hidden name="videoType">
					<Input />
				</Form.Item>
				<Form.Item hidden name="releasePlatform">
					<Input />
				</Form.Item>
				<Form.Item hidden name="delTTChannelsIds">
					<Input />
				</Form.Item>
				<Form.Item hidden name="rankingNum">
					<Input />
				</Form.Item>

				<div style={{ display: hash !== 'section3' ? 'block' : 'none' }}>
					{/* 视频信息 开始 */}
					<div className="margin-top-20 padding-20 border-radius-8 bg-color-ffffff">
						<div className="padding-bottom-30 line-height-26 font-size-18 font-weight-500 color-1d2129">视频信息</div>
						<Form.Item label="视频标题" name="title" rules={[{ required: true, message: '请输入标题' }]}>
							<Input className="input-box" placeholder="请输入标题" />
						</Form.Item>
						<Form.Item label="视频时长" name="duration" rules={[{ required: true, message: '请输入视频时长' }]}>
							<DurationFormItem />
						</Form.Item>
						{videoType === 2 && (
							<Form.Item label="视频" name="videoUrl" rules={[{ required: true, message: '请上传视频' }]}>
								<UploadVideo size={(1 / 1024) * 200} showTitle="视频链接">
									<Button type="primary" className="margin-right-20">
										点击上传视频
									</Button>
								</UploadVideo>
							</Form.Item>
						)}
						<Form.Item label="视频封面" name="coverUrl" rules={[{ required: true, message: '请上传视频封面' }]}>
							<UploadImg size={5} width={154} height={205} tips={'建议尺寸：308*410px'} cropperProps={{ width: 308, height: 410 }} />
						</Form.Item>
						{videoType === 1 && (
							<Form.Item label="视频号" name="ttWechatChannelsAccountId" rules={[{ required: true, message: '请选择视频号' }]}>
								<Select
									placeholder="请选择视频号"
									options={videoAccountOptions}
									onChange={(_, item) => {
										setMainStatus(item.mainStatus);
										// 如果没有关联账号自动加上
										if (releaseType === 2 && item.ttChannelsIds && form.getFieldValue('ttChannelsIds').length === 0) {
											form.setFieldsValue({
												ttChannelsIds: item.ttChannelsIds,
											});
										}
									}}
								/>
							</Form.Item>
						)}
						{videoType === 1 && ttWechatChannelsAccountId && (
							<Form.Item
								label={mainStatus === 1 ? '视频授权ID' : '视频授权TOKEN'}
								name={mainStatus === 1 ? 'feedId' : 'feedToken'}
								rules={[{ required: true, message: `请输入${mainStatus === 1 ? '视频授权ID' : '视频授权TOKEN'}` }]}
								extra={
									<div
										className="a color-165dff"
										onClick={() => {
											openNewTab(
												`https://developers.weixin.qq.com/miniprogram/dev/framework/open-ability/channels-activity.html#${
													mainStatus === 1 ? 'feedId' : 'feed-token'
												}`
											);
										}}
									>
										视频号获取"{mainStatus === 1 ? '视频授权ID' : '视频授权TOKEN'}"指引
									</div>
								}
							>
								<Input.TextArea rows={2} placeholder="请输入视频ID" />
							</Form.Item>
						)}
						<Form.Item label="发布时间">
							<Space size={0}>
								<Space size={32}>
									<Form.Item noStyle name="releaseTime">
										<FormComp.DatePicker
											placeholder="请选择发布时间"
											showTime
											onCb={(val) => {
												if (!val) {
													form.setFieldValue('isTimingRelease', 0);
												}
											}}
										/>
									</Form.Item>
									<Form.Item noStyle name="isTimingRelease">
										<FormComp.Checkbox disabled={!releaseTime}>定时发布</FormComp.Checkbox>
									</Form.Item>
								</Space>
								<Tooltip
									title={
										<>
											<div>1.不选发布时间将以提交时间作为发布时间</div>
											<div>2.发布时间小于当前时间将于10分钟内发布</div>
										</>
									}
								>
									<QuestionCircleOutlined />
								</Tooltip>
							</Space>
						</Form.Item>

						{releaseType === 2 && (
							<Form.Item label="关联话题" name="ttHashtagList">
								<Select maxCount={2} mode="multiple" options={hashTagOptions} allowClear placeholder="请选择关联话题" />
							</Form.Item>
						)}
						<Form.Item label="发布类型" name="releaseType" required>
							<Radio.Group options={releaseTypeData.filter((ov) => ov.value < 3)} disabled={true || disabledReleaseType} />
						</Form.Item>
					</div>
					{/* 视频信息 结束 */}

					{/* 视频备注 开始 */}
					<div id="section2"></div>
					<div className="margin-top-20 padding-20 border-radius-8 bg-color-ffffff">
						<div className="padding-bottom-30 line-height-26 font-size-18 font-weight-500 color-1d2129">视频备注</div>
						<Form.Item label="备注" name="remarks">
							<Input.TextArea rows={4} placeholder="请输入备注" />
						</Form.Item>
					</div>
					{/* 视频备注 结束 */}
				</div>

				{/* 关联账号 开始 */}
				<div style={{ display: hash === 'section3' ? 'block' : 'none' }}>
					<div id="section3"></div>
					<SelectTTChannels
						form={form}
						name="ttChannelsIds"
						label="关联科转号"
						rules={[{ required: releaseType === 2, type: 'array', message: '请选择关联科转号' }]}
						collectDelIds={true}
					/>
				</div>
				{/* 关联账号 结束 */}
			</Form>
		</div>
	);
};

// 时长
const DurationFormItem = (props) => {
	const [hour, setHour] = useState(0);
	const [minute, setMinute] = useState(0);
	const [second, setSecond] = useState(0);

	useEffect(() => {
		props.onChange(
			[hour ? (hour > 9 ? hour : '0' + hour) : null, minute > 9 ? minute : '0' + minute, second > 9 ? second : '0' + second]
				.filter((ov) => ov !== null)
				.join(':')
		);
	}, [second, minute, hour]);

	useEffect(() => {
		const [second = 0, minute = 0, hour = 0] = props.value ? props.value.split(':').reverse() : [0, 0, 0];

		setHour(hour - 0);
		setMinute(minute - 0);
		setSecond(second - 0);
	}, [props.value]);
	return (
		<Space size={18}>
			<InputNumber value={hour || 0} placeholder="请输入" min={0} precision={0} controls={false} onChange={(value) => setHour(value)} />
			<div>时</div>
			<InputNumber
				value={minute || 0}
				placeholder="请输入"
				min={0}
				max={60}
				precision={0}
				controls={false}
				onChange={(value) => setMinute(value === null ? 0 : value)}
			/>
			<div>分</div>
			<InputNumber
				value={second || 0}
				placeholder="请输入"
				min={0}
				max={60}
				precision={0}
				controls={false}
				onChange={(value) => setSecond(value === null ? 0 : value)}
			/>
			<div>秒</div>
		</Space>
	);
};

export default Index;
