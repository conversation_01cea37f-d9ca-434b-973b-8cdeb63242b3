import { useEffect, useState, useRef, forwardRef, useImperativeHandle } from 'react';
import { useRouterLink } from '@/hook/useRouter';

import { Button, Input, Affix, Anchor, message, Form, Modal, Image, Space } from 'antd';
import { EditOutlined } from '@ant-design/icons';

import ModalForm from '@/components/ModalForm';

import { getWechatChannels, auditRecommendStatus } from '@/api/Achv/InformationManage/VideoRelease/index';
import { releaseTypeTextList, releasePlatformTextList, showStatusTextList, videoTypeTextList } from '@/pages/Achv/config';

const Index = (props = {}) => {
	const ModalFormRef = useRef();
	const { linkTo, searchParams } = useRouterLink();
	const id = searchParams.get('id') || '';
	const fromList = !!searchParams.get('fromList');

	const [detail, setDetail] = useState({});

	// 跳转地址
	const linkToPath = props.linkToPath || `/newAchv/informationManage/videoRelease`;

	// 获取详情
	const getDetail = () => {
		if (id) {
			getWechatChannels({ id }).then((res) => {
				const resData = res.data || {};
				setDetail(resData);
			});
		}
	};

	// 通过审核
	const handlePass = () => {
		Modal.confirm({
			title: '需求审核',
			content: `是否通过审核？`,
			onOk() {
				handleAudit({ status: 3 });
			},
		});
	};

	// 撤销审核
	const handleRevoke = () => {
		Modal.confirm({
			title: '撤销审核',
			content: `是否撤销审核结果？`,
			onOk() {
				handleAudit({ status: 1 });
			},
		});
	};

	// 审核操作
	const handleAudit = (values) => {
		auditRecommendStatus({ ...values, id }).then(() => {
			message.success('操作成功');

			setTimeout(() => {
				linkTo(-1);
			}, 500);
		});
	};

	useEffect(() => {
		getDetail();
	}, []);

	return (
		<div className="">
			<div className="flex flex-shrink align-center margin-bottom-4 height-48 font-size-22 font-weight-500 color-1d2129">
				<Space size={10}>
					<div className="a color-86909c" onClick={() => linkTo(fromList ? -1 : linkToPath)}>
						{props.releaseType === 2 ? '视频管理' : '视频发布'}
					</div>
					<div className="color-86909c">/</div>
					<div>视频详情</div>
				</Space>
			</div>
			<div id="section1"></div>
			{/* Tabs & 功能按钮 开始 */}
			<Affix>
				<div className="height-66 flex align-center justify-between bg-color-ffffff border-radius-8 padding-lr-24 border-solid-bottom-f2f3f5">
					<div className="flex align-center justify-between color-1d2129 line-height-26 font-size-18 font-weight-500">
						<Anchor
							affix={false}
							rootClassName="custom-anchor-box"
							direction="horizontal"
							replace
							targetOffset={80}
							items={[
								{
									key: 'section1',
									href: '#section1',
									title: <div className="margin-right-40 font-size-16 font-weight-500">视频信息</div>,
								},
								{
									key: 'section2',
									href: '#section2',
									title: <div className="margin-right-40 font-size-16 font-weight-500">排序备注</div>,
								},
								{
									key: 'section3',
									href: '#section3',
									title: <div className="margin-right-40 font-size-16 font-weight-500">关联账号</div>,
								},
							].filter((ov) => ov.key !== 'section3' || detail.releaseType === 2)}
						/>
					</div>
					<div className="flex align-center justify-between color-1d2129 line-height-26 font-size-18 font-weight-500">
						{detail.auditStatus === 1 ? (
							<>
								<Button
									type="primary"
									danger
									ghost
									onClick={() => {
										ModalFormRef.current.setOpen(true);
										ModalFormRef.current.setTitle('不通过原因');
									}}
									className="height-40 width-104"
								>
									不通过
								</Button>
								<Button type="primary" onClick={handlePass} className="margin-left-20 height-40 width-104">
									通过审核
								</Button>
							</>
						) : (
							<Button onClick={handleRevoke} className="margin-left-20 height-40 width-104">
								撤销审核
							</Button>
						)}
					</div>
				</div>
			</Affix>
			{/* Tabs & 功能按钮 结束 */}

			{/* 视频信息 开始 */}
			<div className="margin-top-20 padding-20 border-radius-8 bg-color-ffffff">
				<div className="flex align-center justify-between margin-bottom-20">
					<div className="font-size-18 line-height-26 font-weight-500">视频信息</div>
					<Button
						type="link"
						icon={<EditOutlined />}
						onClick={() => {
							linkTo(`${linkToPath}/curd?id=${id}#section1`);
						}}
					>
						编辑信息
					</Button>
				</div>
				<div className="flex align-center justify-start font-size-14 line-height-22 padding-left-16 margin-bottom-20">
					<div className="color-86909c margin-right-4">审核状态：</div>
					<div className="">
						<div className={`tag-status-${['default', 'warning', 'error', 'primary'][detail.auditStatus]}`}>
							{['', '审核中', '审核不通过', '审核通过'][detail.auditStatus] || ''}
						</div>
					</div>
				</div>
				{detail.auditStatus === 2 && (
					<div className="flex align-center justify-start font-size-14 line-height-22 padding-left-16 margin-bottom-20">
						<div className="color-86909c margin-right-4">拒绝原因：</div>
						<div className="">{detail.reason || '无'}</div>
					</div>
				)}
				<div className="flex align-center justify-start font-size-14 line-height-22 padding-left-16 margin-bottom-20">
					<div className="color-86909c margin-right-4">视频类型：</div>
					<div className="">
						<div className={`tag-status-${['', 'primary', 'warning', '700eb2', 'success', 'error'][detail.videoType]}`}>
							{videoTypeTextList[detail.videoType] || '--'}
						</div>
					</div>
				</div>
				<div className="flex align-center justify-start font-size-14 line-height-22 padding-left-16 margin-bottom-20">
					<div className="color-86909c margin-right-4">视频标题：</div>
					<div className="">{detail.title || '--'}</div>
				</div>
				<div className="flex align-center justify-start font-size-14 line-height-22 padding-left-16 margin-bottom-20">
					<div className="color-86909c margin-right-4">视频时长：</div>
					<div className="">{detail.duration || '--'}</div>
				</div>
				{detail.videoType === 2 && (
					<div className="flex justify-start font-size-14 line-height-22 padding-left-16 margin-bottom-20">
						<div className="color-86909c margin-right-4">视频：</div>
						<div className="flex-sub ">{detail.videoUrl || '--'}</div>
					</div>
				)}
				<div className="flex justify-start font-size-14 line-height-22 padding-left-16 margin-bottom-20">
					<div className="color-86909c margin-right-4">视频封面：</div>
					<div className="">
						<Image src={detail.coverUrl} width={200} />
					</div>
				</div>
				{detail.videoType === 1 && (
					<div className="flex align-center justify-start font-size-14 line-height-22 padding-left-16 margin-bottom-20">
						<div className="color-86909c margin-right-4">视频号：</div>
						<div className="">{detail.channelsName || '--'}</div>
					</div>
				)}
				{detail.videoType === 1 && (
					<div className="flex align-center justify-start font-size-14 line-height-22 padding-left-16 margin-bottom-20">
						<div className="color-86909c margin-right-4">{detail.mainStatus === 1 ? '视频授权ID' : '视频授权TOKEN'}：</div>
						<div className="">{detail.feedId || detail.feedToken || '--'}</div>
					</div>
				)}
				<div className="flex align-center justify-start font-size-14 line-height-22 padding-left-16 margin-bottom-20">
					<div className="color-86909c margin-right-4">发布时间：</div>
					<div className="">{(detail.releaseTime || '').slice(0, 16) || '--'}</div>
				</div>

				{detail.ttHashtagList && detail.ttHashtagList.length > 0 && (
					<div className="flex align-center justify-start font-size-14 line-height-22 padding-left-16 margin-bottom-20">
						<div className="color-86909c margin-right-4">管理话题：</div>
						<div className="flex-sub">
							<Space size={16}>
								{detail.ttHashtagList.map((ov, oi) => {
									return (
										<div key={oi} className="color-165dff">
											#{ov.title}
										</div>
									);
								})}
							</Space>
						</div>
					</div>
				)}

				<div className="flex align-center justify-start font-size-14 line-height-22 padding-left-16 margin-bottom-20">
					<div className="color-86909c margin-right-4">显示状态：</div>
					<div className="">
						<div className={`tag-status-${['error', 'primary'][detail.showStatus]}`}>{showStatusTextList[detail.showStatus] || '--'}</div>
					</div>
				</div>
				<div className="flex align-center justify-start font-size-14 line-height-22 padding-left-16 margin-bottom-20">
					<div className="color-86909c margin-right-4">发布类型：</div>
					<div className="">
						<div className={`tag-status-${['', 'primary', 'warning', '700eb2', 'success', 'error'][detail.releaseType]}`}>
							{releaseTypeTextList[detail.releaseType] || '--'}
						</div>
					</div>
				</div>
				<div className="flex align-center justify-start font-size-14 line-height-22 padding-left-16 margin-bottom-20">
					<div className="color-86909c margin-right-4">发布平台：</div>
					<div className="">
						<div className={`tag-status-${['', 'primary', 'success', '700eb2'][detail.releasePlatform]}`}>
							{releasePlatformTextList[detail.releasePlatform] || '--'}
						</div>
					</div>
				</div>
			</div>
			{/* 视频信息 结束 */}

			{/* 排序备注 开始 */}
			<div id="section2"></div>
			<div className="margin-top-20 padding-20 border-radius-8 bg-color-ffffff">
				<div className="flex align-center justify-between margin-bottom-20">
					<div className="font-size-18 line-height-26 font-weight-500">排序备注</div>
					<Button
						type="link"
						icon={<EditOutlined />}
						onClick={() => {
							linkTo(`${linkToPath}/curd?id=${id}#section2`);
						}}
					>
						编辑信息
					</Button>
				</div>

				<div className="flex align-center justify-start font-size-14 line-height-22 padding-left-16 margin-bottom-20">
					<div className="color-86909c margin-right-4">备注：</div>
					<div className="">{detail.remarks || '--'}</div>
				</div>
				<div className="flex align-center justify-start font-size-14 line-height-22 padding-left-16 margin-bottom-20">
					<div className="color-86909c margin-right-4">排序：</div>
					<div className="">{detail.rankingNum || '--'}</div>
				</div>
			</div>
			{/* 排序备注 结束 */}

			{detail.releaseType === 2 && (
				<>
					<div id="section3"></div>
					<div className="bg-color-ffffff border-radius-8 padding-lr-24 padding-tb-20 margin-tb-16">
						<div className="flex align-center justify-between margin-bottom-20">
							<div className="font-size-18 line-height-26 font-weight-500">关联账号</div>
							<Button
								type="link"
								icon={<EditOutlined />}
								onClick={() => {
									linkTo(`${linkToPath}/curd?id=${id}${detail.originalUrl ? '&type=originUrl' : ''}#section3`);
								}}
							>
								编辑信息
							</Button>
						</div>
						<div className="flex align-center justify-start font-size-14 line-height-22 padding-left-16 margin-bottom-20">
							<div className="color-86909c margin-right-24">关联科转号</div>
							<div className="flex align-start justify-start flex-wrap">
								{(detail.ttChannelsList || []).map((item) => {
									return (
										<div key={item.id} className="flex align-center justify-start margin-right-20">
											<img src={item.avatarUrl} className="width-20 height-20 border-radius-10 bg-color-e5e6eb" />
											<div className="font-size-14 color-165dff margin-left-6 line-height-20">{item.accountName || ''}</div>
										</div>
									);
								})}
								{(detail.ttChannelsList || []).length === 0 && '--'}
							</div>
						</div>
					</div>
				</>
			)}

			{/* 询问弹窗 开始 */}
			<ModalForm ref={ModalFormRef} onOk={handleAudit} FormComp={(props) => <ConrirmForm ref={props.FormCompRef} />} />
			{/* 询问弹窗 结束 */}
		</div>
	);
};

// 询问表单
const ConrirmForm = forwardRef((props = {}, ref) => {
	const [form] = Form.useForm();

	useImperativeHandle(ref, () => {
		return {
			onCancel: () => Promise.resolve(),
			onOk: () => {
				return new Promise((resolve) => {
					form.validateFields().then((values) => {
						resolve(values);
					});
				});
			},
		};
	});

	return (
		<Form form={form} labelCol={{ style: { width: '100px' } }} initialValues={{ status: 2 }}>
			<Form.Item hidden name="status">
				<Input />
			</Form.Item>
			<Form.Item label="原因" name="reason" rules={[{ required: true, message: '请输入不通过原因' }]}>
				<Input.TextArea rows={4} placeholder="请输入不通过原因" />
			</Form.Item>
		</Form>
	);
});

export default Index;
