import { useEffect, useState } from 'react';
import { Table, Button, Popconfirm, Space, Switch, Affix, message, Form, Select, Input, Row, Col, Modal } from 'antd';
import { useTableData } from '@/hook/useTableData';
import { useRouterLink } from '@/hook/useRouter';
import {
	pageWechatChannels as getTablePageData,
	delWechatChannels as delTableItemData,
	updateRankingNum as batchUpdateSort,
	updateShowStatus,
} from '@/api/Achv/InformationManage/VideoRelease/index';

import { auditStatusData, auditStatusTextList, showStatusData, videoTypeData, videoTypeTextList } from '@/pages/Achv/config';
import { pageAccount } from '@/api/Achv/TTChannels/VideoAccount/index';

const Index = (props = {}) => {
	const { linkTo, searchParams } = useRouterLink();
	const linkToPath = props.linkToPath || '/newAchv/informationManage/videoRelease';

	const status = searchParams.get('auditStatus');
	const [auditStatus, setAuditStatus] = useState(status === null ? 3 : status - 0 || '');

	const { form, dataSource, pagination, changePage, delTableData, getTableData, onReset, onSearch, SortInput } = useTableData({
		params: { auditStatus, releaseType: props.releaseType || undefined },
		getTablePageData,
		delTableItemData,
		batchUpdateSort,
		pageCallback: (paramsData = {}) => {
			getStatisticsData({ ...paramsData });
		},
	});

	// 新建弹窗显示
	const [open, setOpen] = useState(false);

	// 获取统计数据
	const [statistics, setStatistics] = useState({});
	const getStatisticsData = (paramsData) => {
		Promise.all([
			getTablePageData({
				...paramsData,
				pageSize: 1,
				pageNum: 1,
				auditStatus: 3,
			}),
			getTablePageData({
				...paramsData,
				pageSize: 1,
				pageNum: 1,
				auditStatus: 1,
			}),
			getTablePageData({
				...paramsData,
				pageSize: 1,
				pageNum: 1,
				auditStatus: 2,
			}),
		]).then((resList) => {
			setStatistics({
				total: resList.reduce((sum, cur) => sum + (cur.data.total - 0), 0),
				releaseNum: resList[0].data.total,
				waitReleaseNum: resList[1].data.total,
				notPassNum: resList[2].data.total,
			});
		});
	};

	// 视频号选项
	const [videoAccountOptions, setVideoAccountOptions] = useState([]);

	// 获取选项数据
	const getOptionsData = () => {
		pageAccount({
			pageSize: 100,
			pageNum: 1,
		}).then((res) => {
			setVideoAccountOptions(
				(res.data.records || []).map((ov) => {
					return {
						label: ov.wechatChannelsAccountName,
						value: ov.id,
						mainStatus: ov.mainStatus,
					};
				})
			);
		});
	};

	// 修改状态展示
	const showStatusChange = (record) => {
		updateShowStatus({ id: record.id, showStatus: record.showStatus == 1 ? 0 : 1 }).then(() => {
			message.success('操作成功');
			getTableData();
		});
	};

	useEffect(() => {
		getOptionsData();
	}, []);

	return (
		<div className="">
			<div className="flex flex-shrink align-center margin-bottom-4 height-48 font-size-22 font-weight-500 color-1d2129">
				{props.releaseType === 2 ? '视频管理' : '视频发布'}
			</div>

			{/* Tabs & 功能按钮 开始 */}
			<Affix offsetTop={0}>
				<div className="flex align-center justify-between padding-24 border-radius-8 bg-color-ffffff border-solid-bottom-f2f3f5">
					<div className="flex align-center">
						{auditStatusData.map((ov) => (
							<div
								key={ov.value}
								className={`a margin-right-40 font-size-18 font-weight-500 ${auditStatus === ov.value ? 'color-165dff' : ''}`}
								onClick={() => setAuditStatus(ov.value)}
							>
								{ov.label}
								{statistics[ov.countName] > 0 ? `（${statistics[ov.countName]}）` : ''}
							</div>
						))}
					</div>
					<Space size={16}>
						<Button
							type="primary"
							onClick={() => {
								linkTo(`${linkToPath}/curd`);
								return;
								if (props.releaseType === undefined) {
									linkTo(`${linkToPath}/curd?videoType=1`);
								} else {
									setOpen(true);
								}
							}}
						>
							新增视频
						</Button>
					</Space>
				</div>
			</Affix>
			{/* Tabs & 功能按钮 结束 */}

			<div className="flex-sub margin-top-18 padding-24 border-radius-8 bg-color-ffffff">
				{/* 筛选条件 开始 */}
				<div className="flex justify-between margin-bottom-18">
					<div className="flex-sub">
						<Form form={form} labelAlign="right" layout="inline">
							<Row className="width-100per" gutter={[12, 12]}>
								<Col span={8}>
									<Form.Item label="视频号" name="ttWechatChannelsAccountId">
										<Select placeholder="请选择视频号" options={videoAccountOptions} allowClear />
									</Form.Item>
								</Col>
								<Col span={8}>
									<Form.Item name="title" label="视频标题">
										<Input placeholder="请输入视频标题" />
									</Form.Item>
								</Col>
								<Col span={8}>
									<Form.Item name="videoType" label="视频类型">
										<Select options={videoTypeData} placeholder="请选择视频类型" allowClear />
									</Form.Item>
								</Col>
								<Col span={8}>
									<Form.Item name="showStatus" label="显示状态">
										<Select options={showStatusData} placeholder="请选择显示状态" allowClear />
									</Form.Item>
								</Col>
							</Row>
						</Form>
					</div>
					<div className="margin-left-12 padding-left-12 border-solid-left-e5e6eb">
						<Space size={16}>
							<Button onClick={onReset}>重置</Button>
							<Button type="primary" onClick={onSearch}>
								查询
							</Button>
						</Space>
					</div>
				</div>
				{/* 筛选条件 结束 */}

				{/* 表格 开始 */}
				<Table rowKey="id" dataSource={dataSource} pagination={pagination} onChange={changePage} scroll={{ x: 'max-content' }}>
					<Table.Column
						title="序号"
						key="index"
						width={60}
						align="center"
						render={(_, __, index) => {
							return index + 1;
						}}
					/>
					<Table.Column
						title="发布状态"
						dataIndex="auditStatus"
						align="center"
						render={(text) => {
							return (
								<div className={`tag-status-small-${['default', 'warning', 'error', 'primary'][text]}  `}>
									{auditStatusTextList[text || ''] || '--'}
								</div>
							);
						}}
					/>
					<Table.Column title="视频标题" dataIndex="title" render={(text) => <div className="max-width-400">{text}</div>} />
					<Table.Column title="视频号" dataIndex="channelsName" render={(text) => text || '--'} />
					<Table.Column title="视频时长" dataIndex="duration" />
					<Table.Column
						title="视频类型"
						dataIndex="videoType"
						render={(text) => {
							return (
								<div className={`tag-status-small-${['', 'primary', 'success'][text]}  `}>
									{videoTypeTextList[text || ''] || '--'}
								</div>
							);
						}}
					/>
					<Table.Column
						title="显示/隐藏"
						dataIndex="showStatus"
						alcign="center"
						render={(_, record) => {
							return (
								<Switch
									checked={record.showStatus == 1}
									disabled={record.auditStatus !== 3}
									title={record.auditStatus !== 3 ? '仅审核通过后才能操作' : ''}
									onChange={() => {
										showStatusChange(record);
									}}
								/>
							);
						}}
					/>
					{!props.hideSort && (
						<Table.Column
							title="排序"
							align="center"
							dataIndex="rankingNum"
							render={(_, record) => {
								return <SortInput record={record} />;
							}}
						/>
					)}
					<Table.Column
						title="操作"
						key="option"
						dataIndex="option"
						align="center"
						fixed="right"
						width="160px"
						render={(_, record) => {
							return (
								<>
									<Button type="link" size="small" onClick={() => linkTo(`${linkToPath}/detail?id=${record.id}&fromList=1`)}>
										详情/编辑
									</Button>
									<Popconfirm
										title="提示"
										description="确定删除吗？"
										onConfirm={() => {
											delTableData(record.id);
										}}
										okText="确定"
										cancelText="取消"
									>
										<Button type="link" size="small">
											删除
										</Button>
									</Popconfirm>
								</>
							);
						}}
					/>
				</Table>
				{/* 表格 结束 */}
			</div>

			{/* 新建类型选择 弹窗 开始 */}
			<Modal
				title="请选择视频类型"
				open={open}
				centered
				onCancel={() => {
					setOpen(false);
				}}
				footer={null}
				width={360}
			>
				<div className="flex justify-center">
					<Space direction="vertical" size={16} className="margin-top-16 width-260">
						{videoTypeData.map((ov) => {
							return (
								<Button
									disabled={ov.value === 2 && props.releaseType !== 2}
									className="width-100per"
									size="large"
									key={ov.value}
									type="primary"
									onClick={() => {
										linkTo(`${linkToPath}/curd?videoType=${ov.value}`);
									}}
								>
									{ov.label}
								</Button>
							);
						})}
					</Space>
				</div>
			</Modal>
			{/* 新建类型选择 弹窗 结束 */}
		</div>
	);
};

export default Index;
