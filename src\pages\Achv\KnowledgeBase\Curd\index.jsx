/**
 * @description Curd - 文件描述
 * <AUTHOR>
 *
 * Created on 2024/10/28 16:36
 */
import { useEffect, useState, forwardRef, useImperativeHandle, memo, useRef } from 'react';
import { Space, Form, Card, Input, DatePicker, Cascader, message, Radio, Button, Affix, Anchor, Image, Avatar, Tag, List } from 'antd';
import UploadImg from '@/components/UploadImg';
import { useRouterLink } from '@/hook/useRouter';
import UEditor from '@/components/UEditor';

import { detailPredictEnterprise, addPredictEnterprise, updatePredictEnterprise, auditPredictEnterprise } from '@/api/Achv/KnowledgeBase';
import dayjs from 'dayjs';
import { EditOutlined } from '@ant-design/icons';
import ModalForm from '@/components/ModalForm';

const { RangePicker } = DatePicker;
/* 审核选项 */
const auditOptions = [
	{
		label: '审核中',
		value: 0,
	},
	{
		label: '审核不通过',
		value: 1,
	},
	{
		label: '审核通过',
		value: 2,
	},
];

const feedbackList = [
	{
		name: '公司简介',
		value: 1,
	},
	{
		name: '需求预判',
		value: 2,
	},
	{
		name: '技术路线',
		value: 3,
	},
	{
		name: '主营业务',
		value: 4,
	},
	{
		name: '核心产品',
		value: 5,
	},
	{
		name: '答非所问',
		value: 6,
	},
];
const StepOne = ({ areaList, visible, labelClass }) => {
	return (
		<div>
			<Form.Item
				label={<div className={labelClass(99)}>公司名称</div>}
				name={'enterpriseName'}
				hidden={!visible}
				required
				rules={[{ required: true, message: '请输入公司名称' }]}
			>
				<Input placeholder={'请输入'} />
			</Form.Item>
			<Form.Item
				label={<div className={labelClass(1)}>公司简介</div>}
				name={'introduction'}
				hidden={!visible}
				required
				rules={[{ required: true, message: '请输入公司简介' }]}
			>
				<Input.TextArea rows={4} placeholder={'请输入'} />
			</Form.Item>
			<Form.Item label={<div className={labelClass(4)}>主营业务</div>} name={'mainBusiness'} hidden={!visible}>
				<Input.TextArea rows={4} placeholder={'请输入'} />
			</Form.Item>
			<Form.Item label={<div className={labelClass(5)}>核心产品</div>} name={'coreProduct'} hidden={!visible}>
				<Input.TextArea rows={4} placeholder={'请输入'} />
			</Form.Item>
			<Form.Item label={<div className={labelClass(99)}>产业链和产业链节点</div>} name={'industryChainNode'} hidden={!visible}>
				<Input.TextArea rows={4} placeholder={'请输入'} />
			</Form.Item>
			<Form.Item label={<div className={labelClass(3)}>技术路线</div>} name={'technologicalRoute'} hidden={!visible}>
				<Input.TextArea rows={4} placeholder={'请输入'} />
			</Form.Item>
			<Form.Item label={<div className={labelClass(2)}>预判技术需求</div>} name={'predictRequirement'} hidden={!visible}>
				<Input.TextArea rows={4} placeholder={'请输入'} />
			</Form.Item>

			<Form.Item label={'来源'} name={'source'} hidden>
				<Input placeholder={'请输入'} />
			</Form.Item>
		</div>
	);
};

const Index = () => {
	const { linkTo, searchParams, setSearchParams } = useRouterLink();

	const [detail, setDetail] = useState({});
	const [step, setStep] = useState(1);
	const [form] = Form.useForm();
	const [areaList, setAreaList] = useState([]);
	/* 编辑状态 */
	const [editing, setEditing] = useState(false);

	useEffect(() => {
		// 获取区域接口
		const searchId = searchParams.get('id');
		if (searchId) {
			getDetail(searchId);
		} else {
			setEditing(true);
		}
	}, []);

	/* 查询拼团详情 */
	const getDetail = async (id) => {
		const res = await detailPredictEnterprise({ id });
		if (res.data) {
			setDetail(res.data);
			form.setFieldsValue(res.data);
			setEditing(false);
		}
	};

	/* 保存提交数据 */
	const handleSubmit = async () => {
		try {
			const { ...params } = await form.validateFields();
			const id = detail.id;
			if (!params.source) {
				params.source = 2;
			}
			const feedbackProcessFlag0List = (detail.feedbacks || []).filter((ov) => ov.processFlag != 1);
			if (feedbackProcessFlag0List.length) {
				params.latestFeedbackId = feedbackProcessFlag0List[0].id || '';
			}
			let res;
			if (id) {
				res = await updatePredictEnterprise({ id, ...params });
			} else {
				res = await addPredictEnterprise(params);
			}
			if (res.data) {
				message.success('保存成功');
				setSearchParams({ id: detail.id || res.data }, { replace: true });
				getDetail(detail.id || res.data);
				setEditing(false);
			}
		} catch (e) {
			console.error(e);
			if (e.errorFields?.length) {
				message.error(e.errorFields[0].errors[0]);
				return;
			}
			message.error('保存失败');
		}
	};

	/* 取消 */
	const handleCancel = () => {
		setEditing(false);
	};
	const handleBack = () => {
		window.history.back();
	};
	const setEditModal = () => {
		setEditing(true);
		// form.setFieldsValue(detail);
	};

	/* 通过审核 */
	const handleAudit = async (auditStatus, reason = '') => {
		const params = { id: detail.id, auditStatus, reason };

		const feedbackProcessFlag0List = (detail.feedbacks || []).filter((ov) => ov.processFlag != 1);
		if (feedbackProcessFlag0List.length) {
			params.latestFeedbackId = feedbackProcessFlag0List[0].id || '';
		}
		const res = await auditPredictEnterprise(params);
		if (res.data) {
			let str = '审核通过';
			if (auditStatus === 0) {
				str = '撤销审核';
			}
			if (auditStatus === 1) {
				str = '审核通过';
			}
			if (auditStatus === 2) {
				str = '审核不通过';
			}
			message.success(str);
			// handleBack();
			getDetail(detail.id);
		}
	};

	const ModalFormRef = useRef();

	const labelClass = (val) => {
		return !!(detail.feedbacks || []).filter((ov) => ov.processFlag != 1).find((ov) => (ov.opposeItem || []).includes(val))
			? 'color-f53f3f'
			: 'color-86909c';
	};

	return (
		<div>
			<div className="flex flex-shrink align-center margin-bottom-4 height-48 font-size-22 font-weight-500 color-1d2129">
				<Space size={10}>
					<div className="a color-86909c" onClick={() => linkTo('/newAchv/knowledgeBase/list')}>
						知识库管理
					</div>
					<div className="color-86909c">/</div>
					<div>{detail?.id ? '修改' : '创建'}</div>
				</Space>
			</div>
			{/* Tabs & 功能按钮 开始 */}
			<Affix>
				<div className="height-66 flex align-center justify-between bg-color-ffffff border-radius-8 padding-lr-24 border-solid-bottom-f2f3f5">
					<Anchor
						affix={false}
						rootClassName="custom-anchor-box"
						direction="horizontal"
						replace
						targetOffset={80}
						items={[
							{
								key: 'section1',
								href: '',
								title: <div className="margin-right-40 font-size-16 font-weight-500">基本信息</div>,
							},
						]}
					/>
					{detail.id && (
						<div className="flex align-center justify-between color-1d2129 line-height-26 font-size-18 font-weight-500">
							<Space size={16}>
								{editing ? (
									<>
										<Button onClick={handleCancel}>取消</Button>
										<Button type="primary" onClick={handleSubmit}>
											保存
										</Button>
									</>
								) : (
									<>
										{detail.auditStatus === 0 && (
											<div>
												<Button
													onClick={() => {
														ModalFormRef.current.setOpen(true);
														ModalFormRef.current.setTitle('不通过原因');
													}}
													danger
												>
													审核不通过
												</Button>
												<Button className="margin-left-16" type="primary" onClick={() => handleAudit(1)}>
													审核通过
												</Button>
											</div>
										)}
										{detail.auditStatus === 1 && (
											<div>
												<Button className="margin-left-16" onClick={() => handleAudit(0)} danger>
													撤销审核
												</Button>
											</div>
										)}
										{detail.auditStatus === 2 && (
											<Button onClick={() => handleAudit(0)} danger>
												撤销审核
											</Button>
										)}
									</>
								)}
							</Space>
						</div>
					)}
					{!detail.id && (
						<Space size={16}>
							<Button onClick={handleBack}>取消</Button>
							<Button type="primary" onClick={handleSubmit}>
								保存
							</Button>
						</Space>
					)}
				</div>
			</Affix>
			{/* Tabs & 功能按钮 结束 */}

			{!editing && (
				<>
					<div id="section1" />

					<Card
						title={<div className="font-size-18 line-height-26 font-weight-500">基本信息</div>}
						className={'margin-top-20'}
						extra={
							<Button type="link" icon={<EditOutlined />} onClick={setEditModal}>
								编辑信息
							</Button>
						}
					>
						<div className="">
							<div className="flex align-center justify-start font-size-14 line-height-22 padding-left-16 margin-bottom-20">
								<div className={`${labelClass(99)} margin-right-4 flex-shrink`}>公司名称：</div>
								<div className="">{detail.enterpriseName || '--'}</div>
							</div>
							<div className="flex align-center justify-start font-size-14 line-height-22 padding-left-16 margin-bottom-20">
								<div className={`${labelClass(1)} margin-right-4 flex-shrink`}>公司简介：</div>
								<div className="">{detail.introduction || '--'}</div>
							</div>
							<div className="flex align-center justify-start font-size-14 line-height-22 padding-left-16 margin-bottom-20">
								<div className={`${labelClass(4)} margin-right-4 flex-shrink`}>主营业务：</div>
								<div className="">{detail.mainBusiness || '--'}</div>
							</div>
							<div className="flex align-center justify-start font-size-14 line-height-22 padding-left-16 margin-bottom-20">
								<div className={`${labelClass(5)} margin-right-4 flex-shrink`}>核心产品：</div>
								<div className="">{detail.coreProduct || '--'}</div>
							</div>
							<div className="flex align-center justify-start font-size-14 line-height-22 padding-left-16 margin-bottom-20">
								<div className={`${labelClass(99)} margin-right-4 flex-shrink`}>产业链和产业链节点：</div>
								<div className="">{detail.industryChainNode || '--'}</div>
							</div>
							<div className="flex align-center justify-start font-size-14 line-height-22 padding-left-16 margin-bottom-20">
								<div className={`${labelClass(3)} margin-right-4 flex-shrink`}>技术路线：</div>
								<div className="">{detail.technologicalRoute || '--'}</div>
							</div>
							<div className="flex align-center justify-start font-size-14 line-height-22 padding-left-16 margin-bottom-20">
								<div className={`${labelClass(2)} margin-right-4 flex-shrink`}>预判技术需求：</div>
								<div className="">{detail.predictRequirement || '--'}</div>
							</div>
						</div>
					</Card>
				</>
			)}

			<Form form={form} initialValues={{}} labelCol={{ span: 4 }} wrapperCol={{ span: 16 }} hidden={!editing}>
				<div id="section1" />
				<Card title={<div className="font-size-18 line-height-26 font-weight-500">基本信息</div>} className={'margin-top-20'}>
					<StepOne areaList={areaList} visible labelClass={labelClass} />
				</Card>
			</Form>

			<Card title={<div className="font-size-18 line-height-26 font-weight-500">反馈日志</div>} className={'margin-top-20'}>
				<List
					bordered
					dataSource={detail.feedbacks || []}
					renderItem={(item) => (
						<List.Item>
							<div className={item.processFlag == 1 ? 'color-4e5969' : ''}>
								<div>
									{item.createUsername}：{item.createTime}{' '}
									{feedbackList
										.filter((ov) => {
											return (item.opposeItem || []).includes(ov.value);
										})
										.map((ov) => ov.name)
										.join('、')}
								</div>
								{item.content && <div>内容：{item.content || '--'}</div>}
							</div>
						</List.Item>
					)}
				/>
			</Card>

			{/* 询问弹窗 开始 */}
			<ModalForm
				ref={ModalFormRef}
				onOk={(e) => {
					handleAudit(2, e.reason);
				}}
				FormComp={(props) => <ConrirmForm ref={props.FormCompRef} />}
			/>
			{/* 询问弹窗 结束 */}
		</div>
	);
};

export default Index;

// 询问表单
const ConrirmForm = forwardRef((props = {}, ref) => {
	const [form] = Form.useForm();

	useImperativeHandle(ref, () => {
		return {
			onCancel: () => Promise.resolve(),
			onOk: () => {
				return new Promise((resolve) => {
					form.validateFields().then((values) => {
						resolve(values);
					});
				});
			},
		};
	});

	return (
		<Form form={form} labelCol={{ style: { width: '100px' } }} initialValues={{}}>
			<Form.Item label="原因" name="reason" rules={[{ required: true, message: '请输入不通过原因' }]}>
				<Input.TextArea rows={4} placeholder="请输入不通过原因" />
			</Form.Item>
		</Form>
	);
});
