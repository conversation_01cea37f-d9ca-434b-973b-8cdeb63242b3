/**
 * @description index - 文件描述
 * <AUTHOR>
 *
 * Created on 2024/10/28 16:34
 */
import { useEffect, useState } from 'react';
import {
	Button,
	Tooltip,
	Tag,
	Table,
	Space,
	Modal,
	message,
	Form,
	Switch,
	Affix,
	Image,
	Popover,
	Select,
	Popconfirm,
	Input,
	Col,
	Row,
	DatePicker,
} from 'antd';
import { DownloadOutlined, QuestionCircleOutlined } from '@ant-design/icons';

import { useRouterLink } from '@/hook/useRouter';

import { useTableData } from '@/hook/useTableData';
import {
	pagePredictEnterprise as getTablePageData,
	statisticPredictEnterprise,
	deletePredictEnterprise as delTableItemData,
} from '@/api/Achv/KnowledgeBase';

import FormComp from '@/components/FormComp';
import './index.scss';

const tabsList = [
	{ label: '已发布', type: 1, count: 0, key: 'releaseTotal', value: 1 },
	{ label: '大模型反馈', type: 2, count: 0, key: 'llmTotal', value: 2 },
	{ label: '知识库反馈', type: 3, count: 0, key: 'feedbackTotal', value: 3 },
];

const auditStatusList = [
	{ label: '全部', value: '' },
	{ label: '待审核', value: 0 },
	{ label: '审核通过', value: 1 },
	{ label: '审核不通过', value: 2 },
];
const sourceList = [
	{ label: '全部', value: '' },
	{ label: '导入', value: 0 },
	{ label: '大模型', value: 1 },
	{ label: '手动添加', value: 2 },
];
const Index = () => {
	const { linkTo, searchParams } = useRouterLink();
	const id = searchParams.get('id') || undefined;
	const defaultType = searchParams.get('type') || undefined;

	const [type, setType] = useState(defaultType - 0 || 1);

	const [tabsListData, setTabsListData] = useState(tabsList);

	const { form, dataSource, pagination, changePage, onSearch, getTableData, onReset, delTableData } = useTableData({
		params: {
			type,
		},
		getTablePageData,
		delTableItemData,
	});

	// 表格数据
	const columns = [
		{
			title: '企业名称',
			dataIndex: 'enterpriseName',
			key: 'enterpriseName',
			className: 'max-width-200',
		},
		{
			title: '创建时间',
			dataIndex: 'createTime',
			key: 'createTime',
			className: 'max-width-200',
			render: (createTime) => {
				return <div>{(createTime || '').slice(0, 16)}</div>;
			},
		},
		{
			title: '最近更新时间',
			dataIndex: 'updateTime',
			key: 'updateTime',
			className: 'max-width-200',
			render: (updateTime) => {
				return <div>{(updateTime || '').slice(0, 16)}</div>;
			},
		},
		{
			title: '历史反馈次数',
			dataIndex: 'feedbackTotal',
			key: 'feedbackTotal',
			className: 'max-width-200',
		},
		{
			title: '反馈错误次数',
			dataIndex: 'opposeCount',
			key: 'opposeCount',
			className: 'max-width-200',
		},
		{
			title: '反馈正确次数',
			dataIndex: 'endorseCount',
			key: 'endorseCount',
			className: 'max-width-200',
		},
		{
			title: '审核人',
			dataIndex: 'feedbackUsername',
			key: 'feedbackUsername',
			className: 'max-width-200',
			render: (feedbackUsername) => {
				return <div>{(feedbackUsername || '').slice(0, 16)}</div>;
			},
		},
		{
			title: '操作',
			dataIndex: 'operate',
			key: 'operate',
			fixed: 'right',
			align: 'center',
			render: (text, record) => {
				return (
					<Space size={0}>
						<Button size={'small'} type={'link'} onClick={() => handleCurd(record.id)}>
							编辑{type == 1 && '/审核'}
						</Button>

						<Popconfirm
							title="提示"
							description="确定删除吗？"
							onConfirm={() => {
								delTableData(record.id, { id: record.id });
							}}
							okText="确定"
							cancelText="取消"
						>
							<Button type="link" size="small" danger>
								删除
							</Button>
						</Popconfirm>
					</Space>
				);
			},
		},
	];

	useEffect(() => {
		getStatisticPredictEnterprise();
	}, []);

	/* 查询拼团统计数据 */
	const getStatisticPredictEnterprise = async () => {
		const res = await statisticPredictEnterprise({});
		if (res.data) {
			console.log(res.data);
			const resp = {
				releaseTotal: res.data.releaseTotal || 0,
				llmTotal: res.data.llmTotal || 0,
				feedbackTotal: res.data.feedbackTotal || 0,
			};
			setTabsListData(tabsList.map((item) => ({ ...item, count: resp[item.key] })));
		}
	};
	/* 切换tabs */
	const handleTabsChange = (type) => {
		console.log('🚀 ~ handleTabsChange ~ type:', type);
		setType(type);
	};

	// 新增/编辑
	const handleCurd = (id = '') => {
		linkTo(`/newAchv/knowledgeBase/curd${id ? `?id=${id}` : ''}`);
	};

	return (
		<div className="">
			<div className="flex flex-shrink align-center margin-bottom-4 height-48 font-size-22 font-weight-500 color-1d2129">知识库管理</div>
			{/* Tabs 开始 */}
			<Affix offsetTop={0}>
				<div className="flex align-center justify-between padding-24 border-radius-8 bg-color-ffffff border-solid-bottom-f2f3f5">
					<div className="flex align-center">
						{tabsListData.map((ov, oi) => (
							<div
								key={oi}
								className={`a margin-right-40 font-size-18 font-weight-500 ${type == ov.type ? 'color-165dff' : ''}`}
								onClick={() => {
									getStatisticPredictEnterprise();
									handleTabsChange(ov.type);
								}}
							>
								{ov.label}
								{ov.count - 0 > 0 ? `（${ov.count}）` : ''}
							</div>
						))}
					</div>
					<Space size={10}>
						<Button type={'primary'} onClick={() => {}}>
							导出
						</Button>
						<Button type={'primary'} onClick={() => {}}>
							上传文档
						</Button>
						<Button type={'primary'} onClick={() => handleCurd()}>
							新增数据
						</Button>
					</Space>
				</div>
			</Affix>
			{/* Tabs 结束 */}
			<div className="flex-sub margin-top-18 padding-24 border-radius-8 bg-color-ffffff">
				{/* 筛选条件 开始 */}
				<div className="flex justify-between margin-bottom-18">
					<div className="flex-sub">
						<Form
							form={form}
							labelAlign="right"
							layout="inline"
							initialValues={{
								startTime: undefined,
								endTime: undefined,
								tempDate: ['', ''],
							}}
						>
							<Row className="width-100per" gutter={[12, 12]}>
								<Col span={8}>
									<Form.Item name="enterpriseName">
										<Input placeholder="请输入公司名称" />
									</Form.Item>
								</Col>

								<Col span={8}>
									<Form.Item name={'source'}>
										<Select placeholder={'请选择数据来源'} options={sourceList} allowClear />
									</Form.Item>
								</Col>
								<Col span={8}>
									<Form.Item name="auditPerson">
										<Input placeholder="请输入审核人名称" />
									</Form.Item>
								</Col>
								<Col span={8}>
									<Form.Item hidden name="createTimeStart">
										<Input placeholder="" />
									</Form.Item>
									<Form.Item hidden name="createTimeEnd">
										<Input placeholder="" />
									</Form.Item>
									<Form.Item name="tempDate">
										<FormComp.DatePicker.RangePicker
											placeholder={['开始时间', '结束时间']}
											onChange={(date) => {
												form.setFieldsValue({
													createTimeStart: date[0] ? `${date[0]} 00:00:00` : null,
													createTimeEnd: date[1] ? `${date[1]} 23:56:56` : null,
												});
											}}
										/>
									</Form.Item>
								</Col>
							</Row>
						</Form>
					</div>
					<div className="margin-left-12 padding-left-12 border-solid-left-e5e6eb">
						<Space size={16}>
							<Button onClick={onReset}>重置</Button>
							<Button type="primary" onClick={onSearch}>
								查询
							</Button>
						</Space>
					</div>
				</div>
				{/* 筛选条件 结束 */}
				{/* 表格 开始 */}
				<Table
					rowKey="id"
					dataSource={dataSource}
					pagination={pagination}
					onChange={changePage}
					scroll={{ x: 'max-content' }}
					columns={columns}
				/>
				{/* 表格 结束 */}
			</div>
			{/*<DetailModal ref={detailRef} />*/}
		</div>
	);
};

export default Index;
