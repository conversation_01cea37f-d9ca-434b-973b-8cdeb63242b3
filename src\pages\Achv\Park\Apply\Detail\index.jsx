import { useEffect, useState, useRef, forwardRef, useImperativeHandle } from 'react';
import { useRouterLink } from '@/hook/useRouter';

import { Button, Affix, Anchor, Form, Space, Radio, message } from 'antd';
import ModalForm from '@/components/ModalForm';

import { updateFollowStatus, getParkApplyDetail } from '@/api/Achv/Park/Apply/index';
import { parkFollowStatusData, parkFollowStatusTextList } from '@/pages/Achv/config';

const Index = () => {
	const ModalFormRef = useRef();
	const { linkTo, searchParams } = useRouterLink();
	const id = searchParams.get('id') || '';
	const fromList = !!searchParams.get('fromList');

	const [detail, setDetail] = useState({});

	// 跳转地址
	const linkToPath = '/newAchv/park/apply';

	// 获取详情
	const getDetail = () => {
		if (id) {
			getParkApplyDetail({ id }).then((res) => {
				const resData = res.data || {};

				setDetail(resData);
			});
		}
	};

	// 更改状态
	const changeStatus = (e) => {
		updateFollowStatus({
			id,
			...e,
		}).then(() => {
			message.success('操作成功');
			getDetail();
		});
	};

	useEffect(() => {
		getDetail();
	}, []);

	return (
		<div className="">
			<div className="flex flex-shrink align-center margin-bottom-4 height-48 font-size-22 font-weight-500 color-1d2129">
				<Space size={10}>
					<div className="a color-86909c" onClick={() => linkTo(fromList ? -1 : linkToPath)}>
						园区入驻管理
					</div>
					<div className="color-86909c">/</div>
					<div>申请信息</div>
				</Space>
			</div>
			<div id="section1"></div>
			{/* Tabs & 功能按钮 开始 */}
			<Affix>
				<div className="height-66 flex align-center justify-between bg-color-ffffff border-radius-8 padding-lr-24 border-solid-bottom-f2f3f5">
					<div className="flex align-center justify-between color-1d2129 line-height-26 font-size-18 font-weight-500">
						<Anchor
							affix={false}
							rootClassName="custom-anchor-box"
							direction="horizontal"
							replace
							targetOffset={80}
							items={[
								{
									key: 'section1',
									href: '#section1',
									title: <div className="margin-right-40 font-size-16 font-weight-500">基本信息</div>,
								},
								{
									key: 'section2',
									href: '#section2',
									title: <div className="margin-right-40 font-size-16 font-weight-500">线索来源</div>,
								},
							]}
						/>
					</div>
					<div className="flex align-center justify-between color-1d2129 line-height-26 font-size-18 font-weight-500">
						<Button
							type="primary"
							className="height-40 width-104"
							onClick={() => {
								ModalFormRef.current.setOpen(true);
								ModalFormRef.current.setTitle('更新状态');
							}}
						>
							更新状态
						</Button>
					</div>
				</div>
			</Affix>
			{/* Tabs & 功能按钮 结束 */}

			{/* 基本信息 开始 */}
			<div className="margin-top-20 padding-20 border-radius-8 bg-color-ffffff">
				<div className="flex align-center justify-between margin-bottom-20">
					<div className="font-size-18 line-height-26 font-weight-500">基本信息</div>
				</div>
				<div className="flex align-center justify-start font-size-14 line-height-22 padding-left-16 margin-bottom-20">
					<div className="color-86909c margin-right-4">跟进状态：</div>
					<div className="">
						<div className={`tag-status-${['', 'primary', 'warning', '700eb2', 'success'][detail.followStatus || 0]}`}>
							{parkFollowStatusTextList[detail.followStatus || 0] || '--'}
						</div>
					</div>
				</div>
				<div className="flex align-center justify-start font-size-14 line-height-22 padding-left-16 margin-bottom-20">
					<div className="color-86909c margin-right-4">申请人：</div>
					<div className="">{detail.applyName || '--'}</div>
				</div>
				<div className="flex align-center justify-start font-size-14 line-height-22 padding-left-16 margin-bottom-20">
					<div className="color-86909c margin-right-4">联系方式：</div>
					<div className="">{detail.phone || '--'}</div>
				</div>
				<div className="flex align-center justify-start font-size-14 line-height-22 padding-left-16 margin-bottom-20">
					<div className="color-86909c margin-right-4">关联园区：</div>
					<div className="">{detail.parkName || '--'}</div>
				</div>
				<div className="flex align-start justify-start font-size-14 line-height-22 padding-left-16 margin-bottom-20">
					<div className="color-86909c margin-right-4">入驻需求：</div>
					<div className="flex-sub">{detail.reason || '无'}</div>
				</div>
				<div className="flex align-start justify-start font-size-14 line-height-22 padding-left-16 margin-bottom-20">
					<div className="color-86909c margin-right-4">提交时间：</div>
					<div className="flex-sub">{(detail.createTime || '').slice(0, 16) || '--'}</div>
				</div>
			</div>
			{/* 基本信息 结束 */}

			{/* 线索来源 开始 */}
			<div id="section2"></div>
			<div className="margin-top-20 padding-20 border-radius-8 bg-color-ffffff">
				<div className="flex align-center justify-between margin-bottom-20">
					<div className="font-size-18 line-height-26 font-weight-500">线索来源</div>
				</div>
				<div className="flex align-center justify-start font-size-14 line-height-22 padding-left-16 margin-bottom-20">
					<div className="color-86909c margin-right-24">线索来源人</div>
					<div className="flex align-start justify-start flex-wrap">
						{detail.inviterUserName ? (
							<div className="flex align-center justify-start margin-right-20">
								<div className="font-size-14 color-165dff margin-left-6 line-height-20">{detail.inviterUserName || ''}</div>
							</div>
						) : (
							'--'
						)}
					</div>
				</div>
			</div>
			{/* 线索来源 结束 */}

			{/* 更改状态弹窗 开始 */}
			<ModalForm
				ref={ModalFormRef}
				onOk={changeStatus}
				FormComp={(props) => <ConrirmForm ref={props.FormCompRef} followStatus={detail.followStatus} />}
			/>
			{/* 更改状态弹窗 结束 */}
		</div>
	);
};

// 更改状态表单
const ConrirmForm = forwardRef((props = {}, ref) => {
	const [form] = Form.useForm();

	useImperativeHandle(ref, () => {
		return {
			onCancel: () => Promise.resolve(),
			onOk: () => {
				return new Promise((resolve) => {
					form.validateFields().then((values) => {
						resolve(values);
					});
				});
			},
		};
	});

	return (
		<Form form={form} labelCol={{ style: { width: '100px' } }} initialValues={{ followStatus: props.followStatus || 1 }}>
			<Form.Item label="跟进状态" name="followStatus" required>
				<Radio.Group options={parkFollowStatusData}></Radio.Group>
			</Form.Item>
		</Form>
	);
});

export default Index;
