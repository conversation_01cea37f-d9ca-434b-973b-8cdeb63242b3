import { Table, Button, Popconfirm, Space, Form, Input, Row, Col, Select } from 'antd';
import { useTableData } from '@/hook/useTableData';
import { useRouterLink } from '@/hook/useRouter';
import { pageParkApply as getTablePageData, delByIds as delTableItemData } from '@/api/Achv/Park/Apply/index';
import { parkFollowStatusData, parkFollowStatusTextList } from '@/pages/Achv/config';
const Index = () => {
	const linkToPath = '/newAchv/park/apply';

	const { linkTo } = useRouterLink();

	const { form, dataSource, pagination, changePage, delTableData, onReset, onSearch } = useTableData({
		params: {},
		getTablePageData,
		delTableItemData,
	});

	return (
		<div className="">
			<div className="flex flex-shrink align-center margin-bottom-4 height-48 font-size-22 font-weight-500 color-1d2129">园区入驻管理</div>
			<div className="flex-sub margin-top-18 padding-24 border-radius-8 bg-color-ffffff">
				{/* 筛选条件 开始 */}
				<div className="flex justify-between margin-bottom-18">
					<div className="flex-sub">
						<Form form={form} labelAlign="right" layout="inline">
							<Row className="width-100per" gutter={[12, 12]}>
								<Col span={8}>
									<Form.Item name="applyName" label="申请人">
										<Input placeholder="请输入申请人姓名" />
									</Form.Item>
								</Col>
								<Col span={8}>
									<Form.Item name="followStatus" label="跟进状态">
										<Select placeholder="请选择跟进状态" options={parkFollowStatusData} />
									</Form.Item>
								</Col>
							</Row>
						</Form>
					</div>
					<div className="margin-left-12 padding-left-12 border-solid-left-e5e6eb">
						<Space size={16}>
							<Button onClick={onReset}>重置</Button>
							<Button type="primary" onClick={onSearch}>
								查询
							</Button>
						</Space>
					</div>
				</div>
				{/* 筛选条件 结束 */}

				{/* 表格 开始 */}
				<Table rowKey="id" dataSource={dataSource} pagination={pagination} onChange={changePage} scroll={{ x: 'max-content' }}>
					<Table.Column
						title="序号"
						key="index"
						width={60}
						align="center"
						render={(_, __, index) => {
							return index + 1;
						}}
					/>
					<Table.Column title="申请人" dataIndex="applyName" />
					<Table.Column title="联系方式" dataIndex="phone" />
					<Table.Column
						title="跟进状态"
						dataIndex="followStatus"
						render={(text) => {
							return (
								<div className={`tag-status-small-${['', 'primary', 'warning', '700eb2', 'success'][text]}`}>
									{parkFollowStatusTextList[text] || '--'}
								</div>
							);
						}}
					/>
					<Table.Column title="关联园区" dataIndex="parkName" />
					<Table.Column title="线索来源" dataIndex="inviterUserName" />
					<Table.Column title="提交时间" dataIndex="createTime" render={(text) => (text || '').slice(0, 16)} />
					<Table.Column
						title="操作"
						key="option"
						dataIndex="option"
						align="center"
						fixed="right"
						width="160px"
						render={(_, record) => {
							return (
								<>
									<Button type="link" size="small" onClick={() => linkTo(`${linkToPath}/detail?id=${record.id}&fromList=1`)}>
										详情/编辑
									</Button>
									<Popconfirm
										title="提示"
										description="确定删除吗？"
										onConfirm={() => {
											delTableData(record.id);
										}}
										okText="确定"
										cancelText="取消"
									>
										<Button type="link" size="small">
											删除
										</Button>
									</Popconfirm>
								</>
							);
						}}
					/>
				</Table>
				{/* 表格 结束 */}
			</div>
		</div>
	);
};

export default Index;
