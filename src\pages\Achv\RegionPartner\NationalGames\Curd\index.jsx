import { useEffect, useState } from 'react';
import { useRouterLink } from '@/hook/useRouter';
import { Button, Space, Form, Input, message, Affix } from 'antd';

import {
	addNationalGamesInfoCollect,
	updateNationalGamesInfoCollect,
	queryNationalGamesInfoCollect,
} from '@/api/Achv/RegionPartner/NationalGames/index';

const Index = () => {
	const { linkTo, searchParams } = useRouterLink();
	const id = searchParams.get('id') || '';
	const [form] = Form.useForm();

	// 跳转地址
	const linkToPath = `/newAchv/topicManage/regionPartner/nationalGames`;

	// 禁止编辑发布类型
	const [hash, setHash] = useState('');

	// 取消
	const onCancel = () => {
		linkTo(-1);
	};

	// 提交
	const onSubmit = () => {
		form.validateFields()
			.then((values) => {
				const params = { ...values };

				delete params.tempArea;
				(values.id ? updateNationalGamesInfoCollect : addNationalGamesInfoCollect)(params).then(() => {
					message.success(values.id ? '修改成功' : '添加成功');
					setTimeout(() => {
						linkTo(-1);
					}, 500);
				});
			})
			.catch((error) => {
				console.log(error);
			});
	};

	// 获取详情
	const getDetail = () => {
		if (id) {
			queryNationalGamesInfoCollect({ id }).then((res) => {
				const resData = res.data || {};

				// 处理区域
				resData.tempArea = [resData.provinceCode, resData.cityCode];
				// 处理 关联科转号
				resData.ttChannelsIds = resData.ttChannelsList || [];

				form.setFieldsValue(resData);
			});
		}
	};

	useEffect(() => {
		const hash = window.location.hash.replace('#', '');
		setHash(hash || 'section1');

		getDetail();
	}, []);

	// 跳转到指定锚点
	useEffect(() => {
		if (hash) {
			setTimeout(() => {
				const element = document.getElementById(hash);
				if (element) {
					element.scrollIntoView({ behavior: 'smooth' });
				}
			}, 500);
		}
	}, [hash]);

	return (
		<div className="">
			<div className="flex flex-shrink align-center margin-bottom-4 height-48 font-size-22 font-weight-500 color-1d2129">
				<Space size={10}>
					<div className="a color-86909c" onClick={() => linkTo(linkToPath)}>
						登记管理
					</div>
					<div className="color-86909c">/</div>
					<div>{id ? '登记编辑' : '新增登记'}</div>
				</Space>
			</div>

			{/* Tabs & 功能按钮 开始 */}
			<div id="section1"></div>
			<Affix offsetTop={0}>
				<div className="flex align-center justify-between padding-24 border-radius-8 border-solid-bottom-f2f2f2 bg-color-ffffff">
					<div className="flex align-center">
						{['基本信息'].map((ov, oi) => {
							const currentHash = `section${oi + 1}`;
							return (
								<div
									key={oi}
									className={`a margin-right-40 font-size-18 font-weight-500 ${hash === currentHash ? 'color-165dff' : ''}`}
								>
									{ov}
								</div>
							);
						})}
					</div>
					<Space size={16}>
						<Button onClick={onCancel}>取消</Button>
						<Button type="primary" onClick={onSubmit}>
							保存
						</Button>
					</Space>
				</div>
			</Affix>
			{/* Tabs & 功能按钮 结束 */}

			<Form form={form} className="antd-form-box" labelAlign="right" labelCol={{ style: { width: '160px' } }} initialValues={{}}>
				<Form.Item hidden name="id">
					<Input />
				</Form.Item>
				{/* 基本信息 开始 */}
				<div className="margin-top-20 padding-20 border-radius-8 bg-color-ffffff">
					<div className="padding-bottom-30 line-height-26 font-size-18 font-weight-500 color-1d2129">基本信息</div>

					<Form.Item label="企业名称" name="company" rules={[{ required: true, message: '请输入企业名称' }]}>
						<Input className="input-box" placeholder="请输入企业名" />
					</Form.Item>
					<Form.Item label="行业领域" name="industrySector" rules={[{ required: true, message: '请输入行业领域' }]}>
						<Input className="input-box" placeholder="请输入行业领域" />
					</Form.Item>
					<Form.Item label="产品（技术）名称" name="productName" rules={[{ required: true, message: '请输入产品（技术）名称' }]}>
						<Input className="input-box" placeholder="请输入产品（技术）名称" />
					</Form.Item>
					<Form.Item label="产品（技术）简介" name="productDesc" rules={[{ required: true, message: '请输入产品（技术）简介' }]}>
						<Input.TextArea rows={4} className="input-box" placeholder="请输入产品（技术）简介（包括功能、应用场景、展示方案）" />
					</Form.Item>
					<Form.Item label="使用场景设想" name="usageScenario" rules={[{ required: true, message: '请输入使用场景设想' }]}>
						<Input.TextArea rows={4} className="input-box" placeholder="请输入使用场景设想（如何在全运会中呈现?观众如何互动?）" />
					</Form.Item>
					<Form.Item label="联系人" name="name" rules={[{ required: true, message: '请输入联系人' }]}>
						<Input className="input-box" placeholder="请输入联系人姓名" />
					</Form.Item>
					<Form.Item label="联系方式" name="phone" rules={[{ required: true, message: '请输入联系方式' }]}>
						<Input className="input-box" placeholder="请输入联系手机" />
					</Form.Item>
				</div>
				{/* 基本信息 结束 */}
			</Form>
		</div>
	);
};
export default Index;
