import { useEffect, useState } from 'react';
import { useRouterLink } from '@/hook/useRouter';

import { Button, Affix, Anchor, Image, Space } from 'antd';
import { EditOutlined } from '@ant-design/icons';

import { queryNationalGamesInfoCollect } from '@/api/Achv/RegionPartner/NationalGames/index';

const Index = () => {
	const { linkTo, searchParams } = useRouterLink();
	const id = searchParams.get('id') || '';
	const fromList = !!searchParams.get('fromList');

	const [detail, setDetail] = useState({});

	// 跳转地址
	const linkToPath = '/newAchv/topicManage/regionPartner/nationalGames';

	// 获取详情
	const getDetail = () => {
		if (id) {
			queryNationalGamesInfoCollect({ id }).then((res) => {
				const resData = res.data || {};

				setDetail(resData);
			});
		}
	};

	useEffect(() => {
		getDetail();
	}, []);

	return (
		<div className="">
			<div className="flex flex-shrink align-center margin-bottom-4 height-48 font-size-22 font-weight-500 color-1d2129">
				<Space size={10}>
					<div className="a color-86909c" onClick={() => linkTo(fromList ? -1 : linkToPath)}>
						登记管理
					</div>
					<div className="color-86909c">/</div>
					<div>登记详情</div>
				</Space>
			</div>
			<div id="section1"></div>
			{/* Tabs & 功能按钮 开始 */}
			<Affix>
				<div className="height-66 flex align-center justify-between bg-color-ffffff border-radius-8 padding-lr-24 border-solid-bottom-f2f3f5">
					<div className="flex align-center justify-between color-1d2129 line-height-26 font-size-18 font-weight-500">
						<Anchor
							affix={false}
							rootClassName="custom-anchor-box"
							direction="horizontal"
							replace
							targetOffset={80}
							items={[
								{
									key: 'section1',
									href: '#section1',
									title: <div className="margin-right-40 font-size-16 font-weight-500">基本信息</div>,
								},
							]}
						/>
					</div>
					<div className="flex align-center justify-between color-1d2129 line-height-26 font-size-18 font-weight-500"></div>
				</div>
			</Affix>
			{/* Tabs & 功能按钮 结束 */}

			{/* 基本信息 开始 */}
			<div className="margin-top-20 padding-20 border-radius-8 bg-color-ffffff">
				<div className="flex align-center justify-between margin-bottom-20">
					<div className="font-size-18 line-height-26 font-weight-500">基本信息</div>
					<Button
						type="link"
						icon={<EditOutlined />}
						onClick={() => {
							linkTo(`${linkToPath}/curd?id=${id}#section1`);
						}}
					>
						编辑信息
					</Button>
				</div>

				<div className="flex justify-start font-size-14 line-height-22 padding-left-16 margin-bottom-20">
					<div className="flex-shrink color-86909c margin-right-4">企业名称：</div>
					<div className="flex-sub">{detail.company || '--'}</div>
				</div>
				<div className="flex justify-start font-size-14 line-height-22 padding-left-16 margin-bottom-20">
					<div className="flex-shrink color-86909c margin-right-4">行业领域：</div>
					<div className="flex-sub">{detail.industrySector || '--'}</div>
				</div>
				<div className="flex justify-start font-size-14 line-height-22 padding-left-16 margin-bottom-20">
					<div className="flex-shrink color-86909c margin-right-4">产品（技术）名称：</div>
					<div className="flex-sub">{detail.productName || '--'}</div>
				</div>
				<div className="flex justify-start font-size-14 line-height-22 padding-left-16 margin-bottom-20">
					<div className="flex-shrink color-86909c margin-right-4">产品（技术）简介：</div>
					<div className="flex-sub">{detail.productDesc || '--'}</div>
				</div>
				<div className="flex justify-start font-size-14 line-height-22 padding-left-16 margin-bottom-20">
					<div className="flex-shrink color-86909c margin-right-4">使用场景设想：</div>
					<div className="flex-sub">{detail.usageScenario || '--'}</div>
				</div>
				<div className="flex justify-start font-size-14 line-height-22 padding-left-16 margin-bottom-20">
					<div className="flex-shrink color-86909c margin-right-4">联系人：</div>
					<div className="flex-sub">{detail.name || '--'}</div>
				</div>
				<div className="flex justify-start font-size-14 line-height-22 padding-left-16 margin-bottom-20">
					<div className="flex-shrink color-86909c margin-right-4">联系方式：</div>
					<div className="flex-sub">{detail.phone || '--'}</div>
				</div>
			</div>
			{/* 基本信息 结束 */}
		</div>
	);
};

export default Index;
