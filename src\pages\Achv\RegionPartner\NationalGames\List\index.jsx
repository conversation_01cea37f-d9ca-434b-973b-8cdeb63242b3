import { Table, Button, Popconfirm, Space, Affix, Form, Input, Row, Col } from 'antd';
import { useTableData } from '@/hook/useTableData';
import { useRouterLink } from '@/hook/useRouter';
import {
	pageNationalGamesInfoCollect as getTablePageData,
	batchDel as delTableItemData,
	exportNationalGamesInfoCollect as exportTableData,
} from '@/api/Achv/RegionPartner/NationalGames/index';

const Index = () => {
	const linkToPath = '/newAchv/topicManage/regionPartner/nationalGames';

	const { linkTo } = useRouterLink();

	const { form, dataSource, pagination, changePage, delTableData, onReset, onSearch, exportData } = useTableData({
		params: {},
		getTablePageData,
		delTableItemData,
		exportTableData,
	});

	return (
		<div className="">
			<div className="flex flex-shrink align-center margin-bottom-4 height-48 font-size-22 font-weight-500 color-1d2129">登记管理</div>
			{/* Tabs & 功能按钮 开始 */}
			<Affix offsetTop={0}>
				<div className="flex align-center justify-between padding-24 border-radius-8 bg-color-ffffff border-solid-bottom-f2f3f5">
					<div></div>
					<Space size={16}>
						<Button onClick={exportData}>批量导出</Button>
						<Button
							type="primary"
							onClick={() => {
								linkTo(`${linkToPath}/curd`);
							}}
						>
							新增登记
						</Button>
					</Space>
				</div>
			</Affix>
			{/* Tabs & 功能按钮 结束 */}
			<div className="flex-sub margin-top-18 padding-24 border-radius-8 bg-color-ffffff">
				{/* 筛选条件 开始 */}
				<div className="flex justify-between margin-bottom-18">
					<div className="flex-sub">
						<Form form={form} labelAlign="right" layout="inline">
							<Row className="width-100per" gutter={[12, 12]}>
								<Col span={8}>
									<Form.Item name="name" label="姓名">
										<Input placeholder="请输入姓名" />
									</Form.Item>
								</Col>
							</Row>
						</Form>
					</div>
					<div className="margin-left-12 padding-left-12 border-solid-left-e5e6eb">
						<Space size={16}>
							<Button onClick={onReset}>重置</Button>
							<Button type="primary" onClick={onSearch}>
								查询
							</Button>
						</Space>
					</div>
				</div>
				{/* 筛选条件 结束 */}

				{/* 表格 开始 */}
				<Table rowKey="id" dataSource={dataSource} pagination={pagination} onChange={changePage} scroll={{ x: 'max-content' }}>
					<Table.Column
						title="序号"
						key="index"
						width={60}
						align="center"
						render={(_, __, index) => {
							return index + 1;
						}}
					/>
					<Table.Column title="企业名称" dataIndex="company" />
					<Table.Column title="行业领域" dataIndex="industrySector" />
					<Table.Column title="产品（技术）名称" dataIndex="productName" />
					<Table.Column title="产品（技术）简介" dataIndex="productDesc" />
					<Table.Column title="使用场景设想" dataIndex="usageScenario" />
					<Table.Column title="联系人" dataIndex="name" />
					<Table.Column title="联系方式" dataIndex="phone" />
					<Table.Column
						title="操作"
						key="option"
						dataIndex="option"
						align="center"
						fixed="right"
						width="160px"
						render={(_, record) => {
							return (
								<>
									<Button type="link" size="small" onClick={() => linkTo(`${linkToPath}/detail?id=${record.id}&fromList=1`)}>
										详情/编辑
									</Button>
									<Popconfirm
										title="提示"
										description="确定删除吗？"
										onConfirm={() => {
											delTableData(record.id);
										}}
										okText="确定"
										cancelText="取消"
									>
										<Button type="link" size="small">
											删除
										</Button>
									</Popconfirm>
								</>
							);
						}}
					/>
				</Table>
				{/* 表格 结束 */}
			</div>
		</div>
	);
};

export default Index;
