import { useEffect, useState } from 'react';
import { useRouterLink } from '@/hook/useRouter';
import { Button, Space, Form, Input, message, Affix } from 'antd';

import { addPartnerInfo, updatePartnerInfo, queryPartnerInfo } from '@/api/Achv/RegionPartner/Register/index';

const Index = () => {
	const { linkTo, searchParams } = useRouterLink();
	const id = searchParams.get('id') || '';
	const [form] = Form.useForm();

	// 跳转地址
	const linkToPath = `/newAchv/topicManage/regionPartner/register`;

	// 禁止编辑发布类型
	const [hash, setHash] = useState('');

	// 取消
	const onCancel = () => {
		linkTo(-1);
	};

	// 提交
	const onSubmit = () => {
		form.validateFields()
			.then((values) => {
				const params = { ...values };

				delete params.tempArea;
				(values.id ? updatePartnerInfo : addPartnerInfo)(params).then(() => {
					message.success(values.id ? '修改成功' : '添加成功');
					setTimeout(() => {
						linkTo(-1);
					}, 500);
				});
			})
			.catch((error) => {
				console.log(error);
			});
	};

	// 获取详情
	const getDetail = () => {
		if (id) {
			queryPartnerInfo({ id }).then((res) => {
				const resData = res.data || {};

				// 处理区域
				resData.tempArea = [resData.provinceCode, resData.cityCode];
				// 处理 关联科转号
				resData.ttChannelsIds = resData.ttChannelsList || [];

				form.setFieldsValue(resData);
			});
		}
	};

	useEffect(() => {
		const hash = window.location.hash.replace('#', '');
		setHash(hash || 'section1');

		getDetail();
	}, []);

	// 跳转到指定锚点
	useEffect(() => {
		if (hash) {
			setTimeout(() => {
				const element = document.getElementById(hash);
				if (element) {
					element.scrollIntoView({ behavior: 'smooth' });
				}
			}, 500);
		}
	}, [hash]);

	return (
		<div className="">
			<div className="flex flex-shrink align-center margin-bottom-4 height-48 font-size-22 font-weight-500 color-1d2129">
				<Space size={10}>
					<div className="a color-86909c" onClick={() => linkTo(linkToPath)}>
						登记管理
					</div>
					<div className="color-86909c">/</div>
					<div>{id ? '登记编辑' : '新增登记'}</div>
				</Space>
			</div>

			{/* Tabs & 功能按钮 开始 */}
			<div id="section1"></div>
			<Affix offsetTop={0}>
				<div className="flex align-center justify-between padding-24 border-radius-8 border-solid-bottom-f2f2f2 bg-color-ffffff">
					<div className="flex align-center">
						{['基本信息'].map((ov, oi) => {
							const currentHash = `section${oi + 1}`;
							return (
								<div
									key={oi}
									className={`a margin-right-40 font-size-18 font-weight-500 ${hash === currentHash ? 'color-165dff' : ''}`}
								>
									{ov}
								</div>
							);
						})}
					</div>
					<Space size={16}>
						<Button onClick={onCancel}>取消</Button>
						<Button type="primary" onClick={onSubmit}>
							保存
						</Button>
					</Space>
				</div>
			</Affix>
			{/* Tabs & 功能按钮 结束 */}

			<Form form={form} className="antd-form-box" labelAlign="right" labelCol={{ style: { width: '120px' } }} initialValues={{}}>
				<Form.Item hidden name="id">
					<Input />
				</Form.Item>
				{/* 基本信息 开始 */}
				<div className="margin-top-20 padding-20 border-radius-8 bg-color-ffffff">
					<div className="padding-bottom-30 line-height-26 font-size-18 font-weight-500 color-1d2129">基本信息</div>
					<Form.Item label="姓名" name="name" rules={[{ required: true, message: '请输入姓名' }]}>
						<Input className="input-box" placeholder="请输入姓名" />
					</Form.Item>
					<Form.Item label="联系电话" name="phone" rules={[{ required: true, message: '请输入联系电话' }]}>
						<Input className="input-box" placeholder="请输入联系电话" />
					</Form.Item>
					<Form.Item label="所在单位" name="company" rules={[{ required: true, message: '请输入所在单位' }]}>
						<Input className="input-box" placeholder="请输入所在单位" />
					</Form.Item>
					<Form.Item label="职位" name="position" rules={[{ required: true, message: '请输入职位' }]}>
						<Input className="input-box" placeholder="请输入职位" />
					</Form.Item>
					<Form.Item label="期待合作内容" name="cooperationContent">
						<Input.TextArea rows={4} className="input-box" placeholder="请输入期待合作内容" />
					</Form.Item>
				</div>
				{/* 基本信息 结束 */}
			</Form>
		</div>
	);
};
export default Index;
