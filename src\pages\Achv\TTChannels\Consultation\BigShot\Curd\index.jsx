import { useEffect, useState } from 'react';
import { useRouterLink } from '@/hook/useRouter';
import { Button, Space, Form, Input, InputNumber, message, Affix, Radio, Cascader, Checkbox } from 'antd';
import UploadImg from '@/components/UploadImg';

import { getConsultation, addConsultation, updateConsultation } from '@/api/Achv/TTChannels/Consultation/BigShot/index';
import { getThreeLevelData } from '@/api/common';
import { getCategoryValueList } from '@/utils/achv';

import { consultationTypeData } from '@/pages/Achv/config';

const Index = () => {
	const { linkTo, searchParams } = useRouterLink();
	const id = searchParams.get('id') || '';
	const [form] = Form.useForm();

	// 跳转地址
	const linkToPath = `/newAchv/event`;

	const [hash, setHash] = useState('');

	// 取消
	const onCancel = () => {
		linkTo(-1);
	};

	// 提交
	const onSubmit = () => {
		form.validateFields()
			.then((values) => {
				const params = { ...values };

				delete params.tempArea;
				(values.id ? updateConsultation : addConsultation)(params).then(() => {
					message.success(values.id ? '修改成功' : '添加成功');
					setTimeout(() => {
						linkTo(-1);
					}, 500);
				});
			})
			.catch((error) => {
				console.log(error);
			});
	};

	// 获取详情
	const getDetail = () => {
		if (id) {
			getConsultation({ id }).then((res) => {
				const resData = res.data || {};

				// 处理区域
				resData.tempArea = [resData.provinceCode, resData.cityCode];

				form.setFieldsValue(resData);
			});
		}
	};

	useEffect(() => {
		const hash = window.location.hash.replace('#', '');
		setHash(hash || 'section1');

		getDetail();
	}, []);

	// 跳转到指定锚点
	useEffect(() => {
		if (hash) {
			setTimeout(() => {
				const element = document.getElementById(hash);
				if (element) {
					element.scrollIntoView({ behavior: 'smooth' });
				}
			}, 500);
		}
	}, [hash]);

	const [areaOptions, setAreaOptions] = useState([]);
	const [industryLabelOptions, setIndustryLabelOptions] = useState([]);

	useEffect(() => {
		getThreeLevelData({ level: 2 }).then((res) => {
			setAreaOptions(res.data || []);
		});

		getCategoryValueList('consultation_experts_industry_labels').then((res) => {
			setIndustryLabelOptions(res);
		});
	}, []);

	return (
		<div className="">
			<div className="flex flex-shrink align-center margin-bottom-4 height-48 font-size-22 font-weight-500 color-1d2129">
				<Space size={10}>
					<div className="a color-86909c" onClick={() => linkTo(linkToPath)}>
						大咖直答管理
					</div>
					<div className="color-86909c">/</div>
					<div>{id ? '大咖编辑' : '新增大咖'}</div>
				</Space>
			</div>

			{/* Tabs & 功能按钮 开始 */}
			<div id="section1"></div>
			<Affix offsetTop={0}>
				<div className="flex align-center justify-between padding-24 border-radius-8 border-solid-bottom-f2f2f2 bg-color-ffffff">
					<div className="flex align-center">
						{['基本信息'].map((ov, oi) => {
							const currentHash = `section${oi + 1}`;
							return (
								<div
									key={oi}
									className={`a margin-right-40 font-size-18 font-weight-500 ${hash === currentHash ? 'color-165dff' : ''}`}
								>
									{ov}
								</div>
							);
						})}
					</div>
					<Space size={16}>
						<Button onClick={onCancel}>取消</Button>
						<Button type="primary" onClick={onSubmit}>
							保存
						</Button>
					</Space>
				</div>
			</Affix>
			{/* Tabs & 功能按钮 结束 */}

			<Form form={form} className="antd-form-box" labelAlign="right" labelCol={{ style: { width: '120px' } }} initialValues={{}}>
				<Form.Item hidden name="id">
					<Input />
				</Form.Item>
				<Form.Item hidden name="rankingNum">
					<Input />
				</Form.Item>
				<Form.Item hidden name="ttChannelsId">
					<Input />
				</Form.Item>

				{/* 基本信息 开始 */}
				<div className="margin-top-20 padding-20 border-radius-8 bg-color-ffffff">
					<div className="padding-bottom-30 line-height-26 font-size-18 font-weight-500 color-1d2129">基本信息</div>
					<Form.Item label="大咖类型" name="consultationType" rules={[{ required: true, message: '请选择大咖类型' }]} initialValue={1}>
						<Radio.Group options={consultationTypeData} />
					</Form.Item>
					<Form.Item label="形象照片" name="avatarUrl" rules={[{ required: true, message: '请上传形象照片' }]}>
						<UploadImg size={5} width={160} height={160} />
					</Form.Item>
					<Form.Item label="大咖名称" name="name" rules={[{ required: true, message: '请输入大咖名称' }]}>
						<Input className="input-box" placeholder="请输入大咖名称" />
					</Form.Item>
					<Form.Item label="单位名称" name="companyName" rules={[{ required: true, message: '请输入单位名称' }]}>
						<Input className="input-box" placeholder="请输入单位名称" />
					</Form.Item>
					<Form.Item label="职位名称" name="positionName" rules={[{ required: true, message: '请输入职位名称' }]}>
						<Input className="input-box" placeholder="请输入职位名称" />
					</Form.Item>
					<Form.Item label="性别" name="gender" rules={[{ required: true, message: '请选择性别' }]} initialValue={1}>
						<Radio.Group
							options={[
								{ label: '男', value: 1 },
								{ label: '女', value: 2 },
							]}
						/>
					</Form.Item>
					<Form.Item hidden name="provinceCode">
						<Input />
					</Form.Item>
					<Form.Item hidden name="cityCode">
						<Input />
					</Form.Item>
					<Form.Item label="所属区域" name="tempArea" rules={[{ required: true, message: '请选择所属区域' }]}>
						<Cascader
							className="cascader-box"
							options={areaOptions}
							placeholder="请选择所属区域"
							displayRender={(label) => label.filter((ov) => ov).join('-')}
							onChange={(e = [undefined, undefined]) => {
								form.setFieldValue('provinceCode', e[0]);
								form.setFieldValue('cityCode', e[1]);
							}}
						/>
					</Form.Item>
					<Form.Item label="所属行业" name="industryLabelIds" rules={[{ required: true, type: 'array', message: '请选择所属行业' }]}>
						<Checkbox.Group options={industryLabelOptions} />
					</Form.Item>
					<Form.Item label="研究领域" name="researchDirection" rules={[{ required: true, message: '请输入研究领域' }]}>
						<Input.TextArea rows={4} placeholder="请输入研究领域" />
					</Form.Item>
					<Form.Item label="可咨询内容" name="consultationContent" rules={[{ required: true, message: '请输入可咨询内容' }]}>
						<Input.TextArea rows={4} placeholder="请输入可咨询内容" />
					</Form.Item>
					<Form.Item label="咨询人数基数" name="baseNum">
						<InputNumber className="width-160" min={0} precision={0} controls={false} placeholder="请输⼊基数" />
					</Form.Item>
				</div>
				{/* 基本信息 结束 */}
			</Form>
		</div>
	);
};

export default Index;
