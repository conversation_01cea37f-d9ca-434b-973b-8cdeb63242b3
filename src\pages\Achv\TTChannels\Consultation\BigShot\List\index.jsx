import { useState, useEffect } from 'react';
import { Table, Button, Popconfirm, Space, Affix, Form, Input, Row, Col, Select, Switch, Cascader, message } from 'antd';
import { useTableData } from '@/hook/useTableData';
import { useRouterLink } from '@/hook/useRouter';
import {
	pageConsultation as getTablePageData,
	batchDel as delTableItemData,
	updateRankingNum as batchUpdateSort,
	updateRecommendStatus,
} from '@/api/Achv/TTChannels/Consultation/BigShot/index';
import { getThreeLevelData } from '@/api/common';

import { auditStatusData, auditStatusTextList, consultationTypeData, consultationTypeList } from '@/pages/Achv/config';

const Index = () => {
	const { linkTo, searchParams } = useRouterLink();
	const linkToPath = '/newAchv/TTChannels/consultation/bigShot';

	const status = searchParams.get('auditStatus');
	const [auditStatus, setAuditStatus] = useState(status === null ? 3 : status - 0 || '');

	const { form, dataSource, pagination, changePage, getTableData, delTableData, onReset, onSearch, SortInput } = useTableData({
		params: { auditStatus },
		getTablePageData,
		delTableItemData,
		batchUpdateSort,
		pageCallback: (paramsData = {}) => {
			getStatisticsData({ ...paramsData });
		},
	});

	// 获取统计数据
	const [statistics, setStatistics] = useState({});
	const getStatisticsData = (paramsData) => {
		delete paramsData.auditStatus;
		delete paramsData.pageSize;
		delete paramsData.pageNum;
		delete paramsData.tempArea;
		Promise.all([
			getTablePageData({
				...paramsData,
				pageSize: 1,
				pageNum: 1,
				auditStatus: 3,
			}),
			getTablePageData({
				...paramsData,
				pageSize: 1,
				pageNum: 1,
				auditStatus: 1,
			}),
			getTablePageData({
				...paramsData,
				pageSize: 1,
				pageNum: 1,
				auditStatus: 2,
			}),
		]).then((resList) => {
			setStatistics({
				total: resList.reduce((sum, cur) => sum + (cur.data.total - 0), 0),
				releaseNum: resList[0].data.total,
				waitReleaseNum: resList[1].data.total,
				notPassNum: resList[2].data.total,
			});
		});
	};

	// 修改推荐状态
	const recommendStatusChange = (record) => {
		updateRecommendStatus({ id: record.id, recommendStatus: record.recommendStatus == 1 ? 0 : 1 }).then(() => {
			message.success('操作成功');
			getTableData();
		});
	};

	// 获取选项数据
	const [areaOptions, setAreaOptions] = useState([]);
	const getOptionsData = () => {
		getThreeLevelData({ level: 2 }).then((res) => {
			setAreaOptions(res.data || []);
		});
	};

	useEffect(() => {
		getOptionsData();
	}, []);

	return (
		<div className="">
			<div className="flex flex-shrink align-center margin-bottom-4 height-48 font-size-22 font-weight-500 color-1d2129">大咖管理</div>

			{/* Tabs & 功能按钮 开始 */}
			<Affix offsetTop={0}>
				<div className="flex align-center justify-between padding-24 border-radius-8 bg-color-ffffff border-solid-bottom-f2f3f5">
					<div className="flex align-center">
						{auditStatusData.map((ov) => (
							<div
								key={ov.value}
								className={`a margin-right-40 font-size-18 font-weight-500 ${auditStatus === ov.value ? 'color-165dff' : ''}`}
								onClick={() => setAuditStatus(ov.value)}
							>
								{ov.label}
								{statistics[ov.countName] > 0 ? `（${statistics[ov.countName]}）` : ''}
							</div>
						))}
					</div>
					<Space size={16}></Space>
				</div>
			</Affix>
			{/* Tabs & 功能按钮 结束 */}

			<div className="flex-sub margin-top-18 padding-24 border-radius-8 bg-color-ffffff">
				{/* 筛选条件 开始 */}
				<div className="flex justify-between margin-bottom-18">
					<div className="flex-sub">
						<Form form={form} labelAlign="right" layout="inline">
							<Row className="width-100per" gutter={[12, 12]}>
								<Col span={8}>
									<Form.Item name="name" label="账号名称">
										<Input placeholder="请输入账号名称" />
									</Form.Item>
								</Col>
								<Col span={8}>
									<Form.Item name="consultationType" label="账号类型">
										<Select placeholder="请选择账号类型" options={consultationTypeData} allowClear />
									</Form.Item>
								</Col>
								<Col span={8}>
									<Form.Item hidden name="provinceCodes">
										<Input />
									</Form.Item>
									<Form.Item hidden name="cityCodes">
										<Input />
									</Form.Item>
									<Form.Item name="tempArea" label="城市" initialValue={[]}>
										<Cascader
											options={areaOptions}
											placeholder="请选择城市"
											displayRender={(label) => label.join('-')}
											onChange={(e = [undefined, undefined]) => {
												form.setFieldValue('provinceCodes', e[0] ? [e[0]] : undefined);
												form.setFieldValue('cityCodes', e[1] ? [e[1]] : undefined);
											}}
										/>
									</Form.Item>
								</Col>
							</Row>
						</Form>
					</div>
					<div className="margin-left-12 padding-left-12 border-solid-left-e5e6eb">
						<Space size={16}>
							<Button onClick={onReset}>重置</Button>
							<Button type="primary" onClick={onSearch}>
								查询
							</Button>
						</Space>
					</div>
				</div>
				{/* 筛选条件 结束 */}

				{/* 表格 开始 */}
				<Table rowKey="id" dataSource={dataSource} pagination={pagination} onChange={changePage} scroll={{ x: 'max-content' }}>
					<Table.Column
						title="序号"
						key="index"
						width={60}
						align="center"
						render={(_, __, index) => {
							return index + 1;
						}}
					/>

					<Table.Column title="账号名" dataIndex="name" />
					<Table.Column
						title="类型"
						dataIndex="consultationType"
						align="center"
						render={(text) => {
							return (
								<div className={`tag-status-small-${['default', 'warning', 'error', 'primary', '700eb2'][text]}  `}>
									{consultationTypeList[text || ''] || '--'}
								</div>
							);
						}}
					/>
					<Table.Column
						title="发布状态"
						dataIndex="auditStatus"
						align="center"
						render={(text) => {
							return (
								<div className={`tag-status-small-${['default', 'warning', 'error', 'primary'][text]}  `}>
									{auditStatusTextList[text || ''] || '--'}
								</div>
							);
						}}
					/>
					<Table.Column
						title="咨询人数"
						align="center"
						dataIndex="consultationNum"
						render={(text) => {
							return text || 0;
						}}
					/>
					<Table.Column
						title="首页推荐"
						dataIndex="recommendStatus"
						align="center"
						render={(_, record) => {
							return (
								<Switch
									checked={record.recommendStatus == 1}
									disabled={record.auditStatus !== 3}
									title={record.auditStatus !== 3 ? '仅审核通过后才能操作' : ''}
									onChange={() => {
										recommendStatusChange(record);
									}}
								/>
							);
						}}
					/>
					<Table.Column
						title="排序"
						align="center"
						dataIndex="rankingNum"
						render={(_, record) => {
							return <SortInput record={record} />;
						}}
					/>
					<Table.Column
						title="城市"
						dataIndex="cityName"
						align="center"
						render={(_, record) => {
							return [record.provinceName, record.cityName, record.areaName].filter((ov) => ov).join('-');
						}}
					/>
					<Table.Column
						title="操作"
						key="option"
						dataIndex="option"
						align="center"
						fixed="right"
						width="160px"
						render={(_, record) => {
							return (
								<>
									<Button type="link" size="small" onClick={() => linkTo(`${linkToPath}/detail?id=${record.id}&fromList=1`)}>
										详情/编辑
									</Button>
									<Popconfirm
										title="提示"
										description="确定删除吗？"
										onConfirm={() => {
											delTableData(record.id);
										}}
										okText="确定"
										cancelText="取消"
									>
										<Button type="link" size="small">
											删除
										</Button>
									</Popconfirm>
								</>
							);
						}}
					/>
				</Table>
				{/* 表格 结束 */}
			</div>
		</div>
	);
};

export default Index;
