import { useEffect, useState } from 'react';
import { useRouterLink } from '@/hook/useRouter';
import { Button, Space, Form, Input, DatePicker, message, Affix } from 'antd';
import UploadImg from '@/components/UploadImg';
import UEditor from '@/components/UEditor';
import SelectTTChannels from '@/components/Achv/SelectTTChannels';

import { getTTInteractiveTopic, saveTTInteractiveTopic, updateTTInteractiveTopic } from '@/api/Achv/TTChannels/Consultation/InteractiveTopic/index';

import dayjs from 'dayjs';
const Index = () => {
	const { linkTo, searchParams } = useRouterLink();
	const id = searchParams.get('id') || '';
	const [form] = Form.useForm();

	// 跳转地址
	const linkToPath = `/newAchv/TTChannels/consultation/interactiveTopic`;

	const [hash, setHash] = useState('');

	// 取消
	const onCancel = () => {
		linkTo(-1);
	};

	// 提交
	const onSubmit = () => {
		form.validateFields()
			.then((values) => {
				const params = { ...values, ttChannelsIds: values.ttChannelsIds ? values.ttChannelsIds.map((ov) => ov.id) : undefined };

				(values.id ? updateTTInteractiveTopic : saveTTInteractiveTopic)(params).then(() => {
					message.success(values.id ? '修改成功' : '添加成功');
					setTimeout(() => {
						linkTo(-1);
					}, 500);
				});
			})
			.catch((error) => {
				const errorName = error.errorFields[0].name[0];
				if (['ttChannelsIds'].includes(errorName)) {
					setHash('section2');
				} else {
					setHash('section1');
				}
			});
	};

	// 获取详情
	const getDetail = () => {
		if (id) {
			getTTInteractiveTopic({ id }).then((res) => {
				const resData = res.data || {};
				// 处理 关联科转号
				resData.ttChannelsIds = resData.ttChannelsList || [];
				form.setFieldsValue(resData);
			});
		}
	};

	useEffect(() => {
		const hash = window.location.hash.replace('#', '');
		setHash(hash || 'section1');

		getDetail();
	}, []);

	// 跳转到指定锚点
	useEffect(() => {
		if (hash) {
			setTimeout(() => {
				const element = document.getElementById(hash);
				if (element) {
					element.scrollIntoView({ behavior: 'smooth' });
				}
			}, 500);
		}
	}, [hash]);

	return (
		<div className="">
			<div className="flex flex-shrink align-center margin-bottom-4 height-48 font-size-22 font-weight-500 color-1d2129">
				<Space size={10}>
					<div className="a color-86909c" onClick={() => linkTo(linkToPath)}>
						互动专题管理
					</div>
					<div className="color-86909c">/</div>
					<div>{id ? '编辑专题' : '创建专题'}</div>
				</Space>
			</div>

			{/* Tabs & 功能按钮 开始 */}
			<div id="section1"></div>
			<Affix offsetTop={0}>
				<div className="flex align-center justify-between padding-24 border-radius-8 border-solid-bottom-f2f2f2 bg-color-ffffff">
					<div className="flex align-center">
						{['基本信息', '关联嘉宾'].map((ov, oi) => {
							const currentHash = `section${oi + 1}`;
							return (
								<div
									key={oi}
									className={`a margin-right-40 font-size-18 font-weight-500 ${hash === currentHash ? 'color-165dff' : ''}`}
									onClick={() => {
										if (currentHash === 'section2') {
											form.validateFields()
												.then(() => {
													setHash(`section${oi + 1}`);
												})
												.catch((error) => {
													const errorName = error.errorFields[0].name[0];
													if (['ttChannelsIds'].includes(errorName)) {
														setHash('section2');
													} else {
														setHash('section1');
													}
												});
										} else {
											setHash(`section${oi + 1}`);
										}
									}}
								>
									{ov}
								</div>
							);
						})}
					</div>
					<Space size={16}>
						<Button onClick={onCancel}>取消</Button>
						<Button type="primary" onClick={onSubmit}>
							保存
						</Button>
					</Space>
				</div>
			</Affix>
			{/* Tabs & 功能按钮 结束 */}

			<Form
				form={form}
				className="antd-form-box"
				labelAlign="right"
				labelCol={{ style: { width: '120px' } }}
				initialValues={{
					ttChannelsIds: [],
				}}
			>
				<Form.Item hidden name="id">
					<Input />
				</Form.Item>
				<Form.Item hidden name="rankingNum">
					<Input />
				</Form.Item>

				{/* 基本信息 开始 */}
				<div style={{ display: hash === 'section1' ? 'block' : 'none' }} className="margin-top-20 padding-20 border-radius-8 bg-color-ffffff">
					<div className="padding-bottom-30 line-height-26 font-size-18 font-weight-500 color-1d2129">基本信息</div>
					<Form.Item label="专题名称" name="name" rules={[{ required: true, message: '请输入专题名称' }]}>
						<Input className="input-box" placeholder="请输入专题名称" />
					</Form.Item>
					<Form.Item hidden name="endTime">
						<Input />
					</Form.Item>
					<Form.Item label="互动时间" name="startTime" rules={[{ required: true, message: '请选择互动时间' }]}>
						<TimeFormItem form={form} />
					</Form.Item>
					<Form.Item label="专题描述" name="content" rules={[{ required: true, message: '请输入专题描述' }]}>
						<UEditor toolbars={['bold']} />
					</Form.Item>
					<Form.Item label="专题封面" name="topicCoverUrl" rules={[{ required: true, message: '请上传专题封面' }]}>
						<UploadImg size={5} width={200} height={120} />
					</Form.Item>
					<Form.Item label="详情banner" name="bannerUrl" rules={[{ required: true, message: '请上传专题封面' }]}>
						<UploadImg size={5} width={200} height={120} />
					</Form.Item>
					<Form.Item label="分享海报" name="sharePosterUrl" rules={[{ required: true, message: '请上传分享海报' }]}>
						<UploadImg size={5} width={200} height={120} />
					</Form.Item>
					<Form.Item label="关联合作二维码" name="linkQrCode">
						<UploadImg size={5} width={120} height={120} cropperProps={{ width: 120, height: 120 }} />
					</Form.Item>
					<Form.Item label="分享文案" name="shareDesc">
						<Input className="input-box" placeholder="请输入分享文案" />
					</Form.Item>
					<Form.Item label="分享朋友圈封面" name="timelineShareCoverUrl">
						<UploadImg size={5} width={120} height={120} cropperProps={{ width: 120, height: 120 }} />
					</Form.Item>
					<Form.Item label="分享好友封面" name="friendShareCoverUrl">
						<UploadImg size={5} width={150} height={120} cropperProps={{ width: 150, height: 120 }} />
					</Form.Item>
				</div>
				{/* 基本信息 结束 */}
				{/* 关联嘉宾 开始 */}
				<div style={{ display: hash === 'section2' ? 'block' : 'none' }}>
					<div id="section2"></div>
					<SelectTTChannels
						form={form}
						name="ttChannelsIds"
						label="关联嘉宾"
						rules={[{ required: true, type: 'array', message: '请选择关联嘉宾' }]}
					/>
				</div>
				{/* 关联嘉宾 结束 */}
			</Form>
		</div>
	);
};

// 互动时间
const TimeFormItem = (props = {}) => {
	const startTime = Form.useWatch('startTime', props.form);
	const endTime = Form.useWatch('endTime', props.form);

	return (
		<DatePicker.RangePicker
			value={[startTime ? dayjs(startTime) : null, endTime ? dayjs(endTime) : null]}
			showTime
			format={'YYYY-MM-DD HH:mm'}
			onChange={(_, e) => {
				props.form.setFieldsValue({
					startTime: e[0] ? e[0].padEnd(19, ':00') : null,
					endTime: e[1] ? e[1].padEnd(19, ':59') : null,
				});
			}}
		/>
	);
};

export default Index;
