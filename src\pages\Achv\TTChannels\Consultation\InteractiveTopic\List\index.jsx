import { useState } from 'react';
import { Table, Button, Popconfirm, Space, Affix, Switch, message, Image, Tooltip, Form } from 'antd';
import { QuestionCircleFilled } from '@ant-design/icons';
import { useTableData } from '@/hook/useTableData';
import { useRouterLink } from '@/hook/useRouter';
import {
	getTTInteractiveTopicPage as getTablePageData,
	batchDelTTInteractiveTopic as delTableItemData,
	updateRankingNum as batchUpdateSort,
	updateShowStatus,
	getTTIntegetStatisticsractiveTopic,
} from '@/api/Achv/TTChannels/Consultation/InteractiveTopic/index';

import { auditStatusData, auditStatusTextList } from '@/pages/Achv/config';

const Index = () => {
	const { linkTo, searchParams } = useRouterLink();
	const linkToPath = '/newAchv/TTChannels/consultation/interactiveTopic';

	const status = searchParams.get('auditStatus');
	const [auditStatus, setAuditStatus] = useState(status === null ? 3 : status - 0 || '');

	const { form, dataSource, pagination, changePage, getTableData, delTableData, SortInput } = useTableData({
		params: { auditStatus },
		getTablePageData,
		delTableItemData,
		batchUpdateSort,
		pageCallback: (paramsData = {}) => {
			getStatisticsData({ ...paramsData });
		},
	});

	// 获取统计数据
	const [statistics, setStatistics] = useState({});
	const getStatisticsData = (paramsData) => {
		delete paramsData.auditStatus;
		delete paramsData.pageSize;
		delete paramsData.pageNum;
		getTTIntegetStatisticsractiveTopic(paramsData).then((res) => {
			setStatistics(res.data || {});
		});
	};

	// 修改展示状态
	const showStatusChange = (record) => {
		updateShowStatus({ id: record.id, showStatus: record.showStatus == 1 ? 0 : 1 }).then(() => {
			message.success('操作成功');
			getTableData();
		});
	};

	return (
		<div className="">
			<div className="flex flex-shrink align-center margin-bottom-4 height-48 font-size-22 font-weight-500 color-1d2129">互动专题管理</div>
			<Form form={form}></Form>
			{/* Tabs & 功能按钮 开始 */}
			<Affix offsetTop={0}>
				<div className="flex align-center justify-between padding-24 border-radius-8 bg-color-ffffff border-solid-bottom-f2f3f5">
					<div className="flex align-center">
						{auditStatusData.map((ov) => (
							<div
								key={ov.value}
								className={`a margin-right-40 font-size-18 font-weight-500 ${auditStatus === ov.value ? 'color-165dff' : ''}`}
								onClick={() => setAuditStatus(ov.value)}
							>
								{ov.label}
								{statistics[ov.countName] > 0 ? `（${statistics[ov.countName]}）` : ''}
							</div>
						))}
					</div>
					<Space size={16}>
						<Button
							type="primary"
							onClick={() => {
								linkTo(`${linkToPath}/curd`);
							}}
						>
							创建专题
						</Button>
					</Space>
				</div>
			</Affix>
			{/* Tabs & 功能按钮 结束 */}

			<div className="flex-sub margin-top-18 padding-24 border-radius-8 bg-color-ffffff">
				{/* 表格 开始 */}
				<Table rowKey="id" dataSource={dataSource} pagination={pagination} onChange={changePage} scroll={{ x: 'max-content' }}>
					<Table.Column
						title="序号"
						key="index"
						width={60}
						align="center"
						render={(_, __, index) => {
							return index + 1;
						}}
					/>
					<Table.Column
						title="专题封面"
						key="topicCoverUrl"
						width={100}
						render={(_, record) => {
							return (
								(record.topicCoverUrl && (
									<div className="flex align-center justify-center width-100 height-100 overflow-hidden">
										<Image width={100} src={record.topicCoverUrl} />
									</div>
								)) ||
								null
							);
						}}
					/>
					<Table.Column title="专题名称" dataIndex="name" />
					<Table.Column
						title="发布状态"
						dataIndex="auditStatus"
						align="center"
						render={(text) => {
							return (
								<div className={`tag-status-small-${['default', 'warning', 'error', 'primary'][text]}  `}>
									{auditStatusTextList[text || ''] || '--'}
								</div>
							);
						}}
					/>
					<Table.Column
						title="互动时间"
						dataIndex="name"
						render={(_, record) => {
							return (
								<div>
									<div>{(record.startTime || '').slice(0, 16)}</div>
									<div>至</div>
									<div>{(record.endTime || '').slice(0, 16)}</div>
								</div>
							);
						}}
					/>
					<Table.Column
						title="展示状态"
						dataIndex="showStatus"
						align="center"
						render={(_, record) => {
							return (
								<Switch
									checked={record.showStatus == 1}
									disabled={record.auditStatus !== 3}
									title={record.auditStatus !== 3 ? '仅审核通过后才能操作' : ''}
									onChange={() => {
										showStatusChange(record);
									}}
								/>
							);
						}}
					/>
					<Table.Column
						title="嘉宾"
						dataIndex="ttChannelsList"
						render={(text) => {
							return (text || []).map((ov) => ov.accountName).join('、') || '--';
						}}
					/>

					<Table.Column
						title={
							<Tooltip title="此问答数量是指:问题数量/回答数量">
								<Space size={4}>
									<div>问答数量</div>
									<QuestionCircleFilled className="color-165dff" />
								</Space>
							</Tooltip>
						}
						align="center"
						dataIndex="questionNum"
						render={(_, record) => {
							return (
								<div
									className="a color-165dff"
									onClick={() => {
										linkTo(`${linkToPath}/qa/list?interactiveTopicId=${record.id}&fromPage=1`);
									}}
								>{`${record.replyNum}/${record.questionNum}`}</div>
							);
						}}
					/>
					<Table.Column
						title="排序"
						align="center"
						dataIndex="rankingNum"
						render={(_, record) => {
							return <SortInput record={record} />;
						}}
					/>
					<Table.Column
						title="操作"
						key="option"
						dataIndex="option"
						align="center"
						fixed="right"
						width="160px"
						render={(_, record) => {
							return (
								<>
									<Button type="link" size="small" onClick={() => linkTo(`${linkToPath}/detail?id=${record.id}&fromList=1`)}>
										详情/编辑
									</Button>
									<Popconfirm
										title="提示"
										description="确定删除吗？"
										onConfirm={() => {
											delTableData(record.id);
										}}
										okText="确定"
										cancelText="取消"
									>
										<Button type="link" size="small">
											删除
										</Button>
									</Popconfirm>
								</>
							);
						}}
					/>
				</Table>
				{/* 表格 结束 */}
			</div>
		</div>
	);
};

export default Index;
