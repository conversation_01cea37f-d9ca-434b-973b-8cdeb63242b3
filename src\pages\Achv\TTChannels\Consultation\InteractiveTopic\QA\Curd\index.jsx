import { useEffect, useState } from 'react';
import { useRouterLink } from '@/hook/useRouter';
import { Button, Space, Form, Input, DatePicker, message, Affix, Radio } from 'antd';
import UploadImg from '@/components/UploadImg';

import {
	getTTInteractiveTopicQa,
	saveTTInteractiveTopicQa,
	updateTTInteractiveTopicQa,
} from '@/api/Achv/TTChannels/Consultation/InteractiveTopic/QA/index';
import { getTTInteractiveTopic } from '@/api/Achv/TTChannels/Consultation/InteractiveTopic/index';
import { QAReleasePlatformsData } from '@/pages/Achv/config';

import dayjs from 'dayjs';
const Index = () => {
	const { linkTo, searchParams } = useRouterLink();
	const id = searchParams.get('id') || '';
	const interactiveTopicId = searchParams.get('interactiveTopicId');
	const fromPage = !!searchParams.get('fromPage');
	const [form] = Form.useForm();
	const anonymousStatus = Form.useWatch('anonymousStatus', form);
	const userId = Form.useWatch('userId', form);
	const avatarUrl = Form.useWatch('avatarUrl', form);

	// 跳转地址
	const linkToPath = `/newAchv/TTChannels/consultation/interactiveTopic`;

	const [hash, setHash] = useState('');

	// 取消
	const onCancel = () => {
		linkTo(-1);
	};

	// 提交
	const onSubmit = () => {
		form.validateFields().then((values) => {
			const params = {
				...values,
				replyList: values.replyList.filter((ov) => (ov.content || '').trim()),
			};

			if (anonymousStatus === 1 || params.userId) {
				params.avatarUrl = '';
				params.userName = '';
			}

			(values.id ? updateTTInteractiveTopicQa : saveTTInteractiveTopicQa)(params).then(() => {
				message.success(values.id ? '修改成功' : '添加成功');
				setTimeout(() => {
					linkTo(-1);
				}, 500);
			});
		});
	};

	// 获取详情
	const getDetail = () => {
		if (id) {
			getTTInteractiveTopicQa({ id }).then((res) => {
				const resData = res.data || {};
				if (resData.replyList.length === 0) {
					resData.replyList = [
						{
							id: '',
							parentId: '',
							releasePlatform: 1,
							content: '',
						},
					];
				}
				form.setFieldsValue(resData);
			});
		}
	};

	useEffect(() => {
		const hash = window.location.hash.replace('#', '');
		setHash(hash || 'section1');

		getDetail();
	}, []);

	// 跳转到指定锚点
	useEffect(() => {
		if (hash) {
			setTimeout(() => {
				const element = document.getElementById(hash);
				if (element) {
					element.scrollIntoView({ behavior: 'smooth' });
				}
			}, 500);
		}
	}, [hash]);

	// 获取专题名称
	const [interactiveTopicName, setInteractiveTopicName] = useState('');
	useEffect(() => {
		if (interactiveTopicId) {
			getTTInteractiveTopic({ id: interactiveTopicId }).then((res) => {
				setInteractiveTopicName(res?.data?.name);
			});
			form.setFieldsValue({
				interactiveTopicId,
			});
		}
	}, [interactiveTopicId]);

	return (
		<div className="">
			<div className="flex flex-shrink align-center margin-bottom-4 height-48 font-size-22 font-weight-500 color-1d2129">
				<Space size={10}>
					<div className="a color-86909c" onClick={() => linkTo(linkToPath)}>
						互动专题管理
					</div>
					<div className="color-86909c">/</div>
					<div className="a color-86909c" onClick={() => linkTo(`${linkToPath}/detail?id=${interactiveTopicId}`)}>
						{interactiveTopicName}
					</div>
					<div className="color-86909c">/</div>
					<div
						className="a color-86909c"
						onClick={() => linkTo(fromPage ? -1 : `${linkToPath}/qa?interactiveTopicId=${interactiveTopicId}`)}
					>
						问答管理
					</div>
					<div className="color-86909c">/</div>
					<div>{id ? '编辑问答' : '创建问答'}</div>
				</Space>
			</div>

			{/* Tabs & 功能按钮 开始 */}
			<div id="section1"></div>
			<Affix offsetTop={0}>
				<div className="flex align-center justify-between padding-24 border-radius-8 border-solid-bottom-f2f2f2 bg-color-ffffff">
					<div className="flex align-center">
						{['问题内容', '回复内容'].map((ov, oi) => {
							const currentHash = `section${oi + 1}`;
							return (
								<div
									key={oi}
									className={`a margin-right-40 font-size-18 font-weight-500 ${hash === currentHash ? 'color-165dff' : ''}`}
									onClick={() => {
										setHash(`section${oi + 1}`);
									}}
								>
									{ov}
								</div>
							);
						})}
					</div>
					<Space size={16}>
						<Button onClick={onCancel}>取消</Button>
						<Button type="primary" onClick={onSubmit}>
							保存
						</Button>
					</Space>
				</div>
			</Affix>
			{/* Tabs & 功能按钮 结束 */}

			<Form
				form={form}
				className="antd-form-box"
				labelAlign="right"
				labelCol={{ style: { width: '120px' } }}
				initialValues={{
					releasePlatform: 1,
					anonymousStatus: 0,
					replyList: [
						{
							id: '',
							parentId: '',
							releasePlatform: 1,
							content: '',
						},
					],
				}}
			>
				<Form.Item hidden name="id">
					<Input />
				</Form.Item>
				<Form.Item hidden name="userId">
					<Input />
				</Form.Item>
				<Form.Item hidden name="rankingNum">
					<Input />
				</Form.Item>
				<Form.Item hidden name="interactiveTopicId">
					<Input />
				</Form.Item>
				{/* 问题内容 开始 */}
				<div className="margin-top-20 padding-20 border-radius-8 bg-color-ffffff">
					<div className="padding-bottom-30 line-height-26 font-size-18 font-weight-500 color-1d2129">问题内容</div>
					<Form.Item label="问题内容" name="content" rules={[{ required: true, message: '请输入问题内容' }]}>
						<Input.TextArea rows={4} placeholder="请输入问题内容" />
					</Form.Item>
					<Form.Item label="提问来源" name="releasePlatform" rules={[{ required: true, message: '请选择提问来源' }]}>
						<Radio.Group
							options={QAReleasePlatformsData}
							onChange={(e) => {
								if (e.target.value === 3) {
									form.setFieldValue('userName', '21财经APP用户');
									form.setFieldValue(
										'avatarUrl',
										'https://gbac-produce.oss-cn-shenzhen.aliyuncs.com/static-assets/gbac-achv-applet/ttChannels/InteractiveTopicDetail/%E8%B4%A2%E7%BB%8Fapp-logo.jpg'
									);
								}
							}}
						/>
					</Form.Item>
					<Form.Item label="是否匿名" name="anonymousStatus" rules={[{ required: true, message: '请选择是否匿名' }]}>
						<Radio.Group
							options={[
								{ label: '否', value: 0 },
								{ label: '是', value: 1 },
							]}
						/>
					</Form.Item>
					{anonymousStatus === 0 && (
						<>
							<Form.Item label="用户头像" name="avatarUrl" rules={[{ required: true, message: '请上传用户头像' }]}>
								{userId ? (
									<Image width={120} height={120} src={avatarUrl} />
								) : (
									<UploadImg size={5} width={120} height={120} cropperProps={{ width: 120, height: 120 }} />
								)}
							</Form.Item>
							<Form.Item label="用户昵称" name="userName" rules={[{ required: true, message: '请输入用户昵称' }]}>
								<Input rows={4} placeholder="请输入用户昵称" disabled={userId} />
							</Form.Item>
						</>
					)}
				</div>
				{/* 问题内容 结束 */}
				{/* 回复内容 开始 */}
				<div id="section2"></div>
				<div className="margin-top-20 padding-20 border-radius-8 bg-color-ffffff">
					<div className="padding-bottom-30 line-height-26 font-size-18 font-weight-500 color-1d2129">回复内容</div>
					<Form.List name="replyList">
						{(fields) => {
							return fields.map(({ key, name, ...restField }) => {
								return (
									<>
										<Form.Item hidden {...restField} name={[name, 'id']}>
											<Input />
										</Form.Item>
										<Form.Item hidden {...restField} name={[name, 'parentId']}>
											<Input />
										</Form.Item>
										<Form.Item label="回复内容" {...restField} name={[name, 'content']}>
											<Input.TextArea rows={4} placeholder="请输入回复内容" />
										</Form.Item>
										<Form.Item label="回复方式" {...restField} name={[name, 'releasePlatform']}>
											<Radio.Group className="line-height-32" options={QAReleasePlatformsData.filter((ov) => ov.value < 3)} />
										</Form.Item>
									</>
								);
							});
						}}
					</Form.List>
				</div>
				{/* 回复内容 结束 */}
			</Form>
		</div>
	);
};

// 互动时间
const TimeFormItem = (props = {}) => {
	const startTime = Form.useWatch('startTime', props.form);
	const endTime = Form.useWatch('endTime', props.form);

	return (
		<DatePicker.RangePicker
			value={[startTime ? dayjs(startTime) : null, endTime ? dayjs(endTime) : null]}
			showTime
			format={'YYYY-MM-DD HH:mm'}
			onChange={(_, e) => {
				props.form.setFieldsValue({
					startTime: e[0] ? e[0].padEnd(19, ':00') : null,
					endTime: e[1] ? e[1].padEnd(19, ':59') : null,
				});
			}}
		/>
	);
};

export default Index;
