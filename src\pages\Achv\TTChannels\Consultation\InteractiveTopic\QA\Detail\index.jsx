import { useEffect, useState, useRef, forwardRef, useImperativeHandle } from 'react';
import { useRouterLink } from '@/hook/useRouter';

import { Button, Input, Affix, Anchor, message, Form, Modal, Image, Space, Tooltip } from 'antd';
import { QuestionCircleFilled, EditOutlined } from '@ant-design/icons';
import ModalForm from '@/components/ModalForm';

import { getTTInteractiveTopicQa, auditQa } from '@/api/Achv/TTChannels/Consultation/InteractiveTopic/QA/index';
import { getTTInteractiveTopic } from '@/api/Achv/TTChannels/Consultation/InteractiveTopic/index';

import { auditStatusTextList, QAReleasePlatformTextList } from '@/pages/Achv/config';

const Index = () => {
	const ModalFormRef = useRef();
	const { linkTo, searchParams } = useRouterLink();
	const id = searchParams.get('id') || '';
	const interactiveTopicId = searchParams.get('interactiveTopicId');
	const fromPage = searchParams.get('fromPage') ? 1 : '';

	const [detail, setDetail] = useState({});

	// 跳转地址
	const linkToPath = `/newAchv/TTChannels/consultation/interactiveTopic`;

	// 获取详情
	const getDetail = () => {
		if (id) {
			getTTInteractiveTopicQa({ id }).then((res) => {
				const resData = res.data || {};

				setDetail(resData);
			});
		}
	};

	// 通过审核
	const handlePass = () => {
		Modal.confirm({
			title: '需求审核',
			content: `是否通过审核？`,
			onOk() {
				handleAudit({ status: 3 });
			},
		});
	};

	// 撤销审核
	const handleRevoke = () => {
		Modal.confirm({
			title: '撤销审核',
			content: `是否撤销审核结果？`,
			onOk() {
				handleAudit({ status: 1 });
			},
		});
	};

	// 审核操作
	const handleAudit = (values) => {
		auditQa({ ...values, id }).then(() => {
			message.success('操作成功');

			setTimeout(() => {
				linkTo(-1);
			}, 500);
		});
	};

	useEffect(() => {
		getDetail();
	}, []);

	// 获取专题名称
	const [interactiveTopicName, setInteractiveTopicName] = useState('');
	useEffect(() => {
		if (interactiveTopicId) {
			getTTInteractiveTopic({ id: interactiveTopicId }).then((res) => {
				setInteractiveTopicName(res?.data?.name);
			});
		}
	}, [interactiveTopicId]);

	return (
		<div className="">
			<div className="flex flex-shrink align-center margin-bottom-4 height-48 font-size-22 font-weight-500 color-1d2129">
				<Space size={10}>
					<div className="a color-86909c" onClick={() => linkTo(linkToPath)}>
						互动专题管理
					</div>
					<div className="color-86909c">/</div>
					<div className="a color-86909c" onClick={() => linkTo(`${linkToPath}/detail?id=${interactiveTopicId}`)}>
						{interactiveTopicName}
					</div>
					<div className="color-86909c">/</div>
					<div
						className="a color-86909c"
						onClick={() => linkTo(fromPage ? -1 : `${linkToPath}/qa?interactiveTopicId=${interactiveTopicId}`)}
					>
						问答管理
					</div>
					<div className="color-86909c">/</div>
					<div>专题详情</div>
				</Space>
			</div>
			<div id="section1"></div>
			{/* Tabs & 功能按钮 开始 */}
			<Affix>
				<div className="height-66 flex align-center justify-between bg-color-ffffff border-radius-8 padding-lr-24 border-solid-bottom-f2f3f5">
					<div className="flex align-center justify-between color-1d2129 line-height-26 font-size-18 font-weight-500">
						<Anchor
							affix={false}
							rootClassName="custom-anchor-box"
							direction="horizontal"
							replace
							targetOffset={80}
							items={[
								{
									key: 'section1',
									href: '#section1',
									title: <div className="margin-right-40 font-size-16 font-weight-500">问题内容</div>,
								},
								{
									key: 'section2',
									href: '#section2',
									title: <div className="margin-right-40 font-size-16 font-weight-500">回复内容</div>,
								},
							]}
						/>
					</div>
					<div className="flex align-center justify-between color-1d2129 line-height-26 font-size-18 font-weight-500">
						{detail.auditStatus === 1 ? (
							<>
								<Button
									type="primary"
									danger
									ghost
									onClick={() => {
										ModalFormRef.current.setOpen(true);
										ModalFormRef.current.setTitle('不通过原因');
									}}
									className="height-40 width-104"
								>
									不通过
								</Button>
								<Button type="primary" onClick={handlePass} className="margin-left-20 height-40 width-104">
									通过审核
								</Button>
							</>
						) : (
							<Button onClick={handleRevoke} className="margin-left-20 height-40 width-104">
								撤销审核
							</Button>
						)}
					</div>
				</div>
			</Affix>
			{/* Tabs & 功能按钮 结束 */}

			{/* 问题内容 开始 */}
			<div className="margin-top-20 padding-20 border-radius-8 bg-color-ffffff">
				<div className="flex align-center justify-between margin-bottom-20">
					<div className="font-size-18 line-height-26 font-weight-500">问题内容</div>
					<Button
						type="link"
						icon={<EditOutlined />}
						onClick={() => {
							linkTo(`${linkToPath}/qa/curd?&id=${id}&interactiveTopicId=${interactiveTopicId}&fromPage=1#section1`);
						}}
					>
						编辑信息
					</Button>
				</div>
				<div className="flex align-center justify-start font-size-14 line-height-22 padding-left-16 margin-bottom-20">
					<div className="color-86909c margin-right-4">发布状态：</div>
					<div className="">
						<div className={`tag-status-small-${['default', 'warning', 'error', 'primary'][detail.auditStatus]}  `}>
							{auditStatusTextList[detail.auditStatus || ''] || '--'}
						</div>
					</div>
				</div>
				{detail.auditStatus === 2 && (
					<div className="flex align-center justify-start font-size-14 line-height-22 padding-left-16 margin-bottom-20">
						<div className="color-86909c margin-right-4">拒绝原因：</div>
						<div className="">{detail.reason || '无'}</div>
					</div>
				)}
				<div className="flex justify-start font-size-14 line-height-22 padding-left-16 margin-bottom-20">
					<div className="flex-shrink color-86909c margin-right-4">问题内容：</div>
					<div className="">{detail.content || '--'}</div>
				</div>
				<div className="flex align-center justify-start font-size-14 line-height-22 padding-left-16 margin-bottom-20">
					<div className="color-86909c margin-right-4">提问来源：</div>
					<div className="">
						<div className={`tag-status-small-${['', 'warning', 'error', 'primary'][detail.releasePlatform]}  `}>
							{QAReleasePlatformTextList[detail.releasePlatform || ''] || '--'}
						</div>
					</div>
				</div>
				<div className="flex align-center justify-start font-size-14 line-height-22 padding-left-16 margin-bottom-20">
					<div className="color-86909c margin-right-4">提问用户：</div>
					<div className="flex align-center justify-start margin-right-20">
						<img
							src={
								detail.avatarUrl ||
								'https://gbac-produce.oss-cn-shenzhen.aliyuncs.com/static-assets/gbac-achv-applet/My/default-avatar.png'
							}
							className="width-20 height-20 border-radius-10 bg-color-e5e6eb"
						/>
						<div className="font-size-14 margin-left-6 line-height-20">{detail.userName || '匿名提问'}</div>
					</div>
				</div>
				<div className="flex align-center justify-start font-size-14 line-height-22 padding-left-16 margin-bottom-20">
					<div className="color-86909c margin-right-4">排序：</div>
					<div className="">{detail.rankingNum || '--'}</div>
				</div>
			</div>
			{/* 问题内容 结束 */}

			{/* 回复内容 开始 */}
			<div id="section2"></div>
			<div className="margin-top-20 padding-20 border-radius-8 bg-color-ffffff">
				<div className="flex align-center justify-between margin-bottom-20">
					<div className="font-size-18 line-height-26 font-weight-500">回复内容</div>
					<Button
						type="link"
						icon={<EditOutlined />}
						onClick={() => {
							linkTo(`${linkToPath}/qa/curd?&id=${id}&interactiveTopicId=${interactiveTopicId}&fromPage=1#section2`);
						}}
					>
						编辑信息
					</Button>
				</div>
				{(detail.replyList || []).map((ov, oi) => {
					return (
						<div key={oi}>
							<div className="flex justify-start font-size-14 line-height-22 padding-left-16 margin-bottom-20">
								<div className="flex-shrink color-86909c margin-right-4">回复内容：</div>
								<div className="">{ov.content || '--'}</div>
							</div>
							<div className="flex align-center justify-start font-size-14 line-height-22 padding-left-16 margin-bottom-20">
								<div className="color-86909c margin-right-4">回复方式：</div>
								<div className="">
									<div className={`tag-status-small-${['', 'warning', 'error', 'primary'][ov.releasePlatform]}  `}>
										{QAReleasePlatformTextList[ov.releasePlatform || ''] || '--'}
									</div>
								</div>
							</div>
						</div>
					);
				})}
				{(detail.replyList || []).length === 0 && (
					<div className="flex align-center justify-start font-size-14 line-height-22 padding-left-16 margin-bottom-20">
						<div className="color-86909c margin-right-4">回复内容：</div>
						<div className="">--</div>
					</div>
				)}
			</div>
			{/* 回复内容 结束 */}

			{/* 询问弹窗 开始 */}
			<ModalForm ref={ModalFormRef} onOk={handleAudit} FormComp={(props) => <ConrirmForm ref={props.FormCompRef} />} />
			{/* 询问弹窗 结束 */}
		</div>
	);
};

// 询问表单
const ConrirmForm = forwardRef((props = {}, ref) => {
	const [form] = Form.useForm();

	useImperativeHandle(ref, () => {
		return {
			onCancel: () => Promise.resolve(),
			onOk: () => {
				return new Promise((resolve) => {
					form.validateFields().then((values) => {
						resolve(values);
					});
				});
			},
		};
	});

	return (
		<Form form={form} labelCol={{ style: { width: '100px' } }} initialValues={{ status: 2 }}>
			<Form.Item hidden name="status">
				<Input />
			</Form.Item>
			<Form.Item label="原因" name="reason" rules={[{ required: true, message: '请输入不通过原因' }]}>
				<Input.TextArea rows={4} placeholder="请输入不通过原因" />
			</Form.Item>
		</Form>
	);
});

export default Index;
