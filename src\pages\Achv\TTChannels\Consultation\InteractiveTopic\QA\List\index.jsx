import { useEffect, useState } from 'react';
import { Table, Button, Popconfirm, Space, Affix, Form, Input, Row, Col, Select, Switch, Cascader, message } from 'antd';
import { useTableData } from '@/hook/useTableData';
import { useRouterLink } from '@/hook/useRouter';
import {
	getTTInteractiveTopicQaPage as getTablePageData,
	batchDelQa as delTableItemData,
	updateRankingNumToQa as batchUpdateSort,
	updateFeaturedStatus,
} from '@/api/Achv/TTChannels/Consultation/InteractiveTopic/QA/index';
import { getTTInteractiveTopic } from '@/api/Achv/TTChannels/Consultation/InteractiveTopic/index';

import { auditStatusData, auditStatusTextList } from '@/pages/Achv/config';

const Index = () => {
	const { linkTo, searchParams } = useRouterLink();
	const linkToPath = '/newAchv/TTChannels/consultation/interactiveTopic';

	const status = searchParams.get('auditStatus');
	const interactiveTopicId = searchParams.get('interactiveTopicId');
	const fromPage = searchParams.get('fromPage') ? 1 : '';

	const [auditStatus, setAuditStatus] = useState(status - 0 || '');

	const { form, dataSource, pagination, changePage, getTableData, delTableData, onReset, onSearch, SortInput } = useTableData({
		params: {
			auditStatus,
			interactiveTopicId,
			fromPage,
		},
		getTablePageData,
		delTableItemData,
		batchUpdateSort,
	});

	// 修改展示状态
	const featuredStatusChange = (record) => {
		updateFeaturedStatus({ id: record.id, featuredStatus: record.featuredStatus == 1 ? 0 : 1 }).then(() => {
			message.success('操作成功');
			getTableData();
		});
	};

	// 获取专题名称
	const [interactiveTopicName, setInteractiveTopicName] = useState('');
	useEffect(() => {
		if (interactiveTopicId) {
			getTTInteractiveTopic({ id: interactiveTopicId }).then((res) => {
				setInteractiveTopicName(res?.data?.name);
			});
		}
	}, [interactiveTopicId]);

	return (
		<div className="">
			<div className="flex flex-shrink align-center margin-bottom-4 height-48 font-size-22 font-weight-500 color-1d2129">
				<Space size={10}>
					<div className="a color-86909c" onClick={() => linkTo(linkToPath)}>
						互动专题管理
					</div>
					<div className="color-86909c">/</div>
					<div className="a color-86909c" onClick={() => linkTo(fromPage ? -1 : `${linkToPath}/qa`)}>
						{interactiveTopicName}
					</div>
					<div className="color-86909c">/</div>
					<div>问答管理</div>
				</Space>
			</div>
			{/* Tabs & 功能按钮 开始 */}
			<Affix offsetTop={0}>
				<div className="flex align-center justify-between padding-24 border-radius-8 bg-color-ffffff border-solid-bottom-f2f3f5">
					<div className="flex align-center">
						{auditStatusData.map((ov) => (
							<div
								key={ov.value}
								className={`a margin-right-40 font-size-18 font-weight-500 ${auditStatus === ov.value ? 'color-165dff' : ''}`}
								onClick={() => setAuditStatus(ov.value)}
							>
								{ov.label}
							</div>
						))}
					</div>
					<Space size={16}>
						<Button
							type="primary"
							onClick={() => {
								linkTo(`${linkToPath}/qa/curd?interactiveTopicId=${interactiveTopicId}&fromPage=1`);
							}}
						>
							创建问答
						</Button>
					</Space>
				</div>
			</Affix>
			{/* Tabs & 功能按钮 结束 */}
			<div className="flex-sub margin-top-18 padding-24 border-radius-8 bg-color-ffffff">
				{/* 筛选条件 开始 */}
				<div className="flex justify-between margin-bottom-18">
					<div className="flex-sub">
						<Form form={form} labelAlign="right" layout="inline">
							<Row className="width-100per" gutter={[12, 12]}>
								<Col span={8}>
									<Form.Item name="content" label="问题内容">
										<Input placeholder="请输入问题内容" />
									</Form.Item>
								</Col>
								<Col span={8}>
									<Form.Item name="replyStatus" label="回复状态">
										<Select
											placeholder="请选择回复状态"
											options={[
												{ label: '待回复', value: 0 },
												{ label: '已回复', value: 1 },
											]}
											allowClear
										/>
									</Form.Item>
								</Col>
							</Row>
						</Form>
					</div>
					<div className="margin-left-12 padding-left-12 border-solid-left-e5e6eb">
						<Space size={16}>
							<Button onClick={onReset}>重置</Button>
							<Button type="primary" onClick={onSearch}>
								查询
							</Button>
						</Space>
					</div>
				</div>
				{/* 筛选条件 结束 */}
				{/* 表格 开始 */}
				<Table rowKey="id" dataSource={dataSource} pagination={pagination} onChange={changePage} scroll={{ x: 'max-content' }}>
					<Table.Column
						title="序号"
						key="index"
						width={60}
						align="center"
						render={(_, __, index) => {
							return index + 1;
						}}
					/>
					<Table.Column title="问题内容" dataIndex="content" render={(text) => <div className="max-width-240">{text}</div>} />
					<Table.Column
						title="审核状态"
						dataIndex="auditStatus"
						align="center"
						render={(text) => {
							return (
								<div className={`tag-status-small-${['default', 'warning', 'error', 'primary'][text]}  `}>
									{auditStatusTextList[text || ''] || '--'}
								</div>
							);
						}}
					/>
					<Table.Column
						title="精选问答"
						dataIndex="featuredStatus"
						align="center"
						render={(_, record) => {
							return (
								<Switch
									checked={record.featuredStatus == 1}
									disabled={record.auditStatus !== 3}
									title={record.auditStatus !== 3 ? '仅审核通过后才能操作' : ''}
									onChange={() => {
										featuredStatusChange(record);
									}}
								/>
							);
						}}
					/>
					<Table.Column
						title="回复状态"
						dataIndex="replyStatus"
						align="center"
						render={(text) => {
							return (
								<div className={`tag-status-small-${['warning', 'primary'][text]}  `}>{['待回复', '已回复'][text || 0] || '--'}</div>
							);
						}}
					/>
					<Table.Column title="提问者" dataIndex="userName" render={(text) => text || '匿名提问'} />
					<Table.Column
						title="提问时间"
						dataIndex="createTime"
						render={(text) => {
							return (text || '').slice(0, 16);
						}}
					/>
					<Table.Column
						title="排序"
						align="center"
						dataIndex="rankingNum"
						render={(_, record) => {
							return <SortInput record={record} />;
						}}
					/>
					<Table.Column
						title="操作"
						key="option"
						dataIndex="option"
						align="center"
						fixed="right"
						width="160px"
						render={(_, record) => {
							return (
								<>
									<Button
										type="link"
										size="small"
										onClick={() =>
											linkTo(`${linkToPath}/qa/detail?interactiveTopicId=${interactiveTopicId}&id=${record.id}&fromList=1`)
										}
									>
										详情/编辑
									</Button>
									<Popconfirm
										title="提示"
										description="确定删除吗？"
										onConfirm={() => {
											delTableData(record.id);
										}}
										okText="确定"
										cancelText="取消"
									>
										<Button type="link" size="small">
											删除
										</Button>
									</Popconfirm>
								</>
							);
						}}
					/>
				</Table>
				{/* 表格 结束 */}
			</div>
		</div>
	);
};

export default Index;
