import { useState, useEffect } from 'react';
import { Button, Card, Input, Form, message, Space, Select } from 'antd';
import FormComp from '@/components/FormComp/index';

import {
	exportTTTimeLine,
	exportTtChannels,
	exportPartnerInfo,
	innovationWhitelistPhoneDelete,
	innovationWhitelistPhoneExists,
	innovationWhitelistPhoneSave,
} from '@/api/Achv/TTChannels/DataManage';

import { download } from '@/utils/common';
import dayjs from 'dayjs';

const rangePresets = [
	{
		label: '本周',
		value: [dayjs().startOf('week'), dayjs().endOf('week')],
	},
	{
		label: '上周',
		value: [dayjs().subtract(1, 'week').startOf('week'), dayjs().subtract(1, 'week').endOf('week')],
	},
	{
		label: '本月',
		value: [dayjs().startOf('month'), dayjs().endOf('month')],
	},
	{
		label: '上月',
		value: [dayjs().subtract(1, 'month').startOf('month'), dayjs().subtract(1, 'month').endOf('month')],
	},
	{
		label: '本年',
		value: [dayjs().startOf('year'), dayjs().endOf('year')],
	},
	{
		label: '上年',
		value: [dayjs().subtract(1, 'year').startOf('year'), dayjs().subtract(1, 'year').endOf('year')],
	},
];

const Index = () => {
	return (
		<div className="flex-sub flex flex-direction-column">
			<div className="flex flex-shrink align-center margin-bottom-4 height-48 font-size-22 font-weight-500 color-1d2129">运营数据管理</div>

			<Space direction="vertical" size={18}>
				<DataCard title="科转圈数据" exportFn={exportTTTimeLine} />
				<DataCard title="科转号入驻数据" exportFn={exportTtChannels} />
				<DataCard title="区域合伙人登记数据" exportFn={exportPartnerInfo} />
				<WhiteList />
			</Space>
		</div>
	);
};

const DataCard = (props = {}) => {
	const [form] = Form.useForm();

	const exportDataCallback = () => {
		form.validateFields().then((values) => {
			props.exportFn(values).then((res) => {
				if (res.size > 0) {
					download.excel(res, `${props.title}-${dayjs().format('YYYYMMDD_HH:mm')}`);
					message.success('导出成功');
				} else {
					message.error('导出失败');
				}
			});
		});
	};
	return (
		<Card
			title={props.title}
			extra={
				<Button type="primary" onClick={exportDataCallback}>
					导出
				</Button>
			}
		>
			<Form
				form={form}
				initialValues={{
					startTime: dayjs().startOf('week').format('YYYY-MM-DD'),
					endTime: dayjs().endOf('week').format('YYYY-MM-DD'),
					tempDate: [dayjs().startOf('week').format('YYYY-MM-DD'), dayjs().endOf('week').format('YYYY-MM-DD')],
				}}
			>
				<Form.Item hidden name="startTime">
					<Input placeholder="" />
				</Form.Item>
				<Form.Item hidden name="endTime">
					<Input placeholder="" />
				</Form.Item>
				<Form.Item label="" name="tempDate">
					<FormComp.DatePicker.RangePicker
						allowClear={false}
						presets={rangePresets}
						onChange={(date) => {
							form.setFieldsValue({
								startTime: date[0],
								endTime: date[1],
							});
						}}
					/>
				</Form.Item>
			</Form>
		</Card>
	);
};

const WhiteList = () => {
	const [form] = Form.useForm();

	const addWhiteList = (data) => {
		innovationWhitelistPhoneSave(data)
			.then(() => {
				message.success('添加成功');
			})
			.catch(() => {
				message.error('添加失败');
			});
	};

	const deleteWhiteList = (data) => {
		innovationWhitelistPhoneDelete(data)
			.then(() => {
				message.success('删除成功');
			})
			.catch(() => {
				message.error('删除失败');
			});
	};

	const checkWhiteList = (data) => {
		innovationWhitelistPhoneExists(data).then((res) => {
			console.log(res);
			if (res.data) {
				message.success('存在');
			} else {
				message.error('不存在');
			}
		});
	};

	const isPhone = (_, value, callback) => {
		if (/^1[123456789]\d{9}$/.test(value)) {
			return callback();
		} else {
			return callback('请输入正确的手机号码');
		}
	};

	const onSubmit = (values) => {
		form.validateFields()
			.then((postData) => {
				if (values === 1) {
					addWhiteList(postData);
				} else if (values === 2) {
					deleteWhiteList(postData);
				} else if (values === 3) {
					checkWhiteList(postData);
				}
			})
			.catch((e) => {
				console.log(e);
			});
	};
	return (
		<Card
			title="测评白名单管理"
			extra={
				<Space>
					<Button type="primary" onClick={() => onSubmit(1)}>
						添加
					</Button>
					<Button type="primary" onClick={() => onSubmit(2)}>
						删除
					</Button>
					<Button type="primary" onClick={() => onSubmit(3)}>
						查询
					</Button>
				</Space>
			}
		>
			<Form form={form} initialValues={{ type: 1 }}>
				<Form.Item label="类型" name="type" rules={[{ required: true, message: '请选择类型' }]}>
					<Select
						placeholder="请选择类型"
						options={[
							{ label: '知识产权', value: 1 },
							{ label: 'AI报告', value: 2 },
						]}
					></Select>
				</Form.Item>
				<Form.Item label="手机号码" name="phone" rules={[{ required: true, message: '请输入手机号码' }, { validator: isPhone }]}>
					<Input placeholder="请输入手机号" />
				</Form.Item>
			</Form>
		</Card>
	);
};

export default Index;
