import { useState } from 'react';
import { Button, Space, Affix } from 'antd';
import Permission from '@/components/Permission';
import { FilerForm, ListTable } from '@/pages/Achv/Demand/DemandManage/List/index';
import { useTableData } from '@/hook/useTableData';
import { useRouterLink } from '@/hook/useRouter';
import {
	pageDemand as getTablePageData,
	delDemand as delTableItemData,
	exportDemand as exportTableData,
	updateRankingNum as batchUpdateSort,
	queryDemandStatistics,
} from '@/api/Achv/Demand/DemandManage/index';
import { auditStatusData } from '@/pages/Achv/config';

const Index = () => {
	const { linkTo, searchParams } = useRouterLink();
	const status = searchParams.get('auditStatus');
	const [auditStatus, setAuditStatus] = useState(status === null ? 3 : status - 0 || '');
	const { form, dataSource, pagination, changePage, delTableData, exportData, onSearch, onReset, SortInput } = useTableData({
		params: { auditStatus, releaseType: 2 },
		getTablePageData,
		delTableItemData,
		exportTableData,
		batchUpdateSort,
		pageCallback: (paramsData = {}) => {
			getStatisticsData({ ...paramsData });
		},
	});

	// 获取统计数据
	const [statistics, setStatistics] = useState({});
	const getStatisticsData = (paramsData) => {
		delete paramsData.auditStatus;
		delete paramsData.pageSize;
		delete paramsData.pageNum;
		delete paramsData.tempArea;
		queryDemandStatistics(paramsData).then((res) => {
			const resData = res.data || {};
			setStatistics({
				...resData,
				total: Object.values(resData).reduce((pre, cur) => pre + (cur - 0), 0),
			});
		});
	};

	return (
		<div className="">
			<div className="flex flex-shrink align-center margin-bottom-4 height-48 font-size-22 font-weight-500 color-1d2129">需求管理</div>
			{/* Tabs & 功能按钮 开始 */}
			<Affix offsetTop={0}>
				<div className="flex align-center justify-between padding-24 border-radius-8 bg-color-ffffff border-solid-bottom-f2f3f5">
					<div className="flex align-center">
						{auditStatusData.map((ov) => (
							<div
								key={ov.value}
								className={`a margin-right-40 font-size-18 font-weight-500 ${auditStatus === ov.value ? 'color-165dff' : ''}`}
								onClick={() => setAuditStatus(ov.value)}
							>
								{ov.label}
								{statistics[ov.countName] > 0 ? `（${statistics[ov.countName]}）` : ''}
							</div>
						))}
					</div>
					<Space size={16}>
						<Permission hasPermi={['newAchv:TTChannels:demand:list:export']}>
							<Button onClick={exportData}>批量导出</Button>
						</Permission>
						<Button
							type="primary"
							onClick={() => {
								linkTo(`/newAchv/TTChannels/demand/curd`);
							}}
						>
							新建需求
						</Button>
					</Space>
				</div>
			</Affix>
			{/* Tabs & 功能按钮 结束 */}
			<div className="flex-sub margin-top-18 padding-24 border-radius-8 bg-color-ffffff">
				{/* 筛选条件 开始 */}
				<FilerForm hideReleaseType form={form} onReset={onReset} onSearch={onSearch} />
				{/* 筛选条件 结束 */}

				{/* 表格 开始 */}
				<ListTable
					linkToPath="/newAchv/TTChannels/demand"
					dataSource={dataSource}
					pagination={pagination}
					changePage={changePage}
					delTableData={delTableData}
				/>
				{/* 表格 结束 */}
			</div>
		</div>
	);
};

export default Index;
