@mixin proclamation-status($color) {
	display: inline-flex;
	align-items: center;
	justify-content: center;
	padding: 0 8px;
	line-height: 22px;
	font-size: 14;
	color: $color;
	&::before {
		content: '';
		margin: 0 6px 0 0;
		width: 6px;
		height: 6px;
		border-radius: 50%;
		background-color: $color;
	}
}

.proclamation-status-1 {
	@include proclamation-status(#00b42a);
}

.proclamation-status-2 {
	@include proclamation-status(#ff7d00);
}

.proclamation-status-3 {
	@include proclamation-status(#3491fa);
}
