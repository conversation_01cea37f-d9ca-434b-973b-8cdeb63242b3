import { useState } from 'react';
import { Table, Button, Popconfirm, Space, Form, Input, Row, Col, Select, Switch, Affix, message } from 'antd';

import { useTableData } from '@/hook/useTableData';
import { useRouterLink } from '@/hook/useRouter';
import {
	ttTimeLinePage as getTablePageData,
	deleteTTTimeLineList as delTableItemData,
	updateRankingNum as batchUpdateSort,
	updateShowStatus,
	updateRecommendStatus,
} from '@/api/Achv/TTChannels/Dynamic';

import { timeLineTypeData, timeLineTypeTextList, showStatusData, auditStatusData, auditStatusTextList } from '@/pages/Achv/config';
import { copeText } from '@/utils/common';

const Index = () => {
	const { searchParams } = useRouterLink();
	const status = searchParams.get('auditStatus');
	const [auditStatus, setAuditStatus] = useState(status === null ? 3 : status - 0 || '');

	const { form, dataSource, pagination, getTableData, changePage, delTableData, onSearch, onReset, SortInput } = useTableData({
		params: { auditStatus },
		getTablePageData,
		delTableItemData,
		batchUpdateSort,
		pageCallback: (paramsData = {}) => {
			getStatisticsData({ ...paramsData });
		},
	});

	// 获取统计数据
	const [statistics, setStatistics] = useState({});
	const getStatisticsData = (paramsData) => {
		Promise.all([
			getTablePageData({
				...paramsData,
				pageSize: 1,
				pageNum: 1,
				auditStatus: 3,
			}),
			getTablePageData({
				...paramsData,
				pageSize: 1,
				pageNum: 1,
				auditStatus: 1,
			}),
			getTablePageData({
				...paramsData,
				pageSize: 1,
				pageNum: 1,
				auditStatus: 2,
			}),
		]).then((resList) => {
			setStatistics({
				total: resList.reduce((sum, cur) => sum + (cur.data.total - 0), 0),
				releaseNum: resList[0].data.total,
				waitReleaseNum: resList[1].data.total,
				notPassNum: resList[2].data.total,
			});
		});
	};

	// 修改状态展示
	const showStatusChange = (record) => {
		updateShowStatus({ id: record.id, showStatus: record.showStatus == 1 ? 0 : 1 }).then(() => {
			message.success('操作成功');
			getTableData();
		});
	};

	// 修改推荐状态
	const recommendStatusChange = (record) => {
		updateRecommendStatus({ id: record.id, recommendStatus: record.recommendStatus == 1 ? 0 : 1 }).then(() => {
			message.success('操作成功');
			getTableData();
		});
	};

	return (
		<div className="">
			<div className="flex flex-shrink align-center margin-bottom-4 height-48 font-size-22 font-weight-500 color-1d2129">科转圈管理</div>
			{/* Tabs & 功能按钮 开始 */}
			<Affix offsetTop={0}>
				<div className="flex align-center justify-between padding-24 border-radius-8 bg-color-ffffff border-solid-bottom-f2f3f5">
					<div className="flex align-center">
						{auditStatusData.map((ov) => (
							<div
								key={ov.value}
								className={`a margin-right-40 font-size-18 font-weight-500 ${auditStatus === ov.value ? 'color-165dff' : ''}`}
								onClick={() => setAuditStatus(ov.value)}
							>
								{ov.label}
								{statistics[ov.countName] > 0 ? `（${statistics[ov.countName]}）` : ''}
							</div>
						))}
					</div>
				</div>
			</Affix>
			{/* Tabs & 功能按钮 结束 */}

			<div className="flex-sub margin-top-18 padding-24 border-radius-8 bg-color-ffffff">
				{/* 筛选条件 开始 */}
				<div className="flex justify-between margin-bottom-18">
					<div className="flex-sub">
						<Form form={form} labelAlign="right" layout="inline">
							<Row className="width-100per" gutter={[12, 12]}>
								{/* <Col span={8}>
									<Form.Item name="accountName" label="科转号名称">
										<Input allowClear placeholder="请输入科转号名称" />
									</Form.Item>
								</Col> */}
								{/* <Col span={8}>
									<Form.Item name="auditStatu" label="发布状态">
										<Select options={auditStatusData} allowClear placeholder="请选择发布状态" />
									</Form.Item>
								</Col> */}
								<Col span={8}>
									<Form.Item name="timeLineType" label="动态类型">
										<Select options={timeLineTypeData} allowClear placeholder="请选择动态类型" />
									</Form.Item>
								</Col>
								<Col span={8}>
									<Form.Item name="showStatus" label="显示状态">
										<Select options={showStatusData} allowClear placeholder="请选择显示状态" />
									</Form.Item>
								</Col>
								{/* <Col span={8}>
									<Form.Item name="recommendStatus" label="推荐状态">
										<Select
											options={[
												{ value: 0, label: '默认' },
												{ value: 1, label: '推荐' },
											]}
											allowClear
											placeholder="请选择推荐状态"
										/>
									</Form.Item>
								</Col> */}
							</Row>
						</Form>
					</div>
					<div className="margin-left-12 padding-left-12 border-solid-left-e5e6eb">
						<Space size={16}>
							<Button onClick={onReset}>重置</Button>
							<Button type="primary" onClick={onSearch}>
								查询
							</Button>
						</Space>
					</div>
				</div>
				{/* 筛选条件 结束 */}

				{/* 表格 开始 */}
				<Table rowKey="id" dataSource={dataSource} pagination={pagination} onChange={changePage} scroll={{ x: 'max-content' }}>
					<Table.Column
						title="序号"
						key="index"
						width={60}
						render={(_, __, index) => {
							return index + 1;
						}}
					/>

					<Table.Column title="标题" dataIndex="title" render={(_, record) => <Title data={record} />} />
					<Table.Column
						title="科转号"
						dataIndex="ttChannelsList"
						render={(ttChannelsList, { accountName }) => {
							return (
								accountName ||
								ttChannelsList.map((ov) => {
									return <div key={ov.id}>{ov.accountName}</div>;
								})
							);
						}}
					/>
					<Table.Column
						title="发布状态"
						dataIndex="auditStatus"
						align="center"
						render={(text) => {
							return (
								<div className={`tag-status-small-${['default', 'warning', 'error', 'primary'][text]}  `}>
									{auditStatusTextList[text || ''] || '--'}
								</div>
							);
						}}
					/>
					<Table.Column
						title="动态类型"
						dataIndex="timeLineType"
						align="center"
						render={(text) => {
							return (
								<div
									className={`tag-status-small-${
										['', 'f8bb35', '700eb2', 'primary', 'warning', 'success', 'error', '0aa5a8', '0aa5a8'][text]
									}`}
								>
									{timeLineTypeTextList[text] || '--'}
								</div>
							);
						}}
					/>
					<Table.Column title="发布时间" dataIndex="createTime" align="center" render={(text) => text || '--'} />
					<Table.Column
						title="显示/隐藏"
						dataIndex="showStatus"
						align="center"
						render={(_, record) => {
							return (
								<Switch
									checked={record.showStatus == 1}
									disabled={record.auditStatus !== 3}
									title={record.auditStatus !== 3 ? '仅审核通过后才能操作' : ''}
									onChange={() => {
										showStatusChange(record);
									}}
								/>
							);
						}}
					/>
					<Table.Column
						title="推荐"
						dataIndex="recommendStatus"
						align="center"
						render={(_, record) => {
							return (
								<Switch
									checked={record.recommendStatus == 1}
									disabled={record.auditStatus !== 3}
									title={record.auditStatus !== 3 ? '仅审核通过后才能操作' : ''}
									onChange={() => {
										recommendStatusChange(record);
									}}
								/>
							);
						}}
					/>
					<Table.Column
						title="排序"
						align="center"
						dataIndex="rankingNum"
						fixed="right"
						render={(_, record) => {
							return <SortInput record={record} />;
						}}
					/>
					<Table.Column
						title="操作"
						key="option"
						dataIndex="option"
						align="center"
						fixed="right"
						render={(_, record) => {
							return (
								<>
									<Button
										type="link"
										size="small"
										onClick={() => {
											copeText(record.id).then(() => {
												message.success('复制成功');
											});
										}}
									>
										复制ID
									</Button>
									<Popconfirm
										title="提示"
										description="确定删除吗？"
										onConfirm={() => {
											delTableData(record.id);
										}}
										okText="确定"
										cancelText="取消"
									>
										<Button type="link" size="small">
											删除
										</Button>
									</Popconfirm>
								</>
							);
						}}
					/>
				</Table>
			</div>
		</div>
	);
};

const Title = (props) => {
	const { linkTo } = useRouterLink();
	const { businessId, timeLineType, policyVo, wechatChannelsVo, achievementVo, demandVo, eventVo, groupEventVo, workVo } = props.data;
	const { title, name, postName } = policyVo || wechatChannelsVo || achievementVo || demandVo || eventVo || groupEventVo || workVo || {};

	const toDetail = () => {
		let path = `/newAchv/TTChannels/${
			['', 'demand', 'achievement', 'article', 'video', 'event', 'group', 'group'][timeLineType]
		}/detail?id=${businessId}`;

		if (timeLineType === 8) {
			path = `/newAchv/employment/post/detail?id=${businessId}`;
		}

		linkTo(path);
	};
	return (
		<div className="a color-165dff max-width-260" onClick={toDetail}>
			【科转圈动态】{['', '技术需求', '科技成果', '', '', '', '', '', '招聘需求'][timeLineType] || ''}：{title || name || postName}
		</div>
	);
};

export default Index;
