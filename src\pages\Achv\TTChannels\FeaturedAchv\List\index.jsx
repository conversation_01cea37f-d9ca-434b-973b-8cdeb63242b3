import { useEffect, useState } from 'react';
import { Table, Button, Space, Form, Input, Row, Col, Affix } from 'antd';
import FormComp from '@/components/FormComp/index';

import { useTableData } from '@/hook/useTableData';
import { useRouterLink } from '@/hook/useRouter';

import {
	pagePreferredAchv as getTablePageData,
	updateRankingNum as batchUpdateSort,
	achievementStatistics,
} from '@/api/Achv/TTChannels/FeaturedAchv';

const Index = () => {
	const { linkTo, searchParams } = useRouterLink();

	const [categoryValueId, setCategoryValueId] = useState(searchParams.get('categoryValueId') || null);
	const { form, dataSource, pagination, changePage, SortInput, onSearch, onReset } = useTableData({
		params: { categoryValueId: categoryValueId || undefined },
		getTablePageData,
		batchUpdateSort,
	});

	// 获取统计数据
	const [statistics, setStatistics] = useState([]);
	const getStatisticsData = () => {
		achievementStatistics().then((res) => {
			setStatistics(res.data || []);
		});
	};

	useEffect(() => {
		getStatisticsData();
	}, []);

	return (
		<div className="">
			<div className="flex flex-shrink align-center margin-bottom-4 height-48 font-size-22 font-weight-500 color-1d2129">湾创甄选</div>
			{/* Tabs & 功能按钮 开始 */}
			<Affix offsetTop={0}>
				<div className="flex align-center justify-between padding-24 border-radius-8 bg-color-ffffff border-solid-bottom-f2f3f5 overflowX-auto">
					<div className="flex align-center">
						{statistics.map((ov, oi) => (
							<div
								key={oi}
								className={`a margin-right-40 font-size-18 font-weight-500 nowrap ${
									categoryValueId === ov.categoryValueId ? 'color-165dff' : ''
								}`}
								onClick={() => setCategoryValueId(ov.categoryValueId)}
							>
								{ov.categoryValueName}
								{ov.count ? `（${ov.count}）` : ''}
							</div>
						))}
					</div>
					<Space size={16}></Space>
				</div>
			</Affix>
			{/* Tabs & 功能按钮 结束 */}
			<div className="flex-sub margin-top-18 padding-24 border-radius-8 bg-color-ffffff">
				{/* 筛选条件 开始 */}
				<div className="flex  justify-between margin-bottom-18">
					<div className="flex-sub">
						<Form
							form={form}
							labelAlign="right"
							layout="inline"
							initialValues={{
								startTime: '',
								endTime: '',
								tempDate: [],
							}}
						>
							<Form.Item hidden name="startTime">
								<Input placeholder="" />
							</Form.Item>
							<Form.Item hidden name="endTime">
								<Input placeholder="" />
							</Form.Item>
							<Row className="width-100per" gutter={[12, 12]}>
								<Col span={8}>
									<Form.Item name="achievementName" label="成果名称">
										<Input placeholder="请输入成果名称" />
									</Form.Item>
								</Col>
								<Col span={8}>
									<Form.Item label="更新日期" name="tempDate">
										<FormComp.DatePicker.RangePicker
											onChange={(date) => {
												form.setFieldsValue({
													startTime: date[0],
													endTime: date[1],
												});
											}}
										/>
									</Form.Item>
								</Col>
							</Row>
						</Form>
					</div>
					<div className="margin-left-12 padding-left-12 border-solid-left-e5e6eb">
						<Space size={16}>
							<Button onClick={onReset}>重置</Button>
							<Button type="primary" onClick={onSearch}>
								查询
							</Button>
						</Space>
					</div>
				</div>
				{/* 筛选条件 结束 */}

				{/* 表格 开始 */}
				<Table rowKey="id" dataSource={dataSource} pagination={pagination} onChange={changePage} scroll={{ x: 'max-content' }}>
					<Table.Column
						title="序号"
						key="index"
						width={60}
						render={(_, __, index) => {
							return index + 1;
						}}
					/>
					<Table.Column
						title="成果名称"
						dataIndex="name"
						render={(text) => {
							return <div className="max-width-240">{text}</div>;
						}}
					/>
					<Table.Column
						title="技术领域"
						dataIndex="areaCategoryListName"
						render={(text) => (text && text.length ? text.join('、') : '--')}
					/>
					<Table.Column title="所处阶段" dataIndex="stageIdName" />
					<Table.Column title="发布账号" dataIndex="ttChannelsList" render={(text) => (text && text[0] ? text[0].accountName : '--')} />
					<Table.Column
						title="所属区域"
						dataIndex="provinceCode"
						render={(_, record) => {
							return [record.provinceCodeName, record.cityCodeName, record.areaCodeName].filter((ov) => ov).join('-');
						}}
					/>
					<Table.Column
						title="合作意向"
						dataIndex="cooperateNum"
						align="center"
						render={(text, record) => {
							return text > 0 ? (
								<div
									className="a color-165dff"
									onClick={() => linkTo(`/newAchv/TTChannels/achievement/detail?id=${record.sourceId}#section6`)}
								>
									{text}
								</div>
							) : (
								0
							);
						}}
					/>
					<Table.Column
						title="排序"
						align="center"
						dataIndex="rankingNum"
						render={(_, record) => {
							return <SortInput record={record} />;
						}}
					/>
					<Table.Column
						title="甄选时间"
						dataIndex="createTime"
						render={(text) => {
							return (text || '--').slice(0, 16);
						}}
					/>
					<Table.Column
						title="操作"
						key="option"
						dataIndex="option"
						align="center"
						fixed="right"
						render={(_, record) => {
							return (
								<>
									<Button
										type="link"
										size="small"
										onClick={() => linkTo(`/newAchv/TTChannels/achievement/detail?id=${record.sourceId}`)}
									>
										编辑/审核
									</Button>
								</>
							);
						}}
					/>
				</Table>
				{/* 表格 结束 */}
			</div>
		</div>
	);
};

export default Index;
