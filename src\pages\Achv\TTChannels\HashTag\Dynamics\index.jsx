import { useEffect, useState } from 'react';
import { Table, Button, Space, Affix, Form, Input, Row, Col, message } from 'antd';
import { useTableData } from '@/hook/useTableData';
import { useRouterLink } from '@/hook/useRouter';

import { getTimeByHashtagId as getTablePageData, getTTHashtagAssociationTypeStatistics } from '@/api/Achv/TTChannels/HashTag/Dynamic/index';
import { getHashTag } from '@/api/Achv/TTChannels/HashTag/index';

import { timeLineTypeTextList, auditStatusTextList } from '@/pages/Achv/config';

export const businessTypeData = [
	{
		label: '全部',
		value: undefined,
		countName: 'total',
	},
	{
		label: '图文动态',
		value: 3,
		countName: 'articleNum',
	},
	{
		label: '视频动态',
		value: 4,
		countName: 'videoNum',
	},
];

const Index = () => {
	const { linkTo, searchParams } = useRouterLink();

	const fromList = !!searchParams.get('fromList');
	const hashtagId = searchParams.get('hashtagId');
	const type = searchParams.get('businessType');
	const [businessType, setBusinessType] = useState(type ? type - 0 : undefined);

	const { form, dataSource, pagination, changePage, onReset, onSearch } = useTableData({
		params: { hashtagId, businessType },
		getTablePageData,
		pageCallback: (paramsData = {}) => {
			getStatisticsData({ ...paramsData });
		},
	});

	// 获取统计数据
	const [statistics, setStatistics] = useState({});
	const getStatisticsData = (paramsData) => {
		delete paramsData.pageSize;
		delete paramsData.pageNum;
		delete paramsData.businessType;
		getTTHashtagAssociationTypeStatistics(paramsData).then((res) => {
			setStatistics(res.data || {});
		});
	};

	const linkToDetail = (record = {}) => {
		const { businessId, businessType } = record;

		if (businessId && businessType) {
			linkTo(
				`/newAchv/TTChannels/${
					['', 'demand', 'achievement', 'article', 'video', 'event', 'group', 'group'][businessType]
				}/detail?id=${businessId}`
			);
		} else {
			message.error('暂不支持该类型');
		}
	};

	const [hashtagTitle, setHashtagTitle] = useState('');

	useEffect(() => {
		getHashTag({ id: hashtagId }).then((res) => {
			setHashtagTitle(res?.data?.title || '');
		});
	}, [hashtagId]);

	return (
		<div className="">
			<div className="flex flex-shrink align-center margin-bottom-4 height-48 font-size-22 font-weight-500 color-1d2129">
				<Space size={10}>
					<div className="a color-86909c" onClick={() => linkTo(fromList ? -1 : '/newAchv/TTChannels/hashTag')}>
						话题管理
					</div>
					<div className="color-86909c">/</div>
					<div className="a color-86909c" onClick={() => linkTo(`/newAchv/TTChannels/hashTag/detail?id=${hashtagId}`)}>
						{hashtagTitle ? `#${hashtagTitle}` : '话题详情'}
					</div>
					<div className="">/</div>
					<div>关联动态</div>
				</Space>
			</div>

			{/* Tabs & 功能按钮 开始 */}
			<Affix offsetTop={0}>
				<div className="flex align-center justify-between padding-24 border-radius-8 bg-color-ffffff border-solid-bottom-f2f3f5">
					<div className="flex align-center">
						{businessTypeData.map((ov) => (
							<div
								key={ov.value}
								className={`a margin-right-40 font-size-18 font-weight-500 ${businessType === ov.value ? 'color-165dff' : ''}`}
								onClick={() => setBusinessType(ov.value)}
							>
								{ov.label}
								{statistics[ov.countName] > 0 ? `（${statistics[ov.countName]}）` : ''}
							</div>
						))}
					</div>
					<Space size={16}></Space>
				</div>
			</Affix>
			{/* Tabs & 功能按钮 结束 */}

			<div className="flex-sub margin-top-18 padding-24 border-radius-8 bg-color-ffffff">
				{/* 筛选条件 开始 */}
				{false && (
					<div className="flex justify-between margin-bottom-18">
						<div className="flex-sub">
							<Form form={form} labelAlign="right" layout="inline">
								<Row className="width-100per" gutter={[12, 12]}>
									<Col span={8}>
										<Form.Item name="title" label="话题名称">
											<Input placeholder="请输入话题名称" />
										</Form.Item>
									</Col>
								</Row>
							</Form>
						</div>
						<div className="margin-left-12 padding-left-12 border-solid-left-e5e6eb">
							<Space size={16}>
								<Button onClick={onReset}>重置</Button>
								<Button type="primary" onClick={onSearch}>
									查询
								</Button>
							</Space>
						</div>
					</div>
				)}
				{/* 筛选条件 结束 */}

				{/* 表格 开始 */}
				<Table rowKey="id" dataSource={dataSource} pagination={pagination} onChange={changePage} scroll={{ x: 'max-content' }}>
					<Table.Column
						title="序号"
						key="index"
						width={60}
						align="center"
						render={(_, __, index) => {
							return index + 1;
						}}
					/>
					<Table.Column
						title="标题"
						dataIndex="title"
						render={(_, record) => <Title data={record} onClick={() => linkToDetail(record)} />}
					/>
					<Table.Column
						title="发布状态"
						dataIndex="auditStatus"
						align="center"
						render={(text) => {
							return (
								<div className={`tag-status-small-${['default', 'warning', 'error', 'primary'][text]}  `}>
									{auditStatusTextList[text || ''] || '--'}
								</div>
							);
						}}
					/>
					<Table.Column
						title="动态类型"
						dataIndex="businessType"
						align="center"
						render={(text) => {
							return (
								<div
									className={`tag-status-small-${
										['', 'f8bb35', '700eb2', 'primary', 'warning', 'success', 'error', '0aa5a8'][text]
									}`}
								>
									{timeLineTypeTextList[text] || '--'}
								</div>
							);
						}}
					/>
					<Table.Column
						title="发布账号"
						dataIndex="ttChannelsList"
						render={(ttChannelsList) => (ttChannelsList || []).map((ov) => ov.accountName).join('、') || '--'}
					/>
					<Table.Column title="发布时间" dataIndex="createTime" align="center" render={(text) => (text || '').slice(0, 16) || '--'} />
					<Table.Column
						title="操作"
						key="option"
						dataIndex="option"
						align="center"
						fixed="right"
						width="100px"
						render={(_, record) => {
							return (
								<Button type="link" size="small" onClick={() => linkToDetail(record)}>
									详情/编辑
								</Button>
							);
						}}
					/>
				</Table>
				{/* 表格 结束 */}
			</div>
		</div>
	);
};

const Title = (props) => {
	const { linkTo } = useRouterLink();
	const { title, businessId, businessType } = props.data;
	return (
		<div className="a color-165dff max-width-260">
			【科转圈动态】{['', '技术需求', '科技成果'][businessType] || ''}：{title}
		</div>
	);
};

export default Index;
