import { useState } from 'react';
import { Table, Button, Popconfirm, Space, Affix, Form, Input, Row, Col } from 'antd';
import { useTableData } from '@/hook/useTableData';
import { useRouterLink } from '@/hook/useRouter';
import {
	pageHashTag as getTablePageData,
	batchDelHashTag as delTableItemData,
	updateRankingNum as batchUpdateSort,
	getTTHashtagAuditNum,
} from '@/api/Achv/TTChannels/HashTag/index';

import { auditStatusData, auditStatusTextList } from '@/pages/Achv/config';

const Index = () => {
	const { linkTo, searchParams } = useRouterLink();
	const linkToPath = '/newAchv/TTChannels/hashTag';

	const status = searchParams.get('auditStatus');
	const [auditStatus, setAuditStatus] = useState(status === null ? 3 : status - 0 || '');

	const { form, dataSource, pagination, changePage, delTableData, onReset, onSearch, SortInput } = useTableData({
		params: { auditStatus },
		getTablePageData,
		delTableItemData,
		batchUpdateSort,
		pageCallback: (paramsData = {}) => {
			getStatisticsData({ ...paramsData });
		},
	});

	// 获取统计数据
	const [statistics, setStatistics] = useState({});
	const getStatisticsData = (paramsData) => {
		delete paramsData.auditStatus;
		delete paramsData.pageSize;
		delete paramsData.pageNum;
		delete paramsData.tempArea;
		getTTHashtagAuditNum(paramsData).then((res) => {
			setStatistics(res.data || {});
		});
	};

	return (
		<div className="">
			<div className="flex flex-shrink align-center margin-bottom-4 height-48 font-size-22 font-weight-500 color-1d2129">话题管理</div>

			{/* Tabs & 功能按钮 开始 */}
			<Affix offsetTop={0}>
				<div className="flex align-center justify-between padding-24 border-radius-8 bg-color-ffffff border-solid-bottom-f2f3f5">
					<div className="flex align-center">
						{auditStatusData.map((ov) => (
							<div
								key={ov.value}
								className={`a margin-right-40 font-size-18 font-weight-500 ${auditStatus === ov.value ? 'color-165dff' : ''}`}
								onClick={() => setAuditStatus(ov.value)}
							>
								{ov.label}
								{statistics[ov.countName] > 0 ? `（${statistics[ov.countName]}）` : ''}
							</div>
						))}
					</div>
					<Space size={16}>
						<Button
							type="primary"
							onClick={() => {
								linkTo(`${linkToPath}/curd`);
							}}
						>
							创建话题
						</Button>
					</Space>
				</div>
			</Affix>
			{/* Tabs & 功能按钮 结束 */}

			<div className="flex-sub margin-top-18 padding-24 border-radius-8 bg-color-ffffff">
				{/* 筛选条件 开始 */}
				<div className="flex justify-between margin-bottom-18">
					<div className="flex-sub">
						<Form form={form} labelAlign="right" layout="inline">
							<Row className="width-100per" gutter={[12, 12]}>
								<Col span={8}>
									<Form.Item name="title" label="话题名称">
										<Input placeholder="请输入话题名称" />
									</Form.Item>
								</Col>
							</Row>
						</Form>
					</div>
					<div className="margin-left-12 padding-left-12 border-solid-left-e5e6eb">
						<Space size={16}>
							<Button onClick={onReset}>重置</Button>
							<Button type="primary" onClick={onSearch}>
								查询
							</Button>
						</Space>
					</div>
				</div>
				{/* 筛选条件 结束 */}

				{/* 表格 开始 */}
				<Table rowKey="id" dataSource={dataSource} pagination={pagination} onChange={changePage} scroll={{ x: 'max-content' }}>
					<Table.Column
						title="序号"
						key="index"
						width={60}
						align="center"
						render={(_, __, index) => {
							return index + 1;
						}}
					/>
					<Table.Column title="话题名称" dataIndex="title" render={(text) => <div className="max-width-400">#{text}</div>} />
					<Table.Column
						title="发布状态"
						dataIndex="auditStatus"
						align="center"
						render={(text) => {
							return (
								<div className={`tag-status-small-${['default', 'warning', 'error', 'primary'][text]}  `}>
									{auditStatusTextList[text || ''] || '--'}
								</div>
							);
						}}
					/>
					<Table.Column
						title="动态数量"
						align="center"
						dataIndex="dynamicNum"
						render={(_, record) => {
							return (
								<div
									className="cursor-pointer"
									onClick={() => {
										linkTo(`${linkToPath}/dynamics?hashtagId=${record.id}&fromList=1`);
									}}
								>
									<span className="color-165dff">{record.dynamicNum || 0}</span>
								</div>
							);
						}}
					/>
					<Table.Column
						title="总流量"
						dataIndex="totalFlow"
						align="center"
						render={(text) => {
							return text || '0';
						}}
					/>
					<Table.Column
						title="互动次数"
						dataIndex="interactionTotalNum"
						align="center"
						render={(text) => {
							return text || '0';
						}}
					/>
					<Table.Column
						title="排序"
						align="center"
						dataIndex="rankingNum"
						render={(_, record) => {
							return <SortInput record={record} />;
						}}
					/>
					<Table.Column
						title="操作"
						key="option"
						dataIndex="option"
						align="center"
						fixed="right"
						width="160px"
						render={(_, record) => {
							return (
								<>
									<Button type="link" size="small" onClick={() => linkTo(`${linkToPath}/detail?id=${record.id}&fromList=1`)}>
										详情/编辑
									</Button>
									<Popconfirm
										title="提示"
										description="确定删除吗？"
										onConfirm={() => {
											delTableData(record.id);
										}}
										okText="确定"
										cancelText="取消"
									>
										<Button type="link" size="small">
											删除
										</Button>
									</Popconfirm>
								</>
							);
						}}
					/>
				</Table>
				{/* 表格 结束 */}
			</div>
		</div>
	);
};

export default Index;
