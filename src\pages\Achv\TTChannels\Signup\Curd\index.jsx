import { useEffect, useState } from 'react';
import { useRouterLink } from '@/hook/useRouter';

import { Button, Space, Form, Input, Cascader, Radio, Checkbox, InputNumber, message, Affix, DatePicker, Card } from 'antd';
import { DeleteOutlined } from '@ant-design/icons';

import FormComp from '@/components/FormComp';
import UploadImg from '@/components/UploadImg';
import UploadVideo from '@/components/UploadVideo';
import SelectTTChannels from '@/components/Achv/SelectTTChannels';

import { getTTChannelsDetail, getTTChannelsAssociationMember, addTTChannels, updateTTChannels } from '@/api/Achv/TTChannels/Signup/index';
import { getThreeLevelData } from '@/api/common';

import { getCategoryValueList } from '@/utils/achv';
import { getDictData } from '@/utils/dictionary';

import { brokerCertificateLevelData, TTChannelsTypeTextList, TTChannelsTabsByType } from '@/pages/Achv/config';

import dayjs from 'dayjs';

const Index = () => {
	const { linkTo, searchParams } = useRouterLink();
	const [form] = Form.useForm();
	const channelType = Form.useWatch('channelType', form);
	// 1 个人 2 集体
	const channelAttr = Form.useWatch('channelAttr', form);

	const id = searchParams.get('id') || '';

	const [hash, setHash] = useState('');
	const [tabsList, setTabsList] = useState([]);

	// 联系人id收集
	const [associationContactIds, setAssociationContactIds] = useState([]);

	// 取消
	const onCancel = () => {
		linkTo(-1);
	};

	// 提交
	const onSubmit = () => {
		form.validateFields()
			.then((values) => {
				const params = { ...values };
				params.showPhotos = (values.showPhotos && values.showPhotos.join(',')) || undefined;
				params.associationMemberIds = params.associationMemberIds.map((ov) => ov.ttChannelsId);
				params.associationContactIds = params.associationContactIds.map((ov) => ov.ttChannelsId);
				params.inviteTtChannelsId = params.inviteTtChannelsIds[0]?.ttChannelsId || '';

				// 删除联系人id 对比
				params.deleteAssociationContactIds = associationContactIds.filter((ov) => !params.associationContactIds.includes(ov));

				delete params.tempArea;

				(values.id ? updateTTChannels : addTTChannels)(params).then(() => {
					message.success(values.id ? '修改成功' : '添加成功');
					setTimeout(() => {
						linkTo(-1);
					}, 500);
				});
			})
			.catch((err) => {
				console.log(err);
			});
	};

	// 获取详情
	const getDetail = () => {
		if (id) {
			Promise.all([
				getTTChannelsDetail({ id, isUpdate: 1 }),
				getTTChannelsAssociationMember({ id, personnelType: 1 }),
				getTTChannelsAssociationMember({ id, personnelType: 2 }),
			]).then((resList) => {
				const detailData = resList[0]?.data || {};
				const associationMemberIds = resList[1]?.data || [];
				const associationContactIds = resList[2]?.data || [];

				// 处理邀约人
				detailData.inviteTtChannelsIds = detailData.inviteTtChannels ? [detailData.inviteTtChannels] : [];

				// 处理区域
				detailData.tempArea = [detailData.provinceCode, detailData.cityCode];

				// 处理展示图片
				detailData.showPhotos = (detailData.showPhotos || '').split(',').filter((ov) => ov);

				form.setFieldsValue({ ...detailData, associationMemberIds, associationContactIds });

				setAssociationContactIds(associationContactIds.map((ov) => ov.ttChannelsId));

				const { tabsList, channelAttr } = TTChannelsTabsByType(detailData.channelType);
				form.setFieldsValue({
					channelAttr,
				});
				setTabsList(tabsList);
			});
		} else {
			const channelType = searchParams.get('channelType') - 0 || 1;
			const { tabsList, channelAttr } = TTChannelsTabsByType(channelType);

			form.setFieldsValue({
				channelType,
				channelAttr,
			});
			setTabsList(tabsList);
		}
	};

	useEffect(() => {
		getDetail();

		const hash = window.location.hash.replace('#', '');
		setHash(hash || 'section1');
	}, []);

	// 跳转到指定锚点
	useEffect(() => {
		if (hash) {
			setTimeout(() => {
				const element = document.getElementById(hash);
				if (element) {
					element.scrollIntoView({ behavior: 'smooth' });
				}
			}, 500);
		}
	}, [hash]);

	return (
		<div className="">
			<div className="flex flex-shrink align-center margin-bottom-4 height-48 font-size-22 font-weight-500 color-1d2129">
				<Space size={10}>
					<div className="a color-86909c" onClick={() => linkTo('/newAchv/TTChannels/signup')}>
						入驻管理
					</div>
					<div className="color-86909c">/</div>
					<div className="color-1d2129">{TTChannelsTypeTextList[channelType || 0]}编辑</div>
				</Space>
			</div>

			{/* Tabs & 功能按钮 开始 */}
			<div id="section1"></div>
			<Affix offsetTop={0}>
				<div className="flex align-center justify-between padding-24 border-radius-8 border-solid-bottom-f2f2f2 bg-color-ffffff">
					<div className="flex align-center">
						{tabsList
							.map((ov, oi) => {
								const currentHash = `section${oi + 1}`;
								return {
									el: (
										<div
											key={oi}
											className={`a margin-right-40 font-size-18 font-weight-500 ${hash === currentHash ? 'color-165dff' : ''}`}
											onClick={() => {
												if (currentHash === 'section4') {
													form.validateFields()
														.then(() => {
															setHash(`section${oi + 1}`);
														})
														.catch((ov) => {});
												} else {
													setHash(`section${oi + 1}`);
												}
											}}
										>
											{ov}
										</div>
									),
									label: ov,
								};
							})
							.filter(({ label }) => label)
							.map((ov) => ov.el)}
					</div>
					<Space size={16}>
						<Button onClick={onCancel}>取消</Button>
						<Button type="primary" onClick={onSubmit}>
							保存
						</Button>
					</Space>
				</div>
			</Affix>
			{/* Tabs & 功能按钮 结束 */}

			<Form
				form={form}
				className="antd-form-box"
				labelAlign="right"
				labelCol={{ style: { width: '180px' } }}
				initialValues={{
					id: '',
					channelType: 1,
					channelAttr: 1,
					releasePlatform: 1,
					brokerCertificateStatus: 1,
					industryIds: [],
					showPhotos: [],
					isAuth: 0,
					wechatChannelsAssociationStatus: 0,
					associationMemberIds: [],
					associationContactIds: [],
					inviteTtChannelsIds: [],
				}}
			>
				<Form.Item hidden name="id">
					<Input />
				</Form.Item>
				<Form.Item hidden name="channelType">
					<Input />
				</Form.Item>
				<Form.Item hidden name="channelAttr">
					<Input />
				</Form.Item>
				<Form.Item hidden name="releasePlatform">
					<Input />
				</Form.Item>
				<Form.Item hidden name="rankingNum">
					<Input />
				</Form.Item>

				<div style={{ display: ['section4', 'section5'].includes(hash) ? 'none' : 'block' }}>
					<div className="margin-top-20 padding-20 border-radius-8 bg-color-ffffff">
						<div className="padding-bottom-30 line-height-26 font-size-18 font-weight-500 color-1d2129">基本信息</div>
						<Form.Item
							name="name"
							label={
								['', '机构全称', '姓名', '企业全称', '姓名', '机构全称', '企业全称', '政府部门全称', '协会全称', '机构全称'][
									channelType
								]
							}
							rules={[
								{
									required: true,
									message: `请输入${
										[
											'',
											'机构全称',
											'技术经理⼈真实姓名',
											'企业全称',
											'专家真实姓名',
											'机构全称',
											'企业全称',
											'政府部门全称',
											'协会全称',
											'机构全称',
										][channelType]
									}`,
								},
							]}
						>
							<Input
								placeholder={`请输入${
									[
										'',
										'机构全称',
										'技术经理⼈真实姓名',
										'企业全称',
										'专家真实姓名',
										'机构全称',
										'企业全称',
										'政府部门全称',
										'协会全称',
										'机构全称',
									][channelType]
								}`}
							/>
						</Form.Item>
						{channelAttr === 1 && <BaseInfoForm1 form={form} channelType={channelType} />}
						{channelAttr === 2 && <BaseInfoForm2 form={form} channelType={channelType} />}
					</div>
					{channelAttr === 2 && (
						<>
							<div id="section2"></div>
							<div className="margin-top-20 padding-20 border-radius-8 bg-color-ffffff">
								<div className="padding-bottom-30 line-height-26 font-size-18 font-weight-500 color-1d2129">运营责任人信息</div>
								<LiableForm form={form} />
							</div>
						</>
					)}
					<div id="section3"></div>
					<div className="margin-top-20 padding-20 border-radius-8 bg-color-ffffff">
						<div className="padding-bottom-30 line-height-26 font-size-18 font-weight-500 color-1d2129">账号信息</div>
						<UserInfoForm form={form} />
					</div>
				</div>

				<div style={{ display: hash === 'section4' ? 'block' : 'none' }}>
					<div id="section4"></div>
					<SelectTTChannels
						form={form}
						name="associationMemberIds"
						disableIds={id ? [id] : []}
						label={channelAttr === 1 ? '关联单位' : '关联成员'}
					/>
				</div>

				<div style={{ display: hash === 'section5' ? 'block' : 'none' }}>
					<div id="section4"></div>
					<SelectTTChannels maxCount={1} form={form} name="associationContactIds" label="联络人" />
					<SelectTTChannels maxCount={1} form={form} name="inviteTtChannelsIds" label="邀约人" />
				</div>
			</Form>
		</div>
	);
};

// 基本信息(个人)
const BaseInfoForm1 = (props = {}) => {
	// 1 个人 2 集体
	const channelAttr = Form.useWatch('channelAttr', props.form);
	const brokerCertificateStatus = Form.useWatch('brokerCertificateStatus', props.form);
	const [areaOptions, setAreaOptions] = useState([]);
	// 学历：1博士 2硕士 3本科 4专科 5中专及以下
	const degreeOptions = ['博士', '硕士', '本科', '专科', '中专及以下'].map((ov, oi) => {
		return {
			label: ov,
			value: oi + 1,
			code: oi + 1,
		};
	});
	const [companyTypeOptions, setCompanyTypeOptions] = useState([]);
	const [areaCategoryListOptions, setAreaCategoryListOptions] = useState([]);

	useEffect(() => {
		getThreeLevelData({ level: 2 }).then((res) => {
			setAreaOptions(res.data || []);
		});
		getCategoryValueList('ttchannels_unit_type').then((res) => {
			setCompanyTypeOptions(res);
		});
		getCategoryValueList('ttchannels_area').then((res) => {
			setAreaCategoryListOptions(res);
		});
	}, []);
	return (
		<>
			<Form.Item label="性别" name="gender" rules={[{ required: true, message: '请选择性别' }]}>
				<Radio.Group options={getDictData('gender')} />
			</Form.Item>
			<Form.Item hidden name="provinceCode">
				<Input />
			</Form.Item>
			<Form.Item hidden name="cityCode">
				<Input />
			</Form.Item>
			<Form.Item label="常住城市" name="tempArea">
				<Cascader
					className="cascader-box"
					options={areaOptions}
					placeholder="请选择常住城市"
					displayRender={(label) => label.filter((ov) => ov).join('-')}
					onChange={(e = [undefined, undefined]) => {
						props.form.setFieldValue('provinceCode', e[0]);
						props.form.setFieldValue('cityCode', e[1]);
					}}
				/>
			</Form.Item>
			<Form.Item label="出生年月" name="birthDay">
				<FormComp.DatePicker picker="month" valueFormat="YYYY-MM" placeholder="请选择出生年月" />
			</Form.Item>
			<Form.Item label="联系电话" name="contactPhone">
				<Input className="input-box" placeholder="请输入联系电话" />
			</Form.Item>
			<Form.Item label="就职单位" name="companyName">
				<Input className="input-box" placeholder="请输入就职单位" />
			</Form.Item>
			<Form.Item label="单位类型" name="orgTypeId">
				<Radio.Group options={companyTypeOptions} />
			</Form.Item>
			<Form.Item label="工作职务" name="positionName">
				<Input className="input-box" placeholder="请输入工作职务" />
			</Form.Item>
			<Form.Item label="职称" name="professionalCertificateName">
				<Input className="input-box" placeholder="请输入职称" />
			</Form.Item>
			<Form.Item label="个人履历">
				<Form.List name="workExperiences">
					{(fields, { add, remove }) => {
						return (
							<Space direction="vertical" className="width-100per">
								<Button
									type="primary"
									onClick={() =>
										add({
											companyName: '',
											positionName: '',
											deptName: '',
											startDate: '',
											endDate: '',
										})
									}
								>
									新增履历
								</Button>
								{fields.map((field, index) => {
									return (
										<Card
											title={`履历${index + 1}`}
											key={index}
											styles={{
												body: { padding: '12px 12px 0' },
												header: { padding: '0 12px' },
											}}
											extra={
												<DeleteOutlined className="a font-size-16 hover-color-165dff" onClick={() => remove(field.name)} />
											}
										>
											<WorkExperiencesForm field={field} form={props.form} />
										</Card>
									);
								})}
							</Space>
						);
					}}
				</Form.List>
			</Form.Item>
			<Form.Item label="最高学历" name="degree">
				<Radio.Group options={degreeOptions} />
			</Form.Item>
			<Form.Item label="毕业学校" name="graduationSchool">
				<Input className="input-box" placeholder="请输入毕业学校" />
			</Form.Item>
			{/* channelType 2 经理人独有 开始 */}
			{props.channelType === 2 && (
				<>
					<Form.Item label="是否持有证书" name="brokerCertificateStatus">
						<Radio.Group
							options={[
								{ label: '是', value: 1 },
								{ label: '否', value: 0 },
							]}
						/>
					</Form.Item>
					{brokerCertificateStatus === 1 && (
						<>
							<Form.Item label="证书类型" name="brokerCertificateLevel" rules={[{ required: true, message: '请输入证书类型' }]}>
								<Radio.Group options={brokerCertificateLevelData} />
							</Form.Item>
							<Form.Item label="证件扫描件" name="brokerCertificatePhoto">
								<UploadImg size={5} width={200} height={300} />
							</Form.Item>
						</>
					)}
				</>
			)}
			<Form.Item label="擅长领域" name="industryIds" rules={[{ required: true, type: 'array', message: '请输入擅长领域' }]}>
				<Checkbox.Group options={areaCategoryListOptions} />
			</Form.Item>

			{/* channelType 2 经理人独有 开始 */}
			{props.channelType === 2 && (
				<Form.Item label="从事技术转移工作时间" name="workingExperience">
					<InputNumber className="input-number-box" min={1} precision={0} placeholder="请输入从事技术转移工作时间" suffix="年" />
				</Form.Item>
			)}
			{/* channelType 2 经理人独有 结束 */}

			{/* channelType 4 专家独有 开始 */}
			{props.channelType === 4 && (
				<>
					<Form.Item label="研究方向" name="researchDirection">
						<Input.TextArea rows={4} placeholder="请输入研究方向" />
					</Form.Item>
					<Form.Item label="代表论⽂/成果" name="achievementPaper">
						<Input.TextArea rows={4} placeholder="请输入代表论⽂/成果：" />
					</Form.Item>
				</>
			)}
			{/* channelType 4 专家独有 结束 */}
			<Form.Item label="个人简介" name="introduction">
				<Input.TextArea rows={4} placeholder="请输入个人简介" />
			</Form.Item>
			{/* channelType 4 专家独有 开始 */}
			<Form.Item label="关联⼤咖直答" name="consultationStatus" hidden={props.channelType !== 4} initialValue={0}>
				<FormComp.Switch />
			</Form.Item>
			{/* channelType 4 专家独有 结束 */}
			<Form.Item label="主要荣誉/项目" name="mainHonorsProjects">
				<Input.TextArea rows={4} placeholder={`请输入主要荣誉/项目`} />
			</Form.Item>

			<Form.Item name="idCardPhoto" label="身份证正面">
				<UploadImg size={5} width={240} height={120} tips="建议尺寸：480px*240px" />
			</Form.Item>
			<Form.Item name="idCardPhotoBack" label="身份证反面">
				<UploadImg size={5} width={240} height={120} tips="建议尺寸：480px*240px" />
			</Form.Item>
		</>
	);
};

// 基本信息(机构/企业)
const BaseInfoForm2 = (props = {}) => {
	console.log('🚀 ~ BaseInfoForm2 ~ props:', props);
	const [areaOptions, setAreaOptions] = useState([]);
	const [companyTypeOptions, setCompanyTypeOptions] = useState([]);
	const [areaCategoryListOptions, setAreaCategoryListOptions] = useState([]);
	const video = Form.useWatch('video', props.form);

	useEffect(() => {
		getThreeLevelData({ level: 2 }).then((res) => {
			setAreaOptions(res.data || []);
		});

		getCategoryValueList('ttchannels_org_type').then((res) => {
			setCompanyTypeOptions(res);
		});

		getCategoryValueList('ttchannels_area').then((res) => {
			setAreaCategoryListOptions(res);
		});
	}, []);
	return (
		<>
			<Form.Item
				label={['', '单位类型', '', '企业类型', '', '单位类型', '企业类型', '部门类型', '', '单位类型'][props.channelType]}
				name="orgTypeId"
			>
				<Radio.Group options={companyTypeOptions} />
			</Form.Item>
			<Form.Item hidden name="provinceCode">
				<Input />
			</Form.Item>
			<Form.Item hidden name="cityCode">
				<Input />
			</Form.Item>
			<Form.Item label="所在城市" name="tempArea">
				<Cascader
					className="cascader-box"
					options={areaOptions}
					placeholder="请选择所在城市 "
					displayRender={(label) => label.filter((ov) => ov).join('-')}
					onChange={(e = [undefined, undefined]) => {
						props.form.setFieldValue('provinceCode', e[0]);
						props.form.setFieldValue('cityCode', e[1]);
					}}
				/>
			</Form.Item>
			<Form.Item name="contactAddress" label="办公地址">
				<Input placeholder="请输入办公地址" />
			</Form.Item>
			<Form.Item name="contactPhone" label="办公电话">
				<Input placeholder="请输入办公电话" />
			</Form.Item>
			{!(props.channelType == 7 || props.channelType == 8 || props.channelType == 9) && (
				<Form.Item label="⾏业领域" name="industryIds" rules={[{ required: true, type: 'array', message: '请输入擅长领域' }]}>
					<Checkbox.Group options={areaCategoryListOptions} />
				</Form.Item>
			)}

			<Form.Item name="businessLicense" label="营业执照">
				<UploadImg size={5} width={240} height={120} tips="建议尺寸：480px*240px" />
			</Form.Item>
			<Form.Item
				label={['', '机构简介', '', '企业简介', '', '机构简介', '企业简介', '政府部门简介', '协会简介', '机构简介'][props.channelType]}
				name="introduction"
			>
				<Input.TextArea
					rows={4}
					placeholder={`请输入${
						['', '机构简介', '', '企业简介', '', '机构简介', '企业简介', '政府部门简介', '协会简介', '机构简介'][props.channelType]
					}`}
				/>
			</Form.Item>
			{props.channelType == 4 && (
				<Form.Item label="研究方向" name="researchDirection">
					<Input.TextArea rows={4} placeholder={`请输入研究方向`} />
				</Form.Item>
			)}
			<Form.Item label="主要荣誉/项目" name="mainHonorsProjects">
				<Input.TextArea rows={4} placeholder={`请输入主要荣誉/项目`} />
			</Form.Item>
			<Form.Item name="video" label="展示视频">
				<UploadVideo size={0.5}>
					<Button type="primary" className="margin-right-20">
						点击上传视频
					</Button>
				</UploadVideo>
			</Form.Item>
			{video && (
				<Form.Item name="videoCover" label="视频封面">
					<UploadImg size={5} width={240} height={134} tips="建议尺寸：420px*236px" cropperProps={{ width: 420, height: 236 }} />
				</Form.Item>
			)}
			<Form.Item name="showPhotos" label="展示照⽚">
				<UploadImg.MultipleUpload maxCount={9} size={5} width={240} height={120} />
			</Form.Item>
		</>
	);
};

// 运营责任人信息
const LiableForm = () => {
	return (
		<>
			<Form.Item name="operatorName" label="责任⼈姓名" rules={[{ required: true, message: '请输入责任⼈姓名' }]}>
				<Input placeholder="请输入责任⼈姓名" />
			</Form.Item>
			<Form.Item name="operatorPhone" label="联系手机">
				<Input placeholder="请输入联系手机" />
			</Form.Item>
			<Form.Item name="idCardPhoto" label="身份证正面">
				<UploadImg size={5} width={240} height={120} tips="建议尺寸：480px*240px" />
			</Form.Item>
			<Form.Item name="idCardPhotoBack" label="身份证反面">
				<UploadImg size={5} width={240} height={120} tips="建议尺寸：480px*240px" />
			</Form.Item>
			<Form.Item name="operationAuthorizationBook" label="运营授权书">
				<UploadImg size={5} width={240} height={120} tips="建议尺寸：480px*240px" />
			</Form.Item>
		</>
	);
};

// 账号信息
const UserInfoForm = (props = {}) => {
	const wechatChannelsAssociationStatus = Form.useWatch('wechatChannelsAssociationStatus', props.form);
	return (
		<>
			<Form.Item name="accountPhone" label="登录手机" rules={[{ required: true, message: '请输入登录手机' }]}>
				<Input placeholder="请输入登录手机" />
			</Form.Item>
			<Form.Item name="accountName" label="账号名称" rules={[{ required: true, message: '请输入账号名称' }]}>
				<Input placeholder="请输入账号名称" />
			</Form.Item>
			<Form.Item name="avatarUrl" label="账号头像" rules={[{ required: true, message: '请输入账号头像' }]}>
				<UploadImg size={5} width={120} height={120} tips="建议尺寸：200px*200px" cropperProps={{ width: 200, height: 200 }} />
			</Form.Item>
			<Form.Item name="homePageBackground" label="主页背景图">
				<UploadImg size={5} width={240} height={120} tips="建议尺寸：480px*240px" />
			</Form.Item>
			<Form.Item name="accountDesc" label="一句话介绍">
				<Input.TextArea rows={4} placeholder="请输入一句话介绍" />
			</Form.Item>
			<Form.Item name="isAuth" label="是否认证">
				<FormComp.Switch />
			</Form.Item>
			<Form.Item name="wechatChannelsAssociationStatus" label="关联视频号">
				<FormComp.Switch />
			</Form.Item>
			{/* {wechatChannelsAssociationStatus === 1 && <></>} */}

			<Form.Item name="wechatChannels" label="视频号名称">
				<Input placeholder="请输入视频号名称" />
			</Form.Item>
			<Form.Item name="fansNum" label="粉丝基数">
				<InputNumber className="width-160" min={0} precision={0} controls={false} placeholder="请输入粉丝基数" />
			</Form.Item>
		</>
	);
};

const WorkExperiencesForm = (props = {}) => {
	return (
		<Space direction="vertical" className="width-100per">
			<Form.Item
				labelAlign="right"
				labelCol={{ style: { width: '100px' } }}
				label="单位名称"
				name={[props.field.name, 'companyName']}
				rules={[{ required: true, message: '请输入单位名称' }]}
			>
				<Input placeholder="请输入单位名称" className="input-box" />
			</Form.Item>
			<Form.Item
				labelAlign="right"
				labelCol={{ style: { width: '100px' } }}
				label="职位"
				name={[props.field.name, 'positionName']}
				rules={[{ required: true, message: '请输入职位' }]}
			>
				<Input placeholder="请输入职位" className="input-box" />
			</Form.Item>
			<Form.Item labelAlign="right" labelCol={{ style: { width: '100px' } }} label="所在部门" name={[props.field.name, 'deptName']}>
				<Input placeholder="请输入所在部门" className="input-box" />
			</Form.Item>
			<Form.Item
				labelAlign="right"
				labelCol={{ style: { width: '100px' } }}
				label="在职时间"
				name={[props.field.name]}
				rules={[{ required: true, message: '请输入在职时间' }]}
			>
				<DatePickerComp index={props.field.name} form={props.form} />
			</Form.Item>
		</Space>
	);
};

const DatePickerComp = (props = {}) => {
	const [tempDate, setTempDate] = useState([null, null]);
	useEffect(() => {
		setTempDate(dayjs(new Date()), dayjs(new Date()));
		const startDate = (props.value && props.value.startDate) || null;
		const endDate = (props.value && props.value.endDate) || null;
		setTempDate([startDate && startDate !== '至今' ? dayjs(startDate) : null, endDate && endDate !== '至今' ? dayjs(endDate) : null]);
	}, [props.value]);
	return (
		<DatePicker.RangePicker
			value={tempDate}
			placeholder={['开始时间', '至今']}
			allowEmpty={[false, true]}
			format="YYYY-MM"
			picker="month"
			onChange={(value) => {
				const workExperiences = props.form.getFieldValue('workExperiences');
				const currentData = workExperiences[props.index] || {};

				currentData.startDate = value[0] ? dayjs(value[0]).format('YYYY-MM') : null;
				currentData.endDate = value[1] ? dayjs(value[1]).format('YYYY-MM') : '至今';

				if (!workExperiences[props.index]) {
					workExperiences[props.index] = currentData;
				}
				props.form.setFieldsValue({ workExperiences });
			}}
		/>
	);
};

export default Index;
