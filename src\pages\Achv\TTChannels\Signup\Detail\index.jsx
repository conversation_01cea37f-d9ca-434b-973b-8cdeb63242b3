import { useEffect, useState, useRef, forwardRef, useImperativeHandle } from 'react';
import { Button, Input, Affix, Anchor, message, Form, Modal, Row, Col, Space, Image } from 'antd';
import { EditOutlined } from '@ant-design/icons';

import ModalForm from '@/components/ModalForm';

import { getTTChannelsDetail, auditTTChannels, getTTChannelsAssociationMember } from '@/api/Achv/TTChannels/Signup/index';

import { useRouterLink } from '@/hook/useRouter';
import {
	authTextList,
	TTChannelsTypeTextList,
	TTChannelsTabsByType,
	releasePlatformTextList,
	brokerCertificateLevelTextList,
} from '@/pages/Achv/config';

const Index = () => {
	const ModalFormRef = useRef();
	const { linkTo, searchParams } = useRouterLink();

	const id = searchParams.get('id') || '';
	const fromList = !!searchParams.get('fromList');

	const [tabsList, setTabsList] = useState([]);
	const [detail, setDetail] = useState({});

	// 获取详情
	const getDetail = () => {
		Promise.all([
			getTTChannelsDetail({ id, isUpdate: 1 }),
			getTTChannelsAssociationMember({ id, personnelType: 1 }),
			getTTChannelsAssociationMember({ id, personnelType: 2 }),
		]).then((resList) => {
			const detailData = resList[0]?.data || {};
			const associationMemberIds = resList[1]?.data || [];
			const associationContactIds = resList[2]?.data || [];

			setDetail({ ...detailData, associationMemberIds, associationContactIds });

			const { tabsList } = TTChannelsTabsByType(detailData.channelType);
			setTabsList(tabsList);
		});
	};

	// 通过审核
	const handlePass = () => {
		Modal.confirm({
			title: '科转号审核',
			content: `是否通过【${detail.name}】的审核？`,
			onOk() {
				handleAudit({ status: 3 });
			},
		});
	};

	// 撤销审核
	const handleRevoke = () => {
		Modal.confirm({
			title: '撤销审核',
			content: `是否撤销${TTChannelsTypeTextList[detail.channelType]}：【${detail.name}】的审核结果？`,
			onOk() {
				handleAudit({ status: 1 });
			},
		});
	};

	// 审核操作
	const handleAudit = (values) => {
		auditTTChannels({ ...values, id }).then(() => {
			message.success('操作成功');

			setTimeout(() => {
				linkTo(-1);
			}, 500);
		});
	};

	useEffect(() => {
		getDetail();

		// 跳转到指定锚点
		const hash = window.location.hash.replace('#', '');
		if (hash) {
			setTimeout(() => {
				const element = document.getElementById(hash);
				if (element) {
					element.scrollIntoView({ behavior: 'smooth' });
				}
			}, 500);
		}
	}, []);

	return (
		<div>
			<div className="flex flex-shrink align-center margin-bottom-4 height-48 font-size-22 font-weight-500">
				<div className="a color-86909c" onClick={() => linkTo(fromList ? -1 : '/newAchv/TTChannels/signup')}>
					入驻管理
				</div>
				<div className="margin-lr-10 color-86909c">/</div>
				<div className="color-1d2129">{TTChannelsTypeTextList[detail.channelType || 0]}详情</div>
			</div>

			<div id="section1"></div>
			<Affix>
				<div className="height-66 flex align-center justify-between bg-color-ffffff border-radius-8 padding-lr-24 border-solid-bottom-f2f3f5">
					<div className="flex align-center justify-between color-1d2129 line-height-26 font-size-18 font-weight-500">
						<Anchor
							affix={false}
							rootClassName="anchor-header-tabBar-box"
							direction="horizontal"
							replace
							targetOffset={80}
							items={tabsList
								.map((ov, oi) => {
									return {
										key: `section${oi + 1}`,
										href: `#section${oi + 1}`,
										title: <div className="margin-right-40 font-size-16 font-weight-500">{ov}</div>,
										label: ov,
									};
								})
								.filter(({ label }) => label)}
						/>
					</div>
					<div className="flex align-center justify-between color-1d2129 line-height-26 font-size-18 font-weight-500">
						{detail.auditStatus === 1 ? (
							<>
								<Button
									type="primary"
									danger
									ghost
									onClick={() => {
										ModalFormRef.current.setOpen(true);
										ModalFormRef.current.setTitle('不通过原因');
									}}
									className="height-40 width-104"
								>
									不通过
								</Button>
								<Button type="primary" onClick={handlePass} className="margin-left-20 height-40 width-104">
									通过审核
								</Button>
							</>
						) : (
							<Button onClick={handleRevoke} className="margin-left-20 height-40 width-104">
								撤销审核
							</Button>
						)}
					</div>
				</div>
			</Affix>

			<div className="bg-color-ffffff border-radius-8 padding-lr-24 padding-tb-20 margin-tb-16">
				<div className="flex align-center justify-between margin-bottom-20">
					<div className="font-size-18 line-height-26 font-weight-500">基本信息</div>
					<Button
						type="link"
						icon={<EditOutlined />}
						onClick={() => {
							linkTo(`/newAchv/TTChannels/signup/curd?id=${id}`);
						}}
					>
						编辑信息
					</Button>
				</div>
				<div className="flex align-center justify-start font-size-14 line-height-22 padding-left-16 margin-bottom-20">
					<div className="color-86909c margin-right-4">审核状态：</div>
					<div className="">
						<div className={`tag-status-${['default', 'warning', 'error', 'primary'][detail.auditStatus]}`}>
							{['', '审核中', '审核不通过', '审核通过'][detail.auditStatus] || ''}
						</div>
					</div>
				</div>
				{detail.auditStatus === 2 && (
					<div className="flex align-center justify-start font-size-14 line-height-22 padding-left-16 margin-bottom-20">
						<div className="color-86909c margin-right-4">拒绝原因：</div>
						<div className="">{detail.reason || '无'}</div>
					</div>
				)}
				<div className="flex align-center justify-start font-size-14 line-height-22 padding-left-16 margin-bottom-20">
					<div className="color-86909c margin-right-4">
						{
							['', '机构全称', '姓名', '企业全称', '姓名', '机构全称', '企业全称', '政府部门全称', '协会全称', '机构全称'][
								detail.channelType
							]
						}
						：
					</div>
					<div className="">{detail.name || '--'}</div>
				</div>

				{/* 个人 开始 */}
				{detail.channelAttr === 1 && (
					<>
						<div className="flex align-center justify-start font-size-14 line-height-22 padding-left-16 margin-bottom-20">
							<div className="color-86909c margin-right-4">性别：</div>
							<div className="">{['--', '男', '女'][detail.gender || 0]}</div>
						</div>
						<div className="flex align-center justify-start font-size-14 line-height-22 padding-left-16 margin-bottom-20">
							<div className="color-86909c margin-right-4">常住城市：</div>
							<div className="">{[detail.provinceName, detail.cityName, detail.areaName].filter((ov) => ov).join('-') || '--'}</div>
						</div>
						<div className="flex align-center justify-start font-size-14 line-height-22 padding-left-16 margin-bottom-20">
							<div className="color-86909c margin-right-4">出生年月：</div>
							<div className="">{detail.birthDay || '--'}</div>
						</div>
						<div className="flex align-center justify-start font-size-14 line-height-22 padding-left-16 margin-bottom-20">
							<div className="color-86909c margin-right-4">联系电话：</div>
							<div className="">{detail.contactPhone || '--'}</div>
						</div>
						<div className="flex align-center justify-start font-size-14 line-height-22 padding-left-16 margin-bottom-20">
							<div className="color-86909c margin-right-4">就职单位：</div>
							<div className="">{detail.companyName || '--'}</div>
						</div>
						<div className="flex align-center justify-start font-size-14 line-height-22 padding-left-16 margin-bottom-20">
							<div className="color-86909c margin-right-4">单位类型：</div>
							<div className="">{detail.orgTypeName || '--'}</div>
						</div>
						<div className="flex align-center justify-start font-size-14 line-height-22 padding-left-16 margin-bottom-20">
							<div className="color-86909c margin-right-4">工作职务：</div>
							<div className="">{detail.positionName || '--'}</div>
						</div>
						<div className="flex align-center justify-start font-size-14 line-height-22 padding-left-16 margin-bottom-20">
							<div className="color-86909c margin-right-4">职称：</div>
							<div className="">{detail.professionalCertificateName || '--'}</div>
						</div>
						<div className="flex justify-start font-size-14 line-height-22 padding-left-16 margin-bottom-20">
							<div className="color-86909c margin-right-4">个人履历：</div>
							<div className="flex-sub">
								{detail.workExperiencesList ? <ExperiencesList data={detail.workExperiencesList} /> : '--'}
							</div>
						</div>
						<div className="flex align-center justify-start font-size-14 line-height-22 padding-left-16 margin-bottom-20">
							<div className="color-86909c margin-right-4">最高学历：</div>
							<div className="">{['', '博士', '硕士', '本科', '专科', '中专及以下'][detail.degree] || ''}</div>
						</div>
						<div className="flex align-center justify-start font-size-14 line-height-22 padding-left-16 margin-bottom-20">
							<div className="color-86909c margin-right-4">毕业学校：</div>
							<div className="">{detail.graduationSchool || '--'}</div>
						</div>
						{/* channelType 2 经理人独有 开始 */}
						{detail.channelType === 2 && (
							<>
								<div className="flex align-center justify-start font-size-14 line-height-22 padding-left-16 margin-bottom-20">
									<div className="color-86909c margin-right-4">是否持有技术经理（经纪）人证书：</div>
									<div className="">{detail.brokerCertificateStatus === 1 ? '是' : '否'}</div>
								</div>
								{detail.brokerCertificateStatus === 1 && (
									<>
										<div className="flex align-center justify-start font-size-14 line-height-22 padding-left-16 margin-bottom-20">
											<div className="color-86909c margin-right-4">证书类型：</div>
											<div className="">{brokerCertificateLevelTextList[detail.brokerCertificateLevel] || '--'}</div>
										</div>

										<div className="flex justify-start font-size-14 line-height-22 padding-left-16 margin-bottom-20">
											<div className="color-86909c margin-right-4">证件扫描件：</div>
											<div className="">
												{(detail.brokerCertificatePhoto && <Image width={120} src={detail.brokerCertificatePhoto} />) || null}
											</div>
										</div>
									</>
								)}
							</>
						)}
						{/* channelType 2 经理人独有 结束 */}
						<div className="flex align-center justify-start font-size-14 line-height-22 padding-left-16 margin-bottom-20">
							<div className="color-86909c margin-right-4">擅长领域：</div>
							<div className="">{(detail.industryNames || []).join('、') || '--'}</div>
						</div>
						{/* channelType 2 经理人独有 开始 */}
						{detail.channelType === 2 && (
							<>
								<div className="flex align-center justify-start font-size-14 line-height-22 padding-left-16 margin-bottom-20">
									<div className="color-86909c margin-right-4">从事技术转移工作时间（单位: 年）：</div>
									<div className="">{detail.workingExperience || '--'}</div>
								</div>
							</>
						)}
						{/* channelType 2 经理人独有 结束 */}
						{/* channelType 4 专家独有 开始 */}
						{detail.channelType === 4 && (
							<>
								<div className="flex justify-start font-size-14 line-height-22 padding-left-16 margin-bottom-20">
									<div className="color-86909c margin-right-4">研究方向：</div>
									<div className="flex-sub">{detail.researchDirection || '--'}</div>
								</div>
								<div className="flex justify-start font-size-14 line-height-22 padding-left-16 margin-bottom-20">
									<div className="color-86909c margin-right-4">代表论⽂/成果：</div>
									<div className="flex-sub">{detail.achievementPaper || '--'}</div>
								</div>
							</>
						)}
						{/* channelType 4 专家独有 结束 */}
						<div className="flex justify-start font-size-14 line-height-22 padding-left-16 margin-bottom-20">
							<div className="color-86909c margin-right-4">个人简介：</div>
							<div className="flex-sub pre-line">{detail.introduction || '--'}</div>
						</div>
						{/* channelType 4 专家独有 开始 */}
						<div className="flex justify-start font-size-14 line-height-22 padding-left-16 margin-bottom-20">
							<div className="color-86909c margin-right-4">关联⼤咖直答：</div>
							<div className="flex-sub pre-line">{detail.consultationStatus === 1 ? '有' : '无'}</div>
						</div>
						{/* channelType 4 专家独有 结束 */}
						<div className="flex justify-start font-size-14 line-height-22 padding-left-16 margin-bottom-20">
							<div className="color-86909c margin-right-4">主要荣誉/项目：</div>
							<div className="flex-sub pre-line">{detail.mainHonorsProjects || '--'}</div>
						</div>
						<div className="flex align-start justify-start font-size-14 line-height-22 padding-left-16 margin-bottom-20">
							<div className="color-86909c margin-right-4">身份证正面：</div>
							<div className="">{(detail.idCardPhoto && <Image width={120} src={detail.idCardPhoto} />) || '--'}</div>
						</div>
						<div className="flex align-start justify-start font-size-14 line-height-22 padding-left-16 margin-bottom-20">
							<div className="color-86909c margin-right-4">身份证反面：</div>
							<div className="">{(detail.idCardPhotoBack && <Image width={120} src={detail.idCardPhotoBack} />) || '--'}</div>
						</div>
					</>
				)}
				{/* 个人 结束 */}

				{/* 集体 开始 */}
				{detail.channelAttr === 2 && (
					<>
						{['', '单位类型', '', '企业类型', '', '单位类型', '企业类型', '部门类型', '单位类型'][detail.channelType] && (
							<div className="flex align-center justify-start font-size-14 line-height-22 padding-left-16 margin-bottom-20">
								<div className="color-86909c margin-right-4">
									{['', '单位类型', '', '企业类型', '', '单位类型', '企业类型', '部门类型', '单位类型'][detail.channelType]}：
								</div>
								<div className="">{detail.orgTypeName || '--'}</div>
							</div>
						)}
						<div className="flex align-center justify-start font-size-14 line-height-22 padding-left-16 margin-bottom-20">
							<div className="color-86909c margin-right-4">所在城市：</div>
							<div className="">{[detail.provinceName, detail.cityName, detail.areaName].filter((ov) => ov).join('-') || '--'}</div>
						</div>
						<div className="flex align-center justify-start font-size-14 line-height-22 padding-left-16 margin-bottom-20">
							<div className="color-86909c margin-right-4">办公地址：</div>
							<div className="">{detail.contactAddress || '--'}</div>
						</div>
						<div className="flex align-center justify-start font-size-14 line-height-22 padding-left-16 margin-bottom-20">
							<div className="color-86909c margin-right-4">办公电话：</div>
							<div className="">{detail.contactPhone || '--'}</div>
						</div>
						{!(detail.channelType == 7 || detail.channelType == 8 || detail.channelType == 9) && (
							<div className="flex align-center justify-start font-size-14 line-height-22 padding-left-16 margin-bottom-20">
								<div className="color-86909c margin-right-4">⾏业领域：</div>
								<div className="">{(detail.industryNames || []).join('、') || '--'}</div>
							</div>
						)}
						<div className="flex align-start justify-start font-size-14 line-height-22 padding-left-16 margin-bottom-20">
							<div className="color-86909c margin-right-4">营业执照：</div>
							<div className="">{(detail.businessLicense && <Image width={120} src={detail.businessLicense} />) || '--'}</div>
						</div>
						<div className="flex justify-start font-size-14 line-height-22 padding-left-16 margin-bottom-20">
							<div className="color-86909c margin-right-4">
								{
									['', '机构简介', '', '企业简介', '', '机构简介', '企业简介', '政府部门简介', '协会简介', '机构简介'][
										detail.channelType
									]
								}
								：
							</div>
							<div className="flex-sub pre-line">{detail.introduction || '--'}</div>
						</div>
						{detail.channelType == 4 && (
							<div className="flex justify-start font-size-14 line-height-22 padding-left-16 margin-bottom-20">
								<div className="color-86909c margin-right-4">研究方向：</div>
								<div className="flex-sub pre-line">{detail.researchDirection || '--'}</div>
							</div>
						)}
						<div className="flex justify-start font-size-14 line-height-22 padding-left-16 margin-bottom-20">
							<div className="color-86909c margin-right-4">主要荣誉/项目：</div>
							<div className="flex-sub pre-line">{detail.mainHonorsProjects || '--'}</div>
						</div>
						<div className="flex align-start justify-start font-size-14 line-height-22 padding-left-16 margin-bottom-20">
							<div className="color-86909c margin-right-4">展示视频：</div>
							<div className="flex-sub">{detail.video || '--'}</div>
						</div>
						{detail.video && (
							<div className="flex align-start justify-start font-size-14 line-height-22 padding-left-16 margin-bottom-20">
								<div className="color-86909c margin-right-4">视频封面：</div>
								<div className="flex-sub">
									<Image width={240} src={detail.videoCover} />;
								</div>
							</div>
						)}
						<div className="flex align-start justify-start font-size-14 line-height-22 padding-left-16 margin-bottom-20">
							<div className="color-86909c margin-right-4">展示照⽚：</div>
							<div className="flex-sub">
								<Space wrap>
									{(detail.showPhotos &&
										detail.showPhotos.split(',').map((ov, oi) => {
											return <Image key={oi} width={240} height={240} src={ov} />;
										})) ||
										'--'}
								</Space>
							</div>
						</div>
					</>
				)}
				{/* 集体 结束 */}

				<div className="flex align-center justify-start font-size-14 line-height-22 padding-left-16 margin-bottom-20">
					<div className="color-86909c margin-right-4">发布平台：</div>
					<div className="">
						<div className={`tag-status-${['', 'primary', 'success', '700eb2'][detail.releasePlatform]}`}>
							{releasePlatformTextList[detail.releasePlatform] || '--'}
						</div>
					</div>
				</div>

				{detail.inviteBrokerName && (
					<div className="flex align-center justify-start font-size-14 line-height-22 padding-left-16 margin-bottom-20">
						<div className="color-86909c margin-right-4">入驻邀请人：</div>
						<div className="">{detail.inviteBrokerName}</div>
					</div>
				)}
				<div className="flex align-center justify-start font-size-14 line-height-22 padding-left-16 margin-bottom-20">
					<div className="color-86909c margin-right-4">创建时间：</div>
					<div className="">{(detail.createTime || '--').slice(0, 16)}</div>
				</div>
				<div className="flex align-center justify-start font-size-14 line-height-22 padding-left-16 margin-bottom-20">
					<div className="color-86909c margin-right-4">最后修改时间：</div>
					<div className="">{(detail.updateTime || '--').slice(0, 16)}</div>
				</div>
			</div>

			{detail.channelAttr === 2 && (
				<>
					<div id="section2"></div>
					<div className="bg-color-ffffff border-radius-8 padding-lr-24 padding-tb-20 margin-tb-16">
						<div className="flex align-center justify-between margin-bottom-20">
							<div className="font-size-18 line-height-26 font-weight-500">运营责任人信息</div>
							<Button
								type="link"
								icon={<EditOutlined />}
								onClick={() => {
									linkTo(`/newAchv/TTChannels/signup/curd?id=${id}#section2`);
								}}
							>
								编辑信息
							</Button>
						</div>

						<div className="flex align-center justify-start font-size-14 line-height-22 padding-left-16 margin-bottom-20">
							<div className="color-86909c margin-right-4">责任⼈姓名：</div>
							<div className="">{detail.operatorName || '--'}</div>
						</div>
						<div className="flex align-center justify-start font-size-14 line-height-22 padding-left-16 margin-bottom-20">
							<div className="color-86909c margin-right-4">联系手机：</div>
							<div className="">{detail.operatorPhone || '--'}</div>
						</div>
						<div className="flex align-start justify-start font-size-14 line-height-22 padding-left-16 margin-bottom-20">
							<div className="color-86909c margin-right-4">身份证正面：</div>
							<div className="">{(detail.idCardPhoto && <Image width={120} src={detail.idCardPhoto} />) || '--'}</div>
						</div>
						<div className="flex align-start justify-start font-size-14 line-height-22 padding-left-16 margin-bottom-20">
							<div className="color-86909c margin-right-4">身份证反面：</div>
							<div className="">{(detail.idCardPhotoBack && <Image width={120} src={detail.idCardPhotoBack} />) || '--'}</div>
						</div>
						<div className="flex align-start justify-start font-size-14 line-height-22 padding-left-16 margin-bottom-20">
							<div className="color-86909c margin-right-4">运营授权书：</div>
							<div className="">
								{(detail.operationAuthorizationBook && <Image width={120} src={detail.operationAuthorizationBook} />) || '--'}
							</div>
						</div>
					</div>
				</>
			)}

			<div id="section3"></div>
			<div className="bg-color-ffffff border-radius-8 padding-lr-24 padding-tb-20 margin-tb-16">
				<div className="flex align-center justify-between margin-bottom-20">
					<div className="font-size-18 line-height-26 font-weight-500">账号信息</div>
					<Button
						type="link"
						icon={<EditOutlined />}
						onClick={() => {
							linkTo(`/newAchv/TTChannels/signup/curd?id=${id}#section3`);
						}}
					>
						编辑信息
					</Button>
				</div>
				<div className="flex align-center justify-start font-size-14 line-height-22 padding-left-16 margin-bottom-20">
					<div className="color-86909c margin-right-4">登录手机：</div>
					<div className="flex-sub">{detail.accountPhone || '--'}</div>
				</div>
				<div className="flex align-center justify-start font-size-14 line-height-22 padding-left-16 margin-bottom-20">
					<div className="color-86909c margin-right-4">账号名称：</div>
					<div className="flex-sub">{detail.accountName || '--'}</div>
				</div>
				<div className="flex align-start justify-start font-size-14 line-height-22 padding-left-16 margin-bottom-20">
					<div className="color-86909c margin-right-4">账号头像：</div>
					<div className="flex-sub">{(detail.avatarUrl && <Image width={120} src={detail.avatarUrl} />) || '--'}</div>
				</div>
				<div className="flex align-start justify-start font-size-14 line-height-22 padding-left-16 margin-bottom-20">
					<div className="color-86909c margin-right-4">主页背景图：</div>
					<div className="flex-sub">{(detail.homePageBackground && <Image width={120} src={detail.homePageBackground} />) || '--'}</div>
				</div>
				<div className="flex justify-start font-size-14 line-height-22 padding-left-16 margin-bottom-20">
					<div className="color-86909c margin-right-4">一句话介绍：</div>
					<div className="flex-sub">{detail.accountDesc || '--'}</div>
				</div>
				<div className="flex align-center justify-start font-size-14 line-height-22 padding-left-16 margin-bottom-20">
					<div className="color-86909c margin-right-4">是否认证：</div>
					<div className="flex align-center justify-start">
						{detail.isAuth == 1 && (
							<img
								src="https://gbac-produce.oss-cn-shenzhen.aliyuncs.com/static-assets/gbac-achv-applet/Home/icon-renzheng.png"
								className="width-20 height-20"
							/>
						)}

						<div className="margin-left-4">{authTextList[detail.isAuth || 0]}</div>
					</div>
				</div>
				<div className="flex align-center justify-start font-size-14 line-height-22 padding-left-16 margin-bottom-20">
					<div className="color-86909c margin-right-4">关联视频号：</div>
					<div className="flex-sub">{detail.wechatChannelsAssociationStatus === 1 ? '已关联' : '未关联'}</div>
				</div>
				{/* 关联就展示 不关联隐藏 */}
				{true && (
					<>
						<div className="flex align-center justify-start font-size-14 line-height-22 padding-left-16 margin-bottom-20">
							<div className="color-86909c margin-right-4">视频号名称：</div>
							<div className="flex-sub">{detail.wechatChannels || '--'}</div>
						</div>
						<div className="flex align-center justify-start font-size-14 line-height-22 padding-left-16 margin-bottom-20">
							<div className="color-86909c margin-right-4">粉丝基数：</div>
							<div className="flex-sub">{detail.fansNum !== undefined && detail.fansNum !== null ? detail.fansNum || 0 : '--'}</div>
						</div>
					</>
				)}
			</div>

			<div id="section4"></div>
			<div className="bg-color-ffffff border-radius-8 padding-lr-24 padding-tb-20 margin-tb-16">
				<div className="flex align-center justify-between margin-bottom-20">
					<div className="font-size-18 line-height-26 font-weight-500">{detail.channelAttr === 1 ? '关联单位' : '关联成员'}</div>
					<Button
						type="link"
						icon={<EditOutlined />}
						onClick={() => {
							linkTo(`/newAchv/TTChannels/signup/curd?id=${id}#section4`);
						}}
					>
						编辑参与人
					</Button>
				</div>
				<Row gutter={[16, 16]}>
					{(detail.associationMemberIds || []).map((ov, oi) => {
						return (
							<Col span={8} key={oi}>
								<div key={oi} className="flex align-center padding-16 border-solid-e3f4fc">
									<img src={ov.avatarUrl} className="width-40 height-40 border-radius-40" />
									<div className="flex-sub flex flex-direction-column justify-center margin-left-12 height-40">
										<div className="flex align-center">
											<div className="font-size-14 color-165dff">{ov.name || ''}</div>
											{ov.isAuth === 1 && (
												<img
													src="https://gbac-produce.oss-cn-shenzhen.aliyuncs.com/static-assets/gbac-achv-applet/Home/icon-renzheng.png"
													className="margin-left-4 width-20 height-20"
												/>
											)}
										</div>
										{(ov.companyName || ov.positionName) && (
											<Space className="flex-sub flex align-end font-size-14 color-86909c">
												<div className="text-cut">{ov.companyName}</div>
												<div className="text-cut">{ov.positionName}</div>
											</Space>
										)}
									</div>
								</div>
							</Col>
						);
					})}
				</Row>
			</div>

			<div id="section5"></div>
			<div className="bg-color-ffffff border-radius-8 padding-lr-24 padding-tb-20 margin-tb-16">
				<div className="flex align-center justify-between margin-bottom-20">
					<div className="font-size-18 line-height-26 font-weight-500">相关人员</div>
					<Button
						type="link"
						icon={<EditOutlined />}
						onClick={() => {
							linkTo(`/newAchv/TTChannels/signup/curd?id=${id}#section5`);
						}}
					>
						编辑参与人
					</Button>
				</div>

				<div className="flex align-center justify-start font-size-14 line-height-22 padding-left-16 margin-bottom-20">
					<div className="margin-right-24 width-90 text-align-right color-86909c">联络人：</div>
					<div className="flex align-start justify-start flex-wrap">
						{(detail.associationContactIds || []).map((item, index) => {
							return (
								<div key={index} className="flex align-center justify-start margin-right-20">
									<img src={item.avatarUrl} className="width-20 height-20 border-radius-10 bg-color-e5e6eb" />
									<div className="font-size-14 color-165dff margin-left-6 line-height-20">{item.accountName || ''}</div>
								</div>
							);
						})}
						{(detail.associationContactIds || []).length === 0 && '--'}
					</div>
				</div>

				{detail.inviteTtChannels && (
					<div className="flex align-center justify-start font-size-14 line-height-22 padding-left-16 margin-bottom-20">
						<div className="margin-right-24 width-90 text-align-right color-86909c">邀约人：</div>
						<div className="flex align-start justify-start flex-wrap">
							<div key={detail.inviteTtChannels.id} className="flex align-center justify-start margin-right-20">
								<img src={detail.inviteTtChannels.avatarUrl} className="width-20 height-20 border-radius-10 bg-color-e5e6eb" />
								<div className="font-size-14 color-165dff margin-left-6 line-height-20">
									{detail.inviteTtChannels.accountName || ''}
								</div>
							</div>
						</div>
					</div>
				)}
			</div>

			{/* 询问弹窗 开始 */}
			<ModalForm ref={ModalFormRef} onOk={handleAudit} FormComp={(props) => <ConrirmForm ref={props.FormCompRef} />} />
			{/* 询问弹窗 结束 */}
		</div>
	);
};

// 询问表单
const ConrirmForm = forwardRef((props = {}, ref) => {
	const [form] = Form.useForm();

	useImperativeHandle(ref, () => {
		return {
			onCancel: () => Promise.resolve(),
			onOk: () => {
				return new Promise((resolve) => {
					form.validateFields().then((values) => {
						resolve(values);
					});
				});
			},
		};
	});

	return (
		<Form form={form} labelCol={{ style: { width: '100px' } }} initialValues={{ status: 2 }}>
			<Form.Item hidden name="status">
				<Input />
			</Form.Item>
			<Form.Item label="原因" name="reason" rules={[{ required: true, message: '请输入不通过原因' }]}>
				<Input.TextArea rows={4} placeholder="请输入不通过原因" />
			</Form.Item>
		</Form>
	);
});

// 经历展示
const ExperiencesList = (props = {}) => {
	return (
		<Space direction="vertical">
			{(props.data || []).map((ov, oi) => {
				return (
					<div key={oi} className="">
						<div className="line-height-22">
							{ov.startDate} - {ov.endDate} {ov.deptName}
						</div>
						<div className="margin-top-4 color-86909c">
							{ov.companyName} {ov.positionName}
						</div>
					</div>
				);
			})}
		</Space>
	);
};

export default Index;
