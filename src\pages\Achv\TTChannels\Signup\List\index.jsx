import { useEffect, useState } from 'react';
import { Table, Button, Popconfirm, Space, Form, Input, Cascader, Row, Col, Affix, Image, Select, message, Switch, Modal } from 'antd';

import { useTableData } from '@/hook/useTableData';
import { useRouterLink } from '@/hook/useRouter';
import {
	pageTTChannels as getTablePageData,
	batchDel as delTableItemData,
	updateRankingNum as batchUpdateSort,
	queryStatistics,
	updateRecommendStatus,
} from '@/api/Achv/TTChannels/Signup/index';
import { getThreeLevelData } from '@/api/common';

import { auditStatusData, auditStatusTextList, TTChannelsTypeData, TTChannelsTypeTextList, authData, authTextList } from '@/pages/Achv/config';

import './index.scss';

const Index = () => {
	const { linkTo, searchParams } = useRouterLink();
	const status = searchParams.get('auditStatus');
	const [auditStatus, setAuditStatus] = useState(status === null ? 3 : status - 0 || '');
	const { form, dataSource, pagination, changePage, getTableData, delTableData, onSearch, onReset, SortInput } = useTableData({
		params: { auditStatus },
		getTablePageData,
		delTableItemData,
		batchUpdateSort,
		pageCallback: (paramsData = {}) => {
			getStatisticsData({ ...paramsData });
		},
	});

	// 新建弹窗显示
	const [open, setOpen] = useState(false);

	// 获取统计数据
	const [statistics, setStatistics] = useState({});
	const getStatisticsData = (paramsData) => {
		delete paramsData.auditStatus;
		delete paramsData.pageSize;
		delete paramsData.pageNum;
		delete paramsData.tempArea;
		queryStatistics(paramsData).then((res) => {
			setStatistics(res.data || {});
		});
	};

	// 改变状态
	const changeStatus = (row, typeKey, apiFn) => {
		const status = row[typeKey] === 0 ? 1 : 0;
		const params = {
			id: row.id,
		};
		params[typeKey] = status;
		apiFn(params).then(() => {
			message.success('操作成功');
			row[typeKey] = status;
			getTableData();
		});
	};

	// 获取选项数据
	const [areaOptions, setAreaOptions] = useState([]);
	const getOptionsData = () => {
		getThreeLevelData({ level: 2 }).then((res) => {
			setAreaOptions(res.data || []);
		});
	};

	useEffect(() => {
		getOptionsData();
	}, []);

	return (
		<div className="">
			<div className="flex flex-shrink align-center margin-bottom-4 height-48 font-size-22 font-weight-500 color-1d2129">入驻管理</div>
			{/* Tabs & 功能按钮 开始 */}
			<Affix offsetTop={0}>
				<div className="flex align-center justify-between padding-24 border-radius-8 bg-color-ffffff border-solid-bottom-f2f3f5">
					<div className="flex align-center">
						{[...auditStatusData].map((ov) => (
							<div
								key={ov.value}
								className={`a margin-right-40 font-size-18 font-weight-500 ${auditStatus === ov.value ? 'color-165dff' : ''}`}
								onClick={() => setAuditStatus(ov.value)}
							>
								{ov.label}
								{statistics[ov.countName] > 0 ? `（${statistics[ov.countName]}）` : ''}
							</div>
						))}
					</div>
					<Space size={16}>
						<Button
							type="primary"
							onClick={() => {
								setOpen(true);
							}}
						>
							创建科转号
						</Button>
					</Space>
				</div>
			</Affix>
			{/* Tabs & 功能按钮 结束 */}
			<div className="flex-sub margin-top-18 padding-24 border-radius-8 bg-color-ffffff">
				{/* 筛选条件 开始 */}

				<div className="flex justify-between margin-bottom-18">
					<div className="flex-sub">
						<Form
							form={form}
							labelAlign="right"
							layout="inline"
							initialValues={{
								provinceCodes: undefined,
								cityCodes: undefined,
								tempArea: [],
							}}
						>
							<Form.Item hidden name="provinceCodes">
								<Input />
							</Form.Item>
							<Form.Item hidden name="cityCodes">
								<Input />
							</Form.Item>
							<Row className="width-100per" gutter={[12, 12]}>
								<Col span={8}>
									<Form.Item name="accountName" label="账号名称">
										<Input placeholder="请输入账号名称" />
									</Form.Item>
								</Col>
								<Col span={8}>
									<Form.Item name="channelType" label="类型">
										<Select options={TTChannelsTypeData} allowClear placeholder="请选择类型" />
									</Form.Item>
								</Col>
								<Col span={8}>
									<Form.Item name="tempArea" label="城市">
										<Cascader
											options={areaOptions}
											placeholder="请选择城市"
											changeOnSelect
											displayRender={(label) => label.join('-')}
											onChange={(e = [undefined, undefined]) => {
												form.setFieldValue('provinceCodes', e[0] ? [e[0]] : undefined);
												form.setFieldValue('cityCodes', e[1] ? [e[1]] : undefined);
											}}
										/>
									</Form.Item>
								</Col>
								<Col span={8}>
									<Form.Item name="isAuth" label="认证状态">
										<Select options={authData} allowClear placeholder="请选择认证状态" />
									</Form.Item>
								</Col>
							</Row>
						</Form>
					</div>
					<div className="margin-left-12 padding-left-12 border-solid-left-e5e6eb">
						<Space size={16}>
							<Button onClick={onReset}>重置</Button>
							<Button type="primary" onClick={onSearch}>
								查询
							</Button>
						</Space>
					</div>
				</div>
				{/* 筛选条件 结束 */}

				{/* 表格 开始 */}

				<Table rowKey="id" dataSource={dataSource} pagination={pagination} onChange={changePage} scroll={{ x: 'max-content' }}>
					<Table.Column
						title="序号"
						key="index"
						width={60}
						render={(_, __, index) => {
							return index + 1;
						}}
					/>

					<Table.Column
						title="图片"
						dataIndex="avatarUrl"
						width={100}
						render={(text) => {
							return (
								(text && (
									<div className="flex align-center justify-center width-100 height-100 overflow-hidden">
										<Image width={100} src={text} />
									</div>
								)) ||
								null
							);
						}}
					/>
					<Table.Column title="账号名" dataIndex="accountName" render={(text) => <div className="max-width-240">{text}</div>} />
					<Table.Column
						title="类型"
						dataIndex="channelType"
						align="center"
						render={(text) => {
							return (
								<div
									className={`tag-status-small-${
										['', 'f8bb35', '700eb2', 'primary', 'warning', '00b42a', 'success', 'error', 'f8bb35', '700eb2'][text]
									}`}
								>
									{TTChannelsTypeTextList[text] || '--'}
								</div>
							);
						}}
					/>
					<Table.Column title="关注量" dataIndex="followNum" align="center" render={(text) => text || '--'} />
					<Table.Column
						title="发布状态"
						dataIndex="auditStatus"
						align="center"
						render={(text) => {
							return (
								<div className={`tag-status-small-${['default', 'warning', 'error', 'primary'][text]}  `}>
									{auditStatusTextList[text || ''] || '--'}
								</div>
							);
						}}
					/>
					<Table.Column
						title="认证状态"
						dataIndex="isAuth"
						align="center"
						render={(text) => {
							return (
								<div
									style={{
										color: text === 1 ? '#1890ff' : '',
									}}
								>
									{authTextList[text || 0]}
								</div>
							);
						}}
					/>
					<Table.Column title="邀约人" dataIndex="inviteName" align="center" render={(text) => text || '--'} />
					<Table.Column
						title="城市"
						dataIndex="provinceCode"
						render={(_, record) => {
							return [record.provinceName, record.cityName, record.areaName].filter((ov) => ov).join('-');
						}}
					/>
					<Table.Column
						title="排序"
						align="center"
						dataIndex="rankingNum"
						render={(_, record) => {
							return <SortInput record={record} />;
						}}
					/>
					<Table.Column
						title="推荐状态"
						dataIndex="recommendStatus"
						align="center"
						render={(text, record) => {
							return (
								<Switch
									checked={text === 1}
									onChange={() => {
										changeStatus(record, 'recommendStatus', updateRecommendStatus);
									}}
								/>
							);
						}}
					/>
					<Table.Column
						title="操作"
						key="option"
						dataIndex="option"
						align="center"
						fixed="right"
						render={(_, record) => {
							return (
								<>
									<Button
										type="link"
										size="small"
										onClick={() => linkTo(`/newAchv/TTChannels/signup/detail?id=${record.id}&fromList=1`)}
									>
										编辑/审核
									</Button>
									<Popconfirm
										title="提示"
										description="确定删除吗？"
										onConfirm={() => {
											delTableData(record.id);
										}}
										okText="确定"
										cancelText="取消"
									>
										<Button type="link" size="small">
											删除
										</Button>
									</Popconfirm>
								</>
							);
						}}
					/>
				</Table>
			</div>

			{/* 新建类型选择 弹窗 开始 */}
			<Modal
				title="请选择账号类型"
				open={open}
				centered
				onCancel={() => {
					setOpen(false);
				}}
				footer={null}
			>
				<div className="flex justify-center">
					<Space direction="vertical" size={16} className="margin-top-16 width-300">
						{TTChannelsTypeData.map((ov) => {
							return (
								<Button
									className="width-100per"
									size="large"
									key={ov.value}
									type="primary"
									onClick={() => {
										linkTo(`/newAchv/TTChannels/signup/curd?channelType=${ov.value}`);
									}}
								>
									{ov.label}
								</Button>
							);
						})}
					</Space>
				</div>
			</Modal>
			{/* 新建类型选择 弹窗 结束 */}
		</div>
	);
};

export default Index;
