import { useEffect, useState } from 'react';
import { useRouterLink } from '@/hook/useRouter';

import { Button, Space, Form, Input, message, Affix } from 'antd';

import UploadImg from '@/components/UploadImg';
import SelectTTChannels from '@/components/Achv/SelectTTChannels';

import { getAccount, addAccount, updateAccount } from '@/api/Achv/TTChannels/VideoAccount';

const Index = () => {
	const { linkTo, searchParams } = useRouterLink();
	const id = searchParams.get('id') || '';
	const [form] = Form.useForm();

	const [hash, setHash] = useState('');

	// 取消
	const onCancel = () => {
		linkTo(-1);
	};

	// 提交
	const onSubmit = () => {
		form.validateFields()
			.then((values) => {
				const params = { ...values };
				(values.id ? updateAccount : addAccount)({
					...params,
					ttChannelsIds: params.ttChannelsIds ? params.ttChannelsIds.map((ov) => ov.id) : undefined,
				}).then(() => {
					message.success(values.id ? '修改成功' : '添加成功');
					setTimeout(() => {
						linkTo(-1);
					}, 500);
				});
			})
			.catch((error) => {
				console.log(error);

				const errorName = error.errorFields[0].name[0];
				if (['ttChannelsIds'].includes(errorName)) {
					setHash('section2');
				} else {
					setHash('section1');
				}
			});
	};

	// 获取详情
	const getDetail = () => {
		if (id) {
			getAccount({ id }).then((res) => {
				const resData = res.data || {};

				// 处理 关联科转号
				resData.ttChannelsIds = resData.ttChannelsList || [];
				form.setFieldsValue(resData);
			});
		}
	};

	useEffect(() => {
		const hash = window.location.hash.replace('#', '');
		setHash(hash || 'section1');

		getDetail();
	}, []);

	// 跳转到指定锚点
	useEffect(() => {
		if (hash) {
			setTimeout(() => {
				const element = document.getElementById(hash);
				if (element) {
					element.scrollIntoView({ behavior: 'smooth' });
				}
			}, 500);
		}
	}, [hash]);

	return (
		<div className="">
			<div className="flex flex-shrink align-center margin-bottom-4 height-48 font-size-22 font-weight-500 color-1d2129">
				<Space size={10}>
					<div className="a color-86909c" onClick={() => linkTo('/newAchv/TTChannels/videoAccount')}>
						视频号管理
					</div>
					<div className="color-86909c">/</div>
					<div>{id ? '视频号编辑' : '新增视频号'}</div>
				</Space>
			</div>

			{/* Tabs & 功能按钮 开始 */}
			<div id="section1"></div>
			<Affix offsetTop={0}>
				<div className="flex align-center justify-between padding-24 border-radius-8 border-solid-bottom-f2f2f2 bg-color-ffffff">
					<div className="flex align-center">
						{['基本信息', '关联账号'].map((ov, oi) => {
							const currentHash = `section${oi + 1}`;
							return (
								<div
									key={oi}
									className={`a margin-right-40 font-size-18 font-weight-500 ${hash === currentHash ? 'color-165dff' : ''}`}
									onClick={() => {
										if (currentHash === 'section2') {
											form.validateFields()
												.then(() => {
													setHash(`section${oi + 1}`);
												})
												.catch((error) => {
													const errorName = error.errorFields[0].name[0];
													if (['ttChannelsIds'].includes(errorName)) {
														setHash('section2');
													} else {
														setHash('section1');
													}
												});
										} else {
											setHash(`section${oi + 1}`);
										}
									}}
								>
									{ov}
								</div>
							);
						})}
					</div>
					<Space size={16}>
						<Button onClick={onCancel}>取消</Button>
						<Button type="primary" onClick={onSubmit}>
							保存
						</Button>
					</Space>
				</div>
			</Affix>
			{/* Tabs & 功能按钮 结束 */}

			<Form
				form={form}
				className="antd-form-box"
				labelAlign="right"
				labelCol={{ style: { width: '120px' } }}
				initialValues={{
					mainStatus: 0,
					ttChannelsIds: [],
					delTTChannelsIds: [],
				}}
			>
				<Form.Item hidden name="id">
					<Input />
				</Form.Item>
				<Form.Item hidden name="mainStatus">
					<Input />
				</Form.Item>
				<Form.Item hidden name="delTTChannelsIds">
					<Input />
				</Form.Item>
				<div style={{ display: hash !== 'section2' ? 'block' : 'none' }}>
					{/* 基本信息 开始 */}
					<div className="margin-top-20 padding-20 border-radius-8 bg-color-ffffff">
						<div className="padding-bottom-30 line-height-26 font-size-18 font-weight-500 color-1d2129">基本信息</div>
						<Form.Item label="视频号名称" name="wechatChannelsAccountName" rules={[{ required: true, message: '请输入视频号名称' }]}>
							<Input className="input-box" placeholder="请输入视频号名称" />
						</Form.Item>
						<Form.Item label="视频号ID" name="wechatChannelsAccountId" rules={[{ required: true, message: '请输入视频号ID' }]}>
							<Input className="input-box" placeholder="请输入视频号ID" />
						</Form.Item>
						<Form.Item label="视频号头像" name="avatarUrl">
							<UploadImg size={5} width={160} height={160} />
						</Form.Item>
					</div>
					{/* 基本信息 结束 */}
				</div>

				{/* 关联账号 开始 */}
				<div style={{ display: hash === 'section2' ? 'block' : 'none' }}>
					<div id="section2"></div>
					<SelectTTChannels
						form={form}
						name="ttChannelsIds"
						label="关联科转号"
						rules={[{ required: true, type: 'array', message: '请选择关联科转号' }]}
						collectDelIds={true}
					/>
				</div>
				{/* 关联账号 结束 */}
			</Form>
		</div>
	);
};

export default Index;
