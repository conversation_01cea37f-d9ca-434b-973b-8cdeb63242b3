import { useEffect, useState, useRef, forwardRef, useImperativeHandle } from 'react';
import { useRouterLink } from '@/hook/useRouter';

import { Button, Input, Affix, Anchor, message, Form, Modal, Image, Space } from 'antd';
import { EditOutlined } from '@ant-design/icons';

import ModalForm from '@/components/ModalForm';

import { getAccount } from '@/api/Achv/TTChannels/VideoAccount';

const Index = () => {
	const { linkTo, searchParams } = useRouterLink();
	const id = searchParams.get('id') || '';
	const fromList = !!searchParams.get('fromList');

	const [detail, setDetail] = useState({});

	// 跳转地址
	const linkToPath = `/newAchv/TTChannels/videoAccount`;

	// 获取详情
	const getDetail = () => {
		if (id) {
			getAccount({ id }).then((res) => {
				const resData = res.data || {};

				setDetail(resData);
			});
		}
	};

	useEffect(() => {
		getDetail();
	}, []);

	return (
		<div className="">
			<div className="flex flex-shrink align-center margin-bottom-4 height-48 font-size-22 font-weight-500 color-1d2129">
				<Space size={10}>
					<div className="a color-86909c" onClick={() => linkTo(fromList ? -1 : linkToPath)}>
						视频号管理
					</div>
					<div className="color-86909c">/</div>
					<div>视频号详情</div>
				</Space>
			</div>
			<div id="section1"></div>
			{/* Tabs & 功能按钮 开始 */}
			<Affix>
				<div className="height-66 flex align-center justify-between bg-color-ffffff border-radius-8 padding-lr-24 border-solid-bottom-f2f3f5">
					<div className="flex align-center justify-between color-1d2129 line-height-26 font-size-18 font-weight-500">
						<Anchor
							affix={false}
							rootClassName="custom-anchor-box"
							direction="horizontal"
							replace
							targetOffset={80}
							items={[
								{
									key: 'section1',
									href: '#section1',
									title: <div className="margin-right-40 font-size-16 font-weight-500">基础信息</div>,
								},
								{
									key: 'section2',
									href: '#section2',
									title: <div className="margin-right-40 font-size-16 font-weight-500">关联账号</div>,
								},
							]}
						/>
					</div>
				</div>
			</Affix>
			{/* Tabs & 功能按钮 结束 */}

			{/* 基础信息 开始 */}
			<div className="margin-top-20 padding-20 border-radius-8 bg-color-ffffff">
				<div className="flex align-center justify-between margin-bottom-20">
					<div className="font-size-18 line-height-26 font-weight-500">基础信息</div>
					<Button
						type="link"
						icon={<EditOutlined />}
						onClick={() => {
							linkTo(`${linkToPath}/curd?id=${id}#section1`);
						}}
					>
						编辑信息
					</Button>
				</div>
				<div className="flex align-center justify-start font-size-14 line-height-22 padding-left-16 margin-bottom-20">
					<div className="color-86909c margin-right-4">视频号名称：</div>
					<div className="">{detail.wechatChannelsAccountName || '--'}</div>
				</div>
				<div className="flex align-center justify-start font-size-14 line-height-22 padding-left-16 margin-bottom-20">
					<div className="color-86909c margin-right-4">视频号ID：</div>
					<div className="">{detail.wechatChannelsAccountId || '--'}</div>
				</div>
				<div className="flex justify-start font-size-14 line-height-22 padding-left-16 margin-bottom-20">
					<div className="color-86909c margin-right-4">视频号头像：</div>
					<div className="">
						<Image src={detail.avatarUrl} width={200} height={200} />
					</div>
				</div>
				<div className="flex align-center justify-start font-size-14 line-height-22 padding-left-16 margin-bottom-20">
					<div className="color-86909c margin-right-4">主体状态：</div>
					<div className="">
						<div className={`tag-status-${['success', 'primary'][detail.mainStatus || 0]}`}>
							{detail.mainStatus === 1 ? '同主体' : '非同主体'}
						</div>
					</div>
				</div>
			</div>
			{/* 基础信息 结束 */}

			{/* 关联账号 开始 */}
			<div id="section2"></div>
			<div className="bg-color-ffffff border-radius-8 padding-lr-24 padding-tb-20 margin-tb-16">
				<div className="flex align-center justify-between margin-bottom-20">
					<div className="font-size-18 line-height-26 font-weight-500">关联账号</div>
					<Button
						type="link"
						icon={<EditOutlined />}
						onClick={() => {
							linkTo(`${linkToPath}/curd?id=${id}${detail.originalUrl ? '&type=originUrl' : ''}#section3`);
						}}
					>
						编辑信息
					</Button>
				</div>
				<div className="flex align-center justify-start font-size-14 line-height-22 padding-left-16 margin-bottom-20">
					<div className="color-86909c margin-right-24">关联科转号</div>
					<div className="flex align-start justify-start flex-wrap">
						{(detail.ttChannelsList || []).map((item) => {
							return (
								<div key={item.id} className="flex align-center justify-start margin-right-20">
									<img src={item.avatarUrl} className="width-20 height-20 border-radius-10 bg-color-e5e6eb" />
									<div className="font-size-14 color-165dff margin-left-6 line-height-20">{item.accountName || ''}</div>
								</div>
							);
						})}
						{(detail.ttChannelsList || []).length === 0 && '--'}
					</div>
				</div>
			</div>
			{/* 关联账号 结束 */}
		</div>
	);
};

export default Index;
