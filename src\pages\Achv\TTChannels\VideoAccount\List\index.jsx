import { useState } from 'react';
import { Table, Button, Popconfirm, Space, Image, Affix, Form, Input, Row, Col } from 'antd';
import { useTableData } from '@/hook/useTableData';
import { useRouterLink } from '@/hook/useRouter';
import { pageAccount as getTablePageData, batchDelAccount as delTableItemData } from '@/api/Achv/TTChannels/VideoAccount/index';

const Index = () => {
	const { linkTo } = useRouterLink();
	const linkToPath = '/newAchv/TTChannels/videoAccount';
	const [total, setTotal] = useState(0);
	const { form, dataSource, pagination, changePage, delTableData, onReset, onSearch } = useTableData({
		params: {},
		getTablePageData,
		delTableItemData,
		getPageResult: (result) => {
			setTotal(result.total || 0);
			return Promise.resolve(result);
		},
	});

	return (
		<div className="">
			<div className="flex flex-shrink align-center margin-bottom-4 height-48 font-size-22 font-weight-500 color-1d2129">视频号管理</div>

			{/* Tabs & 功能按钮 开始 */}
			<Affix offsetTop={0}>
				<div className="flex align-center justify-between padding-24 border-radius-8 bg-color-ffffff border-solid-bottom-f2f3f5">
					<div className="flex align-center">
						<div className={`a margin-right-40 font-size-18 font-weight-500 color-165dff`}>全部{total > 0 ? `(${total})` : ''}</div>
					</div>
					<Space size={16}>
						<Button
							type="primary"
							onClick={() => {
								linkTo(`${linkToPath}/curd`);
							}}
						>
							新增视频号
						</Button>
					</Space>
				</div>
			</Affix>
			{/* Tabs & 功能按钮 结束 */}

			<div className="flex-sub margin-top-18 padding-24 border-radius-8 bg-color-ffffff">
				{/* 筛选条件 开始 */}
				<div className="flex justify-between margin-bottom-18">
					<div className="flex-sub">
						<Form form={form} labelAlign="right" layout="inline">
							<Row className="width-100per" gutter={[12, 12]}>
								<Col span={12}>
									<Form.Item name="wechatChannelsAccountName" label="视频号名称">
										<Input placeholder="请输入视频号名称" />
									</Form.Item>
								</Col>
							</Row>
						</Form>
					</div>
					<div className="margin-left-12 padding-left-12 border-solid-left-e5e6eb">
						<Space size={16}>
							<Button onClick={onReset}>重置</Button>
							<Button type="primary" onClick={onSearch}>
								查询
							</Button>
						</Space>
					</div>
				</div>
				{/* 筛选条件 结束 */}

				{/* 表格 开始 */}
				<Table rowKey="id" dataSource={dataSource} pagination={pagination} onChange={changePage} scroll={{ x: 'max-content' }}>
					<Table.Column
						title="序号"
						key="index"
						width={60}
						align="center"
						render={(_, __, index) => {
							return index + 1;
						}}
					/>
					<Table.Column
						title="视频号名称"
						dataIndex="wechatChannelsAccountName"
						render={(text) => <div className="max-width-400">{text}</div>}
					/>
					<Table.Column
						title="头像"
						dataIndex="avatarUrl"
						align="center"
						width={100}
						render={(text) => {
							return (
								(text && (
									<div className="flex align-center justify-center width-100 height-100 overflow-hidden">
										<Image width={100} src={text} />
									</div>
								)) ||
								'--'
							);
						}}
					/>
					<Table.Column title="ID" dataIndex="wechatChannelsAccountId" />
					<Table.Column
						title="关联科转号"
						dataIndex="ttChannelsList"
						render={(text) => {
							return (text && text.map((ov) => ov.accountName).join('、')) || '--';
						}}
					/>
					<Table.Column
						title="操作"
						key="option"
						dataIndex="option"
						align="center"
						fixed="right"
						width="160px"
						render={(_, record) => {
							return (
								<>
									<Button type="link" size="small" onClick={() => linkTo(`${linkToPath}/detail?id=${record.id}&fromList=1`)}>
										详情/编辑
									</Button>
									<Popconfirm
										title="提示"
										description="确定删除吗？"
										onConfirm={() => {
											delTableData(record.id);
										}}
										okText="确定"
										cancelText="取消"
									>
										<Button type="link" size="small">
											删除
										</Button>
									</Popconfirm>
								</>
							);
						}}
					/>
				</Table>
				{/* 表格 结束 */}
			</div>
		</div>
	);
};

export default Index;
