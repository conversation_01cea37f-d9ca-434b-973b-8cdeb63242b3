import { useEffect, useState, useRef } from 'react';
import { useRouterLink } from '@/hook/useRouter';

import { Button, Space, message, Modal } from 'antd';
import ModalForm from '@/components/ModalForm';
import Docking from '@/components/Achv/DockingManage/index';
import { BaseData as AchievementBaseData } from '@/pages/Achv/Achievement/Manage/Docking/Detail';
import { BaseData as DemandBaseData } from '@/pages/Achv/Demand/DemandManage/Docking/Detail';

import { taskDetail, updateHandlingStatus } from '@/api/Achv/Task';
import { detailCooperate } from '@/api/Achv/Cooperate';
import { auditSendOrder } from '@/api/Achv/SendOrder';
import { getSupplier, updateStageStatus } from '@/api/Achv/Demand/DockingManage';

import { getDemand } from '@/api/Achv/Demand/DemandManage/index';
import { getAchievementDetail } from '@/api/Achv/Achievement/Manage/index';
import { detailByUserId, pageSignUp } from '@/api/Achv/Competition/Signup';

import { updateTaskCount } from '@/utils/achv';

import { taskTypeObj } from '@/pages/Achv/Task/index';

import { competitionGroupTextList } from '@/pages/Achv/config';

const ConrirmForm = Docking.ConrirmForm;
const Index = (props = {}) => {
	console.log(props);

	const { linkTo, searchParams } = useRouterLink();
	const id = searchParams.get('id');

	// 跳转地址
	const linkToPath = props.linkToPath || `/newAchv/task`;

	const [detail, setDetail] = useState({});

	// 更新任务状态
	const updateTaskStatus = () => {
		message.success('操作成功');
		updateHandlingStatus({ id }).then(() => {
			updateTaskCount();
			setTimeout(() => {
				linkTo(-1);
			}, 500);
		});
	};

	// 获取详情
	const getTaskDetail = () => {
		taskDetail({ id }).then((res) => {
			const taskDetail = res.data || {};
			if (taskDetail.handlingStatus === 1) {
				message.warning('任务已处理');
				setTimeout(() => {
					linkTo(-1);
				}, 500);
				return;
			}
			if (taskDetail) {
				// 请求 供给商 详情
				if (['demand_claim_audit', 'achievement_claim_audit', 'third_party_meeting'].includes(taskDetail.typeCode)) {
					getSupplier({
						id: taskDetail.businessId,
					}).then((res) => {
						const resData = res.data || {};
						getSupplierContactsGrouopName(resData.supplierContacts).then((competitionGroup) => {
							setDetail({ ...taskDetail, ...resData, competitionGroup });
						});
					});
				} else {
					setDetail(taskDetail);
				}
			} else {
				message.error('任务不存在');
				setTimeout(() => {
					linkTo(-1);
				}, 500);
			}
		});
	};

	// 获取供给方联系人组名 赛事需求才需要
	const getSupplierContactsGrouopName = (supplierContacts) => {
		return new Promise((resolve) => {
			if (supplierContacts && props.releaseType === 3) {
				pageSignUp({
					pageNum: 1,
					pageSize: 1,
					name: supplierContacts,
				})
					.then((res) => {
						const competitionGroup = res?.data?.records[0]?.competitionGroup;
						resolve(competitionGroup);
					})
					.catch(() => {
						resolve('');
					});
			} else {
				resolve('');
			}
		});
	};

	useEffect(() => {
		if (id) {
			getTaskDetail();
		} else {
			message.error('参数错误');
			setTimeout(() => {
				linkTo(-1);
			}, 500);
		}
	}, []);

	return (
		<div className="">
			<div className="flex flex-shrink align-center margin-bottom-4 height-48 font-size-22 font-weight-500 color-1d2129">
				<Space size={10}>
					<div className="a color-86909c" onClick={() => linkTo(linkToPath)}>
						待办事项
					</div>
					<div className="color-86909c">/</div>
					<div>任务详情</div>
				</Space>
			</div>

			{/* 任务详情 开始 */}
			<div className="margin-top-20 padding-top-20 padding-lr-20 padding-bottom-30 border-radius-8 bg-color-ffffff">
				<div className="flex align-center justify-between">
					<div className="line-height-26 font-size-18 font-weight-500 color-1d2129">任务详情</div>
				</div>
				<div className="flex margin-top-20">
					<div className="margin-right-4 line-height-22 color-86909c">任务类型：</div>
					<div className="flex-sub flex line-height-22 ">
						<div className="a font-weight-500 color-165dff">
							{taskTypeObj[detail.typeCode] && (
								<div className={`tag-status-${taskTypeObj[detail.typeCode].color}`}>{taskTypeObj[detail.typeCode].typeName}</div>
							)}
						</div>
					</div>
				</div>

				{/* 需求认领/成果对接 (审核) 开始 */}
				{['demand_claim_audit', 'achievement_claim_audit'].includes(detail.typeCode) && (
					<ClaimAudit detailData={detail} releaseType={props.releaseType} updateTaskStatus={updateTaskStatus} />
				)}
				{/* 需求认领/成果对接 (审核) 结束 */}

				{/* 组织第三方会 开始 */}
				{detail.typeCode === 'third_party_meeting' && (
					<ThirdPartyMeeting detailData={detail} releaseType={props.releaseType} updateTaskStatus={updateTaskStatus} />
				)}
				{/* 组织第三方会 结束 */}

				{/* 综合合作申请 开始 */}
				{detail.typeCode === 'cooperate_intention' && (
					<CooperateIntention detailData={detail} releaseType={props.releaseType} updateTaskStatus={updateTaskStatus} />
				)}
				{/* 综合合作申请 结束 */}
			</div>
			{/* 任务详情 结束 */}
		</div>
	);
};

// 需求认领/成果对接 (审核)
const ClaimAudit = (props = {}) => {
	const ModalFormRef = useRef();
	const detailData = props.detailData;

	// 审核 (3:已认领 5 审核不通过)
	const onAudit = (claimStatus) => {
		if (claimStatus === 3) {
			Modal.confirm({
				title: '是否确认通过审核?',
				content: '',
				onOk() {
					updateAuditStatus({ claimStatus });
				},
			});
		} else {
			ModalFormRef.current.setOpen(true);
			ModalFormRef.current.setTitle('不通过原因');
		}
	};

	// 更新审核状态
	const updateAuditStatus = ({ claimStatus, refuseReason }) => {
		auditSendOrder({
			id: detailData.sendOrderId,
			claimStatus,
			refuseReason,
		}).then(() => {
			props.updateTaskStatus();
		});
	};

	return (
		<>
			<div className="flex margin-top-20">
				<div className="margin-right-4 line-height-22 color-86909c">申请人：</div>
				<div className="flex-sub line-height-22 color-1d2129">{detailData.brokerName}</div>
			</div>
			<DemandORAchievementBaseData detailData={detailData} releaseType={props.releaseType} />
			<div className="flex margin-top-20">
				<div className="margin-right-4 line-height-22 color-86909c">申请时间：</div>
				<div className="flex-sub line-height-22 color-1d2129">{(detailData.createTime || '--').slice(0, 16)}</div>
			</div>
			{/* 功能按钮 开始 */}
			<div className="flex align-center justify-between margin-top-20">
				<div className="flex align-center"></div>
				<Space size={16}>
					<Button
						onClick={() => {
							onAudit(5);
						}}
					>
						不通过
					</Button>
					<Button
						type="primary"
						onClick={() => {
							onAudit(3);
						}}
					>
						通过
					</Button>
				</Space>
			</div>
			{/* 功能按钮 结束 */}

			{/* 询问弹窗 开始 */}
			<ModalForm
				ref={ModalFormRef}
				onOk={updateAuditStatus}
				FormComp={(props) => <ConrirmForm ref={props.FormCompRef} releaseType={props.releaseType} />}
			/>
			{/* 询问弹窗 结束 */}
		</>
	);
};

// 组织第三方会
const ThirdPartyMeeting = (props = {}) => {
	const ModalFormRef = useRef();
	const detailData = props.detailData;

	const [stageStatus, setStageStatus] = useState('');
	// 供给方 流程处理 2 已三方会议 4 已终止谈判
	const onSupplierProcess = (stageStatus) => {
		setStageStatus(stageStatus);
		ModalFormRef.current.setOpen(true);
		ModalFormRef.current.setTitle(stageStatus === 4 ? '终止原因' : '会议信息');
	};

	// 更新状态
	const updateStatus = ({ stageStatus, reasonReason, meetingTime, meetingContent }) => {
		updateStageStatus({
			id: detailData.id,
			sendOrderId: detailData.sendOrderId,
			stageStatus,
			reasonReason,
			meetingTime,
			meetingContent,
		}).then(() => {
			props.updateTaskStatus();
		});
	};

	return (
		<>
			<DemandORAchievementBaseData detailData={detailData} releaseType={props.releaseType} />
			<div className="flex margin-top-20">
				<div className="margin-right-4 line-height-22 color-86909c">申请时间：</div>
				<div className="flex-sub line-height-22 color-1d2129">{(detailData.createTime || '--').slice(0, 16)}</div>
			</div>
			{/* 功能按钮 开始 */}
			<div className="flex align-center justify-between margin-top-20">
				<div className="flex align-center"></div>
				<Space size={16}>
					<Button
						onClick={() => {
							onSupplierProcess(4);
						}}
					>
						终止流程
					</Button>
					<Button
						type="primary"
						onClick={() => {
							onSupplierProcess(2);
						}}
					>
						设置对接会
					</Button>
				</Space>
			</div>
			{/* 功能按钮 结束 */}

			{/* 询问弹窗 开始 */}
			<ModalForm
				ref={ModalFormRef}
				onOk={updateStatus}
				FormComp={(props) => <ConrirmForm ref={props.FormCompRef} stageStatus={stageStatus} />}
			/>
			{/* 询问弹窗 结束 */}
		</>
	);
};

// 成果需求基础信息
const DemandORAchievementBaseData = (props = {}) => {
	const { linkTo, openNewTab } = useRouterLink();
	const detailData = props.detailData || {};
	const { sourceType, sourceId, sourceName } = detailData;

	// 跳转地址
	let linkToPath = '';

	if (props.releaseType === 3) {
		linkToPath = sourceType === 1 ? '/newAchv/competition/demand/detail' : '/newAchv/competition/achievement/detail';
	} else {
		linkToPath = sourceType === 1 ? '/newAchv/demand/demandManage/detail' : '/newAchv/achievement/manage/detail';
	}

	const [competitionData, setCompetitionData] = useState({});
	const getCompetitionData = () => {
		['', getDemand, getAchievementDetail][detailData.sourceType]({ id: detailData.sourceId }).then((res) => {
			const createBy = res?.data?.createBy;
			if (createBy) {
				detailByUserId({ userId: createBy }).then((res) => {
					const { id: signUpId, name: signUpName = '', competitionGroup = '' } = res.data || {};
					setCompetitionData({
						signUpId,
						signUpName,
						competitionGroup,
					});
				});
			}
		});
	};
	useEffect(() => {
		if (detailData.sourceId && detailData.createSource === 3) {
			getCompetitionData();
		}
	}, []);

	return (
		<>
			<div className="flex margin-top-20">
				<div className="margin-right-4 line-height-22 color-86909c">{sourceType === 1 ? '需求名称' : '成果名称'}：</div>
				<div className="flex-sub flex line-height-22 ">
					<div
						className="a font-weight-500 color-165dff"
						onClick={() => {
							openNewTab(`${linkToPath}?id=${sourceId}`);
						}}
					>
						{sourceName}
					</div>
				</div>
			</div>

			{competitionData.competitionGroup && (
				<div className="flex margin-top-20">
					<div className="margin-right-4 line-height-22 color-86909c">参赛组别：</div>
					<div className="flex-sub">
						<div className={`tag-status-${['', 'primary', 'warning', '700eb2'][competitionData.competitionGroup]}`}>
							{competitionGroupTextList[competitionData.competitionGroup] || '--'}
						</div>
					</div>
				</div>
			)}
			{competitionData.signUpName && (
				<div className="flex margin-top-20">
					<div className="margin-right-4 line-height-22 color-86909c">参赛者姓名：</div>
					<div className="flex-sub line-height-22">
						<span
							className="a color-165dff"
							onClick={() => {
								linkTo(`/newAchv/competition/signup/detail?id=${competitionData.signUpId}`);
							}}
						>
							{competitionData.signUpName || '--'}
						</span>
					</div>
				</div>
			)}

			{sourceType === 1 ? <DemandBaseData detailData={detailData} /> : <AchievementBaseData detailData={detailData} />}
		</>
	);
};

// 综合对接申请

const CooperateIntention = (props = {}) => {
	const { linkTo } = useRouterLink();
	console.log(props);

	const { detailData, releaseType } = props;
	const { id, businessId } = detailData;

	const [cooperateData, setCooperateData] = useState({});

	// 跳转详情
	const toDetail = () => {
		const { cooperateType, cooperateProjectId } = cooperateData;
		let linkToPath = '';
		if (cooperateType == 1) {
			if (releaseType === 3) {
				linkToPath = `/newAchv/competition/achievement/detail?id=${cooperateProjectId}`;
			} else {
				linkToPath = `/newAchv/achievement/manage/detail?id=${cooperateProjectId}`;
			}
		} else if (cooperateType == 2) {
			if (releaseType === 3) {
				linkToPath = `/newAchv/competition/demand/detail?id=${cooperateProjectId}`;
			} else {
				linkToPath = `/newAchv/demand/demandManage/detail?id=${cooperateProjectId}`;
			}
		} else if (cooperateType == 3) {
			linkToPath = `/newAchv/agencyManage/detail?id=${cooperateProjectId}`;
		} else if (cooperateType == 4) {
			linkToPath = `/newAchv/expertManage/detail?id=${cooperateProjectId}`;
		}
		linkTo(linkToPath);
	};

	// 关闭待办任务
	const closeTask = () => {
		Modal.confirm({
			content: '是否要关闭当前任务？',
			okText: '关闭',
			cancelText: '暂不',
			centered: true,
			onOk: () => {
				updateHandlingStatus({ id }).then(() => {
					updateTaskCount();
					setTimeout(() => {
						linkTo(-1);
					}, 500);
				});
			},
		});
	};

	useEffect(() => {
		detailCooperate({ id: businessId }).then((res) => {
			setCooperateData(res.data || {});
		});
	}, []);
	return (
		<>
			<div className="flex margin-top-20">
				<div className="margin-right-4 line-height-22 color-86909c">申请人：</div>
				<div className="flex-sub line-height-22 color-1d2129">{cooperateData.contacts || '--'}</div>
			</div>
			<div className="flex margin-top-20">
				<div className="margin-right-4 line-height-22 color-86909c">就职单位：</div>
				<div className="flex-sub line-height-22 color-1d2129">{cooperateData.name || '--'}</div>
			</div>
			<div className="flex margin-top-20">
				<div className="margin-right-4 line-height-22 color-86909c">工作职务：</div>
				<div className="flex-sub line-height-22 color-1d2129">{cooperateData.position || '--'}</div>
			</div>
			<div className="flex margin-top-20">
				<div className="margin-right-4 line-height-22 color-86909c">联系电话：</div>
				<div className="flex-sub line-height-22 color-1d2129">{cooperateData.contactsPhone || '--'}</div>
			</div>
			<div className="flex margin-top-20">
				<div className="margin-right-4 line-height-22 color-86909c">
					对接{['', '成果', '需求', '机构', '专家'][cooperateData.cooperateType - 0 || 0]}：
				</div>
				<div className="flex-sub line-height-22">
					<div className="a color-165dff" onClick={toDetail}>
						{cooperateData.cooperateProjectName || '--'}
					</div>
				</div>
			</div>
			<div className="flex margin-top-20">
				<div className="margin-right-4 line-height-22 color-86909c">拟合作内容：</div>
				<div className="flex-sub line-height-22 color-1d2129">{cooperateData.content || '--'}</div>
			</div>
			<div className="flex margin-top-20">
				<div className="margin-right-4 line-height-22 color-86909c">申请时间：</div>
				<div className="flex-sub line-height-22 color-1d2129">{(detailData.createTime || '--').slice(0, 16)}</div>
			</div>
			<div className="flex align-center justify-between margin-top-20">
				<div className="flex align-center"></div>
				<Space size={16}>
					<Button type="primary" onClick={closeTask}>
						关闭任务
					</Button>
				</Space>
			</div>
		</>
	);
};
export default Index;
