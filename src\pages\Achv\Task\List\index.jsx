import { useEffect, useState } from 'react';
import { Table, Button, Affix } from 'antd';
import { useTableData } from '@/hook/useTableData';
import { useRouterLink } from '@/hook/useRouter';
import { taskPage as getTablePageData, getTaskCount } from '@/api/Achv/Task';
import { taskTypeObj } from '@/pages/Achv/Task/index';

const tabsList = Object.values(taskTypeObj) || [];

const Index = (props = {}) => {
	// 跳转地址
	const linkToPath = props.linkToPath || `/newAchv/task`;

	const { linkTo, searchParams } = useRouterLink();

	const [typeCode, setTypeCode] = useState(searchParams.get('typeCode') || '');

	const { dataSource, pagination, changePage } = useTableData({
		params: { typeCode, createSources: props.releaseType ? [props.releaseType] : [1, 2] },
		getTablePageData,
		closeFilter: true,
		pageCallback: (paramsData = {}) => {
			getStatisticsData({ ...paramsData });
		},
	});

	// 获取统计数据
	const [statistics, setStatistics] = useState({});
	const getStatisticsData = (paramsData) => {
		delete paramsData.pageSize;
		delete paramsData.pageNum;
		getTaskCount(paramsData).then((res) => {
			setStatistics(res.data || {});
		});
	};

	return (
		<div className="">
			<div className="flex flex-shrink align-center margin-bottom-4 height-48 font-size-22 font-weight-500 color-1d2129">待办管理</div>
			{/* Tabs 开始 */}
			<Affix offsetTop={0}>
				<div className="flex align-center justify-between padding-24 border-radius-8 bg-color-ffffff border-solid-bottom-f2f3f5">
					<div className="flex align-center">
						{tabsList.map((ov, oi) => (
							<div
								key={oi}
								className={`a margin-right-40 font-size-18 font-weight-500 ${typeCode === ov.typeCode ? 'color-165dff' : ''}`}
								onClick={() => setTypeCode(ov.typeCode)}
							>
								{ov.label}
								{statistics[ov.countName] > 0 ? `（${statistics[ov.countName]}）` : ''}
							</div>
						))}
					</div>
				</div>
			</Affix>
			{/* Tabs 结束 */}
			<div className="flex-sub margin-top-18 padding-24 border-radius-8 bg-color-ffffff">
				{/* 表格 开始 */}
				<Table rowKey="id" dataSource={dataSource} pagination={pagination} onChange={changePage} scroll={{ x: 'max-content' }}>
					<Table.Column
						title="序号"
						key="index"
						fixed="left"
						width={60}
						render={(_, __, index) => {
							return index + 1;
						}}
					/>
					<Table.Column title="待办内容" dataIndex="taskTitle" />

					<Table.Column
						title="类型"
						dataIndex="typeCode"
						render={(text) => {
							return (
								taskTypeObj[text] && <div className={`tag-status-small-${taskTypeObj[text].color}`}>{taskTypeObj[text].typeName}</div>
							);
						}}
					/>
					<Table.Column title="发起时间" dataIndex="createTime" align="center" />
					<Table.Column
						title="操作"
						key="option"
						dataIndex="option"
						align="center"
						fixed="right"
						render={(_, record) => {
							return (
								<>
									<Button type="link" size="small" onClick={() => linkTo(`${linkToPath}/detail?id=${record.id}`)}>
										去处理
									</Button>
								</>
							);
						}}
					/>
				</Table>
				{/* 表格 结束 */}
			</div>
		</div>
	);
};

export default Index;
