/** 任务类型code
 *  需求认领审核  demand_claim_audit
 *  组织三方会议 third_party_meeting
 *  技术经理人审核  broker_audit
 *  专家审核  expert_audit
 *  机构认证审核  org_audit
 **/
export const taskTypeObj = {
	total: {
		label: '全部待办',
		typeName: '',
		typeCode: '',
		countName: 'total',
		count: 0,
		color: '',
	},
	demand_claim_audit: {
		label: '待审需求认领',
		typeName: '需求认领',
		typeCode: 'demand_claim_audit',
		countName: 'demandClaimAudit',
		count: 0,
		color: 'success',
	},
	achievement_claim_audit: {
		label: '待审成果对接',
		typeName: '成果对接',
		typeCode: 'achievement_claim_audit',
		countName: 'achievementClaimAudit',
		count: 0,
		color: 'warning',
	},
	third_party_meeting: {
		label: '待组织供需对接会',
		typeName: '组织供需对接会',
		typeCode: 'third_party_meeting',
		countName: 'meeting',
		count: 0,
		color: '700eb2',
	},
	cooperate_intention: {
		label: '综合合作申请',
		typeName: '综合合作申请',
		typeCode: 'cooperate_intention',
		countName: 'cooperateIntention',
		count: 0,
		color: 'cd4c57',
	},
};
