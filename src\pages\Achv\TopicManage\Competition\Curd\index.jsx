import { useEffect, useState } from 'react';
import { useRouterLink } from '@/hook/useRouter';
import { Button, Space, Form, Input, message, Affix, Select, InputNumber } from 'antd';
import UploadImg from '@/components/UploadImg';
import { save, update, getCompetitionTopicVo } from '@/api/Achv/TopicManage/Competition';

import { ratingTypeData } from '@/pages/Achv/config';

const Index = () => {
	const { linkTo, searchParams } = useRouterLink();
	const id = searchParams.get('id') || '';
	const [form] = Form.useForm();

	// 跳转地址
	const linkToPath = '/newAchv/trainingManage/course';

	// 禁止编辑发布类型
	const [hash, setHash] = useState('');

	// 取消
	const onCancel = () => {
		linkTo(-1);
	};

	// 提交
	const onSubmit = () => {
		form.validateFields()
			.then((values) => {
				const params = { ...values };

				delete params.tempArea;
				(values.id ? update : save)(params).then(() => {
					message.success(values.id ? '修改成功' : '添加成功');
					setTimeout(() => {
						linkTo(-1);
					}, 500);
				});
			})
			.catch((error) => {
				console.log(error);
			});
	};

	// 获取详情
	const getDetail = () => {
		if (id) {
			getCompetitionTopicVo({ id }).then((res) => {
				const resData = res.data || {};

				form.setFieldsValue(resData);
			});
		}
	};

	useEffect(() => {
		const hash = window.location.hash.replace('#', '');
		setHash(hash || 'section1');

		getDetail();
	}, []);

	// 跳转到指定锚点
	useEffect(() => {
		if (hash) {
			setTimeout(() => {
				const element = document.getElementById(hash);
				if (element) {
					element.scrollIntoView({ behavior: 'smooth' });
				}
			}, 500);
		}
	}, [hash]);

	return (
		<div className="">
			<div className="flex flex-shrink align-center margin-bottom-4 height-48 font-size-22 font-weight-500 color-1d2129">
				<Space size={10}>
					<div className="a color-86909c" onClick={() => linkTo(linkToPath)}>
						大赛查询管理
					</div>
					<div className="color-86909c">/</div>
					<div>{id ? '专题编辑' : '新增专题'}</div>
				</Space>
			</div>

			{/* Tabs & 功能按钮 开始 */}
			<div id="section1"></div>
			<Affix offsetTop={0}>
				<div className="flex align-center justify-between padding-24 border-radius-8 border-solid-bottom-f2f2f2 bg-color-ffffff">
					<div className="flex align-center">
						{['基本信息'].map((ov, oi) => {
							const currentHash = `section${oi + 1}`;
							return (
								<div
									key={oi}
									className={`a margin-right-40 font-size-18 font-weight-500 ${hash === currentHash ? 'color-165dff' : ''}`}
								>
									{ov}
								</div>
							);
						})}
					</div>
					<Space size={16}>
						<Button onClick={onCancel}>取消</Button>
						<Button type="primary" onClick={onSubmit}>
							保存
						</Button>
					</Space>
				</div>
			</Affix>
			{/* Tabs & 功能按钮 结束 */}

			<Form
				form={form}
				className="antd-form-box"
				labelAlign="right"
				labelCol={{ style: { width: '120px' } }}
				initialValues={{
					ratingType: 1,
				}}
			>
				<Form.Item hidden name="id">
					<Input />
				</Form.Item>
				<Form.Item hidden name="rankingNum">
					<Input />
				</Form.Item>
				{/* 基本信息 开始 */}
				<div className="margin-top-20 padding-20 border-radius-8 bg-color-ffffff">
					<div className="padding-bottom-30 line-height-26 font-size-18 font-weight-500 color-1d2129">基本信息</div>
					<Form.Item label="赛事专题名称" name="name" rules={[{ required: true, message: '请输入姓名' }]}>
						<Input className="input-box" placeholder="请输入姓名" />
					</Form.Item>
					<Form.Item label="主视图" name="bannerUrl" rules={[{ required: true, message: '请输入姓名' }]}>
						<UploadImg size={5} width={260} height={120} />
					</Form.Item>
					<Form.Item label="评分规则" name="ratingType" rules={[{ required: true, message: '请选择评分规则' }]}>
						<Select placeholder="请选择评分规则" options={ratingTypeData} />
					</Form.Item>
					<Form.Item label="评委数量" name="reviewQuantity" rules={[{ required: true, message: '请输入评委数量' }]}>
						<InputNumber min={1} decimalSeparator={0} className="width-120" placeholder="请输入评委数量" />
					</Form.Item>
					<Form.Item label="组别" name="groupName" rules={[{ required: true, message: '请输入组别' }]}>
						<Input className="input-box" placeholder="请输入组别 英文逗号分隔 例：初创组,成长组,综合组" />
					</Form.Item>
					<Form.Item label="关联ID" name="timeLineId">
						<Input.TextArea rows={4} className="input-box" placeholder="请输入关联的动态ID 英文逗号分隔" />
					</Form.Item>
					<Form.Item label="分享文案" name="shareDesc">
						<Input className="input-box" placeholder="请输入分享文案" />
					</Form.Item>
					<Form.Item label="分享朋友圈封面" name="timelineShareCoverUrl">
						<UploadImg size={5} width={120} height={120} cropperProps={{ width: 120, height: 120 }} />
					</Form.Item>
					<Form.Item label="分享好友封面" name="friendShareCoverUrl">
						<UploadImg size={5} width={150} height={120} cropperProps={{ width: 150, height: 120 }} />
					</Form.Item>
				</div>
				{/* 基本信息 结束 */}
			</Form>
		</div>
	);
};
export default Index;
