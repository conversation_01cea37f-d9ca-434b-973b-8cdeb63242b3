import { useEffect, useState } from 'react';
import { useRouterLink } from '@/hook/useRouter';

import { Button, Affix, Anchor, Space } from 'antd';
import { EditOutlined } from '@ant-design/icons';

import { getCompetitionTopicJoinInVo } from '@/api/Achv/TopicManage/Competition/Join';
import { getCompetitionTopicVo } from '@/api/Achv/TopicManage/Competition/index';

const Index = () => {
	const { linkTo, searchParams } = useRouterLink();
	const id = searchParams.get('id') || '';
	const fromList = !!searchParams.get('fromList');
	const competitionTopicId = searchParams.get('competitionTopicId');

	const [detail, setDetail] = useState({});

	// 跳转地址
	const linkToPath = '/newAchv/topicManage/competition';

	// 获取详情
	const getDetail = () => {
		if (id) {
			getCompetitionTopicJoinInVo({ id }).then((res) => {
				const resData = res.data || {};
				resData.scoreJson = (resData.scoreJson || '').split(',').filter((ov) => ov);
				setDetail(resData);
			});
		}
	};

	useEffect(() => {
		getDetail();
	}, []);

	// 获取专题名称
	const [interactiveTopicName, setInteractiveTopicName] = useState('');
	const [reviewQuantity, setReviewQuantity] = useState('');
	useEffect(() => {
		if (competitionTopicId) {
			getCompetitionTopicVo({ id: competitionTopicId }).then((res) => {
				const { name, reviewQuantity } = res?.data || {};
				setInteractiveTopicName(name);
				setReviewQuantity(reviewQuantity);
			});
		}
	}, [competitionTopicId]);

	return (
		<div className="">
			<div className="flex flex-shrink align-center margin-bottom-4 height-48 font-size-22 font-weight-500 color-1d2129">
				<Space size={10}>
					<div className="a color-86909c" onClick={() => linkTo(linkToPath)}>
						大赛查询管理
					</div>
					<div className="color-86909c">/</div>
					<div className="a color-86909c" onClick={() => linkTo(`${linkToPath}/detail?id=${competitionTopicId}`)}>
						{interactiveTopicName}
					</div>
					<div className="color-86909c">/</div>
					<div
						className="a color-86909c"
						onClick={() => linkTo(fromList ? -1 : `${linkToPath}/join/list?competitionTopicId=${competitionTopicId}`)}
					>
						报名列表
					</div>
					<div className="color-86909c">/</div>
					<div>报名详情</div>
				</Space>
			</div>
			<div id="section1"></div>
			{/* Tabs & 功能按钮 开始 */}
			<Affix>
				<div className="height-66 flex align-center justify-between bg-color-ffffff border-radius-8 padding-lr-24 border-solid-bottom-f2f3f5">
					<div className="flex align-center justify-between color-1d2129 line-height-26 font-size-18 font-weight-500">
						<Anchor
							affix={false}
							rootClassName="custom-anchor-box"
							direction="horizontal"
							replace
							targetOffset={80}
							items={[
								{
									key: 'section1',
									href: '#section1',
									title: <div className="margin-right-40 font-size-16 font-weight-500">基本信息</div>,
								},
							]}
						/>
					</div>
					<div className="flex align-center justify-between color-1d2129 line-height-26 font-size-18 font-weight-500"></div>
				</div>
			</Affix>
			{/* Tabs & 功能按钮 结束 */}

			{/* 基本信息 开始 */}
			<div className="margin-top-20 padding-20 border-radius-8 bg-color-ffffff">
				<div className="flex align-center justify-between margin-bottom-20">
					<div className="font-size-18 line-height-26 font-weight-500">基本信息</div>
					<Button
						type="link"
						icon={<EditOutlined />}
						onClick={() => {
							linkTo(`${linkToPath}/join/curd?competitionTopicId=${competitionTopicId}&id=${id}&fromList=1#section1`);
						}}
					>
						编辑信息
					</Button>
				</div>
				<div className="flex align-center justify-start font-size-14 line-height-22 padding-left-16 margin-bottom-20">
					<div className="color-86909c margin-right-4">赛事专题名称：</div>
					<div className="">{interactiveTopicName || '--'}</div>
				</div>
				<div className="flex align-center justify-start font-size-14 line-height-22 padding-left-16 margin-bottom-20">
					<div className="color-86909c margin-right-4">企业名称：</div>
					<div className="">{detail.name || '--'}</div>
				</div>
				<div className="flex align-center justify-start font-size-14 line-height-22 padding-left-16 margin-bottom-20">
					<div className="color-86909c margin-right-4">组别：</div>
					<div className="">{detail.groupName || '--'}</div>
				</div>
				<div className="flex align-center justify-start font-size-14 line-height-22 padding-left-16 margin-bottom-20">
					<div className="color-86909c margin-right-4">赛室名称：</div>
					<div className="">{detail.roomName || '--'}</div>
				</div>
				{/* {detail.scoreJson &&
					new Array(reviewQuantity).fill('').map((_, oi) => {
						return (
							<div key={oi} className="flex align-center justify-start font-size-14 line-height-22 padding-left-16 margin-bottom-20">
								<div className="color-86909c margin-right-4">评委{oi + 1}分数：</div>
								<div className="">{detail.scoreJson[oi] || '--'}</div>
							</div>
						);
					})} */}
				<div className="flex align-start justify-start font-size-14 line-height-22 padding-left-16 margin-bottom-20">
					<div className="color-86909c margin-right-4">成绩：</div>
					<div className="flex-sub">
						<div className={detail.totalScore !== null ? 'color-165dff' : 'color-86909c'}>
							{detail.totalScore !== null ? detail.totalScore || 0 : '未公布'}
						</div>
					</div>
				</div>
				<div className="flex align-center justify-start font-size-14 line-height-22 padding-left-16 margin-bottom-20">
					<div className="color-86909c margin-right-4">排序：</div>
					<div className="">{detail.rankingNum || '--'}</div>
				</div>
				<div className="flex align-start justify-start font-size-14 line-height-22 padding-left-16 margin-bottom-20">
					<div className="color-86909c margin-right-4">创建时间：</div>
					<div className="flex-sub">{(detail.createTime || '').slice(0, 16) || '--'}</div>
				</div>
			</div>
			{/* 基本信息 结束 */}
		</div>
	);
};

export default Index;
