import { useEffect, useState } from 'react';
import { Table, Button, Popconfirm, Space, Form, Affix, Input, Row, Col, Select } from 'antd';
import { useTableData } from '@/hook/useTableData';
import { useRouterLink } from '@/hook/useRouter';
import {
	getCompetitionTopicJoinInPage as getTablePageData,
	batchDelJoinIn as delTableItemData,
	updateRankingNumToJoinIn as batchUpdateSort,
	importJoinInExcelData as importTableData,
} from '@/api/Achv/TopicManage/Competition/Join/index';
import { getCompetitionTopicVo } from '@/api/Achv/TopicManage/Competition/index';

const Index = () => {
	const linkToPath = '/newAchv/topicManage/competition';
	const { linkTo, searchParams } = useRouterLink();
	const fromPage = searchParams.get('fromPage') ? 1 : '';
	const competitionTopicId = searchParams.get('competitionTopicId');
	const { form, dataSource, pagination, changePage, delTableData, onReset, onSearch, SortInput, importData, ImportModal } = useTableData({
		params: {
			competitionTopicId,
			fromPage,
		},
		getTablePageData,
		delTableItemData,
		batchUpdateSort,
		importTableData,
	});

	// 获取专题名称
	const [interactiveTopicName, setInteractiveTopicName] = useState('');
	const [groupData, setGroupData] = useState([]);
	useEffect(() => {
		if (competitionTopicId) {
			getCompetitionTopicVo({ id: competitionTopicId }).then((res) => {
				const { name, groupName } = res?.data || {};
				setInteractiveTopicName(name);
				setGroupData(
					(groupName || '')
						.split(',')
						.filter((ov) => ov)
						.map((ov) => {
							return {
								label: ov,
								value: ov,
							};
						})
				);
			});
		}
	}, [competitionTopicId]);

	return (
		<div className="">
			<div className="flex flex-shrink align-center margin-bottom-4 height-48 font-size-22 font-weight-500 color-1d2129">
				<Space size={10}>
					<div className="a color-86909c" onClick={() => linkTo(linkToPath)}>
						大赛查询管理
					</div>
					<div className="color-86909c">/</div>
					<div className="a color-86909c" onClick={() => linkTo(fromPage ? -1 : `${linkToPath}/detail?id=${competitionTopicId}`)}>
						{interactiveTopicName}
					</div>
					<div className="color-86909c">/</div>
					<div>报名管理</div>
				</Space>
			</div>

			{/* Tabs & 功能按钮 开始 */}
			<Affix offsetTop={0}>
				<div className="flex align-center justify-between padding-24 border-radius-8 bg-color-ffffff border-solid-bottom-f2f3f5">
					<div className="flex align-center"></div>
					<Space size={16}>
						<Button
							type="primary"
							onClick={() => {
								importData();
							}}
						>
							导入报名名单
						</Button>
					</Space>
				</div>
			</Affix>
			{/* Tabs & 功能按钮 结束 */}

			<div className="flex-sub margin-top-18 padding-24 border-radius-8 bg-color-ffffff">
				{/* 筛选条件 开始 */}
				<div className="flex justify-between margin-bottom-18">
					<div className="flex-sub">
						<Form form={form} labelAlign="right" layout="inline">
							<Row className="width-100per" gutter={[12, 12]}>
								<Col span={12}>
									<Form.Item name="name" label="企业名称">
										<Input placeholder="请输入企业名称" />
									</Form.Item>
								</Col>
								<Col span={12}>
									<Form.Item name="groupName" label="组别">
										<Select options={groupData} placeholder="请选择组别" />
									</Form.Item>
								</Col>
							</Row>
						</Form>
					</div>
					<div className="margin-left-12 padding-left-12 border-solid-left-e5e6eb">
						<Space size={16}>
							<Button onClick={onReset}>重置</Button>
							<Button type="primary" onClick={onSearch}>
								查询
							</Button>
						</Space>
					</div>
				</div>
				{/* 筛选条件 结束 */}

				{/* 表格 开始 */}
				<Table rowKey="id" dataSource={dataSource} pagination={pagination} onChange={changePage} scroll={{ x: 'max-content' }}>
					<Table.Column
						title="序号"
						key="index"
						width={60}
						align="center"
						render={(_, __, index) => {
							return index + 1;
						}}
					/>
					<Table.Column title="企业名称" dataIndex="name" />
					<Table.Column title="赛室名称" dataIndex="roomName" />
					<Table.Column title="组别" dataIndex="groupName" />
					<Table.Column
						title="成绩"
						dataIndex="totalScore"
						render={(text) => (
							<div className={text !== null ? 'color-165dff' : 'color-86909c'}>{text !== null ? text || 0 : '未公布'}</div>
						)}
					/>
					<Table.Column
						title="排序"
						align="center"
						dataIndex="rankingNum"
						render={(_, record) => {
							return <SortInput record={record} />;
						}}
					/>
					<Table.Column
						title="操作"
						key="option"
						dataIndex="option"
						align="center"
						fixed="right"
						width="160px"
						render={(_, record) => {
							return (
								<>
									<Button
										type="link"
										size="small"
										onClick={() =>
											linkTo(`${linkToPath}/join/detail?competitionTopicId=${competitionTopicId}&id=${record.id}&fromList=1`)
										}
									>
										详情/编辑
									</Button>
									{/* <Popconfirm
										title="提示"
										description="确定删除吗？"
										onConfirm={() => {
											delTableData(record.id);
										}}
										okText="确定"
										cancelText="取消"
									>
										<Button type="link" size="small">
											删除
										</Button>
									</Popconfirm> */}
								</>
							);
						}}
					/>
				</Table>
				{/* 表格 结束 */}

				{/* 导入 开始 */}
				<ImportModal
					tplUrl="https://gbac-produce.oss-cn-shenzhen.aliyuncs.com/static-assets/Template/%E8%B5%9B%E4%BA%8B%E6%8A%A5%E5%90%8D%E5%AF%BC%E5%85%A5%E6%A8%A1%E7%89%88.xlsx"
					customParams={{ competitionTopicId }}
				/>
				{/* 导入 结束 */}
			</div>
		</div>
	);
};

export default Index;
