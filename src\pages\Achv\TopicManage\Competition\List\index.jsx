import { useEffect, useState } from 'react';
import { Table, Button, Popconfirm, Space, Form, Affix } from 'antd';
import { useTableData } from '@/hook/useTableData';
import { useRouterLink } from '@/hook/useRouter';
import {
	getCompetitionTopicPage as getTablePageData,
	batchDel as delTableItemData,
	updateRankingNum as batchUpdateSort,
	getStatistics,
} from '@/api/Achv/TopicManage/Competition';
import { auditStatusData, auditStatusTextList } from '@/pages/Achv/config';

const Index = () => {
	const linkToPath = '/newAchv/topicManage/competition';
	const { linkTo, searchParams } = useRouterLink();

	const status = searchParams.get('auditStatus');
	const [auditStatus, setAuditStatus] = useState(status === null ? 3 : status - 0 || '');

	const { form, dataSource, pagination, changePage, delTableData, SortInput } = useTableData({
		params: {
			auditStatus,
		},
		getTablePageData,
		delTableItemData,
		batchUpdateSort,
	});

	// 获取统计数据
	const [statistics, setStatistics] = useState({});
	const getStatisticsData = () => {
		getStatistics({}).then((res) => {
			setStatistics(res.data || {});
		});
	};

	useEffect(() => {
		getStatisticsData();
	}, []);

	return (
		<div className="">
			<div className="flex flex-shrink align-center margin-bottom-4 height-48 font-size-22 font-weight-500 color-1d2129">大赛查询管理</div>

			{/* Tabs & 功能按钮 开始 */}
			<Affix offsetTop={0}>
				<div className="flex align-center justify-between padding-24 border-radius-8 bg-color-ffffff border-solid-bottom-f2f3f5">
					<div className="flex align-center">
						{auditStatusData.map((ov) => (
							<div
								key={ov.value}
								className={`a margin-right-40 font-size-18 font-weight-500 ${auditStatus === ov.value ? 'color-165dff' : ''}`}
								onClick={() => setAuditStatus(ov.value)}
							>
								{ov.label}
								{statistics[ov.countName] > 0 ? `（${statistics[ov.countName]}）` : ''}
							</div>
						))}
					</div>
					<Space size={16}>
						<Button
							type="primary"
							onClick={() => {
								linkTo(`${linkToPath}/curd`);
							}}
						>
							创建赛事专题
						</Button>
					</Space>
				</div>
			</Affix>
			{/* Tabs & 功能按钮 结束 */}

			<div className="flex-sub margin-top-18 padding-24 border-radius-8 bg-color-ffffff">
				<Form form={form}></Form>

				{/* 表格 开始 */}
				<Table rowKey="id" dataSource={dataSource} pagination={pagination} onChange={changePage} scroll={{ x: 'max-content' }}>
					<Table.Column
						title="序号"
						key="index"
						width={60}
						align="center"
						render={(_, __, index) => {
							return index + 1;
						}}
					/>
					<Table.Column title="赛事专题名称" dataIndex="name" />
					<Table.Column
						title="发布状态"
						dataIndex="auditStatus"
						render={(text) => {
							return (
								<div className={`tag-status-small-${['', 'primary', 'warning', '700eb2', 'success'][text]}`}>
									{auditStatusTextList[text] || '--'}
								</div>
							);
						}}
					/>
					<Table.Column
						title="排序"
						align="center"
						dataIndex="rankingNum"
						render={(_, record) => {
							return <SortInput record={record} />;
						}}
					/>
					<Table.Column title="创建时间" dataIndex="createTime" render={(text) => (text || '').slice(0, 16)} />
					<Table.Column
						title="操作"
						key="option"
						dataIndex="option"
						align="center"
						fixed="right"
						width="260px"
						render={(_, record) => {
							return (
								<>
									<Button
										type="link"
										size="small"
										onClick={() => linkTo(`${linkToPath}/join/list?competitionTopicId=${record.id}&fromList=1`)}
									>
										查看报名
									</Button>
									<Button type="link" size="small" onClick={() => linkTo(`${linkToPath}/detail?id=${record.id}&fromList=1`)}>
										详情/编辑
									</Button>
									<Popconfirm
										title="提示"
										description="确定删除吗？"
										onConfirm={() => {
											delTableData(record.id);
										}}
										okText="确定"
										cancelText="取消"
									>
										<Button type="link" size="small">
											删除
										</Button>
									</Popconfirm>
								</>
							);
						}}
					/>
				</Table>
				{/* 表格 结束 */}
			</div>
		</div>
	);
};

export default Index;
