import { useEffect, useState } from 'react';
import { useRouterLink } from '@/hook/useRouter';
import { Button, Space, Form, Input, message, Affix, Radio } from 'antd';
import UploadImg from '@/components/UploadImg';
import { ApplyFormConfig } from '@/pages/Achv/Event/Curd/index';
import { saveFormData, updateFormData, getFormData } from '@/api/Achv/TopicManage/QuestionManage';

const Index = () => {
	const { linkTo, searchParams } = useRouterLink();
	const id = searchParams.get('id') || '';
	const [form] = Form.useForm();
	const contactQrCode = Form.useWatch('contactQrCode', form);
	const fillInAuditStatus = Form.useWatch('fillInAuditStatus', form);

	// 跳转地址
	const linkToPath = '/newAchv/topicManage/questionManage';

	// 禁止编辑发布类型
	const [hash, setHash] = useState('');

	// 取消
	const onCancel = () => {
		linkTo(-1);
	};

	// 提交
	const onSubmit = () => {
		form.validateFields()
			.then((values) => {
				values.enrollDesignJson = JSON.stringify(values.enrollDesignJson);

				(values.id ? updateFormData : saveFormData)(values).then(() => {
					message.success(values.id ? '修改成功' : '添加成功');
					setTimeout(() => {
						linkTo(-1);
					}, 500);
				});
			})
			.catch((error) => {
				console.log(error);
			});
	};

	// 获取详情
	const getDetail = () => {
		if (id) {
			getFormData({ id }).then((res) => {
				const resData = res.data || {};
				resData.enrollDesignJson = JSON.parse(resData.enrollDesignJson || '[]');
				form.setFieldsValue(resData);
			});
		}
	};

	useEffect(() => {
		const hash = window.location.hash.replace('#', '');
		setHash(hash || 'section1');

		getDetail();
	}, []);

	// 跳转到指定锚点
	useEffect(() => {
		if (hash) {
			setTimeout(() => {
				const element = document.getElementById(hash);
				if (element) {
					element.scrollIntoView({ behavior: 'smooth' });
				}
			}, 500);
		}
	}, [hash]);

	return (
		<div className="">
			<div className="flex flex-shrink align-center margin-bottom-4 height-48 font-size-22 font-weight-500 color-1d2129">
				<Space size={10}>
					<div className="a color-86909c" onClick={() => linkTo(linkToPath)}>
						问卷管理
					</div>
					<div className="color-86909c">/</div>
					<div>{id ? '问卷编辑' : '新增问卷'}</div>
				</Space>
			</div>

			{/* Tabs & 功能按钮 开始 */}
			<div id="section1"></div>
			<Affix offsetTop={0}>
				<div className="flex align-center justify-between padding-24 border-radius-8 border-solid-bottom-f2f2f2 bg-color-ffffff">
					<div className="flex align-center">
						{['基本信息', '结果页配置', '表单配置'].map((ov, oi) => {
							const currentHash = `section${oi + 1}`;
							return (
								<div
									key={oi}
									className={`a margin-right-40 font-size-18 font-weight-500 ${hash === currentHash ? 'color-165dff' : ''}`}
									onClick={() => {
										setHash(currentHash);
									}}
								>
									{ov}
								</div>
							);
						})}
					</div>
					<Space size={16}>
						<Button onClick={onCancel}>取消</Button>
						<Button type="primary" onClick={onSubmit}>
							保存
						</Button>
					</Space>
				</div>
			</Affix>
			{/* Tabs & 功能按钮 结束 */}

			<Form form={form} className="antd-form-box" labelAlign="right" labelCol={{ style: { width: '120px' } }}>
				<Form.Item hidden name="id">
					<Input />
				</Form.Item>
				<Form.Item hidden name="rankingNum">
					<Input />
				</Form.Item>
				{/* 基本信息 开始 */}
				<div className="margin-top-20 padding-20 border-radius-8 bg-color-ffffff">
					<div className="padding-bottom-30 line-height-26 font-size-18 font-weight-500 color-1d2129">基本信息</div>
					<Form.Item label="问卷标题" name="title" rules={[{ required: true, message: '请输入问卷标题' }]}>
						<Input className="input-box" placeholder="请输入问卷标题" />
					</Form.Item>
					<Form.Item label="是否登录" name="loginStatus" required initialValue={0}>
						<Radio.Group
							options={[
								{ label: '否', value: 0 },
								{ label: '是', value: 1 },
							]}
						/>
					</Form.Item>
					<Form.Item label="是否审核" name="fillInAuditStatus" required initialValue={0}>
						<Radio.Group
							options={[
								{ label: '否', value: 0 },
								{ label: '是', value: 1 },
							]}
						/>
					</Form.Item>
					{fillInAuditStatus === 0 ? (
						<Form.Item label="重复填写" name="repeatFillInStatus" required initialValue={0}>
							<Radio.Group
								options={[
									{ label: '限制填一次', value: 0 },
									{ label: '不限制', value: 1 },
								]}
							/>
						</Form.Item>
					) : (
						<Form.Item label="审核消息推送" name="weChatSubscribeStatus" required initialValue={0}>
							<Radio.Group
								options={[
									{ label: '否', value: 0 },
									{ label: '是', value: 1 },
								]}
							/>
						</Form.Item>
					)}
					<Form.Item label="提交按钮文案" name="submitButtonDesc">
						<Input className="input-box" placeholder="请输入提交按钮文案 默认：提交申请" />
					</Form.Item>
					<Form.Item label="展示二维码" name="contactQrCode">
						<UploadImg size={5} width={120} height={120} cropperProps={{ width: 120, height: 120 }} />
					</Form.Item>
					{contactQrCode && (
						<Form.Item label="联系文案" name="contactDesc" rules={[{ required: true, message: '请输入联系文案' }]}>
							<Input className="input-box" placeholder="请输入提交完成标题 例：长按添加，咨询详情" />
						</Form.Item>
					)}
					<Form.Item label="表单头部提示语" name="introduce">
						<Input className="input-box" placeholder="请输入提示语" />
					</Form.Item>
					<Form.Item label="分享文案" name="shareDesc">
						<Input className="input-box" placeholder="请输入分享文案" />
					</Form.Item>
					<Form.Item label="分享朋友圈封面" name="timelineShareCoverUrl">
						<UploadImg size={5} width={120} height={120} cropperProps={{ width: 120, height: 120 }} />
					</Form.Item>
					<Form.Item label="分享好友封面" name="friendShareCoverUrl">
						<UploadImg size={5} width={150} height={120} cropperProps={{ width: 150, height: 120 }} />
					</Form.Item>
				</div>
				{/* 基本信息 结束 */}
				{/* 结果页配置 开始 */}
				<div className="margin-top-20 padding-20 border-radius-8 bg-color-ffffff">
					<div className="padding-bottom-30 line-height-26 font-size-18 font-weight-500 color-1d2129">结果页配置</div>
					{fillInAuditStatus === 1 ? (
						<>
							<Form.Item label="待审核标题" name="waitAuditTitle">
								<Input className="input-box" placeholder="请输入待审核标题 默认：提交成功" />
							</Form.Item>
							<Form.Item label="待审核文案" name="waitAuditDesc" rules={[{ required: true, message: '请输入提交完成文案' }]}>
								<Input.TextArea
									rows={4}
									className="input-box"
									placeholder="请输入待审核文案 例：我们将尽快完成审核，结果请留意短信通知。"
								/>
							</Form.Item>
							<Form.Item label="审核通过标题" name="submitFinishTitle">
								<Input className="input-box" placeholder="请输入审核通过标题 默认：提交成功" />
							</Form.Item>
							<Form.Item label="审核通过文案" name="submitFinishDesc" rules={[{ required: true, message: '请输入审核通过文案' }]}>
								<Input.TextArea
									rows={4}
									className="input-box"
									placeholder="请输入审核通过文案 例：您的产品材料我们已收到，如合适我们将有专人与您联系，请耐心等候。"
								/>
							</Form.Item>
							<Form.Item label="审核不通过标题" name="notPassTitle">
								<Input className="input-box" placeholder="请输入审核不通过标题 默认：审核不通过" />
							</Form.Item>
							<Form.Item label="审核不通过文案" name="notPassDesc" rules={[{ required: true, message: '请输入提交完成文案' }]}>
								<Input.TextArea
									rows={4}
									className="input-box"
									placeholder="请输入审核不通过文案 例：很抱歉，您申请报名未通过审核，您可以尝试重新申请。"
								/>
							</Form.Item>
						</>
					) : (
						<>
							<Form.Item label="提交完成标题" name="submitFinishTitle">
								<Input className="input-box" placeholder="请输入提交完成标题 默认：提交成功" />
							</Form.Item>
							<Form.Item label="提交完成文案" name="submitFinishDesc" rules={[{ required: true, message: '请输入提交完成文案' }]}>
								<Input.TextArea
									rows={4}
									className="input-box"
									placeholder="请输入提交完成文案 例：您的产品材料我们已收到，如合适我们将有专人与您联系，请耐心等候。"
								/>
							</Form.Item>
						</>
					)}
					<Form.Item label="结果页按钮文案" name="resultBtnDesc">
						<Input className="input-box" placeholder="请输入提交完成标题 默认：返回平台首页" />
					</Form.Item>
					<Form.Item label="按钮跳转地址" name="resultBtnPath">
						<Input className="input-box" placeholder="请输入提交完成标题 默认：/pages/Home/index" />
					</Form.Item>
				</div>
				{/* 结果页配置 结束 */}
				{/* 表单配置 开始 */}
				<div id="section3"></div>
				<div className="margin-top-20 padding-20 border-radius-8 bg-color-ffffff">
					<div className="padding-bottom-30 line-height-26 font-size-18 font-weight-500 color-1d2129">表单配置</div>
					<Form.Item
						name="enrollDesignJson"
						rules={[{ required: true, type: 'array', message: '请填写表单配置' }]}
						initialValue={[
							{
								type: 1,
								isBase: false,
								required: true,
								lable: '姓名',
								placeholder: '请输入姓名',
								keyName: 'name',
							},
							{
								type: 1,
								isBase: false,
								required: true,
								lable: '手机',
								placeholder: '请输入手机',
								keyName: 'phone',
							},
						]}
					>
						<ApplyFormConfig hideTitle />
					</Form.Item>
				</div>
				{/* 表单配置 结束 */}
			</Form>
		</div>
	);
};
export default Index;
