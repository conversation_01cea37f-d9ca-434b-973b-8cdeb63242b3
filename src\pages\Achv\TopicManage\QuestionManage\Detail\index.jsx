import { useEffect, useState, useRef, forwardRef, useImperativeHandle } from 'react';
import { useRouterLink } from '@/hook/useRouter';

import { Button, Affix, Anchor, Form, Space, Modal, message, Image, Input } from 'antd';
import { EditOutlined } from '@ant-design/icons';
import ModalForm from '@/components/ModalForm';
import { FormItem } from '@/pages/Achv/Event/Detail/index';

import { auditFormData, getFormData } from '@/api/Achv/TopicManage/QuestionManage';
import { auditStatusTextList } from '@/pages/Achv/config';

const Index = () => {
	const ModalFormRef = useRef();
	const { linkTo, searchParams } = useRouterLink();
	const id = searchParams.get('id') || '';
	const fromPage = !!searchParams.get('fromPage');

	const [detail, setDetail] = useState({});

	// 跳转地址
	const linkToPath = '/newAchv/topicManage/questionManage';

	// 获取详情
	const getDetail = () => {
		if (id) {
			getFormData({ id }).then((res) => {
				const resData = res.data || {};
				resData.enrollDesignJson = JSON.parse(resData.enrollDesignJson || '[]');
				setDetail(resData);
			});
		}
	};

	// 通过审核
	const handlePass = () => {
		Modal.confirm({
			title: '需求审核',
			content: `是否通过审核？`,
			onOk() {
				handleAudit({ status: 3 });
			},
		});
	};

	// 撤销审核
	const handleRevoke = () => {
		Modal.confirm({
			title: '撤销审核',
			content: `是否撤销审核结果？`,
			onOk() {
				handleAudit({ status: 1 });
			},
		});
	};

	// 审核操作
	const handleAudit = (values) => {
		auditFormData({ ...values, id }).then(() => {
			message.success('操作成功');

			setTimeout(() => {
				linkTo(-1);
			}, 500);
		});
	};

	useEffect(() => {
		getDetail();
	}, []);

	return (
		<div className="">
			<div className="flex flex-shrink align-center margin-bottom-4 height-48 font-size-22 font-weight-500 color-1d2129">
				<Space size={10}>
					<div className="a color-86909c" onClick={() => linkTo(fromPage ? -1 : linkToPath)}>
						问卷管理
					</div>
					<div className="color-86909c">/</div>
					<div>问卷详情</div>
				</Space>
			</div>
			<div id="section1"></div>
			{/* Tabs & 功能按钮 开始 */}
			<Affix>
				<div className="height-66 flex align-center justify-between bg-color-ffffff border-radius-8 padding-lr-24 border-solid-bottom-f2f3f5">
					<div className="flex align-center justify-between color-1d2129 line-height-26 font-size-18 font-weight-500">
						<Anchor
							affix={false}
							rootClassName="custom-anchor-box"
							direction="horizontal"
							replace
							targetOffset={80}
							items={[
								{
									key: 'section1',
									href: '#section1',
									title: <div className="margin-right-40 font-size-16 font-weight-500">基本信息</div>,
								},
								{
									key: 'section2',
									href: '#section2',
									title: <div className="margin-right-40 font-size-16 font-weight-500">结果页配置</div>,
								},
								{
									key: 'section3',
									href: '#section3',
									title: <div className="margin-right-40 font-size-16 font-weight-500">表单配置</div>,
								},
							]}
						/>
					</div>
					<div className="flex align-center justify-between color-1d2129 line-height-26 font-size-18 font-weight-500">
						{detail.auditStatus === 1 ? (
							<>
								<Button
									type="primary"
									danger
									ghost
									onClick={() => {
										ModalFormRef.current.setOpen(true);
										ModalFormRef.current.setTitle('不通过原因');
									}}
									className="height-40 width-104"
								>
									不通过
								</Button>
								<Button type="primary" onClick={handlePass} className="margin-left-20 height-40 width-104">
									通过审核
								</Button>
							</>
						) : (
							<Button onClick={handleRevoke} className="margin-left-20 height-40 width-104">
								撤销审核
							</Button>
						)}
					</div>
				</div>
			</Affix>
			{/* Tabs & 功能按钮 结束 */}

			{/* 基本信息 开始 */}
			<div className="margin-top-20 padding-20 border-radius-8 bg-color-ffffff">
				<div className="flex align-center justify-between margin-bottom-20">
					<div className="font-size-18 line-height-26 font-weight-500">基本信息</div>
					<Button
						type="link"
						icon={<EditOutlined />}
						onClick={() => {
							linkTo(`${linkToPath}/curd?id=${id}#section1`);
						}}
					>
						编辑信息
					</Button>
				</div>
				<div className="flex align-center justify-start font-size-14 line-height-22 padding-left-16 margin-bottom-20">
					<div className="color-86909c margin-right-4">审核状态：</div>
					<div className="">
						<div className={`tag-status-${['default', 'warning', 'error', 'primary'][detail.auditStatus]}`}>
							{auditStatusTextList[detail.auditStatus] || ''}
						</div>
					</div>
				</div>
				{detail.auditStatus === 2 && (
					<div className="flex align-center justify-start font-size-14 line-height-22 padding-left-16 margin-bottom-20">
						<div className="color-86909c margin-right-4">拒绝原因：</div>
						<div className="">{detail.reason || '无'}</div>
					</div>
				)}
				<div className="flex align-center justify-start font-size-14 line-height-22 padding-left-16 margin-bottom-20">
					<div className="color-86909c margin-right-4">问卷标题：</div>
					<div className="">{detail.title || '--'}</div>
				</div>
				<div className="flex align-center justify-start font-size-14 line-height-22 padding-left-16 margin-bottom-20">
					<div className="color-86909c margin-right-4">是否登录：</div>
					<div className="">
						<div className={`tag-status-${['warning', 'primary'][detail.loginStatus]}`}>{['否', '是'][detail.loginStatus] || ''}</div>
					</div>
				</div>
				<div className="flex align-center justify-start font-size-14 line-height-22 padding-left-16 margin-bottom-20">
					<div className="color-86909c margin-right-4">是否审核：</div>
					<div className="">
						<div className={`tag-status-${['warning', 'primary'][detail.fillInAuditStatus]}`}>
							{['否', '是'][detail.fillInAuditStatus] || ''}
						</div>
					</div>
				</div>
				{detail.fillInAuditStatus === 0 ? (
					<div className="flex align-center justify-start font-size-14 line-height-22 padding-left-16 margin-bottom-20">
						<div className="color-86909c margin-right-4">重复填写：</div>
						<div className="">
							<div className={`tag-status-${['warning', 'primary'][detail.repeatFillInStatus]}`}>
								{['限制填一次', '不限制'][detail.repeatFillInStatus] || ''}
							</div>
						</div>
					</div>
				) : (
					<div className="flex align-center justify-start font-size-14 line-height-22 padding-left-16 margin-bottom-20">
						<div className="color-86909c margin-right-4">审核消息推送：</div>
						<div className="">
							<div className={`tag-status-${['warning', 'primary'][detail.weChatSubscribeStatus]}`}>
								{['否', '是'][detail.weChatSubscribeStatus] || ''}
							</div>
						</div>
					</div>
				)}
				<div className="flex align-center justify-start font-size-14 line-height-22 padding-left-16 margin-bottom-20">
					<div className="color-86909c margin-right-4">提交按钮文案：</div>
					<div className="">{detail.submitButtonDesc || '--'}</div>
				</div>
				<div className="flex align-center justify-start font-size-14 line-height-22 padding-left-16 margin-bottom-20">
					<div className="color-86909c margin-right-4">展示二维码：</div>
					<div className="flex-sub">{(detail.contactQrCode && <Image width={120} src={detail.contactQrCode} />) || '--'}</div>
				</div>
				<div className="flex align-center justify-start font-size-14 line-height-22 padding-left-16 margin-bottom-20">
					<div className="color-86909c margin-right-4">联系文案：</div>
					<div className="">{detail.contactDesc || '--'}</div>
				</div>
				<div className="flex align-center justify-start font-size-14 line-height-22 padding-left-16 margin-bottom-20">
					<div className="color-86909c margin-right-4">表单头部提示语：</div>
					<div className="">{detail.introduce || '--'}</div>
				</div>
				<div className="flex align-center justify-start font-size-14 line-height-22 padding-left-16 margin-bottom-20">
					<div className="color-86909c margin-right-4">分享文案：</div>
					<div className="">{detail.shareDesc || '--'}</div>
				</div>
				<div className="flex justify-start font-size-14 line-height-22 padding-left-16 margin-bottom-20">
					<div className="flex-shrink color-86909c margin-right-4">分享朋友圈封面：</div>
					<div className="flex-sub">
						{(detail.timelineShareCoverUrl && <Image width={120} src={detail.timelineShareCoverUrl} />) || '--'}
					</div>
				</div>
				<div className="flex justify-start font-size-14 line-height-22 padding-left-16 margin-bottom-20">
					<div className="flex-shrink color-86909c margin-right-4">分享好友封面：</div>
					<div className="flex-sub">{(detail.friendShareCoverUrl && <Image width={120} src={detail.friendShareCoverUrl} />) || '--'}</div>
				</div>
				<div className="flex align-center justify-start font-size-14 line-height-22 padding-left-16 margin-bottom-20">
					<div className="color-86909c margin-right-4">排序：</div>
					<div className="">{detail.rankingNum || '--'}</div>
				</div>
				<div className="flex align-start justify-start font-size-14 line-height-22 padding-left-16 margin-bottom-20">
					<div className="color-86909c margin-right-4">创建时间：</div>
					<div className="flex-sub">{(detail.createTime || '').slice(0, 16) || '--'}</div>
				</div>
			</div>
			{/* 基本信息 结束 */}

			{/* 结果页配置 开始 */}
			<div id="section2"></div>
			<div className="margin-top-20 padding-20 border-radius-8 bg-color-ffffff">
				<div className="flex align-center justify-between margin-bottom-20">
					<div className="font-size-18 line-height-26 font-weight-500">结果页配置</div>
					<Button
						type="link"
						icon={<EditOutlined />}
						onClick={() => {
							linkTo(`${linkToPath}/curd?id=${id}#section2`);
						}}
					>
						编辑信息
					</Button>
				</div>
				{detail.fillInAuditStatus === 1 ? (
					<>
						<div className="flex align-center justify-start font-size-14 line-height-22 padding-left-16 margin-bottom-20">
							<div className="color-86909c margin-right-4">待审核标题：</div>
							<div className="">{detail.waitAuditTitle || '提交成功'}</div>
						</div>
						<div className="flex align-center justify-start font-size-14 line-height-22 padding-left-16 margin-bottom-20">
							<div className="color-86909c margin-right-4">待审核文案：</div>
							<div className="">{detail.waitAuditDesc || '--'}</div>
						</div>
						<div className="flex align-center justify-start font-size-14 line-height-22 padding-left-16 margin-bottom-20">
							<div className="color-86909c margin-right-4">审核通过标题：</div>
							<div className="">{detail.submitFinishTitle || '提交成功'}</div>
						</div>
						<div className="flex align-center justify-start font-size-14 line-height-22 padding-left-16 margin-bottom-20">
							<div className="color-86909c margin-right-4">审核通过文案：</div>
							<div className="">{detail.submitFinishDesc || '--'}</div>
						</div>
						<div className="flex align-center justify-start font-size-14 line-height-22 padding-left-16 margin-bottom-20">
							<div className="color-86909c margin-right-4">审核不通过标题：</div>
							<div className="">{detail.notPassTitle || '审核不通过'}</div>
						</div>
						<div className="flex align-center justify-start font-size-14 line-height-22 padding-left-16 margin-bottom-20">
							<div className="color-86909c margin-right-4">审核不通过文案：</div>
							<div className="">{detail.notPassDesc || '--'}</div>
						</div>
					</>
				) : (
					<>
						<div className="flex align-center justify-start font-size-14 line-height-22 padding-left-16 margin-bottom-20">
							<div className="color-86909c margin-right-4">提交完成标题：</div>
							<div className="">{detail.submitFinishTitle || '提交成功'}</div>
						</div>
						<div className="flex align-center justify-start font-size-14 line-height-22 padding-left-16 margin-bottom-20">
							<div className="color-86909c margin-right-4">提交完成文案：</div>
							<div className="">{detail.submitFinishDesc || '--'}</div>
						</div>
					</>
				)}
				<div className="flex align-center justify-start font-size-14 line-height-22 padding-left-16 margin-bottom-20">
					<div className="color-86909c margin-right-4">结果页按钮文案：</div>
					<div className="">{detail.resultBtnDesc || '返回平台首页'}</div>
				</div>
				<div className="flex align-center justify-start font-size-14 line-height-22 padding-left-16 margin-bottom-20">
					<div className="color-86909c margin-right-4">按钮跳转地址：</div>
					<div className="">{detail.resultBtnPath || '/pages/Home/index'}</div>
				</div>
			</div>
			{/* 结果页配置 结束 */}

			{/* 表单配置 开始 */}
			<div id="section3"></div>
			<div className="margin-top-20 padding-20 border-radius-8 bg-color-ffffff">
				<div className="flex align-center justify-between margin-bottom-20">
					<div className="font-size-18 line-height-26 font-weight-500">表单配置</div>
					<Button
						type="link"
						icon={<EditOutlined />}
						onClick={() => {
							linkTo(`${linkToPath}/curd?id=${id}#section3`);
						}}
					>
						编辑信息
					</Button>
				</div>
				<Space direction="vertical">
					{(detail.enrollDesignJson || []).map((ov, oi) => {
						return <FormItem key={oi} {...ov} />;
					})}
				</Space>
			</div>
			{/* 表单配置 结束 */}

			{/* 询问弹窗 开始 */}
			<ModalForm ref={ModalFormRef} onOk={handleAudit} FormComp={(props) => <ConrirmForm ref={props.FormCompRef} />} />
			{/* 询问弹窗 结束 */}
		</div>
	);
};

// 询问表单
const ConrirmForm = forwardRef((props = {}, ref) => {
	const [form] = Form.useForm();

	useImperativeHandle(ref, () => {
		return {
			onCancel: () => Promise.resolve(),
			onOk: () => {
				return new Promise((resolve) => {
					form.validateFields().then((values) => {
						resolve(values);
					});
				});
			},
		};
	});

	return (
		<Form form={form} labelCol={{ style: { width: '100px' } }} initialValues={{ status: 2 }}>
			<Form.Item hidden name="status">
				<Input />
			</Form.Item>
			<Form.Item label="原因" name="reason" rules={[{ required: true, message: '请输入不通过原因' }]}>
				<Input.TextArea rows={4} placeholder="请输入不通过原因" />
			</Form.Item>
		</Form>
	);
});

export default Index;
