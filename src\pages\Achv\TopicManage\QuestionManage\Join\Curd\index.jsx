import { useEffect, useState } from 'react';
import { useRouterLink } from '@/hook/useRouter';
import { Button, Space, Form, Input, message, Affix, InputNumber, Select } from 'antd';

import { saveFormDataFillIn, updateFormDataFillIn, getFormDataFillInVo } from '@/api/Achv/TopicManage/QuestionManage/Join';
import { getFormData } from '@/api/Achv/TopicManage/QuestionManage/index';

const Index = () => {
	const { linkTo, searchParams } = useRouterLink();
	const id = searchParams.get('id') || '';
	const formDataId = searchParams.get('formDataId') || '';
	const fromPage = !!searchParams.get('fromPage');

	const [form] = Form.useForm();

	// 跳转地址
	const linkToPath = '/newAchv/topicManage/questionManage';

	// 禁止编辑发布类型
	const [hash, setHash] = useState('');

	// 取消
	const onCancel = () => {
		linkTo(-1);
	};

	// 提交
	const onSubmit = () => {
		form.validateFields()
			.then((values) => {
				const params = { formDataId, ...values };
				// params.scoreJson = params.scoreJson.filter((ov) => ov).join(',');
				(values.id ? updateFormDataFillIn : saveFormDataFillIn)(params).then(() => {
					message.success(values.id ? '修改成功' : '添加成功');
					setTimeout(() => {
						linkTo(-1);
					}, 500);
				});
			})
			.catch((error) => {
				console.log(error);
			});
	};

	// 获取详情
	const [name, setName] = useState('');
	const getDetail = () => {
		if (id) {
			getFormDataFillInVo({ id }).then((res) => {
				const resData = res.data || {};
				// resData.scoreJson = (resData.scoreJson || '').split(',').filter((ov) => ov);
				setName(resData.name);
				form.setFieldsValue(resData);
			});
		}
	};

	useEffect(() => {
		const hash = window.location.hash.replace('#', '');
		setHash(hash || 'section1');

		getDetail();
	}, []);

	// 跳转到指定锚点
	useEffect(() => {
		if (hash) {
			setTimeout(() => {
				const element = document.getElementById(hash);
				if (element) {
					element.scrollIntoView({ behavior: 'smooth' });
				}
			}, 500);
		}
	}, [hash]);

	// 获取问卷标题
	const [interactiveTopicName, setInteractiveTopicName] = useState('');
	const [groupData, setGroupData] = useState([]);
	useEffect(() => {
		if (formDataId && name) {
			getFormData({ id: formDataId }).then((res) => {
				const { title, groupName } = res?.data || {};
				setInteractiveTopicName(title);
				setGroupData(
					(groupName || '')
						.split(',')
						.filter((ov) => ov)
						.map((ov) => {
							return {
								label: ov,
								value: ov,
							};
						})
				);
			});
		}
	}, [formDataId, name]);

	return (
		<div className="">
			<div className="flex flex-shrink align-center margin-bottom-4 height-48 font-size-22 font-weight-500 color-1d2129">
				<Space size={10}>
					<div className="a color-86909c" onClick={() => linkTo(linkToPath)}>
						问卷管理
					</div>
					<div className="color-86909c">/</div>
					<div
						className="a color-86909c max-width-200 text-cut"
						title={interactiveTopicName}
						onClick={() => linkTo(fromPage ? -1 : `${linkToPath}/detail?id=${formDataId}`)}
					>
						{interactiveTopicName}
					</div>
					<div className="color-86909c">/</div>
					<div className="a color-86909c" onClick={() => linkTo(fromPage ? -1 : `${linkToPath}/join/list?formDataId=${formDataId}`)}>
						报名详情
					</div>
					<div className="color-86909c">/</div>
					<div>{id ? '报名编辑' : '新增报名'}</div>
				</Space>
			</div>

			{/* Tabs & 功能按钮 开始 */}
			<div id="section1"></div>
			<Affix offsetTop={0}>
				<div className="flex align-center justify-between padding-24 border-radius-8 border-solid-bottom-f2f2f2 bg-color-ffffff">
					<div className="flex align-center">
						{['基本信息'].map((ov, oi) => {
							const currentHash = `section${oi + 1}`;
							return (
								<div
									key={oi}
									className={`a margin-right-40 font-size-18 font-weight-500 ${hash === currentHash ? 'color-165dff' : ''}`}
								>
									{ov}
								</div>
							);
						})}
					</div>
					<Space size={16}>
						<Button onClick={onCancel}>取消</Button>
						<Button type="primary" onClick={onSubmit}>
							保存
						</Button>
					</Space>
				</div>
			</Affix>
			{/* Tabs & 功能按钮 结束 */}

			<Form form={form} className="antd-form-box" labelAlign="right" labelCol={{ style: { width: '120px' } }}>
				<Form.Item hidden name="id">
					<Input />
				</Form.Item>
				<Form.Item hidden name="rankingNum">
					<Input />
				</Form.Item>
				{/* 基本信息 开始 */}
				<div className="margin-top-20 padding-20 border-radius-8 bg-color-ffffff">
					<div className="padding-bottom-30 line-height-26 font-size-18 font-weight-500 color-1d2129">基本信息</div>
					<Form.Item label="问卷标题">
						<div>{interactiveTopicName}</div>
					</Form.Item>
					<Form.Item label="企业名称">
						<div>{name}</div>
					</Form.Item>
					<Form.Item label="组别" name="groupName" rules={[{ required: true, message: '请选择组别' }]}>
						<Select placeholder="请选择组别" options={groupData} />
					</Form.Item>
					<Form.Item label="赛室名称" name="roomName" rules={[{ required: true, message: '请输入赛事名称' }]}>
						<Input className="input-box" placeholder="请输入赛事名称" />
					</Form.Item>
					<Form.Item label="总分" name="totalScore">
						<Input className="input-box" placeholder="请输入总分" />
					</Form.Item>
					{/* <Form.List name="scoreJson">
						{(fields) => {
							return fields.map(({ key, name, ...restField }) => {
								return (
									<>
										<Form.Item {...restField} label={`评委${key + 1}分数`} name={[name]}>
											<InputNumber
												min={0}
												max={100}
												precision="2"
												className="input-box"
												placeholder={`请输入评委${key + 1}分数`}
											/>
										</Form.Item>
									</>
								);
							});
						}}
					</Form.List> */}
				</div>
				{/* 基本信息 结束 */}
			</Form>
		</div>
	);
};
export default Index;
