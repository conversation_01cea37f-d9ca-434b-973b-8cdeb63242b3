import { useEffect, useState, useRef, forwardRef, useImperativeHandle } from 'react';
import { useRouterLink } from '@/hook/useRouter';

import { Button, Affix, Anchor, Form, Space, Modal, message, Image, Input } from 'antd';
import ModalForm from '@/components/ModalForm';

import { auditFormDataFillIn, getFormDataFillInVo } from '@/api/Achv/TopicManage/QuestionManage/Join';
import { getFormData } from '@/api/Achv/TopicManage/QuestionManage/index';

import { auditStatusTextList } from '@/pages/Achv/config';

const Index = () => {
	const ModalFormRef = useRef();
	const { linkTo, searchParams } = useRouterLink();
	const id = searchParams.get('id') || '';
	const fromPage = !!searchParams.get('fromPage');
	const formDataId = searchParams.get('formDataId');

	const [detail, setDetail] = useState({});

	// 跳转地址
	const linkToPath = '/newAchv/topicManage/questionManage';

	// 获取详情
	const getDetail = () => {
		if (id) {
			getFormDataFillInVo({ id }).then((res) => {
				const resData = res.data || {};
				resData.additionalInfo = JSON.parse(resData.additionalInfoJson || '[]');
				setDetail(resData);
			});
		}
	};

	useEffect(() => {
		getDetail();
	}, []);

	// 获取问卷标题
	const [interactiveTopicName, setInteractiveTopicName] = useState('');
	useEffect(() => {
		if (formDataId) {
			getFormData({ id: formDataId }).then((res) => {
				const { title } = res?.data || {};
				setInteractiveTopicName(title);
			});
		}
	}, [formDataId]);

	// 通过审核
	const handlePass = () => {
		Modal.confirm({
			title: '需求审核',
			content: `是否通过审核？`,
			onOk() {
				handleAudit({ status: 3 });
			},
		});
	};

	// 撤销审核
	const handleRevoke = () => {
		Modal.confirm({
			title: '撤销审核',
			content: `是否撤销审核结果？`,
			onOk() {
				handleAudit({ status: 1 });
			},
		});
	};

	// 审核操作
	const handleAudit = (values) => {
		auditFormDataFillIn({ ...values, id }).then(() => {
			message.success('操作成功');

			setTimeout(() => {
				linkTo(-1);
			}, 500);
		});
	};

	return (
		<div className="">
			<div className="flex flex-shrink align-center margin-bottom-4 height-48 font-size-22 font-weight-500 color-1d2129">
				<Space size={10}>
					<div className="a color-86909c" onClick={() => linkTo(linkToPath)}>
						问卷管理
					</div>
					<div className="color-86909c">/</div>
					<div
						className="a color-86909c max-width-200 text-cut"
						title={interactiveTopicName}
						onClick={() => linkTo(fromPage ? -1 : `${linkToPath}/detail?id=${formDataId}`)}
					>
						{interactiveTopicName}
					</div>
					<div className="color-86909c">/</div>
					<div className="a color-86909c" onClick={() => linkTo(fromPage ? -1 : `${linkToPath}/join/list?formDataId=${formDataId}`)}>
						报名列表
					</div>
					<div className="color-86909c">/</div>
					<div>报名详情</div>
				</Space>
			</div>
			<div id="section1"></div>
			{/* Tabs & 功能按钮 开始 */}
			<Affix>
				<div className="height-66 flex align-center justify-between bg-color-ffffff border-radius-8 padding-lr-24 border-solid-bottom-f2f3f5">
					<div className="flex align-center justify-between color-1d2129 line-height-26 font-size-18 font-weight-500">
						<Anchor
							affix={false}
							rootClassName="custom-anchor-box"
							direction="horizontal"
							replace
							targetOffset={80}
							items={[
								{
									key: 'section1',
									href: '#section1',
									title: <div className="margin-right-40 font-size-16 font-weight-500">基本信息</div>,
								},
							]}
						/>
					</div>
					<div className="flex align-center justify-between color-1d2129 line-height-26 font-size-18 font-weight-500">
						{detail.auditStatus === 1 ? (
							<>
								<Button
									type="primary"
									danger
									ghost
									onClick={() => {
										ModalFormRef.current.setOpen(true);
										ModalFormRef.current.setTitle('不通过原因');
									}}
									className="height-40 width-104"
								>
									不通过
								</Button>
								<Button type="primary" onClick={handlePass} className="margin-left-20 height-40 width-104">
									通过审核
								</Button>
							</>
						) : (
							<Button onClick={handleRevoke} className="margin-left-20 height-40 width-104">
								撤销审核
							</Button>
						)}
					</div>
				</div>
			</Affix>
			{/* Tabs & 功能按钮 结束 */}

			{/* 基本信息 开始 */}
			<div className="margin-top-20 padding-20 border-radius-8 bg-color-ffffff">
				<div className="flex align-center justify-between margin-bottom-20">
					<div className="font-size-18 line-height-26 font-weight-500">基本信息</div>
				</div>
				<div className="flex align-center justify-start font-size-14 line-height-22 padding-left-16 margin-bottom-20">
					<div className="color-86909c margin-right-4">问卷标题：</div>
					<div className="">{interactiveTopicName || '--'}</div>
				</div>
				<div className="flex align-center justify-start font-size-14 line-height-22 padding-left-16 margin-bottom-20">
					<div className="color-86909c margin-right-4">审核状态：</div>
					<div className="">
						<div className={`tag-status-${['default', 'warning', 'error', 'primary'][detail.auditStatus]}`}>
							{auditStatusTextList[detail.auditStatus] || ''}
						</div>
					</div>
				</div>
				{detail.auditStatus === 2 && (
					<div className="flex align-center justify-start font-size-14 line-height-22 padding-left-16 margin-bottom-20">
						<div className="color-86909c margin-right-4">拒绝原因：</div>
						<div className="">{detail.reason || '无'}</div>
					</div>
				)}
				{(detail.additionalInfo || []).map((ov, oi) => {
					const { type, lable, value } = ov;
					let renderText = value;
					if ([5, 6, 7].includes(type)) {
						renderText = (renderText || '').split('/').pop();
					} else if (type === 4) {
						renderText = (renderText || []).join('、');
					}
					return (
						<div key={oi} className="flex justify-start font-size-14 line-height-22 padding-left-16 margin-bottom-20">
							<div className="flex-shrink color-86909c margin-right-4 max-width-300">{lable}：</div>
							<div className="flex-sub">
								{type === 6 ? (
									<Image width={200} src={value} />
								) : (
									<div
										className={`${[5, 7].includes(type) ? 'a color-165dff' : ''}`}
										onClick={() => {
											if (renderText && [5, 7].includes(type)) {
												window.open(value);
											}
										}}
									>
										{renderText || '--'}
									</div>
								)}
							</div>
						</div>
					);
				})}
				<div className="flex align-start justify-start font-size-14 line-height-22 padding-left-16 margin-bottom-20">
					<div className="color-86909c margin-right-4">创建时间：</div>
					<div className="flex-sub">{(detail.createTime || '').slice(0, 16) || '--'}</div>
				</div>
			</div>
			{/* 基本信息 结束 */}

			{/* 询问弹窗 开始 */}
			<ModalForm ref={ModalFormRef} onOk={handleAudit} FormComp={(props) => <ConrirmForm ref={props.FormCompRef} />} />
			{/* 询问弹窗 结束 */}
		</div>
	);
};

// 询问表单
const ConrirmForm = forwardRef((props = {}, ref) => {
	const [form] = Form.useForm();

	useImperativeHandle(ref, () => {
		return {
			onCancel: () => Promise.resolve(),
			onOk: () => {
				return new Promise((resolve) => {
					form.validateFields().then((values) => {
						resolve(values);
					});
				});
			},
		};
	});

	return (
		<Form form={form} labelCol={{ style: { width: '100px' } }} initialValues={{ status: 2 }}>
			<Form.Item hidden name="status">
				<Input />
			</Form.Item>
			<Form.Item label="原因" name="reason" rules={[{ required: true, message: '请输入不通过原因' }]}>
				<Input.TextArea rows={4} placeholder="请输入不通过原因" />
			</Form.Item>
		</Form>
	);
});

export default Index;
