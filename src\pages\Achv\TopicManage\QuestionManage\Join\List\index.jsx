import { useEffect, useState, useRef, forwardRef, useImperativeHandle } from 'react';
import { Table, Button, Space, Form, Input, Modal, Row, Col, Affix, Select, Descriptions, message, Image, Popconfirm } from 'antd';
import ModalForm from '@/components/ModalForm';

import { useTableData } from '@/hook/useTableData';
import { useRouterLink } from '@/hook/useRouter';
import {
	getFormDataFillInPage as getTablePageData,
	batchDelFormDataFillIn as delTableItemData,
	updateFormDataFillIn as batchUpdateSort,
	exportFormDataFillIn as exportTableData,
	auditFormDataFillIn,
	getStatistics,
} from '@/api/Achv/TopicManage/QuestionManage/Join/index';
import { getFormData } from '@/api/Achv/TopicManage/QuestionManage/index';

// 活动报名审核状态文字列表
import { auditStatusData, auditStatusTextList } from '@/pages/Achv/config';

const Index = () => {
	const ModalFormRef = useRef();
	const linkToPath = '/newAchv/topicManage/questionManage';
	const { linkTo, searchParams } = useRouterLink();
	const fromPage = searchParams.get('fromPage') ? 1 : '';
	const formDataId = searchParams.get('formDataId');

	const status = searchParams.get('auditStatus');
	const [auditStatus, setAuditStatus] = useState(status === null ? 3 : status - 0 || 3);

	const { form, dataSource, pagination, changePage, onReset, onSearch, getTableData, delTableData, exportData } = useTableData({
		params: {
			formDataId,
			fromPage,
			auditStatus,
		},
		getTablePageData,
		delTableItemData,
		batchUpdateSort,
		exportTableData,
		getPageResult: (resData) => {
			return new Promise((resolve) => {
				const records = resData.records.map((ov) => {
					const additionalInfoJson = JSON.parse(ov.additionalInfoJson || '[]');
					const params = {};
					additionalInfoJson.forEach((oov) => {
						params[oov.keyName] = oov.type !== 4 ? oov.value : (oov.value || []).join('、');
					});
					return {
						...ov,
						...params,
					};
				});
				resData.records = records;
				resolve(resData);
			});
		},
		pageCallback: (paramsData = {}) => {
			getStatisticsData({ ...paramsData });
		},
	});

	// 获取统计数据
	const [statistics, setStatistics] = useState({});
	const getStatisticsData = (paramsData) => {
		delete paramsData.auditStatus;
		delete paramsData.pageSize;
		delete paramsData.pageNum;
		getStatistics(paramsData).then((res) => {
			setStatistics(res.data || {});
		});
	};

	// 获取问卷标题
	const [interactiveTopicName, setInteractiveTopicName] = useState('');
	const [enrollDesign, setEnrollDesign] = useState([]);
	useEffect(() => {
		if (formDataId) {
			getFormData({ id: formDataId }).then((res) => {
				const { title, enrollDesignJson } = res?.data || {};
				setInteractiveTopicName(title);
				setEnrollDesign(JSON.parse(enrollDesignJson || '[]'));
			});
		}
	}, [formDataId]);

	// 多选
	const [checkedList, setCheckedList] = useState([]);

	// 批量审核
	const batchAudit = () => {
		Modal.confirm({
			title: '批量审核',
			content: '确认操作当前选择的数据吗？',
			centered: true,
			closable: true,
			footer: (
				<div className="flex align-center justify-end margin-top-40">
					<Button
						className="margin-left-16"
						color="pink"
						onClick={() => {
							ModalFormRef.current.setOpen(true);
							ModalFormRef.current.setTitle('不通过原因');
							Modal.destroyAll();
						}}
					>
						批量不通过
					</Button>
					<Button
						className="margin-left-16"
						type="primary"
						onClick={() => {
							handelAudit({
								ids: checkedList,
								status: 3,
							});
							Modal.destroyAll();
						}}
					>
						批量通过
					</Button>
				</div>
			),
		});
	};

	// 审核
	const [auditId, setAuditId] = useState('');

	// 审核操作
	const handelAudit = (params) => {
		auditFormDataFillIn(params).then(() => {
			message.success('操作成功');
			getTableData();
			if (auditId) {
				setAuditId('');
			} else {
				setCheckedList([]);
			}
		});
	};

	return (
		<div className="">
			<div className="flex flex-shrink align-center margin-bottom-4 height-48 font-size-22 font-weight-500 color-1d2129">
				<Space size={10}>
					<div className="a color-86909c" onClick={() => linkTo(linkToPath)}>
						问卷管理
					</div>
					<div className="color-86909c">/</div>
					<div className="a color-86909c" onClick={() => linkTo(fromPage ? -1 : `${linkToPath}/detail?id=${formDataId}`)}>
						{interactiveTopicName}
					</div>
					<div className="color-86909c">/</div>
					<div>报名管理</div>
				</Space>
			</div>

			{/* Tabs & 功能按钮 开始 */}
			<Affix offsetTop={0}>
				<div className="flex align-center justify-between padding-24 border-radius-8 bg-color-ffffff border-solid-bottom-f2f3f5">
					<div className="flex align-center">
						{auditStatusData.map((ov) => (
							<div
								key={ov.value}
								className={`a margin-right-40 font-size-18 font-weight-500 ${auditStatus === ov.value ? 'color-165dff' : ''}`}
								onClick={() => setAuditStatus(ov.value)}
							>
								{ov.label}
								{statistics[ov.countName] > 0 ? `（${statistics[ov.countName]}）` : ''}
							</div>
						))}
					</div>
					<Space size={16}>
						<Button
							type="primary"
							onClick={() => {
								exportData();
							}}
						>
							导出报名名单
						</Button>
						<Button disabled={checkedList.length === 0} onClick={batchAudit}>
							批量审核{checkedList.length ? `(${checkedList.length})` : ''}
						</Button>
						<Button
							type="primary"
							onClick={() => {
								getTableData();
							}}
						>
							刷新
						</Button>
					</Space>
				</div>
			</Affix>
			{/* Tabs & 功能按钮 结束 */}

			<div className="flex-sub margin-top-18 padding-24 border-radius-8 bg-color-ffffff">
				{/* 筛选条件 开始 */}
				<Form form={form} labelAlign="right" layout="inline"></Form>
				{/* 筛选条件 结束 */}

				{/* 表格 开始 */}
				<Table
					rowKey="id"
					dataSource={dataSource}
					pagination={pagination}
					onChange={changePage}
					scroll={{ x: 'max-content' }}
					rowSelection={{
						selectedRowKeys: checkedList,
						onChange: setCheckedList,
					}}
				>
					<Table.Column
						title="序号"
						key="index"
						width={60}
						align="center"
						fixed="left"
						render={(_, __, index) => {
							return index + 1;
						}}
					/>
					<Table.Column
						title="审核状态"
						dataIndex="auditStatus"
						align="center"
						fixed="right"
						render={(text) => {
							return <div className={`tag-status-small-${['', 'warning', 'error', 'primary'][text]}`}>{auditStatusTextList[text]}</div>;
						}}
					/>
					{enrollDesign.map(({ type, lable, keyName }) => {
						return (
							<Table.Column
								key={keyName}
								title={lable}
								dataIndex={keyName}
								render={(text) => {
									let renderText = text;
									if ([5, 6, 7].includes(type)) {
										renderText = (renderText || '').split('/').pop();
									}
									return (
										<>
											{renderText && type === 6 ? (
												<Image src={text} width={100} />
											) : (
												<div
													className={`max-width-200 ${
														renderText && [5, 7].includes(type) ? 'a color-165dff' : ''
													} text-cut-3`}
													title={renderText}
													onClick={() => {
														if (renderText && [5, 7].includes(type)) {
															window.open(text);
														}
													}}
												>
													{renderText || '--'}
												</div>
											)}
										</>
									);
								}}
							/>
						);
					})}
					<Table.Column title="创建时间" dataIndex="createTime" render={(text) => (text || '').slice(0, 16)} />
					<Table.Column title="邀约人" dataIndex="inviterUserName" fixed="right" />
					<Table.Column
						title="操作"
						key="option"
						dataIndex="option"
						align="center"
						fixed="right"
						width="160px"
						render={(_, record) => {
							return (
								<>
									<Button
										type="link"
										size="small"
										onClick={() => linkTo(`${linkToPath}/join/detail?formDataId=${formDataId}&id=${record.id}&fromPage=1`)}
									>
										详情/编辑
									</Button>
									<Popconfirm
										title="提示"
										description="确定删除吗？"
										onConfirm={() => {
											delTableData(record.id);
										}}
										okText="确定"
										cancelText="取消"
									>
										<Button type="link" size="small">
											删除
										</Button>
									</Popconfirm>
								</>
							);
						}}
					/>
				</Table>
				{/* 表格 结束 */}
			</div>

			{/* 弹窗 开始 */}
			<ModalForm
				ref={ModalFormRef}
				onOk={({ reason }) =>
					handelAudit({
						ids: auditId ? [auditId] : checkedList,
						reason,
						status: 2,
					})
				}
				onCancel={() => {
					console.log(1);
					setAuditId(null);
				}}
				FormComp={(props) => <ConrirmForm ref={props.FormCompRef} />}
			/>

			{/* 弹窗 结束 */}
		</div>
	);
};

// 询问表单
const ConrirmForm = forwardRef((props = {}, ref) => {
	const [form] = Form.useForm();

	useImperativeHandle(ref, () => {
		return {
			onCancel: () => Promise.resolve(),
			onOk: () => {
				return new Promise((resolve) => {
					form.validateFields().then((values) => {
						resolve({ ...values });
					});
				});
			},
		};
	});

	return (
		<Form form={form} labelCol={{ style: { width: '100px' } }}>
			<Form.Item label="原因" name="reason" rules={[{ required: true, message: '请输入不通过原因' }]}>
				<Input.TextArea rows={4} placeholder="请输入不通过原因" />
			</Form.Item>
		</Form>
	);
});

export default Index;
