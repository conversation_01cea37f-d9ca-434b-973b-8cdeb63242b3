import { useState } from 'react';
import { Table, Button, Popconfirm, Space, Form, Affix, Row, Col, Input, Image } from 'antd';
import { useTableData } from '@/hook/useTableData';
import { useRouterLink } from '@/hook/useRouter';
import {
	getFormDataPage as getTablePageData,
	batchDelFormData as delTableItemData,
	updateRankingNum as batchUpdateSort,
	getStatistics,
} from '@/api/Achv/TopicManage/QuestionManage';
import { auditStatusData, auditStatusTextList } from '@/pages/Achv/config';

const Index = () => {
	const linkToPath = '/newAchv/topicManage/questionManage';
	const { linkTo, searchParams } = useRouterLink();

	const status = searchParams.get('auditStatus');
	const [auditStatus, setAuditStatus] = useState(status === null ? '' : status - 0 || '');

	const { form, dataSource, pagination, changePage, delTableData, SortInput, onReset, onSearch } = useTableData({
		params: {
			auditStatus,
		},
		getTablePageData,
		delTableItemData,
		batchUpdateSort,
		pageCallback: (paramsData = {}) => {
			getStatisticsData({ ...paramsData });
		},
	});

	// 获取统计数据
	const [statistics, setStatistics] = useState({});
	const getStatisticsData = (paramsData) => {
		delete paramsData.auditStatus;
		delete paramsData.pageSize;
		delete paramsData.pageNum;
		getStatistics(paramsData).then((res) => {
			setStatistics(res.data || {});
		});
	};

	return (
		<div className="">
			<div className="flex flex-shrink align-center margin-bottom-4 height-48 font-size-22 font-weight-500 color-1d2129">问卷管理</div>

			{/* Tabs & 功能按钮 开始 */}
			<Affix offsetTop={0}>
				<div className="flex align-center justify-between padding-24 border-radius-8 bg-color-ffffff border-solid-bottom-f2f3f5">
					<div className="flex align-center">
						{auditStatusData.map((ov) => (
							<div
								key={ov.value}
								className={`a margin-right-40 font-size-18 font-weight-500 ${auditStatus === ov.value ? 'color-165dff' : ''}`}
								onClick={() => setAuditStatus(ov.value)}
							>
								{ov.label}
								{statistics[ov.countName] > 0 ? `（${statistics[ov.countName]}）` : ''}
							</div>
						))}
					</div>
					<Space size={16}>
						<Button
							type="primary"
							onClick={() => {
								linkTo(`${linkToPath}/curd`);
							}}
						>
							创建问卷
						</Button>
					</Space>
				</div>
			</Affix>
			{/* Tabs & 功能按钮 结束 */}
			<div className="flex-sub margin-top-18 padding-24 border-radius-8 bg-color-ffffff">
				{/* 筛选条件 开始 */}
				<div className="flex justify-between margin-bottom-18">
					<div className="flex-sub">
						<Form form={form} labelAlign="right" layout="inline">
							<Row className="width-100per" gutter={[12, 12]}>
								<Col span={12}>
									<Form.Item name="title" label="问卷标题">
										<Input placeholder="请输入问卷标题" />
									</Form.Item>
								</Col>
							</Row>
						</Form>
					</div>
					<div className="margin-left-12 padding-left-12 border-solid-left-e5e6eb">
						<Space size={16}>
							<Button onClick={onReset}>重置</Button>
							<Button type="primary" onClick={onSearch}>
								查询
							</Button>
						</Space>
					</div>
				</div>
				{/* 筛选条件 结束 */}

				{/* 表格 开始 */}
				<Table rowKey="id" dataSource={dataSource} pagination={pagination} onChange={changePage} scroll={{ x: 'max-content' }}>
					<Table.Column
						title="序号"
						key="index"
						width={60}
						align="center"
						render={(_, __, index) => {
							return index + 1;
						}}
					/>
					<Table.Column title="问卷标题" dataIndex="title" />
					<Table.Column
						title="发布状态"
						dataIndex="auditStatus"
						align="center"
						render={(text) => {
							return (
								<div className={`tag-status-small-${['', 'primary', 'warning', '700eb2', 'success'][text]}`}>
									{auditStatusTextList[text] || '--'}
								</div>
							);
						}}
					/>
					<Table.Column
						title="是否登录"
						dataIndex="loginStatus"
						align="center"
						render={(text) => {
							return <div className={`tag-status-small-${['warning', 'primary'][text || 0]}`}>{['否', '是'][text || 0]}</div>;
						}}
					/>
					<Table.Column
						title="是否审核"
						dataIndex="fillInAuditStatus"
						align="center"
						render={(text) => {
							return <div className={`tag-status-small-${['warning', 'primary'][text || 0]}`}>{['否', '是'][text || 0]}</div>;
						}}
					/>
					<Table.Column
						title="审核消息推送"
						dataIndex="weChatSubscribeStatus"
						align="center"
						render={(text) => {
							return <div className={`tag-status-small-${['warning', 'primary'][text || 0]}`}>{['否', '是'][text || 0]}</div>;
						}}
					/>
					<Table.Column
						title="报名二维码"
						dataIndex="formDataQrCodeUrl"
						align="center"
						render={(text) => <Image src={text} width={100} />}
					/>
					<Table.Column
						title="排序"
						align="center"
						dataIndex="rankingNum"
						render={(_, record) => {
							return <SortInput record={record} />;
						}}
					/>
					<Table.Column title="创建时间" dataIndex="createTime" render={(text) => (text || '').slice(0, 16)} />
					<Table.Column
						title="操作"
						key="option"
						dataIndex="option"
						align="center"
						fixed="right"
						width="260px"
						render={(_, record) => {
							return (
								<>
									<Button
										type="link"
										size="small"
										onClick={() => linkTo(`${linkToPath}/join/list?formDataId=${record.id}&fromPage=1`)}
									>
										查看报名
									</Button>
									<Button type="link" size="small" onClick={() => linkTo(`${linkToPath}/detail?id=${record.id}&fromPage=1`)}>
										详情/编辑
									</Button>
									<Popconfirm
										title="提示"
										description="确定删除吗？"
										onConfirm={() => {
											delTableData(record.id);
										}}
										okText="确定"
										cancelText="取消"
									>
										<Button type="link" size="small">
											删除
										</Button>
									</Popconfirm>
								</>
							);
						}}
					/>
				</Table>
				{/* 表格 结束 */}
			</div>
		</div>
	);
};

export default Index;
