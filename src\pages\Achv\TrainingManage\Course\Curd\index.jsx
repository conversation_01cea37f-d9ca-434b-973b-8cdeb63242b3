import { useEffect, useState } from 'react';
import { useRouterLink } from '@/hook/useRouter';
import { Button, Space, Form, Input, message, Affix, InputNumber, Radio, Popconfirm, Modal, Checkbox, Select, Tag } from 'antd';
import { MenuFoldOutlined, LoadingOutlined } from '@ant-design/icons';
import { DragDropContext, Droppable, Draggable } from 'react-beautiful-dnd';

import UploadImg from '@/components/UploadImg';
import UploadFile from '@/components/UploadFile';
import UploadVideo from '@/components/UploadVideo';
import UEditor from '@/components/UEditor';
import SelectTTChannels from '@/components/Achv/SelectTTChannels';

import { saveTrainingCourse, updateTrainingCourse, getTrainingCourse } from '@/api/Achv/TrainingManage/Course';

const Index = () => {
	const { linkTo, searchParams } = useRouterLink();
	const id = searchParams.get('id') || '';
	const [form] = Form.useForm();
	const signUpStatus = Form.useWatch('signUpStatus', form);
	const signUpStopStatus = Form.useWatch('signUpStopStatus', form);
	const examStatus = Form.useWatch('examStatus', form);
	const examOpenStatus = Form.useWatch('examOpenStatus', form);
	const examTime = Form.useWatch('examTime', form);

	// 跳转地址
	const linkToPath = '/newAchv/trainingManage/course';

	// 禁止编辑发布类型
	const [hash, setHash] = useState('');

	// 取消
	const onCancel = () => {
		linkTo(-1);
	};

	// 记录 课时id 开始id
	const [subjectIds, setSubjectIds] = useState([]);
	const [examQuestionIds, setExamQuestionIds] = useState([]);

	// 提交
	const onSubmit = () => {
		form.validateFields()
			.then((values) => {
				const curSubjectIds = (values.subjectList || []).map((ov) => ov.id).filter((ov) => ov);
				const curExamQuestionIds = (values.examQuestionList || []).map((ov) => ov.id).filter((ov) => ov);
				const params = {
					...values,
					staffList: values.staffList ? values.staffList.map((ov) => ov.userId) : undefined,
					serviceList: values.serviceList ? values.serviceList.map((ov) => ov.userId) : undefined,
					managerList: values.managerList ? values.managerList.map((ov) => ov.userId) : undefined,
					delSubjectList: subjectIds.filter((ov) => !curSubjectIds.includes(ov)),
					delExamQuestionLIdList: examQuestionIds.filter((ov) => !curExamQuestionIds.includes(ov)),
				};

				(values.id ? updateTrainingCourse : saveTrainingCourse)(params).then(() => {
					message.success(values.id ? '修改成功' : '添加成功');
					setTimeout(() => {
						linkTo(-1);
					}, 500);
				});
			})
			.catch((error) => {
				console.log(error);
				form.scrollToField(error.errorFields[0].name, {
					block: 'center',
				});
			});
	};

	// 获取详情
	const getDetail = () => {
		if (id) {
			getTrainingCourse({ id }).then((res) => {
				const resData = res.data || {};
				resData.staffList = resData.staffList || [];
				resData.serviceList = resData.serviceList || [];
				resData.managerList = resData.managerList || [];
				setSubjectIds((resData.subjectList || []).map((ov) => ov.id));
				setExamQuestionIds((resData.examQuestionList || []).map((ov) => ov.id));
				form.setFieldsValue(resData);
			});
		}
	};

	useEffect(() => {
		const hash = window.location.hash.replace('#', '');
		setHash(hash || 'section1');

		getDetail();
	}, []);

	// 跳转到指定锚点
	useEffect(() => {
		if (hash) {
			setTimeout(() => {
				const element = document.getElementById(hash);
				if (element) {
					element.scrollIntoView({ behavior: 'smooth' });
				}
			}, 500);
		}
	}, [hash]);

	return (
		<div className="">
			<div className="flex flex-shrink align-center margin-bottom-4 height-48 font-size-22 font-weight-500 color-1d2129">
				<Space size={10}>
					<div className="a color-86909c" onClick={() => linkTo(linkToPath)}>
						课程管理
					</div>
					<div className="color-86909c">/</div>
					<div>{id ? '课程编辑' : '新增课程'}</div>
				</Space>
			</div>

			{/* Tabs & 功能按钮 开始 */}
			<div id="section1"></div>
			<Affix offsetTop={0}>
				<div className="flex align-center justify-between padding-24 border-radius-8 border-solid-bottom-f2f2f2 bg-color-ffffff">
					<div className="flex align-center">
						{['基本信息', '课程描述', '课时设置', '报名表单', '考试题目', '关联账号'].map((ov, oi) => {
							const currentHash = `section${oi + 1}`;
							return (
								<div
									key={oi}
									className={`a margin-right-40 font-size-18 font-weight-500 ${hash === currentHash ? 'color-165dff' : ''}`}
									onClick={() => {
										setHash(currentHash);
									}}
								>
									{ov}
								</div>
							);
						})}
					</div>
					<Space size={16}>
						<Button onClick={onCancel}>取消</Button>
						<Button type="primary" onClick={onSubmit}>
							保存
						</Button>
					</Space>
				</div>
			</Affix>
			{/* Tabs & 功能按钮 结束 */}

			<Form
				form={form}
				className="antd-form-box"
				labelAlign="right"
				labelCol={{ style: { width: '120px' } }}
				initialValues={{
					id: '',
					staffList: [],
					serviceList: [],
					managerList: [],
				}}
			>
				<Form.Item hidden name="id">
					<Input />
				</Form.Item>
				<Form.Item hidden name="rankingNum">
					<Input />
				</Form.Item>
				{/* 基本信息 开始 */}
				<div className="margin-top-20 padding-20 border-radius-8 bg-color-ffffff">
					<div className="padding-bottom-30 line-height-26 font-size-18 font-weight-500 color-1d2129">基本信息</div>
					<Form.Item label="课程名称" name="name" rules={[{ required: true, message: '请输入课程名称' }]}>
						<Input className="input-box" placeholder="请输入课程名称" />
					</Form.Item>
					<Form.Item label="课程简介" name="introduce">
						<Input className="input-box" placeholder="请输入课程简介" />
					</Form.Item>
					<Form.Item label="课程封面" name="courseCoverUrl" rules={[{ required: true, message: '请上传课程封面' }]}>
						<UploadImg size={5} width={260} height={120} />
					</Form.Item>
					<Form.Item label="在学人员" name="studentsNum">
						<InputNumber style={{ width: '180px' }} min={0} precision={0} placeholder="请输入在学人员基数" />
					</Form.Item>
					<Form.Item label="分享文案" name="shareDesc">
						<Input className="input-box" placeholder="请输入分享文案" />
					</Form.Item>
					<Form.Item label="分享朋友圈封面" name="timelineShareCoverUrl">
						<UploadImg size={5} width={120} height={120} cropperProps={{ width: 120, height: 120 }} />
					</Form.Item>
					<Form.Item label="分享好友封面" name="friendShareCoverUrl">
						<UploadImg size={5} width={150} height={120} cropperProps={{ width: 150, height: 120 }} />
					</Form.Item>
				</div>
				{/* 基本信息 结束 */}

				{/* 课程描述 开始 */}
				<div id="section2"></div>
				<div className="margin-top-20 padding-20 border-radius-8 bg-color-ffffff">
					<div className="padding-bottom-30 line-height-26 font-size-18 font-weight-500 color-1d2129">课程描述</div>
					<Form.Item name="courseDesc" wrapperCol={{ span: 24 }} rules={[{ required: true, message: '请输入课程描述' }]}>
						<UEditor />
					</Form.Item>
				</div>
				{/* 课程描述 结束 */}

				{/* 课时设置 开始 */}
				<div id="section3"></div>
				<div className="margin-top-20 padding-20 border-radius-8 bg-color-ffffff">
					<SubjectList form={form} />
				</div>
				{/* 课时设置 结束 */}

				{/* 报名表单 开始 */}

				<div id="section4"></div>
				<div className="margin-top-20 padding-20 border-radius-8 bg-color-ffffff">
					<div className="padding-bottom-30 line-height-26 font-size-18 font-weight-500 color-1d2129">报名表单</div>
					<Form.Item label="需要报名" name="signUpStatus" rules={[{ required: true, message: '请选择需要报名' }]} initialValue={1}>
						<Radio.Group
							options={[
								{ label: '是', value: 1 },
								{ label: '否', value: 0 },
							]}
						/>
					</Form.Item>
					{signUpStatus === 1 && (
						<>
							<Form.Item
								label="报名状态"
								name="signUpStopStatus"
								rules={[{ required: true, message: '请选择是否报名状态' }]}
								initialValue={0}
							>
								<Radio.Group
									options={[
										{ label: '正常报名', value: 0 },
										{ label: '停止报名', value: 1 },
									]}
								/>
							</Form.Item>

							{signUpStopStatus === 1 && (
								<Form.Item label="停止报名提示文案" name="signUpStopDesc">
									<Input className="input-box" placeholder="请输入停止报名提示文案 默认：当前培训课程已停止报名" />
								</Form.Item>
							)}
							<Form.Item label="关联问卷ID" name="formDataId" rules={[{ required: true, message: '请输入关联问卷ID' }]}>
								<Input className="input-box" placeholder="请输入关联问卷ID" />
							</Form.Item>
						</>
					)}
				</div>
				{/* 报名表单 结束 */}

				{/* 基本考试信息 开始 */}
				<div id="section5"></div>
				<div className="margin-top-20 padding-20 border-radius-8 bg-color-ffffff">
					<div className="flex justify-between">
						<div className="padding-bottom-30 line-height-26 font-size-18 font-weight-500 color-1d2129">基本考试信息</div>
					</div>
					<Form.Item label="需要考试" name="examStatus" rules={[{ required: true, message: '请选择是否需要考试' }]} initialValue={1}>
						<Radio.Group
							options={[
								{ label: '不需要', value: 0 },
								{ label: '需要', value: 1 },
							]}
						/>
					</Form.Item>
					{examStatus === 1 && (
						<>
							<Form.Item
								label="考试状态"
								name="examOpenStatus"
								rules={[{ required: true, message: '请选择是否需要考试' }]}
								initialValue={1}
							>
								<Radio.Group
									options={[
										{ label: '未开放', value: 0 },
										{ label: '开放', value: 1 },
									]}
								/>
							</Form.Item>
							{examOpenStatus === 0 && (
								<Form.Item label="未开放提示文案" name="examOpenDesc">
									<Input className="input-box" placeholder="请输入未开放提示文案 默认：当前考试暂未开放" />
								</Form.Item>
							)}
							<Form.Item label="及格要求" name="qualifiedScore" rules={[{ required: true, message: '请输入及格分数' }]}>
								<InputNumber style={{ width: '180px' }} min={0} precision={0} placeholder="请输入及格分数" addonAfter="分" />
							</Form.Item>
							<Form.Item label="用时要求" required>
								<Space>
									<Form.Item noStyle name="examTime">
										<InputNumber
											style={{ width: '180px' }}
											min={0}
											precision={0}
											placeholder="请输入用时要求"
											addonAfter="分钟"
										/>
									</Form.Item>
									<Checkbox
										checked={examTime === null}
										onChange={(e) => {
											if (e.target.checked) {
												form.setFieldValue('examTime', null);
											} else {
												message.error('请输入用时要求');
											}
										}}
									>
										不限时间
									</Checkbox>
								</Space>
							</Form.Item>
						</>
					)}
				</div>
				{/* 基本考试信息 结束 */}

				{/* 题目信息 开始 */}
				{examStatus === 1 && (
					<div className="margin-top-20 padding-20 border-radius-8 bg-color-ffffff">
						<ExamQuestionList form={form} />
					</div>
				)}
				{/* 题目信息 结束 */}

				{/* 关联账号 开始 */}
				<div id="section6"></div>
				<div className="margin-top-20 padding-20 border-radius-8 bg-color-ffffff">
					<SelectTTChannels className="border-radius-8 bg-color-ffffff" form={form} name="staffList" label="关联讲师" />
					<SelectTTChannels
						className="margin-top-20 border-radius-8 bg-color-ffffff"
						form={form}
						name="serviceList"
						label="关联客服人员"
						rules={[{ required: true, type: 'array', message: '请选择关联客服人员' }]}
					/>
					<SelectTTChannels
						className="margin-top-20 border-radius-8 bg-color-ffffff"
						form={form}
						name="managerList"
						label="关联管理员"
						rules={[{ required: true, type: 'array', message: '请选择关联管理员' }]}
					/>
				</div>
				{/* 关联账号 结束 */}
			</Form>
		</div>
	);
};

// 课时设置
const SubjectList = (props = {}) => {
	const [open, setOpen] = useState(false);
	const [current, setCurrent] = useState({});
	return (
		<>
			<Form.List
				name="subjectList"
				initialValue={[]}
				rules={[
					{
						validator: function (_, value) {
							if (value && value.length > 0) {
								return Promise.resolve();
							} else {
								return Promise.reject('请添加课时设置');
							}
						},
					},
				]}
			>
				{(fields, { remove, add }, { errors }) => {
					return (
						<>
							<div className="flex justify-between">
								<div className="padding-bottom-30 line-height-26 font-size-18 font-weight-500 color-1d2129">课时设置</div>
								<Button type="primary" onClick={() => setOpen(true)}>
									添加课时
								</Button>
							</div>
							<DragDropComp fields={fields} form={props.form} keyName="subjectList">
								{({ field, oi, provided }) => {
									return (
										<div key={oi} className="flex line-height-32 padding-12 border-radius-4 bg-color-f2f3f5">
											<div className="flex-shrink color-86909c">第{oi + 1}课时</div>
											<div className="flex-sub margin-lr-24">
												<Form.Item name={[field.name, 'title']} noStyle>
													<Input placeholder="请输入课时标题" readOnly className="" variant="borderless" />
												</Form.Item>
											</div>
											<Space size={0}>
												<MenuFoldOutlined
													className="flex-shrink a padding-lr-10 color-165dff"
													{...provided.dragHandleProps}
												/>
												<Button
													type="link"
													size="small"
													onClick={() => {
														setCurrent({
															...props.form.getFieldValue(['subjectList', field.name]),
															index: oi,
														});
														setOpen(true);
													}}
												>
													编辑
												</Button>
												<Popconfirm
													title="提示"
													description="确定删除吗？"
													onConfirm={() => {
														remove(field.name);
													}}
													okText="确定"
													cancelText="取消"
												>
													<Button type="link" size="small">
														删除
													</Button>
												</Popconfirm>
											</Space>
										</div>
									);
								}}
							</DragDropComp>
							<Form.ErrorList errors={errors} />
							<SubjectFormModal
								open={open}
								current={current}
								onCancel={() => {
									setOpen(false);
									setCurrent({});
								}}
								onOk={(values) => {
									setOpen(false);
									const index = current.index;
									if (index !== undefined && index !== null) {
										delete values.index;
										const subjectList = props.form.getFieldValue('subjectList');
										subjectList.splice(index, 1, values);
										props.form.setFieldsValue({ subjectList });
									} else {
										add(values);
									}
									setCurrent({});
								}}
							/>
						</>
					);
				}}
			</Form.List>
		</>
	);
};

// 课时内容
const SubjectFormModal = (props = {}) => {
	const [form] = Form.useForm();
	const courseFileUrl = Form.useWatch('courseFileUrl', form);

	useEffect(() => {
		if (props.open && props.current) {
			form.setFieldsValue(props.current);
		} else {
			form.resetFields();
		}
	}, [props.open, props.current]);
	return (
		<Modal
			title="课时内容"
			open={props.open}
			centered
			width={650}
			onCancel={props.onCancel}
			onOk={() => {
				form.validateFields().then((values) => {
					props.onOk(values);
				});
			}}
		>
			<div className="height-30"></div>
			<Form form={form} labelAlign="right" labelCol={{ style: { width: '120px' } }}>
				<Form.Item name="id" hidden>
					<Input />
				</Form.Item>
				<Form.Item name="courseFileSize" hidden>
					<Input />
				</Form.Item>
				<Form.Item name="index" hidden>
					<Input />
				</Form.Item>
				<Form.Item label="主题" name="title" rules={[{ required: true, message: '请输入课时标题' }]}>
					<Input className="input-box" placeholder="请输入课时标题" />
				</Form.Item>
				<Form.Item label="封面" name="courseCoverUrl">
					<UploadImg size={5} width={200} height={120} />
				</Form.Item>
				<Form.Item label="视频链接" name="videoUrl" rules={[{ required: true, message: '请输入视频链接' }]}>
					<VideoUrlInput />
				</Form.Item>
				<Form.Item label="课件" name="courseFileUrl">
					<UploadFile
						accept="application/pdf,application/vnd.openxmlformats-officedocument.presentationml.presentation"
						tips="支持pdf、pptx、ppt格式"
						onChange={(_, file) => {
							form.setFieldValue('courseFileSize', file.size);
						}}
					/>
				</Form.Item>
				{courseFileUrl && (
					<Form.Item label="课件标题" name="courseFileName" rules={[{ required: true, message: '请输入课时标题' }]}>
						<Input className="input-box" placeholder="请输入课时标题" />
					</Form.Item>
				)}
				{/* UploadFile UploadVideo */}
			</Form>
		</Modal>
	);
};

const VideoUrlInput = (props = {}) => {
	const [status, setStatus] = useState('');
	const [percent, setPercent] = useState(0);
	return (
		<Space direction="vertical" className="width-100per">
			<Input
				className="width-100per"
				value={props.value}
				placeholder="请输入视频链接或上传视频"
				suffix={
					status !== 'loading' && props.value ? (
						<Button type="link" size="small" onClick={() => props.onChange && props.onChange('')}>
							删除
						</Button>
					) : (
						<UploadVideo
							onChange={(url) => props.onChange && props.onChange(url)}
							tips={false}
							progress={false}
							onChangeStatus={setStatus}
							onPercent={setPercent}
						>
							<Button type="link" size="small" icon={(status === 'loading' && <LoadingOutlined />) || null}>
								{status === 'loading' ? `上传中...${percent}%` : '上传'}
							</Button>
						</UploadVideo>
					)
				}
				onChange={(e) => props.onChange && props.onChange(e.target.value)}
				disabled={status === 'loading'}
			/>
			<div className="font-size-14 color-86909c">支持上传mp4/mov/avi格式的文件，单个文件最大不超过3G</div>
		</Space>
	);
};
// 考试设置
const questionTypeOptions = [
	{ label: '单选题', value: 1 },
	{ label: '多选题', value: 2 },
	{ label: '判断题', value: 3 },
	{ label: '简答题', value: 4 },
];
const questionTypeText = ['', '单选题', '多选题', '判断题', '简答题'];

const ExamQuestionList = (props = {}) => {
	const examQuestionList = Form.useWatch('examQuestionList', props.form);
	const [open, setOpen] = useState(false);
	const [current, setCurrent] = useState({});
	return (
		<>
			<Form.List
				name="examQuestionList"
				initialValue={[]}
				rules={[
					{
						validator: function (_, value) {
							if (value && value.length > 0) {
								return Promise.resolve();
							} else {
								return Promise.reject('请添加试卷题目');
							}
						},
					},
				]}
			>
				{(fields, { remove, add }, { errors }) => {
					return (
						<>
							<div className="flex justify-between">
								<div className="padding-bottom-30 line-height-26 font-size-18 font-weight-500 color-1d2129">题目信息</div>
								<Button type="primary" onClick={() => setOpen(true)}>
									添加题目
								</Button>
							</div>
							<DragDropComp fields={fields} form={props.form} keyName="examQuestionList">
								{({ field, oi, provided }) => {
									return (
										<div key={oi} className="flex line-height-32 padding-12 border-radius-4 bg-color-f2f3f5">
											{examQuestionList && examQuestionList.length > 0 && (
												<>
													<Space size={24}>
														<div className="flex-shrink color-86909c">题目{oi + 1}</div>
														<Tag color={['', 'magenta', 'red', 'volcano', 'orange'][examQuestionList[oi].questionType]}>
															{questionTypeText[examQuestionList[oi].questionType]}
														</Tag>
													</Space>

													<div className="flex-sub flex align-center margin-lr-24 line-height-22">
														<span>{examQuestionList[oi].title}</span>
														<span className="color-86909c">（{examQuestionList[oi].score}分）</span>
													</div>
												</>
											)}
											<Space size={0}>
												<MenuFoldOutlined
													className="flex-shrink a padding-lr-10 color-165dff"
													{...provided.dragHandleProps}
												/>
												<Button
													type="link"
													size="small"
													onClick={() => {
														setCurrent({
															...props.form.getFieldValue(['examQuestionList', field.name]),
															index: oi,
														});
														setOpen(true);
													}}
												>
													编辑
												</Button>
												<Popconfirm
													title="提示"
													description="确定删除吗？"
													onConfirm={() => {
														remove(field.name);
													}}
													okText="确定"
													cancelText="取消"
												>
													<Button type="link" size="small">
														删除
													</Button>
												</Popconfirm>
											</Space>
										</div>
									);
								}}
							</DragDropComp>
							<Form.ErrorList errors={errors} />
							<ExamFormModal
								open={open}
								current={current}
								onCancel={() => {
									setOpen(false);
									setCurrent({});
								}}
								onOk={(values) => {
									setOpen(false);
									const index = current.index;
									if (index !== undefined && index !== null) {
										delete values.index;
										examQuestionList.splice(index, 1, values);
										props.form.setFieldsValue({ examQuestionList });
									} else {
										add(values);
									}
									setCurrent({});
								}}
							/>
						</>
					);
				}}
			</Form.List>
		</>
	);
};

// 课时内容
const ExamFormModal = (props = {}) => {
	const [form] = Form.useForm();
	const questionType = Form.useWatch('questionType', form);
	const answers = Form.useWatch('answers', form);

	useEffect(() => {
		if (props.open && props.current) {
			const current = { ...props.current };
			const { options, answers } = current;

			current.options = (options || '%||%').split('%||%');
			current.answers = (answers || '0')
				.split(',')
				.filter((ov) => ov !== '')
				.map((ov) => ov - 0);

			form.setFieldsValue(current);
		} else {
			form.resetFields();
		}
	}, [props.open, props.current]);

	return (
		<Modal
			title="题目内容"
			open={props.open}
			centered
			width={650}
			onCancel={props.onCancel}
			onOk={() => {
				form.validateFields().then((values) => {
					values.options = (values.options || []).filter((ov) => ov !== '').join('%||%');
					values.answers = (values.answers || []).filter((ov) => ov !== '').join(',');
					props.onOk(values);
				});
			}}
		>
			<div className="height-30"></div>
			<Form form={form} labelAlign="right" labelCol={{ style: { width: '120px' } }}>
				<Form.Item name="id" hidden>
					<Input />
				</Form.Item>
				<Form.Item name="answers" hidden initialValue={[]}>
					<Input />
				</Form.Item>
				<Form.Item name="index" hidden>
					<Input />
				</Form.Item>
				<Form.Item label="题目类型" name="questionType" rules={[{ required: true, message: '请选择题目类型' }]} initialValue={1}>
					<Radio.Group
						options={questionTypeOptions}
						onChange={(e) => {
							if (e.target.value === 3) {
								form.setFieldValue('options', ['', '']);
							}
							form.setFieldValue('answers', [0]);
						}}
					/>
				</Form.Item>
				<Form.Item label="题目分数" name="score" rules={[{ required: true, message: '请输入题目分数' }]}>
					<InputNumber min={0.1} precision={1} placeholder="请输入题目分数" style={{ width: '160px' }} />
				</Form.Item>
				<Form.Item label="问题" name="title" rules={[{ required: true, message: '请输入问题' }]}>
					<Input className="input-box" placeholder="请输入问题" />
				</Form.Item>
				{questionType !== 4 && (
					<Form.List name="options" initialValue={['', '']}>
						{(fields, { add, remove }) => {
							return (
								<>
									{fields.map((field, oi) => {
										return (
											<Form.Item key={oi} className="flex-sub" label={`选项${oi + 1}`} required>
												<div className="flex align-center">
													<Form.Item noStyle name={[field.name]} rules={[{ required: true, message: `请输入选项内容` }]}>
														<Input className="input-box" placeholder="请输入选项内容（无需输入序号）" />
													</Form.Item>
													<Checkbox
														checked={(answers || []).includes(oi)}
														className="flex-shrink margin-left-12"
														onChange={(e) => {
															let newAnswers = [...answers];
															if (questionType === 2) {
																// 多选
																if (e.target.checked) {
																	newAnswers.push(oi);
																} else {
																	newAnswers = newAnswers.filter((ov) => ov != oi);
																}
															} else {
																// 单选或判断
																newAnswers = [oi];
															}
															form.setFieldValue('answers', newAnswers);
														}}
													>
														答案
													</Checkbox>
													<Button
														disabled={oi < 2}
														type="link"
														onClick={() => {
															// 删除答案
															if (answers.includes(oi)) {
																const newAnswers = answers.filter((ov) => ov != oi);
																form.setFieldValue('answers', newAnswers);
															}
															remove(field.name);
														}}
													>
														删除
													</Button>
												</div>
											</Form.Item>
										);
									})}
									{questionType !== 3 && (
										<Form.Item label=" " colon={false}>
											<Button type="primary" ghost onClick={() => add()}>
												添加选项
											</Button>
										</Form.Item>
									)}
								</>
							);
						}}
					</Form.List>
				)}
			</Form>
		</Modal>
	);
};

const DragDropComp = (props = {}) => {
	const onDragEnd = (result) => {
		const { destination, draggableId } = result;
		if (!destination) {
			return;
		}
		const value = props.form.getFieldValue(props.keyName);
		const targetIndex = draggableId.replace('field', '') - 0;
		const index = destination.index;
		// 移动位置
		if (index !== targetIndex) {
			const target = value[targetIndex];
			value.splice(targetIndex, 1);
			value.splice(index, 0, target);
			props.form.setFieldValue(props.keyName, value);
		}
	};
	return (
		<DragDropContext onDragEnd={onDragEnd}>
			<Droppable droppableId="id">
				{(provided = {}) => (
					<div ref={provided.innerRef} {...provided.droppableProps}>
						<Space direction="vertical" className="width-100per">
							{props.fields.map((field, oi) => {
								return (
									<Draggable draggableId={'field' + field.key} index={field.key} key={field.key}>
										{(provided) => (
											<div className="" ref={provided.innerRef} {...provided.draggableProps}>
												{props.children({ field, oi, provided })}
											</div>
										)}
									</Draggable>
								);
							})}
						</Space>
						{provided.placeholder}
					</div>
				)}
			</Droppable>
		</DragDropContext>
	);
};
export default Index;
