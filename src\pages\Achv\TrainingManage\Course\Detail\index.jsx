import { useEffect, useState, useRef, forwardRef, useImperativeHandle } from 'react';
import { useRouterLink } from '@/hook/useRouter';

import { Button, Affix, Anchor, Form, Space, Modal, message, Image, Input, Tag } from 'antd';
import { EditOutlined } from '@ant-design/icons';
import ModalForm from '@/components/ModalForm';

import { auditTrainingCourse, getTrainingCourse } from '@/api/Achv/TrainingManage/Course';
import { auditStatusTextList, ratingTypeTextList } from '@/pages/Achv/config';

const Index = () => {
	const ModalFormRef = useRef();
	const { linkTo, searchParams } = useRouterLink();
	const id = searchParams.get('id') || '';
	const fromList = !!searchParams.get('fromList');

	const [detail, setDetail] = useState({});

	// 跳转地址
	const linkToPath = '/newAchv/trainingManage/course';

	// 获取详情
	const getDetail = () => {
		if (id) {
			getTrainingCourse({ id }).then((res) => {
				const resData = res.data || {};

				setDetail(resData);
			});
		}
	};

	// 通过审核
	const handlePass = () => {
		Modal.confirm({
			title: '需求审核',
			content: `是否通过审核？`,
			onOk() {
				handleAudit({ status: 3 });
			},
		});
	};

	// 撤销审核
	const handleRevoke = () => {
		Modal.confirm({
			title: '撤销审核',
			content: `是否撤销审核结果？`,
			onOk() {
				handleAudit({ status: 1 });
			},
		});
	};

	// 审核操作
	const handleAudit = (values) => {
		auditTrainingCourse({ ...values, id }).then(() => {
			message.success('操作成功');

			setTimeout(() => {
				linkTo(-1);
			}, 500);
		});
	};

	useEffect(() => {
		getDetail();
	}, []);

	return (
		<div className="">
			<div className="flex flex-shrink align-center margin-bottom-4 height-48 font-size-22 font-weight-500 color-1d2129">
				<Space size={10}>
					<div className="a color-86909c" onClick={() => linkTo(fromList ? -1 : linkToPath)}>
						课程管理
					</div>
					<div className="color-86909c">/</div>
					<div>课程详情</div>
				</Space>
			</div>
			<div id="section1"></div>
			{/* Tabs & 功能按钮 开始 */}
			<Affix>
				<div className="height-66 flex align-center justify-between bg-color-ffffff border-radius-8 padding-lr-24 border-solid-bottom-f2f3f5">
					<div className="flex align-center justify-between color-1d2129 line-height-26 font-size-18 font-weight-500">
						<Anchor
							affix={false}
							rootClassName="custom-anchor-box"
							direction="horizontal"
							replace
							targetOffset={80}
							items={[
								{
									key: 'section1',
									href: '#section1',
									title: <div className="margin-right-40 font-size-16 font-weight-500">基本信息</div>,
								},
								{
									key: 'section2',
									href: '#section2',
									title: <div className="margin-right-40 font-size-16 font-weight-500">课程描述</div>,
								},
								{
									key: 'section3',
									href: '#section3',
									title: <div className="margin-right-40 font-size-16 font-weight-500">课时设置</div>,
								},
								{
									key: 'section4',
									href: '#section4',
									title: <div className="margin-right-40 font-size-16 font-weight-500">报名表单</div>,
								},
								{
									key: 'section5',
									href: '#section5',
									title: <div className="margin-right-40 font-size-16 font-weight-500">考试题目</div>,
								},
								{
									key: 'section6',
									href: '#section6',
									title: <div className="margin-right-40 font-size-16 font-weight-500">关联账号</div>,
								},
							]}
						/>
					</div>
					<div className="flex align-center justify-between color-1d2129 line-height-26 font-size-18 font-weight-500">
						{detail.auditStatus === 1 ? (
							<>
								<Button
									type="primary"
									danger
									ghost
									onClick={() => {
										ModalFormRef.current.setOpen(true);
										ModalFormRef.current.setTitle('不通过原因');
									}}
									className="height-40 width-104"
								>
									不通过
								</Button>
								<Button type="primary" onClick={handlePass} className="margin-left-20 height-40 width-104">
									通过审核
								</Button>
							</>
						) : (
							<Button onClick={handleRevoke} className="margin-left-20 height-40 width-104">
								撤销审核
							</Button>
						)}
					</div>
				</div>
			</Affix>
			{/* Tabs & 功能按钮 结束 */}

			{/* 基本信息 开始 */}
			<div className="margin-top-20 padding-20 border-radius-8 bg-color-ffffff">
				<div className="flex align-center justify-between margin-bottom-20">
					<div className="font-size-18 line-height-26 font-weight-500">基本信息</div>
					<Button
						type="link"
						icon={<EditOutlined />}
						onClick={() => {
							linkTo(`${linkToPath}/curd?id=${id}#section1`);
						}}
					>
						编辑信息
					</Button>
				</div>
				<div className="flex align-center justify-start font-size-14 line-height-22 padding-left-16 margin-bottom-20">
					<div className="color-86909c margin-right-4">审核状态：</div>
					<div className="">
						<div className={`tag-status-${['default', 'warning', 'error', 'primary'][detail.auditStatus]}`}>
							{auditStatusTextList[detail.auditStatus] || ''}
						</div>
					</div>
				</div>
				{detail.auditStatus === 2 && (
					<div className="flex align-center justify-start font-size-14 line-height-22 padding-left-16 margin-bottom-20">
						<div className="color-86909c margin-right-4">拒绝原因：</div>
						<div className="">{detail.reason || '无'}</div>
					</div>
				)}
				<div className="flex align-center justify-start font-size-14 line-height-22 padding-left-16 margin-bottom-20">
					<div className="color-86909c margin-right-4">课程名称：</div>
					<div className="">{detail.name || '--'}</div>
				</div>
				<div className="flex justify-start font-size-14 line-height-22 padding-left-16 margin-bottom-20">
					<div className="color-86909c margin-right-4">课程简介：</div>
					<div className="">{detail.introduce || '--'}</div>
				</div>
				<div className="flex justify-start font-size-14 line-height-22 padding-left-16 margin-bottom-20">
					<div className="color-86909c margin-right-4">课程封面：</div>
					<div className="flex-sub">{(detail.courseCoverUrl && <Image width={120} src={detail.courseCoverUrl} />) || '--'}</div>
				</div>
				<div className="flex justify-start font-size-14 line-height-22 padding-left-16 margin-bottom-20">
					<div className="color-86909c margin-right-4">在学人员：</div>
					<div className="">{detail.studentsNum || '--'}</div>
				</div>
				<div className="flex justify-start font-size-14 line-height-22 padding-left-16 margin-bottom-20">
					<div className="color-86909c margin-right-4">分享文案：</div>
					<div className="">{detail.shareDesc || '--'}</div>
				</div>
				<div className="flex justify-start font-size-14 line-height-22 padding-left-16 margin-bottom-20">
					<div className="color-86909c margin-right-4">分享朋友圈封面：</div>
					<div className="flex-sub">
						{(detail.timelineShareCoverUrl && <Image width={120} src={detail.timelineShareCoverUrl} />) || '--'}
					</div>
				</div>
				<div className="flex justify-start font-size-14 line-height-22 padding-left-16 margin-bottom-20">
					<div className="color-86909c margin-right-4">分享好友封面：</div>
					<div className="flex-sub">{(detail.friendShareCoverUrl && <Image width={120} src={detail.friendShareCoverUrl} />) || '--'}</div>
				</div>
				<div className="flex align-center justify-start font-size-14 line-height-22 padding-left-16 margin-bottom-20">
					<div className="color-86909c margin-right-4">排序：</div>
					<div className="">{detail.rankingNum || '--'}</div>
				</div>
				<div className="flex align-start justify-start font-size-14 line-height-22 padding-left-16 margin-bottom-20">
					<div className="color-86909c margin-right-4">创建时间：</div>
					<div className="flex-sub">{(detail.createTime || '').slice(0, 16) || '--'}</div>
				</div>
			</div>
			{/* 基本信息 结束 */}

			{/* 课程描述 开始 */}
			<div id="section3"></div>
			<div className="margin-top-20 padding-20 border-radius-8 bg-color-ffffff">
				<div className="flex align-center justify-between margin-bottom-20">
					<div className="font-size-18 line-height-26 font-weight-500">课程描述</div>
					<Button
						type="link"
						icon={<EditOutlined />}
						onClick={() => {
							linkTo(`${linkToPath}/curd?id=${id}#section3`);
						}}
					>
						编辑信息
					</Button>
				</div>
				<div className="flex justify-start font-size-14 line-height-22 padding-left-16 margin-bottom-20">
					<div className="flex-sub">
						<div
							className="font-size-14 line-height-24 pre-wrap rich-box"
							dangerouslySetInnerHTML={{
								__html: detail.courseDesc || '--',
							}}
						></div>
					</div>
				</div>
			</div>
			{/* 课程描述 结束 */}
			{/* 课时设置 开始 */}
			<div id="section4"></div>
			<div className="margin-top-20 padding-20 border-radius-8 bg-color-ffffff">
				<div className="flex align-center justify-between margin-bottom-20">
					<div className="font-size-18 line-height-26 font-weight-500">课时设置</div>
					<Button
						type="link"
						icon={<EditOutlined />}
						onClick={() => {
							linkTo(`${linkToPath}/curd?id=${id}#section4`);
						}}
					>
						编辑信息
					</Button>
				</div>
				<Space direction="vertical" className="width-100per">
					{(detail.subjectList || []).map((ov, oi) => {
						return (
							<div key={oi} className="flex line-height-32 padding-12 border-radius-4 bg-color-f2f3f5">
								<div className="flex-shrink color-86909c">第{oi + 1}课时</div>
								<div className="flex-sub margin-lr-24">{ov.title}</div>
							</div>
						);
					})}
				</Space>
			</div>
			{/* 课时设置 结束 */}
			{/* 报名表单 开始 */}
			<div id="section5"></div>
			<div className="margin-top-20 padding-20 border-radius-8 bg-color-ffffff">
				<div className="flex align-center justify-between margin-bottom-20">
					<div className="font-size-18 line-height-26 font-weight-500">报名表单</div>
					<Button
						type="link"
						icon={<EditOutlined />}
						onClick={() => {
							linkTo(`${linkToPath}/curd?id=${id}#section5`);
						}}
					>
						编辑信息
					</Button>
				</div>
				<div className="flex align-center justify-start font-size-14 line-height-22 padding-left-16 margin-bottom-20">
					<div className="color-86909c margin-right-4">需要报名：</div>
					<div className="">
						<div className={`tag-status-${['warning', 'primary'][detail.signUpStatus]}`}>{['否', '是'][detail.signUpStatus] || ''}</div>
					</div>
				</div>
				{detail.signUpStatus === 1 && (
					<>
						<div className="flex align-center justify-start font-size-14 line-height-22 padding-left-16 margin-bottom-20">
							<div className="color-86909c margin-right-4">报名状态：</div>
							<div className={`tag-status-${['primary', 'warning'][detail.signUpStopStatus || 0]}`}>
								{['正常报名', '停止报名'][detail.signUpStopStatus || 0] || ''}
							</div>
						</div>
						{detail.signUpStopStatus === 1 && (
							<div className="flex align-center justify-start font-size-14 line-height-22 padding-left-16 margin-bottom-20">
								<div className="color-86909c margin-right-4">停止报名提示文案：</div>
								<div className="">{detail.signUpStopDesc || '当前培训课程已停止报名'}</div>
							</div>
						)}
						<div className="flex align-center justify-start font-size-14 line-height-22 padding-left-16 margin-bottom-20">
							<div className="color-86909c margin-right-4">关联问卷ID：</div>
							<div className="">{detail.formDataId || '--'}</div>
						</div>
					</>
				)}
			</div>
			{/* 报名表单 结束 */}
			{/* 基本考试信息 开始 */}
			<div id="section5"></div>
			<div className="margin-top-20 padding-20 border-radius-8 bg-color-ffffff">
				<div className="flex align-center justify-between margin-bottom-20">
					<div className="font-size-18 line-height-26 font-weight-500">基本考试信息</div>
					<Button
						type="link"
						icon={<EditOutlined />}
						onClick={() => {
							linkTo(`${linkToPath}/curd?id=${id}#section5`);
						}}
					>
						编辑信息
					</Button>
				</div>
				<div className="flex align-center justify-start font-size-14 line-height-22 padding-left-16 margin-bottom-20">
					<div className="color-86909c margin-right-4">需要考试：</div>
					<div className="">
						<div className={`tag-status-${['warning', 'primary'][detail.examStatus]}`}>{['不需要', '需要'][detail.examStatus] || ''}</div>
					</div>
				</div>
				{detail.examStatus === 1 && (
					<>
						<div className="flex align-center justify-start font-size-14 line-height-22 padding-left-16 margin-bottom-20">
							<div className="color-86909c margin-right-4">考试状态：</div>
							<div className={`tag-status-${['warning', 'primary'][detail.examOpenStatus || 0]}`}>
								{['未开放', '开放'][detail.examOpenStatus || 0] || ''}
							</div>
						</div>
						{detail.examOpenStatus === 0 && (
							<div className="flex align-center justify-start font-size-14 line-height-22 padding-left-16 margin-bottom-20">
								<div className="color-86909c margin-right-4">未开放提示文案：</div>
								<div className="">{detail.examOpenDesc || '当前考试暂未开放'}</div>
							</div>
						)}
						<div className="flex align-center justify-start font-size-14 line-height-22 padding-left-16 margin-bottom-20">
							<div className="color-86909c margin-right-4">及格要求：</div>
							<div className="">{detail.qualifiedScore ? detail.qualifiedScore + '分' : '--'}</div>
						</div>
						<div className="flex align-center justify-start font-size-14 line-height-22 padding-left-16 margin-bottom-20">
							<div className="color-86909c margin-right-4">用时要求：</div>
							<div className="">{detail.examTime ? detail.examTime + '分钟' : '不限'}</div>
						</div>
					</>
				)}
			</div>
			{/* 基本考试信息 结束 */}
			{/* 题目信息 开始 */}
			{detail.examStatus === 1 && (
				<div className="margin-top-20 padding-20 border-radius-8 bg-color-ffffff">
					<div className="flex align-center justify-between margin-bottom-20">
						<div className="font-size-18 line-height-26 font-weight-500">题目信息</div>
						<Button
							type="link"
							icon={<EditOutlined />}
							onClick={() => {
								linkTo(`${linkToPath}/curd?id=${id}#section5`);
							}}
						>
							编辑信息
						</Button>
					</div>
					<Space direction="vertical" className="width-100per">
						{(detail.examQuestionList || []).map((ov, oi) => {
							return (
								<div key={oi} className="flex align-center line-height-32 padding-12 border-radius-4 bg-color-f2f3f5">
									<div className="flex-shrink margin-right-12 color-86909c">题目{oi + 1}</div>
									<Tag color={['', 'magenta', 'red', 'volcano', 'orange'][ov.questionType]}>
										{['', '单选题', '多选题', '判断题', '简答题'][ov.questionType]}
									</Tag>
									<div className="flex-sub flex align-center margin-lr-12 line-height-22">
										<span>{ov.title}</span>
										<span className="flex-shrink color-86909c">（{ov.score}分）</span>
									</div>
								</div>
							);
						})}
					</Space>
				</div>
			)}
			{/* 题目信息 结束 */}
			{/* 关联账号 开始 */}
			<div id="section6"></div>
			<div className="margin-top-20 padding-20 border-radius-8 bg-color-ffffff">
				<div className="flex align-center justify-between margin-bottom-20">
					<div className="font-size-18 line-height-26 font-weight-500">关联账号</div>
					<Button
						type="link"
						icon={<EditOutlined />}
						onClick={() => {
							linkTo(`${linkToPath}/curd?id=${id}#section6`);
						}}
					>
						编辑信息
					</Button>
				</div>
				<div className="flex align-center justify-start font-size-14 line-height-22 padding-left-16 margin-bottom-20">
					<div className="margin-right-24 width-110 text-align-right color-86909c">关联讲师：</div>
					<div className="flex align-start justify-start flex-wrap">
						{(detail.staffList || [])
							.filter((ov) => ov)
							.map((item) => {
								return (
									<div key={item.id} className="flex align-center justify-start margin-right-20">
										<img src={item.avatarUrl} className="width-20 height-20 border-radius-10 bg-color-e5e6eb" />
										<div className="font-size-14 color-165dff margin-left-6 line-height-20">{item.accountName || ''}</div>
									</div>
								);
							})}
						{(detail.staffList || []).length === 0 && '--'}
					</div>
				</div>
				<div className="flex align-center justify-start font-size-14 line-height-22 padding-left-16 margin-bottom-20">
					<div className="margin-right-24 width-110 text-align-right color-86909c">关联客服人员：</div>
					<div className="flex align-start justify-start flex-wrap">
						{(detail.serviceList || [])
							.filter((ov) => ov)
							.map((item) => {
								return (
									<div key={item.id} className="flex align-center justify-start margin-right-20">
										<img src={item.avatarUrl} className="width-20 height-20 border-radius-10 bg-color-e5e6eb" />
										<div className="font-size-14 color-165dff margin-left-6 line-height-20">{item.accountName || ''}</div>
									</div>
								);
							})}
						{(detail.serviceList || []).length === 0 && '--'}
					</div>
				</div>
				<div className="flex align-center justify-start font-size-14 line-height-22 padding-left-16 margin-bottom-20">
					<div className="margin-right-24 width-110 text-align-right color-86909c">关联管理员：</div>
					<div className="flex align-start justify-start flex-wrap">
						{(detail.managerList || [])
							.filter((ov) => ov)
							.map((item) => {
								return (
									<div key={item.id} className="flex align-center justify-start margin-right-20">
										<img src={item.avatarUrl} className="width-20 height-20 border-radius-10 bg-color-e5e6eb" />
										<div className="font-size-14 color-165dff margin-left-6 line-height-20">{item.accountName || ''}</div>
									</div>
								);
							})}
						{(detail.managerList || []).length === 0 && '--'}
					</div>
				</div>
			</div>
			{/* 关联账号 结束 */}

			{/* 询问弹窗 开始 */}
			<ModalForm ref={ModalFormRef} onOk={handleAudit} FormComp={(props) => <ConrirmForm ref={props.FormCompRef} />} />
			{/* 询问弹窗 结束 */}
		</div>
	);
};

// 询问表单
const ConrirmForm = forwardRef((props = {}, ref) => {
	const [form] = Form.useForm();

	useImperativeHandle(ref, () => {
		return {
			onCancel: () => Promise.resolve(),
			onOk: () => {
				return new Promise((resolve) => {
					form.validateFields().then((values) => {
						resolve(values);
					});
				});
			},
		};
	});

	return (
		<Form form={form} labelCol={{ style: { width: '100px' } }} initialValues={{ status: 2 }}>
			<Form.Item hidden name="status">
				<Input />
			</Form.Item>
			<Form.Item label="原因" name="reason" rules={[{ required: true, message: '请输入不通过原因' }]}>
				<Input.TextArea rows={4} placeholder="请输入不通过原因" />
			</Form.Item>
		</Form>
	);
});

export default Index;
