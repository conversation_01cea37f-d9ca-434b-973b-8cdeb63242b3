import { useEffect, useState } from 'react';
import { Table, Button, Popconfirm, Space, Form, Affix, Image, Tooltip, Switch, Input, Row, Col, message } from 'antd';
import { QuestionCircleOutlined } from '@ant-design/icons';
import { useTableData } from '@/hook/useTableData';
import { useRouterLink } from '@/hook/useRouter';
import {
	getTrainingCoursePage as getTablePageData,
	batchDeleteTrainingCourse as delTableItemData,
	updateRankingNum as batchUpdateSort,
	getStatistics,
	updateCourseStatus,
} from '@/api/Achv/TrainingManage/Course';

import { auditStatusData, auditStatusTextList } from '@/pages/Achv/config';

const Index = () => {
	const linkToPath = '/newAchv/trainingManage/course';
	const { linkTo, searchParams } = useRouterLink();

	const status = searchParams.get('auditStatus');
	const [auditStatus, setAuditStatus] = useState(status === null ? 3 : status - 0 || '');

	const { form, dataSource, pagination, changePage, getTableData, delTableData, onReset, onSearch, SortInput } = useTableData({
		params: {
			auditStatus,
		},
		getTablePageData,
		delTableItemData,
		batchUpdateSort,
	});

	// 获取统计数据
	const [statistics, setStatistics] = useState({});
	const getStatisticsData = () => {
		getStatistics({}).then((res) => {
			setStatistics(res.data || {});
		});
	};

	useEffect(() => {
		getStatisticsData();
	}, []);

	// 修改显示状态
	const courseStatusChange = (record) => {
		updateCourseStatus({ id: record.id, courseStatus: record.courseStatus == 1 ? 0 : 1 }).then(() => {
			message.success('操作成功');
			getTableData();
		});
	};

	return (
		<div className="">
			<div className="flex flex-shrink align-center margin-bottom-4 height-48 font-size-22 font-weight-500 color-1d2129">课程管理</div>

			{/* Tabs & 功能按钮 开始 */}
			<Affix offsetTop={0}>
				<div className="flex align-center justify-between padding-24 border-radius-8 bg-color-ffffff border-solid-bottom-f2f3f5">
					<div className="flex align-center">
						{auditStatusData.map((ov) => (
							<div
								key={ov.value}
								className={`a margin-right-40 font-size-18 font-weight-500 ${auditStatus === ov.value ? 'color-165dff' : ''}`}
								onClick={() => setAuditStatus(ov.value)}
							>
								{ov.label}
								{statistics[ov.countName] > 0 ? `（${statistics[ov.countName]}）` : ''}
							</div>
						))}
					</div>
					<Space size={16}>
						<Button
							type="primary"
							onClick={() => {
								linkTo(`${linkToPath}/curd`);
							}}
						>
							创建课程
						</Button>
					</Space>
				</div>
			</Affix>
			{/* Tabs & 功能按钮 结束 */}

			<div className="flex-sub margin-top-18 padding-24 border-radius-8 bg-color-ffffff">
				{/* 筛选条件 开始 */}
				<div className="flex justify-between margin-bottom-18">
					<div className="flex-sub">
						<Form form={form} labelAlign="right" layout="inline">
							<Row className="width-100per" gutter={[12, 12]}>
								<Col span={12}>
									<Form.Item name="name" label="课程名称">
										<Input placeholder="请输入课程名称" />
									</Form.Item>
								</Col>
							</Row>
						</Form>
					</div>
					<div className="margin-left-12 padding-left-12 border-solid-left-e5e6eb">
						<Space size={16}>
							<Button onClick={onReset}>重置</Button>
							<Button type="primary" onClick={onSearch}>
								查询
							</Button>
						</Space>
					</div>
				</div>
				{/* 筛选条件 结束 */}

				{/* 表格 开始 */}
				<Table rowKey="id" dataSource={dataSource} pagination={pagination} onChange={changePage} scroll={{ x: 'max-content' }}>
					<Table.Column
						title="序号"
						key="index"
						width={60}
						align="center"
						render={(_, __, index) => {
							return index + 1;
						}}
					/>
					<Table.Column title="课程封面" dataIndex="courseCoverUrl" render={(text) => <Image width={120} src={text} />} />
					<Table.Column title="课程名称" dataIndex="name" />
					<Table.Column
						title="发布状态"
						dataIndex="auditStatus"
						render={(text) => {
							return (
								<div className={`tag-status-small-${['', 'primary', 'warning', '700eb2', 'success'][text]}`}>
									{auditStatusTextList[text] || '--'}
								</div>
							);
						}}
					/>
					<Table.Column
						title="课程状态"
						dataIndex="courseStatus"
						align="center"
						render={(_, record) => {
							return (
								<Switch
									checked={record.courseStatus == 1}
									disabled={record.auditStatus !== 3}
									title={record.auditStatus !== 3 ? '仅审核通过后才能操作' : ''}
									onChange={() => {
										courseStatusChange(record);
									}}
								/>
							);
						}}
					/>
					<Table.Column
						title="课时"
						align="center"
						dataIndex="examNum"
						render={(text) => {
							return <span className="color-165dff">{text || 0}</span>;
						}}
					/>
					<Table.Column
						title={
							<Space>
								<div>学习情况</div>
								<Tooltip title={() => <span className="color-4e5969">报名/合格</span>} color="#ffffff">
									<QuestionCircleOutlined />
								</Tooltip>
							</Space>
						}
						align="center"
						dataIndex="auditNumber"
						render={(_, record) => {
							return (
								<div
									className="cursor-pointer"
									onClick={() => {
										linkTo(`${linkToPath}/applyList?eventId=${record.id}`);
									}}
								>
									<span className="color-165dff">{record.signUpPassNum || 0}</span>
									<span>/</span>
									<span>{record.qualifiedNum || 0}</span>
								</div>
							);
						}}
					/>
					<Table.Column
						title={
							<Space>
								<div>报名人数</div>
								<Tooltip title={() => <span className="color-4e5969">总数/成功</span>} color="#ffffff">
									<QuestionCircleOutlined />
								</Tooltip>
							</Space>
						}
						align="center"
						dataIndex="auditNumber"
						render={(_, record) => {
							return (
								<div
									className="cursor-pointer"
									onClick={() => {
										linkTo(`/newAchv/topicManage/questionManage/join/list?formDataId=${record.formDataId}`);
									}}
								>
									<span className="color-165dff">{record.signUpNum || 0}</span>
									<span>/</span>
									<span>{record.signUpPassNum || 0}</span>
								</div>
							);
						}}
					/>
					<Table.Column
						title="留言"
						align="center"
						dataIndex="auditNumber"
						render={(_, record) => {
							return <span className="color-165dff">{record.totalCommentNum || 0}</span>;
						}}
					/>
					<Table.Column title="课程二维码" dataIndex="qrCodeUrl" align="center" render={(text) => <Image src={text} width={100} />} />
					<Table.Column
						title="排序"
						align="center"
						dataIndex="rankingNum"
						render={(_, record) => {
							return <SortInput record={record} />;
						}}
					/>
					<Table.Column
						title="操作"
						key="option"
						dataIndex="option"
						align="center"
						fixed="right"
						width="260px"
						render={(_, record) => {
							return (
								<>
									<Button type="link" size="small" onClick={() => linkTo(`${linkToPath}/detail?id=${record.id}&fromList=1`)}>
										详情/编辑
									</Button>
									<Popconfirm
										title="提示"
										description="确定删除吗？"
										onConfirm={() => {
											delTableData(record.id);
										}}
										okText="确定"
										cancelText="取消"
									>
										<Button type="link" size="small">
											删除
										</Button>
									</Popconfirm>
								</>
							);
						}}
					/>
				</Table>
				{/* 表格 结束 */}
			</div>
		</div>
	);
};

export default Index;
