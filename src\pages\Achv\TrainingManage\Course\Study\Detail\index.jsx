import { useEffect, useState } from 'react';
import { useRouterLink } from '@/hook/useRouter';

import { Button, Affix, Anchor, Space, Modal, Tag, InputNumber, message } from 'antd';
import { EditOutlined, CaretUpOutlined, CaretDownOutlined } from '@ant-design/icons';

import { queryExamination, saveScore, querySubjectStudyProgress } from '@/api/Achv/TrainingManage/Course/Study';
import { getTrainingCourse } from '@/api/Achv/TrainingManage/Course/index';
import Alert from 'antd/es/alert/Alert';

const finishStatusText = ['待学习', '未完成', '已完成'];
const examFishStatusText = ['', '未完成', '已完成'];
const examResultStatusText = ['待出分', '不及格', '及格'];

const Index = () => {
	const { linkTo, searchParams } = useRouterLink();
	const id = searchParams.get('id') || '';
	const courseId = searchParams.get('courseId');

	const [detail, setDetail] = useState({});

	// 跳转地址
	const linkToPath = '/newAchv/trainingManage/course';

	// 获取详情
	const getDetail = () => {
		if (id) {
			queryExamination({ id }).then((res) => {
				const resData = res.data || {};
				resData.scoreJson = (resData.scoreJson || '').split(',').filter((ov) => ov);
				setDetail(resData);
			});
		}
	};

	useEffect(() => {
		getDetail();
	}, []);

	// 获取专题名称
	const [courseData, setCourseData] = useState({});
	useEffect(() => {
		if (courseId) {
			getTrainingCourse({ id: courseId }).then((res) => {
				setCourseData(res.data || {});
			});
		}
	}, [courseId]);

	// 获取学习进度
	const [studyProgress, setStudyProgress] = useState('');
	const getStudyProgress = () => {
		const { courseId, userId } = detail;
		querySubjectStudyProgress({
			courseId,
			userId,
		}).then((res) => {
			let result = '';
			const subjectList = courseData.subjectList;
			const { subjectId, studyProgress } = (res.data || []).find((ov) => ov.studyProgress != 100) || {};

			if (subjectId) {
				const index = subjectList.findIndex((ov) => ov.id == subjectId);
				const { title } = subjectList[index] || {};
				result = `第${index + 1}课时: 已完成${studyProgress}%`;
			} else {
				result = (res.data || []).length === 0 ? '未开始' : '全部完成';
			}
			setStudyProgress(result);
		});
	};

	useEffect(() => {
		if (detail.id && courseData.id) {
			getStudyProgress();
		}
	}, [detail.id, courseData.id]);

	// 弹窗
	const [modalOpen, setModalOpen] = useState(false);

	return (
		<div className="">
			<div className="flex flex-shrink align-center margin-bottom-4 height-48 font-size-22 font-weight-500 color-1d2129">
				<Space size={10}>
					<div className="a color-86909c" onClick={() => linkTo(linkToPath)}>
						课程管理
					</div>
					<div className="color-86909c">/</div>
					<div className="a color-86909c max-width-200 text-cut" onClick={() => linkTo(`${linkToPath}/detail?id=${courseId}`)}>
						{courseData.name}
					</div>
					<div className="color-86909c">/</div>
					<div>学习情况</div>
				</Space>
			</div>
			<div id="section1"></div>
			{/* Tabs & 功能按钮 开始 */}
			<Affix>
				<div className="height-66 flex align-center justify-between bg-color-ffffff border-radius-8 padding-lr-24 border-solid-bottom-f2f3f5">
					<div className="flex align-center justify-between color-1d2129 line-height-26 font-size-18 font-weight-500">
						<Anchor
							affix={false}
							rootClassName="custom-anchor-box"
							direction="horizontal"
							replace
							targetOffset={80}
							items={[
								{
									key: 'section1',
									href: '#section1',
									title: <div className="margin-right-40 font-size-16 font-weight-500">基本信息</div>,
								},
								{
									key: 'section2',
									href: '#section2',
									title: <div className="margin-right-40 font-size-16 font-weight-500">答题详情</div>,
								},
							]}
						/>
					</div>
					<div className="flex align-center justify-between color-1d2129 line-height-26 font-size-18 font-weight-500"></div>
				</div>
			</Affix>
			{/* Tabs & 功能按钮 结束 */}

			{/* 基本信息 开始 */}
			<div className="margin-top-20 padding-20 border-radius-8 bg-color-ffffff">
				<div className="flex align-center justify-between margin-bottom-20">
					<div className="font-size-18 line-height-26 font-weight-500">基本信息</div>
				</div>
				<div className="flex align-center justify-start font-size-14 line-height-22 padding-left-16 margin-bottom-20">
					<div className="color-86909c margin-right-4">学员姓名：</div>
					<div className="">{detail.userName || '--'}</div>
				</div>
				<div className="flex align-center justify-start font-size-14 line-height-22 padding-left-16 margin-bottom-20">
					<div className="color-86909c margin-right-4">学习状态：</div>
					<div className="">
						<div className={`tag-status-${['error', 'warning', 'primary'][detail.finishStatus]}`}>
							{finishStatusText[detail.finishStatus] || ''}
						</div>
					</div>
				</div>
				<div className="flex align-center justify-start font-size-14 line-height-22 padding-left-16 margin-bottom-20">
					<div className="color-86909c margin-right-4">学习进度：</div>
					<div className="">{studyProgress || ''}</div>
				</div>
				<div className="flex align-center justify-start font-size-14 line-height-22 padding-left-16 margin-bottom-20">
					<div className="color-86909c margin-right-4">考试状态：</div>
					<div className="">
						<div className={`tag-status-${['default', 'warning', 'primary'][detail.examFishStatus]}`}>
							{examFishStatusText[detail.examFishStatus] || ''}
						</div>
					</div>
				</div>
				<div className="flex align-center justify-start font-size-14 line-height-22 padding-left-16 margin-bottom-20">
					<div className="color-86909c margin-right-4">考试结果：</div>
					<div className="">
						<div className={`tag-status-${['warning', 'error', 'primary'][detail.examResultStatus]}`}>
							{examResultStatusText[detail.examResultStatus] || ''}
						</div>
					</div>
				</div>
				<div className="flex align-center justify-start font-size-14 line-height-22 padding-left-16 margin-bottom-20">
					<div className="color-86909c margin-right-4">证书：</div>
					<div className="">{detail.name || '--'}</div>
				</div>
			</div>
			{/* 基本信息 结束 */}

			{/* 答题详情 开始 */}
			<div id="section2"></div>
			<div className="margin-top-20 padding-20 border-radius-8 bg-color-ffffff">
				<div className="flex align-center justify-between margin-bottom-20">
					<div className="font-size-18 line-height-26 font-weight-500">答题详情</div>
					<Button
						type="link"
						icon={<EditOutlined />}
						onClick={() => {
							setModalOpen(true);
						}}
					>
						前往打分
					</Button>
				</div>
				{detail.examResultStatus === 0 && (
					<div style={{ width: '600px' }}>
						<Alert message="当前学员已提交答卷，因当前考试涉及主观题打分，请前往打分" type="warning" showIcon />
					</div>
				)}
				<div className="flex align-center justify-start margin-top-12 font-size-14 line-height-22 padding-left-16 margin-bottom-20">
					<div className="color-86909c margin-right-4">最终得分：</div>
					<div className="">{detail.examResultStatus === 0 || detail.examScore === null ? '待计分' : `${detail.examScore}分`}</div>
				</div>
				{(detail.examResultStatus === 0 || detail.subjectiveScore !== null) && (
					<div className="flex align-center justify-start font-size-14 line-height-22 padding-left-16 margin-bottom-20">
						<div className="color-86909c margin-right-4">主观题分数：</div>
						<div className="">
							{detail.examResultStatus === 0 ? (
								<div
									className="a color-165dff"
									onClick={() => {
										setModalOpen(true);
									}}
								>
									前往打分
								</div>
							) : (
								`${detail.subjectiveScore}分`
							)}
						</div>
					</div>
				)}
				<div className="flex align-center justify-start font-size-14 line-height-22 padding-left-16 margin-bottom-20">
					<div className="color-86909c margin-right-4">客观题分数：</div>
					<div className="">{detail.examScore !== null ? `${detail.examScore - detail.subjectiveScore}分` : '--'}</div>
				</div>
				<div className="flex align-center justify-start font-size-14 line-height-22 padding-left-16 margin-bottom-20">
					<div className="color-86909c margin-right-4">客观题正确率：</div>
					<div className="">
						{detail.rightRate !== null ? `${detail.rightRate - 0}%（${detail.rightQuestionsNum}/${detail.totalQuestionsNum}）` : '--'}
					</div>
				</div>
				<div className="flex align-center justify-start font-size-14 line-height-22 padding-left-16 margin-bottom-20">
					<div className="color-86909c margin-right-4">考试用时：</div>
					<div className="">{detail.examUseTime || '--'}</div>
				</div>
				<div className="flex align-start justify-start font-size-14 line-height-22 padding-left-16 margin-bottom-20">
					<div className="color-86909c margin-right-4">考试时间：</div>
					<div className="flex-sub">{(detail.examDate || '').slice(0, 16) || '--'}</div>
				</div>
			</div>
			{/* 答题详情 结束 */}

			<ConrirmModal
				open={modalOpen}
				detail={detail || {}}
				courseData={courseData || {}}
				onCancel={() => {
					setModalOpen(false);
				}}
				onOk={(result) => {
					saveScore({
						id,
						courseId: courseData.id,
						answers: result,
					}).then(() => {
						message.success('操作成功');
						setModalOpen(false);
						getDetail();
					});
				}}
			/>
		</div>
	);
};

// 打分弹窗
const ConrirmModal = (props = {}) => {
	const { open, onCancel, onOk, courseData, detail } = props;
	const { examScore, answer } = detail || {};
	const { qualifiedScore, examQuestionList } = courseData || {};

	const questionList = (examQuestionList || []).filter((ov) => ov.questionType == 4);

	const [openList, setOpenList] = useState([]);
	const [scoreList, setScoreList] = useState([]);
	const [subjectiveScore, setSubjectiveScore] = useState(0);
	const [answerData, setAnswerData] = useState({});

	useEffect(() => {
		setSubjectiveScore(scoreList.reduce((prev, curr) => prev + (curr || 0), 0));
	}, [scoreList]);

	useEffect(() => {
		if (!answer && (examQuestionList || []).length === 0) {
			return;
		}
		const questionList = (examQuestionList || []).filter((ov) => ov.questionType == 4);
		const length = questionList.length;
		const answerData = {};
		const scoreData = [];
		JSON.parse(answer || '[]').forEach((ov) => {
			answerData[ov.questionId] = ov.answer;
			scoreData[ov.questionId] = ov.score !== undefined ? ov.score - 0 : null;
		});

		setAnswerData(answerData);
		setScoreList(
			questionList.map((ov) => {
				return scoreData[ov.id] || null;
			})
		);
		setOpenList(new Array(length).fill(false));
	}, [answer, examQuestionList]);
	return (
		<Modal
			title="主观题"
			open={open}
			width={800}
			centered
			cancelText="关闭"
			okText="确定保存"
			onCancel={() => onCancel && onCancel()}
			onOk={() => {
				if (scoreList.some((ov) => ov === null)) {
					message.error('请填写所有分数');
				} else {
					const result = JSON.parse(answer || '[]');
					result.forEach((ov) => {
						const questionId = ov.questionId;
						const index = questionList.findIndex((ov) => ov.id === questionId);

						if (index > -1) {
							ov.score = scoreList[index];
						}
					});
					Modal.confirm({
						title: '请确认是否保存',
						content: '请确认是否保存',
						okText: '确定保存',
						cancelText: '取消',
						centered: true,
						onOk: () => {
							onOk && onOk(result);
						},
					});
				}
			}}
			footer={(btns) => {
				return (
					<div className="flex align-center justify-between">
						<Space size={14} className="font-size-12 color-86909c">
							<div>
								当前总得分：
								<text
									className={
										examScore - (detail.subjectiveScore || 0) + subjectiveScore < qualifiedScore ? 'color-ff0000' : 'color-00b42a'
									}
								>
									{examScore - (detail.subjectiveScore || 0) + subjectiveScore}
								</text>
							</div>
							<div>主观题得分：{subjectiveScore}</div>
							<div>客观题得分：{examScore - (detail.subjectiveScore || 0)}</div>
						</Space>
						<Space size={14}>{btns}</Space>
					</div>
				);
			}}
		>
			<div className="margin-tb-24 height-400 overflowY-auto">
				<Space direction="vertical" className="width-100per">
					{questionList.map((ov, oi) => {
						return (
							<div key={oi} className="padding-12 border-radius-4 bg-color-f2f3f5">
								<div className="flex align-center line-height-32">
									<div className="flex-shrink margin-right-12 color-86909c">题目{oi + 1}</div>
									<Tag color={scoreList[oi] === null ? 'red' : 'blue'}>
										{scoreList[oi] === null ? '待打分' : `得分：${scoreList[oi]}分`}
									</Tag>
									<div className="flex-sub flex align-center margin-lr-12 line-height-22">
										<span>{ov.title}</span>
										<span className="flex-shrink color-86909c">（{ov.score}分）</span>
									</div>
									<Space
										className="a flex-shrink"
										onClick={() => {
											openList[oi] = !openList[oi];
											setOpenList([...openList]);
										}}
									>
										{openList[oi] ? (
											<>
												<div className="font-size-12 color-165dff">点击收起</div>
												<CaretUpOutlined />
											</>
										) : (
											<>
												<div className="font-size-12 color-165dff">点击展开</div>
												<CaretDownOutlined />
											</>
										)}
									</Space>
								</div>
								{openList[oi] && (
									<>
										<div className="margin-top-12 font-size-12 color-86909c pre-wrap">答：{answerData[ov.id]}</div>
										<Space className="margin-top-12">
											<InputNumber
												defaultValue={scoreList[oi]}
												className="width-120"
												placeholder="请输入分数"
												min={0.1}
												max={ov.score - 0}
												precision={1}
												onChange={(e) => {
													scoreList[oi] = e;
													setScoreList([...scoreList]);
												}}
											/>
										</Space>
									</>
								)}
							</div>
						);
					})}
				</Space>
			</div>
		</Modal>
	);
};

export default Index;
