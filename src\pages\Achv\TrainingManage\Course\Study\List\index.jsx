import { useEffect, useState } from 'react';
import { Table, Button, Space, Form, Affix, Input, Row, Col, Select } from 'antd';
import { useTableData } from '@/hook/useTableData';
import { useRouterLink } from '@/hook/useRouter';
import {
	getTrainingCourseExaminationPage as getTablePageData,
	exportExamination as exportTableData,
	scoreDataStatistics,
} from '@/api/Achv/TrainingManage/Course/Study/index';
import { getTrainingCourse } from '@/api/Achv/TrainingManage/Course/index';

const finishStatusOptions = [
	{ label: '待学习', value: 0 },
	{ label: '未完成', value: 1 },
	{ label: '已完成', value: 2 },
];
const finishStatusText = ['待学习', '未完成', '已完成'];
const examFishStatusOptions = [
	{ label: '未完成', value: 1 },
	{ label: '已完成', value: 2 },
];
const examFishStatusText = ['', '未完成', '已完成'];
const examResultStatusOptions = [
	{ label: '全部', value: '', countName: 'total' },
	{ label: '及格', value: 2, countName: 'passNum' },
	{ label: '待出分', value: 0, countName: 'waitScoreNum' },
	{ label: '不及格', value: 1, countName: 'notPassNum' },
];
const examResultStatusText = ['待出分', '不及格', '及格'];

const Index = () => {
	const linkToPath = '/newAchv/trainingManage/course';
	const { linkTo, searchParams } = useRouterLink();
	const fromPage = searchParams.get('fromPage') ? 1 : '';
	const courseId = searchParams.get('courseId');

	const status = searchParams.get('examResultStatus');
	const [examResultStatus, setExamResultStatus] = useState(status === null ? '' : status - 0 || '');

	const { form, dataSource, pagination, changePage, onReset, onSearch, exportData } = useTableData({
		params: {
			courseId,
			fromPage,
			examResultStatus,
		},
		getTablePageData,
		exportTableData,
		pageCallback: (paramsData = {}) => {
			getStatisticsData({ ...paramsData });
		},
	});

	// 获取统计数据
	const [statistics, setStatistics] = useState({});
	const getStatisticsData = (paramsData) => {
		delete paramsData.examResultStatus;
		delete paramsData.pageSize;
		delete paramsData.pageNum;
		scoreDataStatistics(paramsData).then((res) => {
			setStatistics(res.data || {});
		});
	};

	// 获取专题名称
	const [courseName, setCourseName] = useState('');
	useEffect(() => {
		if (courseId) {
			getTrainingCourse({ id: courseId }).then((res) => {
				const { name } = res?.data || {};
				setCourseName(name);
			});
		}
	}, [courseId]);

	return (
		<div className="">
			<div className="flex flex-shrink align-center margin-bottom-4 height-48 font-size-22 font-weight-500 color-1d2129">
				<Space size={10}>
					<div className="a color-86909c" onClick={() => linkTo(linkToPath)}>
						课程管理
					</div>
					<div className="color-86909c">/</div>
					<div
						className="a color-86909c max-width-200 text-cut"
						onClick={() => linkTo(fromPage ? -1 : `${linkToPath}/detail?id=${courseId}`)}
					>
						{courseName}
					</div>
					<div className="color-86909c">/</div>
					<div>学习情况</div>
				</Space>
			</div>

			{/* Tabs & 功能按钮 开始 */}
			<Affix offsetTop={0}>
				<div className="flex align-center justify-between padding-24 border-radius-8 bg-color-ffffff border-solid-bottom-f2f3f5">
					<div className="flex align-center">
						{examResultStatusOptions.map((ov) => (
							<div
								key={ov.value}
								className={`a margin-right-40 font-size-18 font-weight-500 ${examResultStatus === ov.value ? 'color-165dff' : ''}`}
								onClick={() => setExamResultStatus(ov.value)}
							>
								{ov.label}
								{statistics[ov.countName] > 0 ? `（${statistics[ov.countName]}）` : ''}
							</div>
						))}
					</div>
					<Space size={16}>
						<Button
							type="primary"
							onClick={() => {
								exportData();
							}}
						>
							导出数据
						</Button>
					</Space>
				</div>
			</Affix>
			{/* Tabs & 功能按钮 结束 */}

			<div className="flex-sub margin-top-18 padding-24 border-radius-8 bg-color-ffffff">
				{/* 筛选条件 开始 */}
				<div className="flex justify-between margin-bottom-18">
					<div className="flex-sub">
						<Form form={form} labelAlign="right" layout="inline">
							<Row className="width-100per" gutter={[12, 12]}>
								{/* <Col span={8}>
									<Form.Item name="userName" label="姓名">
										<Input placeholder="请输入姓名" />
									</Form.Item>
								</Col> */}
								<Col span={12}>
									<Form.Item name="finishStatus" label="学习状态">
										<Select options={finishStatusOptions} placeholder="请选择学习状态" />
									</Form.Item>
								</Col>
								<Col span={12}>
									<Form.Item name="examFishStatus" label="考试状态">
										<Select options={examFishStatusOptions} placeholder="请选择考试状态" />
									</Form.Item>
								</Col>
							</Row>
						</Form>
					</div>
					<div className="margin-left-12 padding-left-12 border-solid-left-e5e6eb">
						<Space size={16}>
							<Button onClick={onReset}>重置</Button>
							<Button type="primary" onClick={onSearch}>
								查询
							</Button>
						</Space>
					</div>
				</div>
				{/* 筛选条件 结束 */}

				{/* 表格 开始 */}
				<Table rowKey="id" dataSource={dataSource} pagination={pagination} onChange={changePage} scroll={{ x: 'max-content' }}>
					<Table.Column
						title="序号"
						key="index"
						width={60}
						align="center"
						render={(_, __, index) => {
							return index + 1;
						}}
					/>
					<Table.Column title="姓名" dataIndex="userName" />
					<Table.Column
						title="学习状态"
						dataIndex="finishStatus"
						render={(text) => {
							return (
								<div className={`tag-status-small-${['error', 'warning', 'primary', '700eb2', 'success'][text]}`}>
									{finishStatusText[text] || '--'}
								</div>
							);
						}}
					/>
					<Table.Column
						title="考试状态"
						dataIndex="examFishStatus"
						render={(text) => {
							return (
								<div className={`tag-status-small-${['', 'warning', 'primary', '700eb2', 'success'][text]}`}>
									{examFishStatusText[text] || '--'}
								</div>
							);
						}}
					/>
					<Table.Column
						title="考试结果"
						dataIndex="examResultStatus"
						render={(text) => {
							return (
								<div className={`tag-status-small-${['primary', 'warning', 'error', 'success'][text]}`}>
									{examResultStatusText[text] || '--'}
								</div>
							);
						}}
					/>
					<Table.Column title="考试分数" dataIndex="examScore" render={(text) => text || '--'} />
					<Table.Column title="考试用时" dataIndex="examUseTime" />
					<Table.Column title="考试时间" dataIndex="examDate" render={(text) => (text || '').slice(0, 16)} />
					<Table.Column title="证书" dataIndex="name" />
					<Table.Column
						title="操作"
						key="option"
						dataIndex="option"
						align="center"
						fixed="right"
						width="160px"
						render={(_, record) => {
							return (
								<Button
									type="link"
									size="small"
									onClick={() => linkTo(`${linkToPath}/study/detail?courseId=${courseId}&id=${record.id}&fromList=1`)}
								>
									详情/编辑
								</Button>
							);
						}}
					/>
				</Table>
				{/* 表格 结束 */}
			</div>
		</div>
	);
};

export default Index;
