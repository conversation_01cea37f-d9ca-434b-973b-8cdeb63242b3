// 审核状态
export const auditStatusData = [
	{
		label: '全部',
		value: '',
		countName: 'total',
	},
	{
		label: '已发布',
		value: 3,
		countName: 'releaseNum',
	},
	{
		label: '待发布',
		value: 1,
		countName: 'waitReleaseNum',
	},
	{
		label: '不通过',
		value: 2,
		countName: 'notPassNum',
	},
];
// 发布类型文字列表
export const auditStatusTextList = ['', '待发布', '不通过', '已发布'];

// 需求、成果 发布类型（来源）
export const releaseTypeData = [
	{
		label: '后台创建',
		value: 1,
	},
	{
		label: '平台提交（科转号）',
		value: 2,
	},
	{
		label: '参赛提交',
		value: 3,
	},
	{
		label: 'AI预判',
		value: 4,
	},
	{
		label: '资源拼团',
		value: 5,
	},
];

// 发布类型文字列表
export const releaseTypeTextList = ['', '后台创建', '平台提交（科转号）', '参赛提交', 'AI预判', '资源拼团'];

// 发布平台
export const releasePlatformData = [
	{
		label: '运营管理平台',
		value: 1,
	},
	{
		label: '成果转化小程序',
		value: 2,
	},
];

// 发布平台文字列表
export const releasePlatformTextList = ['', '运营管理平台', '成果转化小程序', '成果转化PC端'];

// 赛事组别
export const competitionGroupData = [
	{
		label: '全国技术经理人组',
		value: 1,
	},
	{
		label: '江夏技术经理人组',
		value: 2,
	},
	{
		label: '武汉高校大学生组',
		value: 3,
	},
];

// 赛事组别文字列表
export const competitionGroupTextList = ['', '全国技术经理人组', '江夏技术经理人组', '武汉高校大学生组'];

// 审核状态
export const brokerAuditStatusData = [
	{
		label: '全部',
		value: '',
		countName: 'total',
	},
	{
		label: '已通过',
		value: 3,
		countName: 'releaseNum',
	},
	{
		label: '待审核',
		value: 1,
		countName: 'waitReleaseNum',
	},
	{
		label: '不通过',
		value: 2,
		countName: 'notPassNum',
	},
];
// 发布类型文字列表
export const brokerAuditStatusTextList = ['', '待审核', '不通过', '已通过'];

// 经理人类型
export const brokerTypeData = [
	{
		label: '技术经理人',
		value: 1,
	},
	{
		label: '科研财务助理',
		value: 2,
	},
	{
		label: '科创品牌助理',
		value: 3,
	},
];

// 经理人类型文字列表
export const brokerTypeTextList = ['', '技术经理人', '科研财务助理', '科创品牌助理'];

// 经理人证书等级
export const brokerCertificateLevelData = [
	{
		value: 1,
		label: '初级',
	},
	{
		value: 2,
		label: '中级',
	},
	{
		value: 3,
		label: '高级',
	},
];

// 经理人证书等级文字列表
export const brokerCertificateLevelTextList = ['', '初级', '中级', '高级'];

// 对接状态
export const dockingStageStatusData = [
	{ value: 1, label: '待组织供需对接会' },
	{ value: 2, label: '第一次供需对接会' },
	{ value: 5, label: '第二次供需对接会' },
	{ value: 6, label: '第三次供需对接会' },
	{ value: 3, label: '已签约' },
	{ value: 4, label: '已终止谈判' },
];

// 对接状态文字列表
export const dockingStageStatusDataTextList = [
	'待审核通过',
	'待组织供需对接会',
	'第一次供需对接会',
	'已签约',
	'已终止谈判',
	'第二次供需对接会',
	'第三次供需对接会',
];

// 认证状态
export const authData = [
	{ label: '已认证', value: 1 },
	{ label: '未认证', value: 0 },
];

export const authTextList = ['未认证', '已认证'];

// 科转号属性  1 个人 2 集体
export const TTChannelsTypeData = [
	{ label: '科研机构', value: 1, channelAttr: 2 },
	{ label: '技术经理人', value: 2, channelAttr: 1 },
	{ label: '科创企业', value: 3, channelAttr: 2 },
	{ label: '行业专家', value: 4, channelAttr: 1 },
	{ label: '投资机构', value: 5, channelAttr: 2 },
	{ label: '国有企业', value: 6, channelAttr: 2 },
	{ label: '政府部门', value: 7, channelAttr: 2 },
	{ label: '行业协会', value: 8, channelAttr: 2 },
	{ label: '服务机构', value: 9, channelAttr: 2 },
];

// 科转号类型文字列表
export const TTChannelsTypeTextList = [
	'',
	'科研机构',
	'技术经理人',
	'科创企业',
	'行业专家',
	'投资机构',
	'国有企业',
	'政府部门',
	'行业协会',
	'服务机构',
];

export const TTChannelsTabsByType = (channelType) => {
	const channelAttr = TTChannelsTypeData.find((ov) => ov.value === channelType)?.channelAttr || 0;
	return {
		tabsList: [[], ['基本信息', '', '账号信息', '关联单位', '相关人员'], ['基本信息', '运营责任人信息', '账号信息', '关联成员', '相关人员']][
			channelAttr
		],
		channelAttr,
	};
};

// 显示隐藏状态
export const showStatusData = [
	{ label: '显示', value: 1 },
	{ label: '隐藏', value: 0 },
];

// 显示隐藏状态文字列表
export const showStatusTextList = ['隐藏', '显示'];

// 视频类型
export const videoTypeData = [
	{ label: '视频号导入', value: 1 },
	{ label: '上传视频', value: 2 },
];

// 视频类型文字列表
export const videoTypeTextList = ['', '视频号导入', '上传视频'];

// 发布状态
export const releaseStatusData = [
	{
		label: '全部',
		value: '',
	},
	{
		label: '已发布',
		value: 1,
	},
	{
		label: '待发布',
		value: 0,
	},
];

// 视频类型文字列表
export const releaseStatusTextList = ['待发布', '已发布'];

// 动态类型
export const timeLineTypeData = [
	{
		label: '全部',
		value: '',
	},
	{
		label: '技术需求',
		value: 1,
	},
	{
		label: '科技成果',
		value: 2,
	},
	{
		label: '图文',
		value: 3,
	},
	{
		label: '视频',
		value: 4,
	},
	{
		label: '活动',
		value: 5,
	},
	/* {
		label: '拼团',
		value: 6,
	},
	{
		label: '拼团',
		value: 7,
	}, */
	{
		label: '招聘需求',
		value: 8,
	},
];

// 动态类型文字列表
export const timeLineTypeTextList = ['', '技术需求', '科技成果', '图文', '视频', '活动', '拼团', '拼团', '招聘需求'];

// event活动状态状态
export const eventStatusData = [
	{ label: '未开始', value: 1 },
	{ label: '进行中', value: 2 },
	{ label: '已结束', value: 3 },
];

// event活动状态状态文字列表
export const eventStatusTextList = ['', '未开始', '进行中', '已结束'];

// 大咖类型
export const consultationTypeData = [
	{ label: '技术专家', value: 1 },
	{ label: '投资专家', value: 2 },
	{ label: '法律专家', value: 3 },
	{ label: '知识产权专家', value: 4 },
];

// 大咖类型文字列表
export const consultationTypeList = ['', '技术专家', '投资专家', '法律专家', '知识产权专家'];

// 联系状态类型
export const degreeData = [
	{ label: '不限', value: 5 },
	{ label: '大专', value: 4 },
	{ label: '本科', value: 3 },
	{ label: '硕士', value: 2 },
	{ label: '博士', value: 1 },
];

// 学历文字列表
export const degreeTextList = ['', '博士', '硕士', '本科', '大专', '不限'];

// 投递状态类型
export const submissionStatusData = [
	{ label: '待助力', value: 1 },
	{ label: '成功投递', value: 2 },
];

// 投递状态文字列表
export const submissionStatusTextList = ['', '待助力', '成功投递'];

// 联系状态类型
export const contactStatusData = [
	{ label: '待联系', value: 1 },
	{ label: '已联系', value: 2 },
];

// 联系状态文字列表
export const contactStatusTextList = ['', '待联系', '已联系'];

// 园区入驻跟进状态类型
export const parkFollowStatusData = [
	{ label: '待联系', value: 1 },
	{ label: '已联系', value: 2 },
	{ label: '已签约', value: 3 },
	{ label: '不合适', value: 4 },
];

// 园区入驻跟进状态文字列表
export const parkFollowStatusTextList = ['', '待联系', '已联系', '已签约', '不合适'];

// 问答平台来源
export const QAReleasePlatformsData = [
	{ label: '后台创建', value: 1 },
	{ label: '小程序发布', value: 2 },
	{ label: '财经app', value: 3 },
];

// 问答平台来源文字列表
export const QAReleasePlatformTextList = ['', '后台创建', '小程序发布', '财经app'];

// 评分规则
export const ratingTypeData = [
	{ label: '平均分', value: 1 },
	{ label: '去掉最高最低算平均分', value: 2 },
];

// 评分规则文字列表
export const ratingTypeTextList = ['', '平均分', '去最高最低分'];
