import { useEffect, useState } from 'react';
import {
	Col,
	Row,
	Select,
	DatePicker,
	Button,
	Table,
	Popconfirm,
	Space,
	Form,
	message,
	Dropdown,
	Checkbox,
	Modal,
	Input,
	Switch,
} from 'antd';
import {
	SearchOutlined,
	ReloadOutlined,
	PlusOutlined,
	CalendarOutlined,
	CloseOutlined,
} from '@ant-design/icons';
import Breadcrumb from '@/components/Breadcrumb';

import { getDeptData } from '@/utils/dictionary';
import {
	listInvestmentResponsibleDept,
	saveInvestmentResponsibleDept,
} from '@/api/Bidmgt/ConfigCenter/index';

const Index = () => {
	const [dataSource, setDataSource] = useState([]);
	const [open, setOpen] = useState(false);

	const getData = () => {
		listInvestmentResponsibleDept().then((res) => {
			setDataSource(res.data || []);
		});
	};

	const submit = (list) => {
		saveInvestmentResponsibleDept(list).then(() => {
			message.success('操作成功');
			setDataSource(list);
		});
	};

	useEffect(() => {
		getData();
	}, []);

	return (
		<div className='project-curd-box position-absolute inset-0 flex flex-direction-column margin-auto'>
			<div className='flex-sub flex flex-direction-column margin-top-16 padding-lr-20 padding-bottom-20'>
				{/* 面包屑 开始 */}
				<Breadcrumb
					icon='icon-configCenter'
					list={[
						{
							name: '配置中心',
							link: '/bidmgt/configCenter/list',
						},
					]}
					name='招商部门'
				/>
				{/* 面包屑 结束 */}

				<div className='flex-sub padding-20 border-radius-4 bg-color-ffffff'>
					{/* 操作按钮 开始 */}
					<div className='margin-bottom-20 flex align-center justify-between'>
						<Space size={24}>
							<Button
								type='primary'
								icon={<PlusOutlined />}
								onClick={() => {
									setOpen(true);
								}}
							>
								添加招商部门
							</Button>
						</Space>
					</div>
					{/* 操作按钮 结束 */}

					{/* 表格 开始 */}
					<Table
						size='small'
						rowKey='deptId'
						dataSource={dataSource}
						scroll={{
							x: 'min-content',
						}}
						pagination={false}
					>
						<Table.Column
							title='部门名称'
							key='deptName'
							dataIndex='deptName'
						/>
						{/* <Table.Column
							title='是否管理'
							key='manageStatus'
							dataIndex='manageStatus'
							render={(_, record) => {
								return (
									<Switch
										checked={record.manageStatus === 1}
										onChange={(e) => {
											dataSource.forEach(
												(ov) => (ov.manageStatus = 0)
											);
											record.manageStatus = e ? 1 : 1;
											submit(dataSource);
										}}
									/>
								);
							}}
						/> */}
						<Table.Column
							title='操作'
							key='option'
							dataIndex='option'
							align='center'
							width={150}
							render={(_, __, index) => {
								return (
									<Button
										type='link'
										size='small'
										onClick={() => {
											dataSource.splice(index, 1);
											submit(dataSource);
										}}
									>
										删除
									</Button>
								);
							}}
						/>
					</Table>
					{/* 表格 结束 */}
				</div>

				{/* 部门选择弹窗 开始 */}
				<DeptModel
					open={open}
					checkedIds={dataSource.map((ov) => ov.deptId)}
					onCancel={() => {
						setOpen(false);
					}}
					onChange={(checkedList) => {
						submit(checkedList);
					}}
				/>
				{/* 部门选择弹窗 结束 */}
			</div>
		</div>
	);
};

// 选择人员弹窗
const DeptModel = (props = {}) => {
	const [keyword, setKeyword] = useState('');
	const [deptList, setDeptList] = useState([]);
	const [checkedIds, setCheckedIds] = useState([]);
	const [checkedList, setCheckedList] = useState([]);

	// 获取部门数据
	const getDeptList = () => {
		getDeptData().then((list) => {
			setDeptList(
				list.map((ov) => {
					return {
						deptId: ov.value,
						deptName: ov.label,
						manageStatus: 0,
					};
				})
			);
		});
	};

	// 选择
	const checkedItem = (id = '') => {
		const index = checkedIds.findIndex((ov) => ov === id);

		if (index > -1) {
			checkedIds.splice(index, 1);
		} else {
			checkedIds.push(id);
		}
		setCheckedIds([...checkedIds]);
	};

	// 高亮关键词
	const highlightKeywords = (text) => {
		if (keyword) {
			const regex = new RegExp(`(${keyword})`, 'gi');
			return text.split(regex).map((part, i) =>
				regex.test(part) ? (
					<span key={i} className='color-cd4c57'>
						{part}
					</span>
				) : (
					part
				)
			);
		} else {
			return text;
		}
	};

	// 选中数据
	useEffect(() => {
		setCheckedList(
			deptList.filter(({ deptId }) => checkedIds.includes(deptId))
		);
	}, [checkedIds]);

	// 回显选中
	useEffect(() => {
		setCheckedIds(props.checkedIds);
	}, [props.checkedIds]);

	useEffect(() => {
		getDeptList();
	}, []);

	return (
		<Modal
			open={props.open}
			title={`设置招商部门`}
			centered
			width='800px'
			classNames={{
				body: 'presons-modal-box flex flex-direction-column scrollbar',
			}}
			styles={{
				content: {
					padding: 0,
				},
				header: {
					padding: '20px 24px 8px',
					borderBottom: 'solid 1px #e5e6eb',
				},
				body: {
					padding: '16px 24px',
					height: '60vh',
				},
				footer: {
					padding: '8px 24px 20px',
					borderTop: 'solid 1px #e5e6eb',
				},
			}}
			onCancel={() => props.onCancel()}
			onOk={() => {
				props.onChange(checkedList);
				props.onCancel();
			}}
		>
			<div className='flex flex-sub'>
				<div className='flex flex-direction-column width-220'>
					<div>
						<Input
							value={keyword}
							allowClear
							placeholder='请输入名字搜索'
							suffix={keyword ? null : <SearchOutlined />}
							onChange={(e) => {
								setKeyword(e.target.value);
							}}
						/>
					</div>
					<div className='flex-sub position-relative margin-top-16'>
						<div className='position-absolute inset-0 margin-auto overflowY-auto overflowX-hidden scrollbar border-solid-e5e6e8 border-radius-4'>
							<Space
								size={8}
								direction='vertical'
								className='width-100per padding-tb-8'
							>
								{deptList
									.filter(
										(ov) =>
											keyword ? ov.deptName.includes(keyword) : true
									)
									.map((ov) => {
										return (
											<div
												key={ov.deptId}
												className='a flex align-center justify-between padding-lr-8'
												onClick={() =>
													checkedItem(ov.deptId)
												}
											>
												<div
													className={`padding-lr-4 ${
														checkedIds.includes(
															ov.deptId
														)
															? 'color-165dff'
															: ''
													}`}
												>
													{highlightKeywords(
														ov.deptName
													)}
												</div>
												<Checkbox
													checked={checkedIds.includes(
														ov.deptId
													)}
												/>
											</div>
										);
									})}

								{deptList.filter(
									(ov) =>
										keyword === '' ||
										keyword === null ||
										ov.userName?.includes(keyword)
								).length === 0 && (
									<div className='font-size-12 color-86909c text-align-center'>
										暂无数据
									</div>
								)}
							</Space>
						</div>
					</div>
				</div>
				<div className='flex-sub position-relative margin-left-30'>
					<div className='position-absolute inset-0 margin-auto overflowY-auto overflowX-hidden scrollbar'>
						<Space
							size={12}
							className='margin-bottom-12 line-height-24 font-size-16 font-weight-500'
						>
							<div>已选</div>
							<div>招商部门</div>
							<div className='color-165dff'>
								（{checkedList.length}个）
							</div>
						</Space>
						{checkedList.map((ov) => {
							return (
								<div
									key={ov.deptId}
									className='flex align-center justify-between margin-bottom-16 padding-lr-12 width-300 height-32 border-radius-2 border-solid-e5e6eb'
								>
									<div className='font-weight-500'>
										{ov.deptName}
									</div>
									<CloseOutlined
										className='font-size-12'
										onClick={() => {
											checkedItem(ov.deptId);
										}}
									/>
								</div>
							);
						})}
					</div>
				</div>
			</div>
		</Modal>
	);
};

export default Index;
