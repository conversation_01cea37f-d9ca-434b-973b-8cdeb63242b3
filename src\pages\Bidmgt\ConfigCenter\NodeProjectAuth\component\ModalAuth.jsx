/**
 * @description ModalAuth.jsx - 文件描述
 * <AUTHOR>
 *
 * Created on 2024/10/15 15:08
 */
import React, { forwardRef, useEffect, useImperativeHandle, useState } from 'react';
import { Modal, Table, message, Form, Input } from 'antd';
import { authList, roleAuthList, saveRoleAuth } from '@/api/Bidmgt/important/groupEvent';

const ModalAuth = forwardRef((props, ref) => {
	const [open, setOpen] = useState(false);
	const [detail, setDetail] = useState({});
	const [dataSource, setDataSource] = useState([]);
	const [selectedRowKeys, setSelectedRowKeys] = useState([]);
	const [savaLoading, setSavaLoading] = useState(false);
	const [form] = Form.useForm();

	useEffect(() => {
		if (props?.authData) {
			setDataSource(props?.authData);
		}
	}, [props?.authData]);

	/* 打开权限配置 */
	const handleOpen = (detail) => {
		setOpen(true);
		setDetail(detail);
		if (detail?.permissions?.length > 0) {
			setSelectedRowKeys(detail.permissions);
		}
	};

	/* 关闭权限配置 */
	const handleCancel = () => {
		setOpen(false);
		setDetail({});
		setSelectedRowKeys([]);
	};
	/* 提交配置项 */
	const handleOk = async () => {
		setSavaLoading(true);
		const params = {
			permissions: selectedRowKeys,
			roleId: detail.id,
			roleName: detail.roleName,
		};
		console.log('save params', params);
		try {
			const res = await saveRoleAuth(params);
			if (res) {
				handleCancel();
				setSavaLoading(false);
				message.success('配置成功');
				props.refresh?.();
			}
		} catch (e) {
			setSavaLoading(false);
		}
	};

	useImperativeHandle(ref, () => ({
		showModal: handleOpen,
	}));
	const columns = [
		{
			title: '权限项',
			dataIndex: 'cnName',
		},
		{
			title: '权限值',
			dataIndex: 'key',
		},
		{
			title: '描述',
			dataIndex: 'desc',
		},
	];
	return (
		<Modal open={open} onCancel={handleCancel} onOk={handleOk} title={`配置角色配置权限`} width={800} confirmLoading={savaLoading}>
			<Form form={form} hidden={true} labelCol={{ span: 6 }} wrapperCol={{ span: 14 }}>
				<Form.Item label="角色名称" name={'name'}>
					<Input />
				</Form.Item>
				<Form.Item label="角色描述" name={'desc'}>
					<Input.TextArea />
				</Form.Item>
			</Form>
			<Table
				bordered
				rowKey={'key'}
				dataSource={dataSource}
				columns={columns}
				size="small"
				pagination={false}
				rowSelection={{
					type: 'checkbox',
					selectedRowKeys: selectedRowKeys,
					onChange: (selectedRowKeys, selectedRows) => {
						console.log(selectedRowKeys, selectedRows);
						setSelectedRowKeys(selectedRowKeys);
					},
				}}
			/>
		</Modal>
	);
});
export default ModalAuth;
