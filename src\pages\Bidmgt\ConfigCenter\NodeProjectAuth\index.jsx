/**
 * @description index.jsx - 节点督办项目权限配置
 * <AUTHOR>
 *
 * Created on 2024/10/14 11:31
 */
import React, {useEffect, useRef, useState} from 'react';
import {Button, Modal, Space, Table, Typography, message, Form, Input} from 'antd';
import {PlusOutlined} from "@ant-design/icons";
import {useTableData} from "@/hook/useTableData";
import ModalAuth from "./component/ModalAuth";
import {authList, messageList, myRoleAuth, roleAuthList} from "@/api/Bidmgt/important/groupEvent";
import {getRoleList} from "@/api/System";
import {ROLE_AUTH} from "@/pages/Bidmgt/ConfigCenter/NodeProjectAuth/const";

const {Title} = Typography;

const NodeProjectAuth = () => {
    const authRef = useRef();
    const [dataSource, setDataSource] = React.useState([]);
    const [form] = Form.useForm();
    const [roleList, setRoleList] = useState([]);
    const [authData, setAuthData] = useState([]);

    // const {form, onSearch, onReset} =
    //     useTableData({
    //         getTablePageData: roleAuthList,
    //     });

    useEffect(() => {
        getRoleData({});
        getAuthList();
    }, []);

    const onSearch = () => {
        console.log('搜索', form.getFieldsValue());
        getAuth(form.getFieldsValue());
    };

    const onReset = () => {
        form.resetFields();
        onSearch();
    };

    /* 查询角色列表 */
    const getRoleData = async (data = {}) => {
        const res = await getRoleList();
        const roleKeys = Object.keys(ROLE_AUTH);
        const list = res.data.filter(item => roleKeys.includes(item.roleCode))
            .map(({roleCode, roleName, status, id}) => {
                return {
                    roleCode, roleName, status, id,
                };
            });
        setRoleList(list);
        getAuth({}, list);
    };

    /* 查询角色权限列表 */
    const getAuth = async (data = {}, list) => {
        list = list || roleList;
        const res = await roleAuthList(data);
        list = list.map(item => {
            const auth = res.data.find(i => i.roleId === item.id);
            return {
                ...item,
                ...auth,
            };
        });
        console.log('权限列表', res)
        console.log('角色列表', list);
        setDataSource(list);
    };

    /* 查询权限数据 */
    const getAuthList = async () => {
        const res = await authList();
        setAuthData(res.data);
    };

    /* 编辑权限 */
    const handleEditAuth = (record) => {
        console.log('编辑权限', record);
        authRef.current.showModal(record);
    };

    /* 删除权限 */
    const handleDelAuth = async (record) => {
        console.log('删除权限', record);
        Modal.confirm({
            title: '删除角色',
            content: '删除后无法恢复，确认删除吗？',
            onOk: () => {
                return new Promise((resolve, reject) => {
                    setTimeout(() => {
                        resolve();
                        message.success('删除成功');
                        onSearch();
                    }, 2000);
                })
            }
        })
    };

    /* 新建权限 */
    const handleAddAuth = () => {
        authRef.current.showModal({add: true});
    };

    const columns = [
        {
            title: '角色',
            dataIndex: 'roleName',
            width: 180,
        },
        {
            title: '内容',
            dataIndex: 'permissions',
            render: (permissions, record) => {
                return permissions?.map(auth => {
                    return authData.find(i => i.key === auth)?.cnName || '';
                }).join('、')
            }
        },
        {
            title: '配置权限',
            dataIndex: 'action',
            width: 180,
            align: 'center',
            fixed: 'right',
            render: (text, record) => (
                <Space>
                    <Button size={'small'} type={'link'} onClick={() => handleEditAuth(record)}>配置</Button>
                    {/*<Button size={'small'} danger type={'link'} onClick={() => handleDelAuth(record)}>删除</Button>*/}
                </Space>
            )
        }
    ];
    return (<div className={'flex-sub flex flex-direction-column padding-20 bg-color-ffffff margin-20 border-radius-4'}>
        <Title level={4}>
            节点督办项目权限配置
        </Title>
        <div className={'margin-tb-12 flex justify-between align-center'}>
            <div className={'flex align-center gap-12'}>
                {/*<Button icon={<PlusOutlined />} type={'primary'} onClick={handleAddAuth}>新建</Button>*/}
            </div>
            <div>
                <Form form={form} labelAlign="right" layout="inline">
                    <Form.Item label={'角色名称'}>
                        <Input type="text"/>
                    </Form.Item>
                    <Form.Item>
                        <Space>
                            <Button type={'primary'} onClick={onSearch}>查询</Button>
                            <Button onClick={onReset}>重置</Button>
                        </Space>
                    </Form.Item>
                </Form>
            </div>
        </div>
        <Table
            columns={columns}
            dataSource={dataSource}
            pagination={false}
            rowKey={'roleCode'}
            // onChange={changePage}
            scroll={{x: 'max-content'}}
        />
        <ModalAuth ref={authRef} refresh={onSearch} authData={authData}/>
    </div>)
}
export default NodeProjectAuth;
