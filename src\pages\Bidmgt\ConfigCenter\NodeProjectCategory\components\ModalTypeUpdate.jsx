/**
 * @description ModalTypeUpdate - 编辑项目节点类型
 * <AUTHOR>
 *
 * Created on 2024/10/14 15:26
 */
import React, { forwardRef, useImperativeHandle, useState } from 'react';
import { Modal, Form, Input, message } from 'antd';
import { paramSave } from '@/api/Bidmgt/important/groupEvent';

const ModalTypeUpdate = forwardRef(({ getList }, ref) => {
	const [open, setOpen] = useState(false);
	const [detail, setDetail] = useState({});
	const [form] = Form.useForm();
	/* 打开弹框 */
	const showModal = (record) => {
		setOpen(true);
		setDetail(record);
		form.setFieldsValue(record);
	};
	/* 关闭弹框 */
	const handleCancel = () => {
		setOpen(false);
		form.resetFields();
	};

	/* 提交表单 */
	const handleOk = async () => {
		try {
			const values = await form.validateFields();
			const params = {
				...values,
				id: detail?.id,
				type: detail?.type,
			};
			if (!Number(values.ordinal)) {
				message.error('排序只能为数字');
				return;
			}
			const res = await paramSave(params);
			if (res) {
				getList(params);
				handleCancel();
				message.success('操作成功');
			}
		} catch (e) {
			console.log(e);
			message.error('操作失败');
		}
	};

	useImperativeHandle(ref, () => ({
		showModal,
	}));
	return (
		<Modal open={open} onCancel={handleCancel} onOk={handleOk} title={`${detail.id ? '编辑' : '新增'}项目节点类别`}>
			<Form form={form} labelCol={{ span: 6 }}>
				<Form.Item label="类型名称" name="cnName" rules={[{ required: true, message: '请输入类型名称' }]}>
					<Input placeholder={'请输入类型名称'} />
				</Form.Item>
				<Form.Item label="排序" name="ordinal" rules={[{ required: true, message: '请输入排序' }]}>
					<Input placeholder={'请输入排序'} />
				</Form.Item>
			</Form>
		</Modal>
	);
});
export default ModalTypeUpdate;
