/**
 * 节点项目督办项目类型配置
 * */
import { Button, Typography, Input, Form, Space, Table, Modal, message } from 'antd';
import { PlusOutlined, SearchOutlined, UndoOutlined } from '@ant-design/icons';
import { useTableData } from '@/hook/useTableData';
import { useEffect, useRef, useState } from 'react';
import ModalTypeUpdate from './components/ModalTypeUpdate';
import { deleteParam, downParam, pageParamList, upParam } from '@/api/Bidmgt/important/groupEvent';
import { projectType } from '@/pages/Bidmgt/NodeProjectManage/const';

const { Title } = Typography;
const Index = () => {
	const modalRef = useRef();
	const [form] = Form.useForm();
	const [dataSource, setDataSource] = useState([]);

	/* 获取类别列表 */
	const getListType = async (values) => {
		const params = {
			type: projectType.PROJECT_CATEGORY,
			...values,
		};
		const res = await pageParamList(params);
		if (res?.data) {
			setDataSource(res.data);
		}
	};

	/* 搜索参数 */
	const onSearch = () => {
		const values = form.getFieldsValue();
		console.log('搜索参数', values);
		getListType(values);
	};

	useEffect(() => {
		onSearch();
	}, []);

	/* 重置表单 */
	const onReset = () => {
		form.resetFields();
	};
	/* 新增处理 */
	const handleAdd = () => {
		console.log('新增');
		modalRef.current.showModal({ type: projectType.PROJECT_CATEGORY });
	};

	/* 上移 */
	const handleUp = async (record) => {
		console.log('上移', record);
		const params = { id: record.id };
		const res = await upParam(params);
		message.success('上移成功');
		onSearch();
	};
	/* 下移 */
	const handleDown = async (record) => {
		console.log('下移', record);
		const params = { id: record.id };
		const res = await downParam(params);
		message.success('下移成功');
		onSearch();
	};
	/* 删除 */
	const handleDel = (record) => {
		console.log('删除', record);
		Modal.confirm({
			title: '确定删除该条数据？',
			content: '删除后无法恢复，确认删除吗？',
			onOk: async () => {
				const params = { id: record.id };
				const res = await deleteParam(params);
				if (res) {
					message.success('删除成功');
					onSearch();
				}
			},
		});
	};
	/* 弹框编辑 */
	const handleEditModal = async (record) => {
		console.log('弹框编辑', record);
		modalRef.current.showModal(record);
	};

	const columns = [
		{
			title: '序号',
			dataIndex: 'index',
			width: 80,
			render: (_, __, index) => index + 1,
		},
		{
			title: '项目类别名称',
			dataIndex: 'cnName',
		},
		{
			title: '排序',
			dataIndex: 'ordinal',
			width: 80,
			align: 'center',
		},
		{
			title: '操作',
			dataIndex: 'action',
			width: 150,
			render: (_, record, index) => (
				<Space>
					<Button type="link" size={'small'} disabled={index === 0} onClick={() => handleUp(record)}>
						上移
					</Button>
					<Button type="link" size={'small'} disabled={index === dataSource.length - 1} onClick={() => handleDown(record)}>
						下移
					</Button>
					<Button type="link" size={'small'} onClick={() => handleEditModal(record)}>
						编辑
					</Button>
					<Button type="link" size={'small'} danger onClick={() => handleDel(record)}>
						删除
					</Button>
				</Space>
			),
		},
	];
	return (
		<div className={'flex-sub flex flex-direction-column padding-20 bg-color-ffffff margin-20 border-radius-4'}>
			<Title level={4}>节点项目督办项目类别配置</Title>
			<div className={'margin-tb-20 flex justify-between'}>
				<Button type={'primary'} icon={<PlusOutlined />} onClick={handleAdd}>
					新增
				</Button>
				<Form form={form} labelAlign="right" layout="inline">
					<Form.Item label={'关键词'} name="name">
						<Input placeholder="请输入关键词" allowClear />
					</Form.Item>
					<Form.Item>
						<Button type="primary" icon={<SearchOutlined />} onClick={onSearch}>
							查询
						</Button>
					</Form.Item>
					<Form.Item>
						<Button icon={<UndoOutlined />} onClick={onReset}>
							重置
						</Button>
					</Form.Item>
				</Form>
			</div>
			{/* 表格 开始 */}
			<Table rowKey="id" dataSource={dataSource} pagination={false} scroll={{ x: 'max-content' }} columns={columns} />
			<ModalTypeUpdate ref={modalRef} getList={onSearch} />
		</div>
	);
};

export default Index;
