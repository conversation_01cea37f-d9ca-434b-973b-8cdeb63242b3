/**
 * @description ModalUpdate.jsx - 文件描述
 * <AUTHOR>
 *
 * Created on 2024/10/15 12:01
 */
import React, { forwardRef, useImperativeHandle, useState } from 'react';
import { Divider, Form, Input, InputNumber, message, Modal, Radio } from 'antd';
import { LEVEL_OPTIONS } from '../const';
import { paramSave } from '@/api/Bidmgt/important/groupEvent';

const ModalUpdate = forwardRef(({ getList }, ref) => {
	const [visible, setVisible] = useState(false);
	const [details, setDetails] = useState({});
	const [form] = Form.useForm();

	const openModal = (details) => {
		setVisible(true);
		setDetails(details);
		if (!details.add) {
			form.setFieldsValue(details);
		}
	};

	useImperativeHandle(ref, () => {
		return {
			openModal,
		};
	});

	const handleOk = async () => {
		try {
			const values = await form.validateFields();
			console.log(values);
			if (values.parentId === 0) {
				/* 同级则选当前节点的父级ID */
				values.parentId = details?.parentId;
			} else {
				/* 子级 */
				values.parentId = details?.id;
			}
			const params = {
				...values,
				id: details?.add ? undefined : details?.id,
				type: details?.type,
			};
			if (!Number(values.ordinal)) {
				message.error('排序只能为数字');
				return;
			}
			const res = await paramSave(params);
			if (res) {
				getList(params);
				handleCancel();
				message.success('操作成功');
			}
		} catch (e) {
			console.log(e);
		}
	};

	const handleCancel = () => {
		setVisible(false);
		form.resetFields();
	};

	return (
		<Modal
			open={visible}
			onCancel={handleCancel}
			onOk={handleOk}
			title={details?.add ? '新增项目阶段' : '编辑项目阶段'}
			centered
			destroyOnClose={true}
			maskClosable={false}
		>
			<Divider style={{ margin: '24px -24px', width: 'auto' }} />
			<Form form={form} labelCol={{ span: 6 }} initialValues={{ level: 1 }}>
				<Form.Item name="cnName" label="类别名称" rules={[{ required: true, message: '请输入类别名称' }]}>
					<Input placeholder={'请输入类别名称'} />
				</Form.Item>
				<Form.Item name="ordinal" label="序号">
					<InputNumber className={'width-100per'} placeholder={'请输入序号'} />
				</Form.Item>
				{details?.add && (
					<Form.Item name="parentId" label="级别" rules={[{ required: true, message: '请选择级别' }]}>
						<Radio.Group options={LEVEL_OPTIONS} />
					</Form.Item>
				)}
			</Form>
		</Modal>
	);
});
export default ModalUpdate;
