/**
 * @description index.jsx - 节点督办项目阶段配置
 * <AUTHOR>
 *
 * Created on 2024/10/14 11:31
 */
import React, {useEffect, useRef, useState} from 'react';
import {Button, Typography, Input, Form, Space, Table, Modal, message} from 'antd'
import {useTableData} from "@/hook/useTableData";
import ModalUpdate from "./component/ModalUpdate";
import {projectType} from "@/pages/Bidmgt/NodeProjectManage/const";
import {deleteParam, pageParamList} from "@/api/Bidmgt/important/groupEvent";
import {SearchOutlined, UndoOutlined} from "@ant-design/icons";

const {Title} = Typography;
const NodeProjectPhase = () => {
    const nodeRef = useRef();
    const [expandedRowKeys, setExpandedRowKeys] = useState([])
    const [dataSource, setDataSource] = useState([]);
    const [form] = Form.useForm();

    /* 获取状态列表 */
    const getListStatus = async (values) => {
        const params = {
            type: projectType.PROJECT_STATUS,
            ...values
        };
        const res = await pageParamList(params);
        if (res?.data) {
            setDataSource(res.data);
        }
    };

    /* 搜索参数 */
    const onSearch = () => {
        const values = form.getFieldsValue();
        console.log('搜索参数', values);
        getListStatus(values);
    };
    /* 重置表单 */
    const onReset = () => {
        form.resetFields();
    };
    useEffect(() => {
        onSearch();
    }, []);

    /* 修改节点信息 */
    const updateNode = async (node) => {
        /* todo: 查询节点详情~~ */
        nodeRef.current.openModal(node);
    }

    /* 新增节点信息 */
    const addNode = async (node) => {
        /* todo: 新增节点~~ */
        nodeRef.current.openModal({...node, add: true});
    }

    /* 删除节点信息 */
    const deleteNode = async (node) => {
        /* todo: 删除节点~~ */
        Modal.confirm({
            title: '确定删除该节点吗？',
            onOk() {
                return new Promise(async (resolve, reject) => {
                    const res = await deleteParam({id: node.id});
                    if (res) {
                        resolve();
                    }
                }).then(res => {
                    message.success('删除成功');
                    onSearch();
                });
            },
        });
    }

    const columns = [
        // {
        //     title: '序号',
        //     dataIndex: 'index',
        //     key: 'index',
        //     width: 80,
        //     align: 'center',
        //     render: (text, record, index) => {
        //         return index + 1;
        //     }
        // },
        {
            title: '项目阶段名称',
            dataIndex: 'cnName',
            key: 'cnName',
        },
        {
            title: '排序',
            dataIndex: 'ordinal',
            key: 'ordinal',
            width: 80,
            align: 'center',
        },
        {
            title: '操作',
            dataIndex: 'action',
            key: 'action',
            width: 180,
            align: 'center',
            render: (text, record) => (
                <Space.Compact >
                    <Button size={'small'} type={'link'} onClick={() => addNode(record)}>新增</Button>
                    <Button size={'small'} type={'link'} onClick={() => updateNode(record)}>编辑</Button>
                    <Button size={'small'} danger type={'link'} onClick={() => deleteNode(record)}>删除</Button>
                </Space.Compact>
            )
        }
    ];

    /* 展开修改 */
    const onExpandChange = (expandedRows, record) => {
        console.log(expandedRows, record);
        setExpandedRowKeys(expandedRows);
    };
    return (<div className={'flex-sub flex flex-direction-column padding-20 bg-color-ffffff margin-20 border-radius-4'}>
        <Title level={4}>
            节点督办项目阶段配置
        </Title>
        <div className={'flex align-center justify-between margin-tb-12'}>
            <div>
                {/*<Button type={'primary'} onClick={addNode}>新建</Button>*/}
                {/*<Button>编辑</Button>*/}
                {/*<Button danger>删除</Button>*/}
            </div>
            <Form
                form={form}
                labelAlign="right"
                layout="inline"
            >
                <Form.Item label={"关键词"} name="name">
                    <Input placeholder="请输入关键词" allowClear/>
                </Form.Item>
                <Form.Item>
                    <Button type="primary" icon={<SearchOutlined/>} onClick={onSearch}>查询</Button>
                </Form.Item>
                <Form.Item>
                    <Button icon={<UndoOutlined/>} onClick={onReset}>重置</Button>
                </Form.Item>
            </Form>
        </div>
        <Table
            columns={columns}
            dataSource={dataSource}
            pagination={false}
            rowKey={'id'}
            defaultExpandAllRows={true}
            expandedRowKeys={expandedRowKeys}
            onExpandedRowsChange={onExpandChange}
            expandable={{
                childrenColumnName: 'childParams',
            }}
        />
        <ModalUpdate ref={nodeRef} getList={onSearch} />
    </div>)
}
export default NodeProjectPhase;
