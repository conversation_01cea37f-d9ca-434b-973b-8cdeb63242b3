import { useEffect, useState } from 'react';
import RScaleScreen from 'r-scale-screen';
import { Col, Card, Row, Progress, Tooltip } from 'antd';
import { getImageSrc } from '@/assets/images/index';
import './index.scss';
import ChartArea from '@/components/Bidmgt/ChartArea';
import ChartBar from '@/components/Bidmgt/ChartBar';
import { useRouterLink } from '@/hook/useRouter';
import {
	allProjectStatistics,
	projectSixMonthStatistics,
	statistics,
	statisticsUnreadNumber,
} from '@/api/Bidmgt/Dashboard/index';
import { notificationPage } from '@/api/Bidmgt/PersonalCenter/index';
import { pageProjectFollow } from '@/api/Bidmgt/ProjectManage/index';
import { pageProject } from '@/api/Bidmgt/ProjectManage/index';
import { pageCategoryValue } from '@/api/Bidmgt/Dict/index';
import dayjs from 'dayjs';

const weekObj = {
	0: '周日',
	1: '周一',
	2: '周二',
	3: '周三',
	4: '周四',
	5: '周五',
	6: '周六',
};
const Index = () => {
	const { linkTo } = useRouterLink();

	const [projectStatistics, setProjectStatistics] = useState({});
	const [sixMonthStatistics, setSixMonthStatistics] = useState([]);
	const [projectFollows, setProjectFollows] = useState([]);

	const [statisticsArea, setStatisticsArea] = useState([]);
	const [statisticsList, setStatisticsList] = useState([]);

	const [notificeList, setNotificeList] = useState([]);
	const [notificeTotal, setNotificeTotal] = useState(0);

	const [projectList, setProjectList] = useState([]);

	const getDAta = () => {
		allProjectStatistics().then((res) => {
			setProjectStatistics(res.data || {});
		});
		projectSixMonthStatistics().then((res) => {
			setSixMonthStatistics(
				(res.data || [])
					.sort((a, b) => {
						return (
							new Date(`${a.monthTime}-01`).valueOf() -
							new Date(`${b.monthTime}-01`).valueOf()
						);
					})
					.slice(-6)
					.map((ov) => {
						return {
							name: `${ov.monthTime.slice(2)}`,
							value: ov.projectCount,
						};
					})
			);
		});
		statistics().then((res) => {
			setStatisticsList(
				(res.data || []).filter((ov) => ov.investmentResponsibleId)
			);
			const list = (res.data || [])
				.filter((ov) => ov.investmentResponsibleId)
				.reduce((pre, cur) => {
					return [
						...pre,
						{
							xField: cur.investmentResponsibleName || '',
							count: cur.projectCount || 0,
							value: cur.investmentScale || 0,
						},
						{
							xField: cur.investmentResponsibleName || '',
							count: cur.projectCount || 0,
							value: cur.investmentScale || 0,
						},
					];
				}, []);
			setStatisticsArea(list);
		});

		notificationPage({
			pageNum: 1,
			pageSize: 10,
			readFlag: 0,
		}).then((res) => {
			setNotificeList(res.data.records || []);
			setNotificeTotal(res.data.total || []);
		});
		pageCategoryValue({
			categoryCode: 'project_stage',
			pageNum: 1,
			pageSize: 300,
		}).then((reb) => {
			const stepsList = reb.data.records || [];
			pageProjectFollow({
				pageNum: 1,
				pageSize: 2,
			}).then((res) => {
				setProjectFollows(
					(res.data.records || []).map((ov) => {
						const findex = stepsList.findIndex(
							(item) => ov.projectStageId == item.id
						);
						if (findex >= 0) {
							ov.percent = parseInt(
								(findex * 100) / stepsList.length
							);
						} else {
							ov.percent = 0;
						}
						return { ...ov };
					})
				);
			});
		});
		pageProject({
			pageNum: 1,
			pageSize: 4,
		}).then((res) => {
			setProjectList(res.data.records || []);
		});
	};

	useEffect(() => {
		getDAta();
	}, []);

	const [curTooltip, setCurTooltip] = useState('');
	const tooltipOpenChange = (flag, tag) => {
		setCurTooltip(flag ? tag : '');
	};
	const TooltipTitle = (props = {}) => {
		const [obj, setObj] = useState({});
		useEffect(() => {
			const find = statisticsList.find((ov) =>
				ov.investmentResponsibleName.includes(props.str)
			);
			if (find) {
				setObj({ ...find });
			}
		}, [statisticsList]);
		return (
			<div
				className='width-216 height-194 background-no-repeat background-position-center-center background-size-100-100 border-box padding-top-38 padding-left-28 line-height-22 font-size-14'
				style={{
					backgroundImage: `url(${getImageSrc(
						'@/assets/images/DaPing/bg-project-01.png'
					)})`,
				}}
			>
				<div className='font-size-16 font-weight-500 color-ffffff  margin-bottom-6'>
					{obj.investmentResponsibleName || props.detaultName || ''}
				</div>
				<div className='flex align-center justify-start margin-bottom-6'>
					<div className='color-c9cdd4 width-110'>项目总数</div>
					<div className='color-ffffff'>
						{obj.projectCount || '0'}个
					</div>
				</div>
				<div className='flex align-center justify-start margin-bottom-6'>
					<div className='color-c9cdd4 width-110'>投资规模</div>
					<div className='color-ffffff'>
						{obj.investmentScale || '0'}亿
					</div>
				</div>
				<div className='flex align-center justify-start'>
					<div className='color-c9cdd4 width-110'>项目总进度</div>
					<div className='color-ffffff'></div>
				</div>
				<div className='flex align-center justify-start color-ffffff'>
					<Progress
						showInfo={false}
						percent={
							`${obj.projectSchedule || ''}`.replace('%', '') - 0
						}
						trailColor={'#e5e6e8'}
						className='margin-0'
					/>
					<div className='padding-left-6 padding-right-10'>
						{obj.projectSchedule || '0'}
					</div>
				</div>
			</div>
		);
	};

	return (
		<div className='flex-sub flex flex-direction-column'>
			<RScaleScreen
				height={1080}
				width={1920}
				boxStyle={{
					zIndex: 12,
					position: 'fixed',
					top: 0,
					left: 0,
					backgroundImage: `url(${getImageSrc(
						'@/assets/images/DaPing/bg-footer.png'
					)}),url(${getImageSrc(
						'@/assets/images/DaPing/bg-full-screen.png'
					)})`,
					backgroundRepeat: 'no-repeat,no-repeat',
					backgroundPosition: 'bottom center,center center',
					backgroundSize: 'auto 46px,100% 100%',
				}}
			>
				{/* 顶部 开始 */}
				<div
					className='flex justify-between align-start height-116 padding-lr-40 background-no-repeat'
					style={{
						backgroundImage: `url(${getImageSrc(
							'@/assets/images/DaPing/header-bg.png'
						)})`,
						backgroundPosition: 'center top',
						backgroundSize: '100% 138px',
					}}
				>
					<div className='width-300 color-56fefe padding-top-22 font-size-20 line-height-28 letter-spacing-2'>
						{dayjs().format('YYYY年MM月DD日')}{' '}
						{weekObj[dayjs().day()] || ''}
					</div>
					<div className='flex height-66 overflow-hidden padding-top-36 border-box'>
						<img
							className='width-436 height-30 margin-0-auto'
							src={getImageSrc(
								'@/assets/images/DaPing/header-title.png'
							)}
						/>
					</div>
					<div className='width-300 flex justify-end align-start'>
						<div
							className='width-230 height-78 background-no-repeat a'
							style={{
								backgroundImage: `url(${getImageSrc(
									'@/assets/images/DaPing/header-bg-right.png'
								)})`,
								backgroundPosition: 'right top',
								backgroundSize: '100% 78px',
							}}
							onClick={() => {
								if (document && document.exitFullscreen) {
									document.exitFullscreen();
								}
								linkTo('/bidmgt/dashboard');
							}}
						>
							<div className='font-size-20 line-height-22 color-ffffff padding-top-24 padding-left-80'>
								进入工作台
							</div>
						</div>
					</div>
				</div>
				{/* 顶部 结束 */}

				<div className='padding-lr-20 flex justify-between align-stretch'>
					{/* 左边 开始 */}
					<div className='flex-shrink'>
						<div className='flex justify-center align-center'>
							<div
								className='width-448 height-300 boxder-box background-position-bottom-right background-no-repeat background-size-100-100'
								style={{
									backgroundImage: `url(${getImageSrc(
										'@/assets/images/DaPing/bg-project.png'
									)})`,
								}}
							>
								<div className='padding-lr-30 padding-tb-14'>
									<img
										className='width-auto height-18'
										src={getImageSrc(
											'@/assets/images/DaPing/project-new.png'
										)}
										alt='近半年新增项目'
									/>
								</div>
								<ChartArea
									className='width-428 height-220 padding-10'
									config={{
										data: sixMonthStatistics,
										xField: 'name',
										yField: 'value',
										meta: {
											value: {
												alias: '当月新增',
											},
										},
										geometryOptions: [
											{
												geometry: 'area',
												smooth: true,
												isPercent: true,
												lineStyle: {
													lineWidth: 2,
												},
												point: {},
											},
										],
										yAxis: {
											value: {
												// min: 0,
												label: {},
												grid: {
													line: {
														style: {
															lineDash: [4, 5],
														},
													},
												},
											},
										},
									}}
								/>
							</div>
							<div
								className='width-70 height-200 margin-left-30 background-position-center-center background-no-repeat background-size-100-100'
								style={{
									backgroundImage: `url(${getImageSrc(
										'@/assets/images/DaPing/bg-project-new.png'
									)})`,
								}}
							></div>
						</div>
						<div className='flex justify-center align-center margin-tb-20'>
							<div
								className='width-448 height-300 boxder-box background-position-bottom-right background-no-repeat background-size-100-100'
								style={{
									backgroundImage: `url(${getImageSrc(
										'@/assets/images/DaPing/bg-project.png'
									)})`,
								}}
							>
								<div className='padding-lr-30 padding-tb-14'>
									<img
										className='width-auto height-18'
										src={getImageSrc(
											'@/assets/images/DaPing/project-number.png'
										)}
										alt='各责任单位项目数量'
									/>
								</div>
								<ChartBar
									className='width-448 height-250'
									config={{
										data: statisticsArea,
										xField: 'count',
										yField: 'xField',
										meta: {
											count: {
												alias: '项目数量',
												formatter: (val) => `${val} 个`,
											},
											value: {
												alias: '项目规模',
												formatter: (val) => `${val} 亿`,
											},
										},
										minBarWidth: 10,
										maxBarWidth: 10,
									}}
								/>
							</div>
							<div
								className='width-70 height-200 margin-left-30 background-position-center-center background-no-repeat background-size-100-100'
								style={{
									backgroundImage: `url(${getImageSrc(
										'@/assets/images/DaPing/bg-project-number.png'
									)})`,
								}}
							></div>
						</div>
						<div className='flex justify-center align-center'>
							<div
								className='width-448 height-300 boxder-box background-position-bottom-right background-no-repeat background-size-100-100'
								style={{
									backgroundImage: `url(${getImageSrc(
										'@/assets/images/DaPing/bg-project.png'
									)})`,
								}}
							>
								<div className='padding-lr-30 padding-tb-14'>
									<img
										className='width-auto height-18'
										src={getImageSrc(
											'@/assets/images/DaPing/project-size.png'
										)}
										alt='各责任单位项目规模'
									/>
								</div>

								<ChartBar
									className='width-448 height-250'
									config={{
										data: statisticsArea,
										xField: 'value',
										yField: 'xField',
										meta: {
											count: {
												alias: '项目数量',
												formatter: (val) => `${val} 个`,
											},
											value: {
												alias: '项目规模',
												formatter: (val) => `${val} 亿`,
											},
										},
										minBarWidth: 10,
										maxBarWidth: 10,
									}}
								/>
							</div>
							<div
								className='width-70 height-200 margin-left-30 background-position-center-center background-no-repeat background-size-100-100'
								style={{
									backgroundImage: `url(${getImageSrc(
										'@/assets/images/DaPing/bg-project-size.png'
									)})`,
								}}
							></div>
						</div>
					</div>
					{/* 左边 结束 */}

					{/* 中间 开始 */}
					<div className='flex-sub color-ffffff flex justify-between flex-direction-column'>
						<div className='height-650 justify-center align-start flex'>
							<div
								className='position-relative a'
								style={{ width: '643px' }}
							>
								<img
									className='height-auto margin-0-auto a'
									style={{ width: '643px' }}
									src={getImageSrc(
										'@/assets/images/DaPing/map-wuhanjiangxia-empty.png'
									)}
								/>
								<div className='position-absolute top-0 left-0 width-100per height-100per text-align-center font-size-18'>
									<Tooltip
										placement='leftTop'
										title={
											<TooltipTitle
												str='金'
												detaultName='金水'
											/>
										}
										arrow={false}
										trigger='click'
										color={'none'}
										onOpenChange={(e) =>
											tooltipOpenChange(e, '金')
										}
									>
										<div className='position-box position-absolute top-30per left-16per padding-left-20 padding-right-40'>
											<img
												className={`width-34 height-44 ${
													curTooltip == '金'
														? ''
														: 'opacity-0'
												}`}
												src={getImageSrc(
													'@/assets/images/DaPing/map-location.png'
												)}
											/>
											<div className='font-size-18'>
												金水
											</div>
										</div>
									</Tooltip>
									<Tooltip
										placement='leftTop'
										title={
											<TooltipTitle
												str='郑'
												detaultName='郑店'
											/>
										}
										arrow={false}
										trigger='click'
										color={'none'}
										onOpenChange={(e) =>
											tooltipOpenChange(e, '郑')
										}
									>
										<div className='position-box position-absolute top-30per left-34per padding-left-12 padding-right-30 padding-tb-20'>
											<img
												className={`width-34 height-44 ${
													curTooltip == '郑'
														? ''
														: 'opacity-0'
												}`}
												src={getImageSrc(
													'@/assets/images/DaPing/map-location.png'
												)}
											/>
											<div className='font-size-18'>
												郑店
											</div>
										</div>
									</Tooltip>
									<Tooltip
										placement='leftTop'
										title={
											<TooltipTitle
												str='大'
												detaultName='大桥'
											/>
										}
										arrow={false}
										trigger='click'
										color={'none'}
										onOpenChange={(e) =>
											tooltipOpenChange(e, '大')
										}
									>
										<div className='position-box position-absolute top-6per left-42per'>
											<img
												className={`width-34 height-44 ${
													curTooltip == '大'
														? ''
														: 'opacity-0'
												}`}
												src={getImageSrc(
													'@/assets/images/DaPing/map-location.png'
												)}
											/>
											<div className='font-size-18'>
												大桥
											</div>
										</div>
									</Tooltip>
									<Tooltip
										placement='leftTop'
										title={
											<TooltipTitle
												str='庙'
												detaultName='庙山'
											/>
										}
										arrow={false}
										trigger='click'
										color={'none'}
										onOpenChange={(e) =>
											tooltipOpenChange(e, '庙')
										}
									>
										<div className='position-box position-absolute top-8per left-56per'>
											<img
												className={`width-34 height-44 ${
													curTooltip == '庙'
														? ''
														: 'opacity-0'
												}`}
												src={getImageSrc(
													'@/assets/images/DaPing/map-location.png'
												)}
											/>
											<div className='font-size-18'>
												庙山
											</div>
										</div>
									</Tooltip>
									<Tooltip
										placement='leftTop'
										title={
											<TooltipTitle
												str='藏'
												detaultName='藏龙岛'
											/>
										}
										arrow={false}
										trigger='click'
										color={'none'}
										onOpenChange={(e) =>
											tooltipOpenChange(e, '藏')
										}
									>
										<div className='position-box position-absolute top-10per left-66per'>
											<img
												className={`width-34 height-44 ${
													curTooltip == '藏'
														? ''
														: 'opacity-0'
												}`}
												src={getImageSrc(
													'@/assets/images/DaPing/map-location.png'
												)}
											/>
											<div className='font-size-18'>
												藏龙岛
											</div>
										</div>
									</Tooltip>
									<Tooltip
										placement='leftTop'
										title={
											<TooltipTitle
												str='纸'
												detaultName='纸坊'
											/>
										}
										arrow={false}
										trigger='click'
										color={'none'}
										onOpenChange={(e) =>
											tooltipOpenChange(e, '纸')
										}
									>
										<div className='position-box position-absolute top-24per left-50per'>
											<img
												className={`width-34 height-44 ${
													curTooltip == '纸'
														? ''
														: 'opacity-0'
												}`}
												src={getImageSrc(
													'@/assets/images/DaPing/map-location.png'
												)}
											/>
											<div className='font-size-18'>
												纸坊
											</div>
										</div>
									</Tooltip>
									<Tooltip
										placement='leftTop'
										title={
											<TooltipTitle
												str='五'
												detaultName='五里界'
											/>
										}
										arrow={false}
										trigger='click'
										color={'none'}
										onOpenChange={(e) =>
											tooltipOpenChange(e, '五')
										}
									>
										<div className='position-box position-absolute top-28per left-68per padding-lr-40 padding-bottom-26'>
											<img
												className={`width-34 height-44 ${
													curTooltip == '五'
														? ''
														: 'opacity-0'
												}`}
												src={getImageSrc(
													'@/assets/images/DaPing/map-location.png'
												)}
											/>
											<div className='font-size-18'>
												五里界
											</div>
										</div>
									</Tooltip>
									<Tooltip
										placement='leftTop'
										title={
											<TooltipTitle
												str='乌'
												detaultName='乌龙泉'
											/>
										}
										arrow={false}
										trigger='click'
										color={'none'}
										onOpenChange={(e) =>
											tooltipOpenChange(e, '乌')
										}
									>
										<div className='position-box position-absolute top-42per left-50per padding-lr-36 padding-bottom-18'>
											<img
												className={`width-34 height-44 ${
													curTooltip == '乌'
														? ''
														: 'opacity-0'
												}`}
												src={getImageSrc(
													'@/assets/images/DaPing/map-location.png'
												)}
											/>
											<div className='font-size-18'>
												乌龙泉
											</div>
										</div>
									</Tooltip>
									<Tooltip
										placement='leftTop'
										title={
											<TooltipTitle
												str='法'
												detaultName='法寺'
											/>
										}
										arrow={false}
										trigger='click'
										color={'none'}
										onOpenChange={(e) =>
											tooltipOpenChange(e, '法')
										}
									>
										<div className='position-box position-absolute top-54per left-24per padding-lr-16 padding-bottom-26'>
											<img
												className={`width-34 height-44 ${
													curTooltip == '法'
														? ''
														: 'opacity-0'
												}`}
												src={getImageSrc(
													'@/assets/images/DaPing/map-location.png'
												)}
											/>
											<div className='font-size-18'>
												法寺
											</div>
										</div>
									</Tooltip>
									<Tooltip
										placement='leftTop'
										title={
											<TooltipTitle
												str='安山'
												detaultName='安山'
											/>
										}
										arrow={false}
										trigger='click'
										color={'none'}
										onOpenChange={(e) =>
											tooltipOpenChange(e, '安山')
										}
									>
										<div className='position-box position-absolute top-54per left-38per padding-left-18 padding-right-26 padding-bottom-30'>
											<img
												className={`width-34 height-44 ${
													curTooltip == '安山'
														? ''
														: 'opacity-0'
												}`}
												src={getImageSrc(
													'@/assets/images/DaPing/map-location.png'
												)}
											/>
											<div className='font-size-18'>
												安山
											</div>
										</div>
									</Tooltip>
									<Tooltip
										placement='leftTop'
										title={
											<TooltipTitle
												str='山坡'
												detaultName='山坡'
											/>
										}
										arrow={false}
										trigger='click'
										color={'none'}
										onOpenChange={(e) =>
											tooltipOpenChange(e, '山坡')
										}
									>
										<div className='position-box position-absolute top-68per left-50per padding-lr-40 padding-bottom-40'>
											<img
												className={`width-34 height-44 ${
													curTooltip == '山坡'
														? ''
														: 'opacity-0'
												}`}
												src={getImageSrc(
													'@/assets/images/DaPing/map-location.png'
												)}
											/>
											<div className='font-size-18'>
												山坡
											</div>
										</div>
									</Tooltip>
									<Tooltip
										placement='leftTop'
										title={
											<TooltipTitle
												str='舒安'
												detaultName='舒安'
											/>
										}
										arrow={false}
										trigger='click'
										color={'none'}
										onOpenChange={(e) =>
											tooltipOpenChange(e, '舒安')
										}
									>
										<div className='position-box position-absolute top-50per left-78per padding-right-16 padding-bottom-100'>
											<img
												className={`width-34 height-44 ${
													curTooltip == '舒安'
														? ''
														: 'opacity-0'
												}`}
												src={getImageSrc(
													'@/assets/images/DaPing/map-location.png'
												)}
											/>
											<div className='font-size-18'>
												舒安
											</div>
										</div>
									</Tooltip>
									<Tooltip
										placement='leftTop'
										title={
											<TooltipTitle
												str='湖四'
												detaultName='湖四'
											/>
										}
										arrow={false}
										trigger='click'
										color={'none'}
										onOpenChange={(e) =>
											tooltipOpenChange(e, '湖四')
										}
									>
										<div className='position-box position-absolute top-76per left-70per padding-left-20 padding-right-10 padding-bottom-20'>
											<img
												className={`width-34 height-44 ${
													curTooltip == '湖四'
														? ''
														: 'opacity-0'
												}`}
												src={getImageSrc(
													'@/assets/images/DaPing/map-location.png'
												)}
											/>
											<div className='font-size-18'>
												湖四
											</div>
										</div>
									</Tooltip>
								</div>
							</div>
						</div>
						<div className='height-200 flex justify-around align-end padding-lr-60 padding-bottom-30'>
							<div className='width-146 text-align-center'>
								<div
									className='background-no-repeat'
									style={{
										backgroundImage: `url(${getImageSrc(
											'@/assets/images/DaPing/bg-counts.png'
										)})`,
										backgroundPosition: 'center top',
										backgroundSize: '98px 90px',
									}}
								>
									<div className='font-size-28'>
										{projectStatistics.projectCount || '0'}
										个
									</div>
									<div
										className='margin-top-70 font-size-18 line-height-26 background-no-repeat background-position-center-center background-size-100-100'
										style={{
											backgroundImage: `url(${getImageSrc(
												'@/assets/images/DaPing/bg-counts-text.png'
											)})`,
										}}
									>
										项目总数
									</div>
								</div>
							</div>
							<div className='width-146 text-align-center padding-bottom-30'>
								<div
									className='background-no-repeat'
									style={{
										backgroundImage: `url(${getImageSrc(
											'@/assets/images/DaPing/bg-counts.png'
										)})`,
										backgroundPosition: 'center top',
										backgroundSize: '98px 90px',
									}}
								>
									<div className='font-size-28'>
										{projectStatistics.investmentScale ||
											'0'}
										亿
									</div>
									<div
										className='margin-top-70 font-size-18 line-height-26 background-no-repeat background-position-center-center background-size-100-100'
										style={{
											backgroundImage: `url(${getImageSrc(
												'@/assets/images/DaPing/bg-counts-text.png'
											)})`,
										}}
									>
										投资金额
									</div>
								</div>
							</div>
							<div className='width-146 text-align-center padding-bottom-30'>
								<div
									className='background-no-repeat'
									style={{
										backgroundImage: `url(${getImageSrc(
											'@/assets/images/DaPing/bg-counts.png'
										)})`,
										backgroundPosition: 'center top',
										backgroundSize: '98px 90px',
									}}
								>
									<div className='font-size-28'>
										{projectStatistics.projectSigningCount ||
											'0'}
										个
									</div>
									<div
										className='margin-top-70 font-size-18 line-height-26 background-no-repeat background-position-center-center background-size-100-100'
										style={{
											backgroundImage: `url(${getImageSrc(
												'@/assets/images/DaPing/bg-counts-text.png'
											)})`,
										}}
									>
										签约总数
									</div>
								</div>
							</div>
							<div className='width-146 text-align-center'>
								<div
									className='background-no-repeat'
									style={{
										backgroundImage: `url(${getImageSrc(
											'@/assets/images/DaPing/bg-counts.png'
										)})`,
										backgroundPosition: 'center top',
										backgroundSize: '98px 90px',
									}}
								>
									<div className='font-size-28'>
										{projectStatistics.projectSchedule ||
											'0%'}
									</div>
									<div
										className='margin-top-70 font-size-18 line-height-26 background-no-repeat background-position-center-center background-size-100-100'
										style={{
											backgroundImage: `url(${getImageSrc(
												'@/assets/images/DaPing/bg-counts-text.png'
											)})`,
										}}
									>
										签约转化率
									</div>
								</div>
							</div>
						</div>
					</div>
					{/* 中间 结束 */}

					{/* 右边 开始 */}
					<div className='flex-shrink'>
						<div className='flex justify-center align-center'>
							<div
								className='width-70 height-200 margin-right-30 background-position-center-center background-no-repeat background-size-100-100'
								style={{
									backgroundImage: `url(${getImageSrc(
										'@/assets/images/DaPing/bg-project-track.png'
									)})`,
								}}
							></div>
							<div
								className='width-448 height-300 boxder-box background-position-bottom-right background-no-repeat background-size-100-100'
								style={{
									backgroundImage: `url(${getImageSrc(
										'@/assets/images/DaPing/bg-project.png'
									)})`,
								}}
							>
								<div className='padding-lr-30 padding-tb-14'>
									<img
										className='width-auto height-18'
										src={getImageSrc(
											'@/assets/images/DaPing/project-track.png'
										)}
										alt='追踪项目'
									/>
								</div>

								<div className='padding-lr-20 padding-top-16'>
									{projectFollows.map((ov, oi) => {
										return (
											<div
												key={ov.id}
												className={`padding-lr-8 padding-tb-12 border-radius-4 a ${
													oi > 0
														? 'margin-top-16'
														: ''
												}`}
												style={{
													background:
														'rgba(144, 255, 253, 0.1)',
												}}
												onClick={() => {
													linkTo(
														`/bidmgt/projectManage/detail?id=${ov.id}`
													);
												}}
											>
												<div className='flex align-center justify-start'>
													<div className='width-4 height-12 bg-color-165dff flex-shrink border-radius-2'></div>
													<div className='font-size-16 font-weight-500 line-height-22 text-cut padding-left-16 color-14aef3'>
														{ov.projectName || ''}
													</div>
												</div>
												<div className='flex align-center justify-start font-size-14 line-height-22 padding-left-16 margin-tb-4 color-ffd07d'>
													<div className='text-cut flex-shrink'>
														{ov.investmentScale ||
															'0'}
														亿元
													</div>
													<div className='padding-left-12 text-cut'>
														{(
															ov.industryListName ||
															[]
														).join('/')}
													</div>
													<div className='padding-left-12 text-cut'>
														{ov.investmentResponsibleName ||
															''}
													</div>
												</div>
												<div className='flex align-center justify-start font-size-14 color-ffffff'>
													<Progress
														showInfo={false}
														percent={ov.percent}
														trailColor={'#e5e6e8'}
														className='margin-0'
													/>
													<div className='padding-left-6'>
														{ov.percent}%
													</div>
												</div>
											</div>
										);
									})}
								</div>
							</div>
						</div>
						<div className='flex justify-center align-center margin-tb-20'>
							<div
								className='width-70 height-200 margin-right-30 background-position-center-center background-no-repeat background-size-100-100'
								style={{
									backgroundImage: `url(${getImageSrc(
										'@/assets/images/DaPing/bg-project-trends.png'
									)})`,
								}}
							></div>
							<div
								className='width-448 height-300 boxder-box background-position-bottom-right background-no-repeat background-size-100-100'
								style={{
									backgroundImage: `url(${getImageSrc(
										'@/assets/images/DaPing/bg-project.png'
									)})`,
								}}
							>
								<div className='padding-lr-30 padding-tb-14'>
									<img
										className='width-auto height-18'
										src={getImageSrc(
											'@/assets/images/DaPing/project-trends.png'
										)}
										alt='项目动态'
									/>
								</div>
								<div className='padding-lr-20 padding-top-16 line-height-20'>
									<div
										className='flex justify-start align-center color-08a4ff padding-tb-4'
										style={{
											background: 'rgba(0,74,190,0.36)',
										}}
									>
										<div className='width-26 flex-shrink'></div>
										<div className='width-160  flex-shrink'>
											项目
										</div>
										<div className='width-72 flex-shrink margin-lr-4'>
											编号
										</div>
										<div className='flex-sub'>动态</div>
									</div>
									{projectList.slice(0, 4).map((ov, oi) => {
										return (
											<div
												key={ov.id}
												className={`flex justify-start align-center color-e5e6e8 padding-tb-4 a`}
												style={{
													background:
														'radial-gradient(#04245D 0%, rgba(27,40,138,0) 100%)',
													borderBottom:
														'1px solid rgba(50, 197, 255, 0.30)',
												}}
												onClick={() => {
													linkTo(
														`/bidmgt/projectManage/detail?id=${ov.id}`
													);
												}}
											>
												<div className='width-26 flex-shrink'>
													<img
														src={getImageSrc(
															'@/assets/images/DaPing/icon-table-left.png'
														)}
														className='width-26'
													/>
												</div>
												<div className='width-160 flex-shrink text-cut-2'>
													{ov.projectName || ''}
												</div>
												<div className='width-72 flex-shrink text-cut-2 margin-lr-4'>
													{ov.projectCode || ''}
												</div>
												<div className='flex-sub text-cut-2'>
													{ov.latestDevelopments ||
														''}
												</div>
											</div>
										);
									})}
								</div>
							</div>
						</div>
						<div className='flex justify-center align-center'>
							<div
								className='width-70 height-200 margin-right-30 background-position-center-center background-no-repeat background-size-100-100'
								style={{
									backgroundImage: `url(${getImageSrc(
										'@/assets/images/DaPing/bg-project-schedule.png'
									)})`,
								}}
							></div>
							<div
								className='width-448 height-300 boxder-box background-position-bottom-right background-no-repeat background-size-100-100 flex flex-direction-column'
								style={{
									backgroundImage: `url(${getImageSrc(
										'@/assets/images/DaPing/bg-project.png'
									)})`,
								}}
							>
								<div className='padding-lr-30 padding-tb-14 flex-shrink'>
									<img
										className='width-auto height-18'
										src={getImageSrc(
											'@/assets/images/DaPing/project-schedule.png'
										)}
										alt='各单位项目进度'
									/>
								</div>
								<div className='padding-lr-20 padding-tb-16 flex-sub overflow-hidden color-91fffd overflowY-auto'>
									{[...statisticsList].map((ov, oi) => {
										return (
											<div
												key={ov.investmentResponsibleId}
												className={`a ${
													oi > 0
														? 'margin-top-12'
														: ''
												}`}
												onClick={() => {
													linkTo(
														`/bidmgt/projectManage/list?`
													);
												}}
											>
												<div className='flex align-center justify-between'>
													<div className='font-size-14 font-weight-500 line-height-22 text-cut'>
														{ov.investmentResponsibleName ||
															''}
													</div>
													<div className='font-size-14 line-height-22 color-86909c flex-shrink'>
														{ov.projectSigningCount ||
															'0'}
														/
														{ov.projectCount || '0'}
													</div>
												</div>
												<Progress
													percent={
														`${
															ov.projectSchedule ||
															''
														}`.replace('%', '') - 0
													}
													className='margin-0'
													showInfo={false}
													trailColor={'#e5e6e8'}
												/>
											</div>
										);
									})}
								</div>
							</div>
						</div>
					</div>
					{/* 右边 结束 */}
				</div>
			</RScaleScreen>
		</div>
	);
};

export default Index;
