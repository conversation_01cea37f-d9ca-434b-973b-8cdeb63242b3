import { useEffect, useState } from 'react';
import { Col, Card, Row, Progress } from 'antd';
import { getImageSrc } from '@/assets/images/index';
import { RightOutlined } from '@ant-design/icons';
import ChartColumn from '@/components/Bidmgt/ChartColumn';
import ChartDualAxes from '@/components/ChartDualAxes';
import {
	allProjectStatistics,
	projectSixMonthStatistics,
	statistics,
	statisticsUnreadNumber,
} from '@/api/Bidmgt/Dashboard/index';
import { notificationPage } from '@/api/Bidmgt/PersonalCenter/index';
import { pageProjectFollow } from '@/api/Bidmgt/ProjectManage/index';
import { pageProject } from '@/api/Bidmgt/ProjectManage/index';
import { pageCategoryValue } from '@/api/Bidmgt/Dict/index';
import { useRouterLink } from '@/hook/useRouter';
const Index = () => {
	const { linkTo } = useRouterLink();

	const [projectStatistics, setProjectStatistics] = useState({});
	const [sixMonthStatistics, setSixMonthStatistics] = useState([]);
	const [projectFollows, setProjectFollows] = useState([]);

	const [statisticsArea, setStatisticsArea] = useState([]);
	const [statisticsList, setStatisticsList] = useState([]);

	const [notificeList, setNotificeList] = useState([]);
	const [notificeTotal, setNotificeTotal] = useState(0);

	const [projectList, setProjectList] = useState([]);
	const getDAta = () => {
		allProjectStatistics().then((res) => {
			setProjectStatistics(res.data || {});
		});
		projectSixMonthStatistics().then((res) => {
			setSixMonthStatistics(
				(res.data || [])
					.sort((a, b) => {
						return (
							new Date(`${a.monthTime}-01`).valueOf() -
							new Date(`${b.monthTime}-01`).valueOf()
						);
					})
					.slice(-6)
					.map((ov) => {
						return {
							name: `${ov.monthTime.slice(5)}月`,
							value: ov.projectCount,
						};
					})
			);
		});
		statistics().then((res) => {
			setStatisticsList(
				(res.data || []).filter((ov) => ov.investmentResponsibleId)
			);
			const list = (res.data || [])
				.filter((ov) => ov.investmentResponsibleId)
				.reduce((pre, cur) => {
					return [
						...pre,
						{
							xField: cur.investmentResponsibleName || '',
							count: cur.projectCount || 0,
							name: '各责任单位项目数量',
						},
						{
							xField: cur.investmentResponsibleName || '',
							value: cur.investmentScale || 0,
							name: '各责任单位项目规模',
						},
					];
				}, []);
			setStatisticsArea(list);
		});
		// statisticsUnreadNumber().then()

		notificationPage({
			pageNum: 1,
			pageSize: 10,
			readFlag: 0,
		}).then((res) => {
			setNotificeList(res.data.records || []);
			setNotificeTotal(res.data.total || []);
		});
		pageCategoryValue({
			categoryCode: 'project_stage',
			pageNum: 1,
			pageSize: 300,
		}).then((reb) => {
			const stepsList = reb.data.records || [];
			pageProjectFollow({
				pageNum: 1,
				pageSize: 5,
			}).then((res) => {
				setProjectFollows(
					(res.data.records || []).map((ov) => {
						const findex = stepsList.findIndex(
							(item) => ov.projectStageId == item.id
						);
						if (findex >= 0) {
							ov.percent = parseInt(
								(findex * 100) / stepsList.length
							);
						} else {
							ov.percent = 0;
						}
						return { ...ov };
					})
				);
			});
		});
		pageProject({
			pageNum: 1,
			pageSize: 2,
		}).then((res) => {
			setProjectList(res.data.records || []);
		});
	};
	useEffect(() => {
		getDAta();
	}, []);
	return (
		<div className='flex-sub flex padding-20'>
			<div className='flex-sub'>
				{/* 顶部 统计 开始 */}
				<Row gutter={18} className='color-1d2129'>
					<Col span={6}>
						<Card
							size='small'
							title={
								<div className='flex align-center justify-start height-44'>
									<div className='font-size-16 font-weight-500'>
										项目总数
									</div>
									<div className='font-size-14 font-weight-400 color-86909c'>
										（个）
									</div>
								</div>
							}
						>
							<div className='flex align-end justify-between'>
								<div className='font-size-32 line-height-40 font-bold flex-sub'>
									{projectStatistics.projectCount || '0'}
								</div>
								<div className='width-54 height-54'>
									<img
										src={getImageSrc(
											'@/assets/images/Dashboard/icon-project-totals.png'
										)}
										className='width-54 height-54'
									/>
								</div>
							</div>
						</Card>
					</Col>
					<Col span={6}>
						<Card
							size='small'
							title={
								<div className='flex align-center justify-start height-44'>
									<div className='font-size-16 font-weight-500'>
										投资金额
									</div>
									<div className='font-size-14 font-weight-400 color-86909c'>
										（亿元）
									</div>
								</div>
							}
						>
							<div className='flex align-end justify-between'>
								<div className='font-size-32 line-height-40 font-bold flex-sub'>
									{projectStatistics.investmentScale || '0'}
								</div>
								<div className='width-54 height-54'>
									<img
										src={getImageSrc(
											'@/assets/images/Dashboard/icon-investment.png'
										)}
										className='width-54 height-54'
									/>
								</div>
							</div>
						</Card>
					</Col>
					<Col span={6}>
						<Card
							size='small'
							title={
								<div className='flex align-center justify-start height-44'>
									<div className='font-size-16 font-weight-500'>
										签约总数
									</div>
									<div className='font-size-14 font-weight-400 color-86909c'>
										（个）
									</div>
								</div>
							}
						>
							<div className='flex align-end justify-between'>
								<div className='font-size-32 line-height-40 font-bold flex-sub'>
									{projectStatistics.projectSigningCount ||
										'0'}
								</div>
								<div className='width-54 height-54'>
									<img
										src={getImageSrc(
											'@/assets/images/Dashboard/icon-sign-totals.png'
										)}
										className='width-54 height-54'
									/>
								</div>
							</div>
						</Card>
					</Col>
					<Col span={6}>
						<Card
							size='small'
							title={
								<div className='flex align-center justify-start height-44'>
									<div className='font-size-16 font-weight-500'>
										签约转化率
									</div>
									<div className='font-size-14 font-weight-400 color-86909c'>
										（%）
									</div>
								</div>
							}
						>
							<div className='flex align-end justify-between'>
								<div className='font-size-32 line-height-40 font-bold flex-sub'>
									{projectStatistics.projectSchedule || '0'}
								</div>
								<div className='width-54 height-54'>
									<img
										src={getImageSrc(
											'@/assets/images/Dashboard/icon-conversion-rate.png'
										)}
										className='width-54 height-54'
									/>
								</div>
							</div>
						</Card>
					</Col>
				</Row>
				{/* 顶部 统计 结束 */}

				{/* 各责任单位项目数量/规模 开始 */}
				<Card
					size='small'
					bodyStyle={{ padding: '0 20px' }}
					className='height-384 margin-top-20 position-relative'
				>
					<div className='flex align-center justify-start height-44 position-absolute top-0 left-20 z-index-10'>
						<div className='font-size-16 font-weight-500'>
							各责任单位项目数量/规模
						</div>
					</div>
					<ChartDualAxes
						className='height-360 padding-tb-10'
						config={{
							data: [
								statisticsArea.filter((ov) =>
									ov.hasOwnProperty('count')
								),
								statisticsArea.filter((ov) =>
									ov.hasOwnProperty('value')
								),
							],
							xField: 'xField',
							yField: ['count', 'value'],
							meta: {
								count: {
									alias: '项目数量',
									formatter: (val) => `${val} 个`,
								},
								value: {
									alias: '项目规模',
									formatter: (val) => `${val} 亿`,
								},
							},
							geometryOptions: [
								{
									geometry: 'line',
									smooth: true,
									isPercent: true,
									lineStyle: {
										lineWidth: 2,
									},
									point: {},
								},
								{
									geometry: 'line',
									smooth: true,
									isPercent: true,
									lineStyle: {
										lineWidth: 2,
									},
									point: {},
								},
							],
							yAxis: {
								count: {
									// min: 0,
									label: {},
									grid: {
										line: {
											style: {
												lineDash: [4, 5],
											},
										},
									},
								},
								value: {
									// min: 0,
									label: {},
								},
							},
							legend: {
								layout: 'horizontal',
								position: 'top-right',
							},
							title: '各责任单位项目数量/规模',
						}}
					/>
				</Card>
				{/* 各责任单位项目数量/规模 结束 */}

				{/* 底部 统计 开始 */}
				<Row gutter={18} className='color-1d2129  margin-top-20'>
					<Col span={12}>
						{/* 近半年新增项目 开始 */}
						<Card
							size='small'
							bodyStyle={{ padding: '0 20px' }}
							className='height-264'
						>
							<div className='flex align-center justify-start height-44'>
								<div className='font-size-16 font-weight-500'>
									近半年新增项目
								</div>
							</div>
							<ChartColumn
								className='height-210'
								config={{
									data: sixMonthStatistics,
									xField: 'name',
									yField: 'value',
									meta: {
										value: {
											alias: '当月新增',
											formatter: (val) => `${val} 个`,
										},
									},
									axis: {
										x: {},
										y: {},
									},
									minColumnWidth: 20,
									maxColumnWidth: 20,
								}}
							/>
						</Card>
						{/* 近半年新增项目 结束 */}
					</Col>
					<Col span={12}>
						{/* 近半年新增项目 开始 */}
						<Card
							size='small'
							bodyStyle={{ padding: '0 20px' }}
							className='height-264'
						>
							<div className='flex align-center justify-between height-44'>
								<div className='font-size-16 font-weight-500'>
									项目动态
								</div>
								<div
									className='flex align-center justify-end font-size-12 color-86909c a'
									onClick={() => {
										linkTo('/bidmgt/projectManage/list');
									}}
								>
									<div>查看全部</div>
									<RightOutlined className='font-size-12' />
								</div>
							</div>
							{projectList.slice(0, 2).map((ov, oi) => {
								return (
									<div
										key={ov.id}
										className={`padding-8 border-radius-4 a ${
											oi > 0 ? 'margin-top-12' : ''
										}`}
										style={{
											background:
												'linear-gradient(180deg, #EBF5FF 0%, #FFFFFF 100%)',
										}}
										onClick={() => {
											linkTo(
												`/bidmgt/projectManage/detail?id=${ov.id}`
											);
										}}
									>
										<div className='flex align-center justify-start'>
											<div className='width-6 height-6 border-radius-4 bg-color-165dff flex-shrink'></div>
											<div className='font-size-14 font-weight-500 line-height-22 text-cut padding-left-4'>
												{ov.projectName || ''}
											</div>
										</div>
										{ov.projectCode && (
											<div className='flex align-center justify-start font-size-12 line-height-20 padding-left-10 margin-tb-4 color-86909c'>
												项目编号：{ov.projectCode || ''}
											</div>
										)}
										<div className='flex align-center justify-start font-size-12 line-height-20 padding-left-10 color-86909c text-cut'>
											项目动态：
											{ov.latestDevelopments || ''}
										</div>
									</div>
								);
							})}
						</Card>
						{/* 近半年新增项目 结束 */}
					</Col>
				</Row>
				{/* 底部 统计 结束 */}
			</div>
			<div
				className='width-280 flex-shrink margin-left-18 overflowY-auto'
				style={{ maxHeight: `calc(100vh - 100px)` }}
			>
				{/* 待办事项 开始 */}
				<Card size='small' bodyStyle={{ padding: '0 20px' }}>
					<div className='flex align-center justify-start height-44'>
						<div className='font-size-16 font-weight-500'>
							待办事项
						</div>
						<div className='font-size-14 font-weight-400 color-86909c'>
							（{notificeTotal || '0'}）
						</div>
						<div className='flex-sub'></div>
						<div
							className='flex align-center justify-end font-size-12 color-86909c a'
							onClick={() => {
								linkTo(`/businessOppty/personalCenter`);
							}}
						>
							<div>查看全部</div>
							<RightOutlined className='font-size-12' />
						</div>
					</div>
					{[...notificeList].map((ov, oi) => {
						return (
							<div
								key={ov.id}
								className={`flex align-center justify-start padding-4 border-radius-4 bg-color-f7f8fa a ${
									oi > 0 ? 'margin-top-12' : ''
								}`}
								onClick={() => {
									linkTo(`bidmgt/personalCenter`);
								}}
							>
								<div className='width-32 height-32'>
									{ov.noticeType == '1' && (
										<img
											src={getImageSrc(
												'@/assets/images/Dashboard/icon-early-warning.png'
											)}
											className='width-32 height-32'
										/>
									)}
									{ov.noticeType == '2' && (
										<img
											src={getImageSrc(
												'@/assets/images/Dashboard/icon-urging.png'
											)}
											className='width-32 height-32'
										/>
									)}
								</div>
								<div className='font-size-12 line-height-20 flex-sub margin-left-4 padding-right-22'>
									{ov.noticeTitle || ''}
								</div>
							</div>
						);
					})}

					{!notificeList.length && (
						<div className='text-align-center line-height-40 color-aaaaaa'>
							暂无待办事项
						</div>
					)}
					<div className='padding-10'></div>
				</Card>
				{/* 待办事项 结束 */}

				{/* 追踪项目 开始 */}
				<Card
					size='small'
					bodyStyle={{ padding: '0 20px' }}
					className='margin-tb-16'
				>
					<div className='flex align-center justify-between height-44'>
						<div className='font-size-16 font-weight-500'>
							追踪项目
						</div>
						<div
							className='flex align-center justify-end font-size-12 color-86909c a'
							onClick={() => {
								linkTo('bidmgt/personalCenter');
							}}
						>
							<div>查看全部</div>
							<RightOutlined className='font-size-12' />
						</div>
					</div>
					{projectFollows.map((ov, oi) => {
						return (
							<div
								key={ov.id}
								className={`padding-8 border-radius-4 bg-color-f6f9ff a ${
									oi > 0 ? 'margin-top-12' : ''
								}`}
								onClick={() => {
									linkTo(
										`/bidmgt/projectManage/detail?id=${ov.id}`
									);
								}}
							>
								<div className='flex align-center justify-start'>
									<div className='width-4 height-12 bg-color-165dff flex-shrink border-radius-2'></div>
									<div className='font-size-14 font-weight-500 line-height-22 text-cut padding-left-4'>
										{ov.projectName || ''}
									</div>
								</div>
								<div className='flex align-center justify-start font-size-14 line-height-22 padding-left-10 margin-tb-4 color-86909c'>
									<div className='text-cut flex-shrink'>
										{ov.investmentScale || '0'}亿元
									</div>
									<div className='padding-left-12 text-cut'>
										{(ov.industryListName || []).join('/')}
									</div>
									<div className='padding-left-12 text-cut'>
										{ov.investmentResponsibleName || ''}
									</div>
								</div>
								<div className='flex align-center justify-start font-size-14 color-86909c'>
									<Progress
										percent={ov.percent}
										className='margin-0'
									/>
									{/* <div className='margin-left-4 flex-shrink padding-top-4'>
										开工
									</div> */}
								</div>
							</div>
						);
					})}
					{!projectFollows.length && (
						<div className='text-align-center line-height-40 color-aaaaaa'>
							暂无追踪项目
						</div>
					)}
					<div className='padding-10'></div>
				</Card>
				{/* 追踪项目 结束 */}

				{/* 各单位项目进度 开始 */}
				<Card size='small' bodyStyle={{ padding: '0 20px' }}>
					<div className='flex align-center justify-between height-44'>
						<div className='font-size-16 font-weight-500'>
							各单位项目进度
						</div>
						<div
							className='flex align-center justify-end font-size-12 color-86909c a'
							onClick={() => {
								linkTo('/bidmgt/projectManage/list');
							}}
						>
							<div>查看全部</div>
							<RightOutlined className='font-size-12' />
						</div>
					</div>
					{[...statisticsList].map((ov, oi) => {
						return (
							<div
								key={ov.investmentResponsibleId}
								className={`a ${oi > 0 ? 'margin-top-12' : ''}`}
								onClick={() => {
									sessionStorage.setItem(
										'/bidmgt/projectManage/list',
										JSON.stringify({
											investmentResponsibleId:
												ov.investmentResponsibleId,
										})
									);
									linkTo(`/bidmgt/projectManage/list`);
								}}
							>
								<div className='flex align-center justify-between'>
									<div className='font-size-14 font-weight-500 line-height-22 text-cut'>
										{ov.investmentResponsibleName || ''}
									</div>
									<div className='font-size-14 line-height-22 color-86909c flex-shrink'>
										{ov.projectSigningCount || '0'}/
										{ov.projectCount || '0'}
									</div>
								</div>
								<Progress
									percent={
										`${ov.projectSchedule || ''}`.replace(
											'%',
											''
										) - 0
									}
									className='margin-0'
									showInfo={false}
								/>
							</div>
						);
					})}

					{!statisticsList.length && (
						<div className='text-align-center line-height-40 color-aaaaaa'>
							暂无单位
						</div>
					)}
					<div className='padding-10'></div>
				</Card>
				{/* 各单位项目进度 结束 */}
			</div>
		</div>
	);
};

export default Index;
