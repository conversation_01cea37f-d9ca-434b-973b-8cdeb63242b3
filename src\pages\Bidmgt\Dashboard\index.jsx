import { useEffect, useState } from 'react';
import {
	Col,
	Card,
	Row,
	Progress,
	Select,
	Flex,
	Button,
	Carousel,
	Tag,
	Space,
} from 'antd';
import { getImageSrc } from '@/assets/images/index';
import { RightOutlined } from '@ant-design/icons';
import ChartColumn from '@/components/Bidmgt/ChartColumn';
import ChartDualAxes from '@/components/ChartDualAxes';
import {
	allProjectStatistics,
	projectSixMonthStatistics,
	statistics,
	statisticsUnreadNumber,
	indexStatistics,
	taskCenterPage,
} from '@/api/Bidmgt/Dashboard/index';
import { myDeptProjectIntelligencePage } from '@/api/Bidmgt/SituationManage/index';
import { notificationPage } from '@/api/Bidmgt/PersonalCenter/index';
import { pageProjectFollow } from '@/api/Bidmgt/ProjectManage/index';
import { myDeptProjectPage } from '@/api/Bidmgt/ProjectManage/index';
import { pageCategoryValue } from '@/api/Bidmgt/Dict/index';
import { useRouterLink } from '@/hook/useRouter';
import PieChartCenterText from '@/components/Bidmgt/PieChartCenterText';
import './index.scss';

const projectTypeTabObj = {
	1: {
		negotiationStatus: 1,
	},
	2: {
		reviewMeetingStatus: 1,
	},
	3: {
		signStatus: 1,
	},
	4: {
		keyProjectStatus: 1,
		openingStatus: 1,
	},
	5: {
		keyProjectStatus: 1,
		openingStatus: 0,
	},
};
const Index = () => {
	const { linkTo } = useRouterLink();

	const [statisticsData, setStatisticsData] = useState({});
	const [projectFollows, setProjectFollows] = useState([]);

	const [statisticsArea, setStatisticsArea] = useState([]);
	const [statisticsList, setStatisticsList] = useState([]);

	const [todoList, setTodoList] = useState([]);

	const [projectList, setProjectList] = useState([]);

	// 项目分布
	const [projectTypeTabIndex, setProjectTypeTabIndex] = useState('1');
	const projectTypeTabChange = (index) => {
		if (projectTypeTabIndex == index) {
			setProjectTypeTabIndex('');
		} else {
			setProjectTypeTabIndex(index);
		}
	};
	const [projectTypeId, setProjectTypeId] = useState('1');
	const [pieData, setPieData] = useState({
		pie1: [],
		pie2: [],
		pie1Count: '',
		pie2Count: '',
	});
	const projectTypeList = [
		{
			value: '1',
			label: '属地',
		},
		{
			value: '2',
			label: '产业',
		},
	];
	const pieColor = [
		'00b42a',
		'206ccf',
		'ff7d00',
		'd25f00',
		'57a9fb',
		'8d4eda',
		'f76560',
		'08a4ff',
		'165dff',
	];
	// 饼状图 产业 属地
	const getPieData = () => {
		let params = {
			type: '',
		};
		const obj = projectTypeTabObj[projectTypeTabIndex] || {};
		params = {
			...params,
			...obj,
		};
		indexStatistics(params).then((res) => {
			const pie1Count =
				projectTypeId == 1
					? res.data.investmentResponsibleProjectCount
					: res.data.industrialTrackProjectCount;

			const pie2Count =
				projectTypeId == 1
					? res.data.investmentResponsibleInvestmentScale
					: res.data.industrialTrackInvestmentScale;

			let projectStatisticsList = res.data.projectStatisticsList.sort(
				(a, b) => b.projectCount - a.projectCount
			);
			if (projectTypeId == 1) {
				projectStatisticsList =
					res.data.investmentResponsibleProjectStatisticsList.sort(
						(a, b) => b.projectCount - a.projectCount
					);
			}
			projectStatisticsList = projectStatisticsList.map((ov) => {
				return {
					...ov,
					name:
						ov.investmentResponsibleName || ov.industrialTrackName,
				};
			});
			const projectStatisticsElse = (
				projectStatisticsList.length > 8
					? projectStatisticsList.slice(0, 8)
					: []
			).reduce((pre, cur) => {
				return {
					name: '其他',
					investmentScale:
						(cur.investmentScale - 0 || 0) +
						(pre.investmentScale - 0 || 0),
					projectCount:
						(cur.projectCount - 0 || 0) +
						(pre.projectCount - 0 || 0),
					proportion:
						(`${cur.proportion || ''}`.replace('%', '') - 0 || 0) +
						(`${pre.proportion || ''}`.replace('%', '') - 0 || 0),
				};
			}, {});
			if (projectStatisticsList.length > 8) {
				projectStatisticsElse.proportion = `${(
					100 - projectStatisticsElse.proportion
				).toFixed(2)}%`;
				projectStatisticsElse.projectCount =
					pie1Count - projectStatisticsElse.projectCount;
				projectStatisticsElse.investmentScale =
					pie2Count - projectStatisticsElse.investmentScale;
			}

			const pie1 = (
				projectStatisticsList.length > 8
					? [
							...projectStatisticsList.slice(0, 8),
							projectStatisticsElse,
					  ]
					: [...projectStatisticsList.slice(0, 8)]
			).map((ov, oi) => {
				return {
					...ov,
					color: pieColor[oi],
					value: (ov.projectCount || 0) - 0 || 0,
					name: ov.name || '',
				};
			});

			//  ==================== 分割线 =======================================
			let investmentScaleStatisticsList =
				res.data.investmentScaleStatisticsList.sort(
					(a, b) => b.investmentScale - a.investmentScale
				);

			if (projectTypeId == 1) {
				investmentScaleStatisticsList =
					res.data.investmentResponsibleInvestmentScaleStatisticsList.sort(
						(a, b) => b.investmentScale - a.investmentScale
					);
			}
			investmentScaleStatisticsList = investmentScaleStatisticsList.map(
				(ov) => {
					return {
						...ov,
						name:
							ov.investmentResponsibleName ||
							ov.industrialTrackName,
					};
				}
			);

			const investmentScaleStatisticsElse = (
				investmentScaleStatisticsList.length > 8
					? investmentScaleStatisticsList.slice(0, 8)
					: []
			).reduce((pre, cur) => {
				return {
					name: '其他',
					investmentScale:
						(cur.investmentScale - 0 || 0) +
						(pre.investmentScale - 0 || 0),
					projectCount:
						(cur.projectCount - 0 || 0) +
						(pre.projectCount - 0 || 0),
					proportion:
						(`${cur.proportion || ''}`.replace('%', '') - 0 || 0) +
						(`${pre.proportion || ''}`.replace('%', '') - 0 || 0),
				};
			}, {});
			if (investmentScaleStatisticsList.length > 8) {
				investmentScaleStatisticsElse.proportion = `${(
					100 - investmentScaleStatisticsElse.proportion
				).toFixed(2)}%`;
				investmentScaleStatisticsElse.projectCount =
					pie1Count - investmentScaleStatisticsElse.projectCount;
				investmentScaleStatisticsElse.investmentScale =
					pie2Count - investmentScaleStatisticsElse.investmentScale;
			}

			const pie2 = (
				investmentScaleStatisticsList.length > 8
					? [
							...investmentScaleStatisticsList.slice(0, 8),
							investmentScaleStatisticsElse,
					  ]
					: [...investmentScaleStatisticsList.slice(0, 8)]
			).map((ov, oi) => {
				return {
					...ov,
					color: pieColor[oi],
					value: (ov.investmentScale || 0).toFixed(2) - 0 || 0,
					name: ov.name || '',
				};
			});
			setPieData({
				pie1,
				pie2,
				pie1Count,
				pie2Count,
			});
		});
	};
	useEffect(() => {
		getPieData();
	}, [projectTypeId, projectTypeTabIndex]);

	const [situationList, setSituationList] = useState([]);
	const getSituationList = () => {
		myDeptProjectIntelligencePage({
			pageNum: 1,
			pageSize: 3,
			auditStatus: 2,
		}).then((res) => {
			(res.data.records || []).forEach((ov) => {
				ov.evaluationScore = (ov.evaluationScore || '')
					.replace(/（.*）/gi, '')
					.replaceAll(' ', '');
				ov.industryChain = `${ov.industryChain || ''}`
					.split('、')[0]
					.split('-')[0];
				ov.enterpriseNature = `${ov.enterpriseNature || ''}`
					.split('、')[0]
					.split('-')[0];
			});
			setSituationList(res.data.records || []);
		});
	};
	const getDAta = () => {
		indexStatistics().then((res) => {
			setStatisticsData(res.data || {});
		});
		statistics().then((res) => {
			setStatisticsList(
				(res.data || [])
					.filter((ov) => ov.investmentResponsibleId)
					.sort((a, b) => {
						if (sortDeptId == 1) {
							// 按进度
							return (
								b.projectSchedule.replace('%', '') -
								a.projectSchedule.replace('%', '')
							);
						} else {
							// 按项目数
							return b.projectCount - a.projectCount;
						}
					})
			);

			const list = (res.data || [])
				.filter((ov) => ov.investmentResponsibleId)
				.reduce((pre, cur) => {
					return [
						...pre,
						{
							xField: cur.investmentResponsibleName || '',
							count: cur.projectCount || 0,
							name: '各责任单位项目数量',
						},
						{
							xField: cur.investmentResponsibleName || '',
							value: cur.investmentScale || 0,
							name: '各责任单位项目规模',
						},
					];
				}, []);
			setStatisticsArea(list);
		});
		getSituationList();
		taskCenterPage({
			pageNum: 1,
			pageSize: 4,
			operateStatus: 0,
			toDoListType: 6,
		}).then((res) => {
			setTodoList(res.data.records || []);
		});
		pageCategoryValue({
			categoryCode: 'project_stage',
			pageNum: 1,
			pageSize: 300,
		}).then((reb) => {
			const stepsList = reb.data.records || [];
			pageProjectFollow({
				pageNum: 1,
				pageSize: 3,
			}).then((res) => {
				setProjectFollows(
					(res.data.records || []).map((ov) => {
						const findex = stepsList.findIndex(
							(item) => ov.projectStageId == item.id
						);
						if (findex >= 0) {
							ov.percent = parseInt(
								(findex * 100) / stepsList.length
							);
						} else {
							ov.percent = 0;
						}
						return { ...ov };
					})
				);
			});
		});
		myDeptProjectPage({
			auditStatus: 2,
			pageNum: 1,
			pageSize: 10,
		}).then((res) => {
			setProjectList(res.data.records || []);
		});
	};
	useEffect(() => {
		getDAta();
	}, []);

	const [sortDeptId, setSortDeptId] = useState('1');
	const sortDeptChange = () => {
		if (!statisticsList.length) {
			return;
		}
		const statisticsDeptList = [...statisticsList].sort((a, b) => {
			if (sortDeptId == 1) {
				// 按进度
				return (
					b.projectSchedule.replace('%', '') -
					a.projectSchedule.replace('%', '')
				);
			} else {
				// 按项目数
				return b.projectCount - a.projectCount;
			}
		});
		setStatisticsList(statisticsDeptList);
	};
	useEffect(() => {
		sortDeptChange();
	}, [sortDeptId]);
	return (
		<div className='flex-sub flex padding-20 border-box'>
			<div className='flex-sub'>
				{/* 顶部 统计 开始 */}
				<Row
					gutter={[18, 16]}
					className='color-1d2129 margin-bottom-16'
				>
					<Col xxl={6} xl={12} lg={12} md={12} sm={24}>
						<Card
							size='small'
							bodyStyle={{ padding: '0 16px 12px' }}
							title={
								<div className='a flex align-center justify-start height-44'>
									<div className='font-size-16 font-weight-500'>
										全部项目总数
									</div>
									<div className='font-size-14 font-weight-400 color-86909c'>
										（个）
									</div>
								</div>
							}
							onClick={() => {
								linkTo(`/bidmgt/projectManage/list`);
							}}
						>
							<div className='a flex align-center justify-between height-114'>
								<div className='font-size-32 line-height-40 font-bold flex-sub'>
									{statisticsData.projectCount || '0'}
								</div>
								<div className='width-54 height-54'>
									<img
										src={getImageSrc(
											'@/assets/images/Dashboard/icon-project-totals.png'
										)}
										className='width-54 height-54'
									/>
								</div>
							</div>
						</Card>
					</Col>
					<Col xxl={6} xl={12} lg={12} md={12} sm={24}>
						<Card
							size='small'
							bodyStyle={{ padding: '0 16px 12px' }}
							title={
								<div className='a flex align-center justify-start height-44'>
									<div className='font-size-16 font-weight-500'>
										全部投资金额
									</div>
									<div className='font-size-14 font-weight-400 color-86909c'>
										（亿元）
									</div>
								</div>
							}
							onClick={() => {
								linkTo(`/bidmgt/projectManage/list`);
							}}
						>
							<div className='a flex align-center justify-between height-114'>
								<div className='font-size-32 line-height-40 font-bold flex-sub'>
									{statisticsData.investmentScale || '0'}
								</div>
								<div className='width-54 height-54'>
									<img
										src={getImageSrc(
											'@/assets/images/Dashboard/icon-investment.png'
										)}
										className='width-54 height-54'
									/>
								</div>
							</div>
						</Card>
					</Col>
					<Col xxl={12} xl={24} lg={24} md={24} sm={24}>
						{/* 双招双引签约项目 开始 */}
						<Card
							size='small'
							bodyStyle={{ padding: '0 16px 12px' }}
						>
							<div className='flex align-center justify-start height-44'>
								<div className='font-size-16 font-weight-500'>
									双招双引签约项目
								</div>
								<div className='font-size-14 font-weight-400 color-86909c'>
									（单位：个、亿元）
								</div>
							</div>
							<Row gutter={16} className='min-height-110'>
								{statisticsData.projectActivityStatisticsList &&
									statisticsData.projectActivityStatisticsList.map(
										(ov) => {
											return (
												<Col
													flex={1}
													key={ov.activityId}
												>
													<div
														className='border-solid-f2f3f5 border-radius-8 padding-10 border-box a'
														onClick={() => {
															linkTo(
																`/bidmgt/projectManage/list?activityIds=${ov.activityId}`
															);
														}}
													>
														<div className='font-bold font-size-16 line-height-24'>
															{ov.activityName ||
																''}
														</div>
														<div className='flex align-center justify-start margin-tb-8'>
															<div className='font-size-18 color-ff7d00 font-bold'>
																¥
																{ov.investmentScale ||
																	'0'}
															</div>
															<div className='color-86909c font-size-14 margin-left-4'>
																亿
															</div>
														</div>
														<div className='flex align-center justify-start font-size-16 line-height-24'>
															<div className='color-86909c'>
																项目数：
															</div>
															<div className='font-bold'>
																{ov.projectCount ||
																	'0'}
															</div>
														</div>
													</div>
												</Col>
											);
										}
									)}
							</Row>
						</Card>
						{/* 双招双引签约项目 结束 */}
					</Col>
				</Row>
				{/* 顶部 统计 结束 */}

				{/* 关键节点项目统计 开始 */}
				<Card
					size='small'
					bodyStyle={{ padding: '12px 20px' }}
					className='margin-top-20'
					title={
						<div className='flex align-center justify-start height-44'>
							<div className='font-size-16 font-weight-500'>
								关键节点项目统计
							</div>
							<div className='font-size-14 font-weight-400 color-86909c'>
								（单位：个、亿元）
							</div>
						</div>
					}
				>
					<Row gutter={[16, 16]}>
						<Col xxl={4} xl={8} lg={8} md={12} sm={24}>
							<div
								className='border-solid-f2f3f5 border-radius-8 padding-10 border-box a'
								onClick={() => {
									sessionStorage.setItem(
										'formDataProject',
										JSON.stringify({
											projectStatus: [
												'negotiationStatus',
											],
										})
									);
									sessionStorage.setItem(
										'checkedListProject',
										JSON.stringify([
											{
												id: 'projectStatus',
												label: '洽谈中',
												name: '项目状态',
												value: 'negotiationStatus',
											},
										])
									);
									linkTo(
										`/bidmgt/projectManage/list?tabIndex=1`
									);
								}}
							>
								<div className='font-bold font-size-16 line-height-24'>
									洽谈中
								</div>
								<div className='flex align-center justify-start margin-tb-8'>
									<div className='font-size-18 color-ff7d00 font-bold'>
										¥
										{statisticsData.negotiationInvestmentScale ||
											'0'}
									</div>
									<div className='color-86909c font-size-14 margin-left-4'>
										亿
									</div>
								</div>
								<div className='flex align-center justify-start font-size-16 line-height-24'>
									<div className='color-86909c'>项目数：</div>
									<div className='font-bold'>
										{statisticsData.negotiationCount || '0'}
									</div>
								</div>
							</div>
						</Col>
						<Col xxl={4} xl={8} lg={8} md={12} sm={24}>
							<div
								className='border-solid-f2f3f5 border-radius-8 padding-10 border-box a'
								onClick={() => {
									sessionStorage.setItem(
										'formDataProject',
										JSON.stringify({
											projectStatus: [
												'reviewMeetingStatus',
											],
										})
									);
									sessionStorage.setItem(
										'checkedListProject',
										JSON.stringify([
											{
												id: 'projectStatus',
												label: '已过会',
												name: '项目状态',
												value: 'reviewMeetingStatus',
											},
										])
									);
									linkTo(
										`/bidmgt/projectManage/list?tabIndex=2`
									);
								}}
							>
								<div className='font-bold font-size-16 line-height-24'>
									已过会
								</div>
								<div className='flex align-center justify-start margin-tb-8'>
									<div className='font-size-18 color-ff7d00 font-bold'>
										¥
										{statisticsData.promoteInvestmentScale ||
											'0'}
									</div>
									<div className='color-86909c font-size-14 margin-left-4'>
										亿
									</div>
								</div>
								<div className='flex align-center justify-start font-size-16 line-height-24'>
									<div className='color-86909c'>项目数：</div>
									<div className='font-bold'>
										{statisticsData.promoteCount || '0'}
									</div>
								</div>
							</div>
						</Col>
						<Col xxl={4} xl={8} lg={8} md={12} sm={24}>
							<div
								className='border-solid-f2f3f5 border-radius-8 padding-10 border-box a'
								onClick={() => {
									sessionStorage.setItem(
										'formDataProject',
										JSON.stringify({
											projectStatus: ['signStatus'],
										})
									);
									sessionStorage.setItem(
										'checkedListProject',
										JSON.stringify([
											{
												id: 'signStatus',
												label: '已签约',
												name: '项目状态',
												value: 'reviewMeetingStatus',
											},
										])
									);
									linkTo(
										`/bidmgt/projectManage/list?tabIndex=3`
									);
								}}
							>
								<div className='font-bold font-size-16 line-height-24'>
									已签约
								</div>
								<div className='flex align-center justify-start margin-tb-8'>
									<div className='font-size-18 color-ff7d00 font-bold'>
										¥
										{statisticsData.signingInvestmentScale ||
											'0'}
									</div>
									<div className='color-86909c font-size-14 margin-left-4'>
										亿
									</div>
								</div>
								<div className='flex align-center justify-start font-size-16 line-height-24'>
									<div className='color-86909c'>项目数：</div>
									<div className='font-bold'>
										{statisticsData.signingCount || '0'}
									</div>
								</div>
							</div>
						</Col>
						<Col xxl={6} xl={12} lg={12} md={12} sm={24}>
							<div
								className='border-solid-f2f3f5 border-radius-8 padding-10 border-box a'
								onClick={() => {
									sessionStorage.setItem(
										'formDataProject',
										JSON.stringify({
											openingStatusList: [0],
										})
									);
									sessionStorage.setItem(
										'checkedListProject',
										JSON.stringify([
											{
												id: 'openingStatusList',
												label: '重点未开工项目',
												name: '重点项目',
												value: 0,
											},
										])
									);
									linkTo(
										`/bidmgt/projectManage/list?tabIndex=4`
									);
								}}
							>
								<div className='font-bold font-size-16 line-height-24'>
									重点未开工项目
								</div>
								<div className='flex align-center justify-start margin-tb-8'>
									<div className='font-size-18 color-ff7d00 font-bold'>
										¥
										{statisticsData.notOpenInvestmentScale ||
											'0'}
									</div>
									<div className='color-86909c font-size-14 margin-left-4'>
										亿
									</div>
								</div>
								<div className='flex align-center justify-start font-size-16 line-height-24'>
									<div className='color-86909c'>项目数：</div>
									<div className='font-bold'>
										{statisticsData.notOpenCount || '0'}
									</div>
								</div>
							</div>
						</Col>
						<Col xxl={6} xl={12} lg={12} md={12} sm={24}>
							<div
								className='border-solid-f2f3f5 border-radius-8 padding-10 border-box a'
								onClick={() => {
									sessionStorage.setItem(
										'formDataProject',
										JSON.stringify({
											openingStatusList: [1],
										})
									);
									sessionStorage.setItem(
										'checkedListProject',
										JSON.stringify([
											{
												id: 'openingStatusList',
												label: '重点已开工项目',
												name: '重点项目',
												value: 1,
											},
										])
									);
									linkTo(
										`/bidmgt/projectManage/list?tabIndex=5`
									);
								}}
							>
								<div className='font-bold font-size-16 line-height-24'>
									重点已开工项目
								</div>
								<div className='flex align-center justify-start margin-tb-8'>
									<div className='font-size-18 color-ff7d00 font-bold'>
										¥
										{statisticsData.openInvestmentScale ||
											'0'}
									</div>
									<div className='color-86909c font-size-14 margin-left-4'>
										亿
									</div>
								</div>
								<div className='flex align-center justify-start font-size-16 line-height-24'>
									<div className='color-86909c'>项目数：</div>
									<div className='font-bold'>
										{statisticsData.openCount || '0'}
									</div>
								</div>
							</div>
						</Col>
					</Row>
				</Card>
				{/* 关键节点项目统计 结束 */}

				{/* 项目分布 开始 */}
				<Card
					size='small'
					bodyStyle={{ padding: '12px 20px' }}
					className='margin-top-20'
					title={
						<div className='flex justify-between align-center'>
							<div className='flex align-center justify-start height-44'>
								<div className='font-size-16 font-weight-500'>
									项目分布
								</div>
								<div className='font-size-14 font-weight-400 color-86909c'>
									（单位：个、亿元）
								</div>
							</div>
							<Select
								defaultValue='1'
								style={{
									width: 80,
								}}
								options={projectTypeList}
								onChange={(val) => {
									setProjectTypeId(val);
								}}
							/>
						</div>
					}
				>
					<Flex gap='small' wrap>
						<Button
							type={
								projectTypeTabIndex == 1 ? 'primary' : 'default'
							}
							onClick={() => projectTypeTabChange(1)}
						>
							洽谈
						</Button>
						<Button
							type={
								projectTypeTabIndex == 2 ? 'primary' : 'default'
							}
							onClick={() => projectTypeTabChange(2)}
						>
							过会
						</Button>
						<Button
							type={
								projectTypeTabIndex == 3 ? 'primary' : 'default'
							}
							onClick={() => projectTypeTabChange(3)}
						>
							签约
						</Button>
						<Button
							type={
								projectTypeTabIndex == 4 ? 'primary' : 'default'
							}
							onClick={() => projectTypeTabChange(4)}
						>
							已开工
						</Button>
						<Button
							type={
								projectTypeTabIndex == 5 ? 'primary' : 'default'
							}
							onClick={() => projectTypeTabChange(5)}
						>
							未开工
						</Button>
					</Flex>

					<Row gutter={[16, 16]} className='padding-tb-10'>
						<Col xxl={12} xl={12} lg={24} md={24} sm={24}>
							<div className='flex-sub flex align-center justify-center'>
								<div className='width-180 height-180 border-box flex-shrink position-relative'>
									<PieChartCenterText
										id='pie-container-1'
										data={pieData.pie1}
									/>
									<div
										className='a position-absolute top-0 left-0 width-180 height-180 flex align-center justify-center'
										onClick={() => {
											linkTo(
												`/bidmgt/projectManage/list?tabIndex=${projectTypeTabIndex}`
											);
										}}
									>
										<div className='border-box text-align-center'>
											<div className='font-size-24 font-bold color-1d2129 line-height-30'>
												{pieData.pie1Count || '0'}
											</div>
											<div className='font-size-16 color-86909c line-height-20'>
												项目数
											</div>
										</div>
									</div>
								</div>
								<div className='margin-left-10'>
									{pieData.pie1.map((ov, oi) => {
										return (
											<div
												className={`flex align-center justify-start font-size-14 line-height-22 ${
													oi > 0 ? 'margin-top-4' : ''
												}`}
												key={ov.name}
											>
												<div
													className={`width-8 height-8 border-radius-4 margin-right-6 bg-color-${ov.color}`}
												></div>
												<div className='text-cut'>
													{ov.name}
												</div>
												<div className='flex-sub flex-shrink'>
													：{ov.value}
												</div>
											</div>
										);
									})}
								</div>
							</div>
						</Col>
						<Col
							xxl={12}
							xl={12}
							lg={24}
							md={24}
							sm={24}
							className='border-solid-left-e5e6eb'
						>
							<div className='flex-sub flex align-center justify-center'>
								<div className='width-180 height-180 border-box flex-shrink position-relative'>
									<PieChartCenterText
										id='pie-container-2'
										data={pieData.pie2}
									/>
									<div
										className='a position-absolute top-0 left-0 width-180 height-180 flex align-center justify-center'
										onClick={() => {
											linkTo(
												`/bidmgt/projectManage/list?tabIndex=${projectTypeTabIndex}`
											);
										}}
									>
										<div className='border-box text-align-center'>
											<div className='font-size-24 font-bold color-1d2129 line-height-30'>
												{pieData.pie2Count || '0'}
											</div>
											<div className='font-size-16 color-86909c line-height-20'>
												金额(亿)
											</div>
										</div>
									</div>
								</div>
								<div className='margin-left-10'>
									{pieData.pie2.map((ov, oi) => {
										return (
											<div
												className={`flex align-center justify-start font-size-14 line-height-22 ${
													oi > 0 ? 'margin-top-4' : ''
												}`}
												key={ov.name}
											>
												<div
													className={`width-8 height-8 border-radius-4 margin-right-6 bg-color-${ov.color}`}
												></div>
												<div className='text-cut'>
													{ov.name}
												</div>
												<div className='flex-sub flex-shrink'>
													：{ov.value}
												</div>
											</div>
										);
									})}
								</div>
							</div>
						</Col>
					</Row>
				</Card>
				{/* 项目分布 结束 */}

				{/* 各责任单位项目数量/规模 开始 */}
				<Card
					size='small'
					bodyStyle={{ padding: '0 20px' }}
					className='margin-top-20'
				>
					<div className='flex align-center justify-start height-44 position-absolute top-0 left-20 z-index-10'>
						<div className='font-size-16 font-weight-500'>
							各责任单位项目数量/规模
						</div>
					</div>
					<ChartDualAxes
						className='height-360 padding-tb-10'
						config={{
							data: [
								statisticsArea.filter((ov) =>
									ov.hasOwnProperty('count')
								),
								statisticsArea.filter((ov) =>
									ov.hasOwnProperty('value')
								),
							],
							xField: 'xField',
							yField: ['count', 'value'],
							meta: {
								count: {
									alias: '项目数量',
									formatter: (val) => `${val} 个`,
								},
								value: {
									alias: '项目规模',
									formatter: (val) => `${val} 亿`,
								},
							},
							geometryOptions: [
								{
									geometry: 'column',
									smooth: true,
									lineStyle: {
										lineWidth: 2,
									},
									point: {},
									minColumnWidth: 20,
									maxColumnWidth: 20,
								},
								{
									geometry: 'line',
									smooth: true,
									isPercent: true,
									lineStyle: {
										lineWidth: 2,
									},
									point: {},
								},
								// {
								// 	geometry: 'line',
								// 	smooth: true,
								// 	isPercent: true,
								// 	lineStyle: {
								// 		lineWidth: 2,
								// 	},
								// 	point: {},
								// },
							],
							yAxis: {
								count: {
									// min: 0,
									label: {},
									grid: {
										line: {
											style: {
												lineDash: [4, 5],
											},
										},
									},
								},
								value: {
									// min: 0,
									label: {},
								},
							},
							legend: {
								layout: 'horizontal',
								position: 'top-right',
							},
							title: '各责任单位项目数量/规模',
						}}
					/>
				</Card>
				{/* 各责任单位项目数量/规模 结束 */}

				{/* 底部 统计 开始 */}
				<Row gutter={18} className='color-1d2129  margin-top-20'>
					<Col span={24}>
						{/* 近半年新增项目 开始 */}
						<Card size='small' bodyStyle={{ padding: '0 20px' }}>
							<div className='flex align-center justify-between height-44'>
								<div className='font-size-16 font-weight-500'>
									最新项目
								</div>
								<div
									className='flex align-center justify-end font-size-12 color-86909c a'
									onClick={() => {
										linkTo('/bidmgt/projectManage/list');
									}}
								>
									<div>查看全部</div>
									<RightOutlined className='font-size-12' />
								</div>
							</div>
							{!!projectList.length &&
								projectList.slice(0, 10).map((ov, oi) => {
									return (
										<div
											key={ov.id}
											className={`padding-8 border-radius-4 a ${
												oi > 0 ? 'margin-top-12' : ''
											}`}
											style={{
												background:
													'linear-gradient(180deg, #EBF5FF 0%, #FFFFFF 100%)',
											}}
											onClick={() => {
												linkTo(
													`/bidmgt/projectManage/detail?id=${ov.id}`
												);
											}}
										>
											<div className='flex align-center justify-start'>
												<div className='width-6 height-6 border-radius-4 bg-color-165dff flex-shrink'></div>
												<div className='font-size-14 font-weight-500 line-height-22 text-cut padding-left-4'>
													{ov.projectName || ''}
												</div>
											</div>
											<div className='flex align-center justify-start font-size-12 line-height-20 padding-left-10 margin-tb-4 color-86909c'>
												项目责任部门：
												{ov.investmentResponsibleName ||
													''}
											</div>
											<div className='flex align-center justify-start font-size-12 line-height-20 padding-left-10 color-86909c text-cut'>
												创建时间：
												{ov.createTime || ''}
											</div>
										</div>
									);
								})}
							{!projectList.length && (
								<div className='text-align-center line-height-70 color-aaaaaa'>
									~暂无最新项目数据~
								</div>
							)}
						</Card>
						{/* 近半年新增项目 结束 */}
					</Col>
				</Row>
				{/* 底部 统计 结束 */}
			</div>
			<div className='width-280 flex-shrink margin-left-18'>
				{/* 待办事项 开始 */}
				<Card
					size='small'
					bodyStyle={{ padding: '0 12px 16px' }}
					className='margin-bottom-16'
				>
					<div className='flex align-center justify-bewteen height-44'>
						<div className='font-size-16 font-weight-500 flex-sub'>
							待办事项
						</div>
						<div
							className='flex align-center justify-end font-size-12 color-86909c a font-weight-400 '
							onClick={() => {
								linkTo(`/bidmgt/taskCenter/list?tabIndex=`);
							}}
						>
							<div>查看全部</div>
							<RightOutlined className='font-size-12' />
						</div>
					</div>
					<div className='height-70 bg-color-wait-todo bidmgt-dotsClass border-radius-4'>
						{!!todoList.length && (
							<Carousel
								dotPosition='right'
								infinite={true}
								autoplay={!true}
							>
								{todoList.map((ov, oi) => {
									return (
										<div
											className='padding-top-10 a boxder-box'
											key={oi}
											onClick={() => {
												linkTo(
													`/bidmgt/taskCenter/detail?id=${ov.businessId}&toDoListType=${ov.toDoListType}&todoId=${ov.id}`
												);
											}}
										>
											<div className='flex align-center justify-start line-height-22 font-size-14 padding-lr-10'>
												<div
													className={`width-8 height-8 border-radius-4 flex-shrink ${
														ov.readStatus == 0 &&
														'bg-color-f76560'
													}`}
												></div>
												<div className='flex-shrink color-ff7d00'>
													{/* 待办事项类型：1 项目认领 2 项目审核 3 商情审核 4 商情认领  */}
													【
													{ov.toDoListType == 1
														? '待认领'
														: ''}
													{ov.toDoListType == 2
														? '待审核'
														: ''}
													{ov.toDoListType == 4
														? '待认领'
														: ''}
													{ov.toDoListType == 3
														? '待审核'
														: ''}
													】
												</div>
												<div className='flex-sub text-cut'>
													{ov.toDoListContentDesc ||
														''}
												</div>
											</div>
											<div className='flex justify-start align-center color-86909c margin-top-4 padding-lr-10 min-height-22'>
												{ov.createDeptName && (
													<div className='margin-right-20'>
														{ov.createDeptName}
													</div>
												)}
												<div>
													{`${
														ov.createTime || ''
													}`.slice(0, 16)}
												</div>
											</div>
										</div>
									);
								})}
							</Carousel>
						)}

						{!todoList.length && (
							<div className='text-align-center line-height-70 color-aaaaaa'>
								~暂无待办事项~
							</div>
						)}
					</div>
				</Card>
				{/* 待办事项 结束 */}

				{/* 商情大数据推送 开始 */}
				<Card
					size='small'
					bodyStyle={{ padding: '0 20px 16px' }}
					className='margin-bottom-16'
				>
					<div className='flex align-center justify-between height-44'>
						<div className='font-size-16 font-weight-500'>
							商情大数据推送
						</div>
						<div
							className='flex align-center justify-end font-size-12 color-86909c a'
							onClick={() => {
								linkTo('/bidmgt/situationManage/list');
							}}
						>
							<div>查看全部</div>
							<RightOutlined className='font-size-12' />
						</div>
					</div>
					{situationList.slice(0, 3).map((ov, oi) => {
						return (
							<div
								key={oi}
								className={`padding-8 border-radius-4 bg-color-f7f8fa a ${
									oi > 0 ? 'margin-top-10' : ''
								}`}
								onClick={() => {
									linkTo(
										`/bidmgt/situationManage/detail?id=${ov.id}`
									);
								}}
							>
								<div className='flex align-center justify-start'>
									<div className='font-size-14 font-weight-500 line-height-22 text-cut'>
										{ov.enterpriseName || ''}
									</div>
								</div>
								<div className='margin-bottom-6'>
									{(ov.industryChain ||
										ov.enterpriseNature) && (
										<Space wrap>
											{ov.industryChain && (
												<Tag
													bordered={false}
													color='processing'
												>
													{ov.industryChain}
												</Tag>
											)}
											{ov.enterpriseNature && (
												<Tag
													bordered={false}
													color='processing'
												>
													{ov.enterpriseNature}
												</Tag>
											)}
										</Space>
									)}
								</div>
								{(ov.investmentProbability ||
									ov.evaluationScore) && (
									<div className='margin-bottom-6'>
										<Space wrap>
											{ov.investmentProbability && (
												<Tag
													bordered={false}
													color='#FF7D00'
												>
													新增投资概率
													{ov.investmentProbability}
												</Tag>
											)}
											{ov.evaluationScore && (
												<Tag
													bordered={false}
													color='red'
												>
													{ov.evaluationScore}
												</Tag>
											)}
										</Space>
									</div>
								)}
								<div className='flex align-center justify-start font-size-12 line-height-20 color-86909c text-cut'>
									{ov.recommendTime || ''}
								</div>
							</div>
						);
					})}
					{!situationList.length && (
						<div className='text-align-center line-height-40 color-aaaaaa'>
							~暂无数据推送~
						</div>
					)}
					<div className='padding-8'></div>
				</Card>
				{/* 商情大数据推送 结束 */}

				{/* 我的项目动态 开始 */}
				<Card
					size='small'
					bodyStyle={{ padding: '0 20px 16px' }}
					className='margin-bottom-16'
				>
					<div className='flex align-center justify-between height-44'>
						<div className='font-size-16 font-weight-500'>
							跟踪项目动态
						</div>
						<div
							className='flex align-center justify-end font-size-12 color-86909c a'
							onClick={() => {
								linkTo(
									`/bidmgt/personalCenter/info?tabIndex=2`
								);
							}}
						>
							<div>查看全部</div>
							<RightOutlined className='font-size-12' />
						</div>
					</div>
					{projectFollows.slice(0, 3).map((ov, oi) => {
						return (
							<div key={oi}>
								{oi > 0 ? (
									<div
										className={`height-1 bg-color-f2f3f5 ${
											oi > 0 ? 'margin-tb-8' : ''
										}`}
									></div>
								) : null}
								<div
									key={ov.id}
									className={`border-radius-4 font-size-14 line-height-22 a`}
									onClick={() => {
										linkTo(
											`/bidmgt/projectManage/detail?id=${ov.id}`
										);
									}}
								>
									<div className='flex align-center justify-start'>
										<div className='width-8 height-8 bg-color-f76560 flex-shrink border-radius-4'></div>
										<div className='font-size-14 font-weight-500 line-height-22 text-cut padding-left-4'>
											{ov.projectName || ''}
										</div>
									</div>
									<div className='padding-left-10 margin-tb-4 text-cut-2'>
										动态内容：{ov.latestDevelopments || ''}
									</div>
									<div className='flex align-center justify-start padding-left-10 color-86909c text-cut'>
										更新时间：{ov.updatedTime || ''}
									</div>
								</div>
							</div>
						);
					})}
					{!projectFollows.length && (
						<div className='text-align-center line-height-40 color-aaaaaa'>
							~暂无项目动态~
						</div>
					)}
				</Card>
				{/* 我的项目动态 结束 */}

				{/* 各单位项目进度 开始 */}
				<Card size='small' bodyStyle={{ padding: '12px 20px 16px' }}>
					<div className='flex align-center justify-between height-44'>
						<div className='font-size-16 font-weight-500'>
							各单位项目进度
						</div>

						<Select
							defaultValue={sortDeptId}
							size='middle'
							style={{
								width: 116,
							}}
							options={[
								{
									value: '1',
									label: '按进度排名',
								},
								{
									value: '2',
									label: '按数量排名',
								},
							]}
							onChange={(val) => {
								setSortDeptId(val);
							}}
						/>
					</div>
					{[...statisticsList].map((ov, oi) => {
						return (
							<div
								key={ov.investmentResponsibleId}
								className={`a ${oi > 0 ? 'margin-top-12' : ''}`}
								onClick={() => {
									sessionStorage.setItem(
										'/bidmgt/projectManage/list',
										JSON.stringify({
											investmentResponsibleId:
												ov.investmentResponsibleId,
										})
									);
									// linkTo(`/bidmgt/projectManage/list`);
								}}
							>
								<div className='flex align-center justify-between'>
									<div className='font-size-14 font-weight-500 line-height-22 text-cut'>
										{ov.investmentResponsibleName || ''}
									</div>
									<div className='font-size-14 line-height-22 color-86909c flex-shrink'>
										{ov.projectSigningCount || '0'}/
										{ov.projectCount || '0'}
									</div>
								</div>
								<Progress
									percent={
										`${ov.projectSchedule || ''}`.replace(
											'%',
											''
										) - 0
									}
									className='margin-0'
									showInfo={false}
								/>
							</div>
						);
					})}

					{!statisticsList.length && (
						<div className='text-align-center line-height-40 color-aaaaaa'>
							暂无单位
						</div>
					)}
				</Card>
				{/* 各单位项目进度 结束 */}
			</div>
		</div>
	);
};

export default Index;
