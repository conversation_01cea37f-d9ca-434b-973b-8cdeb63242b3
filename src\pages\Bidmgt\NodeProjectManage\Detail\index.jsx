/**
 * @description index.jsx - 督办项目详情
 * <AUTHOR>
 *
 * Created on 2024/10/14 11:16
 */
import React, {forwardRef, useEffect, useImperativeHandle, useRef, useState} from 'react';
import {Form, Modal, Descriptions, Typography, Steps, Card, Button, Empty} from "antd";
import {detailConfig, projectType} from "../const";
import './styles.scss';
import NodeModel from "../Node";
import {detailProject, pageParamList} from "@/api/Bidmgt/important/groupEvent";
import {useRouterLink} from "@/hook/useRouter";
import {flattenTree} from "@/utils/common";

const {Title} = Typography;
const Detail = (props, ref) => {
    const nodeRef = useRef();
    const [open, setOpen] = useState(true);
    const [details, setDetails] = useState({
        /* 基础信息 */
        baseInfo: {
            cnName: '',
            projectSource: '',
            negotiationDate: '',
            endTime: '',
            totalInvestment: '',
            projectType: '',
            projectCategory: '',
            customStatus: '',
            unitName: '',
            responsiblePersonInfo: '',
            remark: ''
        },
        /* 节点数据 */
        nodeList: [],
        /* 进展情况 */
        nodeProgressList: [],
        /* 问题情况 */
        nodeProblemList: [],
        /* 全部点评 */
        nodeEvaluateList: []
    });
    const {searchParams} = useRouterLink();
    const [selectOptions, setSelectOptions] = useState({
        projectType: [],
        projectCategory: [],
        projectProgress: [],
    });
    const [form] = Form.useForm();

    useEffect(() => {
        const id = searchParams.get('id');
        if (id) {
            queryDetail(id);
        }
    }, [])

    const openModal = (details) => {
        setOpen(true);
        setDetails(details);
        if (!details.add) {
            form.setFieldsValue(details);
        }
    };

    /* 查询参数列表 */
    const querySelectOptions = async () => {
        const res = await Promise.all([
            pageParamList({type: projectType.PROJECT_TYPE}),
            pageParamList({type: projectType.PROJECT_CATEGORY}),
            pageParamList({type: projectType.PROJECT_STATUS}),
        ]);
        const [type, category] = res.map(item => (item.data || []).map(item => ({
            label: item.cnName,
            value: item.id
        })));
        const progress = flattenTree(res[2].data, 'childParams').map(item => ({label: item.cnName, value: item.id, level: item.level}));

        setSelectOptions({
            projectType: type,
            projectCategory: category,
            projectProgress: progress,
        });
        return Promise.resolve({type, category, progress});
    };

    /* 查询详情 */
    const queryDetail = async (id) => {
        if (!id) {
            openModal({id: 1});
            return;
        }
        const {type, category, progress} = await querySelectOptions();
        const res = await detailProject({id});
        if (res.data) {
            console.log(res.data);
            const {nodeVos = [], proProgressVos = [], proNodeIssueVos = [], proCommentVos = [], ...extra} = res.data;
            const detailData = {...details};
            const responsiblePersonInfo = extra.responsiblePersonInfo ? JSON.parse(extra.responsiblePersonInfo) : [];
            detailData.baseInfo = {
                ...extra,
                responsiblePersonInfo: responsiblePersonInfo.map(item => `${item.name}(${item.phoneNum || '--'})`).join('、'),
                type: type.find(item => item.value === extra.type)?.label || '',
                category: category.find(item => item.value === extra.category)?.label || '',
                progress: progress.find(item => item.value === extra.progress)?.label || '',
            };
            detailData.nodeList = nodeVos || [];
            detailData.nodeProgressList = proProgressVos || [];
            detailData.nodeProblemList = proNodeIssueVos || [];
            detailData.nodeEvaluateList = proCommentVos || [];
            setDetails(detailData);
            if (!details.add) {
                form.setFieldsValue(details);
            }
        }
    };

    useImperativeHandle(ref, () => {
        return {
            openModal: queryDetail,
        };
    });

    const handleOk = async () => {
        try {
            const values = await form.validateFields();
            console.log(values);
            handleCancel();
        } catch (e) {
            console.log(e);
        }
    };

    const handleCancel = () => {
        setOpen(false);
        form.resetFields();
    };
    /* 查看节点信息 */
    const showNodeDetail = (record) => {
        nodeRef.current.openModal(record);
    };
    /* 返回上级 */
    const backToParent = () => {
        history.back();
    };
    return <Card ref={ref} title={'项目详情'} className={'margin-20 flex-sub'}
                 extra={<Button onClick={backToParent}>返回</Button>}>
        <div className={'flex flex-direction-column margin-top-12'}>
            {
                detailConfig.map((item, index) => {
                    return <div key={item.key}>
                        <Title level={5} className={'detail-title'}>{item.title}</Title>
                        <div className={'padding-tb-20 padding-lr-12'}>
                            {
                                item.info &&
                                <Descriptions column={item.column} className={''}>
                                    {
                                        item.children.map(child => {
                                            return <Descriptions.Item key={child.key} label={child.label}>
                                                {details[item.key][child.key]}
                                            </Descriptions.Item>;
                                        })
                                    }
                                </Descriptions>
                            }
                            {
                                item.steps &&
                                <div>
                                    <Steps
                                        direction={"vertical"}
                                        items={details[item.key].map(ov => {
                                            const responsiblePersonInfo = ov.responsiblePersonInfo ? JSON.parse(ov.responsiblePersonInfo) : [];
                                            const nodeColor = ['bg-color-e8f7ff color-3491fa', 'bg-color-fff7e8 color-ff7d00', 'bg-color-ffece8 color-f53f3f', 'bg-color-e8ffea color-00b42a'];
                                            const nodeStateName = ['正常','黄牌', '红牌','完成']
                                            return {
                                                title: <div className={'flex align-center justify-between'}>
                                                    <div>{ov.time || ov.evaluateTime || ov.problemDate || ov.planCompletionTime || ov.updTime}</div>
                                                    {
                                                        item.node &&
                                                        <Button type={'link'} size={'small'} onClick={() => showNodeDetail(ov)}>查看</Button>
                                                    }
                                                </div>,
                                                description: <div className={'flex flex-direction-column'}>
                                                    <div className={'flex align-center gap-8'}>
                                                        <span>{ov.content || ov.evaluateDesc || ov.cnName || ov.description}</span>
                                                        {
                                                            item.key === 'nodeList' && ov.status > 0 &&
                                                                <div className={`margin-left-8 padding-lr-4 line-height-14 font-size-12 line-height-22 border-radius-4 ${nodeColor[ov.status - 1]}`}>
                                                                    {
                                                                        nodeStateName[ov.status - 1]
                                                                    }
                                                                </div>
                                                        }
                                                        {
                                                            ov.delayCnt > 0 &&
                                                            <div className={'margin-left-8 padding-lr-4 line-height-14 font-size-12 border-radius-4 bg-color-fff7e8 color-ff7d00'}>
                                                                延期{ov.delayCnt}次
                                                            </div>
                                                        }
                                                        {
                                                            ov.yellowCnt > 0 &&
                                                            <div className={'margin-left-8 padding-lr-4 line-height-14 font-size-12 border-radius-4 bg-color-fff7e8 color-ff7d00'}>
                                                                黄牌{ov.yellowCnt}次
                                                            </div>
                                                        }
                                                        {
                                                            ov.redCnt > 0 &&
                                                            <div className={'margin-left-8 padding-lr-4 line-height-14 font-size-12 border-radius-4 bg-color-fff7e8 color-ff7d00'}>
                                                                曾有红牌
                                                            </div>
                                                        }
                                                        {
                                                            ov.needLeaderStatus === 1 &&
                                                            <div className={'margin-left-8 padding-lr-4 line-height-14 font-size-12 border-radius-4 bg-color-fff7e8 color-ff7d00'}>
                                                                需要领导协调
                                                            </div>
                                                        }
                                                        {
                                                            item.key === 'nodeProblemList' &&
                                                            <div className={'margin-left-8 padding-lr-4 line-height-14 font-size-12 border-radius-4 bg-color-fff7e8 color-ff7d00'}>
                                                                {ov.resolveStatus === 1 ? '已解决' : '待解决'}
                                                            </div>
                                                        }
                                                    </div>
                                                    {
                                                        ov.responsibleUnitName &&
                                                        <div>
                                                            单位：{ov.responsibleUnitName || '--'}
                                                        </div>
                                                    }
                                                    {
                                                        responsiblePersonInfo?.length > 0 &&
                                                        <div>责任人及联系方式：{responsiblePersonInfo?.map(person => `${person.name}(${person.phoneNum || '--'})`).join('、') || '--'}</div>
                                                    }
                                                </div>,
                                                status: 'process'
                                            };
                                        })}
                                    />
                                    {
                                        details[item.key].length === 0 && <Empty description={item.emptyTips}/>
                                    }
                                </div>
                            }
                        </div>
                    </div>
                })
            }
        </div>
        <NodeModel ref={nodeRef}/>
    </Card>;
};
export default forwardRef(Detail);
