/**
 * @description index.jsx - 文件描述
 * <AUTHOR>
 *
 * Created on 2024/10/25 16:45
 */
import React, { forwardRef, useImperativeHandle, useMemo, useState } from 'react';
import { Descriptions, Divider, Empty, Modal, Space, Steps, Tabs } from 'antd';
import { nodeConfig } from '@/pages/Bidmgt/NodeProjectManage/const';
import { auditDetail, auditList, nodeDetail, nodeDetailProgress, problemList, progressList } from '@/api/Bidmgt/important/groupEvent';

const nodeColor = ['bg-color-e8f7ff color-3491fa', 'bg-color-fff7e8 color-ff7d00', 'bg-color-ffece8 color-f53f3f', 'bg-color-e8ffea color-00b42a'];
const nodeStateName = ['正常', '黄牌', '红牌', '完成'];

const ModalNode = (props, ref) => {
	const [open, setOpen] = useState(false);
	const [details, setDetails] = useState({
		baseInfo: {
			id: 1,
			planCompletionTime: '2020-07-01',
			nodeTitle: '节点：协调企业完成规划方案报送区自规局审批中心',
			nodeStatus: 1,
			nodeIndex: 1,
			yellowCount: '10',
			redCount: '3',
			delayCount: '1',
			responsiblePerson: '陈鹏',
			responsiblePersonPhone: '13037119050',
			unitPerson: '吴位辉',
			unitPersonPhone: '1876312980',
			organization: '经开区',
		},
		nodeDelay: [
			{
				id: 1,
				delayDate: '2020-07-01',
				delayReason: '项目进度 diagram',
				delayPerson: '陈鹏',
				delayPersonPhone: '13037119050',
				delayUnit: '中科软',
				delayUnitPhone: '1876312980',
				delayRemark: '项目进度 diagram',
			},
		],
		nodeDestroy: [
			{
				id: 1,
				nodeDate: '2024-07-01',
				status: 1,
				approveStatus: 1,
				approveStatusName: '已通过',
				applyDate: '2024-07-15',
				content: '节点已经完成，准备启动下一节点2',
				approveDate: '2024-09-15',
				extraInfoList: [
					{
						label: '申请时间',
						value: '2024-07-15',
						key: 'applyDate',
					},
					{
						label: '申请内容',
						value: '节点工作大部分完成，展厅建设需要调整',
						key: 'content',
					},
					{
						label: '审批时间',
						value: '2024-09-15',
						key: 'approveDate',
					},
				],
			},
			{
				id: 2,
				nodeDate: '2024-07-01',
				status: 2,
				approveStatus: 2,
				approveStatusName: '未通过',
				applyDate: '2024-07-15',
				content: '节点工作大部分完成，展厅建设需要调整',
				approveDate: '2024-09-15',
				extraInfoList: [
					{
						label: '申请时间',
						value: '2024-07-15',
						key: 'applyDate',
					},
					{
						label: '申请内容',
						value: '',
						key: 'content',
					},
					{
						label: '审批时间',
						value: '2024-09-15',
						key: 'approveDate',
					},
				],
			},
		],
		nodeSchedule: [
			{
				id: 1,
				date: '2024-07-01',
				status: 1,
				approveStatus: 1,
				approveStatusName: '已通过',
				applyDate: '2024-07-15',
				content: '节点已经完成，准备启动下一节点2',
				approveDate: '2024-09-15',
				replyContent: '已协调资金，资金已到位',
				replyDate: '2024-09-15',
				fileList: [
					{
						url: 'https://img.alicdn.com/tfs/TB1.Z29GpXXXXc_XpXXXXXXXXXX-1024-1024.png',
						name: '资金协调单.png',
					},
					{
						url: 'https://img.alicdn.com/tfs/TB1.Z29GpXXXXc_XpXXXXXXXXXX-1024-1024.png',
						name: '资金协调单.png',
					},
				],
				extraInfoList: [
					{
						label: '调度时间',
						value: '2024-07-15',
						key: 'applyDate',
					},
					{
						label: '回复内容',
						value: '已协调资金，资金已到位',
						key: 'replyContent',
					},
					{
						label: '审批时间',
						value: '2024-09-15',
						key: 'replyDate',
					},
				],
			},
		],
	});

	const openModal = async (data) => {
		// const res = await nodeDetail({id: details.id})
		setOpen(true);
		getNodeInfo(data);
	};

	const getNodeInfo = async (data) => {
		console.log('查询节点详情');
		const res = await Promise.all([
			nodeDetail({ id: data.id }),
			auditList({ proNodeId: data.id, auditType: 1 }),
			auditList({ proNodeId: data.id, auditType: 2 }),
			progressList({ proNodeId: data.id }),
		]);
		const [baseInfo, nodeDestroy, nodeDelay, nodeSchedule] = res.map((item) => item.data);
		console.log(baseInfo, nodeDelay, nodeDestroy, nodeSchedule);
		nodeDestroy.forEach((item) => {
			item.auditData = item.auditContent ? JSON.parse(item.auditContent) : {};
			// 审批状态，1未审批 2通过 3不通过
			const { fileList = [], description, completion } = item.auditData;
			const auditTime = [
				{
					label: '审批内容',
					value: item.auditDesc || '',
					key: 'auditDesc',
				},
				{
					label: '审批时间',
					value: item.auditTime || '',
					key: 'auditTime',
				},
			];
			item.extraInfoList = [
				{
					label: '申请时间',
					value: item.createTime || '',
					key: 'createTime',
					fileList: fileList,
				},
			];
			if (item.auditTime) {
				item.extraInfoList.push(...auditTime);
			}
		});
		nodeDelay.forEach((item) => {
			item.auditData = item.auditContent ? JSON.parse(item.auditContent) : {};
			// 审批状态，1未审批 2通过 3不通过
			const { fileList = [], description } = item.auditData;
			const auditTime = [
				{
					label: '审批内容',
					value: item.auditDesc || '',
					key: 'auditDesc',
				},
				{
					label: '审批时间',
					value: item.auditTime || '',
					key: 'auditTime',
				},
			];
			item.extraInfoList = [
				{
					label: '申请时间',
					value: item.createTime || '',
					key: 'createTime',
					fileList: fileList,
				},
			];
			if (item.auditTime) {
				item.extraInfoList.push(...auditTime);
			}
		});
		nodeSchedule.forEach((item) => {
			const replyAttachment = item.replyAttachment;
			const { fileList = [] } = replyAttachment ? JSON.parse(replyAttachment) : {};
			const replayData = [
				{
					label: '回复内容',
					value: item.replyContent,
					key: 'replyContent',
					fileList: fileList,
				},
				{
					label: '回复时间',
					value: item.updateTime,
					key: 'updateTime',
				},
			];
			item.extraInfoList = [
				{
					label: '调度时间',
					value: item.createTime,
					key: 'createTime',
				},
			];
			if (item.status === 1) {
				item.extraInfoList.push(...replayData);
			}
		});
		const responsiblePersonInfo = baseInfo.responsiblePersonInfo ? JSON.parse(baseInfo.responsiblePersonInfo) : [];
		baseInfo.responsiblePersonInfo = responsiblePersonInfo.map((item) => `${item.name}(${item.phoneNum || '--'})`).join('、');
		const detail = { baseInfo, nodeDelay, nodeDestroy, nodeSchedule };
		setDetails(detail);
	};

	useImperativeHandle(ref, () => {
		return {
			openModal,
		};
	});

	const handleOk = async () => {
		try {
			handleCancel();
		} catch (e) {
			console.log(e);
		}
	};

	const handleCancel = () => {
		setOpen(false);
	};

	const tabsList = useMemo(() => {
		return nodeConfig.map((item) => {
			return {
				key: item.key,
				label: item.title,
				children: (
					<div className={'padding-tb-20 padding-lr-12'}>
						{item.info && (
							<Descriptions column={item.column} className={''}>
								{item.children.map((child) => {
									const value = details[item.key][child.key];
									return (
										<Descriptions.Item key={child.key} label={child.label}>
											{child.key !== 'status' && value}
											{child.key === 'status' && value > 0 && (
												<div
													className={`margin-left-8 padding-lr-4 line-height-14 line-height-22 font-size-12 border-radius-4 ${nodeColor[value - 1]}`}
												>
													{nodeStateName[value - 1]}
												</div>
											)}
										</Descriptions.Item>
									);
								})}
							</Descriptions>
						)}
						{item.steps && (
							<div>
								<Steps
									direction={'vertical'}
									items={details[item.key].map((ov) => {
										const auditContent = ov.auditData || {};
										return {
											title: ov.planCompletionTime || ov.evaluateTime || ov.problemDate || auditContent.completeDate || '--',
											description: (
												<div className={'flex flex-direction-column min-width-300 '}>
													<div>{ov.content || ov.evaluateDesc || ov.nodeTitle || auditContent.description || '--'}</div>
													{ov.extraInfoList &&
														ov.extraInfoList.length > 0 &&
														ov.extraInfoList.map((extra) => {
															return (
																<div key={extra.key} className={'flex flex-direction-column'}>
																	<div>
																		{extra.label}：{extra.value}
																	</div>
																	{extra.fileList && extra.fileList.length > 0 && (
																		<div className={'flex align-center'}>
																			附件：
																			<Space>
																				{extra.fileList.map((file) => {
																					return (
																						<a href={file.url} target="_blank" className={''}>
																							{file.name}
																						</a>
																					);
																				})}
																			</Space>
																		</div>
																	)}
																</div>
															);
														})}
												</div>
											),
											status: 'process',
										};
									})}
								/>
								{details[item.key].length === 0 && <Empty description={`暂无${item.emptyTips}`} />}
							</div>
						)}
					</div>
				),
			};
		});
	}, [details, nodeConfig]);

	return (
		<Modal
			open={open}
			onCancel={handleCancel}
			onOk={handleOk}
			title={'项目节点信息'}
			styles={{ body: { minHeight: 500 } }}
			width={1000}
			footer={null}
		>
			<Divider style={{ margin: '24px -24px', width: 'auto' }} />
			<Tabs items={tabsList} />
		</Modal>
	);
};
export default forwardRef(ModalNode);
