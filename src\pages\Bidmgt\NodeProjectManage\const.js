/**
 * @description const - 文件描述
 * <AUTHOR>
 *
 * Created on 2024/10/14 17:09
 */

/* 详情配置项 */
export const detailConfig = [
    {
        title: "基本信息",
        key: "baseInfo",
        column: 2,
        children: [
            {
                label: "项目名称",
                key: "cnName",
            },
            {
                label: "项目建设单位",
                key: "constructionUnitName",
            },
            {
                label: "项目开工时间",
                key: "planStartTime",
            },
            {
                label: "项目竣工时间",
                key: "planCompletionTime",
            },
            {
                label: "总投资（今年计划投资）",
                key: "totalInvestment",
            },
            {
                label: "项目类型",
                key: "type",
            },
            {
                label: "项目类别",
                key: "category",
            },
            {
                label: "项目阶段",
                key: "progress",
            },
            {
                label: "项目责任单位",
                key: "responsibleUnitName",
            },
            {
                label: "责任人及联系方式",
                key: "responsiblePersonInfo",
            },
            {
                label: "备注",
                key: "remark",
            },
        ],
        info: true
    },
    {
        title: "节点信息",
        emptyTips: "暂无节点信息",
        key: "nodeList",
        steps: true,
        node: true,
    },
    {
        title: "进展情况",
        emptyTips: "暂无进展情况",
        key: "nodeProgressList",
        steps: true
    },
    {
        title: "问题反馈",
        emptyTips: "暂无问题反馈",
        key: "nodeProblemList",
        steps: true
    },
    {
        title: "全部点评",
        emptyTips: "暂无点评",
        key: "nodeEvaluateList",
        steps: true
    },
];

/* 节点配置项 */
export const nodeConfig = [
    {
        title: "基本信息",
        key: "baseInfo",
        column: 2,
        children: [
            {
                label: '节点名称',
                value: '项目签约',
                key: 'cnName',
            },
            {
                label: '节点时间',
                value: '2024-07-01',
                key: 'planCompletionTime',
            },
            {
                label: '销号标准',
                value: '未完成签约',
                key: 'pinNumStandard',
            },
            {
                label: '节点状态',
                value: '黄牌',
                key: 'status',
            },
            {
                label: '责任单位',
                value: '中山三院',
                key: 'responsibleUnitName',
            },
            {
                label: '责任人及联系方式',
                value: '张三，13037119050',
                key: 'responsiblePersonInfo',
            },
            {
                label: '节点序号',
                value: '1',
                key: 'ordinal',
            },
        ],
        info: true
    },
    {
        title: "节点销号",
        emptyTips: "暂无节点销号信息",
        key: "nodeDestroy",
        steps: true,
    },
    {
        title: "节点延期",
        emptyTips: "暂无节点延期",
        key: "nodeDelay",
        steps: true,
    },
    {
        title: "在线调度",
        emptyTips: "暂无在线调度",
        key: "nodeSchedule",
        steps: true,
    },
]

/* 	督办项目参数类型
* 1项目类型 2项目阶段 3项目类别
*  */
export const projectType = {
    PROJECT_TYPE: 1,
    PROJECT_STATUS: 2,
    PROJECT_CATEGORY: 3,
}

/* 模板文件地址 */
export const templateUrl = {
    // 模板文件
    prdTemplate: 'https://gbac-produce.oss-cn-shenzhen.aliyuncs.com/static-assets/gbac-bidmgt-admfrontend/files/%E8%8A%82%E7%82%B9%E9%A1%B9%E7%9B%AE%E5%AF%BC%E5%85%A5%E6%A8%A1%E6%9D%BF.xlsx',
}
