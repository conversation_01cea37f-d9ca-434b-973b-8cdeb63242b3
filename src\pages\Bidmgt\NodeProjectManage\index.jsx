/**
 * @description index.jsx - 节点项目管理
 * <AUTHOR>
 *
 * Created on 2024/10/14 11:15
 */
import React, { useEffect, useRef, useState } from 'react';
import { Button, Col, Form, Input, Row, Select, Table, Typography, Upload, message } from 'antd';
import { SearchOutlined, UndoOutlined } from '@ant-design/icons';
import { useTableData } from '@/hook/useTableData';
import { useRouterLink } from '@/hook/useRouter';
import { exportSpvPro, importProject, pageParamList, pageProject, refreshNodeStatus } from '@/api/Bidmgt/important/groupEvent';
import { projectType, templateUrl } from './const';
import { listInvestmentResponsibleDept } from '@/api/Bidmgt/ConfigCenter';

const { Title } = Typography;

const layoutSpan = { xs: 24, sm: 12, md: 8, lg: 6 };
const NodeProjectManage = () => {
	const { linkTo } = useRouterLink();
	const [selectOptions, setSelectOptions] = useState({
		projectType: [],
		projectCategory: [],
		projectStatus: [],
		projectUnit: [],
	});

	const { form, dataSource, pagination, changePage, onSearch, onReset } = useTableData({
		getTablePageData: pageProject,
	});

	useEffect(() => {
		getParams();
	}, []);

	/* 获取列表参数 */
	const getParams = async () => {
		const res = await Promise.all([
			pageParamList({ type: projectType.PROJECT_TYPE }),
			pageParamList({ type: projectType.PROJECT_CATEGORY }),
			listInvestmentResponsibleDept(),
		]);
		console.log('res', res);
		const [type, category] = res.map((item) => (item.data || []).map((item) => ({ label: item.cnName, value: item.id })));
		const unit = res[2]?.data?.map((item) => ({ label: item.deptName, value: item.deptId }));
		console.log('res', type, category);
		setSelectOptions({
			projectType: type,
			projectCategory: category,
			projectUnit: unit,
		});
	};

	const columns = [
		{ title: '项目名称', dataIndex: 'cnName' },
		{
			title: '项目类型',
			dataIndex: 'type',
			render: (type) => {
				return selectOptions.projectType?.find((item) => item.value === type)?.label || '--';
			},
		},
		{
			title: '项目类别',
			dataIndex: 'category',
			render: (category) => {
				return selectOptions.projectCategory?.find((item) => item.value === category)?.label || '--';
			},
		},
		{ title: '项目建设单位', dataIndex: 'constructionLegalUnitName' },
		{ title: '计划开工时间', dataIndex: 'planStartTime' },
		{ title: '计划竣工时间', dataIndex: 'planCompletionTime' },
		{ title: '总投资（今年计划投资）', dataIndex: 'totalInvestment', align: 'center' },
		{ title: '项目责任单位', dataIndex: 'responsibleUnitName' },
		{
			title: '责任人及联系方式',
			dataIndex: 'responsiblePersonInfo',
			render: (responsiblePersonInfo) => {
				const Persons = responsiblePersonInfo ? JSON.parse(responsiblePersonInfo) : {};
				return Persons.map((item, index) => `${item.name} ${item.phoneNum || '--'}`).join('、');
			},
		},
		{
			title: '操作',
			dataIndex: 'operation',
			width: 120,
			align: 'center',
			fixed: 'right',
			render: (_, record) => [
				<Button type={'link'} size={'small'} onClick={() => handleDetail(record)}>
					查看
				</Button>,
				// <Button type={'link'} danger size={'small'}>删除</Button>,
			],
		},
	];

	/* 查看项目详情 */
	const handleDetail = (record) => {
		linkTo(`/bidmgt/projectManage/nodeProjectManage/detail?id=${record.id}`);
	};

	/* 导入项目 */
	const handleImport = async ({ file, onSuccess }) => {
		try {
			const res = await importProject({ file });
			if (res) {
				onSuccess();
				console.log('res', res);
				message.success('导入成功');
				onSearch();
			}
		} catch (e) {
			console.error('e', e);
			// message.error('导入失败');
		}
	};

	/* 导出项目数据 */
	const handleExport = async () => {
		const params = form.getFieldsValue();
		const res = await exportSpvPro(params);
		console.log('res', res);
		if (res && res instanceof Blob) {
			// 设置文件名，默认为 "exported-file.xlsx"
			let filename = 'exported-file.xlsx';
			// 如果服务器在响应头中返回了文件名，从响应头中获取
			const contentDisposition = res.headers ? res.headers['content-disposition'] : null;
			if (contentDisposition) {
				const match = contentDisposition.match(/filename="(.+)"/);
				if (match && match[1]) {
					filename = match[1];
				}
			}
			// 创建一个临时的 <a> 标签
			const blobUrl = URL.createObjectURL(res);
			const a = document.createElement('a');
			a.href = blobUrl;
			a.download = filename;
			document.body.appendChild(a);
			a.click();
			// 释放资源
			document.body.removeChild(a);
			URL.revokeObjectURL(blobUrl);
		}
		// message.warning('导出功正在开发中');
	};

	const uploadProps = {
		name: 'file',
		customRequest: handleImport,
		multiple: false,
		showUploadList: false,
	};

	/* 下载模板 */
	const handleDownload = () => {
		// message.warning('下载功正在开发中');
		window.open(templateUrl.prdTemplate);
	};
	/* 更新节点状态 */
	const handleNodeUpd = async () => {
		const res = await refreshNodeStatus();
		if (res) {
			message.success('更新成功');
			onSearch();
		}
	};
	return (
		<div className={'flex-sub flex flex-direction-column padding-20 bg-color-ffffff margin-20 border-radius-4'}>
			<Title level={4}>节点项目管理</Title>
			<div className={'flex gap-12 margin-tb-12'}>
				<Upload {...uploadProps}>
					<Button type={'primary'}>导入</Button>
				</Upload>
				<Button onClick={handleDownload}>下载模板</Button>
				<Button onClick={handleExport}>导出</Button>
				<Button onClick={handleNodeUpd}>节点更新</Button>
			</div>
			<div className={'flex align-center gap-12 margin-bottom-12'}>
				<Form form={form} className={'width-100per'}>
					<Row gutter={12}>
						<Col {...layoutSpan}>
							<Form.Item label={'项目名称'} name="name">
								<Input placeholder="请输入项目名称" allowClear />
							</Form.Item>
						</Col>
						<Col {...layoutSpan}>
							<Form.Item label={'项目类型'} name="typeId">
								<Select options={selectOptions.projectType} placeholder="请选择项目类型" allowClear />
							</Form.Item>
						</Col>
						<Col {...layoutSpan}>
							<Form.Item label={'项目类别'} name="categoryId">
								<Select options={selectOptions.projectCategory} placeholder="请选择项目类别" allowClear />
							</Form.Item>
						</Col>
						<Col {...layoutSpan}>
							<Form.Item label={'项目责任单位'} name="unitId">
								<Select options={selectOptions.projectUnit} placeholder="请选择项目责任单位" allowClear />
							</Form.Item>
						</Col>
						{/*<Col {...layoutSpan}>*/}
						{/*    <Form.Item label={"节点责任单位"} name="nodeUnitId">*/}
						{/*        <Select options={selectOptions.projectUnit} placeholder="请选择节点责任单位" allowClear/>*/}
						{/*    </Form.Item>*/}
						{/*</Col>*/}
					</Row>
					<div className={'flex justify-end align-center gap-12'}>
						<Form.Item noStyle>
							<Button type="primary" icon={<SearchOutlined />} onClick={onSearch}>
								查询
							</Button>
						</Form.Item>
						<Form.Item noStyle>
							<Button icon={<UndoOutlined />} onClick={onReset}>
								重置
							</Button>
						</Form.Item>
					</div>
				</Form>
			</div>
			{/* 表格 开始 */}
			<Table
				rowKey="id"
				dataSource={dataSource}
				pagination={pagination}
				onChange={changePage}
				scroll={{ x: 'max-content' }}
				columns={columns}
			/>
			{/* 表格 结束 */}
		</div>
	);
};
export default NodeProjectManage;
