import {
	Col,
	Card,
	Row,
	Progress,
	Tabs,
	Table,
	Button,
	Tag,
	Avatar,
	Space,
	Input,
	Modal,
} from 'antd';
import { getImageSrc } from '@/assets/images/index';
import { UserOutlined } from '@ant-design/icons';
import {
	useEffect,
	useState,
	useRef,
	useImperativeHandle,
	forwardRef,
} from 'react';
import { useSelector, useDispatch } from 'react-redux';
import { pageProjectFollow } from '@/api/Bidmgt/ProjectManage/index';
import {
	myProjectPage,
	notificationPage,
	updateReadFlag,
} from '@/api/Bidmgt/PersonalCenter/index';
import { useRouterLink } from '@/hook/useRouter';

// 我的消息
const TableNotifice = (props) => {
	const { linkTo } = useRouterLink();
	const [dataSource, setDataSource] = useState([]);
	const [total, setTotal] = useState(0);

	// 页码数据
	const [pagination, setPagination] = useState({
		pageNum: 1,
		pageSize: 10,
	});

	// 页码页数改变
	const changePage = (pageNum, pageSize) => {
		pagination.pageSize = pageSize;
		pagination.pageNum = pageNum;
		setPagination({ ...pagination });
	};

	const getList = () => {
		notificationPage({
			pageNum: pagination.pageNum,
			pageSize: pagination.pageSize,
		}).then((res) => {
			setDataSource(res.data.records || []);
			setTotal(res.data.total - 0);
		});
	};
	useEffect(() => {
		getList();
	}, [pagination]);

	return (
		<Table
			size='small'
			rowKey='id'
			dataSource={dataSource}
			pagination={{
				...pagination,
				total,
				showTotal: (total) => `共 ${total} 条`,
			}}
			onChange={(e) => changePage(e.current, e.pageSize)}
		>
			<Table.Column
				title='消息类型'
				key='noticeType'
				dataIndex='noticeType'
				width={80}
				render={(noticeType) => {
					return (
						<div>
							{noticeType == '1' && <Tag color='red'>预警</Tag>}
							{noticeType == '2' && (
								<Tag color='warning'>催办</Tag>
							)}
						</div>
					);
				}}
			/>
			<Table.Column
				title='消息内容'
				key='noticeContent'
				dataIndex='noticeContent'
			/>
			<Table.Column
				title='是否已读'
				key='readFlag'
				dataIndex='readFlag'
				width={80}
				render={(readFlag) => {
					return (
						<div>
							{readFlag == 1 && <Tag color='default'>已读</Tag>}
							{readFlag == 0 && <Tag color='red'>未读</Tag>}
						</div>
					);
				}}
			/>
			<Table.Column
				title='操作'
				key='option'
				dataIndex='option'
				align='center'
				width={120}
				fixed='right'
				render={(_, record) => {
					return (
						<>
							<Button
								type='link'
								size='small'
								onClick={() =>
									linkTo(
										`/bidmgt/projectManage/detail?id=${record.id}`
									)
								}
							>
								去处理
							</Button>
							{record.readFlag == 0 && (
								<Button
									type='link'
									size='small'
									onClick={() =>
										updateReadFlag({
											id: record.id || '',
											readFlag: 1,
										}).then(() => {
											getList();
										})
									}
								>
									已读
								</Button>
							)}
						</>
					);
				}}
			/>
		</Table>
	);
};

// 我的项目
const Table1 = () => {
	const { linkTo } = useRouterLink();
	const [dataSource, setDataSource] = useState([]);
	const [total, setTotal] = useState(0);

	// 页码数据
	const [pagination, setPagination] = useState({
		pageNum: 1,
		pageSize: 10,
	});

	// 页码页数改变
	const changePage = (pageNum, pageSize) => {
		pagination.pageSize = pageSize;
		pagination.pageNum = pageNum;
		setPagination({ ...pagination });
	};

	const getList = () => {
		myProjectPage({
			pageNum: pagination.pageNum,
			pageSize: pagination.pageSize,
		}).then((res) => {
			setDataSource(res.data.records || []);
			setTotal(res.data.total - 0);
		});
	};
	useEffect(() => {
		getList();
	}, [pagination]);
	return (
		<Table
			rowKey='id'
			dataSource={dataSource}
			pagination={{
				...pagination,
				total,
				showTotal: (total) => `共 ${total} 条`,
			}}
			onChange={(e) => changePage(e.current, e.pageSize)}
			scroll={{
				x: 1300,
			}}
		>
			<Table.Column
				title='项目名称'
				key='projectName'
				dataIndex='projectName'
				width={190}
			/>
			<Table.Column
				title='项目编号'
				key='projectCode'
				dataIndex='projectCode'
				width={110}
			/>

			<Table.Column
				title='项目阶段'
				key='projectStageName'
				dataIndex='projectStageName'
				width={100}
			/>
			<Table.Column
				title='项目类型'
				key='projectTypeName'
				dataIndex='projectTypeName'
				width={200}
			/>
			<Table.Column
				title='上报时间'
				key='createTime'
				dataIndex='createTime'
				width={120}
				render={(createTime) => {
					return `${createTime || ''}`.slice(0, 16);
				}}
			/>
			<Table.Column
				title='更新时间'
				key='updateTime'
				dataIndex='updateTime'
				width={120}
				render={(updateTime) => {
					return `${updateTime || ''}`.slice(0, 16);
				}}
			/>
			<Table.Column
				title='最新进展'
				key='latestDevelopments'
				dataIndex='latestDevelopments'
				width={340}
			/>
			<Table.Column
				title='操作'
				key='option'
				dataIndex='option'
				align='center'
				width={70}
				fixed='right'
				render={(_, record) => {
					return (
						<>
							<Button
								type='link'
								size='small'
								onClick={() =>
									linkTo(
										`/bidmgt/projectManage/detail?id=${record.id}`
									)
								}
							>
								查看
							</Button>
						</>
					);
				}}
			/>
		</Table>
	);
};

// 追踪项目动态
const Table2 = () => {
	const { linkTo } = useRouterLink();
	const [dataSource, setDataSource] = useState([]);
	const [total, setTotal] = useState(0);

	// 页码数据
	const [pagination, setPagination] = useState({
		pageNum: 1,
		pageSize: 10,
	});

	// 页码页数改变
	const changePage = (pageNum, pageSize) => {
		pagination.pageSize = pageSize;
		pagination.pageNum = pageNum;
		setPagination({ ...pagination });
	};

	const getList = () => {
		pageProjectFollow({
			pageNum: pagination.pageNum,
			pageSize: pagination.pageSize,
		}).then((res) => {
			setDataSource(res.data.records || []);
			setTotal(res.data.total - 0);
		});
	};
	useEffect(() => {
		getList();
	}, [pagination]);
	return (
		<Table
			rowKey='id'
			dataSource={dataSource}
			pagination={{
				...pagination,
				total,
				showTotal: (total) => `共 ${total} 条`,
			}}
			onChange={(e) => changePage(e.current, e.pageSize)}
		>
			<Table.Column
				title='项目名称'
				key='projectName'
				dataIndex='projectName'
				width={280}
			/>
			<Table.Column
				title='项目编号'
				key='projectCode'
				dataIndex='projectCode'
				width={120}
			/>
			<Table.Column
				title='最新进展'
				key='latestDevelopments'
				dataIndex='latestDevelopments'
			/>
			<Table.Column
				title='操作'
				key='option'
				dataIndex='option'
				align='center'
				width={70}
				render={(_, record) => {
					return (
						<>
							<Button
								type='link'
								size='small'
								onClick={() =>
									linkTo(
										`/bidmgt/projectManage/detail?id=${record.id}`
									)
								}
							>
								查看
							</Button>
						</>
					);
				}}
			/>
		</Table>
	);
};

// 待办事项
const Table3 = () => {
	const { linkTo } = useRouterLink();
	const [dataSource, setDataSource] = useState([]);
	const [total, setTotal] = useState(0);
	const [readFlag, setReadFlag] = useState(0);

	// 页码数据
	const [pagination, setPagination] = useState({
		pageNum: 1,
		pageSize: 10,
	});

	// 页码页数改变
	const changePage = (pageNum, pageSize) => {
		pagination.pageSize = pageSize;
		pagination.pageNum = pageNum;
		setPagination({ ...pagination });
	};

	const getList = () => {
		notificationPage({
			readFlag,
			pageNum: pagination.pageNum,
			pageSize: pagination.pageSize,
		}).then((res) => {
			setDataSource(res.data.records || []);
			setTotal(res.data.total - 0);
		});
	};

	// 处理 1. 预警 2. 催办 3. 消息 4. 指派 5. 项目审核 6. 审核通过 7. 审核不通过 8. 接受认领 9. 拒绝认领
	const toDetail = (record) => {
		const { projectId, noticeType } = record;
		if ([1, 2, 3].includes(noticeType)) {
			updateReadFlag({
				id: record.id || '',
				readFlag: 1,
			}).then(() => {
				linkTo(`/bidmgt/projectManage/detail?id=${record.projectId}`);
			});
		} else {
			if (
				['6', '7', '8', '9'].includes(`${noticeType}`) &&
				record.readFlag === 0
			) {
				updateReadFlag({
					id: record.id || '',
					readFlag: 1,
				}).then(() => {
					linkTo(
						`/bidmgt/personalCenter/projectDetail?id=${record.projectId}&readFlag=${record.readFlag}&noticeType=${record.noticeType}&noticeId=${record.id}&businessId=${record.businessId}`
					);
				});
			} else {
				linkTo(
					`/bidmgt/personalCenter/projectDetail?id=${record.projectId}&readFlag=${record.readFlag}&noticeType=${record.noticeType}&noticeId=${record.id}&businessId=${record.businessId}`
				);
			}
		}
	};

	useEffect(() => {
		getList();
	}, [pagination, readFlag]);
	return (
		<>
			<Space className='margin-bottom-16' size={16}>
				<Button
					type={readFlag === '' ? 'primary' : 'default'}
					ghost={readFlag === ''}
					size='small'
					onClick={() => setReadFlag('')}
				>
					全部
				</Button>
				<Button
					type={readFlag === 0 ? 'primary' : 'default'}
					ghost={readFlag === 0}
					size='small'
					onClick={() => setReadFlag(0)}
				>
					待处理
				</Button>
				<Button
					type={readFlag === 1 ? 'primary' : 'default'}
					ghost={readFlag === 1}
					size='small'
					onClick={() => setReadFlag(1)}
				>
					已完成
				</Button>
			</Space>
			<Table
				rowKey='id'
				dataSource={dataSource}
				pagination={{
					...pagination,
					total,
					showTotal: (total) => `共 ${total} 条`,
				}}
				onChange={(e) => changePage(e.current, e.pageSize)}
			>
				<Table.Column
					title='待办名称'
					key='noticeTitle'
					dataIndex='noticeTitle'
					width={300}
				/>
				<Table.Column
					title='待办内容'
					key='noticeContent'
					dataIndex='noticeContent'
				/>
				<Table.Column
					title='待办状态'
					key='readFlag'
					dataIndex='readFlag'
					width={160}
					render={(readFlag) => {
						return (
							<Tag
								color={
									readFlag === 1 ? 'processing' : 'warning'
								}
							>
								{readFlag === 1 ? '已完成' : '待处理'}
							</Tag>
						);
					}}
				/>
				<Table.Column
					title='创建时间'
					key='noticeTime'
					dataIndex='noticeTime'
					width={160}
				/>
				<Table.Column
					title='操作'
					key='option'
					dataIndex='option'
					align='center'
					width={70}
					render={(_, record) => {
						return (
							<>
								<Button
									type='link'
									size='small'
									onClick={() => toDetail(record)}
								>
									查看
								</Button>
							</>
						);
					}}
				/>
			</Table>
		</>
	);
};

const Index = () => {
	const { linkTo, searchParams } = useRouterLink();
	// 登录凭证
	const userInfo = useSelector((state) => {
		return state.user.userInfo;
	});

	const [tabsKey, setTabsKey] = useState(searchParams.get('tabIndex') || '1');
	const onTabsChange = (key) => {
		setTabsKey(key);
	};

	const [notifice, setNotifice] = useState([]);
	const getNotifice = () => {
		notificationPage({
			pageNum: 1,
			pageSize: 3,
			readFlag: 0,
		}).then((res) => {
			setNotifice(res.data.records || []);
		});
	};

	const depList = (list = []) => {
		const loop = (ov, arr = []) => {
			arr = [...arr, ov.name];
			if (ov.children && ov.children.length) {
				ov.children.forEach((item) => {
					loop(item, arr);
				});
			}
			return arr;
		};
		return list.map((ov) => {
			return loop(ov).join('/');
		});
	};

	useEffect(() => {
		// getNotifice();
	}, []);

	// 阅读消息
	const readNotifice = (item = {}) => {
		updateReadFlag({
			id: item.id || '',
			readFlag: 1,
		}).then(() => {
			getNotifice();
			Modal.confirm({
				title: '消息内容',
				content: <div>{item.noticeContent || ''}</div>,
				okText: '去处理',
				onOk() {
					item.projectId &&
						linkTo(
							`/bidmgt/projectManage/detail?id=${item.projectId}`
						);
				},
			});
		});
	};

	// 消息弹框
	const [modalFlag, setModalFlag] = useState('');
	return (
		<div className='flex-sub flex flex-direction-column padding-20'>
			<Row gutter={[20]}>
				<Col span={24}>
					<div className='bg-color-ffffff border-radius-4 overflow-hidden height-180 border-box padding-tb-20 padding-lr-40 flex justify-start align-center'>
						<div className='flex-shrink margin-right-40'>
							<Avatar
								size={106}
								icon={
									<img
										src={
											userInfo.wxAvatarUrl ||
											getImageSrc(
												'@/assets/images/Public/defaultAvatar.png'
											)
										}
										alt='arrow-down'
										className='width-106 height-106 border-radius-54'
									/>
								}
							/>
						</div>
						<Row gutter={[40, 16]} className='flex-sub '>
							<Col span={12}>
								<div className='color-86909c margin-bottom-12'>
									用户名
								</div>
								<Input
									disabled
									value={userInfo.userName || '-'}
								/>
							</Col>
							<Col span={12}>
								<div className='color-86909c margin-bottom-12'>
									所属部门
								</div>
								<Input
									disabled
									value={
										userInfo.deptList &&
										depList(userInfo.deptList).join(',')
									}
								/>
							</Col>
							<Col span={12}>
								<div className='color-86909c margin-bottom-12'>
									账号
								</div>
								<Input
									disabled
									value={userInfo.loginName || ''}
								/>
							</Col>
							<Col span={12}>
								<div className='color-86909c margin-bottom-12'>
									联系电话
								</div>
								<Input
									disabled
									value={
										`${userInfo.mobile || ''}`.replace(
											/(\d{3})\d*(\d{4})/,
											'$1****$2'
										) || '-'
									}
								/>
							</Col>
						</Row>
					</div>
				</Col>
				{false && (
					<Col span={10}>
						<div className='bg-color-ffffff border-radius-4 overflow-hidden height-180 border-box padding-20'>
							<div className='flex align-center justify-start height-22 margin-bottom-16'>
								<div className='font-size-16 font-weight-500'>
									待办事项
								</div>
								<div className='font-size-14 font-weight-400 color-86909c'>
									{/* （个） */}
								</div>
								<div
									className='flex-sub flex align-center justify-end font-size-12 color-165dff a'
									onClick={() =>
										setModalFlag(`${new Date().valueOf()}`)
									}
								>
									<div>查看全部</div>
								</div>
							</div>
							{notifice.slice(0, 3).map((ov) => {
								return (
									<div
										className='flex justify-start align-center margin-bottom-12 a'
										key={ov.id}
										onClick={() => readNotifice(ov)}
									>
										{ov.noticeType == '1' && (
											<Tag color='red'>预警</Tag>
										)}
										{ov.noticeType == '2' && (
											<Tag color='warning'>催办</Tag>
										)}
										<div className='flex-sub text-cut'>
											{ov.noticeTitle || ''}
										</div>
									</div>
								);
							})}
							{!notifice.length && (
								<div className='text-align-center line-height-88 color-aaaaaa'>
									暂无待办事项
								</div>
							)}
						</div>
					</Col>
				)}
			</Row>
			<div className='padding-lr-20 padding-tb-10 flex-sub bg-color-ffffff border-radius-4 margin-top-20'>
				<Tabs
					defaultActiveKey={tabsKey}
					items={[
						{
							key: '1',
							label: '我的项目',
						},
						{
							key: '2',
							label: '追踪项目动态',
						},
						{
							key: '3',
							label: '待办事项',
						},
					]}
					onChange={onTabsChange}
				/>
				{tabsKey == '1' && <Table1 />}
				{tabsKey == '2' && <Table2 />}
				{tabsKey == '3' && <Table3 />}
			</div>
			<Modal
				title='全部消息'
				open={!!modalFlag}
				onOk={() => setModalFlag('')}
				onCancel={() => setModalFlag('')}
				afterClose={() => getNotifice()}
			>
				{modalFlag && <TableNotifice key={modalFlag} />}
			</Modal>
		</div>
	);
};

export default Index;
