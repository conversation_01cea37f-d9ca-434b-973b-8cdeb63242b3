import { Col, Row, Button, Input, Steps, Pagination, Tag, Modal } from 'antd';
import { getImageSrc } from '@/assets/images/index';
import { useRouterLink } from '@/hook/useRouter';
import { useEffect, useState, useRef } from 'react';
import { claimProjectAssign, auditProject, updateReadFlag, checkAuditPermission } from '@/api/Bidmgt/PersonalCenter/index';
import './index.scss';
import ProjectManageDetail from '@/pages/Bidmgt/ProjectManage/Detail/index';
import { ReloadOutlined, StarOutlined, ExclamationCircleOutlined, CheckCircleFilled, ExclamationCircleFilled } from '@ant-design/icons';

const Index = () => {
	const { searchParams } = useRouterLink();
	const id = searchParams.get('id');

	// {/* 待办事项类型：1 项目认领 2 项目审核  3 商情审核 4 商情认领  */ }
	const toDoListType = searchParams.get('toDoListType');

	const reflash = useRef(null);
	const [detail, setDetail] = useState({});

	// 标记已经处理
	const updataNoticeRead = () => {
		return new Promise((resovel) => {
			if (noticeId) {
				updateReadFlag({
					id: noticeId,
					readFlag: 1,
				})
					.then(() => {
						resovel();
					})
					.catch(() => {
						resovel();
					});
			} else {
				resovel();
			}
		});
	};

	const auditDesc = useRef('');
	const { confirm } = Modal;
	// 审核 按钮
	const auditBtn = (auditStatus) => {
		if (auditStatus == 2) {
			let content = '';
			if (detail.projectAuditProcessLevel == 1) {
				content = '项目正式录入系统';
			}
			if (detail.projectAuditProcessLevel == 2) {
				if (detail.auditProcessLevel == 2) {
					content = '项目正式录入系统';
				} else {
					content = '项目将进入商务局审批流程';
				}
			}
			Modal.success({
				closable: true,
				okText: '确定',
				title: '通过审批',
				content: content,
				onOk() {
					submitAuidt();
				},
				onCancel() {},
			});
		} else {
			let content = '';
			if (detail.projectAuditProcessLevel == 1) {
				content = '项目将被退回属地单位';
			}
			if (detail.projectAuditProcessLevel == 2) {
				if (detail.auditProcessLevel == 2) {
					content = '项目将被退回属地单位';
				} else {
					content = '项目将被退回';
				}
			}
			Modal.error({
				closable: true,
				okText: '确定',
				title: '不通过',
				content: content,
				onOk() {
					submitAuidt();
				},
				onCancel() {},
			});
		}
	};
	// 操作审核结果 提交保存
	const submitAuidt = () => {
		auditProject({
			auditStatus: auditStatus,
			id: id,
			auditDesc: auditDesc.current,
		}).then((res) => {
			updataNoticeRead().then(() => {
				reflash.current && reflash.current();
			});
		});
	};
	const claimDesc = useRef('');
	// 认领 按钮
	const claimBtn = (claimStatus) => {
		if (claimStatus == 2) {
			let content = `将您所在单位设为改项目招商责任单位`;
			Modal.success({
				closable: true,
				okText: '确定',
				title: '认领成功',
				content: content,
				onOk() {
					claimProjectAssign();
				},
				onCancel() {},
			});
		} else {
			let content = '该项目将退回商务局';

			Modal.error({
				closable: true,
				okText: '确定',
				title: '退回该项目',
				content: content,
				onOk() {
					claimProjectAssign();
				},
				onCancel() {},
			});
		}
	};
	const claimProjectAssign = (claimStatus) => {
		claimProjectAssign({
			claimStatus: claimStatus,
			id: id,
			claimDesc: claimDesc.current,
		}).then((res) => {
			updataNoticeRead().then(() => {
				props.reflash && props.reflash();
			});
		});
	};

	const [showAuditBtn, setShowAuditBtn] = useState(false);

	useEffect(() => {
		checkAuditPermission({ id }).then((res) => {
			setShowAuditBtn(res.data || false);
		});
	}, []);
	return (
		<div className="">
			<ProjectManageDetail setDetail={setDetail} reflash={reflash} />
			{showAuditBtn && toDoListType == 2 && (
				<div className="flex justify-center align-center position-sticky bottom-0 left-0 right-0 bg-color-ffffff padding-20">
					<Button type="primary" className="width-160" onClick={() => auditBtn(2)}>
						通过
					</Button>
					<Button ype="primary" className="width-160 margin-left-20" danger onClick={() => auditBtn(1)}>
						不通过
					</Button>
				</div>
			)}
			{toDoListType == 1 && detail.investmentResponsibleClaimStatus === 0 && (
				<div className="flex justify-center align-center position-sticky bottom-0 left-0 right-0 bg-color-ffffff padding-20">
					<Button type="primary" className="width-160" onClick={() => claimBtn(2)}>
						接受认领
					</Button>
					<Button ype="primary" className="width-160 margin-left-20" danger onClick={() => claimBtn(1)}>
						拒绝认领
					</Button>
				</div>
			)}
		</div>
	);
};

export default Index;
