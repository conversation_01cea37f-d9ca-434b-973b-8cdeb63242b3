import { forwardRef, useImperativeHandle, memo, useEffect } from 'react';
import { DatePicker, Form, Input, message } from 'antd';
import { addFollowUpRecord } from '@/api/Bidmgt/ProjectManage/index';
import dayjs from 'dayjs';

const Index = forwardRef((props = {}, ref) => {
	const [form] = Form.useForm();
	// 提交
	const handleSubmit = () => {
		return new Promise((resolve) => {
			form.validateFields().then((values) => {
				const params = {
					id: props.id || '', // 跟进id
					projectId: props.projectId || '', // 项目id
					latestDevelopments: values.latestDevelopments || '', // 最新进展
					followDate: dayjs(values.followDate).format('YYYY-MM-DD'), // // 进展时间
				};
				addFollowUpRecord(params).then(() => {
					message.success('新增成功');
					form.resetFields();
					resolve();
				});
			});
		});
	};

	// 取消
	const handleCancel = () => {
		return new Promise((resolve) => {
			form.resetFields();
			resolve();
		});
	};

	useEffect(() => {
		if (props.latestDevelopments || props.followDate) {
			form.setFieldsValue({
				latestDevelopments: props.latestDevelopments || '',
				followDate: dayjs(props.followDate || new Date()),
			});
		}
	}, [props]);

	useImperativeHandle(ref, () => {
		return {
			onCancel: handleCancel,
			onOk: handleSubmit,
		};
	});

	return (
		<div className="">
			<Form
				form={form}
				labelCol={{
					style: { width: '120px' },
				}}
				labelAlign="right"
				initialValues={{
					followDate: dayjs(),
				}}
			>
				<Form.Item
					label="进展时间"
					name="followDate"
					rules={[
						{
							required: true,
							message: '请选择进展时间',
						},
					]}
				>
					<DatePicker className="width-100per" placeholder="请选择进展时间" />
				</Form.Item>
				<Form.Item
					label="最新进展"
					name="latestDevelopments"
					rules={[
						{
							required: true,
							message: '请输入最新进展',
						},
					]}
				>
					<Input.TextArea rows={5} placeholder="请输入最新进展" />
				</Form.Item>
			</Form>
		</div>
	);
});

export default memo(Index);
