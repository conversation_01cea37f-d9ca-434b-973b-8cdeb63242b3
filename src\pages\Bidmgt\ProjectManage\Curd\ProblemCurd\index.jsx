import { forwardRef, useImperativeHandle, memo, useEffect } from 'react';
import { Form, Input, Select, message, DatePicker } from 'antd';

import { detailProjectProblem, addProjectProblem, updateProjectProblem } from '@/api/Bidmgt/ProjectManage/index';
import dayjs from 'dayjs';

const Index = forwardRef((props = {}, ref) => {
	const [form] = Form.useForm();
	const { id, problemType } = props.currentData;
	// 提交
	const handleSubmit = () => {
		return new Promise((resolve) => {
			form.validateFields().then((values) => {
				const params = {
					id: id || '',
					projectId: props.projectId || '',
					problemType,
					...values,
					recordDate: dayjs(values.recordDate).format('YYYY-MM-DD'),
				};

				(id ? updateProjectProblem : addProjectProblem)(params).then(() => {
					message.success(id ? '编辑成功' : '新增成功');
					form.resetFields();
					resolve();
				});
			});
		});
	};

	// 取消
	const handleCancel = () => {
		return new Promise((resolve) => {
			form.resetFields();
			resolve();
		});
	};

	// 获取详情
	const getDetail = () => {
		detailProjectProblem({ id }).then((res) => {
			const resData = res.data || {};
			resData.recordDate = dayjs(resData.followDate || new Date());
			form.setFieldsValue({ ...resData });
		});
	};

	useEffect(() => {
		if (id) {
			getDetail();
		}
	}, [id]);

	useImperativeHandle(ref, () => {
		return {
			onCancel: handleCancel,
			onOk: handleSubmit,
		};
	});

	return (
		<div className="">
			<Form
				form={form}
				labelAlign="right"
				initialValues={{
					solveStatus: 0,
					coordinatedResolutionStatus: 0,
					recordDate: dayjs(),
				}}
			>
				<Form.Item
					label={problemType === 1 ? '存在问题' : '计划内容'}
					name="questionContent"
					rules={[
						{
							required: true,
							message: `请描述${problemType === 1 ? '存在问题' : '计划内容'}`,
						},
					]}
				>
					<Input.TextArea rows={5} placeholder={`请描述${problemType === 1 ? '存在问题' : '计划内容'}`} />
				</Form.Item>
				{problemType === 1 && (
					<Form.Item
						label="解决状态"
						name="solveStatus"
						rules={[
							{
								required: true,
								message: '请选择',
							},
						]}
					>
						<Select
							options={[
								{ label: '未解决', value: 0 },
								{ label: '已解决', value: 1 },
							]}
						/>
					</Form.Item>
				)}
				{problemType === 1 && (
					<Form.Item
						label="是否需要区领导协调解决"
						name="coordinatedResolutionStatus"
						rules={[
							{
								required: true,
								message: '请选择',
							},
						]}
					>
						<Select
							options={[
								{ label: '否', value: 0 },
								{ label: '是', value: 1 },
							]}
						/>
					</Form.Item>
				)}
				<Form.Item
					label="更新时间"
					name="recordDate"
					rules={[
						{
							required: true,
							message: '请选择更新时间',
						},
					]}
				>
					<DatePicker className="width-100per" placeholder="请选择更新时间" />
				</Form.Item>
			</Form>
		</div>
	);
});

export default memo(Index);
