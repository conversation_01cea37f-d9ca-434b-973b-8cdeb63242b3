import { forwardRef, useImperativeHandle, memo, useEffect } from 'react';
import { Form, Input, message } from 'antd';

import { useIsAdmin } from '@/hook/Bidmgt/useIsAdmin';
import dayjs from 'dayjs';
import { getInvestmentResponsibleUser, saveInvestmentResponsibleUser, updateInvestmentResponsibleUser } from '@/api/Bidmgt/ConfigCenter/index';

const Index = forwardRef((props = {}, ref) => {
	const { deptIds } = useIsAdmin();
	const [form] = Form.useForm();
	const id = props.id || '';
	// 提交
	const handleSubmit = () => {
		return new Promise((resolve) => {
			form.validateFields().then((values) => {
				(id ? updateInvestmentResponsibleUser : saveInvestmentResponsibleUser)({ ...values, deptId: deptIds[0] }).then(() => {
					message.success(id ? '编辑成功' : '新增成功');
					form.resetFields();
					resolve();
				});
			});
		});
	};

	// 取消
	const handleCancel = () => {
		return new Promise((resolve) => {
			form.resetFields();
			resolve();
		});
	};

	// 获取详情
	const getDetail = () => {
		getInvestmentResponsibleUser({ id }).then((res) => {
			const resData = res.data || {};
			form.setFieldsValue({ ...resData });
		});
	};

	useEffect(() => {
		if (id) {
			getDetail();
		}
	}, [id]);

	useImperativeHandle(ref, () => {
		return {
			onCancel: handleCancel,
			onOk: handleSubmit,
		};
	});

	return (
		<div className="">
			<Form
				form={form}
				labelAlign="right"
				labelCol={{ span: 4 }}
				initialValues={{
					id,
					systemUserId: '',
				}}
			>
				<Form.Item name="id" hidden>
					<Input placeholder="隐藏" />
				</Form.Item>
				<Form.Item name="systemUserId" hidden>
					<Input placeholder="隐藏" />
				</Form.Item>
				<Form.Item
					label="姓名"
					name="userName"
					rules={[
						{
							required: true,
							message: '请输入姓名',
						},
					]}
				>
					<Input placeholder="请输入姓名" />
				</Form.Item>
				<Form.Item
					label="联系电话"
					name="phone"
					rules={[
						{
							required: true,
							message: '请输入联系电话',
						},
					]}
				>
					<Input placeholder="请输入联系电话" />
				</Form.Item>
			</Form>
		</div>
	);
});

export default memo(Index);
