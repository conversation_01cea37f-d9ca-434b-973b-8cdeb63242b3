import { useEffect, useState, useRef } from 'react';
import { Select, InputNumber, Tabs, Form, Checkbox, Radio, Button, Input, DatePicker, Switch, message, Space, Modal, Empty } from 'antd';
import {
	CaretDownOutlined,
	DeleteOutlined,
	CloseOutlined,
	SearchOutlined,
	CalendarOutlined,
	ClockCircleOutlined,
	PlusCircleOutlined,
} from '@ant-design/icons';
import Breadcrumb from '@/components/Breadcrumb';
import ModalForm from '@/components/ModalForm';
import { yearOptions } from '@/components/Bidmgt/FilterComp';
import FollowCurd from './FollowCurd/index';
import ProblemCurd from './ProblemCurd/index';
import UserCurd from './UserCurd/index';

import { useRouterLink } from '@/hook/useRouter';
import { useIsAdmin } from '@/hook/Bidmgt/useIsAdmin';

import { getCategoryValueList, getInvestmentResponsibleDept } from '@/utils/bigmt';

import dayjs from 'dayjs';

import {
	addProject,
	detailProject,
	updateProject,
	listFollowUpRecord,
	delFollowUpRecord,
	listProjectProblem,
	batchDelProjectProblem,
} from '@/api/Bidmgt/ProjectManage/index';
import { listInvestmentResponsibleUser, batchDelInvestmentResponsibleUser } from '@/api/Bidmgt/ConfigCenter/index';

import './index.scss';

// 基础信息
const BaseInfoForm = (props = {}) => {
	const { isAdmin, deptIds } = useIsAdmin();
	const openingStatus = Form.useWatch('openingStatus', props.form);

	const negotiationStatus = Form.useWatch('negotiationStatus', props.form);
	const reviewMeetingStatus = Form.useWatch('reviewMeetingStatus', props.form);
	const signStatus = Form.useWatch('signStatus', props.form);

	const negotiationDate = Form.useWatch('negotiationDate', props.form);
	const reviewMeetingDate = Form.useWatch('reviewMeetingDate', props.form);
	const signDate = Form.useWatch('signDate', props.form);

	const projectStageId = Form.useWatch('projectStageId', props.form);

	const [projectStageOptions, setProjectStageOptions] = useState([]);
	const [projectTypeOptions, setProjectTypeOptions] = useState([]);
	const [activityOptions, setActivityOptions] = useState([]);
	const [investTypeOptions, setInvestTypeOptions] = useState([]);
	const [industrialTrackOptions, setIndustrialTrackOptions] = useState([]);
	const [industryOptions, setIndustryOptions] = useState([]);

	const [hidestageCodeList, setHidestageCodeList] = useState([]);

	// 获取选项数据
	const getOptionsData = () => {
		getCategoryValueList('project_stage').then((listData) => {
			setProjectStageOptions(listData);
		});
		getCategoryValueList('project_type').then((listData) => {
			setProjectTypeOptions(listData);
		});
		getCategoryValueList('activity_type').then((listData) => {
			setActivityOptions(listData);
		});
		getCategoryValueList('invest_type').then((listData) => {
			setInvestTypeOptions(listData);
		});
		getCategoryValueList('industrial_track').then((listData) => {
			setIndustrialTrackOptions(listData);
		});
		getCategoryValueList('industry_type').then((listData) => {
			setIndustryOptions(listData);
		});
	};

	// 是否重点
	useEffect(() => {
		props.form.setFieldsValue({
			keyProjectStatus: openingStatus === 0 || openingStatus === 1 ? 1 : 0,
		});
	}, [openingStatus]);

	// 关键项目阶段 negotiationStatus 与 reviewMeetingStatus signStatus 互斥
	useEffect(() => {
		if (negotiationStatus === 1) {
			props.form.setFieldsValue({
				reviewMeetingStatus: 0,
				signStatus: 0,
			});
		}
	}, [negotiationStatus]);

	useEffect(() => {
		if (reviewMeetingStatus === 1 || signStatus === 1) {
			props.form.setFieldsValue({
				negotiationStatus: 0,
			});
		}
	}, [reviewMeetingStatus, signStatus]);

	// 关键项目阶段  联动 项目实际所处阶段
	useEffect(() => {
		const codeList = [
			{
				value: negotiationStatus,
				code: ['area_promote', 'government_promote', 'signing', 'register', 'work_begins'],
			},
			{
				value: reviewMeetingStatus,
				code: ['negotiation'],
			},
			{
				value: signStatus,
				code: ['negotiation', 'area_promote', 'government_promote', 'signing'],
			},
		]
			.filter((ov) => ov.value === 1)
			.map((ov) => ov.code);

		const hideCodeList = codeList[codeList.length - 1] || [];
		setHidestageCodeList(hideCodeList);

		if (projectStageId && projectStageOptions.length > 0) {
			const currentCode = projectStageOptions.find((ov) => ov.value === projectStageId).code;

			if (hideCodeList.includes(currentCode)) {
				props.form.setFieldsValue({
					projectStageId: '',
				});
			}
		}
	}, [negotiationStatus, reviewMeetingStatus, signStatus]);

	useEffect(() => {
		getOptionsData();
		// 非管理者新增默认责任单位为自身部门
		if (props.id === '' && !isAdmin) {
			props.form.setFieldsValue({
				investmentResponsibleIds: deptIds,
			});
		}
	}, []);

	return (
		<>
			<Form.Item
				name="projectName"
				label="项目名称"
				rules={[
					{
						required: true,
						message: '请输入项目名称',
					},
				]}
			>
				<Input placeholder="请输入项目名称" />
			</Form.Item>
			<Form.Item
				name="investmentEnterprisesName"
				label="投资企业"
				rules={[
					{
						required: true,
						message: '请输入投资企业',
					},
				]}
			>
				<Input placeholder="请输入投资企业" />
			</Form.Item>
			<Form.Item
				label="关键项目阶段"
				name="temp"
				required
				rules={[
					{
						validator: () => {
							if (negotiationStatus === 0 && reviewMeetingStatus === 0 && signStatus === 0) {
								return Promise.reject('请选择关键项目阶段');
							}

							if (negotiationStatus === 1 && negotiationDate === undefined) {
								return Promise.reject('请选择洽谈时间');
							}
							if (reviewMeetingStatus === 1 && reviewMeetingDate === undefined) {
								return Promise.reject('请选择过会时间');
							}
							if (signStatus === 1 && signDate === undefined) {
								return Promise.reject('请选择签约时间');
							}
							return Promise.resolve();
						},
					},
				]}
			>
				<Space direction="vertical">
					<Form.Item noStyle name="negotiationStatus">
						<CustomCheckbox form={props.form}>洽谈中</CustomCheckbox>
					</Form.Item>

					<Form.Item noStyle name="reviewMeetingStatus">
						<CustomCheckbox form={props.form}>已过会</CustomCheckbox>
					</Form.Item>
					<Form.Item noStyle name="signStatus">
						<CustomCheckbox form={props.form}>已签约</CustomCheckbox>
					</Form.Item>
				</Space>
			</Form.Item>
			<Form.Item
				name="projectStageId"
				label="项目实际所处阶段"
				rules={[
					{
						required: true,
						message: '请选择项目阶段',
					},
				]}
			>
				<Radio.Group
					className="line-height-32"
					options={projectStageOptions.filter((ov) => !hidestageCodeList.includes(ov.code))}
				></Radio.Group>
			</Form.Item>
			<Form.Item name="projectTypeId" label="项目类型" rules={[{ required: true, message: '请选择项目类型' }]}>
				<Select
					allowClear
					options={projectTypeOptions}
					placeholder="请选择"
					filterOption={(input, option) => (option?.label ?? '').includes(input)}
				/>
			</Form.Item>
			<Form.Item name="keyProjectStatus" hidden label="是否重点项目">
				<Input placeholder="隐藏不显示: openingStatus 有值时为 1" />
			</Form.Item>
			<Form.Item name="openingStatus" label="关联至重点已开工">
				<CustomSwitch checkValue={1} />
			</Form.Item>
			<Form.Item name="openingStatus" label="关联至重点未开工">
				<CustomSwitch checkValue={0} />
			</Form.Item>
			<Form.Item name="activityIds" label="双招双引活动项目">
				<Select
					allowClear
					options={activityOptions}
					mode="multiple"
					placeholder="请选择"
					filterOption={(input, option) => (option?.label ?? '').includes(input)}
				/>
			</Form.Item>
			<Form.Item
				label="投资规模"
				name="investmentScale"
				rules={[
					{
						required: true,
						type: 'number',
						message: '请输入投资规模',
					},
				]}
			>
				<InputNumber className="input-number-box" min={0.01} placeholder="请输入投资规模" controls={false} suffix="亿" />
			</Form.Item>
			<Form.Item name="investTypeId" label="内外资" rules={[{ required: true, message: '请选择内外资' }]}>
				<Select allowClear options={investTypeOptions} placeholder="请选择" />
			</Form.Item>
			<Form.Item
				name="totalAreaUsed"
				label="拟用地空间"
				rules={[
					{
						required: true,
						message: '请输入拟用地空间',
					},
				]}
			>
				<Input placeholder="请输入拟用地空间" />
			</Form.Item>
			<Form.Item
				name="industryList"
				label="产业类型"
				rules={[
					{
						required: true,
						type: 'array',
						message: '请选择产业类型',
					},
				]}
			>
				<IndustrySelect options={industryOptions} />
			</Form.Item>
			<Form.Item name="industrialTrackId" label="产业赛道" rules={[{ required: true, message: '请选择产业赛道' }]}>
				<Select allowClear options={industrialTrackOptions} placeholder="请选择" />
			</Form.Item>
			<Form.Item name="storageStatus" label="是否入库">
				<CustomSwitch2 />
			</Form.Item>
			<Form.Item name="leaderInspectStatus" label="是否领导考察">
				<CustomSwitch2 />
			</Form.Item>
			<Form.Item name="introduction" label="项目简介" rules={[{ required: true, message: '请输入项目简介' }]}>
				<Input.TextArea autoSize={{ minRows: 4 }} placeholder="请输入项目简介" />
			</Form.Item>
			{isAdmin && (
				<Form.Item name="rankingNum" label="项目排序" rules={[]}>
					<InputNumber className="input-number-box" min={1} placeholder="请输入排序序号" />
				</Form.Item>
			)}
		</>
	);
};

// 相关责任人
const PersonsForm = (props = {}) => {
	const { isAdmin, deptIds } = useIsAdmin();
	const investmentResponsibleIds = Form.useWatch('investmentResponsibleIds', props.form);
	const [deptListOptions, setDeptListOptions] = useState([]);
	const [modalInfo, setModalInfo] = useState({
		keyName: '',
		open: false,
		title: '',
		deptIds: [],
		checkedIds: [],
	});

	// 选中人员
	const onCheckedList = (checkedList) => {
		props.form.setFieldsValue({
			[modalInfo.keyName]: checkedList.map(({ userName, id, phone }) => ({
				userName,
				id,
				phone,
			})),
		});
		cloneModal();
	};

	// 选择人员 打开弹窗
	const onCheckedUser = (e) => {
		// deptIds 根据选择的责任部门进行排序
		// 区商务局项目跟进人 优先显示区商务局
		setModalInfo({
			...e,
			checkedIds: props.form.getFieldValue(e.keyName).map((ov) => ov.id),
			deptIds: e.keyName === 'districtBusinessTrackerList' ? deptIds : investmentResponsibleIds,
		});
	};

	// 关闭弹窗
	const cloneModal = () => {
		setModalInfo({
			...modalInfo,
			open: false,
		});
	};

	useEffect(() => {
		getInvestmentResponsibleDept().then((listData) => {
			setDeptListOptions(listData);
		});

		// 非管理者新增默认责任单位为自身部门
		if (props.id === '' && !isAdmin) {
			props.form.setFieldsValue({
				investmentResponsibleIds: deptIds,
			});
		}
	}, []);

	return (
		<>
			<div className="flex align-center justify-start width-80per margin-0-auto margin-bottom-24 padding-tb-12 border-solid-bottom-e5e6e8">
				<div className="width-6 height-16 border-radius-4 bg-color-165dff"></div>
				<div className="font-bold font-size-20 line-height-28 margin-left-8">责任单位</div>
			</div>
			<Form.Item label="招商责任单位">
				<div className="width-250">
					<Form.Item name="investmentResponsibleIds" noStyle>
						<Select
							disabled={!isAdmin}
							allowClear
							options={deptListOptions}
							mode="multiple"
							placeholder="请选择"
							filterOption={(input, option) => (option?.label ?? '').includes(input)}
						/>
					</Form.Item>
				</div>
			</Form.Item>
			<div className="flex align-center justify-start width-80per margin-0-auto margin-bottom-24 padding-tb-12 border-solid-bottom-e5e6e8">
				<div className="width-6 height-16 border-radius-4 bg-color-165dff"></div>
				<div className="font-bold font-size-20 line-height-28 margin-left-8">责任人</div>
			</div>
			<PersonsFormItem
				label="招商专员"
				name="projectCommissionerList"
				onCheckedUser={onCheckedUser}
				disabled={(investmentResponsibleIds || []).length === 0 || isAdmin}
			/>
			<PersonsFormItem
				label="项目经办人"
				name="projectHandledByList"
				onCheckedUser={onCheckedUser}
				disabled={(investmentResponsibleIds || []).length === 0 || isAdmin}
			/>
			<PersonsFormItem label="区商务局项目跟进人" name="districtBusinessTrackerList" onCheckedUser={onCheckedUser} disabled={!isAdmin} />
			<PresonsModal {...modalInfo} onCancel={cloneModal} onChange={onCheckedList} />
		</>
	);
};

// 进展情况
const FollowForm = (props = {}) => {
	const projectId = props.id;
	const ModalFormRef = useRef();
	const [currentData, setCurrentData] = useState({});
	const [followList, setFollowList] = useState([]);

	// 获取进展数据
	const getFollowData = () => {
		listFollowUpRecord({ projectId }).then((res) => {
			setFollowList(res.data || []);
		});
	};

	// 填写跟进记录
	const handleOpenForm = (id = '', latestDevelopments = '', followDate = '') => {
		setCurrentData({
			id,
			latestDevelopments,
			followDate,
		});
		ModalFormRef.current.setOpen(true);
		ModalFormRef.current.setTitle('进展内容');
	};

	// 删除跟进
	const delFollow = (id) => {
		Modal.confirm({
			title: '确定删除该进展记录？',
			onOk() {
				delFollowUpRecord({ ids: [id] }).then(() => {
					getFollowData();
					ModalFormRef.current.setOpen(false);
					message.success('删除成功');
				});
			},
		});
	};

	useEffect(() => {
		getFollowData();
	}, []);

	return (
		<>
			<div className="margin-auto" style={{ maxWidth: '754px' }}>
				<Button className="width-100per" type="primary" ghost icon={<PlusCircleOutlined />} onClick={() => handleOpenForm()}>
					添加进展
				</Button>
				<div className="margin-top-12 padding-16 border-radius-2 bg-color-f7f9fc">
					<Space direction="vertical" size={16} className="width-100per">
						{followList.map((ov) => (
							<div key={ov.id} className="flex align-start width-100per line-height-22">
								<div className="flex align-center padding-tb-4 padding-lr-12 border-radius-2 border-solid-e5e6eb bg-color-ffffff">
									<ClockCircleOutlined className="font-size-16 color-c9cdd4" />
									<div className="margin-left-12">{dayjs(ov.followDate || new Date()).format('YYYY年MM月DD日')}</div>
								</div>
								<div className="flex-sub margin-lr-12 padding-tb-4 padding-lr-12 border-radius-2 border-solid-e5e6eb bg-color-ffffff">
									{ov.latestDevelopments}
								</div>
								<div
									className="a padding-tb-4 color-165dff"
									onClick={() => handleOpenForm(ov.id, ov.latestDevelopments, ov.followDate)}
								>
									编辑
								</div>
							</div>
						))}
					</Space>
					{followList.length === 0 && (
						<div className="padding-tb-20">
							<Empty image={Empty.PRESENTED_IMAGE_SIMPLE} description="暂无进展" />
						</div>
					)}
				</div>
			</div>
			{/* 编辑/新建 弹窗 开始 */}
			<ModalForm
				ref={ModalFormRef}
				onOk={() => {
					setCurrentData({});
					getFollowData();
				}}
				footerExtra={
					currentData.id ? (
						<div className="a padding-tb-4 color-165dff" onClick={() => delFollow(currentData.id)}>
							删除进展
						</div>
					) : null
				}
				FormComp={(props) => (
					<FollowCurd
						ref={props.FormCompRef}
						id={currentData.id || ''}
						projectId={projectId}
						latestDevelopments={currentData.latestDevelopments || ''}
						followDate={currentData.followDate || ''}
					/>
				)}
			/>
			{/* 编辑/新建 弹窗 结束 */}
		</>
	);
};

// 问题及计划
// problemType 1 问题 2 计划
const ProblemForm = (props = {}) => {
	const projectId = props.id;
	const ModalFormRef = useRef();
	const [currentData, setCurrentData] = useState({
		id: '',
		problemType: 1,
	});
	const [problemList, setProblemList] = useState([]);

	// 获取进展数据
	const getProblemData = () => {
		listProjectProblem({ projectId }).then((res) => {
			setProblemList(res.data || []);
		});
	};

	// 填写跟进记录
	const handleOpenForm = (id = '', problemType = 1) => {
		setCurrentData({ id, problemType });
		ModalFormRef.current.setOpen(true);
		ModalFormRef.current.setTitle(`${id ? '编辑' : '添加'}${problemType === 1 ? '问题' : '计划'}`);
	};

	// 删除跟进
	const delProblem = () => {
		Modal.confirm({
			title: '确定删除该问题及计划？',
			onOk() {
				batchDelProjectProblem({ ids: [currentData.id] }).then(() => {
					getProblemData();
					ModalFormRef.current.setOpen(false);
					message.success('删除成功');
				});
			},
		});
	};

	useEffect(() => {
		getProblemData();
	}, []);

	return (
		<>
			<div className="margin-auto" style={{ maxWidth: '754px' }}>
				<div className="flex width-100per">
					<Button className="flex-sub" type="primary" ghost icon={<PlusCircleOutlined />} onClick={() => handleOpenForm()}>
						添加问题
					</Button>
					<div className="padding-lr-8"></div>
					<Button className="flex-sub" type="primary" ghost icon={<PlusCircleOutlined />} onClick={() => handleOpenForm('', 2)}>
						添加计划
					</Button>
				</div>
				<div className="margin-top-12 padding-16 border-radius-2 bg-color-f7f9fc">
					<Space direction="vertical" size={16} className="width-100per">
						{problemList.map((ov) => (
							<div key={ov.id} className="flex align-start width-100per line-height-22">
								<div className="flex align-center padding-tb-4 padding-lr-12 border-radius-2 border-solid-e5e6eb bg-color-ffffff">
									<ClockCircleOutlined className="font-size-16 color-c9cdd4" />
									<div className="margin-left-12">{dayjs(ov.followDate || new Date()).format('YYYY年MM月DD日')}</div>
								</div>
								<div className="flex-sub margin-lr-12 padding-tb-4 padding-lr-12 border-radius-2 border-solid-e5e6eb bg-color-ffffff">
									{ov.questionContent}
								</div>
								<div className="a padding-tb-4 color-165dff" onClick={() => handleOpenForm(ov.id, ov.problemType)}>
									编辑
								</div>
							</div>
						))}
					</Space>
					{problemList.length === 0 && (
						<div className="padding-tb-20">
							<Empty image={Empty.PRESENTED_IMAGE_SIMPLE} description="暂无问题及计划" />
						</div>
					)}
				</div>
			</div>
			{/* 编辑/新建 弹窗 开始 */}
			<ModalForm
				ref={ModalFormRef}
				onOk={() => {
					setCurrentData({
						id: '',
						problemType: 1,
					});
					getFollowData();
				}}
				footerExtra={
					currentData.id ? (
						<div className="a padding-tb-4 color-165dff" onClick={() => delProblem()}>
							删除
							{currentData.problemType === 1 ? '问题' : '计划'}
						</div>
					) : null
				}
				FormComp={(props) => <ProblemCurd ref={props.FormCompRef} currentData={currentData} projectId={projectId} />}
			/>
			{/* 编辑/新建 弹窗 结束 */}
		</>
	);
};

// 自定义checkBox 0 未选中 1选中
const CustomCheckbox = (props = {}) => {
	return (
		<Space>
			<Checkbox
				checked={props.value === 1}
				onChange={(e) => {
					props.onChange(e.target.checked ? 1 : 0);
				}}
			>
				{props.children}
			</Checkbox>
			<div className="width-160">
				<Form.Item noStyle name={(props.id || '').replace('Status', 'Date')}>
					<Select
						allowClear
						options={yearOptions}
						placeholder={`请选择`}
						suffixIcon={<CalendarOutlined />}
						onChange={() => {
							props.form.validateFields(['temp']);
						}}
					/>
				</Form.Item>
			</div>
		</Space>
	);
};

// 自定义 DatePicker yyyy-MM-dd
const CustomDatePicker = (props = {}) => {
	const [value, setValue] = useState(null);

	useEffect(() => {
		if (props.value) {
			setValue(dayjs(props.value));
		} else {
			setValue(null);
		}
	}, [props.value]);
	return (
		<DatePicker
			disabled={props.disabled}
			className="width-180 bg-color-f2f3f5"
			value={value}
			onChange={(e) => {
				props.onChange(e ? dayjs(e).format('YYYY-MM-DD') : null);
			}}
		/>
	);
};

// 自定义 Switch 1 0 互斥
const CustomSwitch = (props = {}) => {
	return (
		<Switch
			checked={props.value === props.checkValue}
			onChange={(e) => {
				if (e) {
					props.onChange(props.checkValue);
				} else {
					props.onChange(null);
				}
			}}
		/>
	);
};

// 自定义 Switch
const CustomSwitch2 = (props = {}) => {
	return (
		<Switch
			checked={props.value === 1}
			onChange={(e) => {
				props.onChange(e ? 1 : 0);
			}}
		/>
	);
};

// 行业选择
const IndustrySelect = (props = {}) => {
	return (
		<Select
			value={props.value[0] || null}
			allowClear
			options={props.options}
			placeholder="请选择"
			onChange={(e) => {
				if (e) {
					props.onChange([e]);
				} else {
					props.onChange([]);
				}
			}}
		/>
	);
};

// 责任人Item
const PersonsFormItem = (props = {}) => {
	return (
		<Form.Item label={props.label}>
			<Form.List name={props.name}>
				{(fields, { remove }) => {
					return (
						<>
							<Space direction="vertical" size={16}>
								{fields.map((field) => (
									<Form.Item {...field} noStyle>
										<PersonsItem {...props} onDel={() => remove(field.name)} />
									</Form.Item>
								))}
								{fields.length === 0 && <PersonsItem {...props} />}
							</Space>
						</>
					);
				}}
			</Form.List>
		</Form.Item>
	);
};

// 人员Item
const PersonsItem = (props = {}) => {
	const [id, setId] = useState('');
	const [userName, setUserName] = useState('');
	const [phone, setPhone] = useState('');

	useEffect(() => {
		const { id, userName, phone } = props.value || {};
		setId(id || '');
		setUserName(userName || '');
		setPhone(phone || '');
	}, [props.value]);

	return (
		<Space>
			<div
				className="width-250"
				onClick={() =>
					!props.disabled &&
					props.onCheckedUser({
						keyName: props.name,
						open: true,
						title: props.label,
					})
				}
			>
				<Input value={userName} readOnly disabled={props.disabled} placeholder={`请选择${props.label}`} suffix={<CaretDownOutlined />} />
			</div>
			<div className="width-250">
				<Input
					value={phone}
					placeholder={`请输入联系方式`}
					disabled={props.disabled || userName === ''}
					onChange={(e) => {
						props.onChange({
							...props.value,
							phone: e.target.value,
						});
					}}
				/>
			</div>
			{!props.disabled && props.onDel && <DeleteOutlined className="font-size-16 color-165dff a" onClick={props.onDel} />}
		</Space>
	);
};

// 选择人员弹窗
const PresonsModal = (props = {}) => {
	const ModalFormRef = useRef();
	const [currentId, setCurrentId] = useState('');

	const { deptIds } = useIsAdmin();
	// 所有列表
	const [userList, setUserList] = useState([]);
	// 显示列表
	const [showUserList, setShowUserList] = useState([]);

	const [keyword, setKeyword] = useState('');
	const [checkedIds, setCheckedIds] = useState(props.checkedIds);
	const [checkedList, setCheckedList] = useState([]);

	// 获取成员数据
	const getUserData = () => {
		listInvestmentResponsibleUser({ deptIds }, { isCache: true }).then((res) => {
			setUserList(res.data || []);
		});
	};

	// 选择
	const checkedItem = (id = '') => {
		const index = checkedIds.findIndex((ov) => ov === id);

		if (index > -1) {
			checkedIds.splice(index, 1);
		} else {
			checkedIds.push(id);
		}
		setCheckedIds([...checkedIds]);
	};

	// 高亮关键词
	const highlightKeywords = (text) => {
		if (keyword) {
			const regex = new RegExp(`(${keyword})`, 'gi');
			return text.split(regex).map((part, i) =>
				regex.test(part) ? (
					<span key={i} className="color-cd4c57">
						{part}
					</span>
				) : (
					part
				)
			);
		} else {
			return text;
		}
	};

	// 选中数据
	useEffect(() => {
		setCheckedList(userList.filter((ov) => checkedIds.includes(ov.id)));
	}, [userList, checkedIds]);

	// 回显选中
	useEffect(() => {
		setCheckedIds(props.checkedIds);
	}, [props.checkedIds]);

	// 删除成员
	const delUser = (id) => {
		Modal.confirm({
			title: '确定删除该成员？',
			onOk() {
				batchDelInvestmentResponsibleUser({ ids: [id] }).then(() => {
					getUserData();
					ModalFormRef.current.setOpen(false);
					message.success('删除成功');
				});
			},
		});
	};

	useEffect(() => {
		setShowUserList(userList.filter((ov) => keyword.trim() === '' || keyword.trim() === null || ov.userName.includes(keyword.trim())));
	}, [userList, keyword]);

	useEffect(() => {
		getUserData();
	}, []);

	return (
		<>
			<Modal
				open={props.open}
				title={`设置${props.title}`}
				centered
				width="800px"
				classNames={{
					body: 'presons-modal-box flex flex-direction-column scrollbar',
				}}
				styles={{
					content: {
						padding: 0,
					},
					header: {
						padding: '20px 24px 8px',
						borderBottom: 'solid 1px #e5e6eb',
					},
					body: {
						padding: '16px 24px',
						height: '60vh',
					},
					footer: {
						padding: '8px 24px 20px',
						borderTop: 'solid 1px #e5e6eb',
					},
				}}
				onCancel={() => props.onCancel()}
				onOk={() => {
					props.onChange(checkedList);
				}}
				footer={(Btns) => {
					return (
						<div className="flex align-center justify-between">
							<Button
								type="primary"
								ghost
								onClick={() => {
									ModalFormRef.current.setOpen(true);
									ModalFormRef.current.setTitle('录入成员');
								}}
							>
								手动录入
							</Button>
							<Space size={8}>{Btns}</Space>
						</div>
					);
				}}
			>
				<div className="flex flex-sub">
					<div className="flex flex-direction-column width-220">
						<div>
							<Input
								value={keyword}
								allowClear
								placeholder="请输入名字搜索"
								suffix={keyword ? null : <SearchOutlined />}
								onChange={(e) => {
									setKeyword(e.target.value);
								}}
							/>
						</div>
						<div className="flex-sub position-relative margin-top-16">
							<div className="position-absolute inset-0 margin-auto overflowY-auto overflowX-hidden scrollbar border-solid-e5e6e8 border-radius-4">
								{showUserList.map((ov) => {
									return (
										<div
											key={ov.id}
											className="a flex align-center justify-between padding-lr-6 padding-tb-4"
											onClick={() => checkedItem(ov.id)}
										>
											<div className={`padding-lr-4 ${checkedIds.includes(ov.id) ? 'color-165dff' : ''}`}>
												{highlightKeywords(ov.userName)}
											</div>
											<Checkbox checked={checkedIds.includes(ov.id)} />
										</div>
									);
								})}

								{showUserList.length === 0 && (
									<div className="position-absolute inset-0 flex align-center justify-center margin-auto font-size-14 color-86909c">
										暂无数据
									</div>
								)}
							</div>
						</div>
					</div>
					<div className="flex-sub position-relative margin-left-30">
						<div className="position-absolute inset-0 margin-auto overflowY-auto overflowX-hidden scrollbar">
							<Space size={12} className="margin-bottom-12 line-height-24 font-size-16 font-weight-500">
								<div>已选</div>
								<div>{props.title}</div>
								<div className="color-165dff">（{checkedList.length}人）</div>
							</Space>
							{checkedList.map((ov) => {
								return (
									<div
										key={ov.id}
										className="flex align-center margin-bottom-16 padding-lr-12 width-300 height-32 border-radius-2 border-solid-e5e6eb"
									>
										<div className="font-weight-500">{ov.userName}</div>
										<div className="flex-sub margin-lr-8 text-cut color-86909c">{ov.deptName}</div>
										<CloseOutlined
											className="font-size-12"
											onClick={() => {
												checkedItem(ov.id);
											}}
										/>
									</div>
								);
							})}
						</div>
					</div>
				</div>
			</Modal>

			<ModalForm
				ref={ModalFormRef}
				onOk={() => {
					setCurrentId('');
					getUserData();
				}}
				footerExtra={
					currentId ? (
						<div className="a padding-tb-4 color-165dff" onClick={() => delUser(currentId)}>
							删除成员
						</div>
					) : null
				}
				FormComp={(props) => <UserCurd ref={props.FormCompRef} id={currentId} />}
			/>
		</>
	);
};

const Index = () => {
	const { isAdmin, deptIds } = useIsAdmin();
	const { linkTo, searchParams, setSearchParams } = useRouterLink();
	const [id, setId] = useState(searchParams.get('id') || '');
	const [tabIndex, setTabIndex] = useState(searchParams.get('tabIndex') - 0 || 1);

	const [form] = Form.useForm();

	// 获取详情
	const getDetailData = () => {
		detailProject({ id }).then((res) => {
			const resData = res.data || {};
			const investmentResponsibleIds = resData.investmentResponsibleIds || [];
			// 判断是否有权限编辑
			// 管理人员 || 责任单位归属部门
			if (isAdmin || investmentResponsibleIds.some((ov) => deptIds.includes(ov))) {
				// 投资规模
				resData.investmentScale = resData.investmentScale - 0 || null;
				// 是否关联重点项目 当keyProjectStatus为0时， openingStatus为空
				resData.openingStatus = resData.keyProjectStatus === 0 ? null : resData.openingStatus;
				form.setFieldsValue(resData);
			} else {
				message.error('您没有权限编辑');
				linkTo(-1);
			}
		});
	};

	// 提交
	const submit = (activeKey = '', next = false) => {
		form.validateFields()
			.then((values) => {
				(id ? updateProject : addProject)(values).then((res) => {
					if (id === '') {
						setId(res.data);
					}
					if (activeKey) {
						setTabIndex(activeKey);
					} else {
						message.success('提交成功');
						if (next) {
							if (isAdmin) {
								linkTo('/bidmgt/projectManage/list');
							} else {
								linkTo('/bidmgt/projectManage/pendingList');
							}
						}
					}
				});
			})
			.catch((error) => {
				console.log('🚀 ~ submit ~ error:', error);
				form.scrollToField(error.errorFields[0].name, {
					block: 'center',
				});
			});
	};

	useEffect(() => {
		if (id) {
			getDetailData();
		}
	}, [id]);

	useEffect(() => {
		setSearchParams(
			{
				id,
				tabIndex,
			},
			{ replace: true }
		);
	}, [id, tabIndex]);

	return (
		<div className="project-curd-box full-page-width position-absolute inset-0 flex flex-direction-column margin-auto">
			<div className="flex-sub flex flex-direction-column margin-top-16 padding-lr-20 padding-bottom-20">
				{/* 面包屑 开始 */}
				<Breadcrumb
					icon="icon-projectManage"
					list={[
						{
							name: '项目管理',
							link: '/bidmgt/projectManage/list',
						},
						{
							name: '项目列表',
							link: '/bidmgt/projectManage/list',
						},
					]}
					name={id ? '编辑项目' : '新建项目'}
				/>
				{/* 面包屑 结束 */}
				<div className="flex-sub padding-20 border-radius-4 bg-color-ffffff">
					<Form
						form={form}
						labelCol={{ span: 6 }}
						wrapperCol={{ span: 14 }}
						scrollToFirstError={true}
						initialValues={{
							id,
							negotiationStatus: 0,
							reviewMeetingStatus: 0,
							signStatus: 0,
							keyProjectStatus: 0,
							storageStatus: 0,
							leaderInspectStatus: 0,
							activityIds: [],
							industryList: [],
							investmentResponsibleIds: [],
							projectCommissionerList: [],
							projectHandledByList: [],
							districtBusinessTrackerList: [],
							temp: undefined,
						}}
					>
						<Form.Item hidden name="id">
							<Input placeholder="id 隐藏" />
						</Form.Item>
						<Tabs
							activeKey={tabIndex}
							centered
							items={[
								{
									forceRender: true,
									key: 1,
									label: '基本信息',
									children: <BaseInfoForm id={id} form={form} />,
								},
								{
									forceRender: true,
									key: 2,
									label: '责任单位、责任人',
									children: <PersonsForm form={form} />,
								},
								{
									key: 3,
									label: '进展情况',
									children: <FollowForm id={id} />,
								},
								{
									key: 4,
									label: '问题及计划',
									children: <ProblemForm id={id} />,
								},
							]}
							onChange={(key) => {
								if ((tabIndex === 1 || tabIndex === 2) && key > 1) {
									form.validateFields()
										.then(() => {
											submit(key);
										})
										.catch(() => {
											message.warning('请先保存基本信息');
										});
								} else {
									setTabIndex(key);
								}
							}}
						/>
					</Form>
				</div>

				{/* 底部按钮 开始 */}
				{(tabIndex === 1 || tabIndex === 2) && (
					<>
						<div className="height-72"></div>
						<div className="position-fixed bottom-0 left-0 width-100per bg-color-ffffff">
							<div className="flex justify-center align-center margin-0-auto padding-tb-16 padding-lr-20 width-100per windows-width height-72 border-top-f7f8fa border-box">
								<Space size={16}>
									<Button onClick={() => submit()}>暂存</Button>
									<Button type="primary" onClick={() => submit('', true)}>
										保存并提交
									</Button>
								</Space>
							</div>
						</div>
					</>
				)}
				{/* 底部按钮 结束 */}
			</div>
		</div>
	);
};

export default Index;
