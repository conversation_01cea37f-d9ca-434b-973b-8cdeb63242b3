.project-curd-box {
    .ant-radio-wrapper {
        line-height: 32px;
    }
    .ant-tabs-ink-bar {
        display: none !important;
    }
    .ant-tabs-nav {
        margin: 0 100px 24px;
        &::before {
            display: none !important;
        }
        .ant-tabs-nav-wrap {
            display: block;
            width: 100%;
        }
    }
    .ant-tabs-tab {
        margin: 0 !important;
        padding: 0 !important;
        position: relative;
        display: flex;
        align-items: center;
        justify-content: center;
        flex: 1;
        height: 40px;
        background-color: #f2f3f5;
        &.ant-tabs-tab-active {
            background: #165dff;
            .ant-tabs-tab-btn {
                color: #ffffff !important;
            }
        }
        &:after {
            content: "";
            display: block;
            border-top: 24px solid transparent;
            border-bottom: 24px solid transparent;
            border-left: 24px solid #f2f3f5;
            position: absolute;
            right: -24px;
            top: -4px;
            z-index: 1;
        }
        &:before {
            content: "";
            display: block;
            border-top: 24px solid #f2f3f5;
            border-bottom: 24px solid #f2f3f5;
            border-left: 24px solid #ffffff;
            position: absolute;
            left: 4px;
            top: -4px;
            z-index: 0;
        }
        &-active:after {
            content: "";
            display: block;
            border-top: 24px solid transparent;
            border-bottom: 24px solid transparent;
            border-left: 24px solid #165dff;
            position: absolute;
            right: -24px;
            top: -4px;
            z-index: 1;
        }

        &-active:before {
            content: "";
            display: block;
            border-top: 24px solid #165dff;
            border-bottom: 24px solid #165dff;
            border-left: 24px solid #ffffff;
            position: absolute;
            left: 4px;
            top: -4px;
            z-index: 0;
        }

        &:first-child:before {
            display: none;
        }
        &:nth-last-child(2):after {
            display: none;
        }
    }

    .input-number-box {
        width: 375px;
    }
}

.presons-modal-box {
    .ant-collapse-item-active .ant-collapse-header-text {
        font-weight: 500 !important;
        color: #165dff !important;
    }
}
