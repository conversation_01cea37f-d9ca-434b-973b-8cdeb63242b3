import { Col, Row, Button, Input, Steps, Pagination, Tag, Popconfirm, Modal, message, Anchor, Space } from 'antd';
import { StarFilled } from '@ant-design/icons';
import Breadcrumb from '@/components/Breadcrumb';
import Permission, { isPermi } from '@/components/Permission';
import { getImageSrc } from '@/assets/images/index';
import { useRouterLink } from '@/hook/useRouter';
import {
	listFollowUpRecord,
	detailProject,
	projectFollow,
	sendEarlyWarning,
	updateProjectStage,
	projectExport,
	listProjectProblem,
	batchDelProjectProblem,
	batchDelProjectEvaluate,
} from '@/api/Bidmgt/ProjectManage/index';
import { useEffect, useState, useRef } from 'react';
import ModalForm from '@/components/ModalForm';
import { pageCategoryValue } from '@/api/Bidmgt/Dict/index';
import dayjs from 'dayjs';
import { download } from '@/utils/common';
import './index.scss';
import { EditOutlined } from '@ant-design/icons';
import { useSelector } from 'react-redux';

// 项目信息
const ProjectDetailInfo = (props = {}) => {
	const { searchParams } = useRouterLink();
	const toDoListType = searchParams.get('toDoListType');
	const [detailInfo, setDetailInfo] = useState({});
	const detailList = [
		{
			lable: '项目名称',
			keyName: () => detailInfo.projectName || '--',
		},
		{
			lable: '投资企业',
			keyName: () => detailInfo.investmentEnterprisesName || '--',
		},
		{
			lable: '关联项目阶段',
			keyName: () => {
				const arrStatus = [];
				if (detailInfo.negotiationStatus == 1) {
					arrStatus.push('洽谈中');
				}
				if (detailInfo.reviewMeetingStatus == 1) {
					arrStatus.push('已过会');
				}
				if (detailInfo.signStatus == 1) {
					arrStatus.push('已签约');
				}
				return arrStatus.join('、') || '--';
			},
		},
		{
			lable: '项目实际所处阶段',
			keyName: () => detailInfo.projectStageName || '--',
		},
		{
			lable: '洽谈时间',
			keyName: () => detailInfo.negotiationDate || '--',
		},
		{
			lable: '过会时间',
			keyName: () => detailInfo.reviewMeetingDate || '--',
		},
		{
			lable: '签约时间',
			keyName: () => detailInfo.signDate || '--',
		},
		{
			lable: '项目类型',
			keyName: () => detailInfo.projectTypeName || '--',
		},
		{
			lable: '关联至重点已开工',
			keyName: () => {
				if (detailInfo.openingStatus === 1) {
					return '是';
				} else {
					return '否';
				}
			},
		},
		{
			lable: '关联至重点未开工',
			keyName: () => {
				if (detailInfo.openingStatus === 0) {
					return '是';
				} else {
					return '否';
				}
			},
		},
		{
			lable: '双招双引活动项目',
			keyName: () => `${(detailInfo.activityNameList || []).join('、')}` || '--',
		},
		{
			lable: '投资规模',
			keyName: () => (detailInfo.investmentScale ? `${detailInfo.investmentScale} 亿` : '--'),
		},
		{
			lable: '内外资',
			keyName: () => `${detailInfo.investTypeName || '--'}`,
		},
		{
			lable: '拟用地空间',
			keyName: () => `${detailInfo.totalAreaUsed || '--'}`,
		},
		{
			lable: '产业赛道',
			keyName: () => `${detailInfo.industrialTrackName || '--'}`,
		},
		{
			lable: '产业类型',
			keyName: () => `${detailInfo.industryListName || '--'}`,
		},
		{
			lable: '项目详情',
			keyName: () => `${detailInfo.introduction || '--'}`,
		},
		{
			lable: '是否入库',
			keyName: () => `${detailInfo.storageStatus === 1 ? '是' : '否'}`,
		},
		{
			lable: '是否领导考察',
			keyName: () => `${detailInfo.leaderInspectStatus === 1 ? '是' : '否'}`,
		},
		{
			lable: '项目星级',
			hidden: detailInfo.starsCount - 0 <= 0,
			keyName: () =>
				detailInfo.starsCount
					? Array(detailInfo.starsCount)
							.fill('')
							.map((_, oi) => (
								<StarFilled
									key={oi}
									style={{
										fontSize: '16px',
										color: '#ffd24a',
									}}
								/>
							))
					: null,
		},
	];
	useEffect(() => {
		if (props.detailInfo.id) {
			const info = { ...props.detailInfo };
			info.openingStatus = info.openingStatus === 1 ? '是' : '否';
			info.projectTypeName = (info.projectTypeName && <Tag color="success">{info.projectTypeName}</Tag>) || '';

			const { projectCommissionerName, projectCommissionerPhone, districtBusinessTrackerName, districtBusinessTrackerPhone } = info;
			info.projectCommissioner = projectCommissionerName ? `${projectCommissionerName} ${projectCommissionerPhone}` : '';
			info.districtBusinessTracker = districtBusinessTrackerName ? `${districtBusinessTrackerName} ${districtBusinessTrackerPhone}` : '';

			setDetailInfo(info);
		}
	}, [props.detailInfo]);

	return (
		<div className="border-radius-4 bg-color-ffffff margin-bottom-16">
			<div className="padding-lr-20">
				<div className="flex align-center justify-between border-solid-bottom-f2f3f5  padding-tb-14">
					<div className="flex align-center justify-start">
						<div className="width-6 height-16 border-radius-4 bg-color-165dff"></div>
						<div className="font-bold font-size-20 line-height-28 margin-left-8">基本信息</div>
						{toDoListType == '2' && detailInfo.auditStatus == 0 && (
							<Tag bordered={false} color="#ff7d00" className="font-size-14 line-height-24 margin-left-12">
								审核中
							</Tag>
						)}
					</div>

					<Permission hasPermi={['bidmgt:projectManage:list:edit']}>{props.OptionCom && <props.OptionCom />}</Permission>
				</div>
				<div className="padding-lr-30 line-height-24 padding-top-12">
					{detailList
						.filter((ov) => !ov.hidden)
						.map((ov) => {
							return (
								<div key={ov.lable} className="flex justify-start align-start padding-bottom-12">
									<div className="color-86909c flex-shrink">{ov.lable}：</div>
									<div className="flex-sub">
										<ov.keyName />
									</div>
								</div>
							);
						})}
				</div>
			</div>
		</div>
	);
};

// 相关责任人
const RelevantResponsiblePersons = (props = {}) => {
	const [detailInfo, setDetailInfo] = useState({});
	useEffect(() => {
		if (props.detailInfo.id) {
			const info = { ...props.detailInfo };
			setDetailInfo(info);
		}
	}, [props.detailInfo]);

	return (
		<div className="border-radius-4 bg-color-ffffff margin-bottom-16">
			<div className="padding-lr-20">
				<div className="flex align-center justify-between border-solid-bottom-f2f3f5  padding-tb-14">
					<div className="flex align-center justify-start">
						<div className="width-6 height-16 border-radius-4 bg-color-165dff"></div>
						<div className="font-bold font-size-20 line-height-28 margin-left-8">责任单位、责任人</div>
					</div>

					<Permission hasPermi={['bidmgt:projectManage:list:edit']}>{props.OptionCom && <props.OptionCom />}</Permission>
				</div>
				<div className="padding-lr-30 line-height-24 padding-top-12">
					<div className="flex justify-start align-start padding-bottom-12">
						<div className="color-86909c flex-shrink">招商责任单位：</div>
						<div className="flex-sub">
							<Space>
								{(detailInfo.investmentResponsibleList || []).map((ov, oi) => {
									return (
										<Space size={4} key={oi}>
											<div>{ov.name || ''}</div>
											{ov.claimStatus == 0 && <Tag color="warning">待认领</Tag>}
											{ov.claimStatus == 2 && <Tag color="success">已确认</Tag>}
										</Space>
									);
								})}
							</Space>
							{(detailInfo.investmentResponsibleList || []).length === 0 && '--'}
						</div>
					</div>
					<div className="flex justify-start align-start padding-bottom-12">
						<div className="color-86909c flex-shrink">招商专员：</div>
						<div className="flex-sub">
							{(detailInfo.projectCommissionerList &&
								detailInfo.projectCommissionerList.map((ov, oi) => {
									return <span className="mr-10px" key={oi}>{`${ov.userName} ${ov.phone}`}</span>;
								})) ||
								''}
							{(detailInfo.projectCommissionerList || []).length === 0 && '--'}
						</div>
					</div>
					<div className="flex justify-start align-start padding-bottom-12">
						<div className="color-86909c flex-shrink">项目经办人：</div>
						<div className="flex-sub">
							{(detailInfo.projectHandledByList &&
								detailInfo.projectHandledByList.map((ov, oi) => {
									return <span className="mr-10px" key={oi}>{`${ov.userName} ${ov.phone}`}</span>;
								})) ||
								''}
							{(detailInfo.projectCommissionerList || []).length === 0 && '--'}
						</div>
					</div>
					<div className="flex justify-start align-start padding-bottom-12">
						<div className="color-86909c flex-shrink">区商务局项目跟进人：</div>
						<div className="flex-sub">
							{(detailInfo.districtBusinessTrackerList &&
								detailInfo.districtBusinessTrackerList.map((ov) => {
									return <span className="mr-10px" key={ov.phone}>{`${ov.userName} ${ov.phone}`}</span>;
								})) ||
								'--'}
							{(detailInfo.districtBusinessTrackerList || []).length === 0 && '--'}
						</div>
					</div>
				</div>
			</div>
		</div>
	);
};

// 进展情况
const ProgressInfo = (props = {}) => {
	const { searchParams } = useRouterLink();
	const projectId = searchParams.get('id');
	const [followList, setFollowList] = useState([]);
	// 获取跟进数据
	const getFollowData = () => {
		listFollowUpRecord({ projectId }).then((res) => {
			setFollowList(res.data || []);
		});
	};
	useEffect(() => {
		if (projectId) {
			getFollowData();
		}
	}, []);

	return (
		<div className="border-radius-4 bg-color-ffffff margin-bottom-16">
			<div className="padding-lr-20">
				<div className="flex align-center justify-between border-solid-bottom-f2f3f5  padding-tb-14">
					<div className="flex align-center justify-start">
						<div className="width-6 height-16 border-radius-4 bg-color-165dff"></div>
						<div className="font-bold font-size-20 line-height-28 margin-left-8">进展情况</div>
					</div>

					<Permission hasPermi={['bidmgt:projectManage:list:edit']}>{props.OptionCom && <props.OptionCom />}</Permission>
				</div>
				<div className="padding-lr-30 line-height-24 padding-top-12">
					{followList.map((ov, oi) => {
						return (
							<div key={oi} className="flex justify-start align-start padding-bottom-12 position-relative">
								<div className="color-86909c flex-shrink width-124">{`${ov.createTime || ''}`.slice(0, 16)}</div>
								{oi + 1 != followList.length ? (
									<div className="bg-color-165dff width-2 height-100per position-absolute top-16" style={{ left: '135px' }}></div>
								) : null}
								<div className="flex align-center justify-center width-24 height-24 boxder-box">
									<div className="width-12 height-12 border-radius-6 bg-color-165dff  boxder-box"></div>
								</div>
								<div className="flex-sub">{ov.latestDevelopments}</div>
							</div>
						);
					})}
					{!followList.length && <div className="text-align-center padding-tb-20 color-86909c">~暂无进展数据~</div>}
				</div>
			</div>
		</div>
	);
};

// 问题及计划
const ExistingProblems = (props = {}) => {
	const [detailInfo, setDetailInfo] = useState({});
	useEffect(() => {
		if (props.detailInfo.id) {
			const info = { ...props.detailInfo };
			setDetailInfo(info);
		}
	}, [props.detailInfo]);

	return (
		<div className="border-radius-4 bg-color-ffffff margin-bottom-16">
			<div className="padding-lr-20">
				<div className="flex align-center justify-between border-solid-bottom-f2f3f5  padding-tb-14">
					<div className="flex align-center justify-start">
						<div className="width-6 height-16 border-radius-4 bg-color-165dff"></div>
						<div className="font-bold font-size-20 line-height-28 margin-left-8">问题及计划</div>
					</div>

					<Permission hasPermi={['bidmgt:projectManage:list:edit']}>{props.OptionCom && <props.OptionCom />}</Permission>
				</div>
				<div className="padding-lr-30 line-height-24 padding-top-12">
					{detailInfo.projectProblemList &&
						!!detailInfo.projectProblemList.length &&
						detailInfo.projectProblemList.slice(0, 1).map((ov) => {
							return (
								<div className="" key={ov.id}>
									<div className="flex justify-start align-start padding-bottom-12">
										<div className="color-86909c flex-shrink">描述：</div>
										<div className="flex-sub">{ov.questionContent || ''}</div>
									</div>
									<div className="flex justify-start align-start padding-bottom-12">
										<div className="color-86909c flex-shrink">是否需要领导协调：</div>
										<div className="flex-sub">{ov.coordinatedResolutionStatus == 1 ? '是' : '否'}</div>
									</div>
									<div className="flex justify-start align-start padding-bottom-12">
										<div className="color-86909c flex-shrink">是否已解决：</div>
										<div className="flex-sub">{ov.solveStatus == 1 ? '是' : '否'}</div>
									</div>
								</div>
							);
						})}

					{(!detailInfo.projectProblemList || !detailInfo.projectProblemList.length) && (
						<div className="text-align-center padding-tb-20 color-86909c">~暂无问题及计划数据~</div>
					)}
				</div>
			</div>
		</div>
	);
};

// 领导点评
const LeadersComments = (props = {}) => {
	const [detailInfo, setDetailInfo] = useState({});
	useEffect(() => {
		if (props.detailInfo.id) {
			const info = { ...props.detailInfo };
			setDetailInfo(info);
		}
	}, [props.detailInfo]);
	const delBtn = (id) => {
		batchDelProjectEvaluate({
			ids: [id], // 评论等级id
		}).then(() => {
			props.update && props.update();
		});
	};
	// 登录凭证
	const userInfo = useSelector((state) => {
		return state.user.userInfo;
	});

	return (
		<div className="border-radius-4 bg-color-ffffff margin-bottom-16">
			<div className="padding-lr-20">
				<div className="flex align-center justify-between border-solid-bottom-f2f3f5 padding-tb-14">
					<div className="flex align-center justify-start">
						<div className="width-6 height-16 border-radius-4 bg-color-165dff"></div>
						<div className="font-bold font-size-20 line-height-28 margin-left-8">领导点评</div>
					</div>

					<Permission hasPermi={['bidmgt:projectManage:list:edit']}>{props.OptionCom && <props.OptionCom />}</Permission>
				</div>
				{/* 评论 开始 */}
				<Permission hasPermi={['bidmgt:projectManage:detail:evaluate']}></Permission>
				{/* 评论 结束 */}
				{/* 评论列表 开始 */}
				<Permission hasPermi={['bidmgt:projectManage:detail:viewEvaluate']}>
					<div className="padding-tb-12">
						{detailInfo.projectEvaluateVoList &&
							detailInfo.projectEvaluateVoList.map((ov) => {
								return (
									<div className="padding-lr-30 border-solid-bottom-f2f3f5" key={ov.id}>
										<div className="flex justify-start align-start padding-bottom-12 font-size-16 font-weight-500 line-height-24 ">
											<div className="flex-shrink">{ov.evaluationLeaderName}：</div>
											<img class="width-24 height-24 flex-shrink" src={ov.evaluationLevelIconUrl} />
											<div class="flex-sub margin-left-8">{ov.evaluationLevelName}</div>
										</div>
										<div className="padding-bottom-12 line-height-22">备注：{ov.evaluationContent || '--'}</div>
										<div className="flex justify-start align-center padding-bottom-12">
											<div className="color-86909c flex-shrink line-height-20">{ov.createTime}</div>

											<Permission hasPermi={['bidmgt:projectManage:detail:evaluate']}>
												{ov.createBy == userInfo.id && (
													<img
														class="width-24 height-24 flex-shrink margin-left-16 a"
														src={getImageSrc('@/assets/images/Public/icon-delete-gray.png')}
														onClick={() => delBtn(ov.id)}
													/>
												)}
											</Permission>
										</div>
									</div>
								);
							})}
						{(!detailInfo.projectEvaluateVoList || !detailInfo.projectEvaluateVoList.length) && (
							<div className="text-align-center padding-tb-20 color-86909c">~暂无领导点评数据~</div>
						)}
					</div>
				</Permission>
				{/* 评论列表 结束 */}
			</div>
		</div>
	);
};

const Index = (props = {}) => {
	const { searchParams, linkTo } = useRouterLink();
	const id = searchParams.get('id');
	const toDoListType = searchParams.get('toDoListType');

	const [detail, setDetail] = useState({});

	// 获取详情
	const getDetail = () => {
		detailProject({ id }).then((res) => {
			setDetail(res.data || {});
			// 如果父级有需要 详情的数据
			if (props.setDetail) {
				props.setDetail(res.data || {});
			}
		});
	};

	useEffect(() => {
		if (id) {
			getDetail();
		}

		if (props.reflash) {
			props.reflash.current = getDetail;
		}
	}, []);

	return (
		<div className="position-relative">
			<div className=" position-absolute width-156 right-100per overflow-hidden">
				<div className="margin-right-20 text-align-right">
					<Anchor
						className="bidmgt-priject-detail-Anchor bg-color-ffffff padding-right-10 padding-tb-16"
						targetOffset={20}
						rootClassName="anchor-header-tabBar-box"
						items={[
							{
								key: '基本信息',
								href: '#基本信息',
								title: <div className="padding-right-14">基本信息</div>,
							},
							{
								key: '相关责任人',
								href: '#相关责任人',
								title: <div className="padding-right-14">相关责任人</div>,
							},
							{
								key: '进展情况',
								href: '#进展情况',
								title: <div className="padding-right-14">进展情况</div>,
							},
							{
								key: '问题及计划',
								href: '#问题及计划',
								title: <div className="padding-right-14">问题及计划</div>,
							},
							{
								key: '领导点评',
								href: '#领导点评',
								hidden: !isPermi(['bidmgt:projectManage:detail:viewEvaluate', 'bidmgt:projectManage:detail:evaluate']),
								title: <div className="padding-right-14">领导点评</div>,
							},
						].filter((ov) => !ov.hidden)}
						replace
					/>
				</div>
			</div>
			<div>
				<div id="基本信息"></div>
				<ProjectDetailInfo
					detailInfo={detail}
					update={getDetail}
					OptionCom={() =>
						!toDoListType && (
							<Button
								type="primary"
								icon={<EditOutlined />}
								onClick={() => linkTo(`/bidmgt/projectManage/curd?id=${detail.id}&tabIndex=1`)}
							>
								编辑信息
							</Button>
						)
					}
				/>
				<div id="相关责任人"></div>
				<RelevantResponsiblePersons
					detailInfo={detail}
					update={getDetail}
					OptionCom={() =>
						!toDoListType && (
							<Button
								type="primary"
								icon={<EditOutlined />}
								onClick={() => linkTo(`/bidmgt/projectManage/curd?id=${detail.id}&tabIndex=2`)}
							>
								编辑信息
							</Button>
						)
					}
				/>
				<div id="进展情况"></div>
				<ProgressInfo
					detailInfo={detail}
					update={getDetail}
					OptionCom={() =>
						!toDoListType && (
							<Button
								type="primary"
								icon={<EditOutlined />}
								onClick={() => linkTo(`/bidmgt/projectManage/curd?id=${detail.id}&tabIndex=3`)}
							>
								编辑信息
							</Button>
						)
					}
				/>
				<div id="问题及计划"></div>
				<ExistingProblems
					detailInfo={detail}
					update={getDetail}
					OptionCom={() =>
						!toDoListType && (
							<Button
								type="primary"
								icon={<EditOutlined />}
								onClick={() => linkTo(`/bidmgt/projectManage/curd?id=${detail.id}&tabIndex=4`)}
							>
								编辑信息
							</Button>
						)
					}
				/>

				<Permission hasPermi={['bidmgt:projectManage:detail:viewEvaluate', 'bidmgt:projectManage:detail:evaluate']}>
					<div id="领导点评"></div>
					<LeadersComments detailInfo={detail} update={getDetail} />
				</Permission>
			</div>
			<div className="height-400"></div>
			<div className="height-400"></div>
		</div>
	);
};

export default Index;
