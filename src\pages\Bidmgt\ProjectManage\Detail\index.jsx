import Breadcrumb from '@/components/Breadcrumb';
import { useRouterLink } from '@/hook/useRouter';
import DetailComp from './DetailComp';
import { EditOutlined } from '@ant-design/icons';

const Index = (props = {}) => {
	const { searchParams, linkTo } = useRouterLink();

	return (
		<div className="full-page-width flex-sub" style={{ marginTop: '16px' }}>
			{/* 面包屑 开始 */}
			<Breadcrumb
				icon="icon-projectManage"
				list={[
					{
						name: '项目管理',
						link: '/bidmgt/projectManage/list',
					},
					{
						name: '项目列表',
						link: '/bidmgt/projectManage/list',
					},
				]}
				name="项目详情"
			/>
			{/* 面包屑 结束 */}

			<DetailComp />
		</div>
	);
};

export default Index;
