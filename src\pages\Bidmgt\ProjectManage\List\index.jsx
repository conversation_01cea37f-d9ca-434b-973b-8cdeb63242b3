import { useEffect, useState } from 'react';
import { Col, Row, Select, DatePicker, Button, Table, Popconfirm, Space, Form, message, Dropdown, Upload, Modal, Input, InputNumber } from 'antd';
import { SearchOutlined, ReloadOutlined, PlusOutlined, CalendarOutlined, CloseOutlined } from '@ant-design/icons';

import Permission from '@/components/Permission';
import { FilterTitle, FilterOption, FilterDate, FilterChecked } from '@/components/Bidmgt/FilterComp';

import { useRouterLink } from '@/hook/useRouter';
import { useIsAdmin } from '@/hook/Bidmgt/useIsAdmin';

import { myDeptProjectPage, batchDelProject, projectImport, projectExport } from '@/api/Bidmgt/ProjectManage/index';

import { downloadFileByUrl, download } from '@/utils/common';
import { getCategoryValueList, getInvestmentResponsibleDept } from '@/utils/bigmt';

import dayjs from 'dayjs';

const Index = () => {
	const { openNewTab, searchParams, setSearchParams } = useRouterLink();

	// 请求参数
	const [params, setParams] = useState({});

	// 页码数据
	const [pagination, setPagination] = useState({
		total: 0,
		pageNum: searchParams.get('pageNum') - 0 || 1,
		pageSize: searchParams.get('pageSize') - 0 || 10,
	});

	const [dataSource, setDataSource] = useState([]);

	// 选择
	const [selectedRowKeys, setSelectedRowKeys] = useState([]);
	const [selectedRowObj, setSelectedRowObj] = useState({});

	// 导出
	const exportBtn = () => {
		projectExport(
			{
				...getParamsData(),
				auditStatus: 2,
				ids: [...selectedRowKeys],
			},
			{
				responseType: 'blob',
			}
		).then((res) => {
			download.excel(res, `项目-${dayjs().format('YYYYMMDD_HH:mm')}`);
			setSelectedRowKeys([]);
			setSelectedRowObj({});
		});
	};

	// 上传
	const beforeUpload = (e) => {
		if (e.type.includes('sheet')) {
			const file = new FormData();
			file.append('file', e);
			projectImport(file).then(() => {
				resetBtn();
			});
		} else {
			message.warning('请选择 excel 文件 上传');
		}
		return false;
	};

	// 删除
	const handelDel = (id = '') => {
		if (id === '') {
			Modal.confirm({
				title: '确认删除',
				content: '确认删除选中数据吗？',
				onOk() {
					batchDel(selectedRowKeys);
				},
			});
		} else {
			batchDel([id]);
		}
	};

	// 批量删除
	const batchDel = (ids) => {
		batchDelProject({
			ids,
		}).then(() => {
			setSelectedRowKeys([]);
			setSelectedRowObj({});
			const { pageNum, pageSize } = pagination;
			if (pageNum > 1 && pageNum > Math.ceil((total - ids.length) / pageSize)) {
				setPagination({ ...pagination, pageNum: pageNum - 1 });
			} else {
				getList();
			}
		});
	};

	// 搜索
	const onSearch = (values, index = 1) => {
		setParams({ ...values });
		if (index !== 1) {
			setPagination({ ...pagination, pageNum: 1 });
		}
	};

	const getParamsData = () => {
		const { projectStatus, openingStatusList } = params;
		if (projectStatus) {
			projectStatus.forEach((ov) => {
				params[ov] = 1;
			});
			delete params.projectStatus;
		}
		if (openingStatusList) {
			params.keyProjectStatus = 1;
			if (openingStatusList.length === 1) {
				params.openingStatus = openingStatusList[0];
			}
			delete params.openingStatusList;
		}
		return params;
	};

	// 获取表格数据
	const getList = () => {
		const { pageNum, pageSize } = pagination;

		myDeptProjectPage({
			...getParamsData(),
			auditStatus: 2,
			pageNum,
			pageSize,
		}).then((res) => {
			setSearchParams(
				{
					pageNum,
					pageSize,
				},
				{ replace: true }
			);
			setDataSource(res.data.records || []);
			setPagination({
				...pagination,
				total: res.data.total - 0,
			});
		});
	};

	useEffect(() => {
		if (Object.keys(params).length > 0) {
			getList();
		}
	}, [pagination.pageNum, pagination.pageSize, params]);

	return (
		<div className="project-box flex-sub flex flex-direction-column padding-20">
			{/* 搜索 开始 */}
			<SearchFilter onSearch={onSearch} />
			{/* 搜索 结束 */}

			<Space size={12} className="flex align-center margin-top-20 padding-20 border-radius-4 bg-color-ffffff">
				<Permission hasPermi={['bidmgt:projectManage:list:import']}>
					<Dropdown
						menu={{
							items: [
								{
									key: '1',
									label: (
										<Upload maxCount={1} showUploadList={false} beforeUpload={beforeUpload}>
											<Button type="default">文件上传</Button>
										</Upload>
									),
								},
								{
									key: '2',
									label: (
										<Button
											type="default"
											onClick={() => {
												downloadFileByUrl(
													`${window.location.origin}${
														import.meta.env.VITE_BASE_PATH ? import.meta.env.VITE_BASE_PATH : '/'
													}招商批量导入模板.xlsx`,
													'招商批量导入模板.xlsx'
												);
											}}
										>
											模板下载
										</Button>
									),
								},
							],
						}}
					>
						<Button type="default">批量导入</Button>
					</Dropdown>
				</Permission>
				<Permission hasPermi={['bidmgt:projectManage:list:export']}>
					<Button type="default" onClick={() => exportBtn()}>
						批量导出
					</Button>
				</Permission>
				<Permission hasPermi={['bidmgt:projectManage:list:delete']}>
					<Button onClick={() => handelDel()} disabled={selectedRowKeys.length === 0}>
						删除
					</Button>
				</Permission>
				{selectedRowKeys.length > 0 && <div>已选择 {selectedRowKeys.length} 条数据</div>}
			</Space>

			<div className="margin-top-20 padding-20 border-radius-4 bg-color-ffffff">
				{/* 操作按钮 开始 */}
				<div className="margin-bottom-20 flex align-center justify-between">
					<div>
						查询到
						<span className="color-f53f3f">{pagination.total}</span>
						个项目
					</div>
					<Space size={16}>
						{/* <Select
							value={1}
							options={[{ label: '按最新时间', value: 1 }]}
						></Select> */}
						<Permission hasPermi={['bidmgt:projectManage:list:add']}>
							<Button
								type="primary"
								icon={<PlusOutlined />}
								onClick={() => {
									openNewTab('/bidmgt/projectManage/curd');
								}}
							>
								新建
							</Button>
						</Permission>
					</Space>
				</div>
				{/* 操作按钮 结束 */}

				{/* 表格 开始 */}
				<Table
					size="small"
					rowKey="id"
					rowSelection={{
						selectedRowKeys,
						onChange: (checkedRowKeys) => {
							selectedRowObj[pagination.pageNum] = checkedRowKeys;
							setSelectedRowObj({ ...selectedRowObj });
							setSelectedRowKeys(Object.values(selectedRowObj).flat(2));
						},
					}}
					dataSource={dataSource}
					onChange={(e) => {
						setPagination({
							...pagination,
							pageSize: e.pageSize,
							pageNum: e.current,
						});
					}}
					pagination={{
						total: pagination.total,
						pageSize: pagination.pageSize,
						current: pagination.pageNum,
						size: 'default',
					}}
					scroll={{
						x: 'min-content',
					}}
				>
					<Table.Column title="项目名称" key="projectName" dataIndex="projectName" width={190} fixed="left" />
					<Table.Column title="项目阶段" key="projectStageName" dataIndex="projectStageName" width={100} render={(text) => text || '--'} />
					<Table.Column
						title="项目状态"
						key="projectStatus"
						dataIndex="projectStatus"
						width={100}
						render={(_, record) => {
							const { negotiationStatus, reviewMeetingStatus, signStatus } = record;
							const list = [
								negotiationStatus === 1 ? '洽谈中' : '',
								reviewMeetingStatus === 1 ? '已过会' : '',
								signStatus === 1 ? '已签约' : '',
							].filter((ov) => ov);
							return list.length > 0 ? list.map((ov) => <div key={ov}>{ov}</div>) : '--';
						}}
					/>
					<Table.Column
						title="重点项目"
						key="openingStatus"
						dataIndex="openingStatus"
						width={100}
						render={(text, record) => {
							const { openingStatus, keyProjectStatus } = record;
							return keyProjectStatus === 0 ? '--' : openingStatus === 0 ? '重点未开工' : openingStatus === 1 ? '重点已开工' : '--';
						}}
					/>
					<Table.Column
						title="招商责任单位"
						key="investmentResponsibleList"
						dataIndex="investmentResponsibleList"
						width={100}
						render={(text) => (text.length ? text.map((ov) => <div key={ov.id}>{ov.name}</div>) : '--')}
					/>
					<Table.Column
						title="所属产业"
						key="industryListName"
						dataIndex="industryListName"
						width={100}
						render={(text) => (text && text[0]) || '--'}
					/>
					<Table.Column
						title="投资规模（亿）"
						key="investmentScale"
						dataIndex="investmentScale"
						width={140}
						render={(text) => text || '--'}
					/>
					<Table.Column
						title="洽谈时间"
						key="negotiationDate"
						dataIndex="negotiationDate"
						width={100}
						render={(text) => (text ? `${text}年` : '--')}
					/>
					<Table.Column
						title="过会时间"
						key="reviewMeetingDate"
						dataIndex="reviewMeetingDate"
						width={100}
						render={(text) => (text ? `${text}年` : '--')}
					/>
					<Table.Column title="签约时间" key="signDate" dataIndex="signDate" width={100} render={(text) => (text ? `${text}年` : '--')} />
					<Table.Column
						title="操作"
						key="option"
						dataIndex="option"
						fixed="right"
						align="center"
						width={150}
						render={(_, record) => {
							return (
								<>
									<Button type="link" size="small" onClick={() => openNewTab(`/bidmgt/projectManage/detail?id=${record.id}`)}>
										查看
									</Button>
									<Permission hasPermi={['bidmgt:projectManage:list:edit']}>
										<Button type="link" size="small" onClick={() => openNewTab(`/bidmgt/projectManage/curd?id=${record.id}`)}>
											编辑
										</Button>
									</Permission>

									<Permission hasPermi={['bidmgt:projectManage:list:delete']}>
										<Popconfirm
											title="提示"
											description="确定删除吗？"
											onConfirm={() => {
												handelDel(record.id);
											}}
											okText="确定"
											cancelText="取消"
										>
											<Button type="link" danger size="small">
												删除
											</Button>
										</Popconfirm>
									</Permission>
								</>
							);
						}}
					/>
				</Table>
				{/* 表格 结束 */}
			</div>
		</div>
	);
};

// 搜索过滤
const SearchFilter = (props = {}) => {
	const { isAdmin, deptIds } = useIsAdmin();
	const [form] = Form.useForm();

	// 筛选数据
	const [projectStatusOptions, setProjectStatusOptions] = useState([]);
	const [projectStageOptions, setProjectStageOptions] = useState([]);
	const [openingStatusOptions, setOpeningStatusOptions] = useState([]);
	const [deptListOptions, setDeptListOptions] = useState([]);
	const [industryOptions, setIndustryOptions] = useState([]);

	// 选中数据
	const [checkedList, setCheckedList] = useState([]);
	// 提交标记
	const [submitSign, setSubmitSign] = useState(0);

	// 提交
	const submit = () => {
		setSubmitSign(submitSign + 1);
	};

	// 重置
	const resetFilter = () => {
		form.resetFields();
		setCheckedList([]);
		submit();
	};

	// 获取筛选数据
	const getFilterData = () => {
		setProjectStatusOptions([
			{
				label: '洽谈中',
				value: 'negotiationStatus',
			},
			{
				label: '已过会',
				value: 'reviewMeetingStatus',
			},
			{
				label: '已签约',
				value: 'signStatus',
			},
		]);
		getCategoryValueList('project_stage').then((listData) => {
			setProjectStageOptions(listData);
		});
		getInvestmentResponsibleDept().then((listData) => {
			setDeptListOptions(listData);
		});
		setOpeningStatusOptions([
			{
				label: '重点已开工项目',
				value: 1,
			},
			{
				label: '重点未开工项目',
				value: 0,
			},
		]);
		getCategoryValueList('industry_type').then((listData) => {
			setIndustryOptions(listData);
		});
	};

	useEffect(() => {
		// 回显数据
		const checkedList = JSON.parse(sessionStorage.getItem('checkedListProject') || '[]');
		const formData = JSON.parse(sessionStorage.getItem('formDataProject') || '{}');

		if (checkedList.length > 0) {
			setCheckedList(checkedList);
		}
		if (Object.keys(formData).length > 0) {
			form.setFieldsValue(formData);
		}
		getFilterData();
		submit();
	}, []);

	useEffect(() => {
		if (submitSign) {
			const values = form.getFieldsValue();
			sessionStorage.setItem('checkedListProject', JSON.stringify(checkedList));
			sessionStorage.setItem('formDataProject', JSON.stringify(values));
			const params = {};
			for (let i in values) {
				if (!Array.isArray(values[i]) || values[i].length > 0) {
					params[i] = values[i];
				}
			}
			props.onSearch && props.onSearch(params, submitSign);
		}
	}, [submitSign]);

	return (
		<Form
			form={form}
			initialValues={{
				projectStatus: [],
				projectStageIds: [],
				openingStatusList: [],
				investmentResponsibleIds: [],
				industryIds: [],
			}}
		>
			<div className="border-radius-4 bg-color-ffffff">
				<div className="flex align-center justify-between padding-lr-20 padding-top-16">
					<div className="font-size-16 font-weight-500 color-1d2129 line-height-24">项目管理</div>
					<div className="width-220">
						<Form.Item noStyle name="projectName">
							<Input placeholder="请输入关键词搜素" suffix={<SearchOutlined onClick={submit} />} onPressEnter={submit} />
						</Form.Item>
					</div>
				</div>
				<div className="padding-lr-20 padding-tb-20">
					{checkedList.length > 0 && (
						<FilterTitle title={<div className="font-bold color-165dff">已选条件</div>}>
							<FilterChecked form={form} checkedList={checkedList} setCheckedList={setCheckedList} />
						</FilterTitle>
					)}
					<FilterTitle title="项目状态">
						<Form.Item noStyle name="projectStatus">
							<FilterOption name="项目状态" options={projectStatusOptions} checkedList={checkedList} setCheckedList={setCheckedList} />
						</Form.Item>
					</FilterTitle>
					<FilterTitle title="项目阶段">
						<Form.Item noStyle name="projectStageIds">
							<FilterOption name="项目阶段" options={projectStageOptions} checkedList={checkedList} setCheckedList={setCheckedList} />
						</Form.Item>
					</FilterTitle>
					<FilterTitle title="重点项目">
						<Form.Item noStyle name="openingStatusList">
							<FilterOption name="重点项目" options={openingStatusOptions} checkedList={checkedList} setCheckedList={setCheckedList} />
						</Form.Item>
					</FilterTitle>
					{isAdmin && (
						<FilterTitle title="招商责任单位">
							<Form.Item noStyle name="investmentResponsibleIds">
								<FilterOption
									name="招商责任单位"
									options={deptListOptions}
									checkedList={checkedList}
									setCheckedList={setCheckedList}
								/>
							</Form.Item>
						</FilterTitle>
					)}
					<FilterTitle title="所属产业">
						<Form.Item noStyle name="industryIds">
							<FilterOption name="所属产业" options={industryOptions} checkedList={checkedList} setCheckedList={setCheckedList} />
						</Form.Item>
					</FilterTitle>
					<FilterTitle title="重点节点时间" className="margin-top-12">
						<Row gutter={[32, 12]} className="width-100per padding-left-10">
							<Col xs={24} sm={12} md={12} lg={8}>
								<FilterDate label="洽谈时间" name="negotiationDate" />
							</Col>
							<Col xs={24} sm={12} md={12} lg={8}>
								<FilterDate label="过会时间" name="reviewMeetingDate" />
							</Col>
							<Col xs={24} sm={12} md={12} lg={8}>
								<FilterDate label="签约时间" name="signDate" />
							</Col>
						</Row>
					</FilterTitle>
				</div>
				<div className="flex justify-end padding-lr-20 padding-tb-12 border-solid-top-e5e6e8">
					<Space size={20}>
						<Button className="width-100" onClick={resetFilter}>
							重置筛选
						</Button>
						<Button className="width-100" type="primary" onClick={submit}>
							查询
						</Button>
					</Space>
				</div>
			</div>
		</Form>
	);
};

export default Index;
