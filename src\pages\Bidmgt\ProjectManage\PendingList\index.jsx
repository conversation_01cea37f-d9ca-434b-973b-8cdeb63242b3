import { useEffect, useState } from 'react';
import {
	Col,
	Row,
	Select,
	DatePicker,
	Button,
	Table,
	Popconfirm,
	Space,
	Form,
	message,
	Dropdown,
	Upload,
	Modal,
	Tabs,
	Input,
	Tag,
} from 'antd';
import {
	SearchOutlined,
	ReloadOutlined,
	PlusOutlined,
} from '@ant-design/icons';
import Permission from '@/components/Permission';
import { useRouterLink } from '@/hook/useRouter';
import { useIsAdmin } from '@/hook/Bidmgt/useIsAdmin';
import { batchDelProject } from '@/api/Bidmgt/ProjectManage/index';
import { pageCategoryValue } from '@/api/Bidmgt/Dict/index';
import { myDeptProjectPage } from '@/api/Bidmgt/ProjectManage/index';

const Index = () => {
	const { linkTo, openNewTab, searchParams, setSearchParams } =
		useRouterLink();
	const onChange = (key) => {
		console.log(key);
		setStatus(key);
	};

	const auditStatus = searchParams.get('auditStatus') || 0;
	const projectNameDefault = searchParams.get('projectName') || '';
	const [status, setStatus] = useState(auditStatus);
	const [projectName, setProjectName] = useState(projectNameDefault);
	const items = [
		{
			key: 0,
			label: '审核中',
			children: null,
		},
		{
			key: 1,
			label: '不通过',
			children: null,
		},
	];
	// 页码数据
	const [pagination, setPagination] = useState({
		total: 0,
		pageNum: searchParams.get('pageNum') - 0 || 1,
		pageSize: searchParams.get('pageSize') - 0 || 10,
	});

	const [dataSource, setDataSource] = useState([]);

	const getList = (pageNum = 1, pageSize = 10) => {
		myDeptProjectPage({
			pageNum,
			pageSize,
			projectName: projectName || '',
			auditStatus: status,
		}).then((res) => {
			setSearchParams(
				{
					pageNum,
					pageSize,
					projectName: projectName || '',
					auditStatus: status,
				},
				{ replace: true }
			);
			setDataSource(res.data.records || []);
			setPagination({
				pageNum,
				pageSize,
				total: res.data.total - 0,
			});
		});
	};

	// 删除
	const handelDel = (id = '') => {
		batchDel([id]);
	};

	// 批量删除
	const batchDel = (ids) => {
		batchDelProject({
			ids,
		}).then(() => {
			const { pageNum } = pagination;
			if (pagination.total == 1 && pageNum > 1) {
				setPagination({ ...pagination, pageNum: pageNum - 1 });
			} else {
				getList();
			}
		});
	};

	useEffect(() => {
		getList(pagination.pageNum, pagination.pageSize);
	}, [pagination.pageNum, pagination.pageSize, status]);

	return (
		<div className='project-box flex-sub flex flex-direction-column padding-20'>
			<div className='padding-20 flex-sub bg-color-ffffff border-radius-4'>
				<div className='flex align-center justify-between margin-bottom-20'>
					<div className='flex-sub font-bold font-size-16'>
						待发布项目
					</div>
					<Input
						className='width-220'
						placeholder='请输入项目名称搜素'
						onInput={(e) => {
							setProjectName(e.target.value || '');
						}}
						onPressEnter={() => {
							getList(1, 10);
						}}
						suffix={
							<SearchOutlined
								onClick={() => {
									getList(1, 10);
								}}
							/>
						}
					/>
				</div>
				<Tabs
					defaultActiveKey={status}
					items={items}
					onChange={onChange}
					tabBarExtraContent={{
						right: (
							<Permission
								hasPermi={[
									'bidmgt:projectManage:pendingList:add',
								]}
							>
								<Button
									type='primary'
									icon={<PlusOutlined />}
									onClick={() => {
										openNewTab(
											'/bidmgt/projectManage/curd'
										);
									}}
								>
									新建
								</Button>
							</Permission>
						),
					}}
				/>
				<Table
					size='small'
					rowKey='id'
					dataSource={dataSource}
					onChange={(e) => {
						setPagination({
							...pagination,
							pageSize: e.pageSize,
							pageNum: e.current,
						});
					}}
					pagination={{
						total: pagination.total,
						pageSize: pagination.pageSize,
						current: pagination.pageNum,
						size: 'default',
						showTotal: (total) => `共 ${total} 条`,
					}}
					scroll={{
						x: 'min-content',
					}}
				>
					<Table.Column
						title='序号'
						key='index'
						fixed='left'
						width={60}
						render={(_, __, index) => {
							return index + 1;
						}}
						align='center'
					/>
					<Table.Column
						title='项目名称'
						key='projectName'
						dataIndex='projectName'
						fixed='left'
						width={190}
					/>

					<Table.Column
						title='项目阶段'
						key='projectStageName'
						dataIndex='projectStageName'
						width={100}
						render={(text) => text || '--'}
					/>
					<Table.Column
						title='项目状态'
						key='projectStatus'
						dataIndex='projectStatus'
						width={100}
						render={(_, record) => {
							const {
								negotiationStatus,
								reviewMeetingStatus,
								signStatus,
							} = record;
							const list = [
								negotiationStatus === 1 ? '洽谈中' : '',
								reviewMeetingStatus === 1 ? '已过会' : '',
								signStatus === 1 ? '已签约' : '',
							].filter((ov) => ov);
							return list.length > 0
								? list.map((ov) => <div key={ov}>{ov}</div>)
								: '--';
						}}
					/>
					<Table.Column
						title='重点项目'
						key='openingStatus'
						dataIndex='openingStatus'
						width={100}
						render={(text) =>
							text === 0
								? '重点未开工'
								: text === 1
								? '重点已开工'
								: '--'
						}
					/>
					<Table.Column
						title='招商责任单位'
						key='investmentResponsibleList'
						dataIndex='investmentResponsibleList'
						width={100}
						render={(text) =>
							text.length
								? text.map((ov) => (
										<div key={ov.name}>{ov.name}</div>
								  ))
								: '--'
						}
					/>
					<Table.Column
						title='所属产业'
						key='industryListName'
						dataIndex='industryListName'
						width={100}
						render={(text) => (text && text[0]) || '--'}
					/>
					<Table.Column
						title='投资规模（亿）'
						key='investmentScale'
						dataIndex='investmentScale'
						width={140}
						render={(text) => text || '--'}
					/>
					<Table.Column
						title='洽谈时间'
						key='negotiationDate'
						dataIndex='negotiationDate'
						width={100}
						render={(text) => (text ? `${text}年` : '--')}
					/>
					<Table.Column
						title='过会时间'
						key='reviewMeetingDate'
						dataIndex='reviewMeetingDate'
						width={100}
						render={(text) => (text ? `${text}年` : '--')}
					/>
					<Table.Column
						title='签约时间'
						key='signDate'
						dataIndex='signDate'
						width={100}
						render={(text) => (text ? `${text}年` : '--')}
					/>

					<Table.Column
						title='发布状态'
						key='auditStatus'
						dataIndex='auditStatus'
						fixed='right'
						align='center'
						width={140}
						render={(auditStatus, record) => {
							let text = '';
							if (auditStatus == 0) {
								if (
									record.projectAuditProcessLevel == 2 &&
									record.auditProcessLevel == 2
								) {
									text = (
										<Tag color='processing'>
											待商务局审核
										</Tag>
									);
								}
								if (
									record.projectAuditProcessLevel == 2 &&
									record.auditProcessLevel == 1
								) {
									text = (
										<Tag color='warning'>
											待分管领导审核
										</Tag>
									);
								}

								if (record.projectAuditProcessLevel == 1) {
									text = (
										<Tag color='processing'>
											待商务局审核
										</Tag>
									);
								}
							}
							if (auditStatus == 1) {
								text = <Tag color='default'>不通过</Tag>;
							}
							return text;
						}}
					/>

					<Table.Column
						title='操作'
						key='option'
						dataIndex='option'
						fixed='right'
						align='center'
						width={130}
						render={(_, record) => {
							return (
								<>
									<Permission
										hasPermi={[
											'bidmgt:projectManage:pendingList:edit',
										]}
									>
										<Button
											type='link'
											size='small'
											onClick={() =>
												openNewTab(
													`/bidmgt/projectManage/curd?id=${record.id}`
												)
											}
										>
											编辑
										</Button>
									</Permission>

									<Permission
										hasPermi={[
											'bidmgt:projectManage:pendingList:delete',
										]}
									>
										<Popconfirm
											title='提示'
											description='确定删除吗？'
											onConfirm={() => {
												handelDel(record.id);
											}}
											okText='确定'
											cancelText='取消'
										>
											<Button
												type='link'
												danger
												size='small'
											>
												删除
											</Button>
										</Popconfirm>
									</Permission>
								</>
							);
						}}
					/>
				</Table>
			</div>
		</div>
	);
};

export default Index;
