import { useEffect, useState } from 'react';
import CryptoJS from 'crypto-js';
import './index.scss';
import { useRouterLink } from '@/hook/useRouter';
import dayjs from 'dayjs';
import axios from 'axios';
import { Menu } from 'antd';
import { qxbEnv } from '@/config/env';

const Index = () => {
	const { linkTo } = useRouterLink();
	const menuList = [
		{
			key: '/recommend/company-recommend-real-time/meeting-dynamic',
			label: '招商情报',
		},
		{
			key: '/recommend/company-recommend/clue',
			label: '招商推荐',
		},
		{
			key: '/capital-investment/capital/financing-dynamic',
			label: '资本招商',
		},
		{
			key: '/promotion-investment/promotion-group',
			label: '榜单招商',
		},
		{
			key: '/supply-chain/dashboard/supply-dashboard',
			label: '供应链招商',
		},
		{
			key: '/regional/regional-investment/map-search',
			label: '区域招商',
		},
		{
			key: '/people/search',
			label: '企业家招商',
		},
		{
			key: '/enterprise-valuation/dashbord',
			label: '一企一档',
		},
		{
			key: '/search/advanced',
			label: '高级搜索',
		},
	];

	const [path, setPath] = useState('');

	const [selectedKeys, setSelectedKeys] = useState([menuList[0].key]);

	const [returnUrl, setReturnUrl] = useState(menuList[0].key);

	const aesEncrypt = (message, keyStr, ivStr) => {
		// 将字符串密钥和IV转换为WordArray
		const key = CryptoJS.enc.Utf8.parse(keyStr);
		const iv = CryptoJS.enc.Utf8.parse(ivStr);

		// 执行AES加密
		const encrypted = CryptoJS.AES.encrypt(message, key, {
			iv: iv,
			mode: CryptoJS.mode.CBC,
			padding: CryptoJS.pad.Pkcs7, // PKCS7Padding，默认已经是这个
		});

		// 返回加密后的Hex字符串
		return encrypted.ciphertext.toString(CryptoJS.enc.Base64);
	};
	const getToken = () => {
		const domain = qxbEnv.tokenDomain || '';
		const nowTime = dayjs().unix();
		const tenant = qxbEnv.tenant || '';
		const account = qxbEnv.account || '';
		const key = qxbEnv.key || '';
		const iv = qxbEnv.iv || '';

		const str = `${tenant}+${account}+${nowTime}`;
		const aesStr = aesEncrypt(str, key, iv);
		const sig = aesStr.replace(/\+/g, '%2B');

		const url = `${domain}/authTokenApi/fetch_token?tenant=${tenant}&sig=${sig}`;

		axios
			.get(url)
			.then((res) => {
				// 获取到token值
				const token = res.data.token;
				console.log('🚀 ~ .then ~ token:', token);
				if (token) {
					// 如果需要的话，拼接路由参数
					const src = `${qxbEnv.srcDomain}/third-login?tenant=${tenant}&token=${token}&returnUrl=${encodeURIComponent(returnUrl)}`;
					setPath(src);
				}
			})
			.catch((err) => {
				console.log(err);
			});
	};

	useEffect(() => {
		getToken();
	}, [returnUrl]);
	return (
		<div className="flex-sub flex align-stretch justify-start">
			<div className="width-120 bg-color-ffffff border-solid-left-e6e6e6 border-solid-right-e6e6e6">
				<Menu
					className="menu-box"
					onClick={(e) => {
						setSelectedKeys(e.key);
						setReturnUrl(e.key);
					}}
					defaultSelectedKeys={selectedKeys}
					selectedKeys={selectedKeys}
					defaultOpenKeys={[]}
					mode="inline"
					items={menuList}
					inlineCollapsed={false}
				/>
			</div>
			<iframe className="flex-sub height-100per" style={{ height: 'calc(100vh - 60px)' }} src={path}></iframe>
		</div>
	);
};

export default Index;
