import { useEffect, useState } from 'react';
import { Select, InputNumber, Form, Button, Input, message, Space } from 'antd';
import { CloseOutlined } from '@ant-design/icons';
import Breadcrumb from '@/components/Breadcrumb';

import { useRouterLink } from '@/hook/useRouter';
import { useIsAdmin } from '@/hook/Bidmgt/useIsAdmin';
import { getCategoryValueList, getInvestmentResponsibleDept } from '@/utils/bigmt';

import { addSituation, detailSituation, updateSituation } from '@/api/Bidmgt/SituationManage/index';

const Index = () => {
	const { isAdmin, deptIds, isInvestment } = useIsAdmin();
	const { linkTo, searchParams } = useRouterLink();
	const id = searchParams.get('id') || '';
	const [form] = Form.useForm();

	const [industrialTrackOptions, setIndustrialTrackOptions] = useState([]);
	const [deptListOptions, setDeptListOptions] = useState([]);

	// 获取选项数据
	const getOptionsData = () => {
		getCategoryValueList('industrial_track').then((listData) => {
			setIndustrialTrackOptions(listData);
		});
		getInvestmentResponsibleDept().then((listData) => {
			setDeptListOptions(listData);
		});
	};

	// 获取详情
	const getDetailData = () => {
		detailSituation({ id }).then((res) => {
			const resData = res.data || {};
			if (resData.evaluationScore) {
				resData.evaluationScore = resData.evaluationScore.replace(/( )?\/( )?1000.*/gi, '');
			}
			if (resData.enterpriseNatureNameList) {
				resData.enterpriseNatureName = resData.enterpriseNatureNameList;
			}
			if (resData.investmentResponsibleList && resData.investmentResponsibleList.length > 0) {
				resData.investmentResponsibleIds = resData.investmentResponsibleList.map((ov) => ov.id);
			}
			form.setFieldsValue(resData);
		});
	};

	// 提交
	const submit = () => {
		form.validateFields()
			.then((values) => {
				(id ? updateSituation : addSituation)({
					...values,
					evaluationScore: values.evaluationScore ? `${values.evaluationScore}/1000` : undefined,
				}).then(() => {
					message.success('提交成功');
					if (isAdmin) {
						linkTo('/bidmgt/SituationManage/list');
					} else {
						linkTo('/bidmgt/SituationManage/pendingList');
					}
				});
			})
			.catch((error) => {
				console.log('🚀 ~ submit ~ error:', error);
				// form.scrollToField(error.name[0]);
			});
	};

	useEffect(() => {
		if (id) {
			getDetailData();
		}
	}, [id]);

	// 招商单位非管理者 新增 默认跟进单位为自身部门
	useEffect(() => {
		if (isInvestment.current && id === '' && !isAdmin) {
			form.setFieldsValue({
				investmentResponsibleIds: deptIds,
			});
		}
	}, [isInvestment.current]);

	useEffect(() => {
		getOptionsData();
	}, []);

	return (
		<div className="project-curd-box full-page-width position-absolute inset-0 flex flex-direction-column margin-auto">
			<div className="flex-sub flex flex-direction-column margin-top-16 padding-lr-20 padding-bottom-20">
				{/* 面包屑 开始 */}
				<Breadcrumb
					icon="icon-situationManage"
					list={[
						{
							name: '商情管理',
							link: '/bidmgt/situationManage/list',
						},
						{
							name: '商情列表',
							link: '/bidmgt/situationManage/list',
						},
					]}
					name={id ? '编辑商情' : '新建商情'}
				/>
				{/* 面包屑 结束 */}

				<div className="flex-sub padding-20 border-radius-4 bg-color-ffffff">
					<Form
						form={form}
						labelCol={{ span: 6 }}
						wrapperCol={{ span: 14 }}
						initialValues={{
							id,
							investmentResponsibleIds: [],
						}}
					>
						<Form.Item hidden name="id">
							<Input placeholder="id 隐藏" />
						</Form.Item>
						<Form.Item
							name="enterpriseName"
							label="企业名称"
							rules={[
								{
									required: true,
								},
							]}
						>
							<Input placeholder="请输入企业名称" />
						</Form.Item>
						<Form.Item name="industrialTrackId" label="所属产业">
							<Select allowClear options={industrialTrackOptions} placeholder="请选择产业赛道" />
						</Form.Item>
						<Form.Item label="企业性质">
							<FormList />
						</Form.Item>
						<Form.Item name="recommendOrgName" label="推荐单位">
							<Input placeholder="请输入推荐单位" />
						</Form.Item>
						<Form.Item name="investmentResponsibleIds" label="跟进单位" hidden={!isInvestment.current}>
							<Select
								disabled={!isAdmin}
								options={deptListOptions}
								mode="multiple"
								placeholder="请选择"
								filterOption={(input, option) => (option?.label ?? '').includes(input)}
							/>
						</Form.Item>
						<Form.Item name="investmentProbability" label="新增投资概率">
							<Select
								allowClear
								options={['高', '中', '低'].map((ov) => ({
									lable: ov,
									value: ov,
								}))}
								placeholder="请选择投资概率"
							/>
						</Form.Item>
						<Form.Item label="综合评分">
							<Space>
								<Form.Item name="evaluationScore" noStyle>
									<InputNumber className="width-120" placeholder="请输入评分" min={0} max={1000} precision={0} />
								</Form.Item>
								<div>/1000</div>
							</Space>
						</Form.Item>
						<Form.Item name="mainBusiness" label="主营业务">
							<Input.TextArea rows={4} placeholder="请输入主营业务" />
						</Form.Item>
						<Form.Item name="enterpriseIntroduction" label="企业简介">
							<Input.TextArea rows={4} placeholder="请输入企业简介" />
						</Form.Item>
						<Form.Item name="contactInformation" label="联系方式">
							<Input placeholder="请输入联系方式" />
						</Form.Item>
						<Form.Item name="contactAddress" label="联系地址">
							<Input placeholder="请输入联系地址" />
						</Form.Item>
						<Form.Item name="enterpriseWebsite" label="企业网址">
							<Input placeholder="请输入企业网址" />
						</Form.Item>
						<Form.Item name="recommendReason" label="推荐理由">
							<Input.TextArea rows={4} placeholder="请输入推荐理由" />
						</Form.Item>
					</Form>
				</div>

				{/* 底部按钮 开始 */}
				<div className="height-72"></div>
				<div className="position-fixed bottom-0 left-0 width-100per bg-color-ffffff">
					<div className="flex justify-center align-center margin-0-auto padding-tb-16 padding-lr-20 width-100per windows-width height-72 border-top-f7f8fa border-box">
						<Button type="primary" onClick={() => submit()}>
							{id ? '保存修改' : '提交'}
						</Button>
					</div>
				</div>
				{/* 底部按钮 结束 */}
			</div>
		</div>
	);
};

const FormList = (props = {}) => {
	const [value, setValue] = useState('');

	const addTag = (add) => {
		if (value) {
			add(value.trim());
			setValue('');
		}
	};
	return (
		<Form.List name="enterpriseNatureName">
			{(fields, { add, remove }) => {
				return (
					<Space direction="vertical" size={16}>
						<Space>
							<Input
								value={value}
								placeholder="请输入企业性质标签"
								onChange={(e) => setValue(e.target.value)}
								onPressEnter={() => {
									addTag(add);
								}}
							/>
							<Button
								type="primary"
								onClick={() => {
									addTag(add);
								}}
							>
								添加
							</Button>
						</Space>
						{fields.length > 0 && (
							<Space wrap size={12}>
								{fields.map((field) => {
									return (
										<Form.Item noStyle {...field}>
											<FormListTag onRemove={() => remove(field.name)} />
										</Form.Item>
									);
								})}
							</Space>
						)}
					</Space>
				);
			}}
		</Form.List>
	);
};

const FormListTag = (props = {}) => {
	return (
		<Space size={12} className="padding-lr-12 line-height-32 border-radius-4 bg-color-e5e6e8">
			<div>{props.value}</div>
			<CloseOutlined className="font-size-12 a" onClick={props.onRemove} />
		</Space>
	);
};

export default Index;
