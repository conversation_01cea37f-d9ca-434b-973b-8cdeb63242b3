import { useEffect, useState } from 'react';
import { Button, Space, Table, Tag, message } from 'antd';
import Breadcrumb from '@/components/Breadcrumb';
import Permission from '@/components/Permission';

import { useRouterLink } from '@/hook/useRouter';
import {
	detailSituation,
	updateReadStatus,
} from '@/api/Bidmgt/SituationManage/index';

const Index = (props = {}) => {
	const { searchParams, linkTo } = useRouterLink();
	const id = searchParams.get('id');
	const toDoListType = searchParams.get('toDoListType');
	const [detail, setDetail] = useState({});

	const detailList = [
		{
			lable: '企业名称',
			keyName: () => detail.enterpriseName || '--',
		},
		{
			lable: '所属产业',
			keyName: () => detail.industrialTrackName || '--',
		},
		{
			lable: '企业性质',
			keyName: () =>
				(detail.enterpriseNatureNameList || [])
					.filter((ov) => ov)
					.join('、') || '--',
		},
		{
			lable: '新增投资概率',
			keyName: () => {
				return (
					(detail.investmentProbability && (
						<Tag
							color='orange-inverse'
							className='padding-lr-8 padding-tb-4 font-size-14'
						>
							{detail.investmentProbability}
						</Tag>
					)) ||
					'--'
				);
			},
		},
		{
			lable: '综合实力评估',
			keyName: () => {
				return (
					(detail.evaluationScore && (
						<Tag
							color='processing'
							className='padding-lr-8 padding-tb-4 font-size-14'
						>
							{detail.evaluationScore}
						</Tag>
					)) ||
					'--'
				);
			},
		},
		{
			lable: '主营业务',
			keyName: () => {
				return detail.mainBusiness || '--';
			},
		},
		{
			lable: '企业简介',
			keyName: () => {
				return detail.enterpriseIntroduction || '--';
			},
		},
		{
			lable: '联系方式',
			keyName: () => {
				return detail.contactInformation || '--';
			},
		},
		{
			lable: '联系地址',
			keyName: () => {
				return detail.contactAddress || '--';
			},
		},
		{
			lable: '企业网址',
			keyName: () => {
				return detail.enterpriseWebsite || '--';
			},
		},
		{
			lable: '推荐单位',
			keyName: () => detail.recommendOrgName || '--',
		},
		{
			lable: '跟进单位',
			keyName: () => {
				return (
					<Space>
						{(detail.investmentResponsibleList || []).map((ov) => {
							return (
								<Space size={4} key={ov.id}>
									<div>{ov.name || ''}</div>
									{(!ov.claimStatus ||
										ov.claimStatus == 0) && (
										<Tag color='warning'>待认领</Tag>
									)}
									{ov.claimStatus == 2 && (
										<Tag color='success'>已确认</Tag>
									)}
								</Space>
							);
						})}
					</Space>
				);
			},
		},
		{
			lable: '推荐时间',
			keyName: () => detail.recommendTime || '--',
		},
	];
	const getDetail = () => {
		detailSituation({ id }).then((res) => {
			const resData = res.data || {};
			setDetail(resData);

			// 如果父级有需要 详情的数据
			if (props.setDetail) {
				props.setDetail(res.data || {});
			}
		});
	};
	useEffect(() => {
		if (id) {
			getDetail();
			// updateReadStatus({ id });

			if (props.reflash) {
				props.reflash.current = getDetail;
			}
		} else {
			message.error('参数错误');
			linkTo(-1);
		}
	}, []);
	return (
		<div className='full-page-width position-relative'>
			<div className='border-radius-4 bg-color-ffffff margin-bottom-16'>
				<div className='padding-lr-20'>
					<div className='flex align-center justify-between border-solid-bottom-f2f3f5  padding-tb-14'>
						<div className='flex align-center justify-start'>
							<div className='width-6 height-16 border-radius-4 bg-color-165dff'></div>
							<div className='font-bold font-size-20 line-height-28 margin-left-8'>
								基本信息
							</div>

							{toDoListType == '3' && detail.auditStatus == 0 && (
								<Tag
									bordered={false}
									color='#ff7d00'
									className='font-size-14 line-height-24 margin-left-12'
								>
									审核中
								</Tag>
							)}
						</div>

						<Permission
							hasPermi={['bidmgt:projectManage:list:edit']}
						>
							{props.OptionCom && <props.OptionCom />}
						</Permission>
					</div>
					<div className='padding-lr-30 line-height-30 padding-top-12'>
						{detailList.map((ov) => {
							return (
								<div
									key={ov.lable}
									className='flex justify-start align-start padding-bottom-12'
								>
									<div className='color-86909c flex-shrink'>
										{ov.lable}：
									</div>
									<div className='flex-sub'>
										<ov.keyName />
									</div>
								</div>
							);
						})}
					</div>
				</div>
			</div>
		</div>
	);
};

export default Index;
