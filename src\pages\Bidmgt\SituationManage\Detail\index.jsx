import { useEffect, useRef, useState } from 'react';
import { Button, Space, Table, Tag, message } from 'antd';
import Breadcrumb from '@/components/Breadcrumb';

import { useRouterLink } from '@/hook/useRouter';
import { acceptOperate } from '@/api/Bidmgt/SituationManage/index';
import DetailComp from './DetailComp';
import { EditOutlined } from '@ant-design/icons';

import Permission from '@/components/Permission';

const Index = () => {
	const { searchParams, linkTo } = useRouterLink();
	const id = searchParams.get('id');
	const toDoListType = searchParams.get('toDoListType');
	const [detail, setDetail] = useState({});
	const reflash = useRef(null);
	// 采纳按钮
	const claimBtn = (acceptStatus = 2) => {
		if (!id) {
			return
		}
		acceptOperate({
			id,
			acceptStatus
		}).then(()=>{
			reflash.current && reflash.current()
		})
	}
	return (
		<div className='full-page-width'>
			<Breadcrumb
				className='margin-top-16'
				icon='icon-competitionManage'
				list={[
					{
						name: '商情管理',
						link: '/bidmgt/situationManage',
					},
				]}
				name={detail.enterpriseName || '商情详情'}
			/>
			<DetailComp
				setDetail={setDetail}
				reflash={reflash}
				OptionCom={() =>
					!toDoListType && detail.acceptStatus == 2 && (
						<Button
							type='primary'
							icon={<EditOutlined />}
							onClick={() =>
								linkTo(
									`/bidmgt/situationManage/curd?id=${detail.id}`
								)
							}
						>
							编辑信息
						</Button>
					)
				}
			/>
			<Permission hasPermi={['bidmgt:situationManage:detail:accept']}>
				{detail.acceptStatus === 0 && (<div className='flex justify-center align-center position-sticky bottom-0 left-0 right-0 bg-color-ffffff padding-20'>
					<Button type="primary" className='width-160'
						onClick={() => claimBtn(2)}
					>
						通过采纳
					</Button>
				</div>)}
			</Permission>
		</div>
	);
};

export default Index;
