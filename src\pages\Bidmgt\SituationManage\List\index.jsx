import { useEffect, useState } from 'react';
import { Button, Space, Table, Popconfirm, Input, Form, Modal } from 'antd';
import { SearchOutlined } from '@ant-design/icons';

import Permission from '@/components/Permission';
import { FilterTitle, FilterOption, FilterRangeDate, FilterChecked } from '@/components/Bidmgt/FilterComp';

import { useRouterLink } from '@/hook/useRouter';
import { useIsAdmin } from '@/hook/Bidmgt/useIsAdmin';

import { getCategoryValueList, getInvestmentResponsibleDept } from '@/utils/bigmt';

import { myDeptProjectIntelligencePage, delSituation } from '@/api/Bidmgt/SituationManage/index';

const Index = () => {
	const { openNewTab, searchParams, setSearchParams } = useRouterLink();

	// 请求参数
	const [params, setParams] = useState({});

	// 页码数据
	const [pagination, setPagination] = useState({
		total: 0,
		pageNum: searchParams.get('pageNum') - 0 || 1,
		pageSize: searchParams.get('pageSize') - 0 || 10,
	});

	const [dataSource, setDataSource] = useState([]);

	// 搜索
	const onSearch = (values, index = 1) => {
		setParams({ ...values });
		if (index !== 1) {
			setPagination({ ...pagination, pageNum: 1 });
		}
	};

	// 获取表格数据
	const getList = () => {
		const { pageNum, pageSize } = pagination;
		const { timeList = [], enterpriseName, industrialTrackIds, recommendOrgIds, investmentResponsibleIds } = params;
		const [startTime, endTime] = timeList;

		myDeptProjectIntelligencePage({
			startTime,
			endTime,
			enterpriseName,
			industrialTrackIds,
			recommendOrgIds,
			investmentResponsibleIds,
			auditStatus: 2,
			pageNum,
			pageSize,
		}).then((res) => {
			const list = res.data.records || [];
			setDataSource(list);
			setPagination({
				...pagination,
				total: res.data.total - 0,
			});
			setSearchParams(
				{
					pageNum,
					pageSize,
				},
				{ replace: true }
			);
		});
	};

	// 删除
	const handelDel = (id = '') => {
		if (id === '') {
			Modal.confirm({
				title: '确认删除',
				content: '确认删除选中数据吗？',
				onOk() {
					batchDel(selectedRowKeys);
				},
			});
		} else {
			batchDel([id]);
		}
	};

	// 批量删除
	const batchDel = (ids) => {
		delSituation({
			ids,
		}).then(() => {
			const { pageNum, pageSize } = pagination;
			if (pageNum > 1 && pageNum > Math.ceil((total - ids.length) / pageSize)) {
				setPagination({ ...pagination, pageNum: pageNum - 1 });
			} else {
				getList();
			}
			message.success('删除成功');
		});
	};

	useEffect(() => {
		if (Object.keys(params).length > 0) {
			getList();
		}
	}, [pagination.pageNum, pagination.pageSize, params]);

	return (
		<div className="flex-sub flex flex-direction-column padding-20">
			{/* 搜索 开始 */}
			<SearchFilter onSearch={onSearch} />
			{/* 搜索 结束 */}

			<div className="flex-sub margin-top-16 padding-20 bg-color-ffffff border-radius-4">
				{/* 操作按钮 开始 */}
				<div className="margin-bottom-16 flex align-center justify-between">
					<div>
						查询到
						<span className="color-f53f3f">{pagination.total}</span>
						个项目
					</div>
					<Permission hasPermi={['bidmgt:situationManage:list:add']}>
						<Button type="primary" className="width-100" onClick={() => openNewTab(`/bidmgt/situationManage/curd`)}>
							新建商情
						</Button>
					</Permission>
				</div>
				{/* 操作按钮 结束 */}

				{/* 表格 开始 */}
				<Table
					size="small"
					rowKey="id"
					dataSource={dataSource}
					onChange={(e) => {
						setPagination({
							...pagination,
							pageSize: e.pageSize,
							pageNum: e.current,
						});
					}}
					pagination={{
						total: pagination.total,
						pageSize: pagination.pageSize,
						current: pagination.pageNum,
						size: 'default',
						showTotal: (total) => `共 ${total} 条`,
					}}
					scroll={{ x: 'max-content' }}
				>
					<Table.Column
						title="序号"
						key="index"
						width={60}
						fixed="left"
						align="center"
						render={(_, __, index) => {
							return index + 1;
						}}
					/>
					<Table.Column title="招商企业" key="enterpriseName" dataIndex="enterpriseName" fixed="left" />
					<Table.Column title="所属产业" key="industrialTrackName" dataIndex="industrialTrackName" render={(text) => text || ''} />
					<Table.Column
						title="企业性质"
						key="enterpriseNatureNameList"
						dataIndex="enterpriseNatureNameList"
						render={(enterpriseNatureNameList) => {
							return (
								<Space direction="vertical" size={4}>
									{(enterpriseNatureNameList || []).map((ov, oi) => {
										return <div key={oi}>{ov}</div>;
									})}
								</Space>
							);
						}}
					/>
					<Table.Column title="推荐单位" key="recommendOrgName" dataIndex="recommendOrgName" render={(text) => text || ''} />
					<Table.Column
						title="跟进单位"
						key="investmentResponsibleList"
						dataIndex="investmentResponsibleList"
						render={(text) => (text.length ? text.map((ov) => <div key={ov.id}>{ov.name}</div>) : '--')}
					/>
					<Table.Column title="推荐时间" key="recommendTime" dataIndex="recommendTime" />
					<Table.Column
						title="操作"
						key="option"
						dataIndex="option"
						fixed="right"
						align="center"
						width={170}
						render={(_, record) => {
							return (
								<Space>
									<Button type="link" size="small" onClick={() => openNewTab(`/bidmgt/situationManage/detail?id=${record.id}`)}>
										查看
									</Button>
									<Permission hasPermi={['bidmgt:situationManage:list:edit']}>
										<Button type="link" size="small" onClick={() => openNewTab(`/bidmgt/situationManage/curd?id=${record.id}`)}>
											编辑
										</Button>
									</Permission>
									<Permission hasPermi={['bidmgt:situationManage:list:delete']}>
										<Popconfirm
											title="提示"
											description="确定删除吗？"
											onConfirm={() => {
												handelDel(record.id);
											}}
											okText="确定"
											cancelText="取消"
										>
											<Button type="link" danger size="small">
												删除
											</Button>
										</Popconfirm>
									</Permission>
								</Space>
							);
						}}
					/>
				</Table>
				{/* 表格 结束 */}
			</div>
		</div>
	);
};

// 筛选条件
const SearchFilter = (props = {}) => {
	const { isAdmin, deptIds } = useIsAdmin();
	const [form] = Form.useForm();

	// 筛选数据
	const [industrialTrackOptions, setIndustrialTrackOptions] = useState([]);
	const [deptListOptions, setDeptListOptions] = useState([]);

	// 选中数据
	const [checkedList, setCheckedList] = useState([]);
	// 提交标记
	const [submitSign, setSubmitSign] = useState(0);

	// 提交
	const submit = () => {
		setSubmitSign(submitSign + 1);
	};

	// 重置
	const resetFilter = () => {
		form.resetFields();
		setCheckedList([]);
		submit();
	};

	// 获取筛选数据
	const getFilterData = () => {
		getCategoryValueList('industrial_track').then((listData) => {
			setIndustrialTrackOptions(listData);
		});
		getInvestmentResponsibleDept().then((listData) => {
			setDeptListOptions(listData);
		});
	};

	useEffect(() => {
		// 回显数据
		const checkedList = JSON.parse(sessionStorage.getItem('checkedListSituation') || '[]');
		const formData = JSON.parse(sessionStorage.getItem('formDataSituation') || '{}');

		if (checkedList.length > 0) {
			setCheckedList(checkedList);
		}
		if (Object.keys(formData).length > 0) {
			form.setFieldsValue(formData);
		}
		getFilterData();
		submit();
	}, []);

	useEffect(() => {
		if (submitSign) {
			const values = form.getFieldsValue();
			sessionStorage.setItem('checkedListSituation', JSON.stringify(checkedList));
			sessionStorage.setItem('formDataSituation', JSON.stringify(values));
			const params = {};
			for (let i in values) {
				if (!Array.isArray(values[i]) || values[i].length > 0) {
					params[i] = values[i];
				}
			}
			props.onSearch && props.onSearch(params, submitSign);
		}
	}, [submitSign]);

	return (
		<Form
			form={form}
			initialValues={{
				industrialTrackIds: [],
				recommendOrgIds: [],
				investmentResponsibleIds: [],
			}}
		>
			<div className="border-radius-4 bg-color-ffffff">
				<div className="flex align-center justify-between padding-lr-20 padding-top-16">
					<div className="font-size-16 font-weight-500 color-1d2129 line-height-24">商情管理</div>
					<div className="width-220">
						<Form.Item noStyle name="enterpriseName">
							<Input placeholder="请输入企业关提词搜素" suffix={<SearchOutlined onClick={submit} />} onPressEnter={submit} />
						</Form.Item>
					</div>
				</div>
				<div className="padding-lr-20 padding-tb-20">
					{checkedList.length > 0 && (
						<FilterTitle title={<div className="font-bold color-165dff">已选条件</div>}>
							<FilterChecked form={form} checkedList={checkedList} setCheckedList={setCheckedList} />
						</FilterTitle>
					)}
					<FilterTitle title="所属产业">
						<Form.Item noStyle name="industrialTrackIds">
							<FilterOption
								name="所属产业"
								options={industrialTrackOptions}
								checkedList={checkedList}
								setCheckedList={setCheckedList}
							/>
						</Form.Item>
					</FilterTitle>
					{/* <FilterTitle title='推荐单位'>
						<Form.Item noStyle name='recommendOrgIds'>
							<FilterOption
								name='推荐单位'
								options={deptListOptions}
								checkedList={checkedList}
								setCheckedList={setCheckedList}
							/>
						</Form.Item>
					</FilterTitle> */}
					{isAdmin && (
						<FilterTitle title="跟进单位">
							<Form.Item noStyle name="investmentResponsibleIds">
								<FilterOption name="跟进单位" options={deptListOptions} checkedList={checkedList} setCheckedList={setCheckedList} />
							</Form.Item>
						</FilterTitle>
					)}
					{/* <FilterTitle title='推荐时间' className='margin-top-12'>
						<Form.Item noStyle name='timeList'>
							<FilterRangeDate />
						</Form.Item>
					</FilterTitle> */}
				</div>
				<div className="flex justify-end padding-lr-20 padding-tb-12 border-solid-top-e5e6e8">
					<Space size={20}>
						<Button className="width-100" onClick={resetFilter}>
							重置筛选
						</Button>
						<Button className="width-100" type="primary" onClick={submit}>
							查询
						</Button>
					</Space>
				</div>
			</div>
		</Form>
	);
};

export default Index;
