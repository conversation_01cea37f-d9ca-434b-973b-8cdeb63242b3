import { useEffect, useState } from 'react';
import { Button, Space, Table, Modal, Input, Popconfirm, Tabs, Tag } from 'antd';
import { SearchOutlined } from '@ant-design/icons';

import Permission from '@/components/Permission';

import { useRouterLink } from '@/hook/useRouter';
import { myDeptProjectIntelligencePage, delSituation } from '@/api/Bidmgt/SituationManage/index';

const items = [
	{
		key: '0',
		label: '审核中',
		children: null,
	},
	{
		key: '1',
		label: '不通过',
		children: null,
	},
];

const Index = () => {
	const { openNewTab, searchParams, setSearchParams } = useRouterLink();

	const [tabIndex, setTabIndex] = useState(searchParams.get('tabIndex') || '0');
	const [enterpriseName, setEnterpriseName] = useState(searchParams.get('enterpriseName') || '');

	const onTabsChange = (key) => {
		if (key !== tabIndex) {
			pagination.pageNum = 1;
			pagination.total = 0;
			setDataSource([]);
			setPagination({ ...pagination });
			setTabIndex(key);
		}
	};

	// 页码数据
	const [pagination, setPagination] = useState({
		total: 0,
		pageNum: searchParams.get('pageNum') - 0 || 1,
		pageSize: searchParams.get('pageSize') - 0 || 10,
	});

	const [dataSource, setDataSource] = useState([]);

	// 获取表格数据
	const getList = () => {
		const { pageNum, pageSize } = pagination;
		myDeptProjectIntelligencePage({
			enterpriseName,
			auditStatus: tabIndex - 0,
			pageNum,
			pageSize,
		}).then((res) => {
			const list = res.data.records || [];
			setDataSource(list);
			setPagination({
				...pagination,
				total: res.data.total - 0,
			});
			setSearchParams(
				{
					pageNum,
					pageSize,
				},
				{ replace: true }
			);
		});
	};

	// 删除
	const handelDel = (id = '') => {
		if (id === '') {
			Modal.confirm({
				title: '确认删除',
				content: '确认删除选中数据吗？',
				onOk() {
					batchDel(selectedRowKeys);
				},
			});
		} else {
			batchDel([id]);
		}
	};

	// 批量删除
	const batchDel = (ids) => {
		delSituation({
			ids,
		}).then(() => {
			const { pageNum, pageSize } = pagination;
			if (pageNum > 1 && pageNum > Math.ceil((total - ids.length) / pageSize)) {
				setPagination({ ...pagination, pageNum: pageNum - 1 });
			} else {
				getList();
			}
			message.success('删除成功');
		});
	};

	useEffect(() => {
		getList();
	}, [pagination.pageNum, pagination.pageSize, tabIndex]);

	return (
		<div className="project-box flex-sub flex flex-direction-column padding-20">
			<div className="padding-20 flex-sub bg-color-ffffff border-radius-4">
				<div className="flex align-center justify-between margin-bottom-20">
					<div className="flex-sub font-bold font-size-16">待发布商情</div>
					<Input
						className="width-220"
						placeholder="请输入关键词搜素"
						onInput={(e) => {
							setEnterpriseName(e.target.value || '');
						}}
						onPressEnter={() => {
							getList(1, 10);
						}}
						suffix={
							<SearchOutlined
								onClick={() => {
									getList(1, 10);
								}}
							/>
						}
					/>
				</div>
				<Tabs
					activeKey={tabIndex}
					items={items}
					onChange={onTabsChange}
					tabBarExtraContent={
						<Permission hasPermi={['bidmgt:situationManage:pendingList:add']}>
							<Button type="primary" className="width-100" onClick={() => openNewTab(`/bidmgt/situationManage/curd`)}>
								新建商情
							</Button>
						</Permission>
					}
				/>

				{/* 表格 开始 */}
				<Table
					size="small"
					rowKey="id"
					dataSource={dataSource}
					onChange={(e) => {
						setPagination({
							...pagination,
							pageSize: e.pageSize,
							pageNum: e.current,
						});
					}}
					pagination={{
						total: pagination.total,
						pageSize: pagination.pageSize,
						current: pagination.pageNum,
						size: 'default',
						showTotal: (total) => `共 ${total} 条`,
					}}
					scroll={{ x: 'max-content' }}
				>
					<Table.Column
						title="序号"
						key="index"
						width={60}
						fixed="left"
						align="center"
						render={(_, __, index) => {
							return index + 1;
						}}
					/>
					<Table.Column title="招商企业" key="enterpriseName" dataIndex="enterpriseName" fixed="left" />
					<Table.Column title="所属产业" key="industrialTrackName" dataIndex="industrialTrackName" render={(text) => text || ''} />
					<Table.Column
						title="企业性质"
						key="enterpriseNatureNameList"
						dataIndex="enterpriseNatureNameList"
						render={(enterpriseNatureNameList) => {
							return (
								<Space direction="vertical" size={4}>
									{(enterpriseNatureNameList || []).map((ov, oi) => {
										return <div key={oi}>{ov}</div>;
									})}
								</Space>
							);
						}}
					/>
					<Table.Column title="推荐单位" key="recommendOrgName" dataIndex="recommendOrgName" render={(text) => text || ''} />
					<Table.Column
						title="发布状态"
						key="auditStatus"
						dataIndex="auditStatus"
						render={(auditStatus, record) => {
							let text = '';
							if (auditStatus == 0) {
								if (record.projectAuditProcessLevel == 2 && record.auditProcessLevel == 2) {
									text = <Tag color="processing">待商务局审核</Tag>;
								}
								if (record.projectAuditProcessLevel == 2 && record.auditProcessLevel == 1) {
									text = <Tag color="warning">待分管领导审核</Tag>;
								}

								if (record.projectAuditProcessLevel == 1) {
									text = <Tag color="processing">待商务局审核</Tag>;
								}
							}
							if (auditStatus == 1) {
								text = <Tag color="default">不通过</Tag>;
							}
							return text;
						}}
					/>
					<Table.Column
						title="操作"
						key="option"
						dataIndex="option"
						fixed="right"
						align="center"
						width={170}
						render={(_, record) => {
							return (
								<Space>
									<Button type="link" size="small" onClick={() => openNewTab(`/bidmgt/situationManage/detail?id=${record.id}`)}>
										查看
									</Button>
									<Permission hasPermi={['bidmgt:situationManage:pendingList:edit']}>
										<Button type="link" size="small" onClick={() => openNewTab(`/bidmgt/situationManage/curd?id=${record.id}`)}>
											编辑
										</Button>
									</Permission>
									<Permission hasPermi={['bidmgt:situationManage:pendingList:delete']}>
										<Popconfirm
											title="提示"
											description="确定删除吗？"
											onConfirm={() => {
												handelDel(record.id);
											}}
											okText="确定"
											cancelText="取消"
										>
											<Button type="link" danger size="small">
												删除
											</Button>
										</Popconfirm>
									</Permission>
								</Space>
							);
						}}
					/>
				</Table>
				{/* 表格 结束 */}
			</div>
		</div>
	);
};

export default Index;
