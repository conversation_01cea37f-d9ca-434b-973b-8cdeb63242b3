import { Col, Row, Button, Input, Steps, Pagination, Tag, Modal } from 'antd';
import { getImageSrc } from '@/assets/images/index';
import { useRouterLink } from '@/hook/useRouter';
import { useEffect, useState, useRef } from 'react';
import {
	claimProjectAssign,
	auditProject,
	checkAuditPermission,
	projectIntelligenceCheckAuditPermission,
} from '@/api/Bidmgt/PersonalCenter/index';
import { updateTaskCenterReadFlag } from '@/api/Bidmgt/Dashboard/index';
import {
	claimProjectIntelligenceAssign,
	auditProjectIntelligence,
} from '@/api/Bidmgt/SituationManage/index';
import './index.scss';
import ProjectDetailComp from '@/pages/Bidmgt/ProjectManage/Detail/DetailComp';
import SituationDetailComp from '@/pages/Bidmgt/SituationManage/Detail/DetailComp';
import Breadcrumb from '@/components/Breadcrumb';
import { useSelector } from 'react-redux';

const Index = () => {
	const { linkTo, searchParams } = useRouterLink();
	const id = searchParams.get('id');
	const todoId = searchParams.get('todoId');

	// 登录凭证
	const userInfo = useSelector((state) => {
		return state.user.userInfo;
	});

	const userDeptIds = (userInfo.deptList || []).map((ov) => ov.id);

	// {/* 待办事项类型：1 项目认领 2 项目审核 3 商情审核 4 商情认领  */ }
	const toDoListType = searchParams.get('toDoListType');

	const reflash = useRef(null);
	const [detail, setDetail] = useState({});

	const auditDesc = useRef('');
	const { confirm } = Modal;
	// 审核 按钮
	const auditBtn = (auditStatus) => {
		submitAuidt(auditStatus).then(() => {
			if (toDoListType == 2) {
				auditTipsToDoListType2(auditStatus);
			}
			if (toDoListType == 3) {
				auditTipsToDoListType3(auditStatus);
			}
		});
	};
	// 审核 提交
	const submitAuidt = (status) => {
		return new Promise((resolve) => {
			// 项目
			if (toDoListType == 2) {
				auditProject({
					auditStatus: status,
					id: todoId,
					auditDesc: auditDesc.current,
				}).then((res) => {
					reflash.current && reflash.current();
					setReflashTime(new Date().valueOf());
					resolve();
				});
			}
			// 商情
			if (toDoListType == 3) {
				auditProjectIntelligence({
					operateStatus: status,
					id: todoId,
					auditDesc: auditDesc.current,
				}).then((res) => {
					reflash.current && reflash.current();
					setReflashTime(new Date().valueOf());
					resolve();
				});
			}
		});
	};
	// 项目审核 提示文案
	const auditTipsToDoListType2 = (auditStatus) => {
		if (auditStatus == 2) {
			let content = '';
			if (detail.projectAuditProcessLevel == 1) {
				content = '项目正式录入系统';
			}
			if (detail.projectAuditProcessLevel == 2) {
				if (detail.auditProcessLevel == 2) {
					content = '项目正式录入系统';
				} else {
					content = '项目将进入商务局审批流程';
				}
			}
			Modal.success({
				closable: true,
				okText: '确定',
				title: '已通过审批',
				content: content,
				onOk() {},
				onCancel() {},
			});
		} else {
			let content = '';
			if (detail.projectAuditProcessLevel == 1) {
				content = '项目将被退回属地单位';
			}
			if (detail.projectAuditProcessLevel == 2) {
				if (detail.auditProcessLevel == 2) {
					content = '项目将被退回属地单位';
				} else {
					content = '项目将被退回';
				}
			}
			Modal.error({
				closable: true,
				okText: '确定',
				title: '不通过',
				content: content,
				onOk() {},
				onCancel() {},
			});
		}
	};

	// 商情审核 提示文案
	const auditTipsToDoListType3 = (auditStatus) => {
		if (auditStatus == 2) {
			let content = '';
			if (detail.projectAuditProcessLevel == 1) {
				content = '商情正式录入系统';
			}
			if (detail.projectAuditProcessLevel == 2) {
				if (detail.auditProcessLevel == 2) {
					content = '商情正式录入系统';
				} else {
					content = '商情将进入商务局审批流程';
				}
			}
			Modal.success({
				closable: true,
				okText: '确定',
				title: '已通过审批',
				content: content,
				onOk() {},
				onCancel() {},
			});
		} else {
			let content = '';
			if (detail.projectAuditProcessLevel == 1) {
				content = '商情将被退回属地单位';
			}
			if (detail.projectAuditProcessLevel == 2) {
				if (detail.auditProcessLevel == 2) {
					content = '商情将被退回属地单位';
				} else {
					content = '商情将被退回';
				}
			}
			Modal.error({
				closable: true,
				okText: '确定',
				title: '不通过',
				content: content,
				onOk() {},
				onCancel() {},
			});
		}
	};

	const claimDesc = useRef('');
	// 认领 按钮
	const claimBtn = (claimStatus) => {
		claimAssign(claimStatus).then(() => {
			if (toDoListType == 1) {
				claimAssignTipsToDoListType1(claimStatus, () => {
					linkTo(-1);
				});
			}
			if (toDoListType == 4) {
				claimAssignTipsToDoListType4(claimStatus, () => {
					linkTo(-1);
				});
			}
		});
	};
	// 认领
	const claimAssign = (status) => {
		return new Promise((resolve) => {
			// 项目
			if (toDoListType == 1) {
				claimProjectAssign({
					claimStatus: status,
					id: todoId,
					claimDesc: claimDesc.current,
				}).then((res) => {
					reflash.current && reflash.current();
					setReflashTime(new Date().valueOf());
					resolve();
				});
			}
			// 商情
			if (toDoListType == 4) {
				claimProjectIntelligenceAssign({
					operateStatus: status,
					id: todoId,
					claimDesc: claimDesc.current,
				}).then((res) => {
					reflash.current && reflash.current();
					setReflashTime(new Date().valueOf());
					resolve();
				});
			}
		});
	};

	// 项目认领 提示文案
	const claimAssignTipsToDoListType1 = (claimStatus, callback = () => {}) => {
		if (claimStatus == 2) {
			let content = `将您所在单位设为该项目招商责任单位`;
			Modal.success({
				closable: true,
				okText: '确定',
				title: '认领成功',
				content: content,
				onOk() {
					callback();
				},
				onCancel() {},
			});
		} else {
			let content = '该项目将退回商务局';
			Modal.error({
				closable: true,
				okText: '确定',
				title: '已退回该项目',
				content: content,
				onOk() {
					callback();
				},
				onCancel() {},
			});
		}
	};
	// 商情认领 提示文案
	const claimAssignTipsToDoListType4 = (claimStatus, callback = () => {}) => {
		if (claimStatus == 2) {
			let content = `将您所在单位设为该商情责任单位`;
			Modal.success({
				closable: true,
				okText: '确定',
				title: '认领成功',
				content: content,
				onOk() {
					callback();
				},
				onCancel() {},
			});
		} else {
			let content = '该商情将退回商务局';
			Modal.error({
				closable: true,
				okText: '确定',
				title: '已退回该商情',
				content: content,
				onOk() {
					callback();
				},
				onCancel() {},
			});
		}
	};

	const [reflashTime, setReflashTime] = useState(new Date().valueOf());

	const [showAuditBtn, setShowAuditBtn] = useState(false);

	useEffect(() => {
		if (toDoListType == 2) {
			checkAuditPermission({ id }).then((res) => {
				setShowAuditBtn(res.data || false);
			});
		}
		if (toDoListType == 3) {
			projectIntelligenceCheckAuditPermission({ id }).then((res) => {
				setShowAuditBtn(res.data || false);
			});
		}
		updateTaskCenterReadFlag({
			id: todoId,
			toDoListType: toDoListType,
		});
	}, [reflashTime]);
	return (
		<div className='flex-sub full-page-width padding-top-16'>
			{/* 面包屑 开始 */}
			<Breadcrumb
				icon='icon-projectManage'
				list={[
					{
						name: '待办事项',
						link: '/bidmgt/taskCenter/list',
					},
				]}
				name='任务详情'
			/>
			{/* 面包屑 结束 */}
			{/* 1 项目认领 2 项目审核  开始 */}
			{['1', '2'].includes(`${toDoListType}`) && (
				<ProjectDetailComp setDetail={setDetail} reflash={reflash} />
			)}
			{/* 1 项目认领 2 项目审核  结束 */}

			{/* 3 商情审核 4 商情认领  开始 */}
			{['3', '4'].includes(`${toDoListType}`) && (
				<SituationDetailComp setDetail={setDetail} reflash={reflash} />
			)}
			{/* 3 商情审核 4 商情认领  结束 */}

			<div className='padding-30'></div>

			{showAuditBtn && ['2', '3'].includes(`${toDoListType}`) && (
				<div className='flex justify-center align-center position-sticky bottom-0 left-0 right-0 bg-color-ffffff padding-20'>
					<Button
						type='primary'
						className='width-160'
						onClick={() => auditBtn(2)}
					>
						通过
					</Button>
					<Button
						ype='primary'
						className='width-160 margin-left-20'
						danger
						onClick={() => auditBtn(1)}
					>
						不通过
					</Button>
				</div>
			)}

			{detail.investmentResponsibleList &&
				detail.investmentResponsibleList.length &&
				!!detail.investmentResponsibleList
					.filter((ov) => {
						return !ov.claimStatus || ov.claimStatus == 0;
					})
					.find((ov) => userDeptIds.includes(ov.id)) &&
				['1', '4'].includes(`${toDoListType}`) && (
					<div className='flex justify-center align-center position-sticky bottom-0 left-0 right-0 bg-color-ffffff padding-20'>
						<Button
							type='primary'
							className='width-160'
							onClick={() => claimBtn(2)}
						>
							接受认领
						</Button>
						<Button
							ype='primary'
							className='width-160 margin-left-20'
							danger
							onClick={() => claimBtn(1)}
						>
							拒绝认领
						</Button>
					</div>
				)}
		</div>
	);
};

export default Index;
