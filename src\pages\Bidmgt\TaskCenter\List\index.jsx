import {
	Col,
	Card,
	Row,
	Progress,
	Tabs,
	Table,
	Button,
	Tag,
	Avatar,
	Space,
	Input,
	Modal,
} from 'antd';
import { getImageSrc } from '@/assets/images/index';
import { UserOutlined } from '@ant-design/icons';
import {
	useEffect,
	useState,
	useRef,
	useImperativeHandle,
	forwardRef,
} from 'react';
import { useSelector, useDispatch } from 'react-redux';
import { pageProjectFollow } from '@/api/Bidmgt/ProjectManage/index';
import { useRouterLink } from '@/hook/useRouter';
import { taskCenterPage, taskCenterStatistics } from '@/api/Bidmgt/Dashboard/index';

const Index = () => {
	const { linkTo, searchParams, setSearchParams } = useRouterLink();
	// 登录凭证
	const userInfo = useSelector((state) => {
		return state.user.userInfo;
	});

	// 	待办事项类型：1 项目认领 2 项目审核 3 商情审核 4 商情认领 5 已处理任务 6全部待处理
	const [tabsKey, setTabsKey] = useState(searchParams.get('tabIndex') || '6');
	const onTabsChange = (key) => {
		pagination.pageSize = 10;
		pagination.pageNum = 1;
		setTabsKey(key);
	};

	const [dataSource, setDataSource] = useState([]);
	const [total, setTotal] = useState(0);

	// 页码数据
	const [pagination, setPagination] = useState({
		pageNum: 1,
		pageSize: 10,
	});

	// 页码页数改变
	const changePage = (pageNum, pageSize) => {
		pagination.pageSize = pageSize;
		pagination.pageNum = pageNum;
		setPagination({ ...pagination });
	};

	const getList = () => {
		taskCenterPage({
			toDoListType: tabsKey,
			// operateStatus: tabsKey == 5 ? 1 : 0,
			pageNum: pagination.pageNum,
			pageSize: pagination.pageSize,
		}).then((res) => {
			setDataSource(res.data.records || []);
			setTotal(res.data.total - 0);
		});
	};

	const [statistics, setStatistics] = useState({});
	const getTaskCenterStatistics = () => {
		taskCenterStatistics().then((res) => {
			setStatistics(res.data || {});
		});
	};
	useEffect(() => {
		getList();
		getTaskCenterStatistics();
		setSearchParams({
			tabIndex: tabsKey,
		});
	}, [pagination, tabsKey]);
	return (
		<div className='flex-sub flex flex-direction-column padding-20'>
			<div className='padding-lr-20 padding-tb-10 flex-sub bg-color-ffffff border-radius-4'>
				<div className='font-size-18 font-bold padding-bottom-20 padding-top-10'>
					待办事项
				</div>
				<Tabs
					defaultActiveKey={tabsKey}
					items={[
						{
							key: '6',
							label: `全部待处理(${statistics.totalAmount || 0})`,
						},
						{
							key: '1',
							label: `项目认领(${
								statistics.projectClaimCount || 0
							})`,
						},
						{
							key: '2',
							label: `项目审核(${
								statistics.projectAuditCount || 0
							})`,
						},
						{
							key: '3',
							label: `商情审核(${
								statistics.projectIntelligenceAuditCount || 0
							})`,
						},
						{
							key: '4',
							label: `商情认领(${
								statistics.projectIntelligenceClaimCount || 0
							})`,
						},
						// {
						// 	key: '5',
						// 	label: `已处理任务(${statistics.processedCount || 0})`,
						// },
					]}
					onChange={onTabsChange}
				/>

				<Table
					rowKey='id'
					dataSource={dataSource}
					pagination={{
						...pagination,
						current: pagination.pageNum,
						total,
						showTotal: (total) => `共 ${total} 条`,
					}}
					onChange={(e) => changePage(e.current, e.pageSize)}
				>
					<Table.Column
						title='序号'
						key='index'
						width={60}
						render={(_, record, index) => {
							return index + 1;
						}}
					/>
					<Table.Column
						title='任务类型'
						key='toDoListType'
						dataIndex='toDoListType'
						width={190}
						render={(toDoListType) => {
							// 待办事项类型：1 项目认领 2 项目审核 3 商情审核 4 商情认领 5 已处理任务 6全部待处理
							return (
								<>
									{toDoListType == 1 ? '项目认领' : ''}
									{toDoListType == 2 ? '项目审核' : ''}
									{toDoListType == 4 ? '商情审核' : ''}
									{toDoListType == 3 ? '商情认领' : ''}
								</>
							);
						}}
					/>
					<Table.Column
						title='任务内容'
						key='toDoListContentDesc'
						dataIndex='toDoListContentDesc'
						render={(toDoListContentDesc, record) => {
							// 待办事项类型：1 项目认领 2 项目审核 3 商情审核 4 商情认领 5 已处理任务 6全部待处理
							return (
								<>
									【{record.toDoListType == 1 ? '待认领' : ''}
									{record.toDoListType == 2 ? '待审核' : ''}
									{record.toDoListType == 4 ? '待认领' : ''}
									{record.toDoListType == 3 ? '待审核' : ''}】
									{toDoListContentDesc || ''}
								</>
							);
						}}
					/>

					<Table.Column
						title='任务来源'
						key='createDeptName'
						dataIndex='createDeptName'
					/>
					<Table.Column
						title='发起时间'
						key='createTime'
						dataIndex='createTime'
						width={160}
					/>
					<Table.Column
						title='操作'
						key='option'
						dataIndex='option'
						align='center'
						width={140}
						fixed='right'
						render={(_, record) => {
							return (
								<>
									<Button
										type='link'
										size='small'
										onClick={() =>
											linkTo(
												`/bidmgt/taskCenter/detail?id=${record.businessId}&toDoListType=${record.toDoListType}&todoId=${record.id}`
											)
										}
									>
										{/* 待办事项类型：1 项目认领 2 项目审核 3 商情审核 4 商情认领 5 已处理任务 6全部待处理 */}
										{record.toDoListType == 1
											? '去认领'
											: ''}
										{record.toDoListType == 2
											? '去审核'
											: ''}
										{record.toDoListType == 4
											? '去认领'
											: ''}
										{record.toDoListType == 3
											? '去审核'
											: ''}
									</Button>
								</>
							);
						}}
					/>
				</Table>
			</div>
		</div>
	);
};

export default Index;
