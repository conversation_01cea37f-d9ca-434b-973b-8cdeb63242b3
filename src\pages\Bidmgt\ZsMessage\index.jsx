import { List } from 'antd';
import { getImageSrc } from '@/assets/images/index';
import { useEffect, useState } from 'react';
import { notificationPage, updateReadFlag } from '@/api/Bidmgt/PersonalCenter/index';
import { useRouterLink } from '@/hook/useRouter';

const textObj = {
	4: '去处理',
	5: '去审核',
	10: '去处理',
	11: '去审核',
};

const Index = () => {
	const { openNewTab } = useRouterLink();
	const [dataSource, setDataSource] = useState([]);
	const [total, setTotal] = useState(0);

	// 页码数据
	const [pagination, setPagination] = useState({
		pageNum: 1,
		pageSize: 10,
	});

	// 页码页数改变
	const changePage = (pageNum, pageSize) => {
		pagination.pageSize = pageSize;
		pagination.pageNum = pageNum;
		setPagination({ ...pagination });
	};

	const getList = () => {
		notificationPage({
			pageNum: pagination.pageNum,
			pageSize: pagination.pageSize,
		}).then((res) => {
			setDataSource(res.data.records || []);
			setTotal(res.data.total - 0);
		});
	};

	// 跳转详情
	// 预警类型：1预警 2催办 3消息 4 指派消息 5 项目审核消息 6 审核通过消息 7 审核不通过消息 8 接受认领消息 9 拒绝认领消息 10 商情指派消息 11 商情项目审核消息 12 商情审核通过消息 13 商情审核不通过消息 14 商情接受认领消息 15 商情拒绝认领消息
	// toDoListType 1.项目认领 2.项目审核 3.商情审核 4.商情认领
	const todoDetailByType = (noticeInfo = {}) => {
		const { id, readFlag, projectId, noticeType } = noticeInfo;
		let path = '';
		let toDoListType = '';

		if ([4, 5].includes(noticeType)) {
			path = '/bidmgt/taskCenter/detail';
			toDoListType = noticeType === 4 ? 1 : 2;
		} else if ([10, 11].includes(noticeType)) {
			path = '/bidmgt/taskCenter/detail';
			toDoListType = noticeType === 10 ? 4 : 3;
		} else {
			path = '/bidmgt/projectManage/detail';
		}

		openNewTab(`${path}?id=${projectId}&toDoListType=${toDoListType}`);
	};

	const updateRead = (data = {}) => {
		console.log('🚀 ~ updateRead ~ data:', data);
		const { id, readFlag } = data;
		// 已读处理
		if (readFlag === 0) {
			updateReadFlag({
				id: id,
				readFlag: 1,
			}).then(() => {
				getList();
			});
		}
	};

	useEffect(() => {
		getList();
	}, [pagination]);
	return (
		<div className="flex-sub flex flex-direction-column padding-tb-20 full-page-width">
			<div className="padding-lr-20 padding-tb-10 flex-sub bg-color-ffffff border-radius-4">
				<div className="font-size-18 font-bold padding-bottom-20 padding-top-10">消息通知</div>
				<List
					rowKey="id"
					dataSource={dataSource}
					pagination={{
						...pagination,
						current: pagination.pageNum,
						total,
						showTotal: (total) => `共 ${total} 条`,
						onChange: (page) => {
							changePage(page, pagination.pageSize);
						},
					}}
					renderItem={(item) => (
						<List.Item>
							<div
								className="flex"
								onClick={() => {
									updateRead(item);
								}}
							>
								<div className="widht-50 height-50 position-relative">
									<img
										src={getImageSrc('@/assets/images/PageHeaderBox/icon-message-list.png')}
										alt="msg"
										className="widht-50 height-50"
									/>
									{item.readFlag == 0 && (
										<div
											className="width-8 height-8 border-radius-10 bg-color-f53f3f position-absolute"
											style={{ right: '-4px', top: '-4px' }}
										></div>
									)}
								</div>
								<div className="flex-sub margin-left-12">
									<div className="flex margin-bottom-8">
										<div className="font-bold font-size-16 line-height-24">系统消息</div>
										<div className="margin-left-12 line-height-24 font-size-12 color-c9cdd4">{item.noticeTime}</div>
									</div>
									<div className="font-size-14 line-height-24 line-height-22 color-4e5969">
										<span>{item.noticeContent || ''}</span>
										{textObj[item.noticeType] && (
											<>
												<span>，</span>
												<span className="a color-165dff" onClick={() => todoDetailByType(item)}>
													{textObj[item.noticeType]}
												</span>
											</>
										)}
									</div>
								</div>
							</div>
						</List.Item>
					)}
				/>
			</div>
		</div>
	);
};

export default Index;
