import { useEffect, useState } from 'react';
import { useSelector } from 'react-redux';

import { Button, Col, Form, Row, Select, Input, Space, Table, Descriptions, Tooltip } from 'antd';
import { QuestionCircleOutlined, ReloadOutlined, SearchOutlined } from '@ant-design/icons';

import { useTableData } from '@/hook/useTableData';
import { useRouterLink } from '@/hook/useRouter';

import { getDeptData } from '@/utils/dictionary';

const linkToPath = '/businessOppty/attendanceManage';
const Index = () => {
	const { linkTo, searchParams } = useRouterLink();
	const id = searchParams.get('id') || '';

	const { form, dataSource, pagination, changePage, onReset, onSearch } = useTableData({
		params: { id },
		getTablePageData: (data) => {
			console.log('getTablePageData', data);
			return new Promise((resolve) => {
				resolve({
					data: [],
					total: 0,
				});
			});
		},
	});

	// 部门列表
	const [departmentList, setDepartmentList] = useState([]);

	// 获取部门列表
	const getDepartmentList = async () => {
		const res = await getDeptData();
		if (res) {
			setDepartmentList(res);
		}
	};

	useEffect(() => {
		getDepartmentList();
	}, []);

	// 权限
	const userInfo = useSelector((state) => {
		return state.user.userInfo || {};
	});
	const [permission, setPermission] = useState(false);

	useEffect(() => {
		const { id, roleList = [] } = userInfo;
		if (id && roleList.length) {
			/* 判断下有没有管理权限 */
			const permission = roleList.some((item) => ['report-hr', 'report-all'].includes(item.roleCode));
			setPermission(permission);
		}
	}, [userInfo.id]);

	// 获取考勤数据
	const [detail, setDetail] = useState({});

	const getDetailData = () => {
		if (id) {
			setDetail({
				id,
				companyName: '上海中科软信息科技有限公司',
				date: '2025-09',
				releaseStatus: 0,
			});
		}
	};
	useEffect(() => {
		getDetailData();
	}, [id]);

	return (
		<div className={'flex-sub flex flex-direction-column margin-20 border-radius-4'}>
			{/* 考勤信息 开始 */}
			<div className="flex justify-between padding-20 flex-sub bg-color-ffffff border-radius-4">
				<Descriptions column={1}>
					<Descriptions.Item label="考勤日期">{detail.date}</Descriptions.Item>
					<Descriptions.Item label="所属公司">{detail.companyName}</Descriptions.Item>
					<Descriptions.Item label="发布状态">
						<div className={`tag-status-${['warning', 'primary'][detail.releaseStatus || 0]}`}>
							{['待发布', '已发布'][detail.releaseStatus || 0]}
						</div>
					</Descriptions.Item>
				</Descriptions>
				{detail.releaseStatus === 0 && <Button type="primary">发 布</Button>}
			</div>
			{/* 考勤信息 结束 */}
			{/* 筛选条件 开始 */}
			<div className="margin-top-12 padding-20 flex-sub bg-color-ffffff border-radius-4">
				<div className="flex">
					<Form
						form={form}
						labelCol={{
							style: { width: '80px' },
						}}
						labelAlign="left"
						className="form-filter flex-sub"
					>
						<Row gutter={[20, 20]}>
							<Col span={8}>
								<Form.Item name="name" label="员工姓名">
									<Input placeholder="请输入员工姓名" />
								</Form.Item>
							</Col>
							<Col span={8}>
								<Form.Item name="deptId" label="部门名称">
									<Select allowClear className="flex-sub" placeholder="请选择部门" options={departmentList} />
								</Form.Item>
							</Col>
						</Row>
					</Form>
					<Space className="flex-shirnk">
						<Button type="primary" icon={<SearchOutlined />} onClick={() => onSearch()}>
							查询
						</Button>
						<Button type="default" icon={<ReloadOutlined />} onClick={() => onReset()}>
							重置
						</Button>
					</Space>
				</div>
				<div className="margin-top-12" style={}>
					<Table
						rowKey="id"
						dataSource={
							dataSource.length
								? dataSource
								: [{ id: 1, name: '章海琪', deptName: '数字化事业部', attendanceDay: 10, shouldAttendanceDay: 22 }]
						}
						pagination={pagination}
						onChange={changePage}
						scroll={{ x: 'max-content' }}
					>
						<Table.Column
							title="序号"
							key="index"
							width={60}
							align="center"
							fixed="left"
							render={(_, __, index) => {
								return index + 1;
							}}
						/>
						<Table.Column title="员工姓名" dataIndex="name" render={(text) => text || '--'} />
						<Table.Column title="部门名称" dataIndex="deptName" render={(text) => text || '--'} />
						<Table.Column
							title={
								<Tooltip title="实际出勤天数/应出勤天数">
									<Space>
										出勤天数
										<QuestionCircleOutlined />
									</Space>
								</Tooltip>
							}
							dataIndex="attendanceDay"
							align="center"
							render={(_, record) => (
								<div>
									<text className="color-165dff">{record.attendanceDay}</text>
									<text>/</text>
									<text>{record.shouldAttendanceDay}</text>
								</div>
							)}
						/>
						<Table.Column title="迟到/早退">
							<Table.Column title="次数" dataIndex="xxx" align="center" render={(text) => text || '--'} />
							<Table.Column title="扣除出勤(天)" dataIndex="xxx" align="center" render={(text) => text || '--'} />
						</Table.Column>
						<Table.Column title="缺卡">
							<Table.Column title="次数" dataIndex="xxx" align="center" render={(text) => text || '--'} />
							<Table.Column title="扣除出勤(天)" dataIndex="xxx" align="center" render={(text) => text || '--'} />
						</Table.Column>
						<Table.Column title="请假(天)">
							<Table.Column title="事假" dataIndex="xxx" align="center" render={(text) => text || '--'} />
							<Table.Column title="病假" dataIndex="xxx" align="center" render={(text) => text || '--'} />
							<Table.Column title="年假" dataIndex="xxx" align="center" render={(text) => text || '--'} />
							<Table.Column title="调休" dataIndex="xxx" align="center" render={(text) => text || '--'} />
							<Table.Column title="婚假" dataIndex="xxx" align="center" render={(text) => text || '--'} />
							<Table.Column title="产假" dataIndex="xxx" align="center" render={(text) => text || '--'} />
							<Table.Column title="产检假" dataIndex="xxx" align="center" render={(text) => text || '--'} />
							<Table.Column title="病假" dataIndex="xxx" align="center" render={(text) => text || '--'} />
							<Table.Column title="丧假" dataIndex="xxx" align="center" render={(text) => text || '--'} />
							<Table.Column title="陪产假" dataIndex="xxx" align="center" render={(text) => text || '--'} />
						</Table.Column>
						<Table.Column title="请假总数" dataIndex="xxx" align="center" render={(text) => text || '--'} />
						<Table.Column title="旷工(天)" dataIndex="xxx" align="center" render={(text) => text || '--'} />
						{permission && (
							<Table.Column
								title="操作"
								key="option"
								dataIndex="option"
								align="center"
								fixed="right"
								width="160px"
								render={(_, record) => {
									return (
										<>
											<Button
												type="link"
												size="small"
												onClick={() => linkTo(`${linkToPath}/detail?id=${record.id}&fromList=1`)}
											>
												编辑
											</Button>
										</>
									);
								}}
							/>
						)}
					</Table>
				</div>
			</div>
			{/* 表格 结束 */}
		</div>
	);
};

export default Index;
