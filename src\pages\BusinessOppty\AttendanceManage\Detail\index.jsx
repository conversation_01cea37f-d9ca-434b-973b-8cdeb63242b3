import { useEffect, useState } from 'react';
import { useSelector } from 'react-redux';

import { Button, Col, Form, Row, Select, Input, Space, Table, Descriptions, Tooltip, Modal, InputNumber } from 'antd';
import { QuestionCircleOutlined, ReloadOutlined, SearchOutlined } from '@ant-design/icons';

import { useTableData } from '@/hook/useTableData';
import { useRouterLink } from '@/hook/useRouter';

import { getDeptData } from '@/utils/dictionary';
import { getByPermissionPerms } from '@/api/common';

const linkToPath = '/businessOppty/attendanceManage';
const Index = () => {
	const { linkTo, searchParams } = useRouterLink();
	const id = searchParams.get('id') || '';

	const { form, dataSource, pagination, changePage, onReset, onSearch } = useTableData({
		params: { id },
		getTablePageData: (data) => {
			console.log('getTablePageData', data);
			return new Promise((resolve) => {
				resolve({
					data: [],
					total: 0,
				});
			});
		},
	});

	// 部门列表
	const [departmentList, setDepartmentList] = useState([]);

	// 获取部门列表
	const getDepartmentList = async () => {
		const res = await getDeptData();
		if (res) {
			setDepartmentList(res);
		}
	};

	// 中心人员名单
	const [centerStaffList, setCenterStaffList] = useState([]);

	// 获取中心人员名单
	const getCenterStaffList = async () => {
		const res = await getByPermissionPerms({ perms: 'businessOppty' }, { showLoading: false });
		if (res) {
			setCenterStaffList(
				res.data.map((ov) => ({
					label: ov.userName,
					value: ov.id,
				}))
			);
		}
	};

	useEffect(() => {
		getDepartmentList();
		getCenterStaffList();
	}, []);

	// 权限
	const userInfo = useSelector((state) => {
		return state.user.userInfo || {};
	});
	const [permission, setPermission] = useState(false);

	useEffect(() => {
		const { id, roleList = [] } = userInfo;
		if (id && roleList.length) {
			/* 判断下有没有管理权限 */
			const permission = roleList.some((item) => ['report-hr', 'report-all'].includes(item.roleCode));
			setPermission(permission);
		}
	}, [userInfo.id]);

	// 获取考勤数据
	const [detail, setDetail] = useState({});

	const getDetailData = () => {
		if (id) {
			setDetail({
				id,
				companyName: '上海中科软信息科技有限公司',
				date: '2025-09',
				releaseStatus: 0,
			});
		}
	};
	useEffect(() => {
		getDetailData();
	}, [id]);

	// 弹窗数据
	const [modalData, setModalData] = useState({});

	return (
		<div className={'flex-sub flex flex-direction-column margin-20 border-radius-4'}>
			{/* 考勤信息 开始 */}
			<div className="flex justify-between padding-20 flex-sub bg-color-ffffff border-radius-4">
				<Descriptions column={1}>
					<Descriptions.Item label="考勤日期">{detail.date}</Descriptions.Item>
					<Descriptions.Item label="所属公司">{detail.companyName}</Descriptions.Item>
					<Descriptions.Item label="发布状态">
						<div className={`tag-status-${['warning', 'primary'][detail.releaseStatus || 0]}`}>
							{['待发布', '已发布'][detail.releaseStatus || 0]}
						</div>
					</Descriptions.Item>
				</Descriptions>
				{detail.releaseStatus === 0 && <Button type="primary">发 布</Button>}
			</div>
			{/* 考勤信息 结束 */}
			{/* 筛选条件 开始 */}
			<div className="margin-top-12 padding-20 flex-sub bg-color-ffffff border-radius-4">
				<div className="flex">
					<Form form={form} className="form-filter flex-sub">
						<Row gutter={[20, 20]}>
							<Col span={8}>
								<Form.Item name="deptId" label="部门名称">
									<Select allowClear className="flex-sub" placeholder="请选择部门" options={departmentList} />
								</Form.Item>
							</Col>
							<Col span={8}>
								<Form.Item name="staffId" label="员工">
									<Select
										placeholder={'请选择员工'}
										className={'width-100per'}
										options={centerStaffList}
										optionFilterProp={'label'}
										maxTagCount={'responsive'}
										showSearch
										allowClear
									/>
								</Form.Item>
							</Col>
						</Row>
					</Form>
					<Space className="flex-shirnk">
						<Button type="primary" icon={<SearchOutlined />} onClick={() => onSearch()}>
							查询
						</Button>
						<Button type="default" icon={<ReloadOutlined />} onClick={() => onReset()}>
							重置
						</Button>
					</Space>
				</div>
				<div className="margin-top-12 vertical-align-middle">
					<Table
						rowKey="id"
						dataSource={
							dataSource.length
								? dataSource
								: [
										{
											id: 1,
											name: '章海琪',
											deptName: '数字化事业部',
											attendanceDay: 10,
											shouldAttendanceDay: 22,
											lateEarly: 1,
											isImportant: false,
										},
										{
											id: 2,
											name: '李四',
											deptName: '技术部',
											attendanceDay: 15,
											shouldAttendanceDay: 22,
											isImportant: false,
										},
										{
											id: 3,
											name: '王五',
											deptName: '技术部',
											attendanceDay: 15,
											shouldAttendanceDay: 22,
											isImportant: true,
										},
								  ]
						}
						pagination={pagination}
						onChange={changePage}
						scroll={{ x: 'max-content' }}
					>
						<Table.Column
							title="序号"
							key="index"
							width={60}
							align="center"
							fixed="left"
							render={(_, __, index) => {
								return index + 1;
							}}
						/>
						<Table.Column
							title="员工姓名"
							dataIndex="name"
							render={(text, record) => <div style={{ position: 'relative' }}>{text || '--'}</div>}
						/>
						<Table.Column title="部门名称" dataIndex="deptName" render={(text) => text || '--'} />
						<Table.Column
							title={
								<Tooltip title="实际出勤天数/应出勤天数">
									<Space>
										出勤天数
										<QuestionCircleOutlined />
									</Space>
								</Tooltip>
							}
							dataIndex="attendanceDay"
							align="center"
							render={(_, record) => (
								<div className="flex justify-center">
									<div className="color-165dff">{record.attendanceDay}</div>
									<div>/</div>
									<div>{record.shouldAttendanceDay}</div>
								</div>
							)}
						/>
						<Table.Column title="迟到/早退">
							<Table.Column title="次数" dataIndex="xxx" align="center" render={(text) => text || '--'} />
							<Table.Column
								title="扣除出勤(天)"
								dataIndex="attendanceDay"
								align="center"
								render={(text, record) => {
									return record.isImportant ? (
										<Tooltip
											title={
												<div>
													<div>原始数据：{record.attendanceDay}</div>
													<div>最后操作人：卧龙</div>
													<div>操作时间：2025-09-10 10:00:00</div>
												</div>
											}
										>
											<div className="position-relative">
												<div>{text || '--'}</div>

												{/* 条件显示红色星号 */}
												{record.isImportant && (
													<span
														style={{
															position: 'absolute',
															top: '-5px',
															right: '-5px',
															color: '#ff4d4f',
															fontSize: '12px',
															fontWeight: 'bold',
														}}
													>
														*
													</span>
												)}
											</div>
										</Tooltip>
									) : (
										text || '--'
									);
								}}
							/>
						</Table.Column>
						<Table.Column title="缺卡">
							<Table.Column title="次数" dataIndex="xxx" align="center" render={(text) => text || '--'} />
							<Table.Column title="扣除出勤(天)" dataIndex="xxx" align="center" render={(text) => text || '--'} />
						</Table.Column>
						<Table.Column title="请假(天)">
							<Table.Column title="事假" dataIndex="xxx" align="center" render={(text) => text || '--'} />
							<Table.Column title="病假" dataIndex="xxx" align="center" render={(text) => text || '--'} />
							<Table.Column title="年假" dataIndex="xxx" align="center" render={(text) => text || '--'} />
							<Table.Column title="调休" dataIndex="xxx" align="center" render={(text) => text || '--'} />
							<Table.Column title="婚假" dataIndex="xxx" align="center" render={(text) => text || '--'} />
							<Table.Column title="产假" dataIndex="xxx" align="center" render={(text) => text || '--'} />
							<Table.Column title="产检假" dataIndex="xxx" align="center" render={(text) => text || '--'} />
							<Table.Column title="丧假" dataIndex="xxx" align="center" render={(text) => text || '--'} />
							<Table.Column title="陪产假" dataIndex="xxx" align="center" render={(text) => text || '--'} />
						</Table.Column>
						<Table.Column title="请假总数(天)" dataIndex="xxx" align="center" render={(text) => text || '--'} />
						<Table.Column title="旷工(天)" dataIndex="xxx" align="center" render={(text) => text || '--'} />
						{!permission && (
							<Table.Column
								title="操作"
								key="option"
								dataIndex="option"
								align="center"
								fixed="right"
								width="90px"
								render={(_, record) => {
									return (
										<>
											<Button type="link" size="small" onClick={() => setModalData(record)}>
												编辑
											</Button>
										</>
									);
								}}
							/>
						)}
					</Table>
				</div>
			</div>
			{/* 表格 结束 */}
			<EditModal
				open={modalData.id}
				modalData={modalData}
				onClose={() => setModalData({})}
				onOk={(data) => {
					console.log(data);
				}}
			/>
		</div>
	);
};

const EditModal = (props = {}) => {
	const { deptName, name } = props.modalData || {};
	const [form] = Form.useForm();
	useEffect(() => {
		if (props.modalData) {
			form.setFieldsValue(props.modalData);
		}
	}, [props.modalData]);
	return (
		<Modal
			open={props.open}
			title={`${deptName}-${name}`}
			onOk={props.submitModal}
			onCancel={props.onClose}
			width={800}
			centered
			maskClosable={false}
		>
			<Form form={form} labelCol={{ style: { width: '100px' } }} className="margin-top-36">
				<Row>
					{[
						{ label: '迟到/早退', name: 'lateEarly', unit: '次' },
						{ label: '缺卡', name: 'absent', unit: '次' },
						{ label: '事假', name: 'personalLeave', unit: '天' },
						{ label: '病假', name: 'sickLeave', unit: '天' },
						{ label: '年假', name: 'annualLeave', unit: '天' },
						{ label: '调休', name: 'compensatoryLeave', unit: '天' },
						{ label: '婚假', name: 'marriageLeave', unit: '天' },
						{ label: '产假', name: 'pregnantLeave', unit: '天' },
						{ label: '产检假', name: 'childCheckLeave', unit: '天' },
						{ label: '丧假', name: 'funeralLeave', unit: '天' },
						{ label: '陪产假', name: 'maxPregnantLeave', unit: '天' },
					].map((item) => (
						<Col span={8} key={item.name}>
							<Form.Item label={item.label} name={item.name} required initialValue={0}>
								<InputNumber min={0} precision={0} className="width-140" placeholder={`请输入`} suffix={item.unit} />
							</Form.Item>
						</Col>
					))}
				</Row>
			</Form>
		</Modal>
	);
};

export default Index;
