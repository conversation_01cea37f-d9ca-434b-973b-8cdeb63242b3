import { useEffect, useState } from 'react';
import { useSelector } from 'react-redux';

import { Button, Col, Form, Row, Select, Space, Table, Descriptions, Tooltip, Modal, InputNumber, Input, message } from 'antd';
import { QuestionCircleOutlined, ReloadOutlined, SearchOutlined } from '@ant-design/icons';

import { useTableData } from '@/hook/useTableData';
import { useRouterLink } from '@/hook/useRouter';

import { getDeptData } from '@/utils/dictionary';

import {
	employeeList,
	getAttendanceReportPage as getTablePageData,
	getAttendanceStatistics,
	releaseAttendanceReport,
	updateAttendanceReport,
} from '@/api/BusinessOppty/AttendanceManage/index.js';

const linkToPath = '/businessOppty/attendanceManage';

const Index = () => {
	const { linkTo, searchParams } = useRouterLink();
	const reportYearMonth = searchParams.get('reportYearMonth') || '';
	const companyName = searchParams.get('companyName') || '';
	const releaseStatus = searchParams.get('releaseStatus') - 0 || 0;

	const { form, dataSource, pagination, getTableData, changePage, onReset, onSearch } = useTableData({
		params: { reportYearMonth, companyName, releaseStatus },
		getTablePageData,
	});

	// 部门列表
	const [departmentList, setDepartmentList] = useState([]);

	// 获取部门列表
	const getDepartmentList = async () => {
		const res = await getDeptData();
		if (res) {
			setDepartmentList(res);
		}
	};

	// 中心人员名单
	const [centerStaffList, setCenterStaffList] = useState([]);

	// 获取中心人员名单
	const getCenterStaffList = async () => {
		const res = await employeeList();
		if (res) {
			setCenterStaffList(
				res.data.map((ov) => ({
					label: ov.userName,
					value: ov.userId,
				}))
			);
		}
	};

	useEffect(() => {
		getDepartmentList();
		getCenterStaffList();
	}, []);

	// 权限
	const userInfo = useSelector((state) => {
		return state.user.userInfo || {};
	});
	const [permission, setPermission] = useState(false);

	useEffect(() => {
		const { id, roleList = [] } = userInfo;
		if (id && roleList.length) {
			/* 判断下有没有管理权限 */
			const permission = !roleList.some((item) => ['report-hr', 'report-all'].includes(item.roleCode));
			setPermission(permission);
		}
	}, [userInfo.id]);

	// 统计数据
	const [statisticsData, setStatisticsData] = useState({});

	const getStatisticsData = () => {
		getAttendanceStatistics({
			reportYearMonth,
		}).then((res) => {
			setStatisticsData(res.data || {});
		});
	};

	useEffect(() => {
		getStatisticsData();
	}, []);

	// 发布考勤
	const onRelease = () => {
		Modal.confirm({
			title: '提示',
			content: '是否确认发布考勤数据？',
			okText: '确定',
			cancelText: '取消',
			centered: true,
			onOk: () => {
				releaseAttendanceReport({
					reportYearMonth,
				}).then(() => {
					message.success('发布成功');
					linkTo(`${linkToPath}/detail?reportYearMonth=${reportYearMonth}&companyName=${companyName}&releaseStatus=1`, { replace: true });
				});
			},
		});
	};

	// 弹窗数据
	const [modalData, setModalData] = useState({});

	// 提交更改
	const submitData = (data) => {
		updateAttendanceReport(data).then(() => {
			message.success('修改成功');
			getStatisticsData();
			getTableData();
			setModalData({});
		});
	};
	return (
		<div className={'flex-sub flex flex-direction-column margin-20 border-radius-4'}>
			{/* 考勤信息 开始 */}
			<div className="flex justify-between padding-20 flex-sub bg-color-ffffff border-radius-4">
				<Descriptions column={1}>
					<Descriptions.Item label="考勤日期">{reportYearMonth}</Descriptions.Item>
					<Descriptions.Item label="所属公司">{companyName}</Descriptions.Item>
					<Descriptions.Item label="发布状态">
						<div className={`tag-status-${['warning', 'primary'][releaseStatus || 0]}`}>{['待发布', '已发布'][releaseStatus || 0]}</div>
					</Descriptions.Item>
				</Descriptions>
				{permission && releaseStatus === 0 && (
					<Button type="primary" onClick={onRelease}>
						发 布
					</Button>
				)}
			</div>
			{/* 考勤信息 结束 */}
			{/* 筛选条件 开始 */}
			<div className="margin-top-12 padding-20 flex-sub bg-color-ffffff border-radius-4">
				<div className="flex">
					<Form form={form} className="form-filter flex-sub">
						<Row gutter={[20, 20]}>
							<Col span={8}>
								<Form.Item name="departmentId" label="部门名称">
									<Select allowClear className="flex-sub" placeholder="请选择部门" options={departmentList} />
								</Form.Item>
							</Col>
							<Col span={8}>
								<Form.Item name="userId" label="员工">
									<Select
										placeholder={'请选择员工'}
										className={'width-100per'}
										options={centerStaffList}
										optionFilterProp={'label'}
										maxTagCount={'responsive'}
										showSearch
										allowClear
									/>
								</Form.Item>
							</Col>
						</Row>
					</Form>
					<Space className="flex-shirnk">
						<Button type="primary" icon={<SearchOutlined />} onClick={() => onSearch()}>
							查询
						</Button>
						<Button type="default" icon={<ReloadOutlined />} onClick={() => onReset()}>
							重置
						</Button>
					</Space>
				</div>
				<div className="margin-top-12 vertical-align-middle">
					<Table
						rowKey="id"
						dataSource={dataSource}
						pagination={pagination}
						onChange={changePage}
						bordered
						scroll={{ x: 'max-content' }}
						summary={() => {
							return (
								<Table.Summary fixed>
									<Table.Summary.Row>
										<Table.Summary.Cell align="center">合计</Table.Summary.Cell>
										<Table.Summary.Cell colSpan={2} align="center">
											--
										</Table.Summary.Cell>
										<Table.Summary.Cell align="center">--</Table.Summary.Cell>
										<Table.Summary.Cell align="center">{statisticsData.lateEarlyCount || 0}</Table.Summary.Cell>
										<Table.Summary.Cell align="center">{statisticsData.lateEarlyDeductionDays || 0}</Table.Summary.Cell>
										<Table.Summary.Cell align="center">{statisticsData.missingCardCount || 0}</Table.Summary.Cell>
										<Table.Summary.Cell align="center">{statisticsData.missingCardDeductionDays || 0}</Table.Summary.Cell>
										<Table.Summary.Cell align="center" colSpan={9}>
											--
										</Table.Summary.Cell>
										<Table.Summary.Cell align="center">{statisticsData.leaveTotalDays || 0}</Table.Summary.Cell>
										<Table.Summary.Cell align="center">{statisticsData.absenteeismDays || 0}</Table.Summary.Cell>
										{permission && releaseStatus === 0 && <Table.Summary.Cell align="center">--</Table.Summary.Cell>}
									</Table.Summary.Row>
								</Table.Summary>
							);
						}}
					>
						<Table.Column
							title="序号"
							key="index"
							width={80}
							align="center"
							fixed="left"
							render={(_, __, index) => {
								return index + 1;
							}}
						/>
						<Table.Column
							title="员工姓名"
							dataIndex="userName"
							render={(text) => <div style={{ position: 'relative' }}>{text || '--'}</div>}
						/>
						<Table.Column
							title="部门名称"
							dataIndex="departmentName"
							render={(text) =>
								(text || '--').split('/').map((ov) => {
									return <div key={ov}>{ov}</div>;
								})
							}
						/>
						<Table.Column
							title={
								<Tooltip title="实际出勤天数/应出勤天数">
									<Space>
										出勤天数
										<QuestionCircleOutlined />
									</Space>
								</Tooltip>
							}
							dataIndex="attendanceDay"
							align="center"
							render={(_, record) => (
								<div className="flex justify-center">
									<div className="color-165dff">{record.actualAttendanceDays}</div>
									<div className="padding-lr-4">/</div>
									<div>{record.shouldAttendanceDays}</div>
								</div>
							)}
						/>
						<Table.Column title="迟到/早退">
							<Table.Column
								title="次数"
								dataIndex="lateEarlyCount"
								align="center"
								render={(text, record) => {
									return <DataItem text={text} originalData={record.lateEarlyOriginalData} />;
								}}
							/>
							<Table.Column title="扣除出勤(天)" dataIndex="lateEarlyDeductionDays" align="center" render={(text) => text || 0} />
						</Table.Column>
						<Table.Column title="缺卡">
							<Table.Column
								title="次数"
								dataIndex="missingCardCount"
								align="center"
								render={(text, record) => {
									return <DataItem text={text} originalData={record.missingCardOriginalData} />;
								}}
							/>
							<Table.Column title="扣除出勤(天)" dataIndex="missingCardDeductionDays" align="center" render={(text) => text || 0} />
						</Table.Column>
						<Table.Column title="请假(天)">
							<Table.Column
								title="事假"
								dataIndex="personalLeaveDays"
								align="center"
								render={(text, record) => {
									return <DataItem text={text} originalData={record.personalLeaveOriginalData} />;
								}}
							/>
							<Table.Column
								title="病假"
								dataIndex="sickLeaveDays"
								align="center"
								render={(text, record) => {
									return <DataItem text={text} originalData={record.sickLeaveOriginalData} />;
								}}
							/>
							<Table.Column
								title="年假"
								dataIndex="annualLeaveDays"
								align="center"
								render={(text, record) => {
									return <DataItem text={text} originalData={record.annualLeaveOriginalData} />;
								}}
							/>
							<Table.Column
								title="调休"
								dataIndex="adjustmentLeaveDays"
								align="center"
								render={(text, record) => {
									return <DataItem text={text} originalData={record.adjustmentLeaveOriginalData} />;
								}}
							/>
							<Table.Column
								title="婚假"
								dataIndex="marriageLeaveDays"
								align="center"
								render={(text, record) => {
									return <DataItem text={text} originalData={record.marriageLeaveOriginalData} />;
								}}
							/>
							<Table.Column
								title="产假"
								dataIndex="maternityLeaveDays"
								align="center"
								render={(text, record) => {
									return <DataItem text={text} originalData={record.maternityLeaveOriginalData} />;
								}}
							/>
							<Table.Column
								title="产检假"
								dataIndex="prenatalCheckLeaveDays"
								align="center"
								render={(text, record) => {
									return <DataItem text={text} originalData={record.prenatalCheckLeaveOriginalData} />;
								}}
							/>
							<Table.Column
								title="丧假"
								dataIndex="bereavementLeaveDays"
								align="center"
								render={(text, record) => {
									return <DataItem text={text} originalData={record.bereavementLeaveOriginalData} />;
								}}
							/>
							<Table.Column
								title="陪产假"
								dataIndex="paternityLeaveDays"
								align="center"
								render={(text, record) => {
									return <DataItem text={text} originalData={record.paternityLeaveOriginalData} />;
								}}
							/>
						</Table.Column>
						<Table.Column title="请假总数(天)" dataIndex="leaveTotalDays" align="center" render={(text) => text || 0} />
						<Table.Column title="旷工(天)" dataIndex="absenteeismDays" align="center" render={(text) => text || 0} />
						{permission && releaseStatus === 0 && (
							<Table.Column
								title="操作"
								key="option"
								dataIndex="option"
								align="center"
								fixed="right"
								width="90px"
								render={(_, record) => {
									return (
										<>
											<Button type="link" size="small" onClick={() => setModalData(record)}>
												编辑
											</Button>
										</>
									);
								}}
							/>
						)}
					</Table>
				</div>
			</div>
			<EditModal open={modalData.id} modalData={modalData} onClose={() => setModalData({})} onOk={submitData} />
		</div>
	);
};

const DataItem = (props = {}) => {
	const { text, originalData } = props;
	const { originalData: oldText, updateTime, updateUserName } = originalData || {};
	return originalData &&  ? (
		<Tooltip
			title={
				<div>
					<div>原始数据：{oldText}</div>
					<div>最后操作人：{updateUserName}</div>
					<div>操作时间：{updateTime}</div>
				</div>
			}
		>
			<div className="position-relative">
				<div>{text || 0}</div>
				<span
					style={{
						position: 'absolute',
						top: '-5px',
						right: '-5px',
						color: '#ff4d4f',
						fontSize: '12px',
						fontWeight: 'bold',
					}}
				>
					*
				</span>
			</div>
		</Tooltip>
	) : (
		text || 0
	);
};

const EditModal = (props = {}) => {
	const { departmentName, userName } = props.modalData || {};
	const [form] = Form.useForm();

	useEffect(() => {
		if (props.modalData) {
			form.setFieldsValue(props.modalData);
		}
	}, [props.modalData]);
	return (
		<Modal
			open={props.open}
			title={`${(departmentName || '').split('/')[0]}-${userName}`}
			onOk={() => {
				props.onOk(form.getFieldsValue());
			}}
			onCancel={props.onClose}
			width={800}
			centered
			maskClosable={false}
			forceRender
		>
			<Form form={form} labelCol={{ style: { width: '100px' } }} className="margin-top-36">
				<Form.Item name="id" hidden>
					<Input />
				</Form.Item>
				<Row>
					{[
						{ label: '迟到/早退', name: 'lateEarlyCount', unit: '次', precision: 0 },
						{ label: '缺卡', name: 'missingCardCount', unit: '次', precision: 0 },
						{ label: '事假', name: 'personalLeaveDays', unit: '天', precision: 1, step: 0.5 },
						{ label: '病假', name: 'sickLeaveDays', unit: '天', precision: 1, step: 0.5 },
						{ label: '年假', name: 'annualLeaveDays', unit: '天', precision: 1, step: 0.5 },
						{ label: '调休', name: 'adjustmentLeaveDays', unit: '天', precision: 2 },
						{ label: '婚假', name: 'marriageLeaveDays', unit: '天', precision: 1, step: 0.5 },
						{ label: '产假', name: 'maternityLeaveDays', unit: '天', precision: 1, step: 0.5 },
						{ label: '产检假', name: 'prenatalCheckLeaveDays', unit: '天', precision: 1, step: 0.5 },
						{ label: '丧假', name: 'bereavementLeaveDays', unit: '天', precision: 1, step: 0.5 },
						{ label: '陪产假', name: 'paternityLeaveDays', unit: '天', precision: 1, step: 0.5 },
					].map((item) => (
						<Col span={8} key={item.name}>
							<Form.Item label={item.label} name={item.name} required initialValue={0}>
								<InputNumber
									min={0}
									step={item.step}
									precision={item.precision}
									className="width-140"
									placeholder="请输入"
									suffix={item.unit}
									parser={(value) => {
										if (!item.step) return value;
										const num = parseFloat(value);
										if (isNaN(num)) return 0;
										const temp = (num % item.step).toFixed(item.precision) - 0;
										return num - temp;
									}}
									formatter={(value) => {
										// 格式化显示值
										return value ? value.toString() : '';
									}}
								/>
							</Form.Item>
						</Col>
					))}
				</Row>
			</Form>
		</Modal>
	);
};

export default Index;
