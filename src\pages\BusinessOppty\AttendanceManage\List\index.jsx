import { useEffect, useState } from 'react';
import { Button, Col, Form, Row, Select, DatePicker, Space, Table } from 'antd';
import { ReloadOutlined, SearchOutlined } from '@ant-design/icons';

import { useTableData } from '@/hook/useTableData';
import { useRouterLink } from '@/hook/useRouter';

const linkToPath = '/businessOppty/attendanceManage';
const Index = () => {
	const { linkTo } = useRouterLink();

	const { form, dataSource, pagination, changePage, onReset, onSearch, exportData } = useTableData({
		params: {},
		getTablePageData: (data) => {
			console.log('getTablePageData', data);

			return new Promise((resolve) => {
				resolve({
					data: [],
					total: 0,
				});
			});
		},
		exportTableData: (data) => {
			console.log('exportTableData', data);
			return new Promise((resolve) => {
				resolve({
					data: [],
					total: 0,
				});
			});
		},
	});

	return (
		<div className={'flex-sub flex flex-direction-column margin-20 border-radius-4'}>
			{/* 筛选条件 开始 */}
			<div className="padding-20 flex-sub bg-color-ffffff border-radius-4">
				<div className="flex">
					<Form
						form={form}
						labelCol={{
							style: { width: '80px' },
						}}
						labelAlign="left"
						className="form-filter flex-sub"
					>
						<Row gutter={[20, 20]}>
							<Col span={8}>
								<Form.Item name="date" label="考勤日期">
									<DatePicker mode="month" placeholder="请选择考勤日期" allowClear className="flex-sub" />
								</Form.Item>
							</Col>
							<Col span={8}>
								<Form.Item name="releaseStatus" label="发布状态">
									<Select
										allowClear
										className="flex-sub"
										placeholder="请选择发布状态"
										options={[
											{ value: 0, label: '待发布' },
											{ value: 1, label: '已发布' },
										]}
									/>
								</Form.Item>
							</Col>
						</Row>
					</Form>
					<Space className="flex-shirnk">
						<Button type="primary" icon={<SearchOutlined />} onClick={() => onSearch()}>
							查询
						</Button>
						<Button type="default" icon={<ReloadOutlined />} onClick={() => onReset()}>
							重置
						</Button>
					</Space>
				</div>
			</div>
			{/* 筛选条件 结束 */}

			{/* 表格 开始 */}
			<div className="margin-top-20 padding-20 flex-sub bg-color-ffffff border-radius-4">
				<Table
					rowKey="id"
					dataSource={
						dataSource.length
							? dataSource
							: [{ id: 1, createTime: '2023-05-01', releaseStatus: 1, companyName: '上海中科软信息科技有限公司' }]
					}
					pagination={pagination}
					onChange={changePage}
					scroll={{ x: 'max-content' }}
				>
					<Table.Column
						title="序号"
						key="index"
						width={60}
						align="center"
						render={(_, __, index) => {
							return index + 1;
						}}
					/>
					<Table.Column title="日期" dataIndex="createTime" render={(text) => (text || '').slice(0, 7)} />
					<Table.Column title="所属公司" dataIndex="companyName" />
					<Table.Column
						title="发布状态"
						dataIndex="releaseStatus"
						render={(text) => {
							return <div className={`tag-status-small-${['warning', 'primary'][text || 0]}`}>{['待发布', '已发布'][text || 0]}</div>;
						}}
					/>
					<Table.Column
						title="操作"
						key="option"
						dataIndex="option"
						align="center"
						fixed="right"
						width="160px"
						render={(_, record) => {
							return (
								<>
									<Button type="link" size="small" onClick={() => linkTo(`${linkToPath}/detail?id=${record.id}&fromList=1`)}>
										详情/编辑
									</Button>
									<Button type="link" size="small" onClick={() => exportData({ id: record.id })}>
										导出
									</Button>
								</>
							);
						}}
					/>
				</Table>
			</div>
			{/* 表格 结束 */}
		</div>
	);
};

export default Index;
