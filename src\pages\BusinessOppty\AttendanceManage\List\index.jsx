import { useEffect, useState } from 'react';
import { useSelector } from 'react-redux';

import { Button, Col, Form, Row, Select, DatePicker, Space, Table, Input } from 'antd';
import { ReloadOutlined, SearchOutlined } from '@ant-design/icons';

import { useTableData } from '@/hook/useTableData';
import { useRouterLink } from '@/hook/useRouter';

import { getReleaseStatusPage as getTablePageData, exportAttendanceData as exportTableData } from '@/api/BusinessOppty/AttendanceManage/index.js';

import dayjs from 'dayjs';

const linkToPath = '/businessOppty/attendanceManage';
const Index = () => {
	const { linkTo } = useRouterLink();

	// 权限
	const userInfo = useSelector((state) => {
		return state.user.userInfo || {};
	});
	const [permission, setPermission] = useState(null);

	const { form, dataSource, pagination, getTableData, changePage, onReset, onSearch, exportData } = useTableData({
		params: {},
		firstActiveLoad: true,
		getTablePageData,
		exportTableData,
	});

	useEffect(() => {
		const { id, roleList = [] } = userInfo;
		if (id && roleList.length) {
			/* 判断下有没有管理权限 */
			const permission = roleList.some((item) => ['report-hr', 'report-all'].includes(item.roleCode));
			setPermission(permission);
			setTimeout(() => {
				getTableData();
			}, 500);
		}
	}, [userInfo.id]);

	return (
		<div className={'flex-sub flex flex-direction-column margin-20 border-radius-4'}>
			{/* 筛选条件 开始 */}
			<div className="padding-20 flex-sub bg-color-ffffff border-radius-4">
				<div className="flex">
					{permission !== null && (
						<Form
							form={form}
							labelCol={{
								style: { width: '80px' },
							}}
							labelAlign="left"
							className="form-filter flex-sub"
						>
							<Row gutter={[20, 20]}>
								<Col span={8}>
									<Form.Item
										name="reportYearMonth"
										label="考勤日期"
										getValueProps={(value) => ({ value: value && dayjs(value) })}
										normalize={(value) => value && `${dayjs(value).format('YYYY-MM')}`}
									>
										<DatePicker picker="month" placeholder="请选择考勤日期" allowClear />
									</Form.Item>
								</Col>
								{permission ? (
									<Col span={8}>
										<Form.Item name="releaseStatus" label="发布状态">
											<Select
												allowClear
												className="flex-sub"
												placeholder="请选择发布状态"
												options={[
													{ value: 0, label: '待发布' },
													{ value: 1, label: '已发布' },
												]}
											/>
										</Form.Item>
									</Col>
								) : (
									<Form.Item name="releaseStatus" hidden label="发布状态" initialValue={1}>
										<Input />
									</Form.Item>
								)}
							</Row>
						</Form>
					)}
					<Space className="flex-shirnk">
						<Button type="primary" icon={<SearchOutlined />} onClick={() => onSearch()}>
							查询
						</Button>
						<Button type="default" icon={<ReloadOutlined />} onClick={() => onReset()}>
							重置
						</Button>
					</Space>
				</div>
			</div>
			{/* 筛选条件 结束 */}

			{/* 表格 开始 */}
			<div className="margin-top-12 padding-20 flex-sub bg-color-ffffff border-radius-4">
				<Table rowKey="reportYearMonth" dataSource={dataSource} pagination={pagination} onChange={changePage} scroll={{ x: 'max-content' }}>
					<Table.Column
						title="序号"
						key="index"
						width={60}
						align="center"
						render={(_, __, index) => {
							return index + 1;
						}}
					/>
					<Table.Column title="日期" dataIndex="reportYearMonth" />
					<Table.Column title="所属公司" dataIndex="companyName" />
					<Table.Column
						title="发布状态"
						dataIndex="releaseStatus"
						render={(text) => {
							return <div className={`tag-status-small-${['warning', 'primary'][text || 0]}`}>{['待发布', '已发布'][text || 0]}</div>;
						}}
					/>
					<Table.Column
						title="操作"
						key="option"
						dataIndex="option"
						align="center"
						fixed="right"
						width="160px"
						render={(_, record) => {
							return (
								<>
									<Button
										type="link"
										size="small"
										onClick={() =>
											linkTo(
												`${linkToPath}/detail?reportYearMonth=${record.reportYearMonth}&companyName=${record.companyName}&releaseStatus=${record.releaseStatus}`
											)
										}
									>
										详情/编辑
									</Button>
									{permission && (
										<Button type="link" size="small" onClick={() => exportData({ reportYearMonth: record.reportYearMonth })}>
											导出
										</Button>
									)}
								</>
							);
						}}
					/>
				</Table>
			</div>
			{/* 表格 结束 */}
		</div>
	);
};

export default Index;
