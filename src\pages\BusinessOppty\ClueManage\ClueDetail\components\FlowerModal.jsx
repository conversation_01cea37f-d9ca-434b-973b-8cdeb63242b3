/**
 * @description FlowerModal - 文件描述
 * <AUTHOR>
 *
 * Created on 2025/4/9 10:28
 */
import React, {forwardRef, useImperativeHandle, useState} from 'react';
import {Cascader, Form, Input, Modal, Tag} from "antd";
import UploadFile from "@/components/UploadFile";
import {DeleteOutlined, PlusOutlined} from "@ant-design/icons";
import {addProjectClueFollowUp, updateProjectClueFollowUp} from "@/api/Opportunity/Clue";

const {SHOW_CHILD} = Cascader;

const FlowerModal = ({userInfo,...props}, ref) => {
    const [visible, setVisible] = useState(false);
    const [details, setDetails] = useState({});
    const [form] = Form.useForm();
    const showModal = (details) => {
        setVisible(true);
        if (details?.id) {
            setDetails(details);
            const followPerson = details.followPersonList.map(ov => [ov.followUpDeptId, ov.followUpUserId]);
            const params = {
                id: details.id,
                latestDevelopments: details.latestDevelopments,
                dynamicKeywords: details.dynamicKeywords,
                followPerson: followPerson,
                contactInformation: details.contactInformation,
            };
            form.setFieldsValue(params);
        } else {
            const dept = userInfo.deptList[0];
            //    默认值填充当前登录用户信息
            const defaultUser = [dept.id, userInfo.id];
            form.setFieldsValue({
                followPerson: [defaultUser],
            });
        }
    }
    const handleOk = () => {
        form.validateFields()
            .then(async (values) => {
                const {followPerson, ...data} = values;
                const followPersonList = [];
                followPerson.forEach((item) => {
                    const dept = props.userList.find((ov) => ov.value === item[0]);
                    const user = dept.children.find((ov) => ov.value === item[1]);
                    followPersonList.push({
                        followUpDeptId: dept.value,
                        followUpDeptName: dept.label,
                        followUpUserId: user.value,
                        followUpUserName: user.label,
                    });
                });
                const params = {
                    ...data,
                    followPersonList: followPersonList
                };
                console.log(params);
                let res;
                if (params.id) {
                    res = await updateProjectClueFollowUp(params);
                } else {
                    params.clueId = props.clueId;
                    res = await addProjectClueFollowUp(params);
                }
                if (res.data) {
                    // 更新记录
                    props.updateList?.();
                    handleCancel();
                }
            })
            .catch((error) => {
                console.log(error);
            });
    }
    const handleCancel = () => {
        setVisible(false);
        form.resetFields();
        setDetails({});
    }
    useImperativeHandle(ref, () => ({
        showModal
    }));
    return (<Modal open={visible} title={`${details.id ? '编辑' : '新增'}跟进记录`} onOk={handleOk} onCancel={handleCancel}>
        <Form form={form} layout={'vertical'}>
            <Form.Item label="id" name="id" hidden>
                <Input/>
            </Form.Item>
            <Form.Item label="跟进情况说明" name="latestDevelopments" required
                       rules={[{required: true, message: '请输入跟进情况说明'}]}>
                <Input.TextArea placeholder={'请输入跟进情况说明'} rows={4} autoSize={{minRows: 4}} />
            </Form.Item>
            <Form.Item label="跟进情况关键词" name="dynamicKeywords" required
                       rules={[{required: true, message: '请输入跟进情况关键词'}]}>
                <Input placeholder={'请输入跟进情况关键词'}/>
            </Form.Item>
            <Form.Item label="跟进人及所属部门" name="followPerson" required rules={[{required: true, message: '请选择跟进人及所属部门'}]}>
                <Cascader options={props.userList || []} showCheckedStrategy={SHOW_CHILD} multiple
                          placeholder="请选择跟进人及所属部门"/>
            </Form.Item>
            <Form.Item label="附件" name="contactInformation">
                <FileUpload/>
            </Form.Item>
        </Form>
    </Modal>)
}
export default forwardRef(FlowerModal);

const FileUpload = ({onChange, value}) => {
    // 删除文件
    const onRemove = () => {
        onChange('');
    }
    return (
        value ?
            <Tag closeIcon={<DeleteOutlined/>} color="processing" onClose={onRemove}
                 className='flex-sub flex align-center width-100per'>
                <a className={'flex-sub width-96per ellipsis'} href={value} target={'_blank'}>{value}</a>
            </Tag>
            : <div className={'flex justify-between'}>
                <UploadFile onChange={onChange} accept={'.doc,.docx,.pdf'}>
                    <div
                        className='width-130 height-130 text-align-center flex align-center justify-center border-box bg-color-f2f3f5 border-radius-8 a margin-right-20'>
                        <PlusOutlined className='font-size-40'/>
                    </div>
                </UploadFile>
                <view
                    className="flex-sub flex justify-end flex-direction-column line-height-18 color-86909c">
                    <view>请上传跟进情况说明附件</view>
                    <view>支持：doc、docx、pdf文件格式，大小不超过10M</view>
                </view>
            </div>
    )
}
