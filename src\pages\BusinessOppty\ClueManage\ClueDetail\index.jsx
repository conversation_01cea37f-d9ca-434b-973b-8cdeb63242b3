/**
 * @description index.jsx - 文件描述
 * <AUTHOR>
 *
 * Created on 2025/4/7 14:03
 */
import React, { useEffect, useRef, useState } from 'react';
import {
	Affix,
	Anchor,
	Button,
	Col,
	Form,
	Input,
	InputNumber,
	Row,
	Select,
	Space,
	Cascader,
	Steps,
	Empty,
	Breadcrumb,
	Modal,
	message,
	Badge,
} from 'antd';
import { getByPermissionPerms } from '@/api/common';
import { pageCategoryValue } from '@/api/Bidmgt/Dict';
import {
	addProjectClue,
	getProjectClue,
	listProjectClueFollowUpByClueIdByClueId,
	updateProjectClue,
	updateProjectClueCancel,
	updateProjectClueReceive,
	updateProjectClueRecover,
} from '@/api/Opportunity/Clue';
import { useRouterLink } from '@/hook/useRouter';
import { EditOutlined, PlusOutlined } from '@ant-design/icons';
import FlowerModal from './components/FlowerModal';
import { Link } from 'react-router-dom';
import { detailMeeting } from '@/api/Opportunity/Meeting';
import { useSelector } from 'react-redux';
import { ClueStatusList } from '@/pages/BusinessOppty/ClueManage/ClueList/const';
import { pageCustomer } from '@/api/Opportunity/Customer';

const { TextArea } = Input;
const ClueDetail = () => {
	const { linkTo, searchParams } = useRouterLink();
	const [baseForm] = Form.useForm();
	const [detailData, setDetailData] = useState({});
	const [meetingDetail, setMeetingDetail] = useState({});
	const [departmentList, setDepartmentList] = useState([]);
	// 客户类型
	const [clueTypeList, setClueTypeList] = useState([]);
	// 跟进列表
	const [followUpList, setFollowUpList] = useState([]);
	const flowerModalRef = useRef(null);
	const [clueId, setClueId] = useState(searchParams.get('id') || '');
	const [meetingId, setMeetingId] = useState(searchParams.get('meetingId') || '');
	const [container, setContainer] = useState(null);
	const [customerList, setCustomerList] = useState([]);
	const reasonRef = useRef(null);
	const [isEditable, setIsEditable] = useState(false);

	// 用户信息
	const userInfo = useSelector((state) => {
		return state?.user.userInfo;
	});
	// 获取部门人员信息
	const getDepartmentList = async () => {
		const res = await getByPermissionPerms({
			perms: 'businessOppty',
		});
		if (res) {
			const selectObj = {};
			(res.data || []).forEach((ov) => {
				const { id, userName, deptList = [] } = ov;
				const staffItem = {
					value: id,
					label: userName,
					// check: false,
					disabled: false,
				};
				deptList.forEach((oov) => {
					const { id, name } = oov;
					if (!selectObj[id]) {
						selectObj[id] = {
							value: id,
							label: name,
							children: [],
							// open: false
						};
					}
					// staffItem.label = id;
					// staffItem.value = name;
					selectObj[id].children.push({ ...staffItem });
					// allList.push({...staffItem})
				});
			});
			const selectList = Object.values(selectObj).filter((ov) => ov.children.length > 0);
			console.log('部门人员信息', selectList);
			setDepartmentList(selectList);
		}
	};
	// 获取客户类型列表
	const getClueTypeList = async () => {
		const res = await pageCategoryValue({ categoryCode: 'customer_type' });
		if (res.data) {
			res.data.records.forEach((ov) => {
				ov.label = ov.value;
				ov.value = ov.id;
			});
			console.log('客户类型列表', res.data.records);
			setClueTypeList(res.data.records || []);
		}
	};
	// 获取客户名称列表
	const getCustomerList = async () => {
		const res = await pageCustomer({ pageSize: 1000, pageNum: 1 });
		if (res.data) {
			setCustomerList(
				res.data.records?.map((ov) => ({
					...ov,
					label: ov.name,
					value: ov.id,
				}))
			);
			console.log('客户名称列表', res.data.records);
		}
	};
	// 获取详情
	const getDetail = async () => {
		const id = searchParams.get('id') || '';
		const meetingId = searchParams.get('meetingId') || '';
		const customerId = searchParams.get('customerId') || '';
		const customerTypeId = searchParams.get('customerTypeId') || '';
		if (!id) {
			setIsEditable(true);
			const deptInfo = userInfo.deptList[0];
			const initForm = {
				clueSourceName: '用户创建',
				clueSourceType: 1,
				clueDispatch: [deptInfo.id, userInfo.id],
			};
			if (meetingId) {
				const res = await detailMeeting({ id: meetingId });
				if (res.data) {
					console.log(res.data);
					setMeetingDetail(res.data);
					initForm.meetingId = meetingId;
					initForm.clueSourceType = 2;
					initForm.clueSourceName = `会议：${res.data.theme || ''}`;
					initForm.clueName = `会议：${res.data.theme || ''}`;
				}
			}
			if (customerId) {
				initForm.customerId = customerId;
			}
			if (customerTypeId) {
				initForm.customerTypeId = customerTypeId;
			}
			baseForm.setFieldsValue(initForm);
			return;
		}
		const res = await getProjectClue({ id });
		if (res.data) {
			setDetailData(res.data);
			const data = res.data;
			const formData = {
				clueStatus: data.clueStatus,
				clueName: data.clueName,
				clueSourceName: data.clueSourceName,
				meetingId: data.meetingId,
				clueSourceType: data.clueSourceType,
				clueTypeName: data.clueTypeName,
				clueSource: data.clueSource,
				clueReceive: [data.clueReceiveDeptId, data.clueReceiveId],
				clueDispatch: [data.clueDispatchDeptId, data.clueDispatchId],
				clueDesc: data.clueDesc,
				customerId: data.customerId,
				customerName: data.customerName || data.customName,
				expectCharge: data.expectCharge,
				customerTypeId: data.customerTypeId,
			};
			baseForm.setFieldsValue(formData);
		}
	};
	// 点击查看记录
	const seeRecord = (record) => {
		if ((record.followType === 1 || typeof record.contactInformation === 'string') && detailData?.cancelStatus === 0) {
			flowerModalRef.current.showModal(record);
		}
	};
	// 新增跟进
	const addFollowUp = () => {
		flowerModalRef.current.showModal();
	};

	// 获取跟进记录
	const getFollowUpList = async () => {
		const id = searchParams.get('id') || '';
		if (!id) {
			return;
		}
		const res = await listProjectClueFollowUpByClueIdByClueId({ clueId: id });
		if (res.data) {
			const list = res.data.map((ov) => {
				const followUpUserName =
					ov.followPersonList?.map((oov) => `${oov.followUpUserName}/${oov.followUpDeptName || '--'}`).join('；') || '--';
				const isDetail = ov.followType === 1 || typeof ov.contactInformation === 'string';
				return {
					// ...ov,
					key: ov.id,
					title: (
						<div className={isDetail ? 'cursor-pointer' : ''} onClick={() => seeRecord(ov)}>
							{ov.dynamicKeywords || ''}
						</div>
					),
					description: (
						<div className={`flex flex-direction-column ${isDetail ? 'cursor-pointer' : ''}`} onClick={() => seeRecord(ov)}>
							{isDetail ? (
								<>
									<span>跟进人：{followUpUserName}</span>
									<span>跟进时间：{`${ov.createTime || ''}`.slice(0, 16)}</span>
								</>
							) : (
								<>
									<span>作废原因：{ov.latestDevelopments || '--'}</span>
									<span>作废时间：{`${ov.createTime || ''}`.slice(0, 16)}</span>
								</>
							)}
						</div>
					),
				};
			});
			setFollowUpList(list);
		}
	};
	useEffect(() => {
		getDepartmentList();
		getClueTypeList();
		getDetail();
		getCustomerList();
	}, []);
	useEffect(() => {
		detailData.id && getFollowUpList();
	}, [detailData]);
	// 确认派发
	const confirmDispatch = () => {
		baseForm.validateFields().then(async (values) => {
			console.log(values);
			const { clueDispatch, clueReceive, ...formData } = values;
			const params = {
				...formData,
				id: detailData?.id || '',
				clueStatus: formData.clueStatus ?? 0,
				clueDispatchId: clueDispatch[1],
				clueDispatchDeptId: clueDispatch[0],
				clueReceiveId: clueReceive[1],
				clueReceiveDeptId: clueReceive[0],
				meetingId: meetingId,
			};
			if (!meetingDetail?.companyName && params.customerId) {
				const selectedCustomer = customerList.find((oov) => oov.id === params.customerId);
				params.customerName = selectedCustomer?.name || '';
				params.customerId = selectedCustomer?.id || '';
			}
			if (!params.id) {
				delete params.id;
			}
			if (!params.meetingId) {
				delete params.meetingId;
			}
			let res;
			if (params.id) {
				res = await updateProjectClue(params);
			} else {
				res = await addProjectClue(params);
			}
			if (res.data) {
				message.success(params.id ? '保存成功' : '派发成功');
				// 判断领取人是否自己，如果是自己则默认领取
				if (params.clueReceiveId === userInfo.id && params.clueStatus === 0) {
					await updateProjectClueReceive({ id: params.id || res.data });
				}
				// getDetail();
				history.back();
			}
		});
	};
	// 作废线索
	const invalidClue = () => {
		Modal.confirm({
			title: '请输入作废的原因？',
			width: 500,
			content: (
				<TextArea rows={4} ref={reasonRef} onChange={(e) => (reasonRef.current.value = e.target.value)} placeholder="请输入作废的原因" />
			),
			onOk: async () => {
				if (!reasonRef.current.value) {
					message.warning('请输入作废的原因');
					return Promise.reject();
				}
				const params = {
					reason: reasonRef.current.value,
					id: clueId,
				};
				const res = await updateProjectClueCancel(params);
				if (res) {
					message.success('作废成功');
					getDetail();
					getFollowUpList();
				}
			},
		});
	};
	// 线索恢复
	const recoverClue = async () => {
		Modal.confirm({
			title: '线索还原',
			content: '确认要还原该线索吗？还原后该线索将恢复。',
			onOk: async () => {
				const res = await updateProjectClueRecover({ id: clueId });
				if (res.data) {
					message.success('恢复成功');
					getDetail();
				}
			},
		});
	};
	const renderPrimaryButton = () => {
		if (!clueId) {
			return (
				<Button type="primary" onClick={confirmDispatch}>
					确认派发
				</Button>
			);
		}
		if (isEditable) {
			return (
				<Button type="primary" onClick={confirmDispatch}>
					提交保存
				</Button>
			);
		}
		const isNotCanceled = detailData?.cancelStatus === 0;
		const isClueDispatchByUser = detailData?.clueDispatchId === userInfo.id;
		const isClueStatusNonZero = detailData?.clueStatus !== 0;

		if (isNotCanceled && (isClueStatusNonZero || isClueDispatchByUser)) {
			return (
				<Button type="primary" icon={<EditOutlined />} onClick={() => setIsEditable(true)}>
					编辑
				</Button>
			);
		}
		return null;
	};

	const renderSecondaryButton = () => {
		if (!clueId) return null;
		return detailData?.cancelStatus === 0 ? (
			<Button danger onClick={invalidClue}>
				作废线索
			</Button>
		) : (
			<Button onClick={recoverClue}>线索恢复</Button>
		);
	};
	/* 判断认领人是否为当前用户 */
	const renderClaimButton = () => {
		return detailData?.clueReceiveId === userInfo.id && detailData?.clueStatus === 0 ? (
			<Button type="default" onClick={claim}>
				领取
			</Button>
		) : null;
	};
	/* 是否可以转商机 */
	const renderConvertButton = () => {
		if (detailData?.cancelStatus === 0 && detailData?.clueStatus !== 2 && detailData?.receiveTime) {
			return (
				<Button type="default" onClick={canConvertToBusiness}>
					转商机
				</Button>
			);
		}
	};
	/* 转商机 */
	const canConvertToBusiness = () => {
		linkTo(`/businessOppty/opptyManage/curd?clueId=${detailData?.id}`);
	};
	const info = ClueStatusList.find((item) => item.value === detailData?.clueStatus);

	/* 认领操作 */
	const claim = () => {
		Modal.confirm({
			title: '领取线索',
			content: '确认要领取该线索吗？领取后你将负责跟进。',
			onOk: async () => {
				const res = await updateProjectClueReceive({ id: clueId });
				if (res.data) {
					getDetail();
					message.success('认领成功');
				}
			},
		});
	};
	// 跳转到客户新增
	const onAddCustomer = () => {
		linkTo('/businessOppty/customerManage/customerDetail');
	};

	/* 监听参数 */
	const changeCustomer = (value) => {
		const customer = customerList.find((ov) => ov.id === value);
		if (customer) {
			baseForm.setFieldsValue({
				customerTypeId: customer.typeId,
			});
		}
	};
	return (
		<div
			id={'clue-detail'}
			ref={setContainer}
			style={{ height: 'calc(100% - 40px)' }}
			className={'flex-sub flex overflowY-auto flex-direction-column padding-20 gap-20 border-radius-4'}
		>
			<Breadcrumb items={[{ title: <Link to={'/businessOppty/clueManage/clueList'}>线索列表</Link> }, { title: '线索详情' }]} />
			<div id={'base'} />
			<Affix
				target={() => document.getElementById('clue-detail')}
				offsetTop={0}
				onChange={(affixed) => console.log('affixed status:', affixed)}
			>
				<div className="padding-20 bg-color-ffffff border-radius-8 flex justify-between align-center line-height-24">
					<Anchor
						affix={false}
						rootClassName="anchor-header-tabBar-box"
						direction="horizontal"
						replace
						targetOffset={140}
						items={[
							{
								key: 'base',
								href: '#base',
								title: <div className="margin-right-40 font-size-16 font-weight-600">基本信息</div>,
							},
							{
								key: 'flower',
								href: '#flower',
								title: <div className="margin-right-40 font-size-16 font-weight-600">跟进情况</div>,
							},
						].filter((ov) => clueId || (!clueId && ov.key === 'base'))}
					/>
					<Space>
						{renderConvertButton()}
						{renderClaimButton()}
						{renderPrimaryButton()}
						{renderSecondaryButton()}
					</Space>
				</div>
			</Affix>
			<section>
				<div className="padding-20 bg-color-ffffff border-radius-8 flex gap-20 flex-direction-column line-height-24 ">
					<div className={'font-weight-600 font-size-16 flex gap-10'}>
						<span>线索基本信息</span>
						{detailData?.cancelStatus === 1 && <Badge status={'default'} text={'已作废'} />}
						{detailData?.cancelStatus === 0 && info && <Badge status={info.status} text={info.label} />}
					</div>
					<Form
						form={baseForm}
						className={''}
						layout="vertical"
						disabled={(clueId && detailData?.cancelStatus === 1) || !isEditable}
						initialValues={{
							clueSourceName: '用户创建',
							clueSourceType: 1,
							clueStatus: 0,
						}}
					>
						<Form.Item label="线索状态" name="clueStatus" hidden>
							<InputNumber placeholder="请输入线索名称" />
						</Form.Item>
						<Form.Item label="meetingId" name="meetingId" hidden>
							<Input placeholder="meetingId" />
						</Form.Item>
						<Row gutter={20}>
							<Col span={8}>
								<Form.Item
									label={
										<Space>
											<span>客户名称</span>
											<Button size={'small'} type={'link'} onClick={onAddCustomer}>
												添加
											</Button>
										</Space>
									}
									tooltip={'列表中不存在客户名称，请先添加客户名称，再进行选择'}
									name={meetingDetail?.companyName || detailData?.customerName ? 'customerName' : 'customerId'}
									required
									rules={[{ required: true, message: '请输入客户名称' }]}
								>
									{meetingDetail?.companyName ? (
										<Select
											placeholder="请选择客户名称"
											disabled={detailData?.customerId}
											options={meetingDetail?.companyName.split('；').map((item) => ({
												label: item,
												value: item,
											}))}
										/>
									) : (
										// <Input placeholder="请输入客户名称"/>
										<Select
											disabled={detailData?.customerId}
											placeholder="请选择客户名称"
											options={customerList}
											onSelect={changeCustomer}
											showSearch
											optionFilterProp={'label'}
										/>
									)}
								</Form.Item>
							</Col>
							<Col span={8}>
								<Form.Item label="客户类型" name="customerTypeId" required rules={[{ required: true, message: '请选择客户类型' }]}>
									<Select placeholder="请选择客户类型" options={clueTypeList} />
								</Form.Item>
							</Col>
							<Col span={8}>
								<Form.Item label="线索名称" name="clueName" required rules={[{ required: true, message: '请输入线索名称' }]}>
									<Input placeholder="请输入线索名称" />
								</Form.Item>
							</Col>
							<Col span={8}>
								<Form.Item label="线索来源类型" name="clueSourceType" hidden>
									<InputNumber placeholder="请输入线索来源" />
								</Form.Item>
								<Form.Item label="线索来源" name="clueSourceName">
									<Input placeholder="请输入线索来源" disabled />
								</Form.Item>
							</Col>
							<Col span={8}>
								<Form.Item label="预计收入金额（万元）" name="expectCharge">
									<InputNumber placeholder="请输入预计收入金额（万元）" className={'width-100per'} />
								</Form.Item>
							</Col>
							<Col span={8}>
								<Form.Item label="线索派发人" name="clueDispatch" required rules={[{ required: true, message: '请输入线索派发人' }]}>
									<Cascader options={departmentList} placeholder="请选择线索派发人" disabled />
								</Form.Item>
							</Col>
							<Col span={8}>
								<Form.Item label="线索接收人" name="clueReceive" required rules={[{ required: true, message: '请输入线索接收人' }]}>
									<Cascader options={departmentList} placeholder="请选择线索接收人" disabled={detailData?.clueReceiveId} />
								</Form.Item>
							</Col>
							<Col span={24}>
								<Form.Item label="线索描述" name="clueDesc">
									<TextArea placeholder="请输入线索描述" rows={4} />
								</Form.Item>
							</Col>
						</Row>
					</Form>
				</div>
			</section>
			{clueId &&
				((detailData?.clueReceiveId === userInfo.id && detailData?.clueStatus !== 0) || detailData?.clueDispatchId === userInfo.id) && (
					<section id={'flower'}>
						<div className="padding-20 bg-color-ffffff border-radius-8 flex gap-20 flex-direction-column line-height-24 ">
							<div className={'flex justify-between'}>
								<div className={'font-weight-600 font-size-16'}>线索跟进情况</div>
								{detailData?.cancelStatus === 0 && (
									<Button type={'primary'} icon={<PlusOutlined />} onClick={addFollowUp}>
										跟进记录
									</Button>
								)}
							</div>
							{/*跟进记录列表*/}
							<Steps progressDot direction="vertical" current={followUpList.length ? 0 : -1} items={followUpList} />
							{followUpList.length === 0 && (
								<div className={'flex justify-center'}>
									<Empty description={'暂无跟进记录'} />
								</div>
							)}
							<FlowerModal
								clueId={clueId}
								ref={flowerModalRef}
								userList={departmentList}
								updateList={getFollowUpList}
								userInfo={userInfo}
							/>
						</div>
					</section>
				)}
		</div>
	);
};
export default ClueDetail;
