/**
 * @description index.jsx - 文件描述
 * <AUTHOR>
 *
 * Created on 2025/4/3 12:02
 */
import React, { useEffect, useMemo, useRef, useState } from 'react';
import { Button, Divider, Form, Input, message, Space, Table, DatePicker, Select, Modal, Row, Col, Badge } from 'antd';
import { CloudDownloadOutlined, PlusOutlined, ReloadOutlined, SearchOutlined } from '@ant-design/icons';
import { exportProjectClue, pageProjectClue, updateProjectClueCancel, updateProjectClueRecover } from '@/api/Opportunity/Clue';
import { ClueSourceList, ClueStatusList, ClueTypeList } from '@/pages/BusinessOppty/ClueManage/ClueList/const';
import { getDeptData } from '@/utils/dictionary';
import { useRouterLink } from '@/hook/useRouter';
import { download } from '@/utils/common';

const { RangePicker } = DatePicker;
const { TextArea } = Input;

const FileName = () => {
	const { linkTo, searchParams } = useRouterLink();

	const [form] = Form.useForm();
	// 部门列表
	const [departmentList, setDepartmentList] = useState([]);
	const [loading, setLoading] = useState(false);
	const [dataSource, setDataSource] = useState([]);
	const [clueType, setClueType] = useState(1);
	const [pagination, setPagination] = useState({
		current: 1,
		pageSize: 10,
		total: 0,
	});
	// 作废原因
	const reasonRef = useRef(null);
	// 初始化获取数据
	useEffect(() => {
		const deptId = searchParams.get('deptId');
		const clueStatus = searchParams.get('clueStatus');
		const clueSourceType = searchParams.get('clueSourceType');
		const initFormValues = {deptId, clueStatus: isNaN(clueStatus) ? undefined : Number(clueStatus), clueSourceType};
		if (deptId || clueStatus || clueSourceType) {
			form.setFieldsValue(initFormValues);
		}
		getTableData();
		getDepartmentList();
	}, []);

	// 获取部门列表
	const getDepartmentList = async () => {
		const res = await getDeptData();
		if (res) {
			console.log(res);
			setDepartmentList(res);
		}
	};
	const exportData = () => {
		console.log('导出');
		// message.warning('导出功能开发中...');
		const { clueName, clueSourceType, clueStatus, createTime, deptId } = form.getFieldsValue();
		const params = {
			clueName,
			clueSourceType,
			clueStatus,
			startTime: createTime?.[0]?.format('YYYY-MM-DD') || '',
			endTime: createTime?.[1]?.format('YYYY-MM-DD') || '',
			deptIds: deptId ? [deptId] : undefined,
			clueType: clueType,
		};
		exportProjectClue(params).then((res) => {
			if (res) {
				console.log(res);
				message.success('导出成功');
				download.excel(res, '线索列表');
			}
		});
	};
	// 新建
	const addData = () => {
		console.log('新建');
		linkTo('/businessOppty/clueManage/clueDetail');
	};
	// 重置
	const onReset = () => {
		form.resetFields();
		getTableData();
	};
	// 获取表格数据
	const getTableData = async (args = {}) => {
		setLoading(true);
		const { clueName, clueSourceType, clueStatus, createTime, deptId } = form.getFieldsValue();
		const params = {
			clueName,
			clueSourceType,
			clueStatus,
			startTime: createTime?.[0]?.format('YYYY-MM-DD') || '',
			endTime: createTime?.[1]?.format('YYYY-MM-DD') || '',
			deptIds: deptId ? [deptId] : undefined,
			pageNum: pagination.current,
			pageSize: pagination.pageSize,
			clueType: clueType,
			...args,
		};
		try {
			const res = await pageProjectClue(params);
			if (res.data) {
				setDataSource(res.data.records);
				setPagination({
					total: +res.data.total,
					current: params.pageNum,
					pageSize: params.pageSize,
				});
			}
		} catch (e) {
			console.log(e?.msg || '请求失败');
		}
		setLoading(false);
	};
	// 修改页码
	const changePage = (page, pageSize) => {
		getTableData({
			pageNum: page,
			pageSize: pageSize,
		});
	};
	// 切换tab
	const changeTab = (key) => {
		setClueType(key);
		getTableData({ clueType: key });
	};
	// 编辑
	const editData = (record) => {
		linkTo(`/businessOppty/clueManage/clueDetail?id=${record.id}`);
	};
	// 恢复
	const recoverData = async (record) => {
		const res = await updateProjectClueRecover({ id: record.id });
		if (res.data) {
			message.success('恢复成功');
			getTableData();
		}
	};
	// 作废
	const deleteData = async (record) => {
		Modal.confirm({
			title: '请输入作废的原因？',
			width: 500,
			content: (
				<TextArea rows={4} ref={reasonRef} onChange={(e) => (reasonRef.current.value = e.target.value)} placeholder="请输入作废的原因" />
			),
			onOk: async () => {
				if (!reasonRef.current.value) {
					message.warning('请输入作废的原因');
					return Promise.reject();
				}
				const params = {
					reason: reasonRef.current.value,
					id: record.id,
				};
				const res = await updateProjectClueCancel(params);
				if (res) {
					message.success('作废成功');
					getTableData();
				}
			},
		});
	};

	const columns = [
		{
			title: '序号',
			dataIndex: 'id',
			key: 'id',
			align: 'center',
			width: 100,
			render: (id, record, index) => {
				return index + 1 + (pagination.current - 1) * pagination.pageSize;
			},
		},
		{
			title: '线索名称',
			dataIndex: 'clueName',
			key: 'clueName',
			align: 'center',
			width: 200,
		},
		{
			title: '线索来源',
			dataIndex: 'clueSourceName',
			key: 'clueSourceName',
			align: 'center',
			width: 200,
		},
		{
			title: '线索状态',
			dataIndex: 'clueStatus',
			key: 'clueStatus',
			align: 'center',
			width: 200,
			render: (clueStatus, record) => {
				if (record.cancelStatus === 1) return <Badge status={'default'} text={'已作废'} />;
				const info = ClueStatusList.find((item) => item.value === clueStatus);
				return <Badge status={info.status} text={info.label} />;
			},
		},
		{
			title: '分配人',
			dataIndex: 'clueDispatchName',
			key: 'clueDispatchName',
			align: 'center',
			width: 200,
		},
		{
			title: '接收人',
			dataIndex: 'clueReceiveName',
			key: 'clueReceiveName',
			align: 'center',
			width: 200,
		},
		{
			title: '所属部门',
			dataIndex: 'clueReceiveDeptName',
			key: 'clueReceiveDeptName',
			align: 'center',
			width: 200,
		},
		{
			title: '接收时间',
			dataIndex: 'receiveTime',
			key: 'receiveTime',
			align: 'center',
			width: 200,
		},
		{
			title: '操作',
			dataIndex: 'action',
			key: 'action',
			align: 'center',
			width: 200,
			render: (_, record) => {
				return (
					<Space>
						<Button type={'link'} onClick={() => editData(record)}>
							查看/编辑
						</Button>
						{record.cancelStatus === 1 ? (
							<Button type={'link'} onClick={() => recoverData(record)}>
								恢复
							</Button>
						) : (
							<Button type={'link'} danger onClick={() => deleteData(record)}>
								作废
							</Button>
						)}
					</Space>
				);
			},
		},
	];
	return (
		<div className={'flex-sub flex flex-direction-column margin-20 padding-20  border-radius-4'}>
			<div className="padding-20 bg-color-ffffff border-radius-8 flex justify-between align-center line-height-24 margin-bottom-20">
				<div className={'font-size-16 flex flex-sub gap-40'}>
					{ClueTypeList.map((item) => {
						return (
							<div key={item.value} className={'flex align-center'} onClick={() => changeTab(item.value)}>
								<span
									className={`font-size-16 cursor-pointer font-weight-600 ${clueType === item.value ? 'color-165dff' : 'color-1d2129'}`}
								>
									{item.label}
								</span>
							</div>
						);
					})}
				</div>
				<Space>
					<Button icon={<CloudDownloadOutlined />} onClick={exportData}>
						导出
					</Button>
					<Button type={'primary'} icon={<PlusOutlined />} onClick={addData}>
						新建
					</Button>
				</Space>
			</div>
			<div className={'bg-color-ffffff padding-20 border-radius-8  '}>
				<Form form={form} layout={'inline'} className={'width-100per flex flex-sub align-center'}>
					<Row gutter={[20, 20]} className={'flex-sub'}>
						<Col span={8}>
							<Form.Item label="线索名称" name="clueName" className={'flex-sub '}>
								<Input placeholder={'请输入线索名称'} className={'width-100per'} allowClear />
							</Form.Item>
						</Col>
						<Col span={8}>
							<Form.Item label="创建时间" name="createTime" className={'flex-sub '}>
								<RangePicker className={'width-100per'} placeholder={'请选择创建时间'} allowClear />
							</Form.Item>
						</Col>
						<Col span={8}>
							<Form.Item label="线索来源" name="clueSourceType" className={'flex-sub '}>
								<Select options={ClueSourceList} className={'width-100per'} placeholder={'请选择线索来源'} allowClear />
							</Form.Item>
						</Col>
						{clueType !== 4 && (
							<Col span={8}>
								<Form.Item label="线索状态" name="clueStatus" className={'flex-sub '}>
									<Select options={ClueStatusList} className={'width-100per'} placeholder={'请选择线索状态'} allowClear />
								</Form.Item>
							</Col>
						)}
						<Col span={8}>
							<Form.Item label="所属部门" name="deptId" className={'flex-sub '}>
								<Select placeholder={'请选择所属部门'} className={'width-100per'} options={departmentList} allowClear />
							</Form.Item>
						</Col>
					</Row>
					{/*<Divider type="vertical" style={{margin: '0 16px'}}/>*/}
					<div className="width-1 height-84 bg-color-e5e6eb flex-shirnk margin-lr-20" />
					<Form.Item noStyle>
						<Space direction={'vertical'}>
							<Button type={'primary'} htmlType={'submit'} icon={<SearchOutlined />} onClick={() => getTableData()}>
								查询
							</Button>
							<Button htmlType={'reset'} icon={<ReloadOutlined />} onClick={onReset}>
								重置
							</Button>
						</Space>
					</Form.Item>
				</Form>
				<Divider />
				<Table
					rowKey="id"
					columns={columns}
					loading={loading}
					pagination={{
						...pagination,
						onChange: changePage,
						showSizeChanger: true,
						showTotal: (total) => `共 ${total} 条`,
					}}
					dataSource={dataSource}
				/>
			</div>
		</div>
	);
};
export default FileName;
