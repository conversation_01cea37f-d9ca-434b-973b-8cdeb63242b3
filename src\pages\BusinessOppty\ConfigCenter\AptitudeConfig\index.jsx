/**
 * @description AptitudeConfig - 文件描述
 * <AUTHOR>
 *
 * Created on 2025/6/9 14:41
 */
import React, { useEffect, useState } from 'react';
import { Button, Form, Input, InputNumber, Modal, Space, Table, message } from 'antd';
import { PlusOutlined, SearchOutlined } from '@ant-design/icons';
import { addAptitude, delAptitude, pageAptitude, updateAptitude } from '@/api/Bidmgt/ConfigCenter/AptitudeConfig';

const AptitudeConfig = () => {
	const [form] = Form.useForm();
	const [isModalOpen, setIsModalOpen] = useState(false);
	// 关键词查询
	const [name, setName] = useState('');
	// 操作数据修改
	const [curRow, setCurRow] = useState({});
	// 数据列表
	const [dataSource, setDataSource] = useState([]);

	useEffect(() => {
		getList();
	}, []);
	// 查询列表
	const getList = async () => {
		console.log('查询资质配置');
		const params = {
			aptitudeName: name,
			pageSize: 10000,
			pageNum: 1,
		};
		const res = await pageAptitude(params);
		if (res.data) {
			setDataSource(res.data.records);
		}
	};
	// 删除
	const deleteItem = async (record) => {
		console.log('删除资质配置');
		Modal.confirm({
			title: '删除资质配置',
			content: `确定要删除这条数据吗？`,
			onOk: async () => {
				const res = await delAptitude({ id: record.id });
				if (res.data) {
					message.success('删除成功');
					getList();
					return Promise.resolve();
				} else {
					message.error('删除失败');
					return Promise.reject();
				}
			},
		});
	};
	// 修改
	const updateItem = (record) => {
		console.log('修改资质配置');
		setCurRow(record);
		form.setFieldsValue(record);
		setIsModalOpen(true);
	};
	// 修改页面
	const changePage = (pageNum, pageSize) => {
		console.log('修改资质配置');
	};
	// 新增
	const addItem = () => {
		console.log('新增资质配置');
		setCurRow({});
		setIsModalOpen(true);
	};
	// 新增/修改弹窗
	const handleOk = async () => {
		try {
			const id = curRow?.id;
			const values = await form.validateFields();
			console.log('新增/修改资质配置', values);
			let res;
			if (id) {
				res = await updateAptitude({ id, ...values });
			} else {
				res = await addAptitude(values);
			}
			if (res.data) {
				message.success(id ? '修改成功' : '添加成功');
				setIsModalOpen(false);
				form.resetFields();
				getList();
			}
		} catch (e) {
			const errors = e?.errorFields.map(({ errors }) => errors) || e;
			throw new Error(errors);
		}
	};
	// 表格列配置
	const columns = [
		{
			title: '序号',
			dataIndex: 'index',
			key: 'index',
			width: 110,
			render: (_, record, index) => {
				return index + 1;
			},
		},
		{
			title: '资质名称',
			dataIndex: 'aptitudeName',
			key: 'aptitudeName',
		},
		{
			title: '排序',
			dataIndex: 'rankingNum',
			key: 'rankingNum',
		},
		{
			title: '操作',
			key: 'action',
			width: 220,
			render: (_, record) => {
				return (
					<Space>
						<Button size={'small'} type="link" onClick={() => updateItem(record)}>
							修改
						</Button>
						<Button size={'small'} danger type="link" onClick={() => deleteItem(record)}>
							删除
						</Button>
					</Space>
				);
			},
		},
	];
	return (
		<div className={'flex-sub flex flex-direction-column padding-20'}>
			<div className="padding-20 flex-sub bg-color-ffffff border-radius-4">
				<div className="font-size-16 font-weight-500 color-1d2129 line-height-24 margin-bottom-20">客户资质管理</div>
				<div className="flex justify-between align-center margin-bottom-20">
					<div className="flex-sub">
						<Button type="primary" icon={<PlusOutlined />} onClick={addItem}>
							新建
						</Button>
					</div>
					<Input
						placeholder="请输入关键词"
						className="width-280 margin-right-14"
						suffix={<SearchOutlined />}
						onInput={(e) => {
							setName(e.target.value || '');
						}}
						allowClear
						onPressEnter={getList}
					/>
					<Button type="primary" icon={<SearchOutlined />} onClick={() => getList()}>
						查询
					</Button>
				</div>
				<Table
					columns={columns}
					dataSource={dataSource}
					pagination={false}
					onChange={changePage}
					scroll={{ x: 'max-content' }}
					rowKey={'id'}
				/>
			</div>
			<Modal
				title={curRow?.id ? '修改资质配置' : '新增资质配置'}
				open={isModalOpen}
				onCancel={() => {
					setIsModalOpen(false);
				}}
				onOk={handleOk}
			>
				<Form form={form} layout={'vertical'}>
					<Form.Item label={'资质名称'} name={'aptitudeName'} required={true} rules={[{ required: true, message: '请输入资质名称' }]}>
						<Input placeholder={'请输入资质名称'} />
					</Form.Item>
					<Form.Item label={'排序'} name={'rankingNum'} rules={[{ type: 'number', message: '请输入正确的排序' }]}>
						<InputNumber placeholder={'请输入排序'} className={'width-100per'} />
					</Form.Item>
				</Form>
			</Modal>
		</div>
	);
};
export default AptitudeConfig;
