import { useEffect, useState } from 'react';

import { Button, Col, Form, Modal, Row, Select, Space, Table, message } from 'antd';
import { ReloadOutlined, SearchOutlined } from '@ant-design/icons';

import { useTableData } from '@/hook/useTableData';

import { getDeptData } from '@/utils/dictionary';
import { getByPermissionPerms } from '@/api/common';

import { employeeList, saveEmployee, deleteBatch } from '@/api/BusinessOppty/AttendanceManage/index.js';

const Index = () => {
	const { form, dataSource, pagination, getTableData, changePage, onReset, onSearch } = useTableData({
		params: {},
		firstActiveLoad: true,
		getTablePageData: (data) => {
			const { addStatus, deptId, staffId, pageNum, pageSize } = data;
			console.log(addStatus, deptId, staffId);
			return new Promise((resolve) => {
				const result = centerStaffList
					.filter((ov) => {
						return (
							(addStatus === undefined || ov.addStatus === addStatus) &&
							(deptId === undefined || (ov.deptList || []).some((oov) => oov.id === deptId)) &&
							(staffId === undefined || ov.id === staffId)
						);
					})
					.slice((pageNum - 1) * pageSize, pageNum * pageSize);
				resolve({
					data: {
						records: result,
						total: centerStaffList.length,
					},
				});
			});
		},
	});

	// 部门列表
	const [departmentList, setDepartmentList] = useState([]);

	// 获取部门列表
	const getDepartmentList = async () => {
		const res = await getDeptData();
		if (res) {
			setDepartmentList(res);
		}
	};

	// 中心人员名单
	const [centerStaffList, setCenterStaffList] = useState([]);

	// 获取中心人员名单
	const getCenterStaffList = async () => {
		// 获取考勤名单 列表
		const employeeData = (await employeeList()) || [];

		const res = await getByPermissionPerms({ perms: 'businessOppty' }, { showLoading: false });
		if (res) {
			const addStatusIds = (employeeData.data || []).map((ov) => ov.userId);
			setCenterStaffList(
				res.data.map((ov) => ({
					label: ov.userName,
					value: ov.id,
					addStatus: addStatusIds.includes(ov.id) ? 1 : 0,
					...ov,
				}))
			);
		}
	};

	useEffect(() => {
		getDepartmentList();
		getCenterStaffList();
	}, []);

	useEffect(() => {
		if (centerStaffList.length) {
			getTableData();
		}
	}, [centerStaffList]);

	// 修改考勤状态
	const changeAddStatus = (record) => {
		if (record.addStatus === 1) {
			Modal.confirm({
				title: '提示',
				content: '确定要移除该员工吗？',
				okText: '确定',
				cancelText: '取消',
				centered: true,
				onOk: () => {
					deleteBatch({
						userIds: [record.id],
					}).then(() => {
						_changeAddStatus();
					});
				},
			});
		} else {
			saveEmployee({
				userIds: [record.id],
			}).then(() => {
				_changeAddStatus();
			});
		}

		function _changeAddStatus() {
			const index = centerStaffList.findIndex((ov) => ov.id === record.id);
			centerStaffList[index].addStatus = record.addStatus === 1 ? 0 : 1;
			setCenterStaffList([...centerStaffList]);
			message.success('操作成功');
		}
	};

	return (
		<div className={'flex-sub flex flex-direction-column margin-20 border-radius-4'}>
			{/* 筛选条件 开始 */}
			<div className="padding-20 flex-sub bg-color-ffffff border-radius-4">
				<div className="flex">
					<Form
						form={form}
						labelCol={{
							style: { width: '80px' },
						}}
						labelAlign="left"
						className="form-filter flex-sub"
					>
						<Row gutter={[20, 20]}>
							<Col span={8}>
								<Form.Item name="addStatus" label="考勤状态">
									<Select
										allowClear
										className="flex-sub"
										placeholder="请选择考勤状态"
										options={[
											{ value: 0, label: '未添加' },
											{ value: 1, label: '已添加' },
										]}
									/>
								</Form.Item>
							</Col>
							<Col span={8}>
								<Form.Item name="deptId" label="部门名称">
									<Select allowClear className="flex-sub" placeholder="请选择部门" options={departmentList} />
								</Form.Item>
							</Col>
							<Col span={8}>
								<Form.Item name="staffId" label="考勤员工">
									<Select
										placeholder={'请选择员工'}
										className={'width-100per'}
										options={centerStaffList}
										optionFilterProp={'label'}
										maxTagCount={'responsive'}
										showSearch
										allowClear
									/>
								</Form.Item>
							</Col>
						</Row>
					</Form>
					<div className="margin-lr-12 width-1 height-32 bg-color-e5e6eb"></div>
					<Space className="flex-shirnk">
						<Button type="primary" icon={<SearchOutlined />} onClick={() => onSearch()}>
							查询
						</Button>
						<Button type="default" icon={<ReloadOutlined />} onClick={() => onReset()}>
							重置
						</Button>
					</Space>
				</div>
			</div>
			{/* 筛选条件 结束 */}

			{/* 表格 开始 */}
			<div className="margin-top-12 padding-20 flex-sub bg-color-ffffff border-radius-4">
				<Table rowKey="id" dataSource={dataSource} pagination={pagination} onChange={changePage} scroll={{ x: 'max-content' }}>
					<Table.Column
						title="序号"
						key="index"
						width={60}
						align="center"
						render={(_, __, index) => {
							return index + 1;
						}}
					/>
					<Table.Column title="员工名字" dataIndex="userName" />
					<Table.Column
						title="所属部门"
						dataIndex="deptList"
						render={(deptList) => (
							<div>
								{(deptList || []).map((ov) => (
									<div key={ov.id}>{ov.name}</div>
								))}
							</div>
						)}
					/>
					<Table.Column title="所属公司" dataIndex="companyName" render={(text) => text || '--'} />
					<Table.Column
						title="考勤状态"
						dataIndex="addStatus"
						render={(text) => {
							return <div className={`tag-status-small-${['warning', 'primary'][text || 0]}`}>{['未添加', '已添加'][text || 0]}</div>;
						}}
					/>
					<Table.Column
						title="操作"
						key="option"
						dataIndex="option"
						align="center"
						fixed="right"
						width="160px"
						render={(_, record) => {
							return (
								<>
									<Button type="link" size="small" onClick={() => changeAddStatus(record)}>
										{record.addStatus === 1 ? '移除' : '添加'}
									</Button>
								</>
							);
						}}
					/>
				</Table>
			</div>
			{/* 表格 结束 */}
		</div>
	);
};

export default Index;
