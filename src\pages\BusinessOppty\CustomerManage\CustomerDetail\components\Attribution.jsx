/**
 * @description Attribution.jsx - 文件描述
 * <AUTHOR>
 *
 * Created on 2025/5/20 9:28
 */
import React, {forwardRef, useEffect, useImperativeHandle, useState} from 'react';
import {Button, Cascader, Descriptions, Form, Input, message, Table} from "antd";
import {EditOutlined, PlusOutlined} from "@ant-design/icons";

const labelStyle = {
    width: 130,
    justifyContent: 'flex-end',
}
const Attribution = ({detail, isEditState, deptList, visitList, userInfo, ownerPermission, distributeRole}, ref) => {
    const [form] = Form.useForm();
    // 合作记录数据
    const [customerInfo, setCustomerInfo] = useState({
        //    签单人
        customerContractees: [],
        //    执行人
        customerExecutors: [],
    });
    const [visitors, setVisitors] = useState([]);
    /* 表单提交 */
    const handleSubmit = async () => {
        try {
            const values = await form.validateFields();
            console.log(values);
            return values;
        } catch (e) {
            console.log(e);
            message.warning(e.errorFields[0].errors[0]);
            throw new Error('校验失败');
        }
    }
    useEffect(() => {
        if (isEditState && detail?.id) {
            const {customerOwners, customerGuiders} = detail;
            form.setFieldsValue({
                customerOwners: customerOwners.map(item => [item.departmentId, item.userId]),
                customerGuiders: customerGuiders.map(item => [item.departmentId, item.userId]),
            });
        }
        if (isEditState && !detail?.id) {
            // 创建设置默认归属人为当前用户
            const {id, deptList = []} = userInfo;
            form.setFieldsValue({
                customerOwners: [[deptList[0].id, id]],
            });
        }
        if (detail?.customerCooperationRecords?.length) {
            const allContracteeNames = detail?.customerCooperationRecords?.flatMap(
                record => record.customerContractees.map(ov => ov.userName)
            ) || [];
            const allExecutorNames = detail?.customerCooperationRecords?.flatMap(
                record => record.customerExecutors.map(ov => ov.userName)
            ) || [];
            const uniqueContracteeNames = [...new Set(allContracteeNames)];
            const uniqueExecutorNames = [...new Set(allExecutorNames)];
            setCustomerInfo({
                customerContractees: uniqueContracteeNames,
                customerExecutors: uniqueExecutorNames,
            });
        }
    }, [detail, isEditState]);

    useEffect(() => {
        if (visitList?.length) {
            const allVisitors = visitList.reduce((acc, record) => {
                const meeting = record.meetings?.[0];
                if (meeting?.participants?.length) {
                    meeting.participants.forEach(participant => {
                        const name = participant.name?.trim();
                        if (participant.type === 4 && name) {
                            acc.push(name);
                        }
                    });
                }
                return acc;
            }, []);
            const uniqueVisitors = [...new Set(allVisitors)];
            setVisitors(uniqueVisitors);
        } else {
            setVisitors([]);
        }
    }, [visitList]);
    // 数组去重
    const uniqueArray = (arr) => {
        return [...new Set(arr)];
    };
    useImperativeHandle(ref, () => ({
        handleSubmit,
    }));
    return (
        <div className={'flex flex-direction-column gap-20 padding-20 bg-color-ffffff border-radius-8 line-height-24'}>
            <div className={'flex width-100per justify-between'}>
                <div className={'font-weight-600 font-size-16'}>客户归属</div>
            </div>
            {
                isEditState ? (
                        <Form form={form} disabled={(!ownerPermission && !distributeRole) && detail.id} layout={'horizontal'} className={'width-468'} labelCol={{flex: '130px'}}>
                            <Form.Item
                                name={'customerOwners'}
                                label={'归属人及归属部门'}
                                required
                                rules={[{required: true, message: '归属人及归属部门不能为空'}]}
                            >
                                <Cascader multiple placeholder={'请选择归属人及归属部门'} showCheckedStrategy={Cascader.SHOW_CHILD}
                                          options={deptList}/>
                            </Form.Item>
                            <Form.Item name={'customerGuiders'} label={'引流人'}>
                                <Cascader multiple placeholder={'请选择引流人'} showCheckedStrategy={Cascader.SHOW_CHILD}
                                          options={deptList}/>
                            </Form.Item>
                        </Form>) :
                    (<Descriptions column={1} labelStyle={labelStyle}>
                        <Descriptions.Item label="归属人">
                            {
                                (uniqueArray(detail?.customerOwners?.map(item => item.userName))).join('、') || <span className={'color-86909c'}>暂无</span>
                            }
                        </Descriptions.Item>
                        <Descriptions.Item label="归属部门">
                            {
                                (uniqueArray(detail?.customerOwners?.map(item => item.departmentName))).join('、') || <span className={'color-86909c'}>暂无</span>
                            }
                        </Descriptions.Item>
                        <Descriptions.Item label="引流人">
                            {
                                (uniqueArray(detail?.customerGuiders?.map(item => item.userName))).join('、') || <span className={'color-86909c'}>暂无</span>
                            }
                        </Descriptions.Item>
                        <Descriptions.Item label="拜访人">
                            {
                                (uniqueArray(visitors))?.join('、') || <span className={'color-86909c'}>暂无</span>
                            }
                        </Descriptions.Item>
                        <Descriptions.Item label="签单人">
                            {
                                (uniqueArray(customerInfo.customerContractees))?.join('、') || <span className={'color-86909c'}>暂无</span>
                            }
                        </Descriptions.Item>
                        <Descriptions.Item label="交付执行人">
                            {
                                (uniqueArray(customerInfo.customerExecutors))?.join('、') || <span className={'color-86909c'}>暂无</span>
                            }
                        </Descriptions.Item>
                    </Descriptions>)
            }
        </div>)
}
export default forwardRef(Attribution);
