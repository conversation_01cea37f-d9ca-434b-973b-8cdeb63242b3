/**
 * @description BusinessInfo.jsx - 文件描述
 * <AUTHOR>
 *
 * Created on 2025/5/20 9:28
 */
import React, { forwardRef, useEffect, useImperativeHandle, useState } from 'react';
import { Button, Cascader, DatePicker, Descriptions, Form, Input, InputNumber, Modal, message, Space, Table } from 'antd';
import { EditOutlined, PlusOutlined } from '@ant-design/icons';
import dayjs from 'dayjs';
import { addCustomerFinance, deleteCustomerFinance, updateCustomerFinance } from '@/api/Opportunity/Customer';

const labelStyle = {
	width: 130,
	justifyContent: 'flex-end',
};
const BusinessInfo = ({ detail, isEditState }, ref) => {
	const [dataSource, setDataSource] = useState([]);
	const [visible, setVisible] = useState(false);
	const [record, setRecord] = useState({});
	const columns = [
		{
			title: '财报年份',
			dataIndex: 'financialYear',
			key: 'financialYear',
		},
		{
			title: '营业收入',
			dataIndex: 'operatingRevenue',
			key: 'operatingRevenue',
			render: (value) => (value ? `${value}万元` : '-'),
		},
		{
			title: '总成本费用',
			dataIndex: 'totalCost',
			key: 'totalCost',
			render: (value) => (value ? `${value}万元` : '-'),
		},
		{
			title: '研发费用',
			dataIndex: 'devExpenses',
			key: 'devExpenses',
			render: (value) => (value ? `${value}万元` : '-'),
		},
		{
			title: '利润总额',
			dataIndex: 'totalProfit',
			key: 'totalProfit',
			render: (value) => (value ? `${value}万元` : '-'),
		},
		{
			title: '净利润',
			dataIndex: 'netProfit',
			key: 'netProfit',
			render: (value) => (value ? `${value}万元` : '-'),
		},
		{
			title: '资产总额',
			dataIndex: 'totalAssets',
			key: 'totalAssets',
			render: (value) => (value ? `${value}万元` : '-'),
		},
		{
			title: '净资产',
			dataIndex: 'netAssets',
			key: 'netAssets',
			render: (value) => (value ? `${value}万元` : '-'),
		},
		{
			title: '负债总额',
			dataIndex: 'totalLiabilities',
			key: 'totalLiabilities',
			render: (value) => (value ? `${value}万元` : '-'),
		},
	];
	const [form] = Form.useForm();
	const [formModal] = Form.useForm();

	useEffect(() => {
		if (detail?.id && isEditState) {
			const {
				firstClassQuantity,
				inventionPatentQuantity,
				secondClassQuantity,
				utilityModelQuantity,
				industrialDesignQuantity,
				softwareCopyrightQuantity,
			} = detail;
			form.setFieldsValue({
				firstClassQuantity: firstClassQuantity || undefined,
				inventionPatentQuantity: inventionPatentQuantity || undefined,
				secondClassQuantity: secondClassQuantity || undefined,
				utilityModelQuantity: utilityModelQuantity || undefined,
				industrialDesignQuantity: industrialDesignQuantity || undefined,
				softwareCopyrightQuantity: softwareCopyrightQuantity || undefined,
			});
		}
		if (detail?.customerFinances?.length) {
			setDataSource(detail.customerFinances);
		}
	}, [detail, isEditState]);
	if (isEditState) {
		columns.push({
			title: '操作',
			dataIndex: 'options',
			key: 'options',
			render: (inviteName, record) => {
				return (
					<Space>
						<Button type="link" size={'small'} onClick={() => handleUpdateRecord(record)}>
							编辑
						</Button>
						<Button type="link" size={'small'} danger onClick={() => handleDeleteRecord(record)}>
							删除
						</Button>
					</Space>
				);
			},
			fixed: 'right',
			width: 100,
		});
	}

	// 操作记录
	const handleUpdateRecord = (record) => {
		const values = {
			...formatNumber(record),
			financialYear: dayjs(record.financialYear),
		};
		formModal.setFieldsValue(values);
		setRecord({
			...record,
			update: true,
		});
		setVisible(true);
	};
	// 新增记录
	const handleAddRecord = () => {
		formModal.resetFields();
		setRecord({
			create: true,
			id: Math.random().toString(36).substring(2, 15),
		});
		setVisible(true);
	};
	// 删除财务数据
	const handleDeleteRecord = (record) => {
		Modal.confirm({
			title: '确定删除该财务数据吗？',
			okText: '确定',
			cancelText: '取消',
			onOk: async () => {
				if (detail?.id && !record.create) {
					// 已创建并且非创建的记录
					try {
						const res = await deleteCustomerFinance({ id: record.id, customerId: detail.id });
						if (res.data) {
							message.success('删除财务数据成功');
						}
					} catch (e) {
						message.error('删除财务数据失败');
						return Promise.reject();
					}
				}
				const newDataSource = dataSource.filter((item) => item.id !== record.id);
				setDataSource(newDataSource);
			},
		});
	};
	// 关闭记录弹框
	const closeModal = () => {
		setVisible(false);
		formModal.resetFields();
		setRecord({});
	};
	// 提交财务数据弹框
	const handleSubmit = async () => {
		const { update, create } = record;
		let values = await formModal.validateFields();
		values = formatNumber(values);
		if (create && !update) {
			// 新增
			console.log('新增财务数据', values);
			if (detail?.id) {
				try {
					const res = await addCustomerFinance({
						...values,
						financialYear: values.financialYear.format('YYYY'),
						customerId: detail.id,
					});
					if (res.data) {
						message.success('新增财务数据成功');
						values.id = res.data;
						delete record?.create;
					}
				} catch (e) {
					message.error('新增财务数据失败');
					return Promise.reject();
				}
			}
			setDataSource([
				...dataSource,
				{
					...record,
					...values,
					financialYear: values.financialYear.format('YYYY'),
				},
			]);
		} else {
			// 修改
			console.log('修改财务数据', values);
			if (detail?.id && !create) {
				try {
					const res = await updateCustomerFinance({
						...values,
						id: record?.id,
						financialYear: values.financialYear.format('YYYY'),
					});
					if (res.data) {
						message.success('修改财务数据成功');
					}
				} catch (e) {
					message.error('修改财务数据失败');
					return Promise.reject();
				}
			}
			setDataSource(
				dataSource.map((item) => {
					if (item.id === record?.id) {
						return {
							...item,
							...values,
							financialYear: values.financialYear.format('YYYY'),
						};
					}
					return item;
				})
			);
		}
		closeModal();
	};
	// 将对象内的数字类型转换为字符串
	const formatNumber = (obj) => {
		for (const key in obj) {
			if (typeof obj[key] === 'number') {
				obj[key] = String(obj[key]);
			}
		}
		return obj;
	};
	//保存提交数据
	const handleSave = async () => {
		console.log('保存财务信息', dataSource);
		const values = await form.validateFields();
		/* 首次创建提交信息 */
		if (!detail?.id) {
			values.customerFinances = dataSource.map((item) => {
				const { id, ...rest } = item;
				return rest;
			});
		}
		return {
			...values,
		};
	};
	// 暴露给父组件
	useImperativeHandle(ref, () => ({
		handleSubmit: handleSave,
	}));
	return (
		<div className={'flex flex-direction-column gap-20 padding-20 bg-color-ffffff border-radius-8 line-height-24'}>
			<div className={'flex width-100per justify-between'}>
				<div className={'font-weight-600 font-size-16'}>经营信息</div>
			</div>
			<div className={'flex justify-between'}>
				<div className={'font-size-14 font-weight-500 color-165dff'}>财务数据</div>
				{isEditState && (
					<Button type={'primary'} size={'small'} icon={<PlusOutlined />} onClick={handleAddRecord}>
						添加
					</Button>
				)}
			</div>
			<Table rowKey="rowKey" dataSource={dataSource} pagination={false} columns={columns} size={'small'} />
			<Modal title={record?.create ? '添加财务信息' : '编辑财务信息'} open={visible} onOk={handleSubmit} onCancel={closeModal} width={600}>
				<Form form={formModal} layout={'horizontal'} labelCol={{ span: 6 }} wrapperCol={{ span: 14 }}>
					<Form.Item label={'财务年份'} name={'financialYear'} required rules={[{ required: true, message: '请选择财务年份' }]}>
						<DatePicker picker="year" placeholder={'请选择财务年份'} className={'width-100per'} />
					</Form.Item>
					<Form.Item label={'营业收入'} name={'operatingRevenue'}>
						<InputNumber
							className={'width-100per'}
							placeholder={'请输入营业收入（万元）'}
							// formatter={value => `¥ ${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',')}
						/>
					</Form.Item>
					<Form.Item label={'总成本费用'} name={'totalCost'}>
						<InputNumber
							className={'width-100per'}
							placeholder={'请输入总成本费用（万元）'}
							// formatter={value => `¥ ${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',')}
						/>
					</Form.Item>
					<Form.Item label={'研发费用'} name={'devExpenses'}>
						<InputNumber
							className={'width-100per'}
							placeholder={'请输入研发费用（万元）'}
							// formatter={value => `¥ ${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',')}
						/>
					</Form.Item>
					<Form.Item label={'利润总额'} name={'totalProfit'}>
						<InputNumber
							className={'width-100per'}
							placeholder={'请输入利润总额（万元）'}
							// formatter={value => `¥ ${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',')}
						/>
					</Form.Item>
					<Form.Item label={'净利润'} name={'netProfit'}>
						<InputNumber
							className={'width-100per'}
							placeholder={'请输入净利润（万元）'}
							// formatter={value => `¥ ${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',')}
						/>
					</Form.Item>
					<Form.Item label={'资产总额'} name={'totalAssets'}>
						<InputNumber
							className={'width-100per'}
							placeholder={'请输入资产总额（万元）'}
							// formatter={value => `¥ ${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',')}
						/>
					</Form.Item>
					<Form.Item label={'净资产'} name={'netAssets'}>
						<InputNumber
							className={'width-100per'}
							placeholder={'请输入净资产（万元）'}
							// formatter={value => `¥ ${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',')}
						/>
					</Form.Item>
					<Form.Item label={'负债总额'} name={'totalLiabilities'}>
						<InputNumber
							className={'width-100per'}
							placeholder={'请输入负债总额（万元）'}
							// formatter={value => `¥ ${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',')}
						/>
					</Form.Item>
				</Form>
			</Modal>
			<div className={'flex justify-between'}>
				<div className={'font-size-14 font-weight-500 color-165dff'}>知识产权</div>
			</div>
			{isEditState ? (
				<Form form={form} layout={'horizontal'} className={'width-468'} labelCol={{ flex: '130px' }}>
					<Form.Item label="Ⅰ类知识产权数量" name={'firstClassQuantity'}>
						<Input placeholder={'请输入Ⅰ类知识产权数量'} />
					</Form.Item>
					<Form.Item label="发明专利数量" name={'inventionPatentQuantity'}>
						<Input placeholder={'请输入发明专利数量'} />
					</Form.Item>
					<Form.Item label="Ⅱ类知识产权数量" name={'secondClassQuantity'}>
						<Input placeholder={'请输入Ⅱ类知识产权数量'} />
					</Form.Item>
					<Form.Item label="实用新型专利数量" name={'utilityModelQuantity'}>
						<Input placeholder={'请输入实用新型专利数量'} />
					</Form.Item>
					<Form.Item label="外观设计专利数量" name={'industrialDesignQuantity'}>
						<Input placeholder={'请输入外观设计专利数量'} />
					</Form.Item>
					<Form.Item label="软件著作权数量" name={'softwareCopyrightQuantity'}>
						<Input placeholder={'请输入软件著作权数量'} />
					</Form.Item>
				</Form>
			) : (
				<Descriptions column={1} labelStyle={labelStyle}>
					<Descriptions.Item label="Ⅰ类知识产权数量">{detail?.firstClassQuantity || '-'}</Descriptions.Item>
					<Descriptions.Item label="发明专利数量">{detail?.inventionPatentQuantity || '-'}</Descriptions.Item>
					<Descriptions.Item label="Ⅱ类知识产权数量">{detail?.secondClassQuantity || '-'}</Descriptions.Item>
					<Descriptions.Item label="实用新型专利数量">{detail?.utilityModelQuantity || '-'}</Descriptions.Item>
					<Descriptions.Item label="外观设计专利数量">{detail?.industrialDesignQuantity || '-'}</Descriptions.Item>
					<Descriptions.Item label="软件著作权数量">{detail?.softwareCopyrightQuantity || '-'}</Descriptions.Item>
				</Descriptions>
			)}
		</div>
	);
};
export default forwardRef(BusinessInfo);
