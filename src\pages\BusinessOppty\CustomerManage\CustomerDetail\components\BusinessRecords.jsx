/**
 * @description BusinessRecords.jsx - 文件描述
 * <AUTHOR>
 *
 * Created on 2025/5/20 9:28
 */
import React, {forwardRef, useImperativeHandle, useRef, useState} from 'react';
import {<PERSON><PERSON>, <PERSON><PERSON>, DatePicker} from "antd";
import FollowRecord from "@/pages/BusinessOppty/CustomerManage/CustomerDetail/components/FollowRecord";
import CooperateRecord from "@/pages/BusinessOppty/CustomerManage/CustomerDetail/components/CooperateRecord";
import ClueRecord from "@/pages/BusinessOppty/CustomerManage/CustomerDetail/components/ClueRecord";
import OppoRecord from "@/pages/BusinessOppty/CustomerManage/CustomerDetail/components/OppoRecord";
import VisitRecord from "@/pages/BusinessOppty/CustomerManage/CustomerDetail/components/VisitRecord";

const BusinessRecords = ({detail, isEditState, deptList, formatUserInfo, meetingTypeList, visitList, setCustomerTag, userInfo}, ref) => {
    const followRef = useRef(null);
    const cooperateRef = useRef(null);
    // 提交数据处理
    const handleSubmit = async () => {
        const followValues = await followRef.current?.handleSubmit();
        const cooperateValues = await cooperateRef.current?.handleSubmit();
        return {
            ...followValues,
            ...cooperateValues
        };
    };
    /* 暴露方法 */
    useImperativeHandle(ref, () => ({
        handleSubmit,
    }))
    return (
        <div className={'flex flex-direction-column gap-20 padding-20 bg-color-ffffff border-radius-8 line-height-24'}>
            <div className={'flex width-100per justify-between'}>
                <div className={'font-weight-600 font-size-16'}>业务记录</div>
                {/*<Button type={'primary'}>编辑</Button>*/}
            </div>
            <FollowRecord
                ref={followRef}
                detail={detail}
                deptList={deptList}
                formatUserInfo={formatUserInfo}
                isEditState={isEditState}
                userInfo={userInfo}
            />
            {/* 来访/拜访 */}
            {
                detail?.id &&
                <VisitRecord detail={detail} isEditState={isEditState} meetingTypeList={meetingTypeList}
                             visitList={visitList} setCustomerTag={setCustomerTag}/>
            }
            {/* 线索/商机 */}
            {
                detail?.id &&
                <>
                    <div className={'flex justify-between'}>
                        <div className={'font-size-14 font-weight-500 color-165dff'}>线索/商机</div>
                    </div>
                    {/* 线索 */}
                    <ClueRecord detail={detail} isEditState={isEditState} setCustomerTag={setCustomerTag}/>
                    {/* 商机 */}
                    <OppoRecord detail={detail} isEditState={isEditState} setCustomerTag={setCustomerTag}/>
                </>
            }
            {/* 合作记录 */}
            <CooperateRecord
                ref={cooperateRef}
                detail={detail}
                deptList={deptList}
                formatUserInfo={formatUserInfo}
                isEditState={isEditState}
                setCustomerTag={setCustomerTag}
            />
        </div>)
}
export default forwardRef(BusinessRecords);
