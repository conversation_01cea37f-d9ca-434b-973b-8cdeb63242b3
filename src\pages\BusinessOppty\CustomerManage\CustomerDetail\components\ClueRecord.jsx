/**
 * @description ClueRecord.jsx - 文件描述
 * <AUTHOR>
 *
 * Created on 2025/5/22 10:56
 */
import React, { useEffect, useState } from 'react';
import { Badge, Button, Cascader, DatePicker, Form, Input, InputNumber, message, Modal, Select, Space, Table } from 'antd';
import { PlusOutlined } from '@ant-design/icons';
import dayjs from 'dayjs';
import { ClueStatusList } from '@/pages/BusinessOppty/ClueManage/ClueList/const';
import { useRouterLink } from '@/hook/useRouter';
import { pageProjectClue } from '@/api/Opportunity/Clue';

const ClueRecord = ({ detail, isEditState, setCustomerTag }) => {
	const { openNewTab, searchParams } = useRouterLink();
	const [dataSource, setDataSource] = useState([]);
	const columns = [
		{
			title: '序号',
			dataIndex: 'index',
			key: 'index',
			align: 'center',
			width: 80,
			render: (_, record, index) => {
				return index + 1;
			},
		},
		{
			title: '线索名称',
			dataIndex: 'clueName',
			key: 'clueName',
			align: 'center',
			width: 200,
		},
		{
			title: '线索来源',
			dataIndex: 'clueSourceName',
			key: 'clueSourceName',
			align: 'center',
			width: 200,
		},
		{
			title: '线索状态',
			dataIndex: 'clueStatus',
			key: 'clueStatus',
			align: 'center',
			width: 200,
			render: (clueStatus, record) => {
				if (record.cancelStatus === 1) return <Badge status={'default'} text={'已作废'} />;
				const info = ClueStatusList.find((item) => item.value === clueStatus);
				return <Badge status={info.status} text={info.label} />;
			},
		},
		{
			title: '分配人',
			dataIndex: 'clueDispatchName',
			key: 'clueDispatchName',
			align: 'center',
			width: 200,
		},
		{
			title: '接收人',
			dataIndex: 'clueReceiveName',
			key: 'clueReceiveName',
			align: 'center',
			width: 200,
		},
		{
			title: '所属部门',
			dataIndex: 'clueReceiveDeptName',
			key: 'clueReceiveDeptName',
			align: 'center',
			width: 200,
		},
	];
	if (isEditState) {
		columns.push({
			title: '操作',
			dataIndex: 'options',
			key: 'options',
			render: (inviteName, record) => {
				return (
					<Space>
						<Button type="link" size={'small'} onClick={() => handleUpdateRecord(record)}>
							编辑
						</Button>
						{/*<Button type="link" size={'small'} danger onClick={() => handleDeleteRecord(record)}>删除</Button>*/}
					</Space>
				);
			},
			fixed: 'right',
			width: 100,
		});
	} else {
		columns.push({
			title: '操作',
			dataIndex: 'options',
			key: 'options',
			render: (inviteName, record) => {
				return (
					<Space>
						<Button type="link" size={'small'} onClick={() => handleUpdateRecord(record)}>
							查看
						</Button>
					</Space>
				);
			},
			fixed: 'right',
			width: 100,
		});
	}
	useEffect(() => {
		const id = searchParams.get('id');
		if (id) {
			getClueRecordList(id);
		}
	}, []);

	// 获取跟进记录列表
	const getClueRecordList = async (id) => {
		const res = await pageProjectClue({ customerId: id, clueType: 1 });
		if (res.data) {
			console.log('获取跟进记录列表数据: ', res.data.records);
			setDataSource(res.data.records);
			setCustomerTag('clueRecord', res.data.records);
		}
	};

	// 操作跟进记录
	const handleUpdateRecord = (record) => {
		openNewTab(`/businessOppty/clueManage/clueDetail?customerId=${detail.id}&id=${record.id}`);
	};
	// 新增记录
	const handleAddRecord = () => {
		if (detail.status <= 1) {
			return message.warning('客户状态还未认领，无法新增记录！');
		}
		openNewTab(`/businessOppty/clueManage/clueDetail?customerId=${detail.id}&customerTypeId=${detail.typeId}`);
	};
	// 删除记录
	const handleDeleteRecord = (record) => {
		Modal.confirm({
			title: '确定删除该跟进记录吗？',
			okText: '确定',
			cancelText: '取消',
			onOk: () => {
				const newDataSource = dataSource.filter((item) => item.id !== record.id);
				setDataSource(newDataSource);
				setCustomerTag('clueRecord', newDataSource);
			},
		});
	};

	return (
		<>
			<div className={'flex justify-between'}>
				<div className={'font-size-14 font-weight-500'}>线索</div>
				{isEditState && (
					<Button type={'primary'} size={'small'} icon={<PlusOutlined />} onClick={handleAddRecord}>
						添加
					</Button>
				)}
			</div>
			<Table rowKey="rowKey" dataSource={dataSource} pagination={false} columns={columns} size={'small'} />
		</>
	);
};
export default ClueRecord;
