/**
 * @description CooperateRecord.jsx - 文件描述
 * <AUTHOR>
 *
 * Created on 2025/5/22 10:56
 */
import React, {forwardRef, useEffect, useImperativeHandle, useState} from 'react';
import {But<PERSON>, Cascader, DatePicker, Form, Input, InputNumber, Modal, message, Space, Table} from "antd";
import {PlusOutlined} from "@ant-design/icons";
import dayjs from "dayjs";
import {
    addCustomerCooperationRecord,
    deleteCustomerCooperationRecord,
    updateCustomerCooperationRecord
} from "@/api/Opportunity/Customer";

const CooperateRecord = ({detail, deptList, formatUserInfo, isEditState, setCustomerTag}, ref) => {
    const [dataSource, setDataSource] = useState([]);
    const [record, setRecord] = useState({});
    const [visible, setVisible] = useState(false);
    /* 跟进记录表单 */
    const [form] = Form.useForm();

    useEffect(() => {
        if (detail.id) {
            const list = detail.customerCooperationRecords;
            setDataSource(list.sort((a, b) => dayjs(b.contractSigningTime) > dayjs(a.contractSigningTime) ? 1 : -1));
            setCustomerTag('cooperateRecord', detail.customerCooperationRecords)
        }
    }, [detail]);
    const columns = [
        {
            title: '序号',
            dataIndex: 'index',
            key: 'index',
            render: (_, record, index) => {
                return index + 1;
            },
        },
        {
            title: '合作内容',
            dataIndex: 'content',
            key: 'content',
        },
        {
            title: '合作金额（元）',
            dataIndex: 'amount',
            key: 'amount',
        },
        {
            title: '合同签单人',
            dataIndex: 'customerContractees',
            key: 'customerContractees',
            render: (customerContractees = []) => {
                return customerContractees?.map(item => item.userName).join(',');
            },
        },
        {
            title: '交付执行人',
            dataIndex: 'customerExecutors',
            key: 'customerExecutors',
            render: (customerExecutors = []) => {
                return customerExecutors?.map(item => item.userName).join(',');
            },
        },
        {
            title: '合同签订时间',
            dataIndex: 'contractSigningTime',
            key: 'contractSigningTime',
        },
        {
            title: '交付进度',
            dataIndex: 'deliveryProgressContent',
            key: 'deliveryProgressContent',
            // width: 200,
            render: (deliveryProgressContent) => {
                return <div className={'text-ellipsis'}>{deliveryProgressContent}</div>
            }
        },
    ];
    if (isEditState) {
        columns.push({
            title: '操作',
            dataIndex: 'options',
            key: 'options',
            render: (inviteName, record) => {
                return <Space size={'small'}>
                    <Button type="link" onClick={() => handleUpdateRecord(record)}>编辑</Button>
                    <Button type="link" danger onClick={() => handleDeleteRecord(record)}>删除</Button>
                </Space>
            },
            fixed: 'right',
            width: 100,
        })
    }
    // 操作跟进记录
    const handleUpdateRecord = (record) => {
        const values = {
            ...record,
            customerContractees: record.customerContractees?.map((item) => [item?.departmentId, item?.userId]),
            customerExecutors: record.customerExecutors?.map((item) => [item?.departmentId, item?.userId]),
            contractSigningTime: record.contractSigningTime ? dayjs(record.contractSigningTime) : null,
        }
        form.setFieldsValue(values);
        setRecord({
            ...record,
            update: true,
        });
        setVisible(true);
    };
    // 新增记录
    const handleAddRecord = () => {
        if (detail.status <= 1) {
            return message.warning('客户状态还未认领，无法新增记录！');
        }
        form.resetFields();
        setRecord({
            create: true,
            id: Math.random().toString(36).substring(2, 15),
        });
        setVisible(true);
    };
    // 删除记录
    const handleDeleteRecord = (record) => {
        Modal.confirm({
            title: '确定删除该合作记录吗？',
            okText: '确定',
            cancelText: '取消',
            onOk: async () => {
                if (detail?.id && !record.create) {
                    try {
                        const res = await deleteCustomerCooperationRecord({id: record.id,});
                        if (res.data) {
                            message.success('删除合作记录成功');
                        }
                    } catch (e) {
                        message.error('删除合作记录失败');
                        return Promise.reject(e);
                    }
                }
                const newDataSource = dataSource.filter((item) => item.id !== record.id);
                setDataSource(newDataSource);
                setCustomerTag('cooperateRecord', newDataSource);
            },
        });
    };

    // 关闭合作记录弹框
    const closeModal = () => {
        setVisible(false);
        form.resetFields();
        setRecord({});
    };
    // 提交合作记录
    const handleSubmit = async () => {
        const {update, create} = record;
        const values = await form.validateFields();
        if (record?.create && !update) {
            // 新增
            console.log('新增合作记录', values);
            if (detail?.id) {
                try {
                    const res = await addCustomerCooperationRecord({
                        ...values,
                        customerId: detail?.id,
                        customerContractees: values.customerContractees.map((item) => formatUserInfo(item)),
                        customerExecutors: values.customerExecutors.map((item) => formatUserInfo(item)),
                        contractSigningTime: values.contractSigningTime.format('YYYY-MM-DD'),
                    });
                    if (res.data) {
                        message.success('新增合作记录成功');
                        values.id = res.data;
                        delete record.create;
                    }
                } catch (e) {
                    message.error('新增合作记录失败');
                    return Promise.reject(e);
                }
            }
            const newDataSource = [
                ...dataSource,
                {
                    ...record,
                    ...values,
                    customerContractees: values.customerContractees.map((item) => formatUserInfo(item)),
                    customerExecutors: values.customerExecutors.map((item) => formatUserInfo(item)),
                    contractSigningTime: values.contractSigningTime.format('YYYY-MM-DD'),
                }
            ];
            setDataSource(newDataSource);
            setCustomerTag('cooperateRecord', newDataSource);
        } else {
            // 修改
            console.log('修改合作记录', values);
            if (detail?.id && !create) {
                try {
                    const res = await updateCustomerCooperationRecord({
                        ...values,
                        id: record?.id,
                        customerContractees: values.customerContractees.map((item) => formatUserInfo(item)),
                        customerExecutors: values.customerExecutors.map((item) => formatUserInfo(item)),
                        contractSigningTime: values.contractSigningTime.format('YYYY-MM-DD'),
                    });
                    if (res.data) {
                        message.success('修改合作记录成功');
                    }
                } catch (e) {
                    message.error('修改合作记录失败');
                    return Promise.reject(e);
                }
            }
            const newDataSource = dataSource.map(item => {
                if (item.id === record?.id) {
                    return {
                        ...item,
                        ...values,
                        customerContractees: values.customerContractees.map((item) => formatUserInfo(item)),
                        customerExecutors: values.customerExecutors.map((item) => formatUserInfo(item)),
                        contractSigningTime: values.contractSigningTime.format('YYYY-MM-DD'),
                    };
                }
                return item;
            })
            setDataSource(newDataSource);
            setCustomerTag('cooperateRecord', newDataSource);
        }
        closeModal();
    }
    const handleSave = async () => {
        /* 首次创建提交信息 */
        if (!detail?.id) {
            return {
                customerCooperationRecords: dataSource.map(item => {
                    const {id, ...rest} = item;
                    return rest;
                }),
            }
        }
        return {};
    }
    // 暴露给父组件
    useImperativeHandle(ref, () => ({
        handleSubmit: handleSave,
    }));
    return (<>
        <div className={'flex justify-between'}>
            <div className={'font-size-14 font-weight-500 color-165dff'}>合作记录</div>
            {
                isEditState && (
                    <Button
                        type={'primary'}
                        size={'small'}
                        icon={<PlusOutlined/>}
                        onClick={handleAddRecord}
                    >添加</Button>
                )
            }
        </div>
        <Table
            rowKey='rowKey'
            dataSource={dataSource}
            pagination={false}
            columns={columns}
            size={'small'}
        />
        <Modal
            title={record?.create ? '添加合作记录' : '编辑合作记录'}
            open={visible}
            onOk={handleSubmit}
            onCancel={closeModal}
            width={600}
        >
            <Form form={form} layout={'horizontal'} labelCol={{span: 6}} wrapperCol={{span: 14}}>
                <Form.Item
                    label={'合作内容'}
                    name={'content'}
                    required
                    rules={[{required: true, message: '请输入合作内容'}]}
                >
                    <Input.TextArea placeholder={'请输入合作内容'}/>
                </Form.Item>
                <Form.Item
                    label={'合作金额（元）'}
                    name={'amount'}
                    required
                    rules={[{required: true, message: '请输入合作金额（元）'}]}
                >
                    <InputNumber className={'width-100per'} placeholder={'请输入合作金额'}/>
                </Form.Item>
                <Form.Item
                    label={'合作签单人'}
                    name={'customerContractees'}
                    required
                    rules={[{required: true, message: '请选择合作签单人'}]}
                >
                    <Cascader multiple placeholder={'请选择合作签单人'} options={deptList}
                              showCheckedStrategy={Cascader.SHOW_CHILD}/>
                </Form.Item>
                <Form.Item
                    label={'交付执行人'}
                    name={'customerExecutors'}
                    required
                    rules={[{required: true, message: '请选择交付执行人'}]}
                >
                    <Cascader multiple placeholder={'请选择交付执行人'} options={deptList}
                              showCheckedStrategy={Cascader.SHOW_CHILD}/>
                </Form.Item>
                <Form.Item
                    label={'合同签订时间'}
                    name={'contractSigningTime'}
                    required
                    rules={[{required: true, message: '请选择合同签订时间'}]}
                >
                    <DatePicker placeholder={'请选择合同签订时间'} className={'width-100per'}/>
                </Form.Item>
                <Form.Item
                    label={'交付进度'}
                    name={'deliveryProgressContent'}
                    required
                    rules={[{required: true, message: '请输入交付进度'}]}
                >
                    <Input.TextArea placeholder={'请输入交付进度'} rows={3} autoSize={{minRows: 3}} />
                </Form.Item>
            </Form>
        </Modal>
    </>)
}
export default forwardRef(CooperateRecord);
