/**
 * @description CustomBase - 文件描述
 * <AUTHOR>
 *
 * Created on 2025/5/19 17:34
 */
import React, {forwardRef, useEffect, useImperativeHandle, useState} from 'react';
import {
    Badge,
    Button,
    Cascader,
    Descriptions,
    Form,
    Input,
    InputNumber,
    message,
    Modal,
    Select,
    Space,
    Table,
    Tag
} from "antd";
import {PlusOutlined, FileSearchOutlined} from "@ant-design/icons";
import {
    addCustomerContact,
    deleteCustomerContact,
    getEnterpriseInfo,
    getEventByCompanyName, pageCustomer,
    updateCustomerContact
} from "@/api/Opportunity/Customer";

import { useRouterLink } from '@/hook/useRouter/index';
import {CustomerStatus, StatusColor} from "@/pages/BusinessOppty/CustomerManage/CustomerList/const";
const StatusName = CustomerStatus.map((item) => item.label);

const baseDesc = [
    {
        label: '客户名称',
        key: 'name',
        id: '',
    },
    {
        label: '社会统一信用代码',
        key: 'uscc',
    },
    {
        label: '客户类型',
        key: 'typeId',
    },
    {
        label: '客户状态',
        key: 'status',
    },
    {
        label: '所属区域',
        key: 'region',
    },
    {
        label: '联系地址',
        key: 'address',
    },
    {
        label: '客户资质',
        key: 'qualification',
    },
    {
        label: '行业领域',
        key: 'industrySector',
    },
    {
        label: '主营业务',
        key: 'mainBusiness',
    },
];
const labelStyle = {
    width: 130,
    justifyContent: 'flex-end',
}
const CustomBase = ({
                        detail,
                        isEditState,
                        customerType,
                        areaData,
                        customerTagRef,
                        setCustomerTag,
                        meetingTypeList,
                        ownerPermission,
                        aptitudeList,
                    }, ref) => {

	const { searchParams } = useRouterLink();
    const [baseInfo, setBaseInfo] = useState({});
    const [isShowAddContact, setIsShowAddContact] = useState(false);
    /* 联系人信息 */
    const [concatInfo, setConcatInfo] = useState({});
    /* 公司联系人 */
    const [contactList, setContactList] = useState([]);
    /* 产业大脑客户信息 */
    const [enterprise, setEnterprise] = useState(null);
    const [showAllActivities, setShowAllActivities] = useState(false);
    /* 参会标签 */
    const [tagList, setTagList] = useState({
        meetingTags: [],
        activityTags: [],
        clueTags: [],
        cooperateTags: [],
    });

    const [form] = Form.useForm();
    /* 监听输入的统一信用代码 */
    const watchUSCC = Form.useWatch('uscc', form);
    /* 监听输入的客户名称 */
    const watchName = Form.useWatch('name', form);
    /* 联系人表单 */
    const [contactForm] = Form.useForm();

    /* 查询产业大脑企业信息 */
    const getEnterprise = async (creditCode, isGetEnterprise = false) => {
        const res = await getEnterpriseInfo({creditCode});
        console.log('res', res.data);
        setEnterprise(res.data || {});
        const enterpriseInfo = {};
        Object.keys(res.data || {}).forEach(key => {
            if (res.data[key]) {
                switch (key) {
                    case 'name':
                        enterpriseInfo.name = res.data[key];
                        break;
                    case 'provinceCode':
                        enterpriseInfo.region = [res.data.provinceCode, res.data.cityCode];
                        break;
                    case 'registerAddress':
                        enterpriseInfo.address = res.data[key];
                        break;
                    case 'coreBusiness':
                        enterpriseInfo.mainBusiness = res.data[key];
                        break;
                    case 'honorNameList':
                        enterpriseInfo.qualification = res.data[key];
                        break;
                    case 'industryList':
                        enterpriseInfo.industrySector = res.data[key];
                        break;
                    default:
                        enterpriseInfo[key] = res.data[key];
                        break;
                }
            }
        });
        console.log('查询产业大脑企业信息：', enterpriseInfo)
        if (isGetEnterprise && res?.data?.id) {
            setEnterprise(res.data);
        } else {
            form.setFieldsValue({
                ...enterpriseInfo,
            });
        }
        return res.data;
    };

    /* 查询客户列表是否已存在 */
    const checkCustomer = async ({uscc, name}) => {
        const res = await pageCustomer({uscc, name});
        if (res.data?.records?.length > 0) {
            Modal.confirm({
                title: '提示',
                content: '该客户已存在，请重新输入！',
                okText: '确定',
                cancelText: '取消',
                onOk: () => {
                    form.resetFields();
                },
            });
        }
    };
    const renderArea = ({regionProvinceCode, regionCityCode}) => {
        const area = [];
        if (regionProvinceCode) {
            const province = areaData.find(item => item.value === regionProvinceCode);
            area.push(province?.label);
            if (regionCityCode && province) {
                const city = province.children.find(item => item.value === regionCityCode);
                area.push(city?.label);
            }
        }
        return area.join('-');
    };
    useEffect(() => {
        if (!detail?.id) return;
        // 提前计算区域和客户类型信息
        const region = renderArea(detail);
        const customerTypeInfo = customerType.find(type => type.value === detail.typeId)?.label || '-';
        const initBaseInfo = {};
        baseDesc.forEach(item => {
            if (item.key === 'region') {
                initBaseInfo[item.key] = region;
            } else if (item.key === 'typeId') {
                initBaseInfo[item.key] = customerTypeInfo;
            } else {
                initBaseInfo[item.key] = detail[item.key];
            }
        })
        setBaseInfo(initBaseInfo);
    }, [detail, areaData, customerType]);
    useEffect(() => {
        if (detail?.id && form) {
            const {
                name,
                uscc,
                typeId,
                regionCityCode,
                regionProvinceCode,
                address,
                qualification,
                industrySector,
                mainBusiness
            } = detail;
            if (isEditState) {
                form.setFieldsValue({
                    name, uscc, typeId, address, industrySector, mainBusiness,
                    region: [regionProvinceCode, regionCityCode],
                    qualification: qualification ? qualification?.split('；') : [],
                    // qualification: qualification,
                });
            }
            if (detail?.customerContacts?.length) {
                setContactList(detail.customerContacts);
            }
            if (uscc && uscc.length === 18) {
                getEnterprise(uscc, true);
            }
            getCustomerActivity(name);
        }
    }, [detail?.id, form, isEditState]);
    /* 监听uscc */
    useEffect(() => {
        if (watchUSCC && watchUSCC.length === 18 && !searchParams.get('id')) {
            // 校验统一信用代码
            const res = validateUSCC(watchUSCC)
            if (res) {
                if (watchUSCC === detail.uscc) {
                    console.log('统一信用代码与当前客户一致，无需查询产业大脑信息');
                    return;
                }
                /* 校验通过查询产业大脑信息 */
                console.log('校验通过查询产业大脑信息');
                getEnterprise(watchUSCC);
                checkCustomer({uscc: watchUSCC});
            } else {
                console.log('校验不通过，请重新输入统一信用代码')
            }
        }
    }, [watchUSCC]);
    /* 监听客户名称 */
    // useEffect(() => {
    //     if (watchName && !enterprise?.id && !searchParams.get('id')) {
    //         console.log('客户名称：', watchName);
    //         checkCustomer({name: watchName});
    //     }
    // }, [watchName]);
    useEffect(() => {
        const tags = {};
        if (customerTagRef.current?.visitRecord) {
            console.log('visitRecord', customerTagRef.current.visitRecord);
            // 参会标签
            tags.meetingTags = []; //  会议标签 格式包含会议类型名称/ID 和会议类型次数
            customerTagRef.current.visitRecord.forEach(item => {
                const meeting = item.meetings?.[0] || {};
                const {meetingTypeId} = meeting;
                if (meetingTypeId) {
                    const meetingTypeName = meetingTypeList?.find(type => type.value === meetingTypeId)?.label || '';
                    const index = tags.meetingTags.findIndex(tag => tag.value === meetingTypeId);
                    if (index > -1) {
                        tags.meetingTags[index].count++;
                    } else {
                        tags.meetingTags.push({
                            label: meetingTypeName,
                            value: meetingTypeId,
                            count: 1,
                        });
                    }
                }
            })
        }
        if (customerTagRef.current?.cooperateRecord) {
            console.log('cooperateRecord', customerTagRef.current.cooperateRecord)
            // 统计合作金额
            const amount = customerTagRef.current.cooperateRecord?.reduce((total, item) => {
                return total + item.amount;
            }, 0);
            tags.cooperateTags = [
                {label: '签订合同', count: customerTagRef.current.cooperateRecord?.length || 0},
                {label: `合作金额：${amount}元`, count: 0},
            ];
        }
        if (customerTagRef.current?.activityTags) {
            tags.activityTags = [{label: '参加活动', count: customerTagRef.current?.activityTags.length, meetings: []}]; // 活动标签 格式包含会议数量和活动名称
            tags.activityTags[0].meetings = customerTagRef.current?.activityTags.map(item => item.title);
        }
        //     合作记录标签
        tags.clueTags = [
            {label: '关联线索', count: 0},
            {label: '关联商机', count: 0},
        ];
        if (customerTagRef.current?.oppoRecord) {
            console.log('oppoRecord', customerTagRef.current.oppoRecord);
            tags.clueTags[1].count = customerTagRef.current?.oppoRecord?.length || 0;
        }
        if (customerTagRef.current?.clueRecord) {
            tags.clueTags[0].count = customerTagRef.current?.clueRecord?.length || 0;
            console.log('clueRecord', customerTagRef.current.clueRecord);
        }
        console.log('标签列表', tags);
        setTagList(tags);
    }, [customerTagRef.current, meetingTypeList]);
    /* 通过名称查询客户参与活动 */
    const getCustomerActivity = async (name) => {
        const res = await getEventByCompanyName({companyName: name});
        if (res.data) {
            // const repeatedArray = Array(8).fill(res.data).flat();
            console.log('客户活动', res.data);
            setCustomerTag('activityTags', res.data);
        }
    };
    /* 校验统一信用代码是否合格 */
    const validateUSCC = (code) => {
        // 格式校验
        if (typeof code !== 'string' || code.length !== 18) return false;
        if (!/^[1-9A-HJ-NPQRTUWXY]{2}\d{6}[0-9A-HJ-NPQRTUWXY]{10}$/.test(code)) return false;
        // 校验码计算
        const charMap = "0123456789ABCDEFGHJKLMNPQRTUWXY";
        const weights = [1, 3, 9, 27, 19, 26, 16, 17, 20, 29, 25, 13, 8, 24, 10, 30, 28];
        let sum = 0;
        for (let i = 0; i < 17; i++) {
            sum += charMap.indexOf(code[i]) * weights[i];
        }
        const checkCode = charMap[(31 - (sum % 31)) % 31];
        return code[17] === checkCode;
    };

    const contactColumns = [
        {
            title: '序号',
            dataIndex: 'index',
            key: 'index',
            render: (_, record, index) => index + 1,
            width: 60,
        },
        {
            title: '姓名',
            dataIndex: 'name',
            key: 'name',
        },
        {
            title: '职位',
            dataIndex: 'position',
            key: 'position',
        },
        {
            title: '联系方式',
            dataIndex: 'phone',
            key: 'phone',
        },
        {
            title: '主要负责内容',
            dataIndex: 'workContent',
            key: 'workContent',
        },
    ];
    if (isEditState) {
        contactColumns.push({
            title: '操作',
            dataIndex: 'action',
            key: 'action',
            width: 100,
            render: (_, record) => (
                <Space size="middle">
                    <Button type={'link'} onClick={() => handleEditContact(record)}>编辑</Button>
                    <Button type={'link'} danger onClick={() => handleDelContact(record)}>删除</Button>
                </Space>
            ),
        })
    }
    /* 添加联系人 */
    const handleAddContact = () => {
        setIsShowAddContact(true);
        setConcatInfo({
            create: true,
        });
    };
    /* 编辑联系人 */
    const handleEditContact = (record) => {
        setIsShowAddContact(true);
        setConcatInfo({
            ...record,
            update: true,
        });
        contactForm.setFieldsValue(record);
    }
    /* 删除联系人 */
    const handleDelContact = (record) => {
        Modal.confirm({
            title: '确认删除?',
            content: `确定要删除联系人【${record.name}】吗?`,
            onOk: async () => {
                if (detail?.id && !record.create) {
                    // 已创建用户并且非创建的用户
                    try {
                        const res = await deleteCustomerContact({id: record.id});
                        if (res.data) {
                            message.success('删除联系人成功');
                        }
                    } catch (e) {
                        message.error('删除联系人失败');
                        return Promise.reject();
                    }
                }
                setContactList(contactList.filter(item => item.id !== record.id));
                return Promise.resolve();
            },
        });
    }
    /* 保存联系人 */
    const handleSaveContact = async () => {
        const {update, create} = concatInfo;
        const values = contactForm.getFieldsValue();
        if (create && !update) {
            //     创建联系人
            if (detail?.id) {
                try {
                    const res = await addCustomerContact({
                        customerId: detail.id,
                        ...values
                    });
                    if (res.data) {
                        message.success('创建联系人成功');
                        values.id = res.data;
                        delete concatInfo?.create;
                    }
                } catch (e) {
                    message.error('创建联系人失败');
                    return Promise.reject();
                }
            }
            setContactList([...contactList, {...values, create: concatInfo?.create,}]);
            contactForm.resetFields();
            setIsShowAddContact(false);
        } else {
            if (detail?.id && !create) {
                try {
                    const res = await updateCustomerContact({
                        id: concatInfo?.id,
                        customerId: detail.id,
                        ...values
                    });
                    if (res.data) {
                        message.success('更新联系人成功');
                    }
                } catch (e) {
                    message.error('更新联系人失败');
                    return Promise.reject();
                }
            }
            //     更新联系人
            setContactList(contactList.map(item => {
                if (item.id === concatInfo?.id) {
                    return {
                        ...item,
                        ...values,
                    }
                }
                return item;
            }));
            setIsShowAddContact(false);
            contactForm.resetFields();
        }
    };
    /* 退出编辑联系人 */
    const handleCancelContact = () => {
        setIsShowAddContact(false);
        contactForm.resetFields();
        setConcatInfo({});
    };
    /* 表单提交 */
    const handleSubmit = async () => {
        try {
            const values = await form.validateFields();
            console.log(values);
            if (values.qualification?.length > 0) {
                values.qualification = values.qualification.join('；');
            } else {
                values.qualification = '';
            }
            /* 首次创建提交信息 */
            if (!detail?.id) {
                values.customerContacts = contactList.map(item => {
                    const {id, ...rest} = item;
                    return rest;
                });
            }
            return values;
        } catch (e) {
            console.log(e);
            message.warning(e.errorFields[0].errors[0]);
            throw new Error('表单验证失败');
        }
    };
    /* 跳转到产业大脑 */
    const handleGoToIndustryBrain = () => {
        // enterprise
        if (enterprise?.id) {
            const host = window.location.host;
            if (host.includes('test') || host.includes('localhost')) {
                window.open(`https://test-smp.dwq360.com/gbac-ida-webfrontend/enterprise/emphasize/emphasizeDetail?id=${enterprise.id}`, '_blank');
            } else {
                window.open(`https://smp.dwq360.com/gbac-ida-webfrontend/enterprise/emphasize/emphasizeDetail?id=${enterprise.id}`, '_blank');
            }
        }
    };
    /* 暴露方法 */
    useImperativeHandle(ref, () => ({
        handleSubmit,
    }));
    return (
        <div className={'flex flex-direction-column gap-20 padding-20 bg-color-ffffff border-radius-8 line-height-24'}>
            <div className={'flex width-100per justify-between'}>
                <div className={'font-weight-600 font-size-16'}>客户基本信息</div>
            </div>
            <div className={'font-size-14 font-weight-500 color-165dff '}>基本信息</div>
            {
                isEditState ?
                    <Form form={form} layout={'horizontal'} className={'width-468'} labelCol={{flex: '130px'}}>
                        <Form.Item label={'客户名称'} name={'name'} required
                                   rules={[{required: true, message: '请输入客户名称'}]}>
                            <Input placeholder={'请输入客户名称'}/>
                        </Form.Item>
                        <Form.Item label={'统一社会信用代码'} name={'uscc'} required
                                   rules={[{required: true, message: '请输入统一社会信用代码'}]}>
                            <Input placeholder={'请输入统一社会信用代码'}/>
                        </Form.Item>
                        <Form.Item label={'客户类型'} name={'typeId'} required
                                   rules={[{required: true, message: '请选择客户类型'}]}>
                            <Select placeholder={'请选择客户类型'} options={customerType}/>
                        </Form.Item>
                        <Form.Item label={'所属区域'} name={'region'}>
                            <Cascader placeholder={'请选择所属区域'} options={areaData}/>
                        </Form.Item>
                        <Form.Item label={'联系地址'} name={'address'}>
                            <Input placeholder={'请输入联系地址'}/>
                        </Form.Item>
                        <Form.Item label={'客户资质'} name={'qualification'} tooltip={'如需新增更多资质，请联系系统管理员'}>
                            {/*<Input placeholder={'请选择客户资质'}/>*/}
                            <Select mode={'tags'} tokenSeparators={['；']} options={aptitudeList} placeholder={'请选择客户资质'}/>
                        </Form.Item>
                        <Form.Item label={'行业领域'} name={'industrySector'}>
                            {/*<Select mode={'tags'} tokenSeparators={['；']} placeholder={'请选择行业领域'}/>*/}
                            <Input placeholder={'请输入行业领域'}/>
                        </Form.Item>
                        <Form.Item label={'主营业务'} name={'mainBusiness'}>
                            <Input placeholder={'请输入主营业务'}/>
                        </Form.Item>
                    </Form> :
                    <Descriptions className={'margin-bottom-20'} column={1} labelStyle={labelStyle}>
                        {/*{*/}
                        {/*    baseInfo.map(item => {*/}
                        {/*        return <Descriptions.Item*/}
                        {/*            label={item.label}*/}
                        {/*            key={item.key}>*/}
                        {/*            {item.children || '-'}*/}
                        {/*        </Descriptions.Item>*/}
                        {/*    })*/}
                        {/*}*/}
                        <Descriptions.Item label={'客户名称'}>
                            {baseInfo?.name || '-'}
                            {
                                enterprise?.id &&
                                <Button
                                    title={'产业大脑查看详情'}
                                    size={'small'}
                                    type={'link'}
                                    icon={<FileSearchOutlined/>}
                                    onClick={handleGoToIndustryBrain}
                                />
                            }
                        </Descriptions.Item>
                        <Descriptions.Item label={'社会统一信用代码'}>
                            {baseInfo?.uscc || '-'}
                        </Descriptions.Item>
                        <Descriptions.Item label={'客户状态'}>
                            <Badge color={StatusColor[baseInfo.status]} text={StatusName[baseInfo.status]} />
                        </Descriptions.Item>
                        <Descriptions.Item label={'客户类型'}>
                            {baseInfo?.typeId || '-'}
                        </Descriptions.Item>
                        <Descriptions.Item label={'所属区域'}>
                            {baseInfo?.region || '-'}
                        </Descriptions.Item>
                        <Descriptions.Item label={'联系地址'}>
                            {baseInfo?.address || '-'}
                        </Descriptions.Item>
                        <Descriptions.Item label={'客户资质'} contentStyle={{display: 'flex', flexWrap: 'wrap', gap: '5px'}}>
                            {(baseInfo?.qualification ? baseInfo?.qualification.split('；')?.map((item, index) => <Tag
                                key={index} color={'blue'}>{item}</Tag>) : '-') || '-'}
                        </Descriptions.Item>
                        <Descriptions.Item label={'行业领域'}>
                            {baseInfo?.industrySector || '-'}
                        </Descriptions.Item>
                        <Descriptions.Item label={'主营业务'}>
                            {baseInfo?.mainBusiness || '-'}
                        </Descriptions.Item>
                    </Descriptions>
            }
            {
                !isEditState &&
                <>
                    <div className={'flex justify-between align-center'}>
                        <div className={'font-size-14 font-weight-500 color-165dff'}>客户标签</div>
                    </div>
                    <Descriptions column={1} labelStyle={labelStyle}>
                        <Descriptions.Item label={'参会标签'}>
                            <Space size={20}>
                                {
                                    tagList.meetingTags?.map(item => {
                                        return <Badge color={'blue'} count={item.count} size={'small'}>
                                            <Tag color={'blue'}>{item.label}</Tag>
                                        </Badge>
                                    })
                                }
                            </Space>
                            {
                                tagList.meetingTags?.length === 0 && <span className={'color-86909c'}>暂无</span>
                            }
                        </Descriptions.Item>
                        <Descriptions.Item label={'活动标签'}>
                            <Space direction={'vertical'}>
                                {
                                    tagList.activityTags?.[0]?.count > 0 ?
                                        tagList.activityTags?.map(item => {
                                            return <Badge color={'blue'} count={item.count} size={'small'}>
                                                <Tag color={'blue'}>{item.label}</Tag>
                                            </Badge>
                                        }) :
                                        <span className={'color-86909c'}>暂无</span>
                                }
                                {/* 如果活动超过三个，显示“展开更多”按钮 */}
                                <div className={'flex justify-between align-end'}>
                                    <Space direction={'vertical'}>
                                        {/* 显示最多三个活动 */}
                                        {
                                            tagList.activityTags?.[0]?.meetings
                                                .slice(0, showAllActivities ? tagList.activityTags?.[0]?.meetings.length : 3)
                                                .map((title, i) => (<div key={i}>{title}</div>))
                                        }
                                    </Space>
                                    {
                                        tagList.activityTags?.[0]?.meetings.length > 3 && (
                                            <Button size={'small'} type="link"
                                                    onClick={() => setShowAllActivities(!showAllActivities)}>
                                                {showAllActivities ? '收起' : '展开更多'}
                                            </Button>
                                        )
                                    }
                                </div>
                            </Space>
                        </Descriptions.Item>
                        <Descriptions.Item label={'线索/商机'}>
                            <Space size={20}>
                                {
                                    tagList.clueTags?.map(item => {
                                        if (item.count === 0) {
                                            return null
                                        }
                                        return <Badge color={'blue'} count={item.count} size={'small'}>
                                            <Tag color={'blue'}>{item.label}</Tag>
                                        </Badge>
                                    })
                                }
                                {
                                    tagList.clueTags?.reduce((pre, cur) => {
                                        return pre + cur.count
                                    }, 0) === 0 && <span className={'color-86909c'}>暂无</span>
                                }
                            </Space>
                        </Descriptions.Item>
                        <Descriptions.Item label={'合同标签'}>
                            <Space size={20}>
                                {
                                    tagList.cooperateTags?.map((item, i) => {
                                        if (i === 0 && item.count === 0) {
                                            return null
                                        }
                                        return <Badge color={'blue'} count={item.count} size={'small'}>
                                            <Tag color={'blue'}>{item.label}</Tag>
                                        </Badge>
                                    })
                                }
                            </Space>
                        </Descriptions.Item>
                    </Descriptions>
                </>
            }
            {
                (ownerPermission || !detail?.id) && (<>
                    <div className={'flex justify-between align-center'}>
                        <div className={'font-size-14 font-weight-500 color-165dff'}>联系信息</div>
                        {
                            isEditState &&
                            <Button type={'primary'} size={'small'} icon={<PlusOutlined/>}
                                    onClick={handleAddContact}>添加</Button>
                        }
                    </div>
                    <Table
                        rowKey='rowKey'
                        dataSource={contactList}
                        pagination={false}
                        columns={contactColumns}
                        size={'small'}
                    />
                </>)
            }
            <Modal
                title={concatInfo?.create ? '添加联系人' : '编辑联系人'}
                open={isShowAddContact}
                onCancel={handleCancelContact}
                onOk={handleSaveContact}
                width={600}
            >
                <Form form={contactForm} layout={'horizontal'} labelCol={{span: 6}} wrapperCol={{span: 14}}>
                    <Form.Item
                        label={'联系人姓名'}
                        name={'name'}
                        required
                        rules={[{required: true, message: '请输入联系人姓名'}]}
                    >
                        <Input placeholder={'请输入联系人姓名'}/>
                    </Form.Item>
                    <Form.Item
                        label={'联系人职位'}
                        name={'position'}
                        required
                        rules={[{required: true, message: '请输入联系人职务'}]}
                    >
                        <Input placeholder={'请输入联系人职位'}/>
                    </Form.Item>
                    <Form.Item
                        label={'联系人联系方式'}
                        name={'phone'}
                        rules={[{
                            pattern: /^1[3-9]\d{9}$/,
                            message: '请输入正确的手机号'
                        }]}
                    >
                        <Input placeholder={'请输入联系人联系方式'}/>
                    </Form.Item>
                    <Form.Item
                        label={'主要负责内容'}
                        name={'workContent'}
                    >
                        <Input.TextArea placeholder={'请输入主要负责内容'}/>
                    </Form.Item>
                </Form>
            </Modal>
        </div>)
}
export default forwardRef(CustomBase);
