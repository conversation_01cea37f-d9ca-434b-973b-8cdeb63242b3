/**
 * @description FollowRecord - 文件描述
 * <AUTHOR>
 *
 * Created on 2025/5/22 10:56
 */
import React, {forwardRef, useEffect, useImperativeHandle, useState} from 'react';
import {Button, Cascader, DatePicker, Form, Input, InputNumber, message, Modal, Select, Space, Table} from "antd";
import {PlusOutlined} from "@ant-design/icons";
import dayjs from "dayjs";
import {
    addCustomerFollowupRecord,
    deleteCustomerFollowupRecord,
    updateCustomerFollowupRecord
} from "@/api/Opportunity/Customer";

const FollowRecord = ({detail ,deptList, formatUserInfo, isEditState, userInfo}, ref) => {
    const [dataSource, setDataSource] = useState([]);
    const [record, setRecord] = useState({});
    const [visible, setVisible] = useState(false);
    /* 跟进记录表单 */
    const [form] = Form.useForm();
    const columns = [
        {
            title: '序号',
            dataIndex: 'index',
            key: 'index',
            render: (_, record, index) => {
                return index + 1;
            },
        },
        {
            title: '跟进时间',
            dataIndex: 'followupTime',
            key: 'followupTime',
        },
        {
            title: '跟进人',
            dataIndex: 'customerFollowupPersons',
            key: 'customerFollowupPersons',
            render: (customerFollowupPersons) => {
                return uniqueArray(customerFollowupPersons?.map((item) => item.userName)).join('；');
            },
        },
        {
            title: '客户联系人',
            dataIndex: 'customerFollowupContacts',
            key: 'customerFollowupContacts',
            render: (customerFollowupContacts = []) => {
                return uniqueArray(customerFollowupContacts?.map((item) => item?.contactName)).join('；');
            },
        },
        {
            title: '跟进内容',
            dataIndex: 'content',
            key: 'content',
        },
    ];
     if (isEditState) {
         columns.push({
             title: '操作',
             dataIndex: 'options',
             key: 'options',
             render: (inviteName, record) => {
                 return <Space>
                     <Button type="link" size={'small'} onClick={() => handleUpdateRecord(record)}>编辑</Button>
                     <Button type="link" size={'small'} danger onClick={() => handleDeleteRecord(record)}>删除</Button>
                 </Space>
             },
             fixed: 'right',
             width: 100,
         });
     }
     useEffect(() => {
         if (detail?.customerFollowupRecords) {
             const list = detail.customerFollowupRecords;
             setDataSource(list.sort((a, b) => dayjs(b.followupTime) > dayjs(a.followupTime) ? 1 : -1));
         }
     }, [detail]);
    // 数组去重
    const uniqueArray = (arr) => {
        return [...new Set(arr)];
    };
    // 操作跟进记录
    const handleUpdateRecord = (record) => {
        const values = {
            ...record,
            customerFollowupPersons: record.customerFollowupPersons?.map((item) => [item?.departmentId, item?.userId]),
            customerFollowupContacts: record.customerFollowupContacts?.map((item) => item?.contactId),
            followupTime: record.followupTime ? dayjs(record.followupTime) : null,
        }
        form.setFieldsValue(values);
        setRecord({
            ...record,
            update: true,
        });
        setVisible(true);
    };
    // 新增记录
    const handleAddRecord = () => {
        if (detail.status <= 1) {
            return message.warning('客户状态还未认领，无法新增记录！');
        }
        form.resetFields();
        setRecord({
            create: true,
            id: Math.random().toString(36).substring(2, 15),
        });
        setVisible(true);
    };
    // 删除记录
    const handleDeleteRecord = (record) => {
        Modal.confirm({
            title: '确定删除该跟进记录吗？',
            okText: '确定',
            cancelText: '取消',
            onOk: async () => {
                if (detail.id && !record.create) {
                    try {
                        const res = await deleteCustomerFollowupRecord({
                            id: record.id,
                        });
                        if (res.data) {
                            message.success('删除跟进记录成功');
                        }
                    } catch (e) {
                        message.error('删除跟进记录失败');
                    }
                }
                const newDataSource = dataSource.filter((item) => item.id !== record.id);
                setDataSource(newDataSource);
            },
        });
    };

    // 关闭跟进记录弹框
    const closeModal = () => {
        setVisible(false);
        form.resetFields();
        setRecord({});
    };
    // 提交跟进记录
    const handleSubmit = async () => {
        const {update, create} = record;
        const values = await form.validateFields();
        const customerContacts = detail.customerContacts;
        if (record?.create && !update) {
            // 新增
            console.log('新增跟进记录', values);
            if (detail?.id) {
                try {
                    const res = await addCustomerFollowupRecord({
                        customerId: detail.id,
                        ...values,
                        customerFollowupPersons: values.customerFollowupPersons?.map((item) => formatUserInfo(item)),
                        customerFollowupContacts: values.customerFollowupContacts?.map((id) => ({
                            contactId: id,
                            contactName: customerContacts.find(item => item?.id === id)?.name || '',
                        })),
                        followupTime: values.followupTime.format('YYYY-MM-DD'),
                    });
                    if (res.data) {
                        message.success('新增跟进记录成功');
                        values.id = res.data;
                        delete record.create;
                    }
                } catch (e) {
                    message.error('新增跟进记录失败');
                    throw new Error(e);
                }
            }
            setDataSource([
                ...dataSource,
                {
                    ...record,
                    ...values,
                    customerFollowupPersons: values.customerFollowupPersons?.map((item) => formatUserInfo(item)),
                    customerFollowupContacts: values.customerFollowupContacts?.map((id) => ({
                        followupRecordId: values.id,
                        contactId: id,
                        contactName: customerContacts.find(item => item?.id === id)?.name || '',
                    })),
                    followupTime: values.followupTime.format('YYYY-MM-DD'),
                }
            ]);
        } else {
            // 修改
            console.log('修改跟进记录', values);
            if (detail?.id) {
                try {
                    const res = await updateCustomerFollowupRecord({
                        id: record?.id,
                        ...values,
                        customerFollowupPersons: values.customerFollowupPersons.map((item) => formatUserInfo(item)),
                        customerFollowupContacts: values.customerFollowupContacts?.map((id) => ({
                            followupRecordId: record?.id,
                            contactId: id,
                            contactName: customerContacts.find(item => item?.id === id)?.name || '',
                        })),
                        followupTime: values.followupTime.format('YYYY-MM-DD'),
                    });
                    if (res.data) {
                        message.success('修改跟进记录成功');
                    }
                } catch (e) {
                    message.error('修改跟进记录失败');
                    throw new Error(e);
                }
            }
            setDataSource(dataSource.map(item => {
                if (item.id === record?.id) {
                    return {
                        ...item,
                        ...values,
                        customerFollowupPersons: values.customerFollowupPersons?.map((item) => formatUserInfo(item)),
                        customerFollowupContacts: values.customerFollowupContacts?.map((id) => ({
                            followupRecordId: record?.id,
                            contactId: id,
                            contactName: customerContacts.find(item => item?.id === id)?.name || '',
                        })),
                        followupTime: values.followupTime.format('YYYY-MM-DD'),
                    };
                }
                return item;
            }));
        }
        closeModal();
    }
    const handleSave = async () => {
        /* 首次创建提交信息 */
        if (!detail?.id) {
            return {
                customerFollowupRecords: dataSource.map(item => {
                    const {id, ...rest} = item;
                    return {
                        ...rest,
                    };
                }),
            }
        }
        return {};
    }
    // 暴露给父组件
    useImperativeHandle(ref, () => ({
        handleSubmit: handleSave,
    }));
    const currentUser = [[userInfo?.deptList?.[0]?.id, userInfo?.id]];
    return (<>
        <div className={'flex justify-between'}>
            <div className={'font-size-14 font-weight-500 color-165dff'}>跟进记录</div>
            {
                isEditState && (
                    <Button
                        type={'primary'}
                        size={'small'}
                        icon={<PlusOutlined/>}
                        onClick={handleAddRecord}
                    >添加</Button>
                )
            }
        </div>
        <Table
            rowKey='rowKey'
            dataSource={dataSource}
            pagination={false}
            columns={columns}
            size={'small'}
        />
        <Modal
            title={record?.create ? '添加跟进记录' : '编辑跟进记录'}
            open={visible}
            onOk={handleSubmit}
            onCancel={closeModal}
            width={600}
        >
            <Form form={form} initialValues={{customerFollowupPersons: currentUser}} layout={'horizontal'} labelCol={{span: 6}} wrapperCol={{span: 14}}>
                <Form.Item
                    label={'跟进时间'}
                    name={'followupTime'}
                    required
                    rules={[{required: true, message: '请选择跟进时间'}]}
                >
                    <DatePicker placeholder={'请选择跟进时间'} className={'width-100per'}/>
                </Form.Item>
                <Form.Item
                    label={'跟进人'}
                    name={'customerFollowupPersons'}
                    required
                    rules={[{required: true, message: '请选择跟进人'}]}
                >
                    <Cascader
                        multiple
                        placeholder={'请选择跟进人'}
                        options={deptList}
                        showCheckedStrategy={Cascader.SHOW_CHILD}
                    />
                </Form.Item>
                <Form.Item
                    label={'客户联系人'}
                    name={'customerFollowupContacts'}
                    tooltip={'请先添加客户联系人'}
                    // required
                    // rules={[{required: true, message: '请选择客户联系人'}]}
                >
                    <Select
                        mode={'multiple'}
                        placeholder={'请选择客户联系人'}
                        options={detail.customerContacts?.map(item => ({label: item.name, value: item.id}))}
                    />
                </Form.Item>
                <Form.Item
                    label={'跟进情况'}
                    name={'content'}
                    required
                    rules={[{required: true, message: '请输入跟进情况'}]}
                >
                    <Input.TextArea placeholder={'请输入跟进情况'} rows={3} autoSize={{minRows: 3}}/>
                </Form.Item>
            </Form>
        </Modal>
    </>)
}
export default forwardRef(FollowRecord);
