/**
 * @description OppoRecord.jsx - 文件描述
 * <AUTHOR>
 *
 * Created on 2025/5/22 10:56
 */
import React, { useEffect, useState } from 'react';
import { Badge, Button, Cascader, DatePicker, Form, Input, InputNumber, message, Modal, Select, Space, Table } from 'antd';
import { PlusOutlined } from '@ant-design/icons';
import dayjs from 'dayjs';
import { ClueStatusList } from '@/pages/BusinessOppty/ClueManage/ClueList/const';
import { useRouterLink } from '@/hook/useRouter';
import { pageOpportunity } from '@/api/Opportunity/OpportunityManage';

const OppoRecord = ({ detail, isEditState, setCustomerTag }) => {
	const { openNewTab, searchParams } = useRouterLink();
	const [dataSource, setDataSource] = useState([]);
	const columns = [
		{
			title: '序号',
			dataIndex: 'index',
			key: 'index',
			align: 'center',
			width: 80,
			render: (_, record, index) => {
				return index + 1;
			},
		},
		{
			title: '商机名称',
			dataIndex: 'projectOpportunityName',
			key: 'projectOpportunityName',
			align: 'center',
			width: 200,
		},
		{
			title: '商机自评靠谱度',
			dataIndex: 'projectReliabilityName',
			key: 'projectReliabilityName',
			align: 'center',
			width: 200,
		},
		{
			title: '预期合作产品',
			dataIndex: 'expectCooperateProduct',
			key: 'expectCooperateProduct',
			align: 'center',
			width: 200,
		},
		{
			title: '产品分类',
			dataIndex: 'productTypeName',
			key: 'productTypeName',
			align: 'center',
			width: 200,
		},
		{
			title: '预计收费（万元）',
			dataIndex: 'expectCharge',
			key: 'expectCharge',
			align: 'center',
			width: 200,
		},
		{
			title: '商机进度',
			dataIndex: 'projectStageName',
			key: 'projectStageName',
			align: 'center',
			width: 200,
		},
	];
	if (isEditState) {
		columns.push({
			title: '操作',
			dataIndex: 'options',
			key: 'options',
			render: (inviteName, record) => {
				return (
					<Space>
						<Button type="link" size={'small'} onClick={() => handleUpdateRecord(record)}>
							编辑
						</Button>
						{/*<Button type="link" size={'small'} danger onClick={() => handleDeleteRecord(record)}>删除</Button>*/}
					</Space>
				);
			},
			fixed: 'right',
			width: 100,
		});
	} else {
		columns.push({
			title: '操作',
			dataIndex: 'options',
			key: 'options',
			render: (inviteName, record) => {
				return (
					<Space>
						<Button type="link" size={'small'} onClick={() => handleViewRecord(record)}>
							查看
						</Button>
					</Space>
				);
			},
			fixed: 'right',
			width: 100,
		});
	}
	useEffect(() => {
		const id = searchParams.get('id');
		if (id) {
			getOpptyList(id);
		}
	}, []);
	/* 查询商机数据列表 */
	const getOpptyList = async (id) => {
		const res = await pageOpportunity({
			pageNum: 1,
			pageSize: 1000,
			customerId: id,
		});
		if (res.data) {
			setDataSource(res.data.records);
			setCustomerTag('oppoRecord', res.data.records);
		}
	};

	// 查看商机
	const handleViewRecord = (record) => {
		// linkTo(`/businessOppty/opptyManage/curd?id=${record.id}`);
		openNewTab(`/businessOppty/opptyManage/detail?id=${record.id}`);
	};
	// 操作跟进记录
	const handleUpdateRecord = (record) => {
		// linkTo(`/businessOppty/opptyManage/curd?id=${record.id}`);
		openNewTab(`/businessOppty/opptyManage/curd?id=${record.id}`);
	};
	// 新增记录
	const handleAddRecord = () => {
		if (detail.status <= 1) {
			return message.warning('客户状态还未认领，无法新增记录！');
		}
		openNewTab(`/businessOppty/opptyManage/curd?customerId=${detail.id}`);
	};
	// 删除记录
	const handleDeleteRecord = (record) => {
		Modal.confirm({
			title: '确定删除该跟进记录吗？',
			okText: '确定',
			cancelText: '取消',
			onOk: () => {
				const newDataSource = dataSource.filter((item) => item.id !== record.id);
				setDataSource(newDataSource);
				setCustomerTag('oppoRecord', res.data.records);
			},
		});
	};
	return (
		<>
			<div className={'flex justify-between'}>
				<div className={'font-size-14 font-weight-500'}>商机</div>
				{isEditState && (
					<Button type={'primary'} size={'small'} icon={<PlusOutlined />} onClick={handleAddRecord}>
						添加
					</Button>
				)}
			</div>
			<Table rowKey="rowKey" dataSource={dataSource} pagination={false} columns={columns} size={'small'} />
		</>
	);
};
export default OppoRecord;
