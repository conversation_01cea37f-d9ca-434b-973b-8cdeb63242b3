/**
 * @description VisitRecord.jsx - 文件描述
 * <AUTHOR>
 *
 * Created on 2025/5/22 10:56
 */
import React, { useEffect, useState } from 'react';
import { Badge, Button, message, Modal, Select, Space, Table } from 'antd';
import { PlusOutlined } from '@ant-design/icons';
import { getPageInvite } from '@/api/Opportunity/Customer';
import { useRouterLink } from '@/hook/useRouter';

const VisitRecord = ({ detail, isEditState, meetingTypeList, visitList, setCustomerTag }) => {
	const [dataSource, setDataSource] = useState([]);
	const { openNewTab, searchParams } = useRouterLink();

	const columns = [
		{
			title: '序号',
			dataIndex: 'index',
			key: 'index',
			align: 'center',
			width: 80,
			render: (_, record, index) => {
				return index + 1;
			},
		},
		{
			title: '会议类型',
			dataIndex: 'type',
			key: 'type',
			// align: 'center',
			render: (type) => {
				return { 1: '来访邀约', 2: '会议记录' }[type] || '';
				// return meetingTypeList?.find(item => item.id === meetingTypeId)?.name;
			},
		},
		{
			title: '会议主题',
			dataIndex: 'them',
			key: 'them',
			// align: 'center',
		},
		{
			title: '会议时间',
			dataIndex: 'meetingStartTime',
			key: 'meetingStartTime',
			// align: 'center',
		},
	];
	if (isEditState) {
		columns.push({
			title: '操作',
			dataIndex: 'options',
			key: 'options',
			render: (inviteName, record) => {
				return (
					<Space>
						<Button type="link" size={'small'} onClick={() => handleUpdateRecord(record)}>
							编辑
						</Button>
						{/*<Button type="link" size={'small'} danger onClick={() => handleDeleteRecord(record)}>删除</Button>*/}
					</Space>
				);
			},
			fixed: 'right',
			width: 100,
		});
	} else {
		columns.push({
			title: '操作',
			dataIndex: 'options',
			key: 'options',
			render: (inviteName, record) => {
				return (
					<Space>
						<Button type="link" size={'small'} onClick={() => handleUpdateRecord(record)}>
							查看
						</Button>
					</Space>
				);
			},
			fixed: 'right',
			width: 100,
		});
	}
	useEffect(() => {
		if (visitList) {
			setDataSource(visitList);
			setCustomerTag('visitRecord', visitList);
		}
	}, [visitList]);
	// 操作跟进记录
	const handleUpdateRecord = (record) => {
		openNewTab(`/businessOppty/meeting/meetingDetail?type=2&customerId=${detail.id}&id=${record.id}`);
	};
	// 新增记录
	const handleAddRecord = () => {
		if (detail.status <= 1) {
			return message.warning('客户状态还未认领，无法新增记录！');
		}
		// linkTo(`/businessOppty/meeting/meetingDetail?type=2&customerId=${detail.id}`);
		openNewTab(`/businessOppty/meeting/meetingDetail?type=2&companyName=${detail.name}`);
	};
	// 删除记录
	const handleDeleteRecord = (record) => {
		Modal.confirm({
			title: '确定删除该跟进记录吗？',
			okText: '确定',
			cancelText: '取消',
			onOk: () => {
				const newDataSource = dataSource.filter((item) => item.id !== record.id);
				setDataSource(newDataSource);
			},
		});
	};

	return (
		<>
			<div className={'flex justify-between'}>
				<div className={'font-size-14 font-weight-500 color-165dff'}>来访/拜访</div>
				{isEditState && (
					<Button type={'primary'} size={'small'} icon={<PlusOutlined />} onClick={handleAddRecord}>
						添加
					</Button>
				)}
			</div>
			<Table rowKey="rowKey" dataSource={dataSource} pagination={false} columns={columns} size={'small'} />
		</>
	);
};
export default VisitRecord;
