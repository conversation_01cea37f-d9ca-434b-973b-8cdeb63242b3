/**
 * @description useScrollToHash.js - 文件描述
 * <AUTHOR>
 *
 * Created on 2025/5/27 18:06
 */
// hooks/useScrollToHash.js
import { useEffect } from "react";
import { useLocation } from "react-router-dom";

const useScrollToHash = () => {
    const location = useLocation();
    useEffect(() => {
        const hash = location.hash;
        if (hash) {
            const element = document.querySelector(hash);
            if (element) {
                setTimeout(() => {
                    element.scrollIntoView({ behavior: 'smooth', block: 'start' });
                }, 100);
            }
        }
    }, [location]);
    return null;
};

export default useScrollToHash;
