/**
 * @description index.jsx - 客户管理
 * <AUTHOR>
 * CustomerList
 * Created on 2025/5/19 9:19
 */
import React, {useEffect, useRef, useState} from 'react';
import {Link} from "react-router-dom";
import {Affix, Anchor, <PERSON>readc<PERSON>b, Button, Space, message, Modal} from "antd";
import {EditOutlined,} from "@ant-design/icons";
import CustomBase from "@/pages/BusinessOppty/CustomerManage/CustomerDetail/components/CustomBase";
import BusinessInfo from "@/pages/BusinessOppty/CustomerManage/CustomerDetail/components/BusinessInfo";
import BusinessRecords from "@/pages/BusinessOppty/CustomerManage/CustomerDetail/components/BusinessRecords";
import Attribution from "@/pages/BusinessOppty/CustomerManage/CustomerDetail/components/Attribution";
import {
    addCustomer,
    customerAllocation,
    detailCustomer,
    getPageInvite,
    updateCustomer
} from "@/api/Opportunity/Customer";
import {useRouterLink} from "@/hook/useRouter";
import {getByPermissionPerms, getThreeLevelData, getTwoLevelData} from "@/api/common";
import {pageCategoryValue} from "@/api/Bidmgt/Dict";
import useScrollToHash from "./hooks/useScrollToHash";
import {listMeetingType} from "@/api/Opportunity/MettingType";
import {useSelector} from "react-redux";
import {pageAptitude} from "@/api/Bidmgt/ConfigCenter/AptitudeConfig";
import {DistributeRole, DistributeType} from "@/pages/BusinessOppty/CustomerManage/SeasPool/const";
import DistributeModal from "@/pages/BusinessOppty/CustomerManage/SeasPool/components/DistributeModal";

const CustomerDetail = () => {
    useScrollToHash();
    const {linkTo, searchParams} = useRouterLink();
    const id = searchParams.get('id');
    const seasPool = searchParams.get('seasPool');
    /* 是否编辑态 */
    const [isEditState, setEditState] = useState(false);
    /* ref 信息 */
    const baseRef = useRef(null);
    const businessRef = useRef(null);
    const recordRef = useRef(null);
    const ownershipRef = useRef(null);
    /* 客户标签 */
    const customerTagRef = useRef({});
    /* 顶部 */
    const topRef = useRef(null);
    /* 客户分配 */
    const distributeModalRef = useRef(null);

    /* 客户详情信息 */
    const [detail, setDetail] = useState({});
    const [targetOffset, setTargetOffset] = useState(null);
    /* 客户类型 */
    const [customerType, setCustomerType] = useState([]);
    /* 所属区域 */
    const [areaData, setAreaData] = useState([]);
    /* 企业员工名单 */
    const [userList, setUserList] = useState([]);
    /* 部门人员名单 */
    const [deptList, setDeptList] = useState([]);
    /* 获取跟进记录 */
    const [visitList, setVisitList] = useState([]);
    /* 获取会议类型 */
    const [meetingTypeList, setMeetingTypeList] = useState([]);
    /* 客户资质列表 */
    const [aptitudeList, setAptitudeList] = useState([]);
    /* 归属权限 */
    const [ownerPermission, setOwnerPermission] = useState(false);
    /* 分配权限 */
    const [distributeRole, setDistributeRole] = useState(false);
    // 登录凭证
    const userInfo = useSelector((state) => {
        return state.user.userInfo;
    });
    useEffect(() => {
        console.log('topRef', topRef.current?.clientHeight)
        setTargetOffset(topRef.current?.clientHeight);
        getCustomerType();
        getArea();
        getEnterpriseEmployee();
        getMeetingType();
        getAptitudeList();
    }, []);
    useEffect(() => {
        if (id) {
            getCustomerDetail(id);
        } else {
            setEditState(true);
        }
    }, [id]);
    useEffect(() => {
        if (userInfo.roleList) {
            userInfo.roleList.find((item) => item.roleCode === DistributeRole) && setDistributeRole(true);
        }
    }, [userInfo]);
    /* 获取客户类型 */
    const getCustomerType = async () => {
        const res = await pageCategoryValue({
            categoryCode: 'customer_type',
            pageSize: 1000,
            pageNum: 1,
        });
        if (res) {
            setCustomerType(res.data.records.map(ov => ({value: ov.id, label: ov.value})));
        }
    };
    /* 获取所属区域 */
    const getArea = async () => {
        const res = await getTwoLevelData({level: 2}, {showLoading: false});
        if (res.data) {
            setAreaData(res.data);
        }
    };
    /* 获取会议类型 */
    const getMeetingType = async () => {
        const res = await listMeetingType({});
        console.log('res', res);
        setMeetingTypeList(res.data.map(ov => ({value: ov.id, label: ov.name})));
    };
    /* 获取客户资质列表 */
    const getAptitudeList = async () => {
        const res = await pageAptitude({
            pageSize: 10000,
            pageNum: 1,
        });
        if (res.data) {
            setAptitudeList(res.data.records.map(ov => ({value: ov.aptitudeName, label: ov.aptitudeName})));
        }
    };
    /* 获取员工 */
    const getEnterpriseEmployee = async () => {
        const res = await getByPermissionPerms({perms: 'businessOppty'}, {showLoading: false});
        if (res) {
            const selectObj = {};
            setUserList(res.data.map(ov => ({
                ...ov,
                value: ov.id,
                label: [ov.userName].concat(ov.deptList?.map(oov => oov.name)).join('/'),
                // check: false,
                disabled: false
            })));
            (res.data || []).forEach(ov => {
                const {
                    id,
                    userName,
                    deptList = []
                } = ov;
                const staffItem = {
                    value: id,
                    label: userName,
                    // check: false,
                    disabled: false
                }
                deptList.forEach(oov => {
                    const {id, name} = oov;
                    if (!selectObj[id]) {
                        selectObj[id] = {
                            value: id,
                            label: name,
                            children: [],
                        }
                    }
                    selectObj[id].children.push({...staffItem})
                });
            });
            const selectList = Object.values(selectObj).filter(ov => ov.children.length > 0)
            console.log('部门人员信息', selectList);
            setDeptList(selectList);
        }
    };
    /* 获取客户详情 */
    const getCustomerDetail = async (id) => {
        const res = await detailCustomer({id: id});
        if (res.data) {
            // console.log('客户详情', res.data);
            setDetail(res.data);
            getVisitRecord(res.data.name);
            // 判断是否有归属权限
            if (res.data.customerOwners?.length > 0) {
                const ids = res.data.customerOwners?.map(ov => ov.userId);
                setOwnerPermission(ids.includes(userInfo.id))
            }
        }
    };
    /* 获取拜访记录 */
    const getVisitRecord = async (name) => {
        const res = await getPageInvite({
            // companyName: '大湾区科技创新服务中心',
            companyName: `${name || ''}`.trim(),
            // type: 2,
            pageSize: 1000,
            pageNum: 1,
        });
        if (res.data) {
            console.log('客户邀请记录', res.data);
            const list = res.data.records.map(item => {
                const meetings = item?.meetings?.[0];
                return {
                    ...item,
                    meetingType: meetings?.meetingTypeId,
                    them: meetings?.theme || item.title,
                    meetingStartTime: meetings?.meetingStartTime,
                };
            });
            setVisitList(list);
        }
    };
    /* 设置客户标签内容 */
    const setCustomerTag = (key, value) => {
        console.log('设置客户标签内容', key, value);
        customerTagRef.current = {
            ...customerTagRef.current,
            [key]: value
        };
    };
    /* 编辑客户信息 */
    const onUpdate = () => {
        // console.log('编辑');
        setEditState(true);
    };
    /* 通过用户ID和部门ID查询用户信息 */
    const formatUserInfo = ([deptId, userId]) => {
        const user = {};
        userList.map((ov) => {
            if (ov.id === userId) {
                user.userId = ov.id;
                user.userName = ov.userName;
                const dept = ov.deptList.find(oov => oov.id === deptId);
                user.departmentId = dept.id;
                user.departmentName = dept.name;
            }
        });
        return user;
    };
    /* 保存提交 */
    const onSave = async () => {
        const {region, ...baseData} = await baseRef.current?.handleSubmit();
        const attributionData = await ownershipRef.current?.handleSubmit();
        const businessData = await businessRef.current?.handleSubmit();
        const recordData = await recordRef.current?.handleSubmit();
        const [regionProvinceCode, regionCityCode] = region || [];
        const {customerOwners = [], customerGuiders = []} = attributionData;
        const params = {
            ...baseData,
            ...businessData,
            ...recordData,
            regionProvinceCode,
            regionCityCode,
            customerOwners: customerOwners.map(ov => formatUserInfo(ov)),
            customerGuiders: customerGuiders.map(ov => formatUserInfo(ov)),
        };
        let res;
        if (id) {
            params.id = id;
            res = await updateCustomer(params);
        } else {
            res = await addCustomer(params);
        }
        if (res.data) {
            setEditState(false);
            message.success('保存成功');
            /* 如果是修改则返回预览；创建则返回上一页 */
            if (id) {
                getCustomerDetail(id);
                setEditState(false);
            } else {
                history.back();
            }
        }
    };
    /* 取消修改 */
    const onCancel = () => {
        if (!id) {
            history.back();
            return;
        }
        setEditState(false);
    };
    /* 认领客户 */
    const onClaim = async () => {
        Modal.confirm( {
            title: '领取客户',
            content: '确认领取客户吗？',
            okText: '领取',
            cancelText: '取消',
            onOk: async () => {
                const params = {
                    customerIds: [id],
                    allocationType: DistributeType.claim
                };
                try {
                    const res = await customerAllocation(params);
                    if (res.data) {
                        message.success('领取成功');
                        getCustomerDetail(id);
                        return Promise.resolve();
                    }
                } catch (e) {
                    console.log(e);
                    return Promise.reject(e);
                }
            }
        });
    };
    /* 释放客户 */
    const onRelease = async () => {
        Modal.confirm( {
            title: '释放客户',
            content: '确认释放客户吗？',
            okText: '释放',
            cancelText: '取消',
            onOk: async () => {
                const params = {
                    customerIds: [id],
                    allocationType: DistributeType.release
                };
                try {
                    const res = await customerAllocation(params);
                    if (res.data) {
                        message.success('释放成功');
                        linkTo(`/businessOppty/customerManage/${seasPool ? 'seasPool' : 'customerList'}`);
                        return Promise.resolve();
                    }
                } catch (e) {
                    return Promise.reject(e);
                }
            },
        })
    };
    /* 分配客户 */
    const onDistribute = async () => {
        const customerIds = [id];
        distributeModalRef.current?.openModal(customerIds);
    }

    return (<div id={'customer-detail'} style={{height: 'calc(100% - 40px)'}}
                 className={'flex-sub overflowY-auto flex flex-direction-column padding-20 gap-20 border-radius-4'}>
        <Breadcrumb
            items={[
                {
                    title: seasPool ? <Link to={`/businessOppty/customerManage/seasPool`}>公海池</Link> :
                        <Link to={`/businessOppty/customerManage/customerList`}>客户列表</Link>
                },
                {title: id ? '客户详情' : '新增客户'},
            ]}
        />
        <Affix
            target={() => document.getElementById('customer-detail')}
            offsetTop={0}  // 增加顶部间距
            onChange={(affixed) => {
                console.log('Affix状态:', affixed);
            }}
        >
            <div ref={topRef}
                 className="padding-20 bg-color-ffffff border-radius-8 flex justify-between align-center line-height-24">
                <Anchor
                    affix={false}
                    rootClassName="anchor-header-tabBar-box"
                    direction="horizontal"
                    replace
                    targetOffset={targetOffset}  // 调整滚动偏移量
                    onClick={(e) => e.preventDefault()}  // 阻止默认跳转行为
                    items={[
                        {
                            key: 'base',
                            href: '#base',
                            title: <div className="margin-right-40 font-size-16 font-weight-600">基本信息</div>,
                        },
                        {
                            key: 'business',
                            href: '#business',
                            title: <div className="margin-right-40 font-size-16 font-weight-600">经营信息</div>,
                        },
                        {
                            key: 'record',
                            href: '#record',
                            title: <div className="margin-right-40 font-size-16 font-weight-600">业务记录</div>,
                        },
                        {
                            key: 'ownership',
                            href: '#ownership',
                            title: <div className="margin-right-40 font-size-16 font-weight-600">客户归属</div>,
                        },
                    ].filter(item => {
                        if (item.key === 'business' && id) {
                            return item.key === 'business' && ownerPermission;
                        }
                        return true;
                    })}
                />
                <Space>
                    {
                        isEditState ? (
                                <>
                                    <Button type={'primary'} onClick={onSave}>保存</Button>
                                    <Button onClick={onCancel}>取消</Button>
                                </>
                            ) :
                            (<>
                                {/* 待认领的可认领 */}
                                {
                                    [1].includes(detail.status) && (
                                        <Button onClick={onClaim}>认领</Button>
                                    )
                                }
                                {/* 待分配 分配处理 */}
                                {
                                    [0].includes(detail.status) && distributeRole && (
                                        <Button variant={'outlined'} onClick={onDistribute}>分配</Button>
                                    )
                                }
                                {/* 跟进中状态可释放 */}
                                {
                                    [2,3].includes(detail.status) && ownerPermission && (
                                        <Button danger onClick={onRelease}>释放</Button>
                                    )
                                }
                                <Button type={'primary'} icon={<EditOutlined/>} onClick={onUpdate}>编辑</Button>
                            </>)
                    }
                </Space>
            </div>
        </Affix>
        {/* 基本信息 */}
        <div id={'base'}>
            <CustomBase
                ref={baseRef}
                detail={detail}
                isEditState={isEditState}
                customerType={customerType}
                areaData={areaData}
                customerTagRef={customerTagRef}
                setCustomerTag={setCustomerTag}
                meetingTypeList={meetingTypeList}
                ownerPermission={ownerPermission}
                aptitudeList={aptitudeList}
            />
        </div>
        {/* 经营信息 */}
        {
            (ownerPermission || !id) && (
                <div id={'business'}>
                    <BusinessInfo
                        ref={businessRef}
                        detail={detail}
                        isEditState={isEditState}
                    />
                </div>
            )
        }
        <div id={'record'}>
            <BusinessRecords
                ref={recordRef}
                baseRef={baseRef}
                detail={detail}
                isEditState={isEditState}
                deptList={deptList}
                formatUserInfo={formatUserInfo}
                meetingTypeList={meetingTypeList}
                visitList={visitList}
                setCustomerTag={setCustomerTag}
                userInfo={userInfo}
            />
        </div>
        <div id={'ownership'}>
            <Attribution
                ref={ownershipRef}
                detail={detail}
                isEditState={isEditState}
                deptList={deptList}
                visitList={visitList}
                userInfo={userInfo}
                ownerPermission={ownerPermission}
                distributeRole={distributeRole}
            />
        </div>
        {/* 导入 结束 */}
        <DistributeModal ref={distributeModalRef} userList={deptList} reload={() => getCustomerDetail(id)}/>
    </div>)
}
export default CustomerDetail;
