/**
 * @description const - 文件描述
 * <AUTHOR>
 *
 * Created on 2025/5/19 9:40
 */

export const TabsList = [
    {
        title: '全部客户',
        key: 'customer'
    },
    {
        title: '我的客户',
        key: 'myCustomer'
    },
    {
        title: '部门客户',
        key: 'myDept'
    },
];
/* 客户状态”字段，分为:待分配、待认领、已认领、跟进中、已成交  */
export const CustomerStatus = [
    {
        label: '待分配',
        value: 0,
    },
    {
        label: '待认领',
        value: 1
    },
    {
        label: '已认领',
        value: 2
    },
    {
        label: '跟进中',
        value: 3
    },
    {
        label: '已成交',
        value: 4
    }
];
/* 状态颜色 */
export const StatusColor = {
    '0': '#8C8C8C',
    '1': '#FAAD14',
    '2': '#4096FF',
    '3': '#52C41A',
    '4': '#135200',
}
/* 状态名称 */
// export const StatusName = CustomerStatus.map(item => item.label);

// 客户导入模板
export const CustomerImportTemplateUrl = `https://gbac-produce.oss-cn-shenzhen.aliyuncs.com/static-assets/gbac-bidmgt-admfrontend/files/%E5%AE%A2%E6%88%B7%E5%AF%BC%E5%85%A5%E6%A8%A1%E6%9D%BF.xlsx`;
