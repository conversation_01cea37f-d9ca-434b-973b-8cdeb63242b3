/**
 * @description index.jsx - 客户管理
 * <AUTHOR>
 * CustomerList
 * Created on 2025/5/19 9:19
 */
import React, { useEffect, useRef, useState } from 'react';
import { Button, Col, Divider, Form, Input, Row, Select, Space, Table, Cascader, Dropdown, message, Tag, Badge } from 'antd';
import { CloudDownloadOutlined, ImportOutlined, PlusOutlined, ReloadOutlined, SearchOutlined, UploadOutlined } from '@ant-design/icons';
import { CustomerImportTemplateUrl, CustomerStatus, TabsList } from './const';
import { getByPermissionPerms, getThreeLevelData, getTwoLevelData } from '@/api/common';
import { getDeptData } from '@/utils/dictionary';
import { pageCategoryValue } from '@/api/Bidmgt/Dict';
import { exportCustomerData, importCustomerData, pageCustomer } from '@/api/Opportunity/Customer';
import { useRouterLink } from '@/hook/useRouter';
import ModalForm from '@/components/ModalForm';
import ImportForm from '@/components/ImportForm';
import { download } from '@/utils/common';
import dayjs from 'dayjs';
import { pageAptitude } from '@/api/Bidmgt/ConfigCenter/AptitudeConfig';
import { useSelector } from 'react-redux';
import { StatusColor } from './const';
import { ExportType } from '@/pages/BusinessOppty/CustomerManage/SeasPool/const';
const StatusName = CustomerStatus.map((item) => item.label);

const CustomerList = () => {
	const { linkTo, openNewTab } = useRouterLink();
	const ModalFormImportRef = useRef();
	const [activeKey, setActiveKey] = useState('customer');
	/* 客户类型 */
	const [CustomTypeList, setCustomTypeList] = useState([]);
	/* 所属区域 省市 */
	const [areaList, setAreaList] = useState([]);
	// 中心人员名单
	const [centerStaffList, setCenterStaffList] = useState([]);
	// 中心部门列表
	const [centerDepartmentList, setCenterDepartmentList] = useState([]);
	/* 客户资质列表 */
	const [aptitudeList, setAptitudeList] = useState([]);
	// pagination 状态
	const [pagination, setPagination] = useState({
		current: 1,
		pageSize: 10,
		total: 0,
	});
	// 登录凭证
	const userInfo = useSelector((state) => {
		return state.user.userInfo;
	});
	// 表格数据
	const [dataSource, setDataSource] = useState([]);
	const [form] = Form.useForm();
	/* 导入数据 */
	const importData = (e) => {
		ModalFormImportRef.current.setOpen(true);
		ModalFormImportRef.current.setTitle('客户导入');
	};
	/* 导出数据 */
	const exportData = async () => {
		console.log('导出数据');
		const params = await form.validateFields();
		if (params.area?.length === 2) {
			const [regionProvinceCode, regionCityCode] = params.area;
			params.regionProvinceCode = regionProvinceCode;
			params.regionCityCode = regionCityCode;
			delete params.area;
		}
		const res = await exportCustomerData({ ...params, exportType: ExportType.nonPublic });
		if (res) {
			// 导出数据处理
			download.excel(res, `客户列表-${dayjs().format('YYYYMMDDhhmmss')}`);
			message.success('导出成功');
		}
	};
	/* 新增数据 */
	const addData = () => {
		console.log('新增数据');
		linkTo('/businessOppty/customerManage/customerDetail');
	};
	// 修改页面
	const changePage = (pageNum, pageSize) => {
		console.log('修改页面');
		getTableData({ pageNum, pageSize });
	};
	// 获取表格数据
	const getTableData = async (args) => {
		const formData = await form.validateFields();
		const params = {
			...formData,
			aptitudeIds: formData.aptitudeIds ? [formData.aptitudeIds] : undefined,
			pageSize: pagination.pageSize,
			pageNum: pagination.current,
			...args,
		};
		if (params.area?.length === 2) {
			const [regionProvinceCode, regionCityCode] = params.area;
			params.regionProvinceCode = regionProvinceCode;
			params.regionCityCode = regionCityCode;
			delete params.area;
		}
		const res = await pageCustomer(params);
		if (res.data) {
			console.log('表格数据', res.data);
			setDataSource(res.data.records);
			setPagination({
				...pagination,
				current: params.pageNum || 1,
				pageSize: params.pageSize || 10,
				total: res.data.total,
			});
		}
	};
	// 重置表单
	const onReset = () => {
		form.resetFields();
		getTableData({ pageNum: 1, pageSize: 10 });
	};
	// 数组去重
	const uniqueArray = (arr) => {
		return [...new Set(arr)];
	};
	const columns = [
		{
			title: '客户名称',
			dataIndex: 'name',
			key: 'name',
		},
		{
			title: '所属区域',
			dataIndex: 'area',
			key: 'area',
			render: (text, { regionProvinceCode, regionCityCode }) => {
				const area = [];
				if (regionProvinceCode) {
					const province = areaList.find((item) => item.value === regionProvinceCode);
					if (province) {
						area.push(province.label);
						const city = province.children.find((item) => item.value === regionCityCode);
						if (city) {
							area.push(city.label);
						}
					}
				}
				return area.join('/');
			},
		},
		{
			title: '客户类型',
			dataIndex: 'typeId',
			key: 'typeId',
			render: (text, record) => {
				const type = CustomTypeList.find((item) => item.value === record.typeId);
				return type?.label || '';
			},
		},
		{
			title: '客户状态',
			dataIndex: 'statusName',
			key: 'statusName',
			render: (text, record) => {
				// return <Tag color={StatusColor[record.status]}>{StatusName[record.status]}</Tag>;
				return <Badge color={StatusColor[record.status]} text={StatusName[record.status]} />;
			},
		},
		{
			title: '归属人',
			dataIndex: 'owner',
			key: 'owner',
			render: (text, { customerOwners }) => {
				return uniqueArray(customerOwners?.map((item) => item.userName)).join('/');
			},
		},
		{
			title: '归属部门',
			dataIndex: 'department',
			key: 'department',
			render: (text, { customerOwners }) => {
				return uniqueArray(customerOwners?.map((item) => item.departmentName)).join('/');
			},
		},
		{
			title: '来访/拜访',
			dataIndex: 'visitQuantity',
			key: 'visitQuantity',
			width: 110,
			render: (text, record) => {
				return (
					<Button size={'small'} type={'link'} onClick={() => onView(record, '#record')}>
						{text || 0}
					</Button>
				);
			},
		},
		{
			title: '线索/商机数',
			dataIndex: 'clueAndOppQuantity',
			key: 'clueAndOppQuantity',
			width: 110,
			render: (text, record) => {
				return (
					<Button size={'small'} type={'link'} onClick={() => onView(record, '#record')}>
						{text || 0}
					</Button>
				);
			},
		},
		{
			title: '合作记录',
			dataIndex: 'customerCooperationRecords',
			key: 'customerCooperationRecords',
			width: 110,
			render: (text, record) => {
				return (
					<Button size={'small'} type={'link'} onClick={() => onView(record, '#record')}>
						{text?.length || 0}
					</Button>
				);
			},
		},
		{
			title: '操作',
			key: 'action',
			fixed: 'right',
			render: (text, record) => (
				<Space>
					<Button type={'link'} size={'small'} onClick={() => onView(record)}>
						查看/编辑
					</Button>
					<Button type={'link'} size={'small'} onClick={() => onAddVisit(record)}>
						新增拜访
					</Button>
					<Button type={'link'} size={'small'} onClick={() => onAddClue(record)}>
						新增线索
					</Button>
				</Space>
			),
		},
	];
	// 初始化获取
	useEffect(() => {
		Promise.all([getCustomTypeList(), getAreaList(), getCenterStaffList(), getCenterDepartmentList(), getTableData(), getAptitudeList()])
			.then(() => {
				console.log('初始化获取数据成功');
			})
			.catch(() => {
				console.log('初始化获取数据失败');
			});
	}, []);
	/* 获取客户资质列表 */
	const getAptitudeList = async () => {
		const res = await pageAptitude({
			pageSize: 10000,
			pageNum: 1,
		});
		if (res.data) {
			setAptitudeList(res.data.records.map((ov) => ({ value: ov.id, label: ov.aptitudeName })));
		}
	};
	// 获取客户类型列表
	const getCustomTypeList = async () => {
		const res = await pageCategoryValue({ categoryCode: 'customer_type' });
		if (res.data) {
			const options = res.data.records?.map((item) => ({
				label: item.value,
				value: item.id,
			}));
			console.log('客户类型列表', options);
			setCustomTypeList(options);
		}
	};
	// 获取省市列表
	const getAreaList = async () => {
		const res = await getTwoLevelData({ level: 2 }, { showLoading: false });
		console.log('省市列表', res);
		if (res.data) {
			setAreaList(res.data);
		}
	};
	// 获取中心人员名单
	const getCenterStaffList = async () => {
		const res = await getByPermissionPerms({ perms: 'businessOppty' }, { showLoading: false });
		if (res) {
			setCenterStaffList(
				res.data.map((ov) => ({
					label: ov.userName,
					value: ov.id,
				}))
			);
		}
	};
	// 获取中心部门列表
	const getCenterDepartmentList = async () => {
		const res = await getDeptData();
		if (res) {
			setCenterDepartmentList(res);
		}
	};

	// 文件上传回调
	const handleUpload = async (file) => {
		console.log('上传文件', file);
		message.success('上传成功');
		const res = await importCustomerData(file);
		if (res.data) {
			console.log('表格数据', res.data);
			message.success('导入成功，等待后台处理中...');
			// getTableData();
		} else {
			message.error('导入失败');
		}
	};
	// 查看详情
	const onView = (record, tabs) => {
		console.log('查看详情', record);
		linkTo(`/businessOppty/customerManage/customerDetail?id=${record.id}${tabs || ''}`);
	};
	// 新增拜访
	const onAddVisit = (record) => {
		console.log('新增拜访');
		openNewTab(`/businessOppty/meeting/meetingDetail?type=2&companyName=${record.name}`);
	};
	// 新增线索
	const onAddClue = (record) => {
		console.log('新增线索');
		openNewTab(`/businessOppty/clueManage/clueDetail?type=2&customerId=${record.id}&customerTypeId=${record.typeId}`);
	};
	// 修改tabs
	const onChangeTabs = (key) => {
		setActiveKey(key);
		const params = {
			pageNum: 1,
			pageSize: 10,
		};
		const userDept = userInfo.deptList[0] || {};
		switch (key) {
			case 'customer':
				break;
			case 'myCustomer':
				params.ownerId = userInfo.id;
				break;
			case 'myDept':
				params.ownerDepartmentId = userDept.id;
				break;
		}
		form.resetFields();
		form.setFieldsValue(params);
		getTableData(params);
	};
	return (
		<div className={'flex-sub flex flex-direction-column margin-20 padding-20  border-radius-4'}>
			<div className="padding-20 bg-color-ffffff border-radius-8 flex justify-between align-center line-height-24 margin-bottom-20">
				<div className={'font-size-16 flex flex-sub gap-40'}>
					{TabsList.map((item) => {
						return (
							<div key={item.key} className={'flex align-center'} onClick={() => onChangeTabs(item.key)}>
								<span
									className={`font-size-16 cursor-pointer font-weight-600 ${activeKey === item.key ? 'color-165dff' : 'color-1d2129'}`}
								>
									{item.title}
								</span>
							</div>
						);
					})}
				</div>
				<Space>
					<Button icon={<ImportOutlined />} onClick={importData}>
						导入
					</Button>
					<Button icon={<CloudDownloadOutlined />} onClick={exportData}>
						导出
					</Button>
					<Button type={'primary'} icon={<PlusOutlined />} onClick={addData}>
						新建
					</Button>
				</Space>
			</div>
			<div className={'bg-color-ffffff padding-20 border-radius-8'}>
				<Form form={form} layout={'inline'} className={'width-100per flex flex-sub align-start'}>
					<Row gutter={[20, 20]} className={'flex-sub'}>
						<Col span={8}>
							<Form.Item label="客户名称" name="name" className={'flex-sub '}>
								<Input placeholder={'请输入客户名称'} className={'width-100per'} allowClear />
							</Form.Item>
						</Col>
						<Col span={8}>
							<Form.Item label="客户类型" name="typeId" className={'flex-sub '}>
								<Select options={CustomTypeList} className={'width-100per'} placeholder={'请选择客户类型'} allowClear />
							</Form.Item>
						</Col>
						<Col span={8}>
							<Form.Item label="所属区域" name="area" className={'flex-sub '}>
								<Cascader options={areaList} className={'width-100per'} placeholder={'请选择所属区域'} allowClear />
							</Form.Item>
						</Col>
						<Col span={8}>
							<Form.Item label="客户资质" name="aptitudeIds" className={'flex-sub '}>
								<Select
									placeholder={'请选择客户资质'}
									className={'width-100per'}
									options={aptitudeList}
									optionFilterProp={'label'}
									maxTagCount={'responsive'}
									showSearch
									allowClear
								/>
							</Form.Item>
						</Col>
						<Col span={8}>
							<Form.Item label="归属人" name="ownerId" className={'flex-sub '}>
								<Select
									placeholder={'请选择归属人'}
									className={'width-100per'}
									options={centerStaffList}
									optionFilterProp={'label'}
									// mode={'multiple'}
									maxTagCount={'responsive'}
									showSearch
									allowClear
								/>
							</Form.Item>
						</Col>
						<Col span={8}>
							<Form.Item label="归属部门" name="ownerDepartmentId" className={'flex-sub '}>
								<Select
									placeholder={'请选择归属部门'}
									className={'width-100per'}
									// mode={'multiple'}
									maxTagCount={'responsive'}
									options={centerDepartmentList}
									allowClear
									showSearch
								/>
							</Form.Item>
						</Col>
						<Col span={8}>
							<Form.Item label="客户状态" name="status" className={'flex-sub '}>
								<Select options={CustomerStatus} allowClear placeholder={'请选择客户状态'} />
							</Form.Item>
						</Col>
					</Row>
					{/*<Divider type="vertical" style={{margin: '0 16px'}}/>*/}
					<div className="width-1 height-84 bg-color-e5e6eb flex-shirnk margin-lr-20" />
					<Form.Item noStyle>
						<Space direction={'vertical'} size={20}>
							<Button type={'primary'} htmlType={'submit'} icon={<SearchOutlined />} onClick={() => getTableData()}>
								查询
							</Button>
							<Button htmlType={'reset'} icon={<ReloadOutlined />} onClick={onReset}>
								重置
							</Button>
						</Space>
					</Form.Item>
				</Form>
				<Divider />
				<Table
					rowKey="id"
					columns={columns}
					pagination={{
						...pagination,
						onChange: changePage,
						showSizeChanger: true,
						showTotal: (total) => `共 ${total} 条`,
					}}
					scroll={{ x: 'max-content' }} // 关键：让表格水平滚动
					style={{ overflowX: 'auto' }}
					dataSource={dataSource}
				/>
			</div>
			{/* 导入 开始 */}
			<ModalForm
				ref={ModalFormImportRef}
				modelConfig={{
					styles: {
						body: {
							minHeight: 'unset',
						},
					},
				}}
				onOk={handleUpload}
				FormComp={(props) => <ImportForm ref={props.FormCompRef} fileName="file" tplUrl={CustomerImportTemplateUrl} />}
			/>
			{/* 导入 结束 */}
		</div>
	);
};
export default CustomerList;
