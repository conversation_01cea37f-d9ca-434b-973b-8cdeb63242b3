/**
 * @description DistributeModal.jsx - 文件描述
 * <AUTHOR>
 *
 * Created on 2025-06-27 上午 11:39
 */
import React, { forwardRef, useEffect, useImperativeHandle, useState } from 'react';
import { Cascader, Form, message, Modal } from 'antd';
import { DistributeType } from '@/pages/BusinessOppty/CustomerManage/SeasPool/const';
import { customerAllocation } from '@/api/Opportunity/Customer';

const DistributeModal = (props, ref) => {
	const [open, setOpen] = useState(false);
	const { userList = [], reload } = props;
	const [selectedKeys, setSelectedKeys] = useState([]);
	useEffect(() => {
		console.log('userList~~~~~', userList);
	}, [userList]);
	const [form] = Form.useForm();
	const openModal = (keys = []) => {
		setOpen(true);
		setSelectedKeys(keys);
	};
	const closeModal = () => {
		setOpen(false);
		form.resetFields();
	};
	/* 提交表单 */
	const handleSubmit = () => {
		form.validateFields()
			.then(async (values) => {
				console.log('Received values of form: ', values);
				const { ownerId } = values;
				const [currentAssigneeDeptId, currentAssigneeId] = ownerId;
				const params = {
					customerIds: selectedKeys,
					allocationType: DistributeType.distribute,
					currentAssigneeDeptId,
					currentAssigneeId,
				};
				const res = await customerAllocation(params);
				if (res.data) {
					message.success('分配成功');
					props.reload?.();
					closeModal();
				}
			})
			.catch((info) => {
				console.log('Validate Failed:', info);
			});
	};
	useImperativeHandle(ref, () => ({
		openModal: openModal,
		closeModal: closeModal,
		handleSubmit: handleSubmit,
	}));
	return (
		<Modal title={'分配客户'} open={open} onCancel={closeModal} onOk={handleSubmit} maskClosable={false}>
			<Form form={form} layout="vertical">
				<Form.Item label="归属人" name={'ownerId'} required rules={[{ required: true, message: '归属人不能为空' }]}>
					<Cascader
						options={userList}
						// className={'width-100per'}
						placeholder="请选择"
						showCheckedStrategy={Cascader.SHOW_CHILD}
					/>
				</Form.Item>
			</Form>
		</Modal>
	);
};
export default forwardRef(DistributeModal);
