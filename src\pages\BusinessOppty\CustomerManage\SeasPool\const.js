/**
 * @description const - 文件描述
 * <AUTHOR>
 *
 * Created on 2025-06-27 上午 9:50
 */
export const TabsList = [
    {
        title: '公海池',
        key: 'customer'
    },
];

/* 认领角色权限 */
export const ClaimRole = 'claim';
/* 分配权限 */
export const DistributeRole = 'distributor';

/* 枚举分配类型 分配类型(1:分配 2:认领 3:转移 4:释放) */
export const DistributeType = {
    distribute: 1,
    claim: 2,
    transfer: 3,
    release: 4
};
/* 状态颜色 */
export const StatusColor = {
    '0': '#FAAD14',
    '1': '#1890FF',
    '2': '#52C41A',
}

/**
 * 导出类型
 * @Schema(description = "导出类型：1.非公海池客户 2.公海池客户 ")
 * private int exportType;
 */
export const ExportType = {
    nonPublic: 1,
    public: 2
}
