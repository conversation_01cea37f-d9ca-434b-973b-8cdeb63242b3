/**
 * @description index.jsx - 客户管理
 * <AUTHOR>
 * CustomerList
 * Created on 2025/5/19 9:19
 */
import React, { useEffect, useRef, useState } from 'react';
import { Button, Col, Divider, Form, Input, Row, Select, Space, Table, Cascader, Dropdown, message, Modal, Tag, Badge } from 'antd';
import { CloudDownloadOutlined, ImportOutlined, PlusOutlined, ReloadOutlined, SearchOutlined, UploadOutlined } from '@ant-design/icons';
import { CustomerImportTemplateUrl, CustomerStatus, StatusColor } from '../CustomerList/const';
import { DistributeRole, DistributeType, ExportType, TabsList } from './const';
import { getByPermissionPerms, getTwoLevelData } from '@/api/common';
import { pageCategoryValue } from '@/api/Bidmgt/Dict';
import { customerAllocation, exportCustomerData, importCustomerData, pageCustomer, pagePoolCustomer } from '@/api/Opportunity/Customer';
import { useRouterLink } from '@/hook/useRouter';
import ModalForm from '@/components/ModalForm';
import ImportForm from '@/components/ImportForm';
import { download } from '@/utils/common';
import dayjs from 'dayjs';
import { pageAptitude } from '@/api/Bidmgt/ConfigCenter/AptitudeConfig';
import { useSelector } from 'react-redux';
import DistributeModal from './components/DistributeModal';

const StatusName = CustomerStatus.map((item) => item.label);
const CustomerList = () => {
	const { linkTo, openNewTab } = useRouterLink();
	const ModalFormImportRef = useRef();
	const [activeKey, setActiveKey] = useState('customer');
	/* 客户类型 */
	const [CustomTypeList, setCustomTypeList] = useState([]);
	/* 所属区域 省市 */
	const [areaList, setAreaList] = useState([]);
	/* 客户资质列表 */
	const [aptitudeList, setAptitudeList] = useState([]);
	/* 人员列表 */
	const [userList, setUserList] = useState([]);
	// pagination 状态
	const [pagination, setPagination] = useState({
		current: 1,
		pageSize: 10,
		total: 0,
	});
	// 选中的客户
	const [selectedRowKeys, setSelectedRowKeys] = useState([]);
	/* 分配权限 */
	const [distributeRole, setDistributeRole] = useState(false);
	// 登录凭证
	const userInfo = useSelector((state) => {
		return state.user.userInfo;
	});
	// 表格数据
	const [dataSource, setDataSource] = useState([]);
	const [form] = Form.useForm();
	const distributeModalRef = useRef();

	useEffect(() => {
		if (userInfo.roleList) {
			userInfo.roleList.find((item) => item.roleCode === DistributeRole) && setDistributeRole(true);
		}
	}, [userInfo]);
	/* 导入数据 */
	const importData = (e) => {
		ModalFormImportRef.current.setOpen(true);
		ModalFormImportRef.current.setTitle('客户导入');
	};
	/* 导出数据 */
	const exportData = async () => {
		console.log('导出数据');
		const params = await form.validateFields();
		if (params.area?.length === 2) {
			const [regionProvinceCode, regionCityCode] = params.area;
			params.regionProvinceCode = regionProvinceCode;
			params.regionCityCode = regionCityCode;
			delete params.area;
		}
		if (params.aptitudeIds) {
			params.aptitudeIds = [params.aptitudeIds];
		}
		const res = await exportCustomerData({ ...params, exportType: ExportType.public });
		if (res) {
			// 导出数据处理
			download.excel(res, `客户列表-${dayjs().format('YYYYMMDDhhmmss')}`);
			message.success('导出成功');
		}
	};
	/* 新增数据 */
	const addData = () => {
		console.log('新增数据');
		linkTo('/businessOppty/customerManage/customerDetail?seasPool=1');
	};
	// 修改页面
	const changePage = (pageNum, pageSize) => {
		console.log('修改页面');
		setSelectedRowKeys([]);
		getTableData({ pageNum, pageSize });
	};
	// 获取表格数据
	const getTableData = async (args) => {
		const formData = await form.validateFields();
		const params = {
			...formData,
			aptitudeIds: formData.aptitudeIds ? [formData.aptitudeIds] : undefined,
			pageSize: pagination.pageSize,
			pageNum: pagination.current,
			...args,
		};
		if (params.area?.length === 2) {
			const [regionProvinceCode, regionCityCode] = params.area;
			params.regionProvinceCode = regionProvinceCode;
			params.regionCityCode = regionCityCode;
			delete params.area;
		}
		const res = await pagePoolCustomer(params);
		if (res.data) {
			console.log('表格数据', res.data);
			setDataSource(res.data.records);
			setPagination({
				...pagination,
				current: params.pageNum || 1,
				pageSize: params.pageSize || 10,
				total: res.data.total,
			});
		}
	};
	// 重置表单
	const onReset = () => {
		form.resetFields();
		getTableData({ pageNum: 1, pageSize: 10 });
	};
	// 数组去重
	const uniqueArray = (arr) => {
		return [...new Set(arr)];
	};
	const columns = [
		{
			title: '客户名称',
			dataIndex: 'name',
			key: 'name',
		},
		{
			title: '所属区域',
			dataIndex: 'area',
			key: 'area',
			render: (text, { regionProvinceCode, regionCityCode }) => {
				const area = [];
				if (regionProvinceCode) {
					const province = areaList.find((item) => item.value === regionProvinceCode);
					if (province) {
						area.push(province.label);
						const city = province.children.find((item) => item.value === regionCityCode);
						if (city) {
							area.push(city.label);
						}
					}
				}
				return area.join('/');
			},
		},
		{
			title: '客户类型',
			dataIndex: 'typeId',
			key: 'typeId',
			render: (text, record) => {
				const type = CustomTypeList.find((item) => item.value === record.typeId);
				return type?.label || '';
			},
		},
		{
			title: '客户状态',
			dataIndex: 'statusName',
			key: 'statusName',
			render: (text, record) => {
				return <Badge color={StatusColor[record.status]} text={StatusName[record.status]} />;
			},
		},
		{
			title: '来访/拜访',
			dataIndex: 'visitQuantity',
			key: 'visitQuantity',
			width: 110,
			render: (text, record) => {
				return (
					<Button size={'small'} type={'link'} onClick={() => onView(record, '#record')}>
						{text || 0}
					</Button>
				);
			},
		},
		{
			title: '线索/商机数',
			dataIndex: 'clueAndOppQuantity',
			key: 'clueAndOppQuantity',
			width: 110,
			render: (text, record) => {
				return (
					<Button size={'small'} type={'link'} onClick={() => onView(record, '#record')}>
						{text || 0}
					</Button>
				);
			},
		},
		{
			title: '合作记录',
			dataIndex: 'customerCooperationRecords',
			key: 'customerCooperationRecords',
			width: 110,
			render: (text, record) => {
				return (
					<Button size={'small'} type={'link'} onClick={() => onView(record, '#record')}>
						{text?.length || 0}
					</Button>
				);
			},
		},
		{
			title: '操作',
			key: 'action',
			fixed: 'right',
			render: (text, record) => (
				<Space>
					<Button type={'link'} size={'small'} onClick={() => onView(record)}>
						查看/编辑
					</Button>
					{distributeRole && (
						<Button type={'link'} size={'small'} onClick={() => onDistribute(record)}>
							分配
						</Button>
					)}
					<Button type={'link'} size={'small'} onClick={() => onClaim(record)}>
						认领
					</Button>
				</Space>
			),
		},
	];
	// 初始化获取
	useEffect(() => {
		Promise.all([getCustomTypeList(), getAreaList(), getTableData(), getAptitudeList(), getCenterStaffList()])
			.then(() => {
				console.log('初始化获取数据成功');
			})
			.catch(() => {
				console.log('初始化获取数据失败');
			});
	}, []);
	/* 获取客户资质列表 */
	const getAptitudeList = async () => {
		const res = await pageAptitude({
			pageSize: 10000,
			pageNum: 1,
		});
		if (res.data) {
			setAptitudeList(res.data.records.map((ov) => ({ value: ov.id, label: ov.aptitudeName })));
		}
	};
	// 获取客户类型列表
	const getCustomTypeList = async () => {
		const res = await pageCategoryValue({ categoryCode: 'customer_type' });
		if (res.data) {
			const options = res.data.records?.map((item) => ({
				label: item.value,
				value: item.id,
			}));
			console.log('客户类型列表', options);
			setCustomTypeList(options);
		}
	};
	// 获取省市列表
	const getAreaList = async () => {
		const res = await getTwoLevelData({ level: 2 }, { showLoading: false });
		console.log('省市列表', res);
		if (res.data) {
			setAreaList(res.data);
		}
	};
	// 获取中心人员名单
	const getCenterStaffList = async () => {
		const res = await getByPermissionPerms({ perms: 'businessOppty' }, { showLoading: false });
		if (res) {
			console.log('中心人员名单', res.data);
			const list = res.data;
			// Step 1: 扁平化部门并去重
			const deptMap = new Map();
			const deptIdsSet = new Set();
			list.forEach((item) => {
				item.deptList.forEach((dept) => {
					if (!deptMap.has(dept.id)) {
						deptMap.set(dept.id, dept);
					}
					deptIdsSet.add(dept.id);
				});
			});
			const deptList = Array.from(deptMap.values());
			// Step 2: 构建人员选项，并按部门ID分组
			const staffOptions = [];
			const staffByDeptId = new Map();
			list.forEach((item) => {
				const staff = {
					label: item.userName,
					value: item.id,
					deptIds: item.deptList.map((d) => d.id),
				};
				staffOptions.push(staff);
				staff.deptIds.forEach((deptId) => {
					if (!staffByDeptId.has(deptId)) {
						staffByDeptId.set(deptId, []);
					}
					staffByDeptId.get(deptId).push(staff);
				});
			});
			// Step 3: 构建带 children 的部门结构
			const userList = deptList.map((dept) => ({
				label: dept.name,
				value: dept.id,
				children: staffByDeptId.get(dept.id) || [],
			}));
			console.log('userOptions', userList);
			setUserList(userList);
		}
	};
	// 文件上传回调
	const handleUpload = async (file) => {
		console.log('上传文件', file);
		message.success('上传成功');
		const res = await importCustomerData(file);
		if (res.data) {
			console.log('表格数据', res.data);
			message.success('导入成功，等待后台处理中...');
			// getTableData();
		} else {
			message.error('导入失败');
		}
	};
	// 查看详情
	const onView = (record, tabs) => {
		console.log('查看详情', record);
		linkTo(`/businessOppty/customerManage/customerDetail?id=${record.id}&seasPool=1${tabs || ''}`);
	};
	/* 分配 */
	const onDistribute = (record) => {
		distributeModalRef.current?.openModal([record.id]);
	};
	/* 认领 */
	const onClaim = (record) => {
		Modal.confirm({
			title: '认领客户',
			content: '确认认领客户吗？',
			okText: '认领',
			onOk: async () => {
				console.log('确认认领', record);
				const params = {
					customerIds: [record.id],
					allocationType: DistributeType.claim,
				};
				try {
					const res = await customerAllocation(params);
					if (res.data) {
						message.success('认领成功');
						getTableData();
						return Promise.resolve();
					} else {
						message.error('认领失败');
						return Promise.reject();
					}
				} catch (e) {
					console.log('认领客户失败', e);
					return Promise.reject(e);
				}
			},
		});
	};
	// 修改tabs
	const onChangeTabs = (key) => {
		setActiveKey(key);
		const params = {
			pageNum: 1,
			pageSize: 10,
		};
		const userDept = userInfo.deptList[0] || {};
		switch (key) {
			case 'customer':
				break;
			case 'myCustomer':
				params.ownerId = userInfo.id;
				break;
			case 'myDept':
				params.ownerDepartmentId = userDept.id;
				break;
		}
		form.resetFields();
		form.setFieldsValue(params);
		getTableData(params);
	};
	// 修改 选中的表格项
	const onSelectChange = (selectedRowKeys, selectedRows) => {
		setSelectedRowKeys(selectedRowKeys);
	};
	/* 批量分配 */
	const onBatchDistribute = async () => {
		console.log('批量分配客户', selectedRowKeys);
		distributeModalRef.current?.openModal(selectedRowKeys);
	};
	/* 批量认领 */
	const onBatchClaim = async () => {
		console.log('批量认领客户', selectedRowKeys);
		const params = {
			customerIds: selectedRowKeys,
			allocationType: DistributeType.claim,
		};
		const res = await customerAllocation(params);
		if (res.data) {
			message.success('批量认领成功');
			getTableData();
		}
	};
	return (
		<div className={'flex-sub flex flex-direction-column margin-20 padding-20  border-radius-4'}>
			<div className="padding-20 bg-color-ffffff border-radius-8 flex justify-between align-center line-height-24 margin-bottom-20">
				<div className={'font-size-16 flex flex-sub gap-40'}>
					{TabsList.slice(0, 1).map((item) => {
						return (
							<div key={item.key} className={'flex align-center'} onClick={() => onChangeTabs(item.key)}>
								<span
									className={`font-size-16 cursor-pointer font-weight-600 ${activeKey === item.key ? 'color-165dff' : 'color-1d2129'}`}
								>
									{item.title}
								</span>
							</div>
						);
					})}
				</div>
				<Space>
					<Button icon={<ImportOutlined />} onClick={importData}>
						导入
					</Button>
					<Button icon={<CloudDownloadOutlined />} onClick={exportData}>
						导出
					</Button>
					{/*<Button type={'primary'} icon={<PlusOutlined/>} onClick={addData}>新建</Button>*/}
				</Space>
			</div>
			<div className={'bg-color-ffffff padding-20 border-radius-8'}>
				<Form form={form} layout={'inline'} className={'width-100per flex flex-sub align-start'}>
					<Row gutter={[20, 20]} className={'flex-sub'}>
						<Col span={8}>
							<Form.Item label="客户名称" name="name" className={'flex-sub '}>
								<Input placeholder={'请输入客户名称'} className={'width-100per'} allowClear />
							</Form.Item>
						</Col>
						<Col span={8}>
							<Form.Item label="客户类型" name="typeId" className={'flex-sub '}>
								<Select options={CustomTypeList} className={'width-100per'} placeholder={'请选择客户类型'} allowClear />
							</Form.Item>
						</Col>
						<Col span={8}>
							<Form.Item label="所属区域" name="area" className={'flex-sub '}>
								<Cascader options={areaList} className={'width-100per'} placeholder={'请选择所属区域'} allowClear />
							</Form.Item>
						</Col>
						<Col span={8}>
							<Form.Item label="客户资质" name="aptitudeIds" className={'flex-sub '}>
								<Select
									placeholder={'请选择客户资质'}
									className={'width-100per'}
									options={aptitudeList}
									optionFilterProp={'label'}
									maxTagCount={'responsive'}
									showSearch
									allowClear
								/>
							</Form.Item>
						</Col>
					</Row>
					{/*<Divider type="vertical" style={{margin: '0 16px'}}/>*/}
					<div className="width-1 height-84 bg-color-e5e6eb flex-shirnk margin-lr-20" />
					<Form.Item noStyle>
						<Space direction={'vertical'} size={20}>
							<Button type={'primary'} htmlType={'submit'} icon={<SearchOutlined />} onClick={() => getTableData()}>
								查询
							</Button>
							<Button htmlType={'reset'} icon={<ReloadOutlined />} onClick={onReset}>
								重置
							</Button>
						</Space>
					</Form.Item>
				</Form>
				<Divider />
				<Space className={'margin-bottom-20'}>
					{distributeRole && (
						<Button type={'primary'} disabled={selectedRowKeys.length === 0} onClick={onBatchDistribute}>
							批量分配
						</Button>
					)}
					<Button disabled={selectedRowKeys.length === 0} type={'primary'} onClick={onBatchClaim}>
						批量认领
					</Button>
				</Space>
				<Table
					rowKey="id"
					columns={columns}
					rowSelection={{
						type: 'checkbox',
						selectedRowKeys: selectedRowKeys,
						onChange: onSelectChange,
					}}
					pagination={{
						...pagination,
						onChange: changePage,
						showSizeChanger: true,
						showTotal: (total) => `共 ${total} 条`,
					}}
					scroll={{ x: 'max-content' }} // 关键：让表格水平滚动
					style={{ overflowX: 'auto' }}
					dataSource={dataSource}
				/>
			</div>
			{/* 导入 开始 */}
			<ModalForm
				ref={ModalFormImportRef}
				modelConfig={{
					styles: {
						body: {
							minHeight: 'unset',
						},
					},
				}}
				onOk={handleUpload}
				FormComp={(props) => <ImportForm ref={props.FormCompRef} fileName="file" tplUrl={CustomerImportTemplateUrl} />}
			/>
			{/* 导入 结束 */}
			<DistributeModal ref={distributeModalRef} userList={userList} reload={getTableData} />
		</div>
	);
};
export default CustomerList;
