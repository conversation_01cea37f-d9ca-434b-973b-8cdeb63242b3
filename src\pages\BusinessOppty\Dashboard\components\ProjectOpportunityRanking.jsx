/* 全员商机排名 */
import React, { useState, useEffect } from 'react';
import {pageProjectOpportunityRanking} from "@/api/Opportunity/Dashboard";
import {Card, Table, Select, Progress} from "antd";

const pageSize = 10;
const sortOptions = [
    {
        key: "amount-desc",
        value: "amount-desc",
        label: "金额排名",
    },
    // {
    //     key: "amount-asc",
    //     value: "amount-asc",
    //     label: "金额由低到高",
    // },
    {
        key: "schedule-desc",
        value: "schedule-desc",
        label: "进度排名",
    },
    // {
    //     key: "schedule-asc",
    //     value: "schedule-asc",
    //     label: "商机进度由低到高",
    // },
    {
        key: "number-desc",
        value: "number-desc",
        label: "数量排名",
    },
    // {
    //     key: "number-asc",
    //     value: "number-asc",
    //     label: "数量由低到高",
    // },
    {
        key: "new-desc",
        value: "new-desc",
        label: "新增数排名",
    },
    // {
    //     key: "new-asc",
    //     value: "new-asc",
    //     label: "新增数量由低到高",
    // },
]
const ProjectOpportunityRanking = () => {
    const [loading, setLoading] = useState(false);
    const [total, setTotal] = useState(0);
    const [pageNum, setPageNum] = useState(1);
    const [sort, setSort] = useState({
        sortKey: "amount",
        sortValue: "desc",
    });
    const [data, setData] = useState([]);

    useEffect(() => {
        getProjectOpportunityRanking({
            pageNum,
            pageSize,
            ...sort,
        });
    }, []);
    /* 分页查询全员商机排名 */
    const getProjectOpportunityRanking = async (params = {}) => {
        setLoading(true);
        try {
            const res = await pageProjectOpportunityRanking(params);
            console.log(res.data)
            if (res.data) {
                setTotal(res.data.total);
                setData(res.data.records || []);
            }
        } catch (e) {
            console.error(e);
        } finally {
            setLoading(false);
        }
    };
    /* 修改页码 */
    const onChange = (pageNum, pageSize) => {
        setPageNum(pageNum);
        getProjectOpportunityRanking({
            pageNum,
            pageSize,
            ...sort,
        });
    };
    /* 修改排序 */
    const onSortChange = (resValue) => {
        const [key, value] = resValue.split('-');
        setSort({
            sortKey: key,
            sortValue: value,
        });
        getProjectOpportunityRanking({
            pageNum,
            pageSize,
            sortKey: key,
            sortValue: value,
        });
    };
    const columns = [
        {
            title: '姓名',
            dataIndex: 'userName',
            key: 'userName',
            align: 'center',
            width: 100,
        },
        {
            title: '所在部门',
            dataIndex: 'deptName',
            key: 'deptName',
            align: 'center',
        },
        {
            title: '数量（线索+商机）',
            dataIndex: 'totalOpportunities',
            key: 'totalOpportunities',
            align: 'center',
            width: 160,
        },
        {
            title: '本月新增',
            dataIndex: 'currentMonthOpportunities',
            key: 'currentMonthOpportunities',
            align: 'center',
            width: 160,
        },
        {
            title: '商机进度',
            dataIndex: 'completedOpportunities',
            key: 'completedOpportunities',
            align: 'center',
            width: 160,
            render: (completedOpportunities, record) => {
                const percent = completedOpportunities / record.totalOpportunities * 100;
                return <Progress
                    percent={percent}
                    size="small"
                    className='margin-0'
                    format={p => `${completedOpportunities}/${record.totalOpportunities}`}
                />
            },
        },
        {
            title: '预计收费/万元',
            dataIndex: 'expectCharge',
            key: 'expectCharge',
            align: 'center',
            width: 160,
        },
    ]
    return (
        <Card
            size={'small'}
            className={'margin-tb-20'}
            bodyStyle={{ padding: '0 20px' }}
            title={'全员商机排名情况'}
            extra={
                <Select
                    className={'width-120'}
                    size={'small'}
                    options={sortOptions}
                    onChange={onSortChange}
                    value={`${sort.sortKey}-${sort.sortValue}`}
                />
            }
        >
           <Table
               size={'small'}
               columns={columns}
               dataSource={data}
               // loading={loading}
               rowKey={'id'}
               className={'margin-top-20'}
               pagination={{
                   total: total,
                   current: pageNum,
                   pageSize: pageSize,
                   onChange: onChange,
                   showTotal: (total) => `共 ${total} 条数据`,
               }}
           />
        </Card>
    )
}
export default ProjectOpportunityRanking;