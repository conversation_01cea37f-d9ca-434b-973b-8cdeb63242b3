import {useEffect, useState} from 'react';
import {Card, Col, Progress, Row, Select, Space, Tag} from 'antd';
import {getImageSrc} from '@/assets/images/index';
import {RightOutlined} from '@ant-design/icons';

import Pie<PERSON>hart from '@/components/PieChart';
import ChartDualAxes from '@/components/ChartDualAxes';

import {useRouterLink} from '@/hook/useRouter';

import {indexStatistics} from '@/api/Opportunity/Dashboard/index';
import {myProjectFollowPage, notificationPage,} from '@/api/Opportunity/PersonalCenter/index';
import {listOpportunity, pageOpportunity} from '@/api/Opportunity/OpportunityManage/index';
import {pageCategoryValue} from '@/api/Bidmgt/Dict/index';
import {useSelector} from 'react-redux';
import ProjectOpportunityRanking from './components/ProjectOpportunityRanking'

import SvgIcon from '@/assets/icons';
import {useSearchParams} from 'react-router-dom';
import {
	keyNodeOpportunityStatisticsConfig,
	opportunityScheduleFilterOptions
} from "@/pages/BusinessOppty/Dashboard/const";
import moment from "moment";

/* 商机自评等级只展示限定类别 */
const nameRemakeList = [
	'A级',
	'B级',
	'C级',
	'D级',
];
function getCategory(nameRemarks) {
	// 根据nameRemarks返回相应的类别'A', 'B', 'C', 'D'
	// 这里只是一个示例，你需要根据实际情况来实现
	if (nameRemarks.startsWith('A')) return 'A';
	if (nameRemarks.startsWith('B')) return 'B';
	if (nameRemarks.startsWith('C')) return 'C';
	if (nameRemarks.startsWith('D')) return 'D';
	if (nameRemarks.startsWith('E')) return 'E';
	return 'Z'; // 如果没有匹配的类别，可以设置一个默认值
}
function compareCategories(categoryA, categoryB) {
	// 比较两个类别并返回适当的数值
	if (categoryA < categoryB) return -1;
	if (categoryA > categoryB) return 1;
	return 0;
}
const Index = () => {
	// 用户信息
	const userInfo = useSelector((state) => {
		return state.user.userInfo;
	});
	const deptIds = (userInfo.deptList || []).map((ov) => ov.id);
	const isManager = (userInfo.roleList || []).some(
		(ov) => ov.roleCode == 'manager'
	);
	const curUserId = userInfo.id || '';

	const [urlSearch, setUrlSearch] = useSearchParams();
	const urlChange = (params = {}) => {
		setUrlSearch(params, { replace: true });
	};

	const { linkTo } = useRouterLink();

	const [statistics, setStatistics] = useState({});
	const [projectFollows, setProjectFollows] = useState([]);

	const [projectCountList, setProjectCountList] = useState([]);
	const [expectChargeList, setExpectChargeList] = useState([]);

	const [notificeList, setNotificeList] = useState([]);
	const [notificeTotal, setNotificeTotal] = useState(0);

	const [projectList, setProjectList] = useState([]);

	const [deptStatistics, setDeptStatistics] = useState([]);

	// 部门列表 进度
	const [deptList, setDeptList] = useState([]);
	// 商机阶段 字典
	const [opportunityStageList, setOpportunityStageList] = useState([]);
	// 商机类型 字典
	const [opportunityTypeList, setOpportunityTypeList] = useState([]);
	/* 统计最新商机 */
	const [lastProjectList, setLastProjectList] = useState([]);
	/* 默认商机排序 */
	const [sortWay, setSortWay] = useState('opportunitySchedule');

	useEffect(() => {
		pageCategoryValue({
			categoryCode: 'opportunity_type',
			pageNum: 1,
			pageSize: 100,
		}).then((res) => {
			setOpportunityTypeList(res.data.records || []);
		});
		getLastProjectList();
	}, []);

	/* 获取最新商机 */
	const getLastProjectList = async (deptId = '') => {
		const res = await pageOpportunity({
			pageNum: 1,
			pageSize: 5,
			sortKey: "createTime",
			sortValue: "desc",
			sortWay: "createTime-desc",
			projectHandlingDeptId: deptId || curDept,
			suspendStatus: 0
		});
		if (res.data) {
			setLastProjectList(res.data.records || []);
		}
	};

	const getIndexStatistics = (params = {}) => {
		indexStatistics(params).then((res) => {
			setStatistics(res.data || {});
			setProjectCountList(
				(res.data.deptList || []).map((ov) => {
					const deptNameList = `${ov.projectHandlingDeptName || ''}`
						.split('')
						.reduce((pre, cur) => {
							const lastItem = pre.length
								? pre[pre.length - 1]
								: '';
							if (lastItem.length < 3) {
								return [
									...pre.slice(0, pre.length - 1),
									lastItem + cur,
								];
							} else {
								return [...pre, cur];
							}
						}, []);
					return {
						projectCount: ov.projectCount || 0,
						// expectCharge: ov.expectCharge || 0,
						projectHandlingDeptName: deptNameList.join('\n'),
						projectHandlingDeptId: ov.projectHandlingDeptId || '',
					};
				})
			);
			setExpectChargeList(
				(res.data.deptList || []).map((ov) => {
					const deptNameList = `${ov.projectHandlingDeptName || ''}`
						.split('')
						.reduce((pre, cur) => {
							const lastItem = pre.length
								? pre[pre.length - 1]
								: '';
							if (lastItem.length < 3) {
								return [
									...pre.slice(0, pre.length - 1),
									lastItem + cur,
								];
							} else {
								return [...pre, cur];
							}
						}, []);
					return {
						// projectCount: ov.projectCount || 0,
						expectCharge: ov.expectCharge || 0,
						projectHandlingDeptName: deptNameList.join('\n'),
						projectHandlingDeptId: ov.projectHandlingDeptId || '',
					};
				})
			);
		});
	};

	// 获取 进度阶段 字典
	const getStageList = () => {
		return new Promise((resolve) => {
			if (opportunityStageList.length) {
				resolve(opportunityStageList);
			} else {
				pageCategoryValue({
					categoryCode: 'opportunity_stage',
					pageNum: 1,
					pageSize: 200,
				}).then((reb) => {
					const stepsList = reb.data.records || [];
					setOpportunityStageList(stepsList);
					resolve(stepsList);
				});
			}
		});
	};
	// 获取详情
	const getDAta = (params) => {
		getIndexStatistics(params);

		notificationPage({
			pageNum: 1,
			pageSize: 10,
			readFlag: 0,
		}).then((res) => {
			setNotificeList(res.data.records || []);
			setNotificeTotal(res.data.total || []);
		});
		pageOpportunity({
			pageNum: 1,
			pageSize: 20,
			...params,
			isFilterData: params.projectHandlingDeptId ? 0 : 1, // 是否读取当前用户所归宿部门进行过滤 0 不过部门数据  1 过滤部
		}).then((res) => {
			setProjectList(
				[
					...(res.data.records || []).filter(
						(ov) => ov.latestDevelopments
					),
				].slice(0, 5)
			);
		});

		getStageList().then((stepsList) => {
			myProjectFollowPage({
				pageNum: 1,
				pageSize: 5,
			}).then((res) => {
				setProjectFollows(
					(res.data.records || []).map((ov) => {
						const findex = stepsList.findIndex(
							(item) => ov.projectStageId == item.id
						);
						if (findex >= 0) {
							// ov.percent = parseInt(
							// 	(findex * 100) / stepsList.length
							// )
							ov.percent = stepsList[findex].remarks - 0;
						} else {
							ov.percent = 0;
						}
						return { ...ov };
					})
				);
			});
		});
	};
	const urlCurDept = urlSearch.get('curDept') || ''

	const [curDept, setCurDept] = useState(urlCurDept);
	const [curPageOpportunity, setCurPageOpportunity] = useState([]);
	// 部门切换
	const cascaderChange = (value) => {
		if (value) {
			setCurDept(value);
			getPageOpportunity(value);
			getLastProjectList(value);
		} else {
			setCurDept('');
			setCurPageOpportunity([]);
			setLastProjectList([]);
		}
	};
	// 部门统计
	// 获取 部门商机列表
	const getPageOpportunity = (projectHandlingDeptId = '') => {
		getStageList().then((stepsList) => {
			listOpportunity({
				pageNum: 1,
				pageSize: 2000,
				projectHandlingDeptId,
				isFilterData: projectHandlingDeptId ? 0 : 1, // 是否读取当前用户所归宿部门进行过滤 0 不过部门数据  1 过滤部门数据
			}).then((res) => {
				const list = res.data || [];
				/* 当前时间 */
				const nowTime = moment();
				list.forEach((ov) => {
					const findex = stepsList.findIndex(
						(item) => ov.projectStageId == item.id
					);
					if (findex >= 0) {
						// ov.percent = parseInt((findex * 100) / stepsList.length)
						ov.percent = stepsList[findex].remarks - 0;
					} else {
						ov.percent = 0;
					}
					/* 创建时间 */
					const createTime = moment(ov.createTime);
					// 检查 createTime 是否在过去七天内
					ov.isWithinSevenDays = nowTime.diff(createTime, 'days') < 7;
					ov.isThisMonthCreate = createTime.isSame(
						nowTime,
						'month'
					);
					ov.isThisWeekCreate = createTime.isSame(
						nowTime,
						'week'
					);
				});

				list.sort((a, b) => {
					return b.percent - a.percent;
				}).sort((a, b) => {
					return b.topStatus - a.topStatus;
				});

				setCurPageOpportunity(list);
			});
		});
	};

	/* 排序商机进度 */
	const sortOpportunity = (sortWay, lst) => {
		const list = lst || [...deptStatistics];
		list.sort((a, b) => {
			const valueA = +`${a[sortWay]}`.replace(/%/g, '') || 0;
			const valueB = +`${b[sortWay]}`.replace(/%/g, '') || 0;
			return valueB - valueA;
		});
		setSortWay(sortWay);
		setDeptStatistics(list);
	};
	useEffect(() => {
		const params = {};
		if (!deptStatistics.length) {
			// 这里只是获取 部门进度
			indexStatistics().then((res) => {
				const deptList = res.data.deptList.filter((ov) => ov.projectHandlingDeptId) || [];
				sortOpportunity(sortWay, deptList)
			});
			// 首次打开页面 如果 这里有数据就请求
			if (curDept) {
				getPageOpportunity(curDept || '');
			}
		}
		if (curDept) {
			params.projectHandlingDeptId = curDept || '';
		}
		urlChange({
			curDept,
		});

		getDAta(params);
	}, [curDept]);
	return (
		<div className='flex-sub flex padding-lr-20 padding-tb-10'>
			<div className='flex-sub'>
				<div className='margin-bottom-12 line-height-30'>
					<Space>
						<SvgIcon
							style={{ fontSize: '16px' }}
							type='icon-dashboard'
						/>

						<div
							className='a'
							onClick={() => {
								cascaderChange();
							}}
						>
							公司总览
						</div>
						<div>/</div>
						<div>
							<Select
								value={curDept}
								allowClear
								showSearch
								className='width-260'
								placeholder='全部'
								options={(isManager
									? statistics.deptList || []
									: userInfo.deptList || []
								).map((ov) => {
									return {
										label:
											ov.projectHandlingDeptName ||
											ov.name ||
											'',
										value:
											ov.projectHandlingDeptId ||
											ov.id ||
											'',
									};
								})}
								filterOption={(input, option) =>
									(option?.label ?? '').includes(input)
								}
								onChange={cascaderChange}
							/>
							{/* <Cascader
								value={curDept}
								className='width-260'
								options={
									(isManager ? (statistics.deptList || []) : (userInfo.deptList || [])).map(
										(ov) => {
											return {
												label:
													ov.projectHandlingDeptName || ov.name ||
													'',
												value:
													ov.projectHandlingDeptId || ov.id || '',
											}
										}
									)}
								placeholder='全部'
								maxTagCount='responsive'
								changeOnSelect
								expandTrigger='hover'
								onChange={cascaderChange}
							/> */}
						</div>
					</Space>
				</div>
				{/* 顶部 统计 开始 */}
				<Row gutter={18} className='color-1d2129'>
					<Col span={8}>
						<Card
							size='small'
							onClick={() => {
								const item =
									opportunityTypeList.find((ov) =>
										ov.value.includes('线索')
									) || {};
								// sessionStorage.setItem(
								// 	'/businessOppty/opptyManage/list',
								// 	JSON.stringify({
								// 		projectTypeId:
								// 			item.id,
								// 	})
								// )
								linkTo(
									`/businessOppty/clueManage/clueList?deptId=${curDept}`
								);
							}}
							className='a'
							title={
								<div className='flex align-center justify-start height-44'>
									<div className='font-size-16 font-weight-500'>
										线索数量
									</div>
									<div className='font-size-14 font-weight-400 color-86909c'>
										（个）
									</div>
								</div>
							}
						>
							<div className='flex align-end justify-between'>
								<div className='font-size-32 line-height-40 font-bold flex-sub'>
									{statistics.clueCount || '0'}
								</div>
								<div className='width-54 height-54'>
									<img
										src={getImageSrc(
											'@/assets/images/Dashboard/icon-project-totals.png'
										)}
										className='width-54 height-54'
									/>
								</div>
							</div>
						</Card>
					</Col>
					<Col span={8}>
						<Card
							size='small'
							onClick={() => {
								const item =
									opportunityTypeList.find((ov) =>
										ov.value.includes('商机')
									) || {};
								// sessionStorage.setItem(
								// 	'/businessOppty/opptyManage/list',
								// 	JSON.stringify({
								// 		projectTypeId:
								// 			item.id,
								// 	})
								// )
								linkTo(
									`/businessOppty/opptyManage/list?projectHandlingDeptId=${curDept}`
								);
							}}
							className='a'
							title={
								<div className='flex align-center justify-start height-44'>
									<div className='font-size-16 font-weight-500'>
										商机数量
									</div>
									<div className='font-size-14 font-weight-400 color-86909c'>
										（个）
									</div>
								</div>
							}
						>
							<div className='flex align-end justify-between'>
								<div className='font-size-32 line-height-40 font-bold flex-sub'>
									{statistics.opportunityCount || '0'}
								</div>
								<div className='width-54 height-54'>
									<img
										src={getImageSrc(
											'@/assets/images/Dashboard/icon-sign-totals.png'
										)}
										className='width-54 height-54'
									/>
								</div>
							</div>
						</Card>
					</Col>
					<Col span={8}>
						<Card
							size='small'
							onClick={() =>
								linkTo(
									`/businessOppty/opptyManage/list?projectHandlingDeptId=${curDept}`
								)
							}
							className='a'
							title={
								<div className='flex align-center justify-start height-44'>
									<div className='font-size-16 font-weight-500'>
										预计收费总额
									</div>
									<div className='font-size-14 font-weight-400 color-86909c'>
										（万元）
									</div>
								</div>
							}
						>
							<div className='flex align-end justify-between'>
								<div className='font-size-32 line-height-40 font-bold flex-sub'>
									{statistics.expectCharge || '0'}
								</div>
								<div className='width-54 height-54'>
									<img
										src={getImageSrc(
											'@/assets/images/Dashboard/icon-investment.png'
										)}
										className='width-54 height-54'
									/>
								</div>
							</div>
						</Card>
					</Col>
				</Row>
				{/* 顶部 统计 结束 */}
				{/* 关键节点商机统计 */}
				<Card
					size={'small'}
					className={'margin-top-20 position-relative'}
					title={
						<div className='flex align-center justify-start height-44'>
							<div className='font-size-16 font-weight-500'>
								关键节点商机统计
							</div>
							<div className='font-size-14 font-weight-400 color-86909c'>
								（单位：个、万元）
							</div>
						</div>
					}
				>
					<Row justify="space-between " gutter={18} >
						{
							keyNodeOpportunityStatisticsConfig.map((ov, index) => {
								const expectCharge = statistics?.stageList?.reduce((pre, cur) => {
									if (ov.opportunitySchedule.includes(cur.name)) {
										return pre + cur.expectCharge;
									}
									return pre;
								}, 0) || 0;
								const projectCount = statistics?.stageList?.reduce((pre, cur) => {
									if (ov.opportunitySchedule.includes(cur.name)) {
										return pre + cur.opportunityCount;
									}
									return pre;
								}, 0) || 0;
								return <Col span={8} key={index}>
									<Card size={'small'} hoverable onClick={() => linkTo(`/businessOppty/opptyManage/list?projectStageId=${ov.projectStageId}`)}>
										<div className='font-bold font-size-16 line-height-24'>
											{ov.title}
										</div>
										<div className='flex align-center justify-start margin-tb-8'>
											<span className={'color-86909c'}>预计收费：</span>
											<span className={'font-size-18 color-ff7d00 font-bold margin-right-4'}>
												￥{(expectCharge).toFixed(2)}
											</span>
											<span className='color-86909c font-size-14 margin-left-4'>
												万元
											</span>
										</div>
										<div className={'flex align-center justify-start font-size-16 line-height-24'}>
											<span className={'color-86909c'}>商机数：</span>
											<span className={'margin-lr-4 font-bold'}>
												{projectCount}
											</span>
											<span className='color-86909c font-size-14 margin-left-4'>
												个
											</span>
										</div>
									</Card>
								</Col>
							})
						}
					</Row>
				</Card>
				{/* 各部门商机情况 开始 */}
				<Card
					size='small'
					bodyStyle={{ padding: '0' }}
					className='height-354 margin-top-20 position-relative'
				>
					<div
						className={`flex align-center justify-start height-44 position-absolute top-0 left-20 z-index-10 ${curDept ? 'bg-color-ffffff right-0' : ''
							}`}
					>
						<div className='font-size-16 font-weight-500'>
							{curDept ? '当前部门商机情况' : '各部门商机情况'}
						</div>
					</div>
					{curDept ? (
						<div className='height-350 border-box padding-top-46 overflow-hidden overflowY-auto'>
							{curPageOpportunity.filter(ov => { return `${ov.percent || ''}` != '100' }).map((ov, oi) => {
								return (
									<div
										key={ov.id}
										className={`a  padding-lr-20 margin-bottom-12`}
										onClick={() => {
											linkTo(
												`/businessOppty/opptyManage/detail?id=${ov.id}`
											);
										}}
									>
										<div className='flex align-center justify-between margin-bottom-4'>
											{/*{ov.topStatus === 1 && (*/}
											{/*	<div className='margin-right-4 padding-lr-4 line-height-22 font-size-12 border-solid-f76560 color-f76560 border-radius-4 bg-color-ebd5d4 border-box'>*/}
											{/*		置顶*/}
											{/*	</div>*/}
											{/*)}*/}
											{
												ov.topStatus === 1 &&
												<Tag color={'red'}>置顶</Tag>
											}
											{
												ov.isThisWeekCreate &&
												<Tag color={'green'}>
													7天内更新
												</Tag>
											}
											{
												ov.isWithinSevenDays &&
												<Tag color={'volcano'}>
													本周新增
												</Tag>
											}
											{
												ov.isThisMonthCreate &&
													<Tag color={'orange'}>
														本月新增
													</Tag>
											}
											<div className='flex-sub font-size-14 font-weight-500 line-height-22 text-cut'>
												{ov.projectOpportunityName ||
													''}
											</div>
											<div className='font-size-14 line-height-22 color-86909c flex-shrink'>
												{ov.projectStageName || ''}
											</div>
										</div>
										<Progress
											size={['100%', 16]}
											percent={
												`${ov.percent || ''}`.replace(
													'%',
													''
												) - 0
											}
											className='margin-0'
											showInfo={true}
										/>
									</div>
								);
							})}
						</div>
					) : (
						<ChartDualAxes
							className='height-330 padding-tb-10 padding-lr-20'
							config={{
								data: [projectCountList, expectChargeList],
								xField: 'projectHandlingDeptName',
								yField: ['projectCount', 'expectCharge'],
								// limitInPlot: false,
								padding: [20, 30, 28, 30],
								// 需要设置底部 padding 值，同 css
								// slider: {},
								meta: {
									projectCount: {
										alias: '商机数量',
										formatter: (val) => `${val} 个`,
									},
									expectCharge: {
										alias: '预计收费',
										formatter: (val) => `${val} 万`,
									},
								},
								geometryOptions: [
									{
										geometry: 'line',
										smooth: true,
										lineStyle: {
											lineWidth: 2,
										},
										point: {},
									},
									{
										geometry: 'column',
										smooth: true,
										lineStyle: {
											lineWidth: 2,
										},
										point: {},
										minColumnWidth: 20,
										maxColumnWidth: 20,
									},
								],
								yAxis: {
									count: {
										// min: 0,
										label: {},
										grid: {
											line: {
												style: {
													lineDash: [4, 5],
												},
											},
										},
									},
									value: {
										// min: 0,
										label: {},
									},
								},
								legend: {
									layout: 'horizontal',
									position: 'top-right',
								},
								title: '各部门商机情况',
							}}
						/>
					)}
				</Card>

				{/* 各部门商机情况 结束 */}
				{/* 底部 统计 开始 */}
				{/* 客户类型分布 开始 */}
				<Card
					size='small'
					className={'margin-top-20'}
					bodyStyle={{
						display: 'flex',
						flexDirection: 'column',
						padding: 0,
						height: '284px',
					}}
				>
					<div className='padding-lr-20 height-44 line-height-44 font-size-16 font-weight-500'>
						客户类型分布
					</div>
					<Row gutter={18} className='color-1d2129  margin-top-20'>
						<Col span={12} className={'margin-bottom-20 position-relative height-226'}>
							<PieChart
								id='pie-container-opportunityCount'
								// y={'expectCharge'}
								text={'数量'}
								// fill={'#1c8dfe'}
								count={(statistics.customList || []).reduce((a, b) => {
									return a + b.opportunityCount;
								}, 0)}
								data={(statistics.customList || []).filter(
									(ov) => ov.nameRemarks && ov.id
								)}
							/>
						</Col>
						<Col span={12} className={'margin-bottom-20 position-relative height-226'}>
							<PieChart
								id='pie-container-p'
								y={'expectCharge'}
								text={'金额'}
								unit={'万'}
								// fill={'#ff9535'}
								count={(statistics.customList || []).reduce((a, b) => {
									return a + b.expectCharge;
								}, 0)}
								data={(statistics.customList || []).filter(
									(ov) => ov.nameRemarks && ov.id
								)}
							/>
						</Col>
					</Row>
				</Card>
				<Row gutter={18} className='color-1d2129  margin-top-20'>
					<Col span={12} className={'margin-bottom-20'}>
						{/* 商机自评等级分布 开始 */}
						<Card
							size='small'
							bodyStyle={{
								display: 'flex',
								flexDirection: 'column',
								padding: '0',
								height: '264px',
							}}
						>
							<div className='padding-lr-20 height-44 line-height-44 font-size-16 font-weight-500'>
								商机自评等级分布
							</div>
							<div className='position-relative height-226'>
								<PieChart
									id='pie-container-2'
									count={(
										statistics.reliabilityList || []
									).reduce((a, b) => {
										return a + b.opportunityCount;
									}, 0)}
									text={'数量'}
									data={(statistics.reliabilityList || [])
										.filter((ov) =>
											ov.nameRemarks && nameRemakeList.includes(ov.nameRemarks)
										)
										.sort((a, b) => {
											const categoryA = getCategory(a.nameRemarks);
											const categoryB = getCategory(b.nameRemarks);
											return compareCategories(categoryA, categoryB);
										})
									}
								/>
							</div>
						</Card>
						{/* 商机自评等级分布 结束 */}
					</Col>
					<Col span={12} className={'margin-bottom-20'}>
						{/* 商机系统测评分数分布 开始 */}
						<Card
							size='small'
							bodyStyle={{
								display: 'flex',
								flexDirection: 'column',
								padding: '0',
								height: '264px',
							}}
						>
							<div className='padding-lr-20 height-44 line-height-44 font-size-16 font-weight-500'>
								商机系统测评分数分布
							</div>
							<div className='position-relative height-226'>
								<PieChart
									id='pie-container-assessScore'
									name={'scoreRange'}
									nameUnit={'分'}
									unit={'个'}
									text={'数量'}
									count={(
										statistics.assessScoreList || []
									).reduce((a, b) => {
										return a + b.opportunityCount;
									}, 0)}
									data={(statistics.assessScoreList || []).filter(
										(ov) => (ov.nameRemarks || ov.scoreRange)
									)}
								/>
							</div>
						</Card>
						{/* 商机系统测评分数分布 结束 */}
					</Col>
				</Row>
				{/* 底部 统计 结束 */}
				{/* 底部 统计 开始 */}
				<Row gutter={18} className='color-1d2129 '>
					<Col span={24}>
						{/* 近半年新增商机 开始 */}
						<Card size='small' bodyStyle={{ padding: '0 20px' }}>
							<div className='flex align-center justify-between height-44'>
								<div className='font-size-16 font-weight-500'>
									新增商机
								</div>
								<div
									className='flex align-center justify-end font-size-12 color-86909c a'
									onClick={() => {
										linkTo('/businessOppty/opptyManage/list?sortWay=createTime-desc');
									}}
								>
									<div>查看全部</div>
									<RightOutlined className='font-size-12' />
								</div>
							</div>
							{lastProjectList.length > 0 &&
							lastProjectList.map((ov, oi) => {
								return (
									<div
										key={ov.id}
										className={`padding-8 border-radius-4 a ${
											oi > 0 ? 'margin-top-12' : ''
										}`}
										style={{
											background:
												'linear-gradient(180deg, #EBF5FF 0%, #FFFFFF 100%)',
										}}
										onClick={() => {
											linkTo(
												`/businessOppty/opptyManage/detail?id=${ov.id}`
											);
										}}
									>
										<div className='flex align-center justify-start'>
											<div className='width-6 height-6 border-radius-4 bg-color-165dff flex-shrink'/>
											<div className='font-size-14 font-weight-500 line-height-22 text-cut padding-left-4'>
												{ov.projectOpportunityName || ''}
											</div>
										</div>
										<div className='flex align-center justify-start font-size-12 line-height-20 padding-left-10 margin-tb-4 color-86909c'>
											商机责任部门：
											{ov.projectHandlingDeptName ||
											''}
										</div>
										<div className='flex align-center justify-start font-size-12 line-height-20 padding-left-10 color-86909c text-cut'>
											创建时间：
											{ov.createTime || ''}
										</div>
									</div>
								);
							})}
							{!lastProjectList.length && (
								<div className='text-align-center line-height-70 color-aaaaaa'>
									~暂无新增商机数据~
								</div>
							)}
						</Card>
						{/* 近半年新增商机 结束 */}
					</Col>
				</Row>
				{/* 全员商机排名 */}
				<ProjectOpportunityRanking />
			</div>
			<div
				className='width-280 flex-shrink margin-left-18 '
				// style={{ maxHeight: `calc(100vh - 86px)` }}
			>
				{/* 待办事项 开始 */}
				<Card size='small' bodyStyle={{ padding: '0 20px' }}>
					<div className='flex align-center justify-start height-44'>
						<div className='font-size-16 font-weight-500'>
							待办事项
						</div>
						<div className='font-size-14 font-weight-400 color-86909c'>
							（{notificeTotal || '0'}）
						</div>
						<div className='flex-sub'></div>
						<div
							className='flex align-center justify-end font-size-12 color-86909c a'
							onClick={() => {
								linkTo(`/businessOppty/personalCenter`);
							}}
						>
							<div>查看全部</div>
							<RightOutlined className='font-size-12' />
						</div>
					</div>
					{[...notificeList].map((ov, oi) => {
						return (
							<div
								key={ov.id}
								className={`flex align-center justify-start padding-4 border-radius-4 bg-color-f7f8fa a ${oi > 0 ? 'margin-top-12' : ''
									}`}
								onClick={() => {
									linkTo(`/businessOppty/personalCenter`);
								}}
							>
								<div className='flex align-center'>
									{ov.noticeType == '1' && (
										<img
											src={getImageSrc(
												'@/assets/images/Dashboard/icon-early-warning.png'
											)}
											className='width-32 height-32'
										/>
									)}
									{ov.noticeType == '16' && (<Tag color='processing'>线索</Tag>)}
									{ov.noticeType == '17' && (<Tag color='processing'>客户认领</Tag>)}
									{ov.noticeType == '2' && (
										<img
											src={getImageSrc(
												'@/assets/images/Dashboard/icon-urging.png'
											)}
											className='width-32 height-32'
										/>
									)}
								</div>
								<div className='font-size-12 line-height-20 flex-sub margin-left-4 padding-right-22'>
									{ov.noticeTitle || ''}
								</div>
							</div>
						);
					})}

					{!notificeList.length && (
						<div className='text-align-center line-height-40 color-aaaaaa'>
							暂无待办事项
						</div>
					)}
					<div className='padding-10'></div>
				</Card>
				{/* 待办事项 结束 */}

				{/* 商机追踪 开始 */}
				<Card
					size='small'
					bodyStyle={{ padding: '0 20px' }}
					className='margin-tb-16'
				>
					<div className='flex align-center justify-between height-44'>
						<div className='font-size-16 font-weight-500'>
							商机追踪
						</div>
						<div
							className='flex align-center justify-end font-size-12 color-86909c a'
							onClick={() => {
								linkTo(`/businessOppty/personalCenter`);
							}}
						>
							<div>查看全部</div>
							<RightOutlined className='font-size-12' />
						</div>
					</div>
					{projectFollows.map((ov, oi) => {
						return (
							<div
								key={ov.id}
								className={`padding-8 border-radius-4 bg-color-f6f9ff a ${oi > 0 ? 'margin-top-12' : ''
									}`}
								onClick={() => {
									linkTo(
										`/businessOppty/opptyManage/detail?id=${ov.id}`
									);
								}}
							>
								<div className='flex align-center justify-start'>
									<div className='width-4 height-12 bg-color-165dff flex-shrink border-radius-2'></div>
									<div className='font-size-14 font-weight-500 line-height-22 text-cut padding-left-4'>
										{ov.projectOpportunityName || ''}
									</div>
								</div>
								<div className='flex align-center justify-start font-size-14 line-height-22 padding-left-10 margin-tb-4 color-86909c'>
									<div className='text-cut flex-shrink'>
										{ov.expectCharge || '0'}万元
									</div>
									<div className='padding-left-12 text-cut'>
										{ov.projectTypeName || ''}
									</div>
									<div className='padding-left-12 text-cut'>
										{ov.projectHandlingDeptName || ''}
									</div>
								</div>
								<div className='flex align-center justify-start font-size-14 color-86909c'>
									<Progress
										percent={ov.percent}
										className='margin-0'
									/>
									{/* <div className='margin-left-4 flex-shrink padding-top-4'>
										开工
									</div> */}
								</div>
							</div>
						);
					})}
					{!projectFollows.length && (
						<div className='text-align-center line-height-40 color-aaaaaa'>
							暂无商机追踪
						</div>
					)}
					<div className='padding-10'></div>
				</Card>
				{/* 商机追踪 结束 */}

				{/* 各单位商机进度 开始 */}
				<Card
					size='small'
					bodyStyle={{ padding: '0 20px' }}
					className='margin-tb-16'
				>
					<div className='flex align-center justify-between height-44'>
						<div className='font-size-16 font-weight-500'>
							商机进度
						</div>
						<div
							className='flex align-center justify-end font-size-12 color-86909c a'
							// onClick={() => {
							// 	cascaderChange();
							// }}
						>
							{/*<div>查看全部</div>*/}
							{/*<RightOutlined className='font-size-12' />*/}
							<Select
								size={'small'}
								className={'width-120'}
								options={opportunityScheduleFilterOptions}
								onChange={value => sortOpportunity(value)}
								value={sortWay}
							/>
						</div>
					</div>
					{(deptStatistics || [])
						.map((ov, oi) => {
							return (
								<div
									key={ov.projectHandlingDeptId}
									className={`${oi > 0 ? 'margin-top-12' : ''
										} ${(deptIds.includes(
											ov.projectHandlingDeptId
										) ||
											isManager) &&
										'a'
										}`}
									onClick={() => {
										if (
											deptIds.includes(
												ov.projectHandlingDeptId
											) ||
											isManager
										) {
											cascaderChange(
												ov.projectHandlingDeptId
											);
										}
									}}
								>
									<div
										className={`flex align-center justify-between`}
									>
										<div className='font-size-14 font-weight-500 line-height-22 text-cut'>
											{ov.projectHandlingDeptName || ''}
										</div>
										<div
											className={`font-size-14 line-height-22  flex-shrink ${deptIds.includes(
												ov.projectHandlingDeptId
											) || isManager
													? 'color-165dff'
													: 'color-86909c'
												}`}
										>
											{ov.opportunityCompleteCount || '0'}
											/{ov.projectCount || '0'}
										</div>
									</div>
									<Progress
										percent={
											`${ov.opportunitySchedule || ''
												}`.replace('%', '') - 0
										}
										className='margin-0'
										showInfo={false}
									/>
								</div>
							);
						})}

					{![...(deptStatistics || [])].length && (
						<div className='text-align-center line-height-40 color-aaaaaa'>
							暂无数据
						</div>
					)}
					<div className='padding-10'></div>
				</Card>

				{/* 各单位商机进度 结束 */}

				{/* 最新动态 开始 */}
				<Card size='small' bodyStyle={{ padding: '0 20px' }}>
					<div className='flex align-center justify-between height-44'>
						<div className='font-size-16 font-weight-500'>
							最新动态
						</div>
						<div
							className='flex align-center justify-end font-size-12 color-86909c a'
							onClick={() => {
								linkTo('/businessOppty/opptyManage/list');
							}}
						>
							<div>查看全部</div>
							<RightOutlined className='font-size-12' />
						</div>
					</div>

					{projectList.slice(0, 5).map((ov, oi) => {
						return (
							<div
								key={ov.id}
								className={`padding-8 border-radius-4 a ${oi > 0 ? 'margin-top-12' : ''
									}`}
								style={{
									background:
										'linear-gradient(180deg, #EBF5FF 0%, #FFFFFF 100%)',
								}}
								onClick={() => {
									linkTo(
										`/businessOppty/opptyManage/detail?id=${ov.id}`
									);
								}}
							>
								<div className='flex align-center justify-start'>
									<div className='width-6 height-6 border-radius-4 bg-color-165dff flex-shrink'></div>
									<div className='font-size-14 font-weight-500 line-height-22 text-cut padding-left-4'>
										{ov.projectOpportunityName || ''}
									</div>
								</div>
								<div className='flex align-center justify-start font-size-12 line-height-20 padding-left-10 margin-tb-4 color-86909c'>
									商机负责部门：
									{ov.projectHandlingDeptName || ''}
								</div>
								<div className='flex align-center justify-start font-size-12 line-height-20 padding-left-10 color-86909c text-cut'>
									商机动态：{ov.latestDevelopments || ''}
								</div>
							</div>
						);
					})}
					{!projectList.length && (
						<div className='text-align-center line-height-40 color-aaaaaa'>
							暂无单位
						</div>
					)}
					<div className='padding-10'></div>
				</Card>
				{/* 最新动态 结束 */}
			</div>
		</div>
	);
};

export default Index;
