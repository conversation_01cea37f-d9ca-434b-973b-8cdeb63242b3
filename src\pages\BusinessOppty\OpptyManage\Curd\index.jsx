import { Col, Card, Row, Progress, Select, DatePicker, Table, Button, Checkbox, Form, Input, Cascader, message, InputNumber, Space } from 'antd';
import Breadcrumb from '@/components/Breadcrumb';
import { useRouterLink } from '@/hook/useRouter';
import React, { useEffect, useState } from 'react';
// import { getDeptData } from '@/utils/dictionary';
import { pageCategoryValue } from '@/api/Bidmgt/Dict/index';
import { addOpportunity, detailOpportunity, updateOpportunity } from '@/api/Opportunity/OpportunityManage/index';
import { useSelector } from 'react-redux';
import { getByPermissionPerms } from '@/api/common';
import { pageCustomer } from '@/api/Opportunity/Customer';
import { Link } from 'react-router-dom';

const Index = () => {
	const { searchParams, linkTo } = useRouterLink();
	const id = searchParams.get('id');
	const [form] = Form.useForm();
	const projectHandlingId = Form.useWatch('projectHandlingId', form);
	const projectHandlingDeptId = Form.useWatch('projectHandlingDeptId', form);
	const [clueId, setClueId] = useState(searchParams.get('clueId') || '');
	// 用户信息
	const userInfo = useSelector((state) => {
		return state.user.userInfo;
	});
	const deptIds = (userInfo.deptList || []).map((ov) => ov.id);

	// 客户类型
	const [customTypeOptions, setCustomTypeOptions] = useState([]);
	// 商机进度
	const [opportunityStageOptions, setOpportunityStageOptions] = useState([]);
	// 商机类型
	const [opportunityTypeOptions, setOpportunityTypeOptions] = useState([]);
	// 产品分类
	const [productTypeOptions, setProductTypeOptions] = useState([]);
	// 商机可靠度
	const [reliabilityTypeOptions, setReliabilityTypeOptions] = useState([]);
	// 客户列表
	const [customerList, setCustomerList] = useState([]);

	// 部门列表 请选择招商责任单位
	const [deptList, setDeptList] = useState([]);

	// 人员列表
	const [userList, setUserList] = useState([]);
	/* 全单位人员列表 */
	const [allUserList, setAllUserList] = useState([]);
	const [detailData, setDetailData] = useState({});
	// 获取分类列表
	const getCategoryList = (categoryCode = '') => {
		return pageCategoryValue({
			categoryCode: categoryCode || '',
			pageNum: 1,
			pageSize: 300,
		});
	};

	useEffect(() => {
		Promise.all([
			getCategoryList('customer_type'),
			getCategoryList('opportunity_stage'),
			getCategoryList('opportunity_type'),
			getCategoryList('product_type'),
			getCategoryList('reliability_type'),
			getByPermissionPerms({ perms: 'businessOppty' }),
			pageCustomer({ pageSize: 1000, pageNum: 1 }),
		]).then((resList) => {
			if (resList[0].data && resList[0].data.records) {
				setCustomTypeOptions(
					(resList[0].data.records || []).map((ov) => {
						return {
							label: ov.value,
							value: ov.id || '',
						};
					})
				);
			}
			if (resList[1].data && resList[1].data.records) {
				setOpportunityStageOptions(
					(resList[1].data.records || []).map((ov) => {
						return {
							label: ov.value,
							value: ov.id || '',
						};
					})
				);
			}
			if (resList[2].data && resList[2].data.records) {
				setOpportunityTypeOptions(
					(resList[2].data.records || []).map((ov) => {
						return {
							label: ov.value,
							value: ov.id || '',
						};
					})
				);
			}
			if (resList[3].data && resList[3].data.records) {
				setProductTypeOptions(
					(resList[3].data.records || []).map((ov) => {
						return {
							label: ov.value,
							value: ov.id || '',
						};
					})
				);
			}
			if (resList[4].data && resList[4].data.records) {
				setReliabilityTypeOptions(
					(resList[4].data.records || []).map((ov) => {
						return {
							label: ov.value,
							value: ov.id || '',
						};
					})
				);
			}
			if (resList[5]) {
				const allUserList = (resList[5].data || []).map((ov) => {
					let label = `${ov.userName}`;
					let deptIds = [];
					if (ov.deptList && ov.deptList.length) {
						label += ov.deptList.reduce((pre, cur) => {
							deptIds.push(ov.id);
							return (pre += `/${cur.name || ''}`);
						}, '');
					}
					return {
						label: label,
						value: ov.id || '',
						deptList: ov.deptList || [],
						deptIds,
						multiple: `${ov.userName}-${ov.id}`,
						id: ov.id,
						userName: ov.userName,
					};
				});
				const userList = allUserList.filter((ov) => {
					const sameDept = ov.deptList.find((ov) => deptIds.includes(ov.id));
					return !!sameDept;
				});
				const allUser = allUserList.map((ov) => ({ ...ov, label: ov.label, value: ov.multiple }));
				setAllUserList(allUser);
				setUserList(userList);
			}
			if (resList[6].data && resList[6].data.records) {
				setCustomerList(
					(resList[6].data.records || []).map((ov) => {
						return {
							...ov,
							label: ov.name,
							value: ov.id || '',
						};
					})
				);
			}
		});

		setDeptList(
			(userInfo.deptList || [])
				.filter((ov) => ov.id)
				.map((ov) => {
					return {
						label: ov.name || '',
						value: ov.id || '',
					};
				})
		);
		if (id) {
			detailOpportunity({ id }).then((res) => {
				setDetailData(res.data || {});
				form.setFieldsValue({
					...(res.data || {}),
					informationProvider: res.data.informationProvider ? res.data.informationProvider.split(',') : [], // 商机线索提供人及部门 参与部门
				});
			});
		}
		const customerId = searchParams.get('customerId');
		const extraParams = {};
		if (customerId) {
			extraParams.customerId = customerId;
		}
		if (clueId) {
			extraParams.clueId = clueId;
		}
		form.setFieldsValue(extraParams);
	}, []);

	const submit = (e) => {
		form.validateFields()
			.then((values) => {
				const params = {
					id: id || '', //
					projectOpportunityName: values.projectOpportunityName || '', // 商机商机名称
					projectTypeId: values.projectTypeId || '', // 商机类型id
					customTypeId: values.customTypeId || '', // 客户类型id
					productTypeId: values.productTypeId || '', // 产品分类id
					expectCharge: values.expectCharge || 0, // 预测收费(单位：万元)
					expectCooperateProduct: values.expectCooperateProduct || '', // 预期合作产品
					projectStageId: values.projectStageId || '', // 商机阶段id
					projectReliabilityId: values.projectReliabilityId || '', // 商机可靠度分类id
					latestDevelopments: values.latestDevelopments || '', // 跟新进展
					projectHandlingId: values.projectHandlingId || '', // 商机责任人id detailData,
					projectHandlingDeptId: values.projectHandlingDeptId || '', // 商机负责部门id
					partakeDept: values.partakeDept || '', // 参与部门
					informationProvider: values.informationProvider?.join(',') || '', // 商机线索提供人及部门 参与部门
					customerId: values.customerId || '', // 客户id
					clueId: values.clueId || '', // 线索id
				};
				if (params.customerId) {
					params.customerName = customerList.find((ov) => ov.id === params.customerId)?.name;
				}
				if (id) {
					updateOpportunity(params).then(() => {
						linkTo(-1);
					});
				} else {
					addOpportunity(params).then(() => {
						linkTo(-1);
					});
				}
			})
			.catch((err) => {
				message.warning(err.errorFields[0].errors[0]);
			});
	};
	// 跳转新增客户
	const onAddCustomer = () => {
		linkTo('/businessOppty/customerManage/customerDetail');
	};

	/* 切换客户同步修改客户类型 */
	const changeCustomer = (value) => {
		const customer = customerList.find((ov) => ov.id === value);
		console.log('changeCustomer', customer);
		form.setFieldsValue({
			customTypeId: customer?.typeId,
		});
	};
	return (
		<div className="flex-sub margin-top-16">
			<Breadcrumb
				icon="icon-projectManage"
				list={[
					{
						name: '商机管理',
						link: '/businessOppty/opptyManage/list',
					},
					{
						name: '商机列表',
						link: '/businessOppty/opptyManage/list',
					},
				]}
				name="新建商机"
			/>

			<Form form={form} name="validateOnly" layout="vertical" autoComplete="off" className="padding-top-4">
				<div className="margin-lr-20 margin-bottom-20 padding-lr-20 padding-top-20 bg-color-ffffff border-radius-4">
					<div className="font-size-16 font-weight-500 color-1d2129 line-height-24 margin-bottom-16">商机基本信息</div>
					<Form.Item label="clueId" name="clueId" hidden>
						<Input placeholder="clueId" />
					</Form.Item>
					<Row gutter={20} className="flex-sub">
						<Col span={8}>
							<Form.Item
								name="projectOpportunityName"
								label="商机名称"
								rules={[
									{
										required: true,
									},
								]}
							>
								<Input placeholder="请输入商机名称" allowClear className="flex-sub" />
							</Form.Item>
						</Col>
						<Col span={8}>
							<Form.Item
								name="projectTypeId"
								label="商机类型"
								rules={[
									{
										required: true,
									},
								]}
							>
								<Select
									allowClear
									showSearch
									optionFilterProp={'label'}
									className="flex-sub"
									placeholder="请选择商机类型"
									options={opportunityTypeOptions}
									filterOption={(input, option) => (option?.label ?? '').includes(input)}
								/>
							</Form.Item>
						</Col>
						<Col span={8}>
							<Form.Item
								name="customerId"
								tooltip={'列表中不存在客户名称，请先添加客户名称，再进行选择'}
								required={true}
								rules={[{ required: true, message: '请选择客户名称' }]}
								label={
									<Space>
										<span>客户名称</span>
										<Button size={'small'} type={'link'} onClick={onAddCustomer}>
											添加
										</Button>
									</Space>
								}
							>
								<Select
									allowClear
									showSearch
									className="flex-sub"
									placeholder="请选择客户名称"
									options={customerList}
									optionFilterProp={'label'}
									onSelect={changeCustomer}
								/>
							</Form.Item>
						</Col>
						<Col span={8}>
							<Form.Item
								name="customTypeId"
								label="客户类型"
								rules={[
									{
										required: true,
									},
								]}
							>
								<Select
									allowClear
									showSearch
									optionFilterProp={'label'}
									className="flex-sub"
									placeholder="请选择客户类型"
									options={customTypeOptions}
									filterOption={(input, option) => (option?.label ?? '').includes(input)}
								/>
							</Form.Item>
						</Col>
						<Col span={8}>
							<Form.Item
								name="expectCooperateProduct"
								label="预期合作产品"
								rules={[
									{
										required: true,
									},
								]}
							>
								<Input.TextArea rows={4} placeholder={'请输入预期合作产品'} />
							</Form.Item>
						</Col>
						<Col span={8}>
							<Form.Item name="productTypeId" label="产品分类">
								<Select
									allowClear
									showSearch
									optionFilterProp={'label'}
									className="flex-sub"
									placeholder="请选择产品分类"
									options={productTypeOptions}
									filterOption={(input, option) => (option?.label ?? '').includes(input)}
								/>
							</Form.Item>
						</Col>
						<Col span={8}>
							<Form.Item name="expectCharge" label="预测收费（万元）">
								<InputNumber
									className="width-100per"
									placeholder={'请输入预测收费'}
									min={0}
									formatter={(value) => (value && `${value}万`) || value}
									parser={(value) => value.replace('万', '')}
								/>
							</Form.Item>
						</Col>
					</Row>
				</div>
				<div className="margin-20 padding-lr-20 padding-top-20 bg-color-ffffff border-radius-4">
					<div className="font-size-16 font-weight-500 color-1d2129 line-height-24 margin-bottom-16">商机动态</div>
					<Row gutter={20} className="flex-sub">
						<Col span={8}>
							<Form.Item
								name="projectReliabilityId"
								label="商机自评靠谱度"
								rules={[
									{
										required: true,
									},
								]}
							>
								<Select
									showSearch
									optionFilterProp={'label'}
									className="flex-sub"
									placeholder="请选择商机自评靠谱度"
									options={reliabilityTypeOptions}
									filterOption={(input, option) => (option?.label ?? '').includes(input)}
								/>
							</Form.Item>
						</Col>
						<Col span={8}>
							<Form.Item
								name="projectStageId"
								label="商机阶段"
								rules={[
									{
										required: true,
									},
								]}
							>
								<Select
									allowClear
									showSearch
									optionFilterProp={'label'}
									className="flex-sub"
									placeholder="请选择商机阶段"
									options={opportunityStageOptions}
									filterOption={(input, option) => (option?.label ?? '').includes(input)}
								/>
							</Form.Item>
						</Col>

						<Col span={8}>
							<Form.Item name="latestDevelopments" label="跟进进展">
								<Input.TextArea rows={2} placeholder={'请输入跟进进展'} />
							</Form.Item>
						</Col>
					</Row>
				</div>
				<div className="margin-20 padding-lr-20 padding-top-20 bg-color-ffffff border-radius-4">
					<div className="font-size-16 font-weight-500 color-1d2129 line-height-24 margin-bottom-16">商机负责人</div>
					<Row gutter={20} className="flex-sub">
						<Col span={8}>
							<Form.Item
								name="projectHandlingId"
								label="商机负责人"
								rules={[
									{
										required: true,
									},
								]}
							>
								<Select
									virtual={userList.length > 300}
									showSearch
									optionFilterProp={'label'}
									className="flex-sub"
									placeholder="请选择商机负责人"
									options={userList}
									filterOption={(input, option) => (option?.label ?? '').includes(input)}
									onChange={(val, data) => {
										const find = data.deptList.find((ov) => ov.id === projectHandlingDeptId);
										if (!find) {
											form.setFieldValue('projectHandlingDeptId', '');
										}
									}}
								/>
							</Form.Item>
						</Col>
						<Col span={8}>
							<Form.Item
								name="projectHandlingDeptId"
								label="商机负责部门"
								rules={[
									{
										required: true,
									},
								]}
							>
								<Select
									showSearch
									optionFilterProp={'label'}
									className="flex-sub"
									placeholder="请选择商机负责部门"
									options={deptList.filter((ov) => {
										if (projectHandlingId) {
											const find = userList.find((oc) => oc.value == projectHandlingId);
											if (find) {
												const userDeptIds = find.deptList.map((oc) => oc.id);
												return userDeptIds.includes(ov.value);
											} else {
												return false;
											}
										} else {
											return true;
										}
									})}
									filterOption={(input, option) => (option?.label ?? '').includes(input)}
									onChange={(val) => {
										const find = userList.find((oc) => oc.value == projectHandlingId);
										if (find) {
											const userDeptIds = find.deptList.map((oc) => oc.id);
											if (!userDeptIds.includes(val)) {
												form.setFieldValue('projectHandlingId', '');
											}
										}
									}}
								/>
							</Form.Item>
						</Col>
						<Col span={8}>
							<Form.Item name="partakeDept" label="参与部门">
								<Input.TextArea rows={2} placeholder={'请输入参与部门'} />
							</Form.Item>
						</Col>
						<Col span={8}>
							<Form.Item name="informationProvider" label="商机线索提供人及部门">
								{/*<Input.TextArea rows={2} />*/}
								<Select
									virtual={allUserList.length > 300}
									showSearch
									optionFilterProp={'label'}
									mode="multiple"
									className="flex-sub"
									placeholder="请选择商机线索提供人及部门"
									options={allUserList}
									filterOption={(input, option) => (option?.label ?? '').includes(input)}
								/>
							</Form.Item>
						</Col>
					</Row>
				</div>
			</Form>
			<div className="padding-42"></div>
			<div className="flex justify-end align-center position-fixed bottom-0 left-0 right-0 padding-20 bg-color-ffffff">
				<Button type="default" onClick={() => {}}>
					重置
				</Button>
				<Button
					className="margin-left-16"
					type="primary"
					onClick={() => {
						submit();
					}}
				>
					保存
				</Button>
			</div>
		</div>
	);
};

export default Index;
