import {
	useEffect,
	useState,
	useRef,
	forwardRef,
	useImperativeHandle,
	memo,
} from 'react';
import {Form, Input, Radio, Cascader, message, Button, Space, Select} from 'antd';
import { getDictData } from '@/utils/dictionary';
import { addFollowUpRecord } from '@/api/Bidmgt/ProjectManage/index';
import {useSelector} from "react-redux";
import {getByPermissionPerms} from "@/api/common";

const Index = forwardRef((props, ref) => {
	const [form] = Form.useForm();
	const [deptUserTreeList, setDeptUserTreeList] = useState([]);
	const [selectMobile, setSelectMobile] = useState('');
	// 用户信息
	const userInfo = useSelector((state) => {
		return state.user.userInfo;
	});
	const deptIds = (userInfo.deptList || []).map((ov) => ov.id);
	const userId = userInfo.id;
	useEffect(() => {
		getDeptUserTreeList();
	}, [])
	/* 获取部门用户信息 */
	const getDeptUserTreeList = async () => {
		const {data} = await getByPermissionPerms({ deptIds, perms: 'businessOppty' });
		console.log(data)
		const deptUserTreeList = data.map(item => {
			const deptNameList = item.deptList.map(item => `/${item.name}`);
			return {
				value: item.id,
				label: `${item.userName}${deptNameList}`,
				filteredValue: `${item.userName} ${item.mobile} ${deptNameList}`,
				...item
			};
		});
		setDeptUserTreeList(deptUserTreeList);
		handleChange(userId, deptUserTreeList);
	}
	/* 修改跟进人 */
	const handleChange = (value, list) => {
		const currentUser = (list || deptUserTreeList).find(item => item.id === value);
		form.setFieldsValue({
			contactInformation: currentUser.mobile,
			recorder: currentUser.id,
		})
		setSelectMobile(currentUser.mobile)
	}
	// 提交
	const handleSubmit = () => {
		return new Promise((resolve) => {
			form.validateFields().then((values) => {
				const params = {
					projectId: props.id || '', // 商机id
					latestDevelopments: values.latestDevelopments || '', // 最新进展
					dynamicKeywords: values.dynamicKeywords || '', // 动态关键字
					recorder: deptUserTreeList.find(item => item.id === values.recorder)?.userName || '', // 记录人
					contactInformation: selectMobile || '', // 联系方式
					departmentId: '', // 所属部门id
					id: props.updateFollowId || '',
				};
				addFollowUpRecord(params).then(() => {
					message.success('新增成功');
					form.resetFields();
					resolve();
				});
			});
		});
	};

	// 取消
	const handleCancel = () => {
		return new Promise((resolve) => {
			form.resetFields();
			resolve();
		});
	};
	useImperativeHandle(ref, () => {
		return {
			onCancel: handleCancel,
			onOk: handleSubmit,
		};
	});

	return (
		<div className=''>
			<Form
				form={form}
				labelCol={{
					style: { width: '120px' },
				}}
				labelAlign='right'
				initialValues={{}}
			>
				<Form.Item
					label='最新动态'
					name='latestDevelopments'
					rules={[
						{
							required: true,
							message: '请输入最新动态',
						},
					]}
				>
					<Input.TextArea rows={5} placeholder='请输入最新动态' />
				</Form.Item>
				<Form.Item
					label='动态关键词'
					name='dynamicKeywords'
					rules={[
						{
							required: true,
							message: '请输入动态关键词',
						},
					]}
				>
					<Input placeholder='请输入不多于10个字的关键词' />
				</Form.Item>
				<Form.Item
					label='跟进人'
					name='recorder'
					rules={[
						{
							required: true,
							message: '请输入跟进人',
						},
					]}
				>
					<Select
						placeholder="请选择跟进人"
						options={deptUserTreeList}
						optionFilterProp={'filteredValue'}
						showSearch
						onSelect={value => handleChange(value, deptUserTreeList)}
					/>
				</Form.Item>
				<Form.Item
					label='联系方式'
					name='contactInformation'
					rules={[
						{
							required: true,
							message: '请输入联系方式',
						},
					]}
				>
					{selectMobile}
				</Form.Item>
			</Form>
		</div>
	);
});

export default memo(Index);
