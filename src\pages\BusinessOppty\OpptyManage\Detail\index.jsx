import {
	Col,
	Card,
	Row,
	Progress,
	Select,
	DatePicker,
	Table,
	Button,
	Checkbox,
	Form,
	Input,
	Steps,
	Pagination,
	Tag,
	Tooltip,
	Popconfirm,
	Modal,
	message,
} from 'antd';
import Breadcrumb from '@/components/Breadcrumb';
import { getImageSrc } from '@/assets/images/index';
// import moment from 'moment';
import {
	ReloadOutlined,
	StarOutlined,
	UploadOutlined,
	PlusCircleOutlined,
	FieldTimeOutlined,
	ShareAltOutlined,
	UserOutlined,
	PhoneOutlined,
	ExclamationCircleOutlined,
} from '@ant-design/icons';
import { useRouterLink } from '@/hook/useRouter';
import {
	detailOpportunity,
	opportunityExport,
	sendEarlyWarning,
	updateProjectStage,
	opportunityFollow,
} from '@/api/Opportunity/OpportunityManage/index';
import { delByProjectId, delFollowUpRecord, listFollowUpRecord } from '@/api/Bidmgt/ProjectManage/index';
import { useEffect, useState, useRef } from 'react';
import ModalForm from '@/components/ModalForm';
import Curd from './Curd/index';
import { pageCategoryValue } from '@/api/Bidmgt/Dict/index';
import dayjs from 'dayjs';
import { download } from '@/utils/common';
import { useSelector, useDispatch } from 'react-redux';

// 实时跟进
const RealTimeStepsTitle = (props = {}) => {
	return <div className="line-height-22 font-weight-500 padding-top-6">{props.dynamicKeywords || ''}</div>;
};
// 实时跟进
const RealTimeStepsDescription = (props = {}) => {
	const { projectId, id, getDetail, handleEditForm } = props;
	/* 删除商机 */
	const deleteOpportunity = () => {
		const confirm = async () => {
			const res = await delFollowUpRecord({ ids: [id] });
			if (res.data) {
				message.success('删除跟进记录成功');
				getDetail();
			}
		};
		Modal.confirm({
			title: '提示',
			icon: <ExclamationCircleOutlined />,
			content: '确定删除跟进记录吗？',
			onOk: confirm,
			onCancel() {
				console.log('Cancel');
			},
		});
	};
	/* 修改商机 */
	const handleEditOpportunity = () => {
		handleEditForm(id);
	};
	return (
		<>
			<Tooltip placement="leftTop" title={props.latestDevelopments || ''}>
				<div className="line-height-20 font-weight-500 text-cut-2 padding-tb-4 color-4e5969">动态内容：{props.latestDevelopments || ''}</div>
			</Tooltip>
			<div className="flex justify-between align-center font-size-12 line-height-20">
				<div className={'flex'}>
					<div className="flex justify-start align-center">
						<FieldTimeOutlined />
						<div className="margin-left-4">{`${props.createTime || ''}`.slice(0, 16)}</div>
					</div>
					<div className="flex justify-start align-center margin-left-18">
						<UserOutlined />
						<div className="margin-left-4">{props.recorder || ''}</div>
					</div>
					<div className="flex justify-start align-center margin-left-18">
						<PhoneOutlined />
						<div className="margin-left-4">{props.contactInformation || ''}</div>
					</div>
				</div>
				{/*<div >*/}
				{/*	<Button size={'small'} type={'primary'} onClick={handleEditOpportunity}>修改</Button>*/}
				{/*	<Button className={'margin-left-8'} danger size={'small'} onClick={deleteOpportunity}>删除</Button>*/}
				{/*</div>*/}
			</div>
		</>
	);
};

// 阶段 title
const StageStepsTitle = (props = {}) => {
	return (
		<div className="width-120 height-14 line-height-14 font-size-12 position-absolute" style={{ top: '-50px', left: '-60px' }}>
			<div>{props.value && props.value.slice(0, 19)}</div>
		</div>
	);
};

// 阶段 Description
const StageStepsDescription = (props = {}) => {
	const [show, setShow] = useState(false);
	useEffect(() => {
		if (props.isHasPre && props.curStage + 1 === props.index && props.time === '' && !props.disableOption) {
			setShow(true);
		} else {
			setShow(false);
		}
	}, [props.projectStageId, props.curStage, props.time, props.index, props.disableOption]);
	return (
		<div className="text-align-center">
			<div className={`font-bold ${props.time ? 'color-333333' : ''}`}>{props.value}</div>
			{show && (
				<Button
					className="margin-top-4"
					type="primary"
					danger
					onClick={() => {
						props.alarmCb && props.alarmCb();
					}}
				>
					{props.noticeType == 2 ? '发送催办' : '发送预警'}
				</Button>
			)}
		</div>
	);
};
const Index = () => {
	const { searchParams, linkTo } = useRouterLink();
	const id = searchParams.get('id');
	const [disableOption, setDisableOption] = useState(false);
	/* 设置修改跟进信息ID */
	const [updateFollowId, setUpdateFollowId] = useState('');
	// 用户信息
	const userInfo = useSelector((state) => {
		return state.user.userInfo;
	});
	// 是这个部门的人
	const isHasPre = (projectHandlingDeptId = '') => {
		return (userInfo.deptList || []).some((ov) => ov.id == projectHandlingDeptId);
	};

	const ModalFormRef = useRef();
	// 新增加 填写跟进记录
	const handleOpenForm = (id = '') => {
		ModalFormRef.current.setOpen(true);
		ModalFormRef.current.setTitle('填写跟进记录');
	};
	/* 修改跟进记录 */
	const handleEditForm = (id = '') => {
		setUpdateFollowId(id);
		ModalFormRef.current.setOpen(true);
		ModalFormRef.current.setTitle('修改跟进记录');
	};

	const [detail, setDetail] = useState({});

	const [followList, setFollowList] = useState([]);
	const [followShowList, setFollowShowList] = useState([]);

	const [total, setTotal] = useState(1);
	// 页码数据
	const [pagination, setPagination] = useState({
		pageNum: 1,
		pageSize: 5,
	});
	// 页码页数改变
	const changePage = (pageNum, pageSize) => {
		pagination.pageSize = pageSize;
		pagination.pageNum = pageNum;
		setPagination({ ...pagination });
	};
	// 获取详情
	const getDetail = () => {
		return new Promise((resolve) => {
			detailOpportunity({ id }).then((res) => {
				setDetail({
					...res.data,
					informationProvider: res.data.informationProvider
						?.split(',')
						.map((ov) => {
							return ov.split('-')[0];
						})
						.join(','),
				});
				setDisableOption(res.data.suspendStatus === 1);
				resolve(res.data || {});
				// 设置 阶段
				getProjectStage(res.data);
				if (updateFollowId) {
					setUpdateFollowId('');
				}
			});
			changePage(1, 5);
			listFollowUpRecord({ projectId: id }).then((res) => {
				setFollowList(res.data || []);
				setTotal((res.data || []).length);
			});
		});
	};
	// 点击关注
	const projectFollowBtn = () => {
		opportunityFollow({ projectId: id }).then((res) => {
			getDetail();
		});
	};

	// 商机阶段
	const [projectStageOptions, setProjectStageOptions] = useState([]);
	// 当前 商机阶段
	const [curStage, setCurStage] = useState(0);
	// 获取分类列表
	const getCategoryList = (categoryCode = '') => {
		return pageCategoryValue({
			categoryCode: categoryCode || '',
			pageNum: 1,
			pageSize: 300,
		});
	};

	// 获取商机阶段数据
	const getProjectStage = (detailData = {}) => {
		const { projectStageId, projectStageTimeList, createTime } = detailData;

		getCategoryList('opportunity_stage').then((res) => {
			const list = [{ id: '', time: createTime || '' }, ...(res.data.records || [])];
			const timeList = projectStageTimeList || [];
			const currentIndex = list.findIndex((ov) => ov.id === projectStageId) || 0;
			setCurStage(currentIndex > -1 ? currentIndex : 0);
			list.forEach((ov, oi) => {
				ov.time = '';
				if (currentIndex >= oi) {
					// 对应阶段的时间
					ov.time = timeList.find((item) => item.projectStageId === ov.id)?.createTimeEnd || '';

					// 编辑修改阶段导致无时间 显示创建时间
					if (!ov.time) {
						ov.time = createTime;
					}
				}
			});

			setProjectStageOptions(list);
		});
	};
	useEffect(() => {
		if (id) {
			getDetail();
		}
	}, []);

	// 当数据齐全的时候 判断 展示 预警 还是 催办
	const [noticeType, setNoticeType] = useState(2);
	useEffect(() => {
		if (projectStageOptions.length && detail.id) {
			const time =
				(detail.projectStageTimeList &&
					detail.projectStageTimeList.length &&
					detail.projectStageTimeList[detail.projectStageTimeList.length - 1].createTimeEnd) ||
				detail.createTime;
			if (new Date().valueOf() - new Date(time).valueOf() > 7 * 24 * 60 * 60 * 1000) {
				setNoticeType(1);
			}
		}
	}, [projectStageOptions, detail]);

	// 这里是 实时
	useEffect(() => {
		setFollowShowList(
			followList.slice(pagination.pageSize * (pagination.pageNum - 1), pagination.pageSize * pagination.pageNum).map((ov) => {
				return {
					id: ov.id,
					title: <RealTimeStepsTitle {...ov} />,
					description: <RealTimeStepsDescription {...ov} getDetail={getDetail} handleEditForm={handleEditForm} />,
				};
			})
		);
	}, [followList, pagination]);

	const alarmTextRef = useRef('');
	const alarmCb = () => {
		Modal.confirm({
			title: `确认要发送${noticeType == 2 ? '催办' : '预警'}信息吗?`,
			icon: <ExclamationCircleOutlined />,
			content: (
				<Input.TextArea
					onChange={(e) => {
						alarmTextRef.current = e.target.value || '';
					}}
				/>
			),
			okText: '确认',
			cancelText: '取消',
			onOk: () => {
				sendEarlyWarning({
					noticeType: noticeType,
					projectId: id,
					earlyWarningContent: alarmTextRef.current,
				}).then(() => {
					alarmTextRef.current = '';
					getDetail();
				});
			},
			onCancel: () => {
				alarmTextRef.current = '';
			},
		});
	};

	const updataStage = () => {
		updateProjectStage({
			projectId: id,
			projectStageId: projectStageOptions[curStage + 1].id,
		}).then(() => {
			getDetail();
		});
	};

	// 导出
	const exportBtn = () => {
		id &&
			opportunityExport(
				{ ids: [id] },
				{
					responseType: 'blob',
				}
			).then((res) => {
				download.excel(res, `商机-${dayjs().format('YYYYMMDD_HH:mm')}`);
			});
	};
	return (
		<div className="flex-sub margin-top-16">
			<Breadcrumb
				icon="icon-projectManage"
				list={[
					{
						name: '商机管理',
						link: '/businessOppty/opptyManage/list',
					},
					{
						name: '商机列表',
						link: '/businessOppty/opptyManage/list',
					},
				]}
				name="商机详情"
			/>
			<div className="margin-lr-20 margin-bottom-20 bg-color-ffffff border-radius-4 overflow-hidden">
				<div
					className="flex justify-start align-center margin-bottom-16 padding-lr-16 padding-tb-8"
					style={{
						background:
							'linear-gradient(90deg, rgba(255,123,71,0.08) 0%, rgba(255,123,71,0) 8%), linear-gradient(90deg, rgba(255,123,71,0.24) 0%, rgba(255,123,71,0) 400px)',
					}}
				>
					<img className="width-24 height-24" src={getImageSrc('@/assets/images/ProjectManage/project-stage.png')} />
					<div className="font-size-16 font-weight-500 color-1d2129 line-height-24 margin-left-10 flex-sub flex justify-start align-center">
						商机阶段
					</div>
					{isHasPre(detail.projectHandlingDeptId) && curStage + 1 < projectStageOptions.length && !disableOption && (
						<Popconfirm
							title="提示"
							description="确定进入下一阶段吗？"
							onConfirm={() => {
								updataStage();
							}}
							okText="确定"
							cancelText="取消"
						>
							<Button type="primary">进入下一阶段</Button>
						</Popconfirm>
					)}
				</div>
				<div className="padding-lr-10 padding-top-40 overflowX-auto">
					<Steps
						progressDot
						current={curStage}
						items={[
							...projectStageOptions.map((ov, oi) => {
								return {
									title: <StageStepsTitle value={ov.time} />,
									description:
										oi > 0 ? (
											<StageStepsDescription
												curStage={curStage}
												{...ov}
												projectStageId={detail.projectStageId}
												index={oi}
												isHasPre={isHasPre(detail.projectHandlingDeptId)}
												disableOption={disableOption}
												alarmCb={alarmCb}
												noticeType={noticeType}
											/>
										) : null,
								};
							}),
						]}
					/>
					<div className="padding-10"></div>
				</div>
			</div>
			<div className="margin-20">
				<Row gutter={20}>
					<Col span={14} className="flex flex-direction-column">
						<div className="bg-color-ffffff border-radius-4 overflow-hidden color-1d2129 flex-sub">
							<div
								className="flex justify-start align-center margin-bottom-16 padding-lr-16 padding-tb-8"
								style={{
									background:
										'linear-gradient(90deg, rgba(3,191,138,0.08) 0%, rgba(3,191,138,0) 10%), linear-gradient(90deg, rgba(3,191,138,0.24) 0%, rgba(3,191,138,0) 360px)',
								}}
							>
								<img className="width-24 height-24" src={getImageSrc('@/assets/images/ProjectManage/project-detail.png')} />
								<div className="font-size-16 font-weight-500 color-1d2129 line-height-24 margin-left-10 flex-sub">商机详情</div>
								{isHasPre(detail.projectHandlingDeptId) && (
									<Button type="default" icon={<UploadOutlined />} onClick={() => exportBtn()}>
										导出数据
									</Button>
								)}
								{!disableOption && (
									<Button
										type="primary"
										icon={
											detail.followStatus == 1 ? (
												<StarOutlined className="color-ff7d00" />
											) : (
												<StarOutlined className="color-ffffff" />
											)
										}
										className="margin-left-12"
										onClick={() => projectFollowBtn()}
									>
										{detail.followStatus == 1 ? '已追踪' : '追踪商机'}
									</Button>
								)}
							</div>
							<div className="padding-lr-20 line-height-22">
								<div className="flex justify-start align-start padding-bottom-16">
									<div className="color-86909c flex-shrink">商机类型：</div>
									<div className="flex-sub">{detail.projectTypeName || ''}</div>
								</div>
								<div className="flex justify-start align-start padding-bottom-16">
									<div className="color-86909c flex-shrink">商机名称：</div>
									<div className="flex-sub">{detail.projectOpportunityName || ''}</div>
								</div>
								<div className="flex justify-start align-start padding-bottom-16">
									<div className="color-86909c flex-shrink">客户类型：</div>
									<div className="flex-sub">{detail.customTypeName || ''}</div>
								</div>
								<div className="flex justify-start align-start padding-bottom-16">
									<div className="color-86909c flex-shrink">客户名称：</div>
									<div className="flex-sub">{detail.customerName || ''}</div>
								</div>
								<div className="flex justify-start align-start padding-bottom-16">
									<div className="color-86909c flex-shrink">预期合作产品：</div>
									<div className="flex-sub">{detail.expectCooperateProduct || ''}</div>
								</div>
								<div className="flex justify-start align-start padding-bottom-16">
									<div className="color-86909c flex-shrink">产品分类：</div>
									<div className="flex-sub">{detail.productTypeName || ''}</div>
								</div>
								<div className="flex justify-start align-start padding-bottom-16">
									<div className="color-86909c flex-shrink">预测收费（万元）：</div>
									<div className="flex-sub">{detail.expectCharge || ''}</div>
								</div>
								{/*<div className='flex justify-start align-start padding-bottom-16'>*/}
								{/*	<div className='color-86909c flex-shrink'>*/}
								{/*		上报时间：*/}
								{/*	</div>*/}
								{/*	<div className='flex-sub'>*/}
								{/*		{detail.reportTime ? moment(detail.reportTime).format('YYYY-MM-DD') : ''}*/}
								{/*	</div>*/}
								{/*</div>*/}
								<div className="flex justify-start align-start padding-bottom-16">
									<div className="color-86909c flex-shrink">创建时间：</div>
									<div className="flex-sub">{`${detail.createTime || ''}`.slice(0, 16) || ''}</div>
								</div>
								<div className="flex justify-start align-start padding-bottom-16">
									<div className="color-86909c flex-shrink">商机自评靠谱度：</div>
									<div className="flex-sub">{detail.projectReliabilityName || ''}</div>
								</div>
								<div className="flex justify-start align-start padding-bottom-16">
									<div className="color-86909c flex-shrink">跟进进展：</div>
									<div className="flex-sub">{detail.latestDevelopments || ''}</div>
								</div>
								<div className="flex justify-start align-start padding-bottom-16">
									<div className="color-86909c flex-shrink">商机负责人：</div>
									<div className="flex-sub">{detail.projectHandlingName || ''}</div>
								</div>
								<div className="flex justify-start align-start padding-bottom-16">
									<div className="color-86909c flex-shrink">商机负责部门：</div>
									<div className="flex-sub">{detail.projectHandlingDeptName || ''}</div>
								</div>
								<div className="flex justify-start align-start padding-bottom-16">
									<div className="color-86909c flex-shrink">参与部门：</div>
									<div className="flex-sub">{detail.partakeDept || ''}</div>
								</div>
								<div className="flex justify-start align-start padding-bottom-16">
									<div className="color-86909c flex-shrink">线索提供人及部门：</div>
									<div className="flex-sub">{detail.informationProvider || ''}</div>
								</div>
							</div>
						</div>
					</Col>
					<Col span={10} className="flex flex-direction-column">
						<div className="bg-color-ffffff border-radius-4 overflow-hidden flex-sub flex flex-direction-column">
							<div
								className="flex justify-start align-center margin-bottom-16 padding-lr-16 padding-tb-8"
								style={{
									background:
										'linear-gradient(90deg, rgba(74,183,255,0.12) 0%, rgba(74,183,255,0) 10%), linear-gradient(90deg, rgba(74,183,255,0.24) 0%, rgba(74,183,255,0) 360px)',
								}}
							>
								<img className="width-24 height-24" src={getImageSrc('@/assets/images/ProjectManage/project-track.png')} />
								<div className="font-size-16 font-weight-500 color-1d2129 line-height-24 margin-left-10">实时跟进记录</div>
								<div
									className="color-4e5969 flex justify-start align-center margin-left-12 font-size-14 a"
									onClick={() => getDetail()}
								>
									<ReloadOutlined />
									<div className="margin-left-4">更新记录</div>
								</div>
								<div className="flex-sub"></div>
								{isHasPre(detail.projectHandlingDeptId) && !disableOption && (
									<Button
										type="primary"
										icon={<PlusCircleOutlined />}
										onClick={() => {
											handleOpenForm();
										}}
									>
										填写跟进记录
									</Button>
								)}
							</div>
							<div className="padding-lr-20 flex-sub overflow-hidden">
								<Steps
									progressDot
									direction="vertical"
									current={followList.length && followShowList.length && followShowList[0].id == followList[0].id ? 0 : -1}
									items={followShowList}
								/>
							</div>
							<div className="text-align-right padding-lr-20">
								<Pagination total={total} pageSize={5} current={pagination.pageNum} onChange={changePage} />
							</div>
							<div className="padding-10"></div>
						</div>
					</Col>
				</Row>
			</div>

			{/* 编辑/新建 弹窗 开始 */}
			<ModalForm
				ref={ModalFormRef}
				onOk={(res) => {
					getDetail();
				}}
				FormComp={(props) => <Curd ref={props.FormCompRef} id={id} updateFollowId={updateFollowId} />}
			/>
			{/* 编辑/新建 弹窗 结束 */}
		</div>
	);
};

export default Index;
