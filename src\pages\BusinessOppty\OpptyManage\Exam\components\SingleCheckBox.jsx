import { useState } from 'react';
import { Checkbox, Form, Radio } from 'antd';
const SingleSelectCheckboxes = ({ options = [], name = '', required, rules, disabled = false }) => {
	const [selectedValue, setSelectedValue] = useState(null);

	const handleCheckboxChange = (value) => {
		// const selected = value.pop();
		// setSelectedValue([selected]);
		setSelectedValue(value);
	};

	return (
		<Form.Item name={name} required={required} rules={rules}>
			{/*{options.map((option) => (*/}
			{/*    <Checkbox*/}
			{/*        key={option.answerScore}*/}
			{/*        value={option.id}*/}
			{/*        checked={selectedValue === option.id}*/}
			{/*        onChange={handleCheckboxChange}*/}
			{/*    >*/}
			{/*        {option.answerName}*/}
			{/*    </Checkbox>*/}
			{/*))}*/}
			<Radio.Group value={selectedValue} onChange={handleCheckboxChange} disabled={disabled}>
				{options.map((option) => (
					<Radio key={option.id} value={`${option.id}-${option.answerScore}`}>
						{option.answerName}
					</Radio>
				))}
			</Radio.Group>
		</Form.Item>
	);
};
export default SingleSelectCheckboxes;
