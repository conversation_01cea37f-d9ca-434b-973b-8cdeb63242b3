/* 提交成功结算 */
import React from 'react';
import {Button, Col, Modal, Row, Space} from "antd";
import SuccessBg from '@/assets/images/Bidmgt/exam/result.png'
import '../index.scss'

const Success = (props) => {
    const {open, result = {}} = props;
    const handleOk = () => {
        props.onOk();
    };
    const reTest = () => {
        props.onCancel();
    };
    const onClose = () => {
        props.onClose();
    };
    return ( <Modal
        className={'exam-success'}
        open={open}
        width={380}
        styles={{
            body: {backgroundColor: 'transparent', height: 420, padding: 0}
        }}
        closeIcon={null}
        onCancel={onClose}
        footer={null}
    >
        <div className={'exam-success-content'}>
            <div className={'font-bold font-size-16 text-align-center padding-top-130'}>测评成功</div>
            <div className={'margin-top-60 padding-10'}>
                <Row>
                    <Col span={6} className={' font-size-16 color-86909c'}>商机名称</Col>
                    <Col span={18} className={'font-bold font-size-16 text-align-right'}>{result.name}</Col>
                </Row>
                <Row className={'margin-top-20'}>
                    <Col span={6} className={' font-size-16 color-86909c'}>测评结果</Col>
                    <Col span={18} className={'font-bold font-size-16 text-align-right color-ff9535'}>{result.score}分</Col>
                </Row>
            </div>
            <div className={'position-absolute bottom-40 flex justify-center margin-top-20 width-100per'}>
                <button className={'retest-btn'} onClick={reTest}>重新测评</button>
                <button className={'submit-btn'} onClick={handleOk}>确认</button>
            </div>
        </div>
    </Modal>)
};
export default Success;