import React, {useEffect, useState} from "react";
import {
	getOpportunityAssessExamVo,
	getOpportunityAssessScoreVo,
	saveAnswerContentAndScore
} from "@/api/Opportunity/OpportunityManage";
import {useSearchParams} from "react-router-dom";
import {Button, Card, Checkbox, Flex, Form, Modal, Row, Tabs, Typography} from 'antd';
import Breadcrumb from "@/components/Breadcrumb";
import number2Chinese from "@/utils/number2Chinese";
import SingleCheckBox from "./components/SingleCheckBox";
import HeaderBg from '@/assets/images/Bidmgt/exam/exam-right-bg.png';
import Success from "@/pages/BusinessOppty/OpptyManage/Exam/components/Success";

const { Paragraph, Text, Title } = Typography;
/* 匹配商机进展的id（表单名称） */
const opportunityId = '9';
const Exam = () => {
	const [examData, setExamData] = useState({
		assessQuestionsList: [],
		assessExamName: '',
		id: '',
		/* 原始试题 */
		originalQuestionsList: [],
	});
	const [urlSearch] = useSearchParams();
	const [form] = Form.useForm();
	const [showSuccess, setShowSuccess] = useState(false);
	const [resultData, setResultData] = useState({
		name: urlSearch.get('projectStageName') || '',
		score: 0,
	});
	useEffect(() => {
		getExamData();
		getResult();
	}, []);

	/* 获取评测试题 */
	const getExamData = async () => {
		const res = await getOpportunityAssessExamVo();
		const { assessQuestionsList, assessExamName, id } = res.data;
		/* 默认选中商机进度，name:id(问题id) 	value:id-answerScore(答案id-分数) */
		const originalQuestionsList = [...assessQuestionsList];
		const { assessAnswerList } = assessQuestionsList.find((question) => question.id === opportunityId)
		const defaultQuestion = assessAnswerList.find((question) => question.id === urlSearch.get('projectStageId'));
		form.setFieldValue(opportunityId, `${defaultQuestion.id}-${defaultQuestion.answerScore}`);
		// 使用reduce函数将相同questionsCategoryName的项分组到一起
		const groupedQuestions = assessQuestionsList.reduce((acc, question) => {
			const existingGroup = acc.find(group => group.questionsCategoryName === question.questionsCategoryName);
			if (existingGroup) {
				existingGroup.children.push(question);
			} else {
				acc.push({
					questionsCategoryName: question.questionsCategoryName,
					id: question.id,
					sortNumber: question.sortNumber,
					children: [question]
				});
			}
			return acc;
		}, []);
		setExamData({...res.data, assessQuestionsList: groupedQuestions, originalQuestionsList});
	}

	/* 获取测评结果 */
	const getResult = async () => {
		const res = await getOpportunityAssessScoreVo({
			opportunityId: urlSearch.get('opportunityId')
		});
		if (res.data.answerList) {
			const answerData = {}
			res.data.answerList.map((item) => {
				answerData[item.questionsId] = `${item.id}-${item.answerScore}`;
			});
			form.setFieldsValue(answerData)
		}
	}

	/* 提交测评 */
	const onFinish = async () => {
		// const values = await form.getFieldsValue();
		// console.log(values)
		form.validateFields()
			.then(async (values) => {
				let result = 0;
				const answerList = Object.entries(values).map(([questionsId, value], key) => {
					const [id, answerScore] = value.split('-');
					result += Number(answerScore);
					return {
						id,
						answerScore: answerScore,
						questionsId
					};
				});
				const params = {
					assessExamId: examData.id,
					opportunityId: urlSearch.get('opportunityId'),
					answerList,
				};
				const res = await saveAnswerContentAndScore(params);
				if (res.code === '00000') {
					setResultData({
						name: urlSearch?.get('projectOpportunityName'),
						score: result,
					})
					setShowSuccess(true);
					// Modal.success({
					// 	title: '测评结果',
					// 	content: <div>
					// 		<p>商机名称：{ urlSearch?.get('projectOpportunityName') }</p>
					// 		<p>测评结果：{result} 分</p>
					// 	</div>,
					// 	onOk() {
					// 		history.back();
					// 	},
					// })
				}
			})
	};
	/* 确认测评 */
	const onOk = () => {
		setShowSuccess(false);
		history.back();
	};
	/* 重新测评 */
	const onCancel = () => {
		onReset();
		setShowSuccess(false);
	};
	/* 仅关闭窗口 */
	const onClose = () => {
		setShowSuccess(false);
	};
	/* 重置表单测评 */
	const onReset = () => {
		form.resetFields();
		const {originalQuestionsList} = examData;
		const { assessAnswerList } = originalQuestionsList.find((question) => question.id === opportunityId)
		const defaultQuestion = assessAnswerList.find((question) => question.id === urlSearch.get('projectStageId'));
		form.setFieldValue(opportunityId, `${defaultQuestion.id}-${defaultQuestion.answerScore}`);
	};
	return <div className={'flex-sub margin-top-16' } >
		<Breadcrumb
			icon='icon-projectManage'
			list={[
				{
					name: '商机管理',
					link: '/businessOppty/opptyManage/list',
				},
				{
					name: '商机列表',
					link: '/businessOppty/opptyManage/list',
				},
			]}
			name='商机测评'
		/>
		<Card
			className={'margin-lr-20 margin-bottom-20'}
			bodyStyle={{padding: 0, backgroundImage: `url(${HeaderBg})`, backgroundSize: 'contain', backgroundRepeat: 'no-repeat', backgroundPosition: 'right center', borderRadius: 8}} >
			<Typography className={'padding-lr-20 padding-tb-40 width-50per font-size-20 border-radius-8'} style={{backgroundImage: `linear-gradient(to bottom right, #f0f4ff, #fbfdff, #ffffff)`}}>
				<Paragraph className={'font-size-20 font-bold'} >
					平台基于
					<Text strong className={'color-1c8dfe font-size-20 font-bold'}>
						需求度、客户画像、交付能力、客户决策认可度、竞争力、进展情况
					</Text>
					等6个影响商机成交可能性的重要维度及其子维度，研究出【商机智能测评模型】
				</Paragraph>
				<Paragraph className={'font-size-14 font-bold '} >
					请您根据商机的实际情况或进展回答下列问题，系统会利用模型自动测算出商机靠谱度，作为商机评价的参考。满分为100分。
				</Paragraph>
			</Typography>
		</Card>
		<Card
			className={'margin-lr-20 margin-bottom-20'}
			title={<div className={'flex flex-sub align-center'}>
				<div className={'padding-left-4'} style={{borderLeft: '8px solid #1c8dfe'}}>{examData.assessExamName}</div>
				<div className={'margin-left-20'}>
					正在测评的商机： {urlSearch?.get('projectOpportunityName')}
				</div>
			</div>}
		>
			<Form form={form} name="validate" >
				{
					examData.assessQuestionsList?.map((item, index) => {
						return <div key={item.id}>
							<div className={'margin-bottom-8'}>
								{number2Chinese(index + 1)}、{item.questionsCategoryName}
							</div>
							{
								item.children.map((ass, assIndex) => {
									return <div key={ass.id}>
										<div className={'margin-bottom-8'}>{assIndex + 1}、{ass.questionsName}</div>
										<div className={'margin-bottom-8'}>
											<SingleCheckBox
												options={ass.assessAnswerList}
												name={ass.id}
												rules={[{ required: true, message: `请选择${ass.questionsName}` }]}
												disabled={ass.id === opportunityId}
											/>
										</div>
									</div>
								})
							}
						</div>
					})
				}
			</Form>
		</Card>
		<Flex justify={"center"} gap={20} className={'padding-bottom-20'} >
			<Button size={'large'} className={'width-120 '} onClick={onReset}>重置</Button>
			<Button size={'large'} className={'width-120'} type={'primary'} onClick={onFinish}>确认提交</Button>
		</Flex>
		<Success open={showSuccess} result={resultData} onOk={onOk} onCancel={onCancel} onClose={onClose} />
	</div>;
};
export default Exam;