.exam-page {
	//background: ;
}

.exam-success {
	background-image: url('@/assets/images/Bidmgt/exam/result.png');
	background-size: cover;
	background-color: transparent;
	background-position: center;
	background-repeat: no-repeat;
	width: 380px;
	height: 420px;
	.exam-success-content {
		position: relative;
		height: 100%;
	}
	.ant-modal-content {
		background: transparent;
		border: none;
		box-shadow: none;
	}
	.retest-btn {
		width: 120px;
		font-size: 14px;
		line-height: 2.5em;
		border: 1px solid transparent;
		border-radius: 4px;
		cursor: pointer;
		box-shadow: 0 2px 0 rgba(0, 0, 0, 0.02);
		transition: all 0.2s cubic-bezier(0.645, 0.045, 0.355, 1);
		&:hover {
			border: 1px solid #0177ff;
			color: #0177ff;
		}
	}
	.submit-btn {
		margin-left: 20px;
		width: 120px;
		font-size: 14px;
		line-height: 2.5em;
		border: 1px solid transparent;
		background-color: #0177ff;
		color: #ffffff;
		border-radius: 4px;
		cursor: pointer;
		box-shadow: 0 2px 0 rgba(0, 0, 0, 0.02);
		transition: all 0.2s cubic-bezier(0.645, 0.045, 0.355, 1);
		&:hover {
			color: #fff;
			background: #4096ff;
		}
	}
	.text-overflow {
		overflow: hidden;
		text-overflow: ellipsis;
		white-space: nowrap;
	}
}
