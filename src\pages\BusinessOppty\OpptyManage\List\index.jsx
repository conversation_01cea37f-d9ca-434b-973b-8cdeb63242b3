import { Button, Col, Dropdown, Form, Input, message, Modal, Row, Select, Space, Switch, Table, Tooltip, Upload } from 'antd';
import { PlusOutlined, QuestionCircleFilled, ReloadOutlined, SearchOutlined } from '@ant-design/icons';
import React, { useEffect, useState } from 'react';
import { useRouterLink } from '@/hook/useRouter';
import {
	getProjectOpportunityProjectTypeStatistics,
	opportunityExport,
	opportunityImport,
	pageOpportunity,
	updateSuspendStatus,
	updateTopStatus,
} from '@/api/Opportunity/OpportunityManage/index';
import { getDeptData } from '@/utils/dictionary';
import { download, downloadFileByUrl } from '@/utils/common';
import { pageCategoryValue } from '@/api/Bidmgt/Dict/index';
import dayjs from 'dayjs';
import { useSelector } from 'react-redux';
import { useSearchParams } from 'react-router-dom';

let PageTabs = [
	{ label: '商机列表', value: undefined, key: 'all' },
	{ label: '未完成', value: 'undone', key: 'undone', ids: [] },
	{ label: '已完成', value: 'done', key: 'done', ids: [] },
];
const extraTabName = ['未完成', '已完成'];

const Index = () => {
	// 用户信息
	const userInfo = useSelector((state) => {
		return state.user.userInfo;
	});
	const deptIds = (userInfo.deptList || []).map((ov) => ov.id);
	const isManager = (userInfo.roleList || []).some((ov) => ov.roleCode == 'manager');
	const curUserId = userInfo.id || '';

	const [urlSearch, setUrlSearch] = useSearchParams();
	const urlChange = (params = {}) => {
		setUrlSearch(params, { replace: true });
	};

	const { linkTo } = useRouterLink();

	const [form] = Form.useForm();

	/* 激活的tabs */
	const [activeTab, setActiveTab] = useState(undefined);
	// 客户类型
	const [customTypeOptions, setCustomTypeOptions] = useState([]);
	// 商机进度
	const [opportunityStageOptions, setOpportunityStageOptions] = useState([]);
	// 商机类型
	const [opportunityTypeOptions, setOpportunityTypeOptions] = useState([]);
	// 产品分类
	const [productTypeOptions, setProductTypeOptions] = useState([]);
	/* 靠谱度选项 */
	const [reliabilityTypeOptions, setReliabilityTypeOptions] = useState([]);

	// 部门列表 请选择招商责任单位
	const [deptList, setDeptList] = useState([]);
	/* 统计 */
	const [statistics, setStatistics] = useState({
		expectChargeTotal: 0,
		opportunityCount: 0,
	});
	// 获取分类列表
	const getCategoryList = (categoryCode = '') => {
		return pageCategoryValue({
			categoryCode: categoryCode || '',
			pageNum: 1,
			pageSize: 300,
		});
	};

	useEffect(() => {
		Promise.all([
			getCategoryList('customer_type'),
			getCategoryList('opportunity_stage'),
			getCategoryList('opportunity_type'),
			getCategoryList('product_type'),
			getDeptData({ hasChild: false }),
			getCategoryList('reliability_type'),
		]).then((resList) => {
			if (resList[0].data && resList[0].data.records) {
				setCustomTypeOptions(
					(resList[0].data.records || []).map((ov) => {
						return {
							label: ov.value,
							value: ov.id || '',
						};
					})
				);
			}
			if (resList[1].data && resList[1].data.records) {
				const doneIds = [];
				const undoneIds = [];
				setOpportunityStageOptions(
					(resList[1].data.records || []).map((ov) => {
						if (ov.value === '已完成') {
							doneIds.push(ov.id);
						} else {
							undoneIds.push(ov.id);
						}
						return {
							label: ov.value,
							value: ov.id || '',
						};
					})
				);
				PageTabs = PageTabs.map((ov) => {
					if (ov.key === 'done' && ov.ids.length === 0) {
						return { ...ov, ids: doneIds };
					} else if (ov.key === 'undone' && ov.ids.length === 0) {
						return { ...ov, ids: undoneIds };
					}
					return ov;
				});
				console.log(PageTabs);
			}
			if (resList[2].data && resList[2].data.records) {
				setOpportunityTypeOptions(
					(resList[2].data.records || []).map((ov) => {
						return {
							label: ov.value,
							value: ov.id || '',
						};
					})
				);
			}
			if (resList[3].data && resList[3].data.records) {
				setProductTypeOptions(
					(resList[3].data.records || []).map((ov) => {
						return {
							label: ov.value,
							value: ov.id || '',
						};
					})
				);
			}
			if (resList[4]) {
				if (isManager) {
					setDeptList(resList[4] || []);
				} else {
					setDeptList(
						(userInfo.deptList || [])
							.filter((ov) => ov.id)
							.map((ov) => {
								return {
									label: ov.name || '',
									value: ov.id || '',
								};
							})
					);
				}
			}
			if (resList[5].data && resList[5].data.records) {
				setReliabilityTypeOptions(
					(resList[5].data.records || []).map((ov) => {
						return {
							label: ov.value,
							value: ov.id || '',
						};
					})
				);
			}
		});
	}, []);

	const [dataSource, setDataSource] = useState([]);
	const [total, setTotal] = useState(0);

	// 页码数据
	const [pagination, setPagination] = useState({
		pageNum: urlSearch.get('pageNum') || 1,
		pageSize: 10,
	});

	// 页码页数改变
	const changePage = (pageNum, pageSize) => {
		pagination.pageSize = pageSize;
		pagination.pageNum = pageNum;
		setPagination({ ...pagination });
	};

	// 选择
	const [selectedRowKeys, setSelectedRowKeys] = useState([]);
	const [selectedRowObj, setSelectedRowObj] = useState({});

	let sessionParams = {};
	// ============ 机构页面 单独有 的 start ================
	const initParams = () => {
		const projectOpportunityName = urlSearch.get('projectOpportunityName') || '';
		const projectHandlingDeptId = urlSearch.get('projectHandlingDeptId') || '';
		const projectTypeId = urlSearch.get('projectTypeId') || '';
		const customTypeId = urlSearch.get('customTypeId') || '';
		const productTypeId = urlSearch.get('productTypeId') || '';
		const projectStageId = urlSearch.get('projectStageId') || '';
		const projectReliabilityId = urlSearch.get('projectReliabilityId') || '';
		const sortWay = urlSearch.get('sortWay') || '';
		if (projectOpportunityName) {
			sessionParams.projectOpportunityName = projectOpportunityName;
		}
		if (projectHandlingDeptId) {
			sessionParams.projectHandlingDeptId = projectHandlingDeptId;
		}
		if (projectTypeId) {
			sessionParams.projectTypeId = projectTypeId;
		}
		if (customTypeId) {
			sessionParams.customTypeId = customTypeId;
		}
		if (productTypeId) {
			sessionParams.productTypeId = productTypeId;
		}
		if (projectStageId) {
			sessionParams.projectStageId = projectStageId;
		}
		if (projectReliabilityId) {
			sessionParams.projectReliabilityId = projectReliabilityId;
		}
		if (sortWay) {
			sessionParams.sortWay = sortWay;
		}
	};
	initParams();

	// if (sessionStorage.getItem('/businessOppty/opptyManage/list')) {
	// 	sessionParams = JSON.parse(
	// 		sessionStorage.getItem('/businessOppty/opptyManage/list')
	// 	)
	// 	sessionStorage.removeItem('/businessOppty/opptyManage/list')
	// }
	useEffect(() => {
		if (Object.keys(sessionParams).length) {
			form.setFieldsValue({
				...sessionParams,
				// projectHandlingDeptId: [
				// 	sessionParams.projectHandlingDeptId || '',
				// ],
			});
		}
	}, []);

	const [params, setParams] = useState({
		...sessionParams,
	});

	const getList = () => {
		const paramsData = {
			...params,
			/* 确保列表数据非暂停 */
			suspendStatus: 0,
		};
		if (!params?.projectStageIds?.length) {
			delete paramsData.projectStageIds;
		}
		if (!params?.projectStageId) {
			delete paramsData.projectStageId;
		}
		if (params.sortWay) {
			const arr = params.sortWay.split('-');
			paramsData.sortKey = arr[0] || '';
			paramsData.sortValue = arr[1] || '';
		}
		getTotal({
			pageNum: pagination.pageNum,
			pageSize: pagination.pageSize,
			...paramsData,
		});
		pageOpportunity({
			pageNum: pagination.pageNum,
			pageSize: pagination.pageSize,
			...paramsData,
		}).then((res) => {
			const listData = res.data.records || [];
			listData.forEach((ov) => {
				ov.isAuth = curUserId == ov.createBy || (deptIds.includes(ov.projectHandlingDeptId) && isManager);
			});

			setDataSource(listData);
			setTotal(res.data.total - 0);
		});
	};

	const getTotal = async (paramsData) => {
		const res = await getProjectOpportunityProjectTypeStatistics(paramsData);
		setStatistics(res?.data || {});
	};
	useEffect(() => {
		getList();
		urlChange({
			...params,
			...pagination,
		});
	}, [pagination, params]);

	const searchBtn = () => {
		const obj = form.getFieldsValue();
		const projectHandlingDeptId = obj.projectHandlingDeptId || '';
		// (obj.projectHandlingDeptId &&
		// 	obj.projectHandlingDeptId[
		// 	obj.projectHandlingDeptId.length - 1
		// 	]) ||
		// '';
		const projectOpportunityName = obj.projectOpportunityName || '';
		const projectTypeId = obj.projectTypeId || '';
		const customTypeId = obj.customTypeId || '';
		const productTypeId = obj.productTypeId || '';
		const projectStageId = obj.projectStageId || '';
		const projectStageIds = obj.projectStageIds || [];
		const projectReliabilityId = obj.projectReliabilityId || '';
		const sortWay = obj.sortWay || '';

		setParams({
			projectOpportunityName,
			projectHandlingDeptId,
			projectTypeId,
			customTypeId,
			productTypeId,
			projectStageId,
			projectStageIds,
			projectReliabilityId,
			sortWay,
		});
		changePage(1, pagination.pageSize);
		setSelectedRowKeys([]);
		setSelectedRowObj({});
	};

	const resetBtn = () => {
		form.resetFields();
		setParams({});
		changePage(1, pagination.pageSize);
		setSelectedRowKeys([]);
		setSelectedRowObj({});
	};

	// 导出
	const exportBtn = () => {
		opportunityExport(
			{
				...params,
				ids: [...selectedRowKeys],
				pageNum: 1,
				pageSize: 1000000,
			},
			{
				responseType: 'blob',
			}
		).then((res) => {
			download.excel(res, `商机-${dayjs().format('YYYYMMDD_HH:mm')}`);
			setSelectedRowKeys([]);
			setSelectedRowObj({});
		});
	};

	// 上传
	const beforeUpload = (e) => {
		if (e.type.includes('sheet')) {
			const file = new FormData();
			file.append('file', e);
			opportunityImport(file).then(() => {
				resetBtn();
			});
		} else {
			message.warning('请选择 excel 文件 上传');
		}
		return false;
	};

	// 改变置顶状态
	const changeTopStatus = (isTop, id) => {
		updateTopStatus({ id, topStatus: isTop ? 1 : 0 }).then(() => {
			message.success('置顶成功');
			getList();
		});
	};

	// 暂停
	const handelDel = (id) => {
		if (!id) {
			Modal.confirm({
				title: '确定要暂停选中的这些商机吗？',
				content: (
					<>
						暂停的商机会进入&nbsp;
						<a href="#">个人中心-商机回收站</a>
					</>
				),
				icon: <QuestionCircleFilled />,
				onOk() {
					batchDel(selectedRowKeys);
				},
			});
		} else {
			batchDel([id]);
		}
	};

	// 批量暂停
	const batchDel = (ids) => {
		updateSuspendStatus({
			ids,
		}).then(() => {
			setSelectedRowKeys([]);
			setSelectedRowObj({});
			const { pageNum, pageSize } = pagination;
			if (pageNum > 1 && pageNum > Math.ceil((total - ids.length) / pageSize)) {
				changePage(pagination.pageNum - 1, pagination.pageSize);
			} else {
				getList();
			}
		});
	};

	/* 行暂停 */
	const rowDel = (id) => {
		Modal.confirm({
			title: '确定要暂停这条商机吗？',
			content: (
				<>
					暂停的商机会进入&nbsp;
					<a href="#">个人中心-商机回收站</a>
				</>
			),
			icon: <QuestionCircleFilled />,
			onOk() {
				handelDel(id);
			},
		});
	};
	/* 切换tabs */
	const changeTab = (value) => {
		setActiveTab(value);
		if (value) {
			const ids = PageTabs.find((item) => item.value === value)?.ids;
			form.setFieldsValue({ projectStageIds: ids, projectStageId: '' });
		} else {
			form.setFieldsValue({ projectStageId: value, projectStageIds: [] });
		}
		searchBtn();
	};
	return (
		<div className="flex-sub flex flex-direction-column padding-20">
			<div className="padding-20 bg-color-ffffff border-radius-8 flex justify-between align-center line-height-24 margin-bottom-20">
				<div className={'font-size-16 flex flex-sub gap-40'}>
					{PageTabs.map((item) => {
						return (
							<div key={item.value} className={'flex align-center'} onClick={() => changeTab(item.value)}>
								<span
									className={`font-size-16 cursor-pointer font-weight-600 ${activeTab === item.value ? 'color-165dff' : 'color-1d2129'}`}
								>
									{item.label}
								</span>
							</div>
						);
					})}
				</div>
			</div>
			<div className="padding-20 flex-sub bg-color-ffffff border-radius-4">
				<div className="flex">
					<Form
						form={form}
						labelCol={{
							style: { width: '80px' },
						}}
						labelAlign="left"
						className="form-filter flex-sub"
					>
						<Row gutter={[20, 20]}>
							<Col span={8}>
								<Form.Item name="projectTypeId" label="商机类型">
									<Select
										allowClear
										showSearch
										className="flex-sub"
										placeholder="请选择商机类型"
										options={opportunityTypeOptions}
										filterOption={(input, option) => (option?.label ?? '').includes(input)}
									/>
								</Form.Item>
							</Col>
							<Col span={8}>
								<Form.Item name="customTypeId" label="客户类型">
									<Select
										allowClear
										showSearch
										className="flex-sub"
										placeholder="请选择客户类型"
										options={customTypeOptions}
										filterOption={(input, option) => (option?.label ?? '').includes(input)}
									/>
								</Form.Item>
							</Col>
							<Col span={8}>
								<Form.Item name="productTypeId" label="产品分类">
									<Select
										allowClear
										showSearch
										className="flex-sub"
										placeholder="请选择产品分类"
										options={productTypeOptions}
										filterOption={(input, option) => (option?.label ?? '').includes(input)}
									/>
								</Form.Item>
							</Col>
							<Col span={8}>
								<Form.Item name="projectHandlingDeptId" label="负责部门">
									{/* <Cascader
										className='flex-sub'
										options={deptList}
										placeholder='请选择负责部门'
										maxTagCount='responsive'
										changeOnSelect
									/> */}
									<Select
										allowClear
										showSearch
										className="flex-sub"
										placeholder="请选择负责部门"
										options={deptList}
										filterOption={(input, option) => (option?.label ?? '').includes(input)}
									/>
								</Form.Item>
							</Col>
							<Col span={8}>
								<Form.Item name={'projectStageId'} label="商机进度" hidden={activeTab}>
									<Select
										allowClear
										showSearch
										className="flex-sub"
										placeholder="请选择商机进度"
										options={opportunityStageOptions}
										filterOption={(input, option) => (option?.label ?? '').includes(input)}
									/>
								</Form.Item>
								<Form.Item name={'projectStageIds'} label="商机进度" hidden={!activeTab}>
									<Select
										mode={'multiple'}
										allowClear
										showSearch
										maxTagCount={'responsive'}
										className="flex-sub"
										placeholder="请选择商机进度"
										options={opportunityStageOptions}
										filterOption={(input, option) => (option?.label ?? '').includes(input)}
									/>
								</Form.Item>
							</Col>
							<Col span={8}>
								<Form.Item name="projectOpportunityName" label="商机关键词">
									<Input placeholder="请输入商机关键词" allowClear className="flex-sub" />
								</Form.Item>
							</Col>
							<Col span={8}>
								<Form.Item name="sortWay" label="排序方式">
									<Select
										allowClear
										showSearch
										className="flex-sub"
										placeholder="请选择排序方式"
										options={[
											{
												label: '跟进时间最新',
												value: 'followUpTime-desc',
											},
											{
												label: '创建时间最新',
												value: 'createTime-desc',
											},
											{
												label: '进度由高到低',
												value: 'stage-desc',
											},
											{
												label: '进度由低到高',
												value: 'stage-asc',
											},
											{
												label: '预测收费由高到低',
												value: 'expectCharge-desc',
											},
											{
												label: '预测收费由低到高',
												value: 'expectCharge-asc',
											},
										]}
										filterOption={(input, option) => (option?.label ?? '').includes(input)}
									/>
								</Form.Item>
							</Col>
							<Col span={8}>
								<Form.Item name="projectReliabilityId" label="商机靠谱度">
									<Select allowClear className="flex-sub" placeholder="请选择商机自评靠谱度" options={reliabilityTypeOptions} />
								</Form.Item>
							</Col>
						</Row>
					</Form>
					<div className="width-1 height-84 bg-color-e5e6eb flex-shirnk margin-lr-20"></div>
					<div className="flex-shirnk">
						<Button type="primary" icon={<SearchOutlined />} onClick={() => searchBtn()}>
							查询
						</Button>
						<div className="padding-10"></div>
						<Button type="default" icon={<ReloadOutlined />} onClick={() => resetBtn()}>
							重置
						</Button>
					</div>
				</div>
				<div className="height-1 bg-color-e5e6eb margin-tb-20"></div>
				<Space className="flex margin-bottom-20" size={12}>
					<div className={'border-radius-2 padding-10 border-f53f3f bg-color-ffffff font-bold color-f53f3f'}>
						商机数量：{statistics.opportunityCount}
					</div>
					<div className={'border-radius-2 padding-10 border-f53f3f bg-color-ffffff font-bold color-f53f3f'}>
						预计收费金额小计：{statistics.expectChargeTotal}万元
					</div>
				</Space>
				<Space size={12} className="flex align-center margin-bottom-20">
					<Button
						type="primary"
						icon={<PlusOutlined />}
						onClick={() => {
							linkTo('/businessOppty/opptyManage/curd');
						}}
					>
						新建
					</Button>
					<Dropdown
						menu={{
							items: [
								{
									key: '1',
									label: (
										<Upload maxCount={1} showUploadList={false} beforeUpload={beforeUpload}>
											<Button type="default">文件上传</Button>
										</Upload>
									),
								},
								{
									key: '2',
									label: (
										<Button
											type="default"
											onClick={() => {
												downloadFileByUrl(
													`${window.location.origin}${
														import.meta.env.VITE_BASE_PATH ? import.meta.env.VITE_BASE_PATH : '/'
													}商机批量导入模板20241219.xlsx`,
													'商机批量导入模板.xlsx'
												);
											}}
										>
											模板下载
										</Button>
									),
								},
							],
						}}
					>
						<Button>批量导入</Button>
					</Dropdown>
					<Button onClick={() => exportBtn()}>批量导出</Button>
					<Button onClick={() => handelDel()} disabled={selectedRowKeys.length === 0}>
						暂停
					</Button>
					{selectedRowKeys.length > 0 && <div>已选择 {selectedRowKeys.length} 条数据</div>}
				</Space>
				<Table
					size="small"
					rowKey="id"
					rowSelection={{
						selectedRowKeys,
						onChange: (checkedRowKeys) => {
							selectedRowObj[pagination.pageNum] = checkedRowKeys;
							setSelectedRowObj({ ...selectedRowObj });
							setSelectedRowKeys(Object.values(selectedRowObj).flat(2));
						},
						getCheckboxProps(record) {
							return {
								disabled: !record.isAuth,
							};
						},
					}}
					dataSource={dataSource}
					onChange={(e) => changePage(e.current, e.pageSize)}
					pagination={{
						...pagination,
						current: pagination.pageNum,
						total,
						size: 'default',
						showTotal: (total) => `共 ${total} 条`,
					}}
					scroll={{
						x: 1900,
					}}
				>
					<Table.Column title="商机名字" key="projectOpportunityName" dataIndex="projectOpportunityName" width={190} fixed="left" />
					<Table.Column title="商机/类型" key="projectTypeName" dataIndex="projectTypeName" width={110} />
					<Table.Column title="客户类型" key="customTypeName" dataIndex="customTypeName" width={110} />
					<Table.Column
						title="系统测评分"
						key="assessScore"
						dataIndex="assessScore"
						width={110}
						render={(assessScore, record) => {
							return (
								<Button
									type="link"
									size="small"
									disabled={!record.isAuth}
									onClick={() =>
										linkTo(
											`/businessOppty/opptyManage/exam?opportunityId=${record.id}&projectOpportunityName=${encodeURIComponent(
												record.projectOpportunityName
											)}&projectStageId=${record.projectStageId}`
										)
									}
								>
									{assessScore || '去测评'}
								</Button>
							);
						}}
					/>
					<Table.Column title="商机自评靠谱度" key="projectReliabilityName" dataIndex="projectReliabilityName" width={110} />
					<Table.Column
						title="预期合作产品"
						key="expectCooperateProduct"
						dataIndex="expectCooperateProduct"
						width={210}
						render={(text) => {
							return (
								<Space className={'text-cut width-100per'}>
									<Tooltip title={text}>
										<span>{text}</span>
									</Tooltip>
								</Space>
							);
						}}
					/>
					<Table.Column title="产品分类" key="productTypeName" dataIndex="productTypeName" width={110} />
					<Table.Column title="商机进度" key="projectStageName" dataIndex="projectStageName" width={110} />
					<Table.Column title="预测收费(万元)" key="expectCharge" dataIndex="expectCharge" width={110} />

					<Table.Column title="创建时间" key="createTime" dataIndex="createTime" width={120} />
					<Table.Column title="跟进进度" key="latestDevelopments" dataIndex="latestDevelopments" width={240} />
					<Table.Column
						title="跟进时间"
						key="followUpTime"
						dataIndex="followUpTime"
						width={120}
						render={(followUpTime, record) => {
							return followUpTime || '';
						}}
					/>
					<Table.Column title="商机负责人" key="projectHandlingName" dataIndex="projectHandlingName" width={110} />
					<Table.Column title="商机负责部门" key="projectHandlingDeptName" dataIndex="projectHandlingDeptName" width={110} />
					<Table.Column title="参与部门" key="partakeDept" dataIndex="partakeDept" width={110} />
					<Table.Column
						title="是否置顶"
						key="topStatus"
						dataIndex="topStatus"
						fixed="right"
						align="center"
						width={70}
						render={(topStatus, record) => {
							return (
								<Switch
									disabled={!record.isAuth}
									checked={topStatus === 1}
									onChange={(e) => {
										changeTopStatus(e, record.id);
									}}
								/>
							);
						}}
					/>
					<Table.Column
						title="操作"
						key="option"
						dataIndex="option"
						fixed="right"
						align="center"
						width={150}
						render={(_, record) => {
							return (
								<>
									<Button type="link" size="small" onClick={() => linkTo(`/businessOppty/opptyManage/detail?id=${record.id}`)}>
										跟进
									</Button>
									<Button
										type="link"
										size="small"
										disabled={!record.isAuth}
										onClick={() => linkTo(`/businessOppty/opptyManage/curd?id=${record.id}`)}
									>
										修改
									</Button>
									<Button
										type="link"
										danger
										size="small"
										disabled={!record.isAuth || record.suspendStatus === 1}
										onClick={() => rowDel(record.id)}
									>
										暂停
									</Button>
								</>
							);
						}}
					/>
				</Table>
			</div>
		</div>
	);
};

export default Index;
