import React, { useEffect, useState } from 'react';
import { Button, Table, Modal } from 'antd';
import { useRouterLink } from '@/hook/useRouter';
import { batchDelOpportunity, pageOpportunity, updateSuspendStatus } from '@/api/Opportunity/OpportunityManage';
import { useSelector } from 'react-redux';
import { QuestionCircleFilled } from '@ant-design/icons';
import { myOpportunityPage } from '@/api/Opportunity/PersonalCenter';
const RecycleBin = () => {
	const { linkTo } = useRouterLink();
	const [dataSource, setDataSource] = useState([]);
	const [total, setTotal] = useState(0);
	const [current, setCurrent] = useState(1);
	const [pageSize, setPageSize] = useState(10);
	/* 删除商机 */
	const deleteOpportunity = async (id) => {
		const res = await batchDelOpportunity({
			ids: [id],
		});
		if (!res) return;
		if (current > 1 && current > Math.ceil((total - 1) / pageSize)) {
			changePage(current - 1, pageSize);
		} else {
			getList();
		}
	};
	/* 恢复商机 */
	const recoverOpportunity = async (id) => {
		const res = await updateSuspendStatus({
			ids: [id],
		});
		if (!res) return;
		if (current > 1 && current > Math.ceil((total - 1) / pageSize)) {
			changePage(current - 1, pageSize);
		} else {
			getList();
		}
	};
	const confirmDelete = (id) => {
		Modal.confirm({
			title: '确认要删除这条信息吗？',
			content: '删除后不可恢复',
			okText: '确定',
			cancelText: '取消',
			icon: <QuestionCircleFilled />,
			onOk: () => {
				deleteOpportunity(id);
			},
		});
	};
	const columns = [
		{
			title: '商机名字',
			dataIndex: 'projectOpportunityName',
			key: 'projectOpportunityName',
			width: 190,
		},
		{
			title: '客户类型',
			dataIndex: 'customTypeName',
			key: 'customTypeName',
			width: 110,
		},
		{
			title: '商机自评靠谱度',
			dataIndex: 'projectReliabilityName',
			key: 'projectReliabilityName',
			width: 110,
		},
		{
			title: '预期合作产品',
			dataIndex: 'expectCooperateProduct',
			key: 'expectCooperateProduct',
			width: 110,
		},
		{
			title: '预测收费(万元)',
			dataIndex: 'expectCharge',
			key: 'expectCharge',
			width: 110,
		},
		{
			title: '创建时间',
			dataIndex: 'createTime',
			key: 'createTime',
			width: 110,
		},
		{
			title: '暂停时间',
			dataIndex: 'updateTime',
			key: 'updateTime',
			width: 110,
		},
		{
			title: '操作',
			key: 'action',
			width: 110,
			fixed: 'right',
			render: (text, record) => (
				<>
					<Button type="link" size="small" onClick={() => record.id && linkTo(`/businessOppty/opptyManage/detail?id=${record.id}`)}>
						查看
					</Button>
					<Button type={'link'} size={'small'} onClick={() => recoverOpportunity(record.id)}>
						重新启动
					</Button>
					<Button type={'link'} size={'small'} danger onClick={() => confirmDelete(record.id)}>
						删除
					</Button>
				</>
			),
		},
	];
	// 用户信息
	const userInfo = useSelector((state) => {
		return state.user.userInfo;
	});
	const deptIds = (userInfo.deptList || []).map((ov) => ov.id);
	const isManager = (userInfo.roleList || []).some((ov) => ov.roleCode === 'manager');
	const curUserId = userInfo.id || '';
	const changePage = (page, pageSize) => {
		setCurrent(page);
		setPageSize(pageSize);
		getList({
			pageNum: page,
			pageSize: pageSize,
		});
	};
	const getList = async (currentPageInfo) => {
		const paramsData = {
			/* 确保列表数据暂停 */
			suspendStatus: 1,
		};
		const res = await myOpportunityPage({
			pageNum: current,
			pageSize: pageSize,
			...paramsData,
			...currentPageInfo,
		});
		const listData = res.data.records || [];
		listData.forEach((ov) => {
			ov.isAuth = curUserId === ov.createBy || (deptIds.includes(ov.projectHandlingDeptId) && isManager);
		});
		setDataSource(listData);
		setTotal(+res.data.total);
	};
	useEffect(() => {
		getList();
	}, []);
	return (
		<Table
			rowKey="id"
			dataSource={dataSource}
			pagination={{
				pageSize,
				current,
				onChange: changePage,
				total,
				showTotal: (total) => `共 ${total} 条`,
			}}
			scroll={{
				x: 1900,
			}}
			columns={columns}
		/>
	);
};
export default RecycleBin;
