import { Col, Card, Row, Progress, Tabs, Table, Button, Tag, Avatar, Form, Input, Modal } from 'antd';
import { getImageSrc } from '@/assets/images/index';
import { QuestionCircleFilled, UserOutlined } from '@ant-design/icons';
import { useEffect, useState } from 'react';
import { useSelector, useDispatch } from 'react-redux';
import { myOpportunityPage, notificationPage, updateReadFlag, myStatistics, myProjectFollowPage } from '@/api/Opportunity/PersonalCenter/index';
import { useRouterLink } from '@/hook/useRouter';
import RecycleBin from '@/pages/BusinessOppty/PersonalCenter/components/RecycleBin';
import { updateSuspendStatus } from '@/api/Opportunity/OpportunityManage';
import { MessageType } from '@/pages/BusinessOppty/PersonalCenter/const';

const tabsItems = [
	{
		key: '1',
		label: '我的项目',
	},
	{
		key: '2',
		label: '追踪项目动态',
	},
	{
		key: '3',
		label: '商机回收站',
	},
];
// 我的消息
const TableNotifice = (props) => {
	const { linkTo } = useRouterLink();
	const [dataSource, setDataSource] = useState([]);
	const [total, setTotal] = useState(0);

	// 页码数据
	const [pagination, setPagination] = useState({
		pageNum: 1,
		pageSize: 10,
	});

	// 页码页数改变
	const changePage = (pageNum, pageSize) => {
		pagination.pageSize = pageSize;
		pagination.pageNum = pageNum;
		setPagination({ ...pagination });
	};

	const getList = () => {
		notificationPage({
			pageNum: pagination.pageNum,
			pageSize: pagination.pageSize,
		}).then((res) => {
			setDataSource(res.data.records || []);
			setTotal(res.data.total - 0);
		});
	};
	useEffect(() => {
		getList();
	}, [pagination]);

	return (
		<Table
			size="small"
			rowKey="id"
			dataSource={dataSource}
			pagination={{
				...pagination,
				total,
				showTotal: (total) => `共 ${total} 条`,
			}}
			onChange={(e) => changePage(e.current, e.pageSize)}
		>
			<Table.Column
				title="消息类型"
				key="noticeType"
				dataIndex="noticeType"
				width={80}
				render={(noticeType) => {
					return (
						<div>
							{noticeType == '1' && <Tag color="red">预警</Tag>}
							{noticeType == '2' && <Tag color="warning">催办</Tag>}
							{noticeType == '16' && <Tag color="processing">线索</Tag>}
							{noticeType == '17' && <Tag color="processing">客户认领</Tag>}
							{noticeType == 101 && <Tag color="processing">日报汇报</Tag>}
							{noticeType == 102 && <Tag color="processing">周报汇报</Tag>}
							{(noticeType > 200 && noticeType < 206) && <Tag color="processing">{MessageType[noticeType]}</Tag>}
						</div>
					);
				}}
			/>
			<Table.Column title="消息内容" key="noticeContent" dataIndex="noticeContent" />
			<Table.Column
				title="是否已读"
				key="readFlag"
				dataIndex="readFlag"
				width={80}
				render={(readFlag) => {
					return (
						<div>
							{readFlag == 1 && <Tag color="default">已读</Tag>}
							{readFlag == 0 && <Tag color="red">未读</Tag>}
						</div>
					);
				}}
			/>
			<Table.Column
				title="操作"
				key="option"
				dataIndex="option"
				align="center"
				width={120}
				fixed="right"
				render={(_, record) => {
					return (
						<>
							<Button
								type="link"
								size="small"
								onClick={() => {
									if (!record.id) return;
									if (record.noticeType === 16) {
										return linkTo(`/businessOppty/clueManage/clueDetail?id=${record.projectId}`);
									} else if (record.noticeType === 17) {
										return linkTo(`/businessOppty/customerManage/customerDetail?id=${record.projectId}`);
									} else if (record.noticeType === 101) {
										return linkTo(`/businessOppty/reportManage/myReport?id=${record.projectId}&activeTab=1`);
									} else if (record.noticeType === 102) {
										return linkTo(`/businessOppty/reportManage/myReport?id=${record.projectId}&activeTab=2`);
									} else if (record.noticeType === 201 || record.noticeType === 202 || record.noticeType === 203) {
										return linkTo(`/businessOppty/workHourManage/myWorkHour`);
									} else if (record.noticeType === 204) {
										return linkTo(`/businessOppty/workHourManage/departmentWorkHour?queryType=2`);
									} else if (record.noticeType === 205) {
										return linkTo(`/businessOppty/workHourManage/departmentWorkHour?queryType=3`);
									}
									return linkTo(`/businessOppty/opptyManage/detail?id=${record.projectId}`);
								}}
							>
								去处理
							</Button>
							{record.readFlag == 0 && (
								<Button
									type="link"
									size="small"
									onClick={() =>
										updateReadFlag({
											id: record.id || '',
											readFlag: 1,
										}).then(() => {
											getList();
										})
									}
								>
									已读
								</Button>
							)}
						</>
					);
				}}
			/>
		</Table>
	);
};

// 我的项目
const Table1 = () => {
	const { linkTo } = useRouterLink();
	const [dataSource, setDataSource] = useState([]);
	const [total, setTotal] = useState(0);

	// 页码数据
	const [pagination, setPagination] = useState({
		pageNum: 1,
		pageSize: 10,
	});

	// 页码页数改变
	const changePage = (pageNum, pageSize) => {
		pagination.pageSize = pageSize;
		pagination.pageNum = pageNum;
		setPagination({ ...pagination });
	};

	const getList = () => {
		myOpportunityPage({
			pageNum: pagination.pageNum,
			pageSize: pagination.pageSize,
		}).then((res) => {
			setDataSource(res.data.records || []);
			setTotal(res.data.total - 0);
		});
	};
	useEffect(() => {
		getList();
	}, [pagination]);

	/* 行暂停 */
	const rowDel = (id) => {
		Modal.confirm({
			title: '确定要暂停这条商机吗？',
			content: (
				<>
					暂停的商机会进入&nbsp;
					<a href="#">个人中心-商机回收站</a>
				</>
			),
			icon: <QuestionCircleFilled />,
			onOk() {
				batchDel([id]);
			},
		});
	};

	// 批量暂停
	const batchDel = (ids) => {
		updateSuspendStatus({
			ids,
		}).then(() => {
			// setSelectedRowKeys([]);
			// setSelectedRowObj({});
			const { pageNum, pageSize } = pagination;
			if (pageNum > 1 && pageNum > Math.ceil((total - ids.length) / pageSize)) {
				changePage(pagination.pageNum - 1, pagination.pageSize);
			} else {
				getList();
			}
		});
	};
	return (
		<Table
			rowKey="id"
			dataSource={dataSource}
			pagination={{
				...pagination,
				total,
				showTotal: (total) => `共 ${total} 条`,
			}}
			onChange={(e) => changePage(e.current, e.pageSize)}
			scroll={{
				x: 1300,
			}}
		>
			<Table.Column title="商机名字" key="projectOpportunityName" dataIndex="projectOpportunityName" width={190} />
			<Table.Column title="商机/类型" key="projectTypeName" dataIndex="projectTypeName" width={110} />
			<Table.Column title="客户类型" key="customTypeName" dataIndex="customTypeName" width={110} />
			<Table.Column title="商机自评靠谱度" key="projectReliabilityName" dataIndex="projectReliabilityName" width={110} />
			<Table.Column title="项目进度" key="projectStageName" dataIndex="projectStageName" width={110} />
			<Table.Column title="预测收费(万元)" key="expectCharge" dataIndex="expectCharge" width={110} />
			<Table.Column
				title="操作"
				key="option"
				dataIndex="option"
				align="center"
				width={110}
				fixed="right"
				render={(_, record) => {
					return (
						<>
							<Button type="link" size="small" onClick={() => record.id && linkTo(`/businessOppty/opptyManage/detail?id=${record.id}`)}>
								查看
							</Button>
							<Button type="link" size="small" onClick={() => linkTo(`/businessOppty/opptyManage/curd?id=${record.id}`)}>
								修改
							</Button>
							<Button type="link" danger size="small" disabled={record.suspendStatus === 1} onClick={() => rowDel(record.id)}>
								暂停
							</Button>
						</>
					);
				}}
			/>
		</Table>
	);
};

// 追踪项目动态
const Table2 = () => {
	const { linkTo } = useRouterLink();
	const [dataSource, setDataSource] = useState([]);
	const [total, setTotal] = useState(0);

	// 页码数据
	const [pagination, setPagination] = useState({
		pageNum: 1,
		pageSize: 10,
	});

	// 页码页数改变
	const changePage = (pageNum, pageSize) => {
		pagination.pageSize = pageSize;
		pagination.pageNum = pageNum;
		setPagination({ ...pagination });
	};

	const getList = () => {
		myProjectFollowPage({
			pageNum: pagination.pageNum,
			pageSize: pagination.pageSize,
		}).then((res) => {
			setDataSource(res.data.records || []);
			setTotal(res.data.total - 0);
		});
	};
	useEffect(() => {
		getList();
	}, [pagination]);
	return (
		<Table
			rowKey="id"
			dataSource={dataSource}
			pagination={{
				...pagination,
				total,
				showTotal: (total) => `共 ${total} 条`,
			}}
			onChange={(e) => changePage(e.current, e.pageSize)}
		>
			<Table.Column title="商机名字" key="projectOpportunityName" dataIndex="projectOpportunityName" width={190} />
			<Table.Column title="商机/类型" key="projectTypeName" dataIndex="projectTypeName" width={110} />
			<Table.Column title="客户类型" key="customTypeName" dataIndex="customTypeName" width={110} />
			<Table.Column title="项目进度" key="projectStageName" dataIndex="projectStageName" width={110} />
			<Table.Column title="预测收费(万元)" key="expectCharge" dataIndex="expectCharge" width={110} />

			<Table.Column title="项目最新动态" key="latestDevelopments" dataIndex="latestDevelopments" width={110} />
			<Table.Column
				title="操作"
				key="option"
				dataIndex="option"
				align="center"
				width={110}
				render={(_, record) => {
					return (
						<>
							<Button type="link" size="small" onClick={() => record.id && linkTo(`/businessOppty/opptyManage/detail?id=${record.id}`)}>
								查看
							</Button>
						</>
					);
				}}
			/>
		</Table>
	);
};

const Index = () => {
	const { linkTo } = useRouterLink();
	// 登录凭证
	const userInfo = useSelector((state) => {
		return state.user.userInfo;
	});

	const [tabsKey, setTabsKey] = useState('1');
	const onTabsChange = (key) => {
		console.log(key);
		setTabsKey(key);
	};

	const [notifice, setNotifice] = useState([]);
	const getNotifice = () => {
		notificationPage({
			pageNum: 1,
			pageSize: 3,
			readFlag: 0,
		}).then((res) => {
			setNotifice(res.data.records || []);
		});
	};

	const depList = (list = []) => {
		const loop = (ov, arr = []) => {
			arr = [...arr, ov.name];
			if (ov.children && ov.children.length) {
				ov.children.forEach((item) => {
					loop(item, arr);
				});
			}
			return arr;
		};
		return list.map((ov) => {
			return loop(ov).join('/');
		});
	};
	const [statistics, setStatistics] = useState({});
	const getStatistics = () => {
		myStatistics().then((res) => {
			setStatistics(res.data || {});
		});
	};
	useEffect(() => {
		getNotifice();
		getStatistics();
	}, []);

	// 阅读消息
	const readNotifice = (item = {}) => {
		updateReadFlag({
			id: item.id || '',
			readFlag: 1,
		}).then(() => {
			getNotifice();
			Modal.confirm({
				title: '消息内容',
				content: <div>{item.noticeContent || ''}</div>,
				okText: '去处理',
				onOk() {
					if (item.noticeType === 16) {
						return linkTo(`/businessOppty/clueManage/clueDetail?id=${item.projectId}`);
					} else if (item.noticeType === 17) {
						return linkTo(`/businessOppty/customerManage/customerDetail?id=${item.projectId}`);
					} else if (item.noticeType === 101) {
						return linkTo(`/businessOppty/reportManage/myReport?id=${item.projectId}&activeTab=1`);
					} else if (item.noticeType === 102) {
						return linkTo(`/businessOppty/reportManage/myReport?id=${item.projectId}&activeTab=2`);
					}
					item.projectId && linkTo(`/businessOppty/opptyManage/detail?id=${item.projectId}`);
				},
			});
		});
	};

	// 消息弹框
	const [modalFlag, setModalFlag] = useState('');
	return (
		<div className="flex-sub flex flex-direction-column padding-20">
			<Row gutter={[20]}>
				<Col span={14}>
					<div className="bg-color-ffffff border-radius-4 overflow-hidden height-180 border-box padding-tb-20 padding-lr-40">
						<div className="flex justify-start align-center">
							<div className="flex-shrink margin-right-40">
								<Avatar
									size={80}
									icon={
										<img
											src={userInfo.wxAvatarUrl || getImageSrc('@/assets/images/Public/defaultAvatar.png')}
											alt="arrow-down"
											className="width-106 height-106 border-radius-54"
										/>
									}
								/>
							</div>
							<Row gutter={[40, 16]} className="flex-sub ">
								<Col span={12}>
									<div className="color-86909c margin-bottom-12">用户名</div>
									<Input disabled value={userInfo.userName || '-'} />
								</Col>
								<Col span={12}>
									<div className="color-86909c margin-bottom-12">所属部门</div>
									<Input disabled value={userInfo.deptList && depList(userInfo.deptList).join(',')} />
								</Col>
							</Row>
						</div>
						<Row gutter={16} className="margin-top-20">
							<Col span={8} className="flex align-center justify-start">
								<div className="color-86909c width-60">我的线索</div>
								<Input className="flex-sub" readOnly value={statistics.clueCount || '-'} />
							</Col>
							<Col span={8} className="flex align-center justify-start">
								<div className="color-86909c width-60">我的商机</div>
								<Input className="flex-sub" readOnly value={statistics.opportunityCount || '-'} />
							</Col>
							<Col span={8} className="flex align-center justify-start">
								<div className="color-86909c width-60">预计收费</div>
								<Input className="flex-sub" readOnly value={`${statistics.expectCharge || '-'}万`} />
							</Col>
						</Row>
					</div>
				</Col>
				<Col span={10}>
					<div className="bg-color-ffffff border-radius-4 overflow-hidden height-180 border-box padding-20">
						<div className="flex align-center justify-start height-22 margin-bottom-16">
							<div className="font-size-16 font-weight-500">待办事项</div>
							<div className="font-size-14 font-weight-400 color-86909c">{/* （个） */}</div>
							<div
								className="flex-sub flex align-center justify-end font-size-12 color-165dff a"
								onClick={() => setModalFlag(`${new Date().valueOf()}`)}
							>
								<div>查看全部</div>
							</div>
						</div>
						{notifice.slice(0, 3).map((ov) => {
							return (
								<div className="flex justify-start align-center margin-bottom-12 a" key={ov.id} onClick={() => readNotifice(ov)}>
									{ov.noticeType == '1' && <Tag color="red">预警</Tag>}
									{ov.noticeType == '2' && <Tag color="warning">催办</Tag>}
									{ov.noticeType == '16' && <Tag color="processing">线索</Tag>}
									{ov.noticeType == '17' && <Tag color="processing">客户认领</Tag>}
									{ov.noticeType == 101 && <Tag color="processing">日报汇报</Tag>}
									{ov.noticeType == 102 && <Tag color="processing">周报汇报</Tag>}
									{ov.noticeType == 102 && <Tag color="processing">周报汇报</Tag>}
									{(ov.noticeType > 200 && ov.noticeType < 206) && <Tag color="processing">{MessageType[ov.noticeType]}</Tag>}
									<div className="flex-sub text-cut">{ov.noticeTitle || ''}</div>
								</div>
							);
						})}
						{!notifice.length && <div className="text-align-center line-height-88 color-aaaaaa">暂无待办事项</div>}
					</div>
				</Col>
			</Row>
			<div className="padding-lr-20 padding-tb-10 flex-sub bg-color-ffffff border-radius-4 margin-top-20">
				<Tabs defaultActiveKey="1" items={tabsItems} onChange={onTabsChange} />
				{tabsKey === '1' && <Table1 />}
				{tabsKey === '2' && <Table2 />}
				{tabsKey === '3' && <RecycleBin />}
			</div>
			<Modal
				title="全部消息"
				open={!!modalFlag}
				onOk={() => setModalFlag('')}
				onCancel={() => setModalFlag('')}
				afterClose={() => getNotifice()}
			>
				{modalFlag && <TableNotifice key={modalFlag} />}
			</Modal>
		</div>
	);
};

export default Index;
