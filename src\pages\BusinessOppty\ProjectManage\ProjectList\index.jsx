/**
 * @description index.jsx - 文件描述
 * <AUTHOR>
 *
 * Created on 2025/4/3 12:02
 */
import React, {useEffect, useMemo, useRef, useState} from 'react';
import {Button, Divider, Form, Input, message, Space, Table, DatePicker, Select, Modal, Row, Col, Badge} from "antd";
import {CloudDownloadOutlined, ImportOutlined, PlusOutlined, ReloadOutlined, SearchOutlined} from "@ant-design/icons";
import {getDeptData} from "@/utils/dictionary";
import {useRouterLink} from "@/hook/useRouter";
import {NAV_LIST, TEMPLATE_FILE_URL} from "./const";
import ModalForm from "@/components/ModalForm";
import ImportForm from "@/components/ImportForm";
import {importProjectData, pageProject} from "@/api/Opportunity/Project";

const {RangePicker} = DatePicker;
const { TextArea } = Input;

const ProjectList = () => {
    const {linkTo, searchParams} = useRouterLink();
    const ModalFormImportRef = useRef(null);
    const [form] = Form.useForm();
    // 部门列表
    const [departmentList, setDepartmentList] = useState([]);
    const [dataSource, setDataSource] = useState([]);
    const [navTabs, setNavTabs] = useState('projectList');
    const [pagination, setPagination] = useState({
        current: 1,
        pageSize: 10,
        total: 0,
    });
    // 作废原因
    const reasonRef = useRef(null);
    // 初始化获取数据
    useEffect(() => {
        const deptId = searchParams.get('deptId');
        if (deptId) {
            form.setFieldsValue({
                deptId
            });
        }
        getTableData();
        getDepartmentList();
    }, []);

    // 获取部门列表
    const getDepartmentList = async () => {
        const res = await getDeptData();
        if (res) {
            console.log(res)
            setDepartmentList(res);
        }
    }
    const exportData = () => {
        console.log('导出');
        message.warning('导出功能开发中...');
    }
    // 新建
    const addData = () => {
        console.log('新建');
        message.warning('新建功能开发中...');
        // linkTo('/businessOppty/ProjectManage/ProjectDetail');
    }
    // 重置
    const onReset = () => {
        form.resetFields();
        getTableData();
    }
    // 获取表格数据
    const getTableData = async (args = {}) => {
        const {
            clueName,
            clueSourceType,
            clueStatus,
            createTime,
            deptId,
        } = form.getFieldsValue();
        const params = {
            clueName,
            clueSourceType,
            clueStatus,
            startTime: createTime?.[0]?.format('YYYY-MM-DD') || '',
            endTime: createTime?.[1]?.format('YYYY-MM-DD') || '',
            deptIds: deptId ? [deptId] : undefined,
            pageNum: pagination.current,
            pageSize: pagination.pageSize,
            ...args
        };
        try {
            const res = await pageProject(params);
            if (res.data) {
                setDataSource(res.data.records);
                setPagination({
                    total: +res.data.total,
                    current: params.pageNum,
                    pageSize: params.pageSize,
                });
            }
        } catch (e) {
            console.log(e?.msg || '请求失败');
        }
    }
    // 修改页码
    const changePage = (page, pageSize) => {
        getTableData({
            pageNum: page,
            pageSize: pageSize
        });
    }
    // 切换tab
    const changeTab = (key) => {
        setNavTabs(key);
        getTableData();
    }
    // 编辑
    const editData = (record) => {
        message.warning('编辑功能开发中...');
        // linkTo(`/businessOppty/ProjectManage/ProjectDetail?id=${record.id}`);
    }

    // 作废
    const deleteData = async (record) => {
        Modal.confirm({
            title: '请输入作废的原因？',
            width: 500,
            content: <TextArea
                rows={4}
                ref={reasonRef}
                onChange={e => reasonRef.current.value = e.target.value}
                placeholder="请输入作废的原因"
            />,
            onOk: async () => {
                if (!reasonRef.current.value) {
                    message.warning('请输入作废的原因');
                    return Promise.reject();
                }
                const params = {
                    reason: reasonRef.current.value,
                    id: record.id,
                };
                // const res = await updateProjectClueCancel(params);
                // if (res) {
                //     message.success('作废成功');
                //     getTableData();
                // }
            },
        });
    }
    /* 导入数据 */
    const importData = (e) => {
        ModalFormImportRef.current.setOpen(true);
        ModalFormImportRef.current.setTitle('项目导入');
    };
    // 文件上传回调
    const handleUpload = async (file) => {
        console.log('上传文件', file);
        message.success('上传成功');
        const res = await importProjectData(file);
        if (res.data) {
            console.log('表格数据', res.data);
            message.success('导入成功，等待后台处理中...');
            // getTableData();
        } else {
            message.error('导入失败');
        }
    };
    const columns = [
        {
            title: '项目编码',
            dataIndex: 'id',
            key: 'id',
            align: 'center',
            width: 100,
            render: (id, record, index) => {
                return index + 1 + (pagination.current - 1) * pagination.pageSize;
            }
        },
        {
            title: '项目名称',
            dataIndex: 'projectName',
            key: 'projectName',
            align: 'center',
            width: 200,
        },
        {
            title: '项目类型',
            dataIndex: 'projectType',
            key: 'projectType',
            align: 'center',
            width: 200,
        },
        {
            title: '项目来源',
            dataIndex: 'originType',
            key: 'originType',
            align: 'center',
            width: 200,
        },
        {
            title: '项目负责人',
            dataIndex: 'projectLeader',
            key: 'projectLeader',
            align: 'center',
            width: 200,
        },
        {
            title: '项目负责部门',
            dataIndex: 'deptName',
            key: 'deptName',
            align: 'center',
            width: 200,
        },
        {
            title: '项目预算（万元）',
            dataIndex: 'projectBudget',
            key: 'projectBudget',
            align: 'center',
            width: 200,
        },
        {
            title: '项目起止时间',
            dataIndex: 'projectStartTime',
            key: 'projectStartTime',
            align: 'center',
            width: 200,
        },
        {
            title: '项目状态',
            dataIndex: 'status',
            key: 'status',
            align: 'center',
            width: 200,
            render: (status, info) => {
                return <Badge status={status} text={info?.statusName} />;
            }
        },
        {
            title: '操作',
            dataIndex: 'action',
            key: 'action',
            align: 'center',
            width: 200,
            fixed: 'right',
            render: (_, record) => {
                return (<Space>
                    <Button type={'link'} onClick={() => editData(record)}>查看/编辑</Button>
                    <Button type={'link'} danger onClick={() => deleteData(record)}>删除</Button>
                </Space>);
            }
        }
    ];
    return (<div className={'flex-sub flex flex-direction-column margin-20 padding-20  border-radius-4'}>
        <div
            className="padding-20 bg-color-ffffff border-radius-8 flex justify-between align-center line-height-24 margin-bottom-20">
            <div className={'font-size-16 flex flex-sub gap-40'}>
                {
                    NAV_LIST.map(item => {
                        return <div key={item.value} className={'flex align-center'}
                                    onClick={() => changeTab(item.value)}>
                            <span
                                className={`font-size-16 cursor-pointer font-weight-600 ${navTabs === item.value ? 'color-165dff' : 'color-1d2129'}`}>{item.label}</span>
                        </div>;
                    })
                }
            </div>
            <Space>
                <Button icon={<ImportOutlined />} onClick={importData}>导入</Button>
                <Button icon={<CloudDownloadOutlined/>} onClick={exportData}>导出</Button>
                <Button type={'primary'} icon={<PlusOutlined/>} onClick={addData}>新建</Button>
            </Space>
        </div>
        <div className={'bg-color-ffffff padding-20 border-radius-8  '}>
            <Form form={form} layout={'inline'}
                  className={'width-100per flex flex-sub align-start'}
            >
                <Row gutter={[20,20]} className={'flex-sub'}>
                    <Col span={8}>
                        <Form.Item label="项目名称/编码" name="projectName" className={'flex-sub '}>
                            <Input placeholder={'请输入项目名称或编码'} className={'width-100per'} allowClear/>
                        </Form.Item>
                    </Col>
                    <Col span={8}>
                        <Form.Item label="项目类型" name="projectType" className={'flex-sub '}>
                            <Select className={'width-100per'} placeholder={'请选择项目类型'} allowClear/>
                        </Form.Item>
                    </Col>
                    <Col span={8}>
                        <Form.Item label="项目阶段" name="projectStatus" className={'flex-sub '}>
                            <Select className={'width-100per'} placeholder={'请选择项目阶段'} allowClear/>
                        </Form.Item>
                    </Col>
                    <Col span={8}>
                        <Form.Item label="起止时间" name="rangeTime" className={'flex-sub '}>
                            <RangePicker className={'width-100per'} allowClear/>
                        </Form.Item>
                    </Col>
                    <Col span={8}>
                        <Form.Item label="所属公司" name="clueSourceType" className={'flex-sub '}>
                            <Select options={[]} className={'width-100per'} placeholder={'请选择所属公司'} allowClear/>
                        </Form.Item>
                    </Col>
                    <Col span={8}>
                        <Form.Item label="审批状态" name="status" className={'flex-sub '}>
                            <Select placeholder={'请选择审批状态'} className={'width-100per'} options={[]} allowClear/>
                        </Form.Item>
                    </Col>
                    <Col span={8}>
                        <Form.Item label="负责部门" name="dept" className={'flex-sub '}>
                            <Select placeholder={'请选择负责部门'} className={'width-100per'} options={[]} allowClear/>
                        </Form.Item>
                    </Col>
                    <Col span={8}>
                        <Form.Item label="项目负责人" name="user" className={'flex-sub '}>
                            <Select placeholder={'请选择项目负责人'} className={'width-100per'} options={[]} allowClear/>
                        </Form.Item>
                    </Col>
                </Row>
                {/*<Divider type="vertical" style={{margin: '0 16px'}}/>*/}
                <div className="width-1 height-84 bg-color-e5e6eb flex-shirnk margin-lr-20"/>
                <Form.Item noStyle>
                    <Space direction={'vertical'}>
                        <Button type={'primary'} htmlType={'submit'} icon={<SearchOutlined/>}
                                onClick={() => getTableData()}>查询</Button>
                        <Button htmlType={'reset'} icon={<ReloadOutlined/>} onClick={onReset}>重置</Button>
                    </Space>
                </Form.Item>
            </Form>
            <Divider/>
            <Table
                rowKey="id"
                columns={columns}
                scroll={{x: 1500}}
                pagination={{
                    ...pagination,
                    onChange: changePage,
                    showSizeChanger: true,
                    showTotal: (total) => `共 ${total} 条`
                }}
                dataSource={dataSource}
            />
        </div>
        {/* 导入 开始 */}
        <ModalForm
            ref={ModalFormImportRef}
            modelConfig={{
                styles: {
                    body: {
                        minHeight: 'unset',
                    },
                },
            }}
            onOk={handleUpload}
            FormComp={(props) => (
                <ImportForm
                    ref={props.FormCompRef}
                    fileName='file'
                    tplUrl={TEMPLATE_FILE_URL}
                />
            )}
        />
    </div>)
}
export default ProjectList;
