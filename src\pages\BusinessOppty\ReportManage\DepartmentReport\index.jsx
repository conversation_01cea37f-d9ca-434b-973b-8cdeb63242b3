/**
 * @description index.jsx - 文件描述
 * <AUTHOR>
 *
 * Created on 2025-08-04 上午 9:30
 */
import React, { useEffect, useState } from 'react';
import {
	MyReportTab,
	ReportStatus,
	ReportStatusText,
	ReportType,
} from '@/pages/BusinessOppty/ReportManage/MyReport/const';
import { Button, DatePicker, Form, Select, Space, Row, Col, Divider, Table, Typography, Tag } from 'antd';
import dayjs from 'dayjs';
import { queryWorkDate } from '@/api/Opportunity/WorkHourManage';
import { queryWorkReportToDepartment, workReportStatistics } from '@/api/Opportunity/ReportManage';
import { getByPermissionPerms } from '@/api/common';
import { getDeptData } from '@/utils/dictionary';
import TableCellExpand from '@/pages/BusinessOppty/ReportManage/MyReport/components/TableCellExpand';

const { Paragraph, Text } = Typography;
const DepartmentReport = () => {
	const [activeTab, setActiveTab] = useState(ReportType.DAILY);
	const [form] = Form.useForm();
	const [dataSource, setDataSource] = useState([]);
	// 周报数据
	const [weekDataSource, setWeekDataSource] = useState([]);
	// 请假数据
	const [leaveData, setLeaveData] = useState([]);
	// 假日数据
	const [holidayData, setHolidayData] = useState([]);
	const [columns, setColumns] = useState([]);
	// 周报表头
	const [weekColumns, setWeekColumns] = useState([]);
	// 中心部门列表
	const [centerDepartmentList, setCenterDepartmentList] = useState([]);
	// 中心人员名单
	const [centerStaffList, setCenterStaffList] = useState([]);
	// 所属公司列表
	const [companyList, setCompanyList] = useState([]);
	// 未填报人员列表
	const [leaveList, setLeaveList] = useState([]);
	// 请假人员列表
	const [noFillList, setNoFillList] = useState([]);
	// 统计数据
	const [statistics, setStatistics] = useState({
		totalNum: 0,
		leaveNum: 0,
		fillNum: 0,
		notFillNum: 0,
		notFillUser: [],
		leaveUser: [],
	});
	// 月份监控
	const watchMonth = Form.useWatch('reportYearMonth', form);
	useEffect(() => {
		queryHoliday();
		getCenterStaffList();
		getCenterDepartmentList();
		getCompanyList();
	}, []);
	useEffect(() => {
		queryReportData();
	}, [activeTab]);
	useEffect(() => {
		if (activeTab === ReportType.DAILY && holidayData.length > 0) {
			getDailyReportColumns(holidayData);
		} else if (activeTab === ReportType.WEEKLY && weekDataSource.length > 0) {
			getWeekReportColumns();
		}
	}, [holidayData, weekDataSource, activeTab, dataSource]);
	// 切换 tab
	const changeTab = (value) => {
		setActiveTab(value);
	};
	// 获取所属公司列表
	const getCompanyList = async () => {
		// todo: 获取所属公司列表接口
		console.log('所属公司列表');
	};
	// 获取中心人员名单
	const getCenterStaffList = async () => {
		const res = await getByPermissionPerms({ perms: 'businessOppty' }, { showLoading: false });
		if (res) {
			setCenterStaffList(
				res.data.map((ov) => ({
					label: ov.userName,
					value: ov.id,
				})),
			);
		}
	};
	// 获取中心部门列表
	const getCenterDepartmentList = async () => {
		const res = await getDeptData();
		if (res) {
			console.log('中心部门列表', res);
			setCenterDepartmentList(res);
		}
	};
	/* 查询假日信息 */
	const queryHoliday = async () => {
		const dateValue = form.getFieldValue('reportYearMonth');
		const res = await queryWorkDate({ year: dateValue?.format('YYYY') });
		if (res.data) {
			setHolidayData(res.data);
		}
	};
	/* 获取日报表头 */
	const getDailyReportColumns = (list) => {
		const dateValue = form.getFieldValue('reportYearMonth');
		const holidayList = list;
		const columns = [
			{
				title: '部门名称',
				dataIndex: 'departmentName',
				rowScope: 'row',
				fixed: 'left',
				width: 220,
				render: (text, record) => <div className={'max-width-200 ellipsis'}>{text}</div>,
			},
			{
				title: '员工姓名',
				dataIndex: 'employeeName',
				rowScope: 'row',
				fixed: 'left',
			},
			{
				title: '填报状态',
				fixed: 'left',
				children: [
					{
						title: '已填报',
						dataIndex: 'count',
						fixed: 'left',
					},
					{
						title: '未填报',
						dataIndex: 'noFillCount',
						fixed: 'left',
					},
					{
						title: '请假',
						dataIndex: 'leaveCount',
						fixed: 'left',
					},
				],
			},
		];
		// 获取本月假日信息
		const monthData = holidayList.find((item) => `${item.year}-${item.month}` === dateValue.format('YYYY-M'));
		// 工作日统计
		const workday = monthData?.days.reduce((pre, cur) => {
			const dayItem = {
				title: dayjs(cur.date).format('YYYY年M月D日'),
				dataIndex: cur.date,
				width: 300,
				render: (text, record) => {
					const { reportList = [] } = record;
					const reportItem = reportList.find((item) => item.reportDate === cur.date) || {};
					if (reportItem.reportStatus === 0) {
						return <span className={'color-ff0000'}>未填报</span>;
					} else if (reportItem.reportStatus === 2) {
						return <span className={'color-86909c'}>请假</span>;
					} else if (reportItem.reportType === 1) {
						const renderDom = (`【今日工作总结】
${reportItem?.currentContent || '暂无数据'}
【明日工作计划】
${reportItem?.futureContent || '暂无数据'}`);
						// 日报
						return (
							<TableCellExpand content={renderDom} />
						);
					}
				},
			};
			if (cur.type === 0) {
				// 工作日
				return pre.concat(dayItem);
			} else {
				return pre;
			}
		}, []);
		columns.push({ title: '日期', children: workday, align: 'left' });
		setColumns(columns);
	};
	/* 获取周报表头 */
	const getWeekReportColumns = () => {
		const columns = [
			{
				title: '部门名称',
				dataIndex: 'departmentName',
				rowScope: 'row',
				width: 220,
				render: (text, record) => <div className={'max-width-200 ellipsis'}>{text}</div>,
				fixed: 'left',
			},
			{
				title: '员工姓名',
				dataIndex: 'employeeName',
				rowScope: 'row',
				fixed: 'left',
			},
			{
				title: '填报状态',
				fixed: 'left',
				children: [
					{
						title: '已填报',
						dataIndex: 'count',
						fixed: 'left',
					},
					{
						title: '未填报',
						dataIndex: 'noFillCount',
						fixed: 'left',
					},
					{
						title: '请假',
						dataIndex: 'leaveCount',
						fixed: 'left',
					},
				],
			},
		];
		const reportList = weekDataSource[0]?.reportList || [];
		const weekColumns = reportList.reduce((pre, item) => {
			const weekItem = {
				title: `第${item.weekNum}周（${item.reportDate.slice(5)} ~ ${item.reportEndDate.slice(5)}）`,
				dataIndex: item.week,
				width: 300,
				render: (text, record) => {
					const { reportList = [] } = record;
					const reportItem = reportList.find((ov) => ov.reportDate === item.reportDate) || {};
					if (reportItem.reportStatus === 0) {
						return <span className={'color-ff0000'}>未填报</span>;
					} else if (reportItem.reportStatus === 2) {
						return <span className={'color-86909c'}>请假</span>;
					} else if (reportItem.reportType === 2) {
						const renderDom = (`【本周工作总结】
${reportItem?.currentContent || '暂无数据'}
【下周工作计划】
${reportItem?.futureContent || '暂无数据'}`);
						return (
							<TableCellExpand content={renderDom} />
						);
					}
				},
			};
			return pre.concat(weekItem);
		}, []);
		columns.push({ title: '日期', children: weekColumns, align: 'left' });
		setWeekColumns(columns);
	};
	/* 查询汇报数据 */
	const queryReportData = async () => {
		const values = form.getFieldsValue();
		const params = {
			...values,
			reportType: activeTab,
			reportYearMonth: values.reportYearMonth.format('YYYY-MM'),
			/*  查询端口：1 移动端 2 PC端 */
			queryPort: 2,
		};
		const res = await queryWorkReportToDepartment(params);
		const statisticRes = await workReportStatistics(params);
		if (res.data) {
			switch (activeTab) {
				case ReportType.DAILY:
					setDataSource(res.data);
					break;
				case ReportType.WEEKLY:
					setWeekDataSource(res.data);
					break;
			}
			const leaveList = [], noFillList = [];
			res.data.map(item => {
				if (item.leaveCount > 0) {
					leaveList.push(item);
				}
				if (item.noFillCount > 0) {
					noFillList.push(item);
				}
			});
			setLeaveList(leaveList);
			setNoFillList(noFillList);
		}
		if (statisticRes.data) {
			setStatistics(statisticRes.data);
		}
	};
	/* 表单重置 */
	const resetForm = () => {
		form.resetFields();
		queryReportData();
	};
	return (
		<div className={'flex-sub flex flex-direction-column margin-20'}>
			<div className='padding-20 bg-color-ffffff border-radius-8 flex flex-direction-column '>
				<div className={'font-size-16 flex flex-sub gap-40'}>
					{MyReportTab.map((item) => {
						return (
							<div key={item.value} className={'flex align-center'} onClick={() => changeTab(item.value)}>
								<span
									className={`font-size-16 cursor-pointer font-weight-600 ${activeTab === item.value ? 'color-165dff' : 'color-1d2129'}`}
								>
									{item.label}
								</span>
							</div>
						);
					})}
				</div>
			</div>
			<div
				className={'flex-sub flex flex-direction-column margin-top-20 padding-20 bg-color-ffffff border-radius-8'}>
				{/* 表单搜索 */}
				<div className={'flex justify-between align-start '}>
					<Form form={form} className={'flex-sub'} initialValues={{ reportYearMonth: dayjs(), queryType: 1 }}
						  labelCol={{ span: 6 }}>
						<Row gutter={[24, 0]}>
							<Col lg={12} xl={8}>
								<Form.Item name='reportYearMonth' label={'日期'}>
									<DatePicker picker='month' placeholder={'请选择日期'} style={{ width: '100%' }}
												allowClear={false} />
								</Form.Item>
								<Form.Item name='queryType' hidden>
									<Select showArrow placeholder={'queryType'} options={companyList} />
								</Form.Item>
							</Col>
							<Col lg={12} xl={8}>
								<Form.Item name='employeeId' label={'员工姓名'}>
									<Select showSearch placeholder={'请选择员工姓名'} options={centerStaffList} />
								</Form.Item>
							</Col>
							<Col lg={12} xl={8}>
								<Form.Item name='reportStatus' label={'填报状态'}>
									{/* 	汇报状态：0未填报 1 已填报 2请假  */}
									<Select
										placeholder={'请选择填报状态'}
										options={[
											{
												label: ReportStatusText[ReportStatus.FILLED],
												value: ReportStatus.FILLED,
											},
											{
												label: ReportStatusText[ReportStatus.UNFILLED],
												value: ReportStatus.UNFILLED,
											},
											{ label: ReportStatusText[ReportStatus.LEAVE], value: ReportStatus.LEAVE },
										]}
									/>
								</Form.Item>
							</Col>
						</Row>
					</Form>
					<Divider type={'vertical'} className={'height-32'} />
					<Space className={'flex justify-end'}>
						<Button type={'primary'} onClick={queryReportData}>
							查询
						</Button>
						<Button onClick={resetForm}>重置</Button>
					</Space>
				</div>
				<Divider />
				{/* 数据统计 */}
				<Space direction={'vertical'} className={'font-size-14'}>
					<div className={'flex flex-wrap align-center'}>
						<label>当月{activeTab === ReportType.DAILY ? '日报' : '周报'}统计：</label>
						<Tag>总人数{statistics.totalNum || 0}人</Tag>
						<Tag color={'success'}>已填报{ statistics.fillNum || 0 }人</Tag>
						<Tag color={'error'}>未填报{statistics.notFillNum || 0}人</Tag>
						<Tag color={'processing'}>请假{statistics.leaveNum || 0}人</Tag>
					</div>
					<div className={`flex flex-wrap gap-8 align-center ${statistics.notFillNum === 0 ? 'display-none' : ''}`}>
						<label>未填报：</label>
						{
							(statistics.notFillUser.slice(0, 5)).map(employeeName => (
								<Tag color={'error'}>{employeeName}</Tag>
							))
						}
						{
							statistics.notFillNum > 5 && (
								<Tag color={'error'}>等共{statistics.notFillNum - 5}人</Tag>
							)
						}
					</div>
					<div className={`flex flex-wrap gap-8 align-center ${statistics.leaveNum === 0 ? 'display-none' : ''}`}>
						<label>请假：</label>
						{
							statistics.leaveUser?.map(employeeName => (
								<Tag color={'processing'}>{employeeName}</Tag>
							))
						}
					</div>
				</Space>
				{/* 表格数据 */}
				<div className={'flex-sub margin-top-20'}>
					{activeTab === ReportType.DAILY && (
						<Table
							columns={columns}
							dataSource={dataSource}
							scroll={{ x: 'max-content' }}
							pagination={false}
							rowKey={'id'}
							size={'small'}
							bordered
						/>
					)}
					{activeTab === ReportType.WEEKLY && (
						<Table
							columns={weekColumns}
							dataSource={weekDataSource}
							scroll={{ x: 'max-content' }}
							pagination={false}
							rowKey={'id'}
							size={'small'}
							bordered
						/>
					)}
				</div>
			</div>
		</div>
	);
};
export default DepartmentReport;
