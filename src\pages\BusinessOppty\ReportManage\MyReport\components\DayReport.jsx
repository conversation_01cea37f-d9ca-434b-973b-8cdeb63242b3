/**
 * @description DayReport.jsx - 文件描述
 * <AUTHOR>
 *
 * Created on 2025-08-04 下午 4:27
 */
import React, { forwardRef, useImperativeHandle, useState } from 'react';
import { DatePicker, Form, Input, Modal, message } from 'antd';
import { addWorkReport, updateWorkReport } from '@/api/Opportunity/ReportManage';

const DayReport = (props, ref) => {
	const [open, setOpen] = useState(false);
	const [form] = Form.useForm();
	const { reSearch } = props;
	// 打开日报填写
	const handleOpen = (values) => {
		setOpen(true);
		form.setFieldsValue(values);
	};
	const handleCancel = () => {
		setOpen(false);
		form.resetFields();
	};

	useImperativeHandle(ref, () => ({
		handleOpen,
		handleSubmit,
	}));

	// 提交日报
	const handleSubmit = async () => {
		try {
			const values = await form.validateFields();
			console.log(values);
			const params = {
				...values,
				reportType: 1,
				reportDate: values.reportDate?.format('YYYY-MM-DD'),
				reportYearMonth: values.reportDate?.format('YYYY-MM'),
			};
			let res;
			if (params.id) {
				res = await updateWorkReport(params);
				message.success('日报保存成功');
			} else {
				res = await addWorkReport(params);
				message.success('日报添加成功');
			}
			if (res.data) {
				handleCancel();
				reSearch();
			}
		} catch (error) {
			throw new Error(error);
		}
	};
	return (
		<Modal open={open} title={'填写日报'} onCancel={handleCancel} onOk={handleSubmit} width={800}>
			<Form form={form} layout={'vertical'}>
				<Form.Item name="id" hidden>
					<Input />
				</Form.Item>
				<Form.Item label="日报日期" name="reportDate" required rules={[{ required: true, message: '请选择日报日期' }]}>
					<DatePicker className={'width-300'} disabled />
				</Form.Item>
				<Form.Item label="今日总结" name="currentContent" required rules={[{ required: true, message: '请填写今日总结' }]}>
					<Input.TextArea rows={4} />
				</Form.Item>
				<Form.Item label="明日计划" name="futureContent">
					<Input.TextArea rows={4} />
				</Form.Item>
			</Form>
		</Modal>
	);
};
export default forwardRef(DayReport);
