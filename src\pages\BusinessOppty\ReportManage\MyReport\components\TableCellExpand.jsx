/**
 * @description TableCellExpand.jsx - 表格单元格展开组件
 * <AUTHOR>
 *
 * Created on 2025-08-25 上午 11:46
 */
import React, { useState } from 'react';
import { Typography } from 'antd';
import PropTypes from 'prop-types';

const { Paragraph } = Typography;

/**
 * 表格单元格展开组件
 * 最多显示两行内容，多余部分可展开/收起
 *
 * @param {string} content - 要显示的内容
 * @param {number} rows - 最大显示行数，默认为2
 * @param {string} className - 自定义类名
 */
const TableCellExpand = ({ content, rows = 2, className = '' }) => {
    const [expanded, setExpanded] = useState(false);

    if (!content) {
		return <span>暂无数据</span>;
	}

	return (
		<Paragraph
			className={`${className} pre-wrap`}
			ellipsis={{
				rows,
				expandable: 'collapsible',
				// symbol: '展开',
                expanded,
                onExpand: (_, info) => setExpanded(info.expanded),
                tooltip: <Paragraph className={`${className} pre-wrap color-ffffff`}>{content}</Paragraph>,
			}}
		>
			{content}
		</Paragraph>
	);
};

TableCellExpand.propTypes = {
	content: PropTypes.string,
	rows: PropTypes.number,
	className: PropTypes.string,
};

export default TableCellExpand;
