/**
 * @description WeekReport.jsx - 文件描述
 * <AUTHOR>
 *
 * Created on 2025-08-04 下午 4:27
 */
import React, { forwardRef, useEffect, useImperativeHandle, useState } from 'react';
import { DatePicker, Form, Input, Modal, Select, message } from 'antd';
import { addWorkReport, listWorkReport, updateWorkReport } from '@/api/Opportunity/ReportManage';
import dayjs from 'dayjs';

const WeekReport = (props, ref) => {
	const [open, setOpen] = useState(false);
	const [form] = Form.useForm();
	const { currentDate, reSearch } = props;
	// 周配置列表
	const [weekList, setWeekList] = useState([]);
	useEffect(() => {
		getWeekList();
	}, [currentDate]);
	// 查询周配置列表
	const getWeekList = async () => {
		// 查询周配置列表的逻辑
		const params = {
			belongYearMonth: currentDate?.format('YYYY-MM'),
		};
		const res = await listWorkReport(params);
		const currentDay = dayjs();
		setWeekList(
			res.data.map((item) => ({
				label: `${currentDate?.format('YYYY年M月')} 第${item.rankingNum}周 (${item.startDate.slice(5)} ~ ${item.endDate.slice(5)})`,
				value: `${item.startDate}~${item.endDate}`,
				disabled: dayjs(item.startDate).isAfter(currentDay),
			}))
		);
	};
	// 打开日报填写
	const handleOpen = (params) => {
		setOpen(true);
		form.setFieldsValue(params);
	};
	const handleCancel = () => {
		setOpen(false);
	};
	useImperativeHandle(ref, () => ({
		handleOpen,
		handleSubmit,
	}));
	// 提交日报
	const handleSubmit = async () => {
		try {
			const { id, date, currentContent, futureContent } = await form.validateFields();
			const [reportDate, reportEndDate] = date.split('~');
			const params = {
				id,
				reportType: 2,
				reportDate,
				reportEndDate,
				currentContent,
				futureContent,
				reportYearMonth: currentDate?.format('YYYY-MM'),
			};
			let res;
			if (id) {
				res = await updateWorkReport(params);
				message.success('周报保存成功');
			} else {
				res = await addWorkReport(params);
				message.success('周报添加成功');
			}
			if (res.data) {
				handleCancel();
				reSearch();
			}
		} catch (error) {
			throw new Error(error);
		}
	};
	return (
		<Modal open={open} title={'周报填写'} onCancel={handleCancel} onOk={handleSubmit} width={800}>
			<Form form={form} layout={'vertical'}>
				<Form.Item name="id" hidden>
					<Input />
				</Form.Item>
				<Form.Item label="周报日期" name="date" required rules={[{ required: true, message: '请选择日报日期' }]}>
					<Select options={weekList} placeholder={'请选择周报日期'} />
				</Form.Item>
				<Form.Item label="本周进展" name="currentContent" required rules={[{ required: true, message: '请填写本周进展' }]}>
					<Input.TextArea rows={4} />
				</Form.Item>
				<Form.Item label="下周计划" name="futureContent" required rules={[{ required: true, message: '请填写下周计划' }]}>
					<Input.TextArea rows={4} />
				</Form.Item>
			</Form>
		</Modal>
	);
};
export default forwardRef(WeekReport);
