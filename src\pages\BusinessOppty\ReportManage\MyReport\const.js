/**
 * @description const - 文件描述
 * <AUTHOR>
 *
 * Created on 2025-08-04 上午 10:35
 */

// 汇报类型
export const ReportType = {
	DAILY: 1,
	WEEKLY: 2,
};

export const MyReportTab = [
	{ label: '日报', value: ReportType.DAILY, key: ReportType.DAILY },
	{ label: '周报', value: ReportType.WEEKLY, key: ReportType.WEEKLY },
];

// 汇报状态 0未填报 1 已填报 2请假
export const ReportStatus = {
	UNFILLED: 0,
	FILLED: 1,
	LEAVE: 2,
};
// 汇报状态颜色
export const ReportStatusColor = {
	[ReportStatus.UNFILLED]: '#ff0000',
	[ReportStatus.FILLED]: '#1c8dfe',
	[ReportStatus.LEAVE]: '#86909c',
};
// 汇报状态文本
export const ReportStatusText = {
	[ReportStatus.UNFILLED]: '未填报',
	[ReportStatus.FILLED]: '已填报',
	[ReportStatus.LEAVE]: '请假',
};

// 	角色类型 1 部门负责人 2 普通员工
export const RoleType = {
	DEPARTMENT_HEAD: 1,
	NORMAL_EMPLOYEE: 2,
};
