/**
 * @description index.jsx - 文件描述
 * <AUTHOR>
 *
 * Created on 2025-08-04 上午 9:29
 */
import React, { useEffect, useMemo, useRef, useState } from 'react';
import { Button, Calendar, Typography, DatePicker, Divider, Form, Space, Table, message, Tooltip, Badge, Modal, Tag } from 'antd';
import { CloudDownloadOutlined, PlusOutlined } from '@ant-design/icons';
import { MyReportTab, ReportStatus, ReportStatusColor, ReportStatusText, ReportType } from './const';
import dayjs from 'dayjs';
import { queryWorkDate } from '@/api/Opportunity/WorkHourManage';

import './index.scss';
import { HOLIDAY_TYPE } from '@/pages/BusinessOppty/WorkHourManage/CompleteWorkHour/const';
import DayReport from '@/pages/BusinessOppty/ReportManage/MyReport/components/DayReport';
import WeekReport from '@/pages/BusinessOppty/ReportManage/MyReport/components/WeekReport';
import { batchDelWorkReport, listWorkReport, queryMyWorkReport } from '@/api/Opportunity/ReportManage';
import TableCellExpand from '@/pages/BusinessOppty/ReportManage/MyReport/components/TableCellExpand';
import { useRouterLink } from '@/hook/useRouter';

const { Paragraph } = Typography;
const today = dayjs();
const MyReport = () => {
	const { searchParams } = useRouterLink();
	const [form] = Form.useForm();
	const dayRef = useRef(null);
	const weekRef = useRef(null);
	const [activeTab, setActiveTab] = useState(ReportType.DAILY);
	// 当前 日期
	const [currentDate, setCurrentDate] = useState(dayjs());
	// 假日信息
	const [holidayList, setHolidayList] = useState([]);
	// 请假记录
	const [leaveList, setLeaveList] = useState([]);
	// 日报数据
	const [dailyData, setDailyData] = useState([]);
	// 周报数据
	const [weekData, setWeekData] = useState([]);
	// 日报填报信息统计
	const [dailyReportData, setDailyReportData] = useState({
		workday: 0,
		fill: 0,
		notFill: 0,
		leave: 0,
	});
	// 周报填报信息统计
	const [weeklyReportData, setWeeklyReportData] = useState({
		workweek: 0,
		fill: 0,
		notFill: 0,
		leave: 0,
	});

	useEffect(() => {
		queryHoliday();
		const activeTab = searchParams.get('activeTab');
		if (activeTab) {
			setActiveTab(+activeTab);
		}
	}, []);

	useEffect(() => {
		queryMyReport();
	}, [activeTab]);
	const changeTab = (value) => {
		setActiveTab(value);
	};
	// 查询我的汇报
	const queryMyReport = async () => {
		// 处理周报数据，根据选择的月份生成周信息
		const selectDate = form.getFieldValue('date');
		const params = {
			reportType: activeTab,
			reportYearMonth: selectDate?.format('YYYY-MM'),
		};
		setCurrentDate(selectDate);
		const res = await queryMyWorkReport(params);
		if (res.data) {
			switch (activeTab) {
				case ReportType.DAILY:
					setDailyData(res.data);
					setLeaveList(res.data.filter(item => item.reportStatus === 2))
					updateStatistics(res.data);
					break;
				case ReportType.WEEKLY:
					setWeekData(res.data);
					const weekInfo = {
						workweek: 0,
						fill: 0,
						notFill: 0,
						leave: 0,
					};
					res.data.map((item) => {
						weekInfo.workweek++;
						switch (item.reportStatus) {
							case 1:
								weekInfo.fill++;
								break;
							case 2:
								weekInfo.leave++;
								break;
							case 0:
								weekInfo.notFill++;
								break;
						}
					});
					setWeeklyReportData(weekInfo);
					break;
			}
		}
	};

	/* 查询假日信息 */
	const queryHoliday = async () => {
		const dateValue = form.getFieldValue('date');
		const res = await queryWorkDate({ year: dateValue?.format('YYYY') });
		if (res.data) {
			// console.log(res.data);
			setHolidayList(res.data);
		}
	};
	// 更新统计信息
	const updateStatistics = (list) => {
		const dateValue = form.getFieldValue('date');
		// 当月假日信息统计
		// 获取本月假日信息
		const monthData = holidayList.find((item) => `${item.year}-${item.month}` === dateValue.format('YYYY-M'));
		const info = {
			workday: 0,
			fill: 0,
			notFill: 0,
			leave: 0,
		};
		monthData?.days.map((day) => {
			switch (day.type) {
				case HOLIDAY_TYPE.workday:
					info.workday++;
					break;
			}
		});
		list.forEach(item => {
			switch (item.reportStatus) {
				case 1:
					info.fill++;
					break;
				case 2:
					info.leave++;
					break;
				case 0:
					info.notFill++;
					break;
			}
		})
		setDailyReportData(info);
	};
	// 填报数据
	const addData = () => {
		console.log('填报数据');
		const params = {
			// reportDate: dayjs(),
		};
		weekRef.current?.handleOpen(params);
	};
	// 修改数据
	const updateData = (params) => {
		console.log('修改数据');
		weekRef.current?.handleOpen({
			id: params.id,
			date: `${params.reportDate}~${params.reportEndDate}`,
			currentContent: params.currentContent,
			futureContent: params.futureContent,
		});
	};
	// 删除数据
	const deleteData = async (params) => {
		console.log('删除数据');
		Modal.confirm({
			title: '确定删除该条数据？',
			content: '删除后无法恢复，确认删除吗？',
			onOk: async () => {
				const res = await batchDelWorkReport({ ids: [params.id] });
				if (res?.data) {
					message.success('删除成功');
					queryMyReport();
					return true;
				}
				return false;
			},
		});
	};
	// 导出数据
	const exportData = () => {
		console.log('导出数据');
	};
	// 点击日历单元格
	const onCellClick = ({ fillData, date, dayData, isRest }) => {
		let params = {
			...fillData,
			reportDate: date,
		};
		if (fillData?.id) {
			params.reportDate = dayjs(fillData.reportDate);
		}
		// 选中的月份与当前月份不一致
		if (!date.isSame(currentDate, 'month')) {
			return;
		}
		// 周末假日不需要填写
		if (isRest || dayData?.type === HOLIDAY_TYPE.restday || dayData?.type === HOLIDAY_TYPE.holiday) {
			message.warning('今天休息不需要填写');
			return;
		}
		// 不可以填写还没到的时间
		if (dayjs(date).startOf('day').isAfter(today, 'day')) {
			message.warning('还未到填写时间');
			return;
		}
		console.log(params);
		dayRef.current?.handleOpen(params);
	};
	// 日历单元格渲染
	const fullCellRender = useMemo(() => {
		return (value) => {
			const date = value;
			// 获取本月假日信息
			const monthData = holidayList.find((item) => `${item.year}-${item.month}` === date.format('YYYY-M'));
			// 获取对应当天的假日信息
			const dayData = monthData?.days?.find((item) => item.date === date.format('YYYY-MM-DD'));
			// 查询指定日期是否填写了日报
			const fillData = dailyData?.find((item) => item.reportDate === date.format('YYYY-MM-DD') && item.id);
			// 判断是否为休假
			const isRest = leaveList.some((item) => item.reportDate === date.format('YYYY-MM-DD'));
			const renderContent = () => {
				if (isRest) {
					return <div className={'color-ff9535'}>休假</div>;
				} else if (dayData?.type === HOLIDAY_TYPE.restday) {
					return <div className={'color-86909c'}>{dayData.weekDay === 6 ? '周六' : '周日'}</div>;
				} else if (dayData?.type === HOLIDAY_TYPE.holiday) {
					return <div className={'color-86909c'}>{dayData.typeDes}</div>;
				} else {
					return fillData ? (
						<Tooltip
							title={
								<div className={'flex flex-direction-column min-width-200'} onClick={(e) => e.stopPropagation()}>
									<div>今日总结：</div>
									<pre className={'pre-wrap '}>{fillData?.currentContent}</pre>
									<div className={'margin-top-20'}>明日计划：</div>
									<pre className={'pre-wrap '}>{fillData?.futureContent}</pre>
								</div>
							}
						>
							<span className={'color-1c8dfe'}>已填报</span>
						</Tooltip>
					) : (
						<div className={'color-ff0000'}>未填报</div>
					);
				}
			};
			return (
				<div className={'calendar-cell'} onClick={() => onCellClick({fillData, date: value, dayData, isRest})}>
					<div className={'flex justify-between align-center position-relative'}>
						<div className={'font-size-20 flex-sub font-weight-400 text-align-right'}>{date.format('D')}</div>
					</div>
					<div className={'font-weight-400 font-size-14'}>
						{currentDate.format('YYYY-MM') === date.format('YYYY-MM') ? renderContent() : null}
					</div>
				</div>
			);
		};
	}, [dailyData, holidayList, leaveList, currentDate]);

	// 周报表头
	const weeklyColumns = [
		{
			title: '日期',
			dataIndex: 'reportDate',
			key: 'reportDate',
			render: (text, record) => {
				return record.reportDate && record.reportEndDate
					? `第${record.weekNum}周 ${record.reportDate.slice(5)} ~ ${record.reportEndDate.slice(5)}`
					: '未配置';
			},
		},
		{
			title: '填报状态',
			dataIndex: 'reportStatus',
			key: 'reportStatus',
			render: (reportStatus, record) => {
				return <Badge color={ReportStatusColor[reportStatus]} text={ReportStatusText[reportStatus]} />;
				// return record.id ?
				//     <span className={'color-1c8dfe'}>已填报</span> :
				//     <span className={'color-ff0000'}>未填报</span>;
			},
		},
		{
			title: '本周进展',
			dataIndex: 'currentContent',
			key: 'currentContent',
			width: '30%',
			render: (text, record) => {
				return (
					<TableCellExpand content={text} />
				);
			},
		},
		{
			title: '下周计划',
			dataIndex: 'futureContent',
			key: 'futureContent',
			width: '30%',
			render: (text, record) => {
				return (
					<TableCellExpand content={text} />
				);
			},
		},
		{
			title: '操作',
			dataIndex: 'operation',
			key: 'operation',
			width: 150,
			render: (text, record) => {
				return (
					<Space>
						<Button type={'link'} size={'small'} onClick={() => updateData(record)}>
							{record.reportStatus === ReportStatus.FILLED ? '修改' : '填报'}
						</Button>
						<Button type={'link'} danger disabled={record.reportStatus !== ReportStatus.FILLED} size={'small'} onClick={() => deleteData(record)}>
							删除
						</Button>
					</Space>
				);
			},
		},
	];
	return (
		<div className={'flex-sub flex flex-direction-column margin-20'}>
			<div className="padding-20 bg-color-ffffff border-radius-8 flex justify-between align-center line-height-24 margin-bottom-20">
				<div className={'font-size-16 flex flex-sub gap-40'}>
					{MyReportTab.map((item) => {
						return (
							<div key={item.value} className={'flex align-center'} onClick={() => changeTab(item.value)}>
								<span
									className={`font-size-16 cursor-pointer font-weight-600 ${activeTab === item.value ? 'color-165dff' : 'color-1d2129'}`}
								>
									{item.label}
								</span>
							</div>
						);
					})}
				</div>
				<Space>
					{/*<Button icon={<CloudDownloadOutlined/>} onClick={exportData}>导出</Button>*/}
					{activeTab === ReportType.WEEKLY && (
						<Button type={'primary'} icon={<PlusOutlined />} onClick={addData}>
							填报
						</Button>
					)}
				</Space>
			</div>
			<div className={'flex-sub flex flex-direction-column padding-20 bg-color-ffffff border-radius-8'}>
				<div className={'flex justify-between align-center'}>
					<div className="flex-sub">
						<Form form={form} layout={'inline'} initialValues={{ date: dayjs() }}>
							<Form.Item name="date">
								<DatePicker picker={'month'} placeholder={'请选择查询日期'} />
							</Form.Item>
						</Form>
					</div>
					<Space>
						<Button type={'primary'} onClick={queryMyReport}>
							查询
						</Button>
					</Space>
				</div>
				<Divider />
				{/* 日报统计 */}
				{activeTab === ReportType.DAILY && (
					<div className={'flex flex-direction-column'}>
						<div className={'flex align-center font-size-14'}>
							<div>当月日报统计：</div>
							<Space>
								<Tag>工作日 {dailyReportData.workday} 天</Tag>
								{
									dailyReportData.fill > 0 &&
									<Tag color={'success'}>已填报 {dailyReportData.fill} 天</Tag>
								}
								{
									dailyReportData.notFill > 0 &&
									<Tag color={'error'}>未填报 {dailyReportData.notFill} 天</Tag>
								}
								{
									dailyReportData.leave > 0 &&
									<Tag color={'processing'}>请假 {dailyReportData.leave} 天</Tag>
								}
							</Space>
						</div>
						<Calendar
							fullscreen
							showWeek={false}
							className={'margin-top-20'}
							fullCellRender={fullCellRender}
							headerRender={() => null}
							value={currentDate}
							validRange={[currentDate.startOf('month'), currentDate.endOf('month')]}
						/>
					</div>
				)}
				{/* 周报统计 */}
				{activeTab === ReportType.WEEKLY && (
					<div className={'flex flex-direction-column'}>
						<div className={'flex align-center font-size-14'}>
							<div>当月周报统计：</div>
							<Space>
								<Tag>总计{weeklyReportData.workweek}周</Tag>
								{
									weeklyReportData.fill > 0 && (
										<Tag color={'success'}>已填报{weeklyReportData.fill}周</Tag>
									)
								}
								{
									weeklyReportData.notFill > 0 && (
										<Tag color={'error'}>未填报{weeklyReportData.notFill}周</Tag>
									)
								}
								{
									weeklyReportData.leave > 0 && (
										<Tag color={'processing'}>请假{weeklyReportData.leave}周</Tag>
									)
								}
							</Space>
						</div>
						<Table
							columns={weeklyColumns}
							dataSource={weekData}
							pagination={false}
							rowKey={'reportDate'}
							size={'small'}
							className={'margin-top-20'}
						/>
					</div>
				)}
			</div>
			<DayReport ref={dayRef} reSearch={queryMyReport} />
			<WeekReport ref={weekRef} reSearch={queryMyReport} currentDate={currentDate} />
		</div>
	);
};
export default MyReport;
