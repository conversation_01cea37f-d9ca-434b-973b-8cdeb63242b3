.calendar-cell {
	display: flex;
	flex-direction: column;
	margin: 0 4px;
	padding: 4px 8px 0;
	min-height: 100px;
	border-top: 2px solid rgba(5, 5, 5, 0.06);
	.calendar-cell-content {
		flex: 1;
		display: flex;
		flex-direction: column;
		overflow-y: auto;
	}
}
.calendar-cell:hover {
	background-color: #f5f5f5;
}
.calendar-cell-active {
	background-color: #e6f4ff;
	border-top: 2px solid #1677ff;
}

.weekly-report-summary {
	display: flex;
	align-items: center;
	gap: 20px;
	margin-bottom: 20px;
	flex-wrap: wrap;
}

.report-title {
	font-size: 16px;
	font-weight: 500;
	color: #165dff;
	white-space: nowrap;
}

.tag-container {
	display: flex;
	gap: 12px;
	flex-wrap: wrap;
}

:global(.summary-tag) {
	display: flex;
	align-items: baseline;
	gap: 4px;
	padding: 8px 16px;
	font-size: 14px;
	border-radius: 6px;
}

:global(.tag-label) {
	margin-right: 4px;
}

:global(.tag-value) {
	font-weight: 500;
	font-size: 16px;
}

:global(.tag-unit) {
	margin-left: 2px;
	font-size: 12px;
}

@media (max-width: 768px) {
	.weekly-report-summary {
		flex-direction: column;
		align-items: flex-start;
	}

	.tag-container {
		width: 100%;
	}
}
