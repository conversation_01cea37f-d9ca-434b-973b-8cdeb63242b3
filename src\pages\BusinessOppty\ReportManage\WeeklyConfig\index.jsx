/**
 * @description index.jsx - 文件描述
 * <AUTHOR>
 *
 * Created on 2025-08-04 上午 9:32
 */
import React, { useEffect, useState } from 'react';
import { Button, DatePicker, Form, Input, InputNumber, message, Modal, Select, Space, Table } from 'antd';
import { PlusOutlined, SearchOutlined } from '@ant-design/icons';
import { addWeekConfig, listWorkReport, updateWeekConfig, batchDel } from '@/api/Opportunity/ReportManage';
import dayjs from 'dayjs';
import { WeekOptions } from '@/pages/BusinessOppty/ReportManage/WeeklyConfig/const';

const WeeklyConfig = () => {
	const [form] = Form.useForm();
	// 列表所属年月
	const [month, setMonth] = useState(dayjs());
	const [isModalOpen, setIsModalOpen] = useState(false);
	// 操作数据修改
	const [curRow, setCurRow] = useState({});
	// 数据列表
	const [dataSource, setDataSource] = useState([]);

	useEffect(() => {
		getList();
	}, []);
	// 查询列表
	const getList = async () => {
		const params = {
			belongYearMonth: month?.format('YYYY-MM'),
		};
		const res = await listWorkReport(params);
		if (res.data) {
			setDataSource(res.data);
		}
	};
	// 删除
	const deleteItem = async (record) => {
		Modal.confirm({
			title: '删除周配置',
			content: `确定要删除这条数据吗？`,
			onOk: async () => {
				const res = await batchDel({ ids: [record.id] });
				if (res.data) {
					message.success('删除成功');
					getList();
					return Promise.resolve();
				} else {
					message.error('删除失败');
					return Promise.reject();
				}
			},
		});
	};
	// 修改
	const updateItem = (record) => {
		setCurRow(record);
		form.setFieldsValue({
			...record,
			date: [dayjs(record.startDate), dayjs(record.endDate)],
			belongYearMonth: dayjs(record.belongYearMonth),
			rankingNum: record.rankingNum,
		});
		setIsModalOpen(true);
	};
	// 新增
	const addItem = () => {
		setCurRow({});
		setIsModalOpen(true);
	};
	// 新增/修改弹窗
	const handleOk = async () => {
		try {
			const { belongYearMonth, date, id, rankingNum } = await form.validateFields();
			const params = {
				id: id,
				belongYearMonth: belongYearMonth.format('YYYY-MM'),
				rankingNum,
				startDate: date[0].format('YYYY-MM-DD'),
				endDate: date[1].format('YYYY-MM-DD'),
			};
			let res;
			if (id) {
				res = await updateWeekConfig(params);
			} else {
				res = await addWeekConfig(params);
			}
			if (res.data) {
				message.success(id ? '修改成功' : '添加成功');
				setIsModalOpen(false);
				form.resetFields();
				getList();
			}
		} catch (e) {
			const errors = e?.errorFields.map(({ errors }) => errors) || e;
			throw new Error(errors);
		}
	};
	// 关闭弹窗
	const handleCancel = () => {
		setIsModalOpen(false);
		form.resetFields();
	};
	// 表格列配置
	const columns = [
		{
			title: '序号',
			dataIndex: 'index',
			key: 'index',
			width: 110,
			render: (_, record, index) => {
				return index + 1;
			},
		},
		{
			title: '月份',
			dataIndex: 'belongYearMonth',
			key: 'belongYearMonth',
		},
		{
			title: '日期',
			dataIndex: 'date',
			key: 'date',
			render: (_, record) => {
				return `${record.startDate} ~ ${record.endDate}`;
			},
		},
		{
			title: '第几周',
			dataIndex: 'rankingNum',
			key: 'rankingNum',
		},
		{
			title: '操作',
			key: 'action',
			width: 220,
			render: (_, record) => {
				return (
					<Space>
						<Button size={'small'} type="link" onClick={() => updateItem(record)}>
							修改
						</Button>
						<Button size={'small'} danger type="link" onClick={() => deleteItem(record)}>
							删除
						</Button>
					</Space>
				);
			},
		},
	];
	return (
		<div className={'flex-sub flex flex-direction-column padding-20'}>
			<div className="padding-20 flex-sub bg-color-ffffff border-radius-4">
				<div className="font-size-16 font-weight-500 color-1d2129 line-height-24 margin-bottom-20">周配置管理</div>
				<div className="flex justify-between align-center margin-bottom-20">
					<div className="flex-sub">
						<Button type="primary" icon={<PlusOutlined />} onClick={addItem}>
							新建
						</Button>
					</div>
					<DatePicker picker={'month'} placeholder="请选择月份" className="width-280 margin-right-14" onChange={setMonth} value={month} />
					<Button type="primary" icon={<SearchOutlined />} onClick={() => getList()}>
						查询
					</Button>
				</div>
				<Table columns={columns} dataSource={dataSource} pagination={false} scroll={{ x: 'max-content' }} rowKey={'id'} />
			</div>
			<Modal title={curRow?.id ? '修改周配置' : '新增周配置'} open={isModalOpen} onCancel={handleCancel} onOk={handleOk}>
				<Form form={form} layout={'vertical'}>
					<Form.Item name={'id'} hidden>
						<Input />
					</Form.Item>
					<Form.Item label={'所属年月'} name={'belongYearMonth'} required={true} rules={[{ required: true, message: '请选择所属年月' }]}>
						<DatePicker picker={'month'} placeholder={'请选择所属年月'} />
					</Form.Item>
					<Form.Item label={'日期'} name={'date'} required={true} rules={[{ required: true, message: '请选择所属年月' }]}>
						<DatePicker.RangePicker placeholder={'请选择所属年月'} />
					</Form.Item>
					<Form.Item label={'第几周'} name={'rankingNum'} required={true} rules={[{ required: true, message: '请选择第几周' }]}>
						<Select placeholder={'请选择第几周'} options={WeekOptions} />
					</Form.Item>
				</Form>
			</Modal>
		</div>
	);
};
export default WeeklyConfig;
