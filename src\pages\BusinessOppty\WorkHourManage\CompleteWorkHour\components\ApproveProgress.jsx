/**
 * @description ApproveProgress.jsx - 文件描述
 * <AUTHOR>
 *
 * Created on 2025-07-17 下午 4:50
 */
import React, { forwardRef, useEffect, useImperativeHandle, useState } from 'react';
import { Modal, Space, Steps } from 'antd';

const ApproveProgress = ({ detail, userInfo }, ref) => {
	const [open, setOpen] = useState(false);
	const [steps, setSteps] = useState([]);

	/* 关闭弹框 */
	const closeModal = () => {
		setOpen(false);
	};
	/* 打开弹框 */
	const openModal = (record) => {
		setOpen(true);
	};

	useImperativeHandle(ref, () => ({
		openModal: openModal,
	}));

	useEffect(() => {
		if (open) {
			const {status: approveStatus, projectList} = detail;
			// 部门负责人信息
			const {deptList} = userInfo || {};
			const {commanders} = deptList[0] || {};
			const steps = [
				{title: '待提交工时', status: 'wait'},
				{title: '待部门审核', status: 'wait'},
				{title: '待项目审核', status: 'wait'},
				{title: '审批完成', status: 'wait'},
			];

			// 根据审批状态设置步骤
			if (approveStatus === 0) {
				// 暂存状态
				steps[0] = {title: '待提交工时', status: 'process'};
			} else if (approveStatus === 1) {
				// 待部门审批
				steps[0] = {title: '已提交工时', status: 'finish'};
				steps[1] = {title: '待部门审核', status: 'process'};
			} else if (approveStatus === 2) {
				// 部门已拒绝
				steps[0] = {title: '已提交工时', status: 'finish'};
				steps[1] = {title: '部门审核不通过', status: 'error'};
			} else if (approveStatus === 3) {
				// 待项目审批
				steps[0] = {title: '已提交工时', status: 'finish'};
				steps[1] = {title: '部门审核通过', status: 'finish'};
				steps[2] = {title: '待项目审核', status: 'process'};
			} else if (approveStatus === 4) {
				// 已通过
				steps[0] = {title: '已提交工时', status: 'finish'};
				steps[1] = {title: '部门审核通过', status: 'finish'};
				steps[2] = {title: '项目审核通过', status: 'finish'};
				steps[3] = {title: '审批完成', status: 'finish'};
			} else if (approveStatus === 5) {
				// 项目不通过
				steps[0] = {title: '已提交工时', status: 'finish'};
				steps[1] = {title: '部门审核通过', status: 'finish'};
				steps[2] = {title: '项目审核不通过', status: 'error'};
			}

			// 添加部门审核描述
			if (commanders && commanders.length) {
				if (steps[1]) {
					steps[1].description = (
						<Space direction={'vertical'}>
							{commanders.map((item, index) => {
								return <div key={item.id}>
									审批人{commanders.length > 1 ? index + 1 : ''}：{item.userName}
								</div>
							})}
						</Space>
					);
				}
			}

			// 添加项目审核描述
			if (projectList && projectList.length) {
				if (steps[2]) {
					steps[2].description = (
						<Space direction={'vertical'}>
							{projectList.map((item, index) => {
								return <div key={item.projectId} className={`${item.reject ? 'color-ff4d4f' : 'color-86909c'}`}>
									项目：{item.projectName}
								</div>
							})}
						</Space>
					);
				}
			}

			setSteps(steps);
		}
	}, [detail, open]);

	return (
		<Modal width={600} open={open} title={'审批流程'} onCancel={closeModal} footer={null}>
			<div className={'padding-tb-20'}>
				<Steps
					direction="vertical"
					size="small"
					current={detail?.approvalStatus}
					items={steps}
				/>
			</div>
		</Modal>
	);
};
export default forwardRef(ApproveProgress);
