/**
 * @description ApproveProgress.jsx - 文件描述
 * <AUTHOR>
 *
 * Created on 2025-07-17 下午 4:50
 */
import React, {forwardRef, useImperativeHandle, useState} from 'react';
import {Modal, Steps} from "antd";

const ApproveProgress = (props, ref) => {
    const [open, setOpen] = useState(false);

    /* 关闭弹框 */
    const closeModal = () => {
        setOpen(false);
    }
    /* 打开弹框 */
    const openModal = (record) => {
        setOpen(true);
    }

    useImperativeHandle(ref, () => ({
        openModal: openModal,
    }))

    return (<Modal
        width={600}
        open={ open}
        title={'审批流程'}
        onCancel={closeModal}
        footer={ null}
    >
        <div className={'padding-tb-20'}>
            <Steps
                direction="vertical"
                size="small"
                current={4}
                items={[
                    {
                        title: '已提交',
                        description: '2025年7月17日17:06:20',
                    },
                    {
                        title: '待部门负责人审批',
                        description: '审批人：张三',
                    },
                    {
                        title: '待项目负责人审批',
                        description: <ul>
                            <li>审批人：乔木</li>
                            <li>审批人：王五</li>
                            <li>审批人：张三</li>
                        </ul>,
                    },
                    {
                        title: '审批完成',
                        description: '2025年7月17日17:09:19',
                    },
                ]}
            />
        </div>
    </Modal>)
}
export default forwardRef(ApproveProgress);
