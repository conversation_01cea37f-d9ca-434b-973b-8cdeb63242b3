/**
 * @description ApproveProject.jsx - 文件描述
 * <AUTHOR>
 *
 * Created on 2025-07-30 上午 10:17
 */
import React, { forwardRef, useImperativeHandle, useState } from 'react';
import { Form, Input, Modal, Radio } from 'antd';

const ApproveProject = (props, ref) => {
	const [form] = Form.useForm();
	const [open, setOpen] = useState(false);
	// 审批数据
	const [approveList, setApproveList] = useState([]);
	// 打开审核
	const openApprove = (approveList) => {
		setOpen(true);
		setApproveList(approveList);
	};
	// 关闭审核
	const closeApprove = () => {
		setOpen(false);
	};
	// 提交审核
	const submitApprove = async () => {
		// 提交审核逻辑
		try {
			const values = await form.validateFields();
			const params = {
				...values,
			};
			const list = approveList.map((item) => {
				return {
					...item,
					...values,
				};
			});
			props?.submit(list);
		} catch (e) {
			throw new Error('表单验证失败', e);
		}
	};
	// 暴露给父组件的方法
	useImperativeHandle(ref, () => ({
		openApprove,
		submitApprove,
		closeModal: closeApprove,
	}));

	return (
		<Modal title="项目工时审核" width={800} open={open} onCancel={closeApprove} onOk={submitApprove}>
			<Form form={form} layout={'horizontal'} labelCol={{ span: 6 }}>
				<Form.Item label="审批结果" name={'approvalStatus'} required rules={[{ required: true, message: '请选择审批结果' }]}>
					<Radio.Group>
						<Radio value={4}>通过</Radio>
						<Radio value={5}>拒绝</Radio>
					</Radio.Group>
				</Form.Item>
				<Form.Item label="备注" name={'projectApproveRemark'}>
					<Input.TextArea placeholder="请输入" rows={4} />
				</Form.Item>
			</Form>
		</Modal>
	);
};
export default forwardRef(ApproveProject);
