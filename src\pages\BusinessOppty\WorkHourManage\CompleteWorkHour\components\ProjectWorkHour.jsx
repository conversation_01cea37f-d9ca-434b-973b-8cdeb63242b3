/**
 * @description ProjectWorkHour.jsx - 文件描述
 * <AUTHOR>
 *
 * Created on 2025-07-14 下午 4:37
 */
import React, { forwardRef, useImperativeHandle, useMemo, useState } from 'react';
import { Form, Input, Modal, Radio, Select, Space } from 'antd';
import dayjs from 'dayjs';

const ProjectWorkHour = ({ projectList, onSubmit, project }, ref) => {
	const [open, setOpen] = useState(false);
	const [form] = Form.useForm();
	const [record, setRecord] = useState({});

	/* 提交填报 */
	const handleSubmit = async () => {
		try {
			const values = await form.validateFields();
			console.log('values', values);
			onSubmit({
				...record,
				...values,
			});
			setOpen(false);
			form.resetFields();
			setRecord({});
		} catch (e) {
			console.error(e);
			return false;
		}
	};
	/* 取消填报 */
	const handleCancel = () => {
		setOpen(false);
	};

	const openModal = (record) => {
		setOpen(true);
		setRecord(record);
		if (record?.batch) {
			form.resetFields();
		} else {
			form.setFieldsValue({
				// projectId: record?.projectId,
				morning: record?.morning,
				afternoon: record?.afternoon,
				morningId: record?.morningId,
				afternoonId: record?.afternoonId,
			});
		}
	};

	useImperativeHandle(ref, () => ({
		openModal: openModal,
	}));
	/* 筛选后的选项列表 */
	const filteredProjectList = useMemo(() => {
		if (project.length === 0) {
			return projectList;
		}
		const projectSet = new Set(project);
		return projectList.filter((p) => projectSet.has(p.value));
	}, [project, projectList]);
	return (
		<Modal
			title={`项目工时${record?.batch ? '批量' : ''}填写`}
			open={open}
			onOk={handleSubmit}
			onCancel={handleCancel}
			maskClosable={false}
			width={600}
		>
			<Form form={form} labelCol={{ style: { width: '120px' } }}>
				{/* 上午下午一起提交了 */}
				<Form.Item label={'morningId'} name={'morningId'} hidden>
					<Input />
				</Form.Item>
				<Form.Item label={'afternoonId'} name={'afternoonId'} hidden>
					<Input />
				</Form.Item>
				<Form.Item label={'填写日期'}>
					<div className={'line-height-32'}>
						<span>{record?.dateValue}</span>
						{(record?.dateList || [])
							.sort((a, b) => a - b)
							.map((day) => +day)
							.join('、')}
						<span>日</span>
					</div>
				</Form.Item>
				{
					record.morningLeave ? (
						<Form.Item label={'上午项目'} name={'morningLeave'} >
							上午休假
						</Form.Item>
					) : <Form.Item label={'上午项目'} name={'morning'} required rules={[{ required: true, message: '请选择填报上午项目' }]}>
						<Radio.Group options={filteredProjectList} placeholder={'请选择填报上午项目'} />
					</Form.Item>
				}
				{
					record.afternoonLeave ? (
						<Form.Item label={'下午项目'} name={'afternoonLeave'} >
							下午休假
						</Form.Item>
					) : <Form.Item label={'下午项目'} name={'afternoon'} required rules={[{ required: true, message: '请选择填报下午项目' }]}>
						<Radio.Group options={filteredProjectList} placeholder={'请选择填下午项目'} />
					</Form.Item>
				}
			</Form>
		</Modal>
	);
};
export default forwardRef(ProjectWorkHour);
