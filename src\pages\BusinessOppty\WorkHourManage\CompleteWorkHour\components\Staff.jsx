/**
 * @description Staff - 文件描述
 * <AUTHOR>
 *
 * Created on 2025-07-18 上午 11:40
 */
import React, { forwardRef, useEffect, useMemo, useRef, useState } from 'react';
import { Badge, Button, Calendar, Checkbox, Col, DatePicker, message, Modal, Row, Select, Space, Tag } from 'antd';
import { APPROVAL_STATUS, APPROVAL_STATUS_COLOR } from '@/pages/BusinessOppty/WorkHourManage/MyWorkHour/const';
import dayjs from 'dayjs';
import ProjectWorkHour from './ProjectWorkHour';
import ApproveProgress from './ApproveProgress';
import './index.scss';
import { EMPLOYEE_TABS, HOLIDAY_TYPE } from '../const';
import { pageProject } from '@/api/Opportunity/Project';
import {
	addTaskTime,
	batchAddOrUpdateTaskTime,
	detailTaskTime,
	detailTaskTimes,
	projectSelection, updateProjectSelection,
} from '@/api/Opportunity/WorkHourManage';
import ApproveStatus from '@/pages/BusinessOppty/WorkHourManage/MyWorkHour/components/ApproveStatus';

const today = dayjs();
const Staff = forwardRef(({ userInfo, holidayList, id, location }, ref) => {
	const {
		state: { userId, taskDate, create },
	} = location;
	const approveRef = useRef(null);
	const [detail, setDetail] = useState({
		id: '',
		status: '1',
		departmentApproveRemark: '',
		projectApproveRemark: '',
	});
	const [activeTab, setActiveTab] = useState('personal');
	const projectWorkHourRef = useRef(null);
	const [dateValue, setDateValue] = useState(dayjs());
	/* 填报工时数据--半天为一条数据 */
	const [workHourData, setWorkHourData] = useState([]);
	/* 项目列表 */
	const [projectList, setProjectList] = useState([]);
	/* 当前选中项目 */
	const [project, setProject] = useState(null);
	/* 是否批量填报 */
	const [batch, setBatch] = useState(false);
	/* 请假数据 */
	const [leaveData, setLeaveData] = useState([]);
	/* 批量申报日期 */
	const [batchDate, setBatchDate] = useState([]);
	const changeTab = (tab) => {
		setActiveTab(tab);
	};
	useEffect(() => {
		const searchDate = taskDate ?? dayjs(taskDate).format('YYYY-MM');
		setDateValue(dayjs(searchDate));
		getProjectList();
		getCacheProject(dayjs(searchDate));
	}, []);

	useEffect(() => {
		if (userId && taskDate) {
			getDataDetail();
		}
	}, [userId, taskDate]);
	/* 查询数据详情 */
	const getDataDetail = async (date) => {
		console.log('查询数据详情', location.state);
		const params = {
			userId,
			currentMonth: date || taskDate,
		};
		const res = await detailTaskTimes(params);
		if (res.data) {
			console.log(res.data);
			setWorkHourData(res.data);
			// 找到approvalStatus最大的值
			const maxApprovalStatus = res.data.reduce((max, item) => {
				if (item.onLeave === 1) {
					return max;
				}
				return item.approvalStatus > max ? item.approvalStatus : max;
			}, 0);
			const minApprovalStatus = res.data.reduce((min, item) => {
				if (item.onLeave === 1) {
					return min;
				}
				return item.approvalStatus < min ? item.approvalStatus : min;
			}, 9);

			const detail = res.data.find((item) => item.currentMonth === params.currentMonth) || {};
			// 找到参与的项目列表并去重
			const projectSet = new Set();
			const projectList = [];

			res.data.forEach(item => {
				if (item.projectId && item.projectName && !projectSet.has(item.projectId)) {
					projectSet.add(item.projectId);
					projectList.push({
						projectId: item.projectId,
						projectName: item.projectName,
					});
				}
			});
			// 判断是否含有部门拒绝或项目拒绝的状态
			const hasDepartmentReject = res.data.some(item => item.approvalStatus === 2) ? 2 : 0;
			const hasProjectReject = res.data.some(item => item.approvalStatus === 5) ? 5 : 0;
			if (hasProjectReject) {
				projectList.forEach(item => {
					item.reject = res.data.some(item2 => item2.projectId === item.projectId && item2.approvalStatus === 5);
				});
			}
			setDetail({
				// status: maxApprovalStatus, // 使用最大的approvalStatus值
				status: hasProjectReject || hasDepartmentReject || minApprovalStatus, // 使用最大的approvalStatus值
				currentMonth: detail?.currentMonth || taskDate,
				projectApproveRemark: detail?.projectApproveRemark || '',
				departmentApproveRemark: detail?.departmentApproveRemark || '',
				projectList: projectList,
			});
		}
	};
	/* 获取项目列表 */
	const getProjectList = async () => {
		console.log('获取项目列表');
		const res = await pageProject({ pageNum: 1, pageSize: 10000 });
		if (res.data) {
			setProjectList(
				res.data.records.map((item) => ({
					label: item.name,
					value: item.id,
				})),
			);
		}
	};
	/* 查看审批进度 */
	const getApprovalProgress = () => {
		console.log('查看审批进度');
		approveRef.current?.openModal();
	};
	/* 暂存数据 */
	const saveData = () => {
		console.log('暂存数据', workHourData);
		message.success('暂存成功');
	};
	/* 撤销提交 */
	const cancelSubmit = () => {
		console.log('撤销提交');
		// 获取当月提交的数据
		const submitList = workHourData
			.filter((item) => dateValue.format('YYYY-MM') === item.currentMonth && item.onLeave !== 1)
			.map((item) => ({
				id: item?.id,
				userId: item.userId,
				userName: item.userName,
				departmentId: item.departmentId,
				departmentName: item.departmentName,
				currentMonth: item.currentMonth,
				taskDate: item.taskDate,
				timeDivision: item.timeDivision,
				projectId: item.projectId,
				projectName: item.projectName,
				// 撤销提交后 为待本人填报0
				approvalStatus: 0,
			}));
		Modal.confirm({
			title: '确定要撤销提交吗？',
			content: '撤销提交后，将无法再进行修改',
			okText: '确定',
			cancelText: '取消',
			onOk() {
				batchAddOrUpdateTaskTime(submitList).then((res) => {
					message.success('撤销成功');
					setDetail({ status: 0, currentMonth: dateValue.format('YYYY-MM') });
				});
			},
			onCancel() {
			},
		});
	};
	/* 提交审核 */
	const submitAudit = () => {
		console.log('提交审核', workHourData);
		// 获取当月提交的数据
		const submitList = workHourData
			.filter((item) => dateValue.format('YYYY-MM') === item.currentMonth && item.onLeave !== 1)
			.map((item) => ({
				id: item?.id,
				userId: item.userId || userInfo.id,
				userName: item.userName || userInfo.userName,
				departmentId: item.departmentId || userInfo.deptList[0]?.id,
				departmentName: item.departmentName || userInfo.deptList[0]?.name,
				currentMonth: item.currentMonth,
				taskDate: item.taskDate,
				timeDivision: item.timeDivision,
				projectId: item.projectId,
				projectName: item.projectName,
				// 提交后 为待部门审批1
				approvalStatus: 1,
			}));
		console.log(submitList);
		// 查询当月需要提交的数据
		const needSubmitDate = [];

		if (holidayList && holidayList.length > 0) {
			const { days } = holidayList[dateValue.month()] || {};
			if (days?.length > 0) {
				days.forEach((holidayItem) => {
					// 工作日才需要提交工时
					if (holidayItem.type === HOLIDAY_TYPE.workday) {
						let list = [
							{ date: holidayItem.date, timeDivision: 1 }, // 上午
							{ date: holidayItem.date, timeDivision: 2 }, // 下午
						];

						// 判断是否请假（注意：当前代码中leaveData始终为空数组）
						const leaveList = workHourData.filter((workData) => workData.onLeave === 1 && workData.taskDate === holidayItem.date);
						if (leaveList.length > 0) {
							// 过滤掉请假数据
							list = list.filter((timeItem) => {
								return !leaveList.find((leaveItem) => leaveItem.timeDivision === timeItem.timeDivision);
							});
						}
						needSubmitDate.push(...list);
					}
				});
			}
		}

		// 判断需要提交的数据和已提交数据列表是否一致
		const unSubmitDate = needSubmitDate.filter(
			(needItem) => !submitList.find((submitItem) => submitItem.timeDivision === needItem.timeDivision && submitItem.taskDate === needItem.date),
		);

		if (unSubmitDate.length > 0) {
			console.log('有未提交的数据', unSubmitDate);
			Modal.warning({
				title: '工时提交',
				content: `存在${unSubmitDate.length}条数据未填报，请先填写后再提交！`,
				onOk() {
					console.log('暂存数据');
					// setDetail({ status: 1, currentMonth: dateValue.format('YYYY-MM') });
				},
			});
			return;
		}

		Modal.confirm({
			title: '工时提交',
			content: `请确认是否提交${dateValue.format('YYYY年M月')}的工时？`,
			onOk() {
				batchAddOrUpdateTaskTime(submitList).then((res) => {
					message.success('提交成功');
					//    返回上一页
					history.back();
				});
				console.log('提交审核');
				// TODO: 实际提交逻辑应该在这里实现
			},
		});
	};

	const getListData = useMemo(() => {
		return (value) => {
			const date = dayjs(value);
			const emptyDay = [
				{
					type: 'error',
					content: '未填报',
					date: date.format('YYYY-MM-DD'),
					moment: 'morning',
					momentName: '上午',
					timeDivision: 1,
					key: `${date.format('YYYY-MM-DD')}-morning`,
					itemType: HOLIDAY_TYPE.workday,
				},
				{
					type: 'error',
					content: '未填报',
					moment: 'afternoon',
					timeDivision: 2,
					momentName: '下午',
					date: date.format('YYYY-MM-DD'),
					key: `${date.format('YYYY-MM-DD')}-afternoon`,
					itemType: HOLIDAY_TYPE.workday,
				},
			];
			let listData = [];
			const workDayTip = [];
			/* 查询填写工时数据是否存在 */
			const isFillData = workHourData.filter((item) => item.taskDate === date.format('YYYY-MM-DD'));
			if (isFillData.length > 0) {
				const updatedEmptyDay = emptyDay.map((item) => {
					const findValue = isFillData.find((workItem) => workItem.timeDivision === item.timeDivision);
					const { onLeave } = findValue;
					const leaveData = onLeave ? { content: '请假', type: 'warning' } : {};
					return {
						...item,
						...findValue,
						content: findValue?.projectName || '未填报',
						type: findValue?.projectName ? 'success' : 'error',
						...leaveData,
					};
				});
				workDayTip.push(...updatedEmptyDay);
			} else {
				workDayTip.push(...emptyDay);
			}
			if (holidayList && holidayList.length > 0) {
				const { days } = holidayList[date.month()] || {};
				if (days) {
					const item = days[date.date() - 1];
					if (item && dayjs(item.date).isSame(value, 'day')) {
						/* 不属于当前月份数据 */
						if (!dayjs(item.date).isSame(dateValue, 'month')) {
							listData = [
								{
									type: 'default',
									key: `${date.format('YYYY-MM-DD')}`,
									// content: ['周日', '周一', '周二', '周三', '周四', '周五', '周六'][date.day()],
									content: `无需填写`,
								},
							];
						} else if (item.type === HOLIDAY_TYPE.workday) {
							listData.push(
								{
									...item,
									...workDayTip[0],
									itemType: item.type,
								},
								{
									...item,
									...workDayTip[1],
									itemType: item.type,
								},
							);
						} else if (item.type === HOLIDAY_TYPE.holiday) {
							listData.push({
								...item,
								type: 'default',
								key: `${date.format('YYYY-MM-DD')}`,
								itemType: item.type,
								content: item?.typeDes,
							});
						}
					}
				}
			}

			/* 判断是否为周末 */
			if (listData.length === 0) {
				if ([0, 6].includes(date.day())) {
					listData = [
						{
							type: 'default',
							key: `${date.format('YYYY-MM-DD')}`,
							content: date.day() === 0 ? '周日' : '周六',
						},
					];
				} else {
					listData = [...workDayTip];
				}
			}
			return listData;
		};
	}, [workHourData, holidayList, dateValue]);

	/* 点击整天的工时填写 */
	const onCellClick = ({ listData, date, dayData, isRest }) => {
		console.log(listData);
		const [morning, afternoon] = listData;
		if (batch) {
			/* 是否存在当前日期 */
			const isExist = batchDate.some((date) => date === morning.date);
			if (!isExist) {
				setBatchDate([...batchDate, morning.date]);
			} else {
				setBatchDate(batchDate.filter((date) => date !== morning.date));
			}
			return;
		}
		// [1, 3, 4]
		if ([1, 3, 4].includes(morning.approvalStatus)) {
			const statusName = APPROVAL_STATUS.find((item) => item.value === morning.approvalStatus)?.label
			message.warning(`当前状态${statusName}，不能修改`);
			return;
		}
		const isWorkday = morning.itemType === HOLIDAY_TYPE.workday;
		// 选中的月份与当前月份不一致
		if (!date.isSame(dateValue, 'month')) {
			return;
		}
		// 不可以填写还没到的时间
		if (dayjs(date).startOf('day').isAfter(today, 'month')) {
			message.warning('还未到填写时间');
			return;
		}
		if (isRest){
			message.warning('今天休假无需填写');
			return;
		}
		if (isWorkday) {
			projectWorkHourRef.current.openModal({
				dateList: [dayjs(morning.date).format('DD')],
				dateValue: dateValue.format('YYYY年M月'),
				morning: morning.projectId,
				afternoon: afternoon.projectId,
				morningId: morning.id,
				afternoonId: afternoon.id,
				batch: false,
				morningLeave: morning.onLeave,
				afternoonLeave: afternoon.onLeave,
				projectList,
			});
		}
	};

	/* 切换日期 */
	const onDateChange = (value) => {
		setDateValue(value);
		setBatchDate([]);
		getDataDetail(value.format('YYYY-MM'));
		getCacheProject(value);
	};

	const dateCellRender = useMemo(() => {
		return (value) => {
			const listData = getListData(value);
			const date = dayjs(value);
			// 是否和激活时间一致
			const isActive = date.isSame(dateValue, 'day');
			const isHoliday = listData.some((item) => item.type === 'default');
			/* 日期需要是当前月份 */
			const isCurrentMonth = date.isSame(dateValue, 'month');
			// 获取本月假日信息
			const monthData = holidayList.find((item) => `${item.year}-${item.month}` === date.format('YYYY-M'));
			// 获取对应当天的假日信息
			const dayData = monthData?.days?.find((item) => item.date === date.format('YYYY-MM-DD'));
			// 查询指定日期是否填写了日报
			// const fillData = workHourData?.find((item) => item.reportDate === date.format('YYYY-MM-DD') && item.id);
			// 判断是否为休假
			const restDate = workHourData.filter((item) => item.taskDate === date.format('YYYY-MM-DD') && item.onLeave);
			const isRest = restDate.length === 2;
			return (
				<div className={`calendar-cell ${isActive ? 'calendar-cell-active' : ''}`}
					 onClick={() => onCellClick({ listData, date, dayData, isRest })}>
					<div className={'flex justify-between align-center position-relative'}>
						{batch && !isHoliday && isCurrentMonth && !isRest && <Checkbox value={date.format('YYYY-MM-DD')} />}
						<div
							className={'font-size-20 flex-sub font-weight-400 text-align-right'}>{date.format('D')}</div>
					</div>
					<ul className='calendar-cell-content position-relative'>
						{listData.map((item) => (
							<li key={item.key} title={item.content}>
								<Badge status={item.type} text={item.content} />
							</li>
						))}
					</ul>
				</div>
			);
		};
	}, [dateValue, batch, workHourData, holidayList, projectList, batchDate, leaveData]);

	/* 修改批量申报 */
	const changeBatch = (value) => {
		console.log('修改批量申报', value);
		setBatchDate(value);
	};
	/* 前往批量申报 */
	const handleClickBatch = () => {
		if (!batchDate.length) {
			return message.warning('请选择批量申报日期');
		}
		projectWorkHourRef.current.openModal({
			dateList: batchDate.map((item) => dayjs(item).format('DD')),
			dateValue: dateValue.format('YYYY年M月'),
			batch: true,
		});
	};
	/* 取消批量申报 */
	const cancelBatch = () => {
		setBatchDate([]);
		setBatch(false);
	};
	/* 提交更新工时 */
	const updateWorkHour = async (values) => {
		console.log('提交更新工时', values);
		const list = [];
		const reqList = [];

		values?.dateList.forEach((day) => {
			const date = dayjs(dateValue).format('YYYY-MM') + '-' + day;
			const workDays = workHourData.filter((item) => item.taskDate === date);
			['morning', 'afternoon'].forEach((moment) => {
				const timeDivision = moment === 'morning' ? 1 : 2;
				const workHour = workDays.find((item) => item.timeDivision === timeDivision);

				// 获取项目名称
				const projectName = projectList.find((item) => item.value === values[moment])?.label;

				const params = {
					id: workHour?.id,
					projectId: values[moment],
					projectName: projectName,
					date: date,
					taskDate: date,
					moment: moment,
					momentName: moment === 'morning' ? '上午' : '下午',
					timeDivision: timeDivision,
					content: projectName,
					currentMonth: dateValue.format('YYYY-MM'),
					type: 'success',
					key: `${date}-${moment}`,
					itemType: values.itemType || HOLIDAY_TYPE.workday,
				};

				const reqParams = {
					id: workHour?.id,
					userId: userInfo.id,
					userName: userInfo.userName,
					departmentId: userInfo.deptList[0]?.id,
					departmentName: userInfo.deptList[0]?.name,
					currentMonth: dateValue.format('YYYY-MM'),
					taskDate: date,
					timeDivision: timeDivision,
					projectId: values[moment],
					projectName: projectName,
				};
				if (!workHour?.onLeave) {
					list.push({ ...params, ...reqParams });
					reqList.push(reqParams);
				}
			});
		});
		cacheSelectProject();
		console.log('reqList', reqList);
		const res = await batchAddOrUpdateTaskTime(reqList);

		if (res.data) {
			let isUpdate = false;
			// 批量添加成功后，更新workHourData中的id字段
			res.data?.forEach((id, i) => {
				// 此处为批量添加的id，需要替换到list中对应的位置上
				if (id && list[i].id && !isUpdate) {
					list[i].id = id;
					isUpdate = true;
				} else {
					list[i].id = id;
				}
			});
			message.success(isUpdate ? '更新成功' : '添加成功');
		}

		// 创建新的workHourData数组而不是直接修改原数组
		const updatedWorkHourData = [...workHourData];
		list.forEach((item) => {
			const index = updatedWorkHourData.findIndex((ov) => ov.taskDate === item.taskDate && ov.timeDivision === item.timeDivision);
			if (index > -1) {
				updatedWorkHourData[index] = item;
			} else {
				updatedWorkHourData.push(item);
			}
		});

		console.log('workHourData', updatedWorkHourData);
		setWorkHourData(updatedWorkHourData);

		if (batch) {
			cancelBatch();
		}
	};
	/* 这里缓存变更的选项 */
	const cacheSelectProject = async () => {
		console.log('缓存变更的选项', project);
		const cacheProject = projectList.filter((item) => project?.includes(item.value));
		const params = {
			userId: userInfo.id,
			currentMonth: dateValue.format('YYYY-MM'),
			projects: cacheProject.map(item => ({ id: item.value, name: item.label })),
		};
		const res = await updateProjectSelection(params);
		if (res.data) {
			console.log('缓存成功');
		}
	};
	/* 获取用户缓存的项目名称 */
	const getCacheProject = async (date) => {
		const param = {
			userId: userInfo.id,
			currentMonth: (date || dateValue).format('YYYY-MM'),
		};
		const res = await projectSelection(param);
		if (res.data) {
			setProject(res.data.map((item) => item.projectId));
		}
	};
	/* 删除选的项目项 */
	const deleteProject = (value) => {
		console.log('删除选的项目项', value);
		const delIndex = project.findIndex((item) => item === value);
		project?.splice(delIndex, 1);
		console.log('project', project);
		setProject([...project]);
	};

	return (
		<div className={'flex-sub flex flex-direction-column margin-20 '}>
			<div
				className='padding-20 bg-color-ffffff border-radius-8 flex justify-between align-center line-height-24 margin-bottom-20'>
				<div className={'font-size-16 flex flex-sub gap-40'}>
					{EMPLOYEE_TABS.map((item) => {
						return (
							<div key={item.value} className={'flex align-center'} onClick={() => changeTab(item.value)}>
								<span
									className={`font-size-16 cursor-pointer font-weight-600 ${activeTab === item.value ? 'color-165dff' : 'color-1d2129'}`}
								>
									{item.label}
								</span>
							</div>
						);
					})}
				</div>
				<Space>
					<Button disabled={detail.status !== 1} danger onClick={cancelSubmit}>
						撤销
					</Button>
					<Button disabled={[1, 3, 4].includes(detail.status)} type={'primary'} onClick={submitAudit}>
						提交
					</Button>
				</Space>
			</div>
			<div className={'bg-color-ffffff padding-20 border-radius-8'}>
				<Row wrap gutter={[20, 20]} className={'flex justify-start align-center margin-bottom-20'}>
					<Col lg={6} md={8}>
						<Space>
							<label className={''}>部门名称：</label>
							<div className={''}>{userInfo.deptList[0]?.name}</div>
						</Space>
					</Col>
					<Col lg={6} md={8}>
						<Space>
							<label className={''}>员工姓名：</label>
							<div className={''}>{userInfo.userName}</div>
						</Space>
					</Col>
					<Col lg={6} md={8}>
						<Space>
							<label className={''}>日期：</label>
							{create ? (
								<DatePicker
									className={'width-100per'}
									value={dateValue}
									placeholder={'请选择查询日期'}
									allowClear={false}
									picker={'month'}
									onChange={onDateChange}
									// disabled
								/>
							) : (
								taskDate
							)}
						</Space>
					</Col>
					<Col lg={6} md={8}>
						<Space>
							<label className={''}>审批状态：</label>
							<div className={'a'} onClick={getApprovalProgress}>
								<ApproveStatus status={detail.status} />
							</div>
						</Space>
					</Col>
					<Col lg={6} md={8}>
						<Space>
							<label className={''}>项目名称：</label>
							<Select
								allowClear
								optionFilterProp={'label'}
								mode={'multiple'}
								maxTagCount={'responsive'}
								placeholder={'请选择项目列表'}
								className={'width-180'}
								options={projectList}
								value={project}
								onChange={(e) => setProject(e)}
							/>
						</Space>
					</Col>
					<Col lg={6} md={8}>
						<Space>
							<label className={''}>总计工时：</label>
							<span className={''}>{workHourData.length * 0.5}天</span>
						</Space>
					</Col>
				</Row>
				<div className={'margin-top-20'}>
					<Checkbox.Group style={{ width: '100%' }} value={batchDate} onChange={changeBatch}>
						<Calendar
							fullscreen
							showWeek={false}
							headerRender={(current, info) => {
								return (
									<div className={'flex justify-between align-start margin-bottom-20 gap-20'}>
										<Space className={'flex align-center flex-wrap'}>
											{project?.map((item) => {
												return (
													<Tag
														color={'processing'}
														key={item}
														type={'primary'}
														closable
														onClose={() => deleteProject(item)}
													>
														{projectList.find((i) => i.value === item)?.label}
													</Tag>
												);
											})}
										</Space>
										<div className={'flex flex-sub justify-end align-center gap-12'}>
											{batch ? (
												<>
													<Button type={'primary'} onClick={() => handleClickBatch()}>
														填报
													</Button>
													<Button onClick={cancelBatch}>取消</Button>
												</>
											) : (
												<Button type={'primary'} onClick={() => setBatch(true)}>
													批量填报
												</Button>
											)}
										</div>
									</div>
								);
							}}
							prefixCls={'calendar-custom'}
							fullCellRender={dateCellRender}
							value={dateValue}
							validRange={[dateValue.startOf('month'), dateValue.endOf('month')]}
						/>
					</Checkbox.Group>
				</div>
			</div>
			<ProjectWorkHour ref={projectWorkHourRef} onSubmit={updateWorkHour} projectList={projectList}
							 project={project || []} />
			<ApproveProgress ref={approveRef} detail={detail} workHourData={workHourData} userInfo={userInfo} />
		</div>
	);
});

export default Staff;
