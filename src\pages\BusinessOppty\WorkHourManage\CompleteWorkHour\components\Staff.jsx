/**
 * @description Staff - 文件描述
 * <AUTHOR>
 *
 * Created on 2025-07-18 上午 11:40
 */
import React, {useEffect, useRef, useState} from 'react';
import {Badge, Button, Calendar, Checkbox, Col, DatePicker, message, Modal, Row, Select, Space, Tag} from "antd";
import {
    APPROVAL_STATUS,
    APPROVAL_STATUS_COLOR,
} from "@/pages/BusinessOppty/WorkHourManage/MyWorkHour/const";
import dayjs from "dayjs";
import ProjectWorkHour from "./ProjectWorkHour";
import ApproveProgress from "./ApproveProgress";
import "./index.scss";
import {EMPLOYEE_TABS, HOLIDAY_TYPE} from "../const";

const Staff = ({date, userInfo, holidayList}) => {
    const approveRef = useRef(null);
    const [detail, setDetail] = useState({
        id: '',
        status: '1',
    });
    const [activeTab, setActiveTab] = useState('personal');
    const projectWorkHourRef = useRef(null);
    const [dateValue, setDateValue] = useState(dayjs('2025-04-01'));
    // const [holidayList, setHolidayList] = useState([]);
    /* 填报工时数据 */
    const [workHourData, setWorkHourData] = useState([]);
    /* 项目列表 */
    const [projectList, setProjectList] = useState([
        {label: '项目1', value: 1},
        {label: '项目2', value: 2},
        {label: '项目3', value: 3},
        {label: '项目4', value: 4},
    ]);
    /* 当前选中项目 */
    const [project, setProject] = useState(null);
    /* 是否批量填报 */
    const [batch, setBatch] = useState(false);
    /* 批量申报日期 */
    const [batchDate, setBatchDate] = useState([]);
    const changeTab = (tab) => {
        setActiveTab(tab);
    }
    useEffect(() => {
        const searchDate = date ?? dayjs(date).format('YYYY-MM');
        const year = dayjs(searchDate).year();
        if (date) {
            setDateValue(dayjs(searchDate));
        }
        // queryHoliday({year}).then(res => {
        //     console.log(res);
        //     setHolidayList(res.dates);
        // });
    }, []);
    /* 查看审批进度 */
    const getApprovalProgress = () => {
        console.log('查看审批进度');
        approveRef.current?.openModal();
    }
    /* 暂存数据 */
    const saveData = () => {
        console.log('暂存数据', workHourData);
        message.success('暂存成功');
    }
    /* 撤销提交 */
    const cancelSubmit = () => {
        console.log('撤销提交');
    }
    /* 提交审核 */
    const submitAudit = () => {
        console.log('提交审核', workHourData);
        Modal.confirm({
            title: '工时提交',
            content: `请确认是否提交${dateValue.format('YYYY年M月')}的工时？`,
            onOk() {
                console.log('提交审核');
            },
        });
    };

    const getListData = value => {
        const date = value;
        let listData = [];
        // type = public_holiday 公共假日
        // type = transfer_workday 补班
        const emptyDay = [
            {
                type: 'error',
                content: '未填写',
                date: date.format('YYYY-MM-DD'),
                moment: 'morning',
                momentName: '上午',
                key: `${date.format('YYYY-MM-DD')}-morning`,
                itemType: HOLIDAY_TYPE.workday
            },
            {
                type: 'error',
                content: '未填写',
                moment: 'afternoon',
                momentName: '下午',
                date: date.format('YYYY-MM-DD'),
                key: `${date.format('YYYY-MM-DD')}-afternoon`,
                itemType: HOLIDAY_TYPE.workday
            },
        ];
        const workDayTip = [];
        /* 查询填写工时数据是否存在 */
        const isFill = workHourData.some(item => item.date === date.format('YYYY-MM-DD'));
        if (isFill) {
            const updatedEmptyDay = emptyDay.map(item => {
                const findValue = workHourData.find(workItem => workItem.key === item.key);
                return findValue || item;
            });
            workDayTip.push(...updatedEmptyDay);
        } else {
            workDayTip.push(...emptyDay);
        }
        if (holidayList && holidayList.length > 0) {
            const {days} = holidayList[date.month()];
            const item = days[date.date() - 1];
            if (dayjs(item.date).isSame(value, 'day')) {
                if (item.type === HOLIDAY_TYPE.workday) {
                    listData.push(
                        {
                            ...item,
                            ...workDayTip[0],
                            itemType: item.type,
                        },
                        {
                            ...item,
                            ...workDayTip[1],
                            itemType: item.type,
                        },
                    );
                } else if (item.type === HOLIDAY_TYPE.holiday) {
                    listData.push({
                        ...item,
                        type: 'default',
                        key: `${date.format('YYYY-MM-DD')}`,
                        itemType: item.type,
                        content: item?.typeDes,
                    });
                }
            }
        }
        /* 判断是否为周末 */
        if (listData && listData.length === 0) {
            if ([0, 6].includes(date.day())) {
                listData = [
                    {
                        type: 'default',
                        key: `${date.format('YYYY-MM-DD')}`,
                        content: date.day() === 0 ? '周日' : '周六',
                    }
                ];
            } else {
                listData = [...workDayTip];
            }
        }
        return listData || [];
    }

    /* 点击整天的工时填写 */
    const onCellClick = (listData) => {
        console.log(listData);
        const [morning, afternoon] = listData;
        if (batch) {
            /* 是否存在当前日期 */
            const isExist = batchDate.some(date => date === morning.date);
            if (!isExist) {
                setBatchDate([...batchDate, morning.date]);
            } else {
                setBatchDate(batchDate.filter(date => date !== morning.date));
            }
            return;
        }
        const isWorkday = morning.itemType === HOLIDAY_TYPE.workday;
        if (isWorkday) {
            projectWorkHourRef.current.openModal({
                dateList: [dayjs(morning.date).format('DD')],
                dateValue: dateValue.format('YYYY年M月'),
                morning: morning.projectId,
                afternoon: afternoon.projectId,
                batch: false,
            });
        }
    }

    /* 切换日期 */
    const onPanelChange = (value) => {
        setDateValue(dayjs(value));
        setBatchDate([]);
        // getListData(value);
    }

    const dateCellRender = (value, info) => {
        const listData = getListData(value);
        const date = dayjs(value);
        // 是否和激活时间一直
        const isActive = date.isSame(dateValue, 'day');
        const isHoliday = listData.some(item => item.type === 'default');
        /* 日期需要是当前月份 */
        const isCurrentMonth = date.isSame(dateValue, 'month');
        return (
            <div className={`calendar-cell ${isActive ? 'calendar-cell-active' : ''}`} onClick={() => onCellClick(listData)}>
                <div className={'flex justify-between align-center position-relative'}>
                    {
                        batch && !isHoliday && isCurrentMonth && (
                            <Checkbox value={date.format('YYYY-MM-DD')}>
                                {/*批量填报*/}
                            </Checkbox>
                        )
                    }
                    <div className={'font-size-20 flex-sub font-weight-400 text-align-right'}>{date.format('D')}</div>
                </div>
                <ul className="calendar-cell-content position-relative">
                    {listData.map(item => (
                        <li key={item.key}>
                            <Badge status={item.type} text={item.content} />
                        </li>
                    ))}
                </ul>
            </div>
        );
    };

    /* 修改批量申报 */
    const changeBatch = (value) => {
        console.log('修改批量申报', value);
        setBatchDate(value);
    }
    /* 前往批量申报 */
    const handleClickBatch = () => {
        if (!batchDate.length) {
            return message.warning('请选择批量申报日期');
        }
        projectWorkHourRef.current.openModal({
            dateList: batchDate.map(item => dayjs(item).format('DD')),
            dateValue: dateValue.format('YYYY年M月'),
            batch: true,
        });
    }
    /* 取消批量申报 */
    const cancelBatch = () => {
        setBatchDate([]);
        setBatch(false);
    }
    /* 提交更新工时 */
    const updateWorkHour = (values) => {
        console.log('提交更新工时', values);
        const list = [];
        values?.dateList.forEach(day => {
            const date = dayjs(dateValue).format('YYYY-MM') + '-' + day;
            ['morning', 'afternoon'].forEach(moment => {
                const params = {
                    projectId: values[moment],
                    date: date,
                    moment: moment,
                    momentName: moment === 'morning' ? '上午' : '下午',
                    content: projectList.find(item => item.value === values[moment])?.label,
                    type: 'success',
                    key: `${date}-${moment}`,
                    itemType: values.itemType || HOLIDAY_TYPE.workday
                };
                list.push(params);
            });
        });
        list.forEach(item => {
            const index = workHourData.findIndex(item2 => item2.key === item.key);
            if (index > -1) {
                workHourData[index] = item;
            } else {
                workHourData.push(item);
            }
        });
        console.log('workHourData', workHourData);
        setWorkHourData([...workHourData]);
        if (batch) {
            cancelBatch();
        }
    }
    /* 删除选的项目项 */
    const deleteProject = (value) => {
        console.log('删除选的项目项', value);
        project?.splice(project.findIndex(item => item === value), 1);
        console.log('project', project)
        setProject([...project]);
    }

    return (<div className={'flex-sub flex flex-direction-column margin-20 padding-20 '}>
        <div
            className="padding-20 bg-color-ffffff border-radius-8 flex justify-between align-center line-height-24 margin-bottom-20">
            <div className={'font-size-16 flex flex-sub gap-40'}>
                {
                    EMPLOYEE_TABS.map(item => {
                        return <div key={item.value} className={'flex align-center'}
                                    onClick={() => changeTab(item.value)}>
                            <span
                                className={`font-size-16 cursor-pointer font-weight-600 ${activeTab === item.value ? 'color-165dff' : 'color-1d2129'}`}>{item.label}</span>
                        </div>;
                    })
                }
            </div>
            <Space>
                <Button onClick={saveData}>暂存</Button>
                <Button danger onClick={cancelSubmit}>撤销</Button>
                <Button type={'primary'} onClick={submitAudit}>提交</Button>
            </Space>
        </div>
        <div className={'bg-color-ffffff padding-20 border-radius-8'}>
            <Row wrap gutter={[20,20]} className={'flex justify-start align-center margin-bottom-20'}>
                <Col lg={6} md={8}>
                    <Space>
                        <label className={''}>部门名称：</label>
                        <div className={''}>{userInfo.deptList[0]?.name}</div>
                    </Space>
                </Col>
                <Col lg={6} md={8}>
                    <Space>
                        <label className={''}>员工姓名：</label>
                        <div className={''}>{userInfo.userName}</div>
                    </Space>
                </Col>
                <Col lg={6} md={8}>
                    <Space>
                        <label className={''}>日期：</label>
                        <DatePicker
                            className={'width-100per'}
                            value={dateValue}
                            placeholder={'请选择查询日期'}
                            allowClear={false}
                            picker={'month'}
                            onChange={onPanelChange}
                            // disabled
                        />
                    </Space>
                </Col>
                <Col lg={6} md={8}>
                    <Space>
                        <label className={''}>审批状态：</label>
                        <div className={'a'} onClick={getApprovalProgress}>
                            <Badge
                                status={APPROVAL_STATUS_COLOR[detail.status]}
                                text={
                                    APPROVAL_STATUS.find(item => item.value === detail.status)?.label || '暂存'
                                }
                            />
                        </div>
                    </Space>
                </Col>
            </Row>
            <div className={'flex justify-start align-center gap-20 margin-bottom-20'}>
                <div >
                    <label className={''}>项目名称：</label>
                    <Select
                        allowClear
                        mode={'multiple'}
                        maxTagCount={"responsive"}
                        placeholder={'请选择项目列表'}
                        className={'width-300'}
                        options={projectList}
                        value={project}
                        onChange={(e) => setProject(e)}
                    />
                </div>
                <div >
                    <label className={''}>总计工时：</label>
                    <span className={''}>{workHourData.length * 0.5}天</span>
                </div>
            </div>
            <div className={'margin-top-20'}>
                <Checkbox.Group style={{width: '100%'}} value={batchDate} onChange={changeBatch}>
                    <Calendar
                        fullscreen
                        showWeek
                        headerRender={(current, info) => {
                            return <div className={'flex justify-between margin-bottom-20 gap-20'}>
                                <Space className={'flex align-center'}>
                                    {
                                        project?.map(item => {
                                            return <Tag color={'processing'} key={item} type={'primary'} closable onClose={() => deleteProject(item)}>
                                                {projectList.find(i => i.value === item)?.label}
                                            </Tag>
                                        })
                                    }
                                </Space>
                                <div className={'flex flex-sub justify-end align-center gap-12'}>
                                    {
                                        batch ?
                                            <>
                                                <Button type={'primary'} onClick={() => handleClickBatch()}>填报</Button>
                                                <Button onClick={cancelBatch}>取消</Button>
                                            </> :
                                            <Button type={'primary'}
                                                    onClick={() => setBatch(true)}>批量填报</Button>
                                    }
                                </div>
                            </div>
                        }}
                        prefixCls={'calendar-custom'}
                        fullCellRender={dateCellRender}
                        value={dateValue}
                        validRange={[dateValue.startOf('month'), dateValue.endOf('month')]}
                    />
                </Checkbox.Group>
            </div>
        </div>
        <ProjectWorkHour
            ref={projectWorkHourRef}
            onSubmit={updateWorkHour}
            projectList={projectList}
            project={project || []}
        />
        <ApproveProgress ref={approveRef} />
    </div>)
}
export default Staff;
