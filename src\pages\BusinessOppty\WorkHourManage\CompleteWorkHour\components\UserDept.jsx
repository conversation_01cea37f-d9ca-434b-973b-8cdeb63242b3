/**
 * @description UserDept.jsx - 文件描述
 * <AUTHOR>
 *
 * Created on 2025-07-18 上午 11:41
 */
import React, {useEffect, useState, forwardRef, useImperativeHandle} from 'react';
import {DEPARTMENT_TABS, EMPLOYEE_TABS, HOLIDAY_TYPE} from "../const";
import {Button, Space, message, Col, DatePicker, Badge, Row, Tag, Table} from "antd";
import {APPROVAL_STATUS, APPROVAL_STATUS_COLOR} from "@/pages/BusinessOppty/WorkHourManage/MyWorkHour/const";
import dayjs from "dayjs";
import {NotificationOutlined} from "@ant-design/icons";
import UserWork from "./UserWork";

const UserDept = ({userInfo, holidayList, allowSelect}, ref) => {
    const [activeTab, setActiveTab] = useState(EMPLOYEE_TABS[0].value);
    const [dateValue, setDateValue] = useState(dayjs('2025-04-01'));
    const [detail, setDetail] = useState({id: '', status: '2'});
    /* 切换tabs */
    const changeTab = (value) => {
        setActiveTab(value);
    };
    /* 审批 */
    const submitAudit = () => {
        console.log('提交审批');
        message.success('提交成功');
    };
    useImperativeHandle(ref, () => ({
        getTableData: getTableData,
    }));
    /* 查询表格信息 */
    const getTableData = async (values) => {
        console.log('查询表格信息', values);
    };

    return (<div className={'flex-sub flex flex-direction-column margin-20 padding-20 '}>
        <div
            className="padding-20 bg-color-ffffff border-radius-8 flex justify-between align-center line-height-24 margin-bottom-20">
            <div className={'font-size-16 flex flex-sub gap-40'}>
                {
                    DEPARTMENT_TABS.map(item => {
                        return <div key={item.value} className={'flex align-center'}
                                    onClick={() => changeTab(item.value)}>
                            <span
                                className={`font-size-16 cursor-pointer font-weight-600 ${activeTab === item.value ? 'color-165dff' : 'color-1d2129'}`}>
                                {item.label}
                            </span>
                        </div>;
                    })
                }
            </div>
            <Space>
                <Button type={'primary'} onClick={submitAudit}>提交</Button>
            </Space>
        </div>
        <div className={'bg-color-ffffff border-radius-8 padding-20'}>
            <div className={'flex justify-between align-center margin-bottom-20'}>
                <Row wrap gutter={[20,20]} className={'flex-sub flex justify-start align-center '}>
                    <Col lg={6} md={8}>
                        <Space>
                            <label className={''}>部门名称：</label>
                            <div className={''}>{userInfo.deptList[0]?.name}</div>
                        </Space>
                    </Col>
                    <Col lg={6} md={8}>
                        <Space>
                            <label className={''}>日期：</label>
                            <span>{dateValue.format('YYYY年M月')}</span>
                        </Space>
                    </Col>
                    <Col lg={6} md={8}>
                        <Space>
                            <label className={''}>审批状态：</label>
                            <div className={'a'} >
                                <Badge
                                    status={APPROVAL_STATUS_COLOR[detail.status]}
                                    text={
                                        APPROVAL_STATUS.find(item => item.value === detail.status)?.label || '暂存'
                                    }
                                />
                            </div>
                        </Space>
                    </Col>
                </Row>
                <Button type={'primary'} icon={<NotificationOutlined />}>提醒未填报员工</Button>
            </div>
            <div className={'flex align-center'}>
                <label >项目名称：</label>
                <Tag color={'processing'}>天河项目</Tag>
                <Tag color={'processing'}>吉林项目</Tag>
                <Tag color={'processing'}>成果项目</Tag>
            </div>
            <UserWork allowSelect dateValue={dateValue} holidayList={holidayList} />
        </div>
    </div>)
}
export default forwardRef(UserDept);
