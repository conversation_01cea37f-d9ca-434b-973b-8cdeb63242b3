/**
 * @description UserDept.jsx - 文件描述
 * <AUTHOR>
 *
 * Created on 2025-07-18 上午 11:41
 */
import React, { forwardRef, useEffect, useRef, useState } from 'react';
import { DEPARTMENT_TABS, EMPLOYEE_TABS } from '../const';
import { Button, Col, message, Row, Space, Tag } from 'antd';
import dayjs from 'dayjs';
import { NotificationOutlined } from '@ant-design/icons';
import UserWork from './UserWork';
import {
	batchAddOrUpdateTaskTime,
	getTaskTimeSummaryList,
	updateBatchTaskTimeApproval,
} from '@/api/Opportunity/WorkHourManage';
import { QUERY_TYPE } from '@/pages/BusinessOppty/WorkHourManage/DepartmentWorkHour/const';
import ApproveStatus from '@/pages/BusinessOppty/WorkHourManage/MyWorkHour/components/ApproveStatus';
import ApproveProgress from '@/pages/BusinessOppty/WorkHourManage/CompleteWorkHour/components/ApproveProgress';
import ApproveWork from '@/pages/BusinessOppty/WorkHourManage/CompleteWorkHour/components/ApproveWork';
import ApproveProject from '@/pages/BusinessOppty/WorkHourManage/CompleteWorkHour/components/ApproveProject';
import LeaderStatus from '@/pages/BusinessOppty/WorkHourManage/MyWorkHour/components/LeaderStatus';

const UserDept = forwardRef(({ userInfo, holidayList, allowSelect, id, location }, ref) => {
	const {
		state: { departmentId, taskDate, projectId, queryType, status },
	} = location;
	const workRef = useRef(null);
	const workApproveRef = useRef(null);
	const projectApproveRef = useRef(null);
	const approveRef = useRef(null);
	const [activeTab, setActiveTab] = useState(EMPLOYEE_TABS[0].value);
	const [dateValue, setDateValue] = useState(dayjs());
	const [detail, setDetail] = useState({ id: '', status: '2', projectList: [] });
	// 表格数据
	const [dataSource, setDataSource] = useState([]);
	// 获取审批数据列表
	const [approveList, setApproveList] = useState([]);

	useEffect(() => {
		const searchDate = taskDate ?? dayjs(taskDate).format('YYYY-MM');
		setDateValue(dayjs(searchDate));
		if (queryType) {
			setActiveTab(queryType);
		}
		getTableData(queryType);
	}, []);
	// 查询列表数据
	const getTableData = async (queryType) => {
		const params = {
			currentMonth: taskDate,
			queryType: 1,
		};
		if (queryType === QUERY_TYPE.department) {
			params.departmentId = departmentId;
		} else if (queryType === QUERY_TYPE.project) {
			params.projectId = projectId;
		}
		console.log('查询 数据', params);
		const res = await getTaskTimeSummaryList(params);
		if (res.data) {
			console.log('列表数据', res.data);
			const list = res.data.map((item) => {
				const { approvalStatus } = item.taskTimes[0] || {};
				return {
					...item,
					disabledApprove: queryType === QUERY_TYPE.department ? approvalStatus !== 1 :approvalStatus !== 3,
				};
			});
			setDataSource(list);
			//    查的数据都是一样的
			const firstData = res.data[0];
			// 项目列表
			const projectList = new Set(
				res.data
					.map((item) => {
						return item.taskTimes.map((ov) => ov.projectName);
					})
					.flat()
			);
			// 项目负责人列表
			const managerUserName = new Set(
				res.data
					.map((item) => {
						return item.taskTimes.map((ov) => ov.managerUserName);
					})
					.flat()
			);
			console.log('managerUserName', managerUserName);
			setDetail({
				status: status,
				approveStatus: firstData.approveStatus,
				currentMonth: firstData.currentMonth,
				departmentId: firstData.departmentId,
				departmentName: firstData.departmentName,
				projectId: firstData.projectId,
				projectName: firstData.projectName,
				projectList: Array.from(projectList),
				managerUserName: Array.from(managerUserName).filter(managerUserName => managerUserName),
				// userId: firstData.userId,
				// userName: firstData.userName,
			});
		} else {
			setDataSource([]);
		}
	};
	/* 切换tabs */
	const changeTab = (value) => {
		setActiveTab(value);
		getTableData(value);
	};
	/* 审批 */
	const submitAudit = async (projectParams) => {
		console.log('提交审批');
		const submitList = [];
		approveList.forEach((userId) => {
			const userData = dataSource.find((item) => item.userId === userId);
			const taskTimes = userData?.taskTimes.map((item) => {
				return {
					...item,
					// approvalStatus: queryType === QUERY_TYPE.project ? '1' : '2'
				};
			});
			taskTimes.onLeave !== 1 && submitList.push(...taskTimes);
		});
		// 部门拒绝 2
		// 部门通过后到项目负责人审批 3
		// 项目负责人拒绝 5
		// 项目负责人通过 4
		if (queryType === QUERY_TYPE.department) {
			workApproveRef.current?.openApprove(submitList);
		} else if (queryType === QUERY_TYPE.project) {
			projectApproveRef.current.openApprove(submitList);
		}
	};
	// 提交部门审批
	const submitApprove = async (list) => {
		console.log('提交审批', list);
		const res = await batchAddOrUpdateTaskTime(list);
		if (res.data) {
			message.success('部门审批成功')
			workApproveRef.current?.closeModal();
			workRef.current?.setSelectedRowKeys([]);
			getTableData(queryType);
		}
	};
	// 提交项目审批
	const submitProjectApprove = async (list) => {
		console.log('提交审批', list);
		const res = await batchAddOrUpdateTaskTime(list);
		if (res.data) {
			message.success('项目审批成功')
			projectApproveRef.current?.closeModal();
			workRef.current?.setSelectedRowKeys([]);
			getTableData(queryType);
		}
	}
	/* 查看审批进度 */
	const getApprovalProgress = () => {
		console.log('查看审批进度');
		// approveRef.current?.openModal();
	};
	return (
		<div className={'flex-sub flex flex-direction-column margin-20 '}>
			<div className="padding-20 bg-color-ffffff border-radius-8 flex justify-between align-center line-height-24 margin-bottom-20">
				<div className={'font-size-16 flex flex-sub gap-40'}>
					{DEPARTMENT_TABS.filter((tab) => tab.value === queryType).map((item) => {
						return (
							<div key={item.value} className={'flex align-center'} onClick={() => changeTab(item.value)}>
								<span
									className={`font-size-16 cursor-pointer font-weight-600 ${activeTab === item.value ? 'color-165dff' : 'color-1d2129'}`}
								>
									{item.label}
								</span>
							</div>
						);
					})}
				</div>
				<Space>
					<Button disabled={approveList.length === 0} type={'primary'} onClick={submitAudit}>
						审批
					</Button>
				</Space>
			</div>
			<div className={'bg-color-ffffff border-radius-8 padding-20'}>
				<div className={'flex justify-between align-center margin-bottom-20'}>
					<Row wrap gutter={[20, 20]} className={'flex-sub flex justify-start align-center '}>
						<Col lg={6} md={8}>
							<Space>
								<label className={''}>部门名称：</label>
								<div className={''}>{detail?.departmentName}</div>
							</Space>
						</Col>
						<Col lg={6} md={8}>
							<Space>
								<label className={''}>日期：</label>
								<span>{dateValue.format('YYYY年M月')}</span>
							</Space>
						</Col>
						<Col lg={6} md={8}>
							<Space>
								<label className={''}>审批状态：</label>
								<div className={'a'} onClick={getApprovalProgress}>
									<LeaderStatus status={detail.status} />
								</div>
							</Space>
						</Col>
					</Row>
					<Space>
						<Button type={'primary'} icon={<NotificationOutlined />}>
							提醒未填报员工
						</Button>
						{/*<Button type={'primary'} onClick={() => getTableData(queryType)}>*/}
						{/*	查询*/}
						{/*</Button>*/}
					</Space>
				</div>
				<div className={'flex align-center flex-wrap gap-12'}>
					<label>项目名称：</label>
					{detail.projectList?.map((item) => {
						return <Tag color={'processing'}>{item}</Tag>;
					})}
				</div>
				<div className={`flex align-center flex-wrap gap-12 margin-top-20 ${detail.managerUserName?.length === 0 ? 'display-none' : ''}`}>
					<label>项目负责人：</label>
					{detail.managerUserName?.map((item) => {
						return <Tag color={'processing'}>{item}</Tag>;
					})}
				</div>

				<UserWork
					ref={workRef}
					approveRef={queryType === QUERY_TYPE.project ? projectApproveRef : workApproveRef}
					allowSelect
					dateValue={dateValue}
					holidayList={holidayList}
					dataSource={dataSource}
					setApproveList={setApproveList}
				/>
				{/*<LeaderProgress ref={approveRef} detail={detail} userInfo={userInfo} />*/}
				<ApproveWork ref={workApproveRef} submit={submitApprove} />
				<ApproveProject ref={projectApproveRef} submit={submitProjectApprove} />
			</div>
		</div>
	);
});

export default UserDept;
