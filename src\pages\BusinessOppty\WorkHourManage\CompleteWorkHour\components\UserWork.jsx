/**
 * @description UserWork.jsx - 文件描述
 * <AUTHOR>
 *
 * Created on 2025-07-21 下午 2:42
 */
import React, {forwardRef, useEffect, useImperativeHandle, useState} from 'react';
import {Table} from "antd";
import {HOLIDAY_TYPE} from "@/pages/BusinessOppty/WorkHourManage/CompleteWorkHour/const";
import dayjs from "dayjs";

const UserWork = ({dateValue,holidayList, allowSelect = true}, ref) => {
    const [columns, setColumns] = useState([]);
    const [dataSource, setDataSource] = useState([]);
    /* 选中的列表项 */
    const [selectedRowKeys, setSelectedRowKeys] = useState([]);
    /* 选中行变化 */
    const onSelectChange = (newSelectedRowKeys) => {
        setSelectedRowKeys(newSelectedRowKeys);
    };
    /* 查询列表 */
    const getTableData = async (values) => {
        console.log('查询列表', values);
    };
    /* 同步 */
    useImperativeHandle(ref, () => ({
        getTableData,
    }));
    useEffect(() => {
        /* 表格表头配置 */
        let columns = [
            {
                title: '员工姓名',
                dataIndex: 'name',
                key: 'name',
                fixed: 'left',
            },
            {
                title: '部门名称',
                dataIndex: 'deptName',
                key: 'deptName',
                fixed: 'left',
            },
            {
                title: '总计工时（天）',
                dataIndex: 'totalWorkHours',
                key: 'totalWorkHours',
                fixed: 'left',
                render: (text) => <span>{text || 0}天</span>
            },
        ];
        if (dateValue && columns.length === 3 && holidayList.length > 0) {
            /* 获取当月工作日列表 */
            const workDayList = [];
            const {days} = holidayList[dateValue.month()];
            days.forEach(item => {
                if (item.type === HOLIDAY_TYPE.workday) {
                    const day = dayjs(item.date);
                    workDayList.push(item);
                    const morningData = {
                        title: day.format('D日上午'),
                        dataIndex: dayjs(item.date).format('YYYY-MM-DD'),
                        key: dayjs(item.date).format('YYYY-MM-DD'),
                        align: 'center',
                        width: 120,
                    };
                    const afternoonData = {
                        title: day.format('D日下午'),
                        dataIndex: dayjs(item.date).format('YYYY-MM-DD'),
                        key: dayjs(item.date).format('YYYY-MM-DD'),
                        align: 'center',
                        width: 120,
                    }
                    columns.push(morningData, afternoonData);
                }
            });
            console.log('workDayList', workDayList);
            setColumns(columns);
        }
    }, [dateValue, holidayList]);
    return (<Table
        className={'margin-top-20'}
        size={'small'}
        rowKey='id'
        columns={columns}
        dataSource={dataSource}
        pagination={false}
        bordered
        scroll={{x: 'max-content'}}
        rowSelection={ allowSelect ? {
            type: 'checkbox',
            selectedRowKeys: selectedRowKeys,
            onChange: onSelectChange,
        } : false }
    />)
}
export default forwardRef(UserWork);
