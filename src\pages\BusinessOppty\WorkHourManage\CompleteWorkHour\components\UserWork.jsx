/**
 * @description UserWork.jsx - 文件描述
 * <AUTHOR>
 *
 * Created on 2025-07-21 下午 2:42
 */
import React, { forwardRef, useEffect, useImperativeHandle, useMemo, useState } from 'react';
import { Button, Table, Typography } from 'antd';
import { HOLIDAY_TYPE } from '@/pages/BusinessOppty/WorkHourManage/CompleteWorkHour/const';
import dayjs from 'dayjs';

const { Paragraph } = Typography;
const UserWork = ({ dateValue, holidayList, allowSelect = true, dataSource, approveRef, setApproveList, }, ref) => {
	const [columns, setColumns] = useState([]);
	const [tableData, setTableData] = useState([]);
	/* 选中的列表项 */
	const [selectedRowKeys, setSelectedRowKeys] = useState([]);
	/* 选中行变化 */
	const onSelectChange = (newSelectedRowKeys) => {
		setSelectedRowKeys(newSelectedRowKeys);
		setApproveList(newSelectedRowKeys);
	};
	useImperativeHandle(ref, () => ({
		getSelectedRowKeys: selectedRowKeys,
		setSelectedRowKeys: setSelectedRowKeys,
	}));
	// 点击审批
	const handleApprove = (record) => {
		console.log('approve');
		approveRef?.current?.openApprove(record.taskTimes.filter(item => item.onLeave !== 1));
	};
	useEffect(() => {
		setTableData([...dataSource]);
	}, [dataSource]);
	useEffect(() => {
		/* 表格表头配置 */
		let columns = [
			{
				title: '员工姓名',
				dataIndex: 'userName',
				key: 'userName',
				fixed: 'left',
			},
			{
				title: '部门名称',
				dataIndex: 'departmentName',
				key: 'departmentName',
				fixed: 'left',
			},
			{
				title: '总计工时（天）',
				dataIndex: 'totalTaskTime',
				key: 'totalTaskTime',
				fixed: 'left',
				render: (text) => <span>{text / 2 || 0}天</span>,
			},
		];
		if (dateValue && columns.length === 3 && holidayList.length > 0) {
			/* 获取当月工作日列表 */
			const workDayList = [];
			const { days } = holidayList[dateValue.month()];
			days.forEach((item) => {
				if (item.type === HOLIDAY_TYPE.workday) {
					const day = dayjs(item.date);
					workDayList.push(item);
					const morningData = {
						title: day.format('D日上午'),
						dataIndex: dayjs(item.date).format('YYYY-MM-DD') + '-morning',
						key: dayjs(item.date).format('YYYY-MM-DD') + '-morning',
						align: 'center',
						width: 120,
						render: (text, { taskTimes }) => {
							const dateItem = taskTimes.find((item) => item.taskDate === day.format('YYYY-MM-DD') && item.timeDivision === 1) || {};
							const onLeave = dateItem.onLeave === 1;
							return (
								<Paragraph ellipsis={{ rows: 2, tooltip: true }} className={`text-align-left ${onLeave && 'color-ff7d00'}`} title={dateItem?.projectName}>
									{dateItem?.projectName || (onLeave && '请假') || '--'}
								</Paragraph>
							);
						},
					};
					const afternoonData = {
						title: day.format('D日下午'),
						dataIndex: dayjs(item.date).format('YYYY-MM-DD') + '-afternoon',
						key: dayjs(item.date).format('YYYY-MM-DD') + '-afternoon',
						align: 'center',
						width: 120,
						render: (text, record) => {
							const dateItem =
								record.taskTimes.find((item) => item.taskDate === day.format('YYYY-MM-DD') && item.timeDivision === 2) || {};
							const onLeave = dateItem.onLeave === 1;
							return (
								<Paragraph ellipsis={{ rows: 2, tooltip: true }} className={`text-align-left ${onLeave && 'color-ff7d00'}`} title={dateItem?.projectName}>
									{dateItem?.projectName || (onLeave && '请假') || '--'}
								</Paragraph>
							);
						},
					};
					columns.push(morningData, afternoonData);
				}
			});
			console.log('workDayList', workDayList);
		}
		if (allowSelect) {
			columns.push({
				title: '操作',
				key: 'operation',
				fixed: 'right',
				align: 'center',
				width: 100,
				render: (text, record) => {
					const { disabledApprove } = record;
					return (
						<Button disabled={disabledApprove} type={'link'} size={'small'} onClick={() => handleApprove(record)}>
							审批
						</Button>
					);
				},
			});
		}
		setColumns(columns);
	}, [dateValue, holidayList, allowSelect]);
	return (
		<Table
			className={'margin-top-20'}
			size={'small'}
			rowKey="userId"
			columns={columns}
			dataSource={tableData}
			pagination={false}
			scroll={{ x: 'max-content' }}
			rowSelection={
				allowSelect
					? {
							type: 'checkbox',
							selectedRowKeys: selectedRowKeys,
							onChange: onSelectChange,
							getCheckboxProps: (record) => ({
								disabled: record.disabledApprove,
							}),
						}
					: false
			}
		/>
	);
};
export default forwardRef(UserWork);
