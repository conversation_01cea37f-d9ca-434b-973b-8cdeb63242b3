/**
 * @description index.jsx - 填报工时
 * <AUTHOR>
 *
 * Created on 2025-07-11 下午 2:21
 */
import React, { useEffect, useRef, useState } from 'react';
import './components/index.scss';
import { useSelector } from 'react-redux';
import Staff from './components/Staff';
import UserDept from './components/UserDept';
import { useRouterLink } from '@/hook/useRouter';
import { queryWorkDate } from '@/api/Opportunity/WorkHourManage';
import { Breadcrumb, Spin } from 'antd';
import dayjs from 'dayjs';
import { useLocation } from 'react-router-dom';

const CompleteWorkHour = (props) => {
	const location = useLocation();
	const {
		state: { userType, sourcePathName, queryType },
	} = location;
	const { linkTo, searchParams } = useRouterLink();
	const [holidayList, setHolidayList] = useState([]);
	const userRef = useRef(null);
	const depRef = useRef(null);
	/* breadcrrumd 列表 */
	const [breadcrumbList, setBreadcrumbList] = useState([]);
	/* 用户信息获取 */
	const userInfo = useSelector((state) => {
		return state.user.userInfo;
	});
	const id = searchParams.get('id');
	const projectId = searchParams.get('projectId');
	useEffect(() => {
		const source = sourcePathName;
		const pageList = [];
		/* 获取来源页 */
		if (source) {
			let title = '我的工时';
			if (source.includes('myWorkHour')) {
				title = '我的工时';
			} else if (source.includes('departmentWorkHour')) {
				title = '工时审批';
			} else if (source.includes('workHourReport')) {
				title = '工时报表';
			}
			pageList.push({
				title: <a onClick={() => linkTo(source)}>{title}</a>,
				// href: source ,
			});
		}
		if (userType) {
			pageList.push({
				title: userType === 'staff' ? '员工填报工时' : '领导审核',
			});
		}
		if (pageList.length > 0) {
			setBreadcrumbList(pageList);
		}
		queryHoliday();
	}, []);

	/* 查询假日信息 */
	const queryHoliday = async () => {
		const date = dayjs();
		const res = await queryWorkDate({ year: date.format('YYYY') });
		if (res.data) {
			console.log(res.data);
			setHolidayList(res.data);
		}
	};
	return (
		<div>
			<Breadcrumb items={breadcrumbList} className={'margin-left-20 margin-top-20'} />
			{userType === 'staff' && <Staff userInfo={userInfo} holidayList={holidayList} id={id} ref={userRef} location={location} />}
			{userType === 'leader' && (
				<UserDept userInfo={userInfo} holidayList={holidayList} id={id} projectId={projectId} ref={depRef} location={location} />
			)}
			{!userType && <Spin />}
		</div>
	);
};
export default CompleteWorkHour;
