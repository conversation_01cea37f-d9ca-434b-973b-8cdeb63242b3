/**
 * @description index.jsx - 填报工时
 * <AUTHOR>
 *
 * Created on 2025-07-11 下午 2:21
 */
import React, {useEffect, useState} from 'react';
import './components/index.scss';
import {useSelector} from "react-redux";
import Staff from "./components/Staff";
import UserDept from "./components/UserDept";
import {useRouterLink} from "@/hook/useRouter";
import {queryWorkDate} from "@/api/Opportunity/WorkHourManage";
import {Spin} from 'antd';
import dayjs from "dayjs";

const CompleteWorkHour = (props) => {
    const {linkTo, searchParams} = useRouterLink();
    const [userType, setUserType] = useState('');
    const [holidayList, setHolidayList] = useState([]);

    /* 用户信息获取 */
    const userInfo = useSelector((state) => {
        return state.user.userInfo;
    });
    useEffect(() => {
        const type = searchParams.get('userType');
        if (type) {
            setUserType(type);
        }
        queryHoliday();
    }, []);

    /* 查询假日信息 */
    const queryHoliday = async () => {
        const date = dayjs();
        const res = await queryWorkDate({year: date.format('YYYY')});
        if (res.data) {
            console.log( res.data);
            setHolidayList(res.data);
        }
    }
    return (<div>
        {
            userType === 'staff' && <Staff userInfo={userInfo} holidayList={holidayList} />
        }
        {
            userType === 'leader' && <UserDept userInfo={userInfo} holidayList={holidayList} />
        }
        {
            !userType && <Spin />
        }
    </div>)
}
export default CompleteWorkHour;
