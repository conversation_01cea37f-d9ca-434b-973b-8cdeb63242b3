/**
 * @description Department.jsx - 文件描述
 * <AUTHOR>
 *
 * Created on 2025-07-21 上午 9:48
 */
import React, {useState} from 'react';
import {useRouterLink} from "@/hook/useRouter";
import {Button, Col, DatePicker, Divider, Form, Row, Select, Space, Table} from "antd";
import {APPROVAL_STATUS} from "@/pages/BusinessOppty/WorkHourManage/MyWorkHour/const";
import {ReloadOutlined, SearchOutlined} from "@ant-design/icons";

/* 部门查看部门 */
const Department = ({dateValue, userInfo, holidayList}) => {
    const {linkTo} = useRouterLink();
    const [loading, setLoading] = useState(false);
    /* 查看所有员工权限 */
    const [permission, setPermission] = useState(true);
    const [dataSource, setDataSource] = useState([{
        key: '1',
        id: '1',
        date: '2023-07-11',
        userName: '张三',
        deptName: '研发部',
        workHours: 8,
        reportTime: '2023-07-11 10:00:00'
    }]);
    const [pagination, setPagination] = useState({
        current: 1,
        pageSize: 10,
        total: 0,
    });
    const [form] = Form.useForm();
    const columns = [
        {
            title: '日期',
            dataIndex: 'date',
            key: 'date',
            align: 'center',
            width: 120,
        },
        {
            title: '部门名称',
            dataIndex: 'deptName',
            key: 'deptName',
            width: 220,
            align: 'center',
        },
        {
            title: '填报时间',
            dataIndex: 'reportTime',
            key: 'reportTime',
            // width: 120,
            align: 'center',
        },
        {
            title: '审批状态',
            dataIndex: 'approvalStatus',
            key: 'approvalStatus',
            width: 120,
            align: 'center',
        },
        {
            title: '操作',
            dataIndex: 'action',
            key: 'action',
            width: 180,
            align: 'center',
            render: (text, record, index) => {
                return <Space>
                    <Button type={'link'} size={'small'} onClick={() => editWorkHour(record)}>编辑</Button>
                    <Button type={'link'} size={'small'} danger>删除</Button>
                </Space>;
            }
        }
    ];
    /* 查询表格数据 */
    const getTableData = (args) => {
        setLoading(true);
        const values = form.getFieldsValue();
        const params = {
            ...values,
            pageIndex: pagination.current,
            pageSize: pagination.pageSize,
            ...args,
        };
        console.log('查询表格数据', params);
        // WorkHourApi.pageWorkHour(params).then(res => {
        // });
        setLoading(false);
    };
    /* 重置表单 */
    const onReset = () => {
        form.resetFields();
    };
    /* 修改页面 */
    const changePage = (page, pageSize) => {
        setPagination({...pagination, current: page, pageSize});
        getTableData({pageSize, pageIndex: page});
    };
    /* 编辑/审核    工时 */
    const editWorkHour = (record) => {
        linkTo(`/businessOppty/workHourManage/completeWorkHour?userType=leader&id=${record.id}`);
    }
    return (<div className={'bg-color-ffffff border-radius-8 padding-20'}>
        <Form form={form} layout={'inline'}
              className={'width-100per flex flex-sub align-start'}
        >
            <Row gutter={[20, 20]} className={'flex-sub'}>
                {
                    permission && (<>
                        <Col span={8}>
                            <Form.Item label="部门信息" name="deptId" className={'flex-sub '}>
                                <Select className={'width-100per'} placeholder={'请选择部门信息'} allowClear/>
                            </Form.Item>
                        </Col>
                    </>)
                }
                <Col span={8}>
                    <Form.Item label="日期" name="clueName" className={'flex-sub '}>
                        <DatePicker picker={'month'} placeholder={'请选择查询日期'} className={'width-100per'} allowClear/>
                    </Form.Item>
                </Col>
                <Col span={8}>
                    <Form.Item label="审批状态" name="createTime" className={'flex-sub '}>
                        <Select className={'width-100per'} placeholder={'请选择审批状态'} allowClear
                                options={APPROVAL_STATUS}/>
                    </Form.Item>
                </Col>
            </Row>
            {/*<Divider type="vertical" style={{margin: '0 16px'}}/>*/}
            {/*<div className="width-1 height-84 bg-color-e5e6eb flex-shirnk margin-lr-20"/>*/}
            <Form.Item noStyle>
                <Space direction={'horizontal'}>
                    <Button type={'primary'} htmlType={'submit'} icon={<SearchOutlined/>}
                            onClick={() => getTableData()}>查询</Button>
                    <Button htmlType={'reset'} icon={<ReloadOutlined/>} onClick={onReset}>重置</Button>
                </Space>
            </Form.Item>
        </Form>
        <Divider/>
        <Table
            rowKey="id"
            columns={columns}
            loading={loading}
            pagination={{
                ...pagination,
                onChange: changePage,
                showSizeChanger: true,
                showTotal: (total) => `共 ${total} 条`
            }}
            dataSource={dataSource}
        />
    </div>)
}
export default Department;
