/**
 * @description Department.jsx - 文件描述
 * <AUTHOR>
 *
 * Created on 2025-07-21 上午 9:48
 */
import React, { useEffect, useState } from 'react';
import { useRouterLink } from '@/hook/useRouter';
import { Button, Col, DatePicker, Divider, Form, Row, Select, Space, Table } from 'antd';
import { APPROVAL_STATUS } from '@/pages/BusinessOppty/WorkHourManage/MyWorkHour/const';
import { ReloadOutlined, SearchOutlined } from '@ant-design/icons';
import { getTaskTimeSummaryList } from '@/api/Opportunity/WorkHourManage';
import {
	QUERY_TYPE,
	APPROVAL_TYPE,
	APPROVAL_STATUS_LIST,
} from '@/pages/BusinessOppty/WorkHourManage/DepartmentWorkHour/const';
import ApproveStatus from '@/pages/BusinessOppty/WorkHourManage/MyWorkHour/components/ApproveStatus';
import { getTaskTimeApprovalList } from '@/api/Opportunity/ReportManage';
import dayjs from 'dayjs';
import LeaderStatus from '@/pages/BusinessOppty/WorkHourManage/MyWorkHour/components/LeaderStatus';

/* 部门查看部门 */
const Department = ({ dateValue, userInfo, holidayList, departmentList, userList }) => {
	const { linkTo } = useRouterLink();
	const [loading, setLoading] = useState(false);
	/* 查看所有员工权限 */
	const [permission, setPermission] = useState(true);
	const [dataSource, setDataSource] = useState([]);
	const [pagination, setPagination] = useState({
		current: 1,
		pageSize: 10,
		total: 0,
	});
	const [form] = Form.useForm();
	const columns = [
		{
			title: '日期',
			dataIndex: 'currentMonth',
			key: 'currentMonth',
			width: 120,
		},
		{
			title: '部门名称',
			dataIndex: 'departmentName',
			key: 'departmentName',
			// width: 220,
			render: (text, record) => <div className={'max-width-200 ellipsis'}>{text}</div>,
		},
		// {
		// 	title: '填报时间',
		// 	dataIndex: 'submitTime',
		// 	key: 'submitTime',
		// 	// width: 120,
		// 	align: 'center',
		// },
		{
			title: '审批状态',
			dataIndex: 'status',
			key: 'status',
			width: 120,
			align: 'left',
			render: (text, record) => <LeaderStatus status={text} record={record} />,
		},
		{
			title: '操作',
			dataIndex: 'action',
			key: 'action',
			width: 100,
			render: (text, record, index) => {
				return (
					<Space>
						<Button type={'link'} size={'small'} onClick={() => editWorkHour(record)}>
							编辑
						</Button>
					</Space>
				);
			},
		},
	];
	useEffect(() => {
		if (userInfo.id) {
			const {deptList, roleList} = userInfo;
			const permission = roleList.some((item) => ['report-hr', 'report-all'].includes(item.roleCode));
			/* 判断下有没有管理权限 */
			setPermission(permission);
			getTableData({}, permission);
		} else {
			getTableData();
		}
	}, [userInfo]);
	/* 查询表格数据 */
	const getTableData = async (args, allow) => {
		setLoading(true);
		const values = form.getFieldsValue();
		const params = {
			...values,
			pageNum: pagination.current,
			pageSize: pagination.pageSize,
			...args,
			type: APPROVAL_TYPE.department,
		};
		if (!permission || (typeof allow!== 'undefined' && !allow)) {
			const {deptList} = userInfo;
			params.departmentId = deptList[0].id;
		}
		if (params.currentMonth) {
			params.currentMonth = dayjs(params.currentMonth).format('YYYY-MM');
		}
		console.log('查询表格数据', params);
		const res = await getTaskTimeApprovalList(params);
		if (res.data) {
			setPagination({
				current: params.pageNum,
				pageSize: params.pageSize,
				total: res.data.length,
			});
			setDataSource(res.data);
		} else {
			setDataSource([]);
			setPagination({
				...pagination,
				total: 0,
			});
		}
		setLoading(false);
	};
	/* 重置表单 */
	const onReset = () => {
		form.resetFields();
		getTableData()
	};
	/* 修改页面 */
	const changePage = (page, pageSize) => {
		setPagination({ ...pagination, current: page, pageSize });
		// getTableData({ pageSize, pageIndex: page });
	};
	/* 编辑/审核    工时 */
	const editWorkHour = (record) => {
		console.log('编辑/审核    工时', record);
		const sourcePathName = window.location.pathname.replace('/gbac-bidmgt-admfrontend', '');
		linkTo(`/businessOppty/workHourManage/completeWorkHour`, {
			state: {
				id: record.id,
				userType: 'leader',
				sourcePathName: sourcePathName + `?queryType=${QUERY_TYPE.department}`,
				userId: record.userId,
				taskDate: record.currentMonth,
				approve: true,
				departmentId: record.departmentId,
				status: record.status,
				queryType: QUERY_TYPE.department,
			},
		});
	};
	return (
		<div className={'bg-color-ffffff border-radius-8 padding-20'}>
			<Form form={form} layout={'inline'} className={'width-100per flex flex-sub align-start'}>
				<Row gutter={[20, 20]} className={'flex-sub'}>
					{permission && (
						<>
							<Col span={8}>
								<Form.Item label="部门名称" name="departmentId" className={'flex-sub '}>
									<Select options={departmentList} className={'width-100per'}
											placeholder={'请选择部门名称'} allowClear showSearch optionFilterProp={'label'} />
								</Form.Item>
							</Col>
						</>
					)}
					<Col span={8}>
						<Form.Item label="日期" name="currentMonth" className={'flex-sub '}>
							<DatePicker picker={'month'} placeholder={'请选择查询日期'} className={'width-100per'} allowClear />
						</Form.Item>
					</Col>
					<Col span={8}>
						<Form.Item label="审批状态" name="status" className={'flex-sub '}>
							<Select className={'width-100per'} placeholder={'请选择审批状态'} allowClear options={APPROVAL_STATUS_LIST} />
						</Form.Item>
					</Col>
				</Row>
				{/*<Divider type="vertical" style={{margin: '0 16px'}}/>*/}
				{/*<div className="width-1 height-84 bg-color-e5e6eb flex-shirnk margin-lr-20"/>*/}
				<Form.Item noStyle>
					<Space direction={'horizontal'}>
						<Button type={'primary'} htmlType={'submit'} icon={<SearchOutlined />} onClick={() => getTableData()}>
							查询
						</Button>
						<Button htmlType={'reset'} icon={<ReloadOutlined />} onClick={onReset}>
							重置
						</Button>
					</Space>
				</Form.Item>
			</Form>
			<Divider />
			<Table
				bordered
				rowKey="id"
				columns={columns}
				pagination={{
					...pagination,
					onChange: changePage,
					showSizeChanger: true,
					showTotal: (total) => `共 ${total} 条`,
				}}
				dataSource={dataSource}
			/>
		</div>
	);
};
export default Department;
