/**
 * @description DepartmentProject.jsx - 文件描述
 * <AUTHOR>
 *
 * Created on 2025-07-21 上午 9:49
 */
import React, {useEffect, useState} from 'react';
import {HOLIDAY_TYPE} from "../const";
import dayjs from "dayjs";
import {Badge, Button, Col, DatePicker, Divider, Form, Row, Select, Space, Table, Tag} from "antd";
import {APPROVAL_STATUS, APPROVAL_STATUS_COLOR} from "@/pages/BusinessOppty/WorkHourManage/MyWorkHour/const";
import {NotificationOutlined, ReloadOutlined, SearchOutlined} from "@ant-design/icons";
import {useRouterLink} from "@/hook/useRouter";

/* 部门查看项目 */
const DepartmentProject = ({dateValue, userInfo, holidayList}) => {
    const {linkTo} = useRouterLink();
    const [dataSource, setDataSource] = useState([
        {
            id: '0',
            status: '2',
            projectId: '1',
            projectName: '天河城天河项目',
            date: '2025-07-19',
            projectManager: '张三',
            fillTime: dayjs().format('YYYY-MM-DD'),
            approvalStatus: '3',
        }
    ]);
    const [form] = Form.useForm();
    const [loading, setLoading] = useState(false);
    const [pagination, setPagination] = useState({
        current: 1,
        pageSize: 10,
        total: 0,
    });
    const [permission, setPermission] = useState(false);
    const columns = [
        {
            title: '日期',
            dataIndex: 'date',
            key: 'date',
            align: 'center',
            width: 120,
        },
        {
            title: '项目名称',
            dataIndex: 'projectName',
            key: 'projectName',
            width: 300,
        },
        {
            title: '项目负责人',
            dataIndex: 'projectManager',
            key: 'projectManager',
            width: 120,
        },
        {
            title: '填报时间',
            dataIndex: 'fillTime',
            key: 'fillTime',
            width: 120,
        },
        {
            title: '审批状态',
            dataIndex: 'approvalStatus',
            key: 'approvalStatus',
            width: 120,
            align: 'center',
            render: (text, record, index) => {
                return <Badge color={APPROVAL_STATUS_COLOR[record.approvalStatus]}
                             text={APPROVAL_STATUS[record.approvalStatus].label}/>;
            }
        },
        {
            title: '操作',
            dataIndex: 'action',
            key: 'action',
            width: 180,
            align: 'center',
            render: (text, record, index) => {
                return <Space>
                    <Button type={'link'} size={'small'} onClick={() => editWorkHour(record)}>编辑</Button>
                    <Button type={'link'} size={'small'} danger>删除</Button>
                </Space>;
            }
        }
    ];
    /* 编辑/审核    工时 */
    const editWorkHour = (record) => {
        linkTo(`/businessOppty/workHourManage/completeWorkHour?userType=leader&id=${record.id}`);
    }
    /* 查询表格数据 */
    const getTableData = (args) => {
        setLoading(true);
        const values = form.getFieldsValue();
        const params = {
            ...values,
            pageIndex: pagination.current,
            pageSize: pagination.pageSize,
            ...args,
        };
        console.log('查询表格数据', params);
        // WorkHourApi.pageWorkHour(params).then(res => {
        // });
        setLoading(false);
    };
    /* 重置表单 */
    const onReset = () => {
        form.resetFields();
    };
    return (<div className={'bg-color-ffffff border-radius-8 padding-20'}>
        <Form form={form} layout={'inline'}
              className={'width-100per flex flex-sub align-start'}
        >
            <Row gutter={[20, 20]} className={'flex-sub'}>
                <Col span={8}>
                    <Form.Item label="项目名称" name="projectId" className={'flex-sub '}>
                        <Select className={'width-100per'} placeholder={'请选择项目名称'} allowClear/>
                    </Form.Item>
                </Col>
                <Col span={8}>
                    <Form.Item label="日期" name="clueName" className={'flex-sub '}>
                        <DatePicker picker={'month'} placeholder={'请选择查询日期'} className={'width-100per'} allowClear/>
                    </Form.Item>
                </Col>
                <Col span={8}>
                    <Form.Item label="审批状态" name="createTime" className={'flex-sub '}>
                        <Select className={'width-100per'} placeholder={'请选择审批状态'} allowClear
                                options={APPROVAL_STATUS}/>
                    </Form.Item>
                </Col>
            </Row>
            <Form.Item noStyle>
                <Space direction={'horizontal'}>
                    <Button type={'primary'} htmlType={'submit'} icon={<SearchOutlined/>}
                            onClick={() => getTableData()}>查询</Button>
                    <Button htmlType={'reset'} icon={<ReloadOutlined/>} onClick={onReset}>重置</Button>
                </Space>
            </Form.Item>
        </Form>
        <Divider/>
        <Table
            loading={loading}
            className={'margin-top-20'}
            size={'small'}
            rowKey='id'
            columns={columns}
            dataSource={dataSource}
            pagination={pagination}
        />
    </div>)
};
export default DepartmentProject;
