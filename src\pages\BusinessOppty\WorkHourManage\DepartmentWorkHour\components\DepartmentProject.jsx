/**
 * @description DepartmentProject.jsx - 文件描述
 * <AUTHOR>
 *
 * Created on 2025-07-21 上午 9:49
 */
import React, { useEffect, useState } from 'react';
import { APPROVAL_STATUS_LIST, HOLIDAY_TYPE, QUERY_TYPE } from '../const';
import dayjs from 'dayjs';
import { Badge, Button, Col, DatePicker, Divider, Form, Row, Select, Space, Table, Tag } from 'antd';
import { APPROVAL_STATUS, APPROVAL_STATUS_COLOR } from '@/pages/BusinessOppty/WorkHourManage/MyWorkHour/const';
import { NotificationOutlined, ReloadOutlined, SearchOutlined } from '@ant-design/icons';
import { useRouterLink } from '@/hook/useRouter';
import { getTaskTimeSummaryList } from '@/api/Opportunity/WorkHourManage';
import ApproveStatus from '@/pages/BusinessOppty/WorkHourManage/MyWorkHour/components/ApproveStatus';
import LeaderStatus from '@/pages/BusinessOppty/WorkHourManage/MyWorkHour/components/LeaderStatus';
import { getTaskTimeApprovalList } from '@/api/Opportunity/ReportManage';

/* 部门查看项目 */
const DepartmentProject = ({ dateValue, userInfo, holidayList, projectList }) => {
	const { linkTo } = useRouterLink();
	const [selectedRowKeys, setSelectedRowKeys] = useState([]);
	const [dataSource, setDataSource] = useState([]);
	const [form] = Form.useForm();
	const [pagination, setPagination] = useState({
		current: 1,
		pageSize: 10,
		total: 0,
	});
	const [permission, setPermission] = useState(false);
	const columns = [
		{
			title: '日期',
			dataIndex: 'currentMonth',
			key: 'currentMonth',
			align: 'center',
			width: 120,
		},
		{
			title: '项目名称',
			dataIndex: 'projectName',
			key: 'projectName',
			// width: 300,
		},
		{
			title: '项目负责人',
			dataIndex: 'managerUserName',
			key: 'managerUserName',
			width: 120,
		},
		// {
		// 	title: '填报时间',
		// 	dataIndex: 'submitTime',
		// 	key: 'submitTime',
		// 	width: 120,
		// },
		{
			title: '审批状态',
			dataIndex: 'status',
			key: 'status',
			width: 120,
			align: 'left',
			render: (text, record) => <LeaderStatus status={text} record={record} />,
		},
		{
			title: '操作',
			dataIndex: 'action',
			key: 'action',
			width: 100,
			align: 'center',
			render: (text, record, index) => {
				return (
					<Space>
						<Button type={'link'} size={'small'} onClick={() => editWorkHour(record)}>
							编辑
						</Button>
					</Space>
				);
			},
		},
	];
	useEffect(() => {
		getTableData();
	}, []);
	/* 编辑/审核    工时 */
	const editWorkHour = (record) => {
		const sourcePathName = window.location.pathname.replace('/gbac-bidmgt-admfrontend', '');
		linkTo(`/businessOppty/workHourManage/completeWorkHour`, {
			state: {
				id: record.id,
				userType: 'leader',
				sourcePathName: sourcePathName + `?queryType=${QUERY_TYPE.project}`,
				userId: userInfo.id,
				projectId: record.projectId,
				taskDate: record.currentMonth,
				status: record.status,
				approve: true,
				queryType: QUERY_TYPE.project,
			},
		});
	};
	/* 查询表格数据 */
	const getTableData = async (args) => {
		const values = form.getFieldsValue();
		const params = {
			...values,
			pageNum: pagination.current,
			pageSize: pagination.pageSize,
			...args,
			type: QUERY_TYPE.project,
		};
		if (params.currentMonth) {
			params.currentMonth = dayjs(params.currentMonth).format('YYYY-MM');
		}
		console.log('查询表格数据', params);
		const res = await getTaskTimeApprovalList(params);
		if (res.data) {
			const dataSource = res.data.map((item) => {
				return {
					...item,
					key: item.id,
				};
			});
			setDataSource(dataSource);
			setPagination({
				pageSize: params.pageSize,
				current: params.pageNum,
				total: res.data.length,
			});
		} else {
			setDataSource([]);
			setPagination({
				...pagination,
				total: 0,
			});
		}
	};
	/* 重置表单 */
	const onReset = () => {
		form.resetFields();
		getTableData()
	};
	// 切换勾选项
	const onSelectChange = (selectedRowKeys) => {
		setSelectedRowKeys(selectedRowKeys);
	};
	// 修改页码
	const onPageChange = (pageIndex, pageSize) => {
		setPagination({
			...pagination,
			current: pageIndex,
			pageSize,
		});
		// getTableData({ pageSize, pageIndex });
	}
	return (
		<div className={'bg-color-ffffff border-radius-8 padding-20'}>
			<Form form={form} layout={'inline'} className={'width-100per flex flex-sub align-start'}>
				<Row gutter={[20, 20]} className={'flex-sub'}>
					<Col span={8}>
						<Form.Item label='项目名称' name='projectId' className={'flex-sub '}>
							<Select options={projectList} className={'width-100per'} placeholder={'请选择项目名称'}
									allowClear showSearch optionFilterProp={'label'} />
						</Form.Item>
					</Col>
					<Col span={8}>
						<Form.Item label='日期' name='currentMonth' className={'flex-sub '}>
							<DatePicker picker={'month'} placeholder={'请选择查询日期'} className={'width-100per'}
										allowClear />
						</Form.Item>
					</Col>
					<Col span={8}>
						<Form.Item label='审批状态' name='status' className={'flex-sub '}>
							<Select className={'width-100per'} placeholder={'请选择审批状态'} allowClear
									options={APPROVAL_STATUS_LIST} />
						</Form.Item>
					</Col>
				</Row>
				<Form.Item noStyle>
					<Space direction={'horizontal'}>
						<Button type={'primary'} htmlType={'submit'} icon={<SearchOutlined />}
								onClick={() => getTableData()}>
							查询
						</Button>
						<Button htmlType={'reset'} icon={<ReloadOutlined />} onClick={onReset}>
							重置
						</Button>
					</Space>
				</Form.Item>
			</Form>
			<Divider />
			<Table
				bordered
				className={'margin-top-20'}
				size={'small'}
				rowKey='id'
				columns={columns}
				dataSource={dataSource}
				pagination={{
					...pagination,
					showTotal: (total) => `共 ${total} 条数据`,
					onChange: onPageChange,
					showSizeChanger: true,
				}}
			/>
		</div>
	);
};
export default DepartmentProject;
