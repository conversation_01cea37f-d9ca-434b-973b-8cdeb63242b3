/**
 * @description DepartmentStaff.jsx - 文件描述
 * <AUTHOR>
 *
 * Created on 2025-07-21 上午 9:48
 */
import React, { useEffect, useState } from 'react';
import { useRouterLink } from '@/hook/useRouter';
import { Button, Col, DatePicker, Divider, Form, Row, Select, Space, Table } from 'antd';
import { APPROVAL_STATUS } from '@/pages/BusinessOppty/WorkHourManage/MyWorkHour/const';
import { ReloadOutlined, SearchOutlined } from '@ant-design/icons';
import { getTaskTimeSummaryList } from '@/api/Opportunity/WorkHourManage';
import { QUERY_TYPE } from '@/pages/BusinessOppty/WorkHourManage/DepartmentWorkHour/const';
import dayjs from 'dayjs';

/* 部门查看员工 */
const DepartmentStaff = ({ dateValue, userInfo, holidayList, departmentList, userList }) => {
	const { linkTo } = useRouterLink();
	/* 查看所有员工权限 */
	const [permission, setPermission] = useState(true);
	const [dataSource, setDataSource] = useState([]);

	const [pagination, setPagination] = useState({
		current: 1,
		pageSize: 10,
		total: 0,
	});
	const [form] = Form.useForm();
	const columns = [
		{
			title: '日期',
			dataIndex: 'currentMonth',
			key: 'currentMonth',
			align: 'center',
			width: 120,
		},
		{
			title: '员工姓名',
			dataIndex: 'userName',
			key: 'userName',
			align: 'center',
		},
		{
			title: '部门名称',
			dataIndex: 'departmentName',
			key: 'departmentName',
			width: 220,
			align: 'center',
		},
		{
			title: '总计工时（天）',
			dataIndex: 'totalTaskTime',
			key: 'totalTaskTime',
			width: 220,
			align: 'center',
		},
		{
			title: '填报时间',
			dataIndex: 'reportTime',
			key: 'reportTime',
			// width: 120,
			align: 'center',
		},
		{
			title: '审批状态',
			dataIndex: 'approvalStatus',
			key: 'approvalStatus',
			width: 120,
			align: 'center',
		},
		{
			title: '操作',
			dataIndex: 'action',
			key: 'action',
			width: 180,
			align: 'center',
			render: (text, record, index) => {
				return (
					<Space>
						<Button type={'link'} size={'small'} onClick={() => editWorkHour(record)}>
							查看/审批
						</Button>
						{/*<Button type={'link'} size={'small'} danger>删除</Button>*/}
					</Space>
				);
			},
		},
	];
	useEffect(() => {
		getTableData();
	}, []);
	/* 查询表格数据 */
	const getTableData = async (args) => {
		const values = form.getFieldsValue();
		const params = {
			...values,
			pageIndex: pagination.current,
			pageSize: pagination.pageSize,
			queryType: QUERY_TYPE.personal,
			...args,
		};
		console.log('查询表格数据', params);
		const res = await getTaskTimeSummaryList(params);
		if (res.data) {
			setDataSource(res.data);
			setPagination({
				pageSize: params.pageSize,
				current: params.pageIndex,
				total: res.total,
			});
		}
	};
	/* 重置表单 */
	const onReset = () => {
		form.resetFields();
	};
	/* 修改页面 */
	const changePage = (page, pageSize) => {
		setPagination({ ...pagination, current: page, pageSize });
		getTableData({ pageSize, pageIndex: page });
	};
	/* 编辑/审核    工时 */
	const editWorkHour = (record) => {
		const sourcePathName = window.location.pathname.replace('/gbac-bidmgt-admfrontend', '');
		linkTo(`/businessOppty/workHourManage/completeWorkHour`, {
			state: {
				id: record.id,
				userType: 'staff',
				sourcePathName,
				userId: record.userId,
				taskDate: record.currentMonth,
				approve: true,
			},
		});
	};
	return (
		<div className={'bg-color-ffffff border-radius-8 padding-20'}>
			<Form form={form} layout={'inline'} className={'width-100per flex flex-sub align-start'}>
				<Row gutter={[20, 20]} className={'flex-sub'}>
					{permission && (
						<>
							<Col span={8}>
								<Form.Item label="部门名称" name="departmentId" className={'flex-sub '}>
									<Select options={departmentList} className={'width-100per'} placeholder={'请选择部门名称'} allowClear />
								</Form.Item>
							</Col>
							<Col span={8}>
								<Form.Item label="员工信息" name="userId" className={'flex-sub '}>
									<Select options={userList} className={'width-100per'} placeholder={'请选择员工信息'} allowClear />
								</Form.Item>
							</Col>
						</>
					)}
					<Col span={8}>
						<Form.Item label="日期" name="taskDate" className={'flex-sub '}>
							<DatePicker picker={'month'} placeholder={'请选择查询日期'} className={'width-100per'} allowClear />
						</Form.Item>
					</Col>
					<Col span={8}>
						<Form.Item label="审批状态" name="approvalStatus" className={'flex-sub '}>
							<Select className={'width-100per'} placeholder={'请选择审批状态'} allowClear options={APPROVAL_STATUS} />
						</Form.Item>
					</Col>
				</Row>
				{/*<Divider type="vertical" style={{margin: '0 16px'}}/>*/}
				{/*<div className="width-1 height-84 bg-color-e5e6eb flex-shirnk margin-lr-20"/>*/}
				<Form.Item noStyle>
					<Space direction={'horizontal'}>
						<Button type={'primary'} htmlType={'submit'} icon={<SearchOutlined />} onClick={() => getTableData()}>
							查询
						</Button>
						<Button htmlType={'reset'} icon={<ReloadOutlined />} onClick={onReset}>
							重置
						</Button>
					</Space>
				</Form.Item>
			</Form>
			<Divider />
			<Table
				rowKey="id"
				columns={columns}
				pagination={{
					...pagination,
					onChange: changePage,
					showSizeChanger: true,
					showTotal: (total) => `共 ${total} 条`,
				}}
				dataSource={dataSource}
			/>
		</div>
	);
};
export default DepartmentStaff;
