/**
 * @description UserDept.jsx - 文件描述
 * <AUTHOR>
 *
 * Created on 2025-07-18 上午 11:41
 */
import React, { useEffect, useState } from 'react';
import { DEPARTMENT_TABS, QUERY_TYPE } from './const';
import { Button, Space, message } from 'antd';
import dayjs from 'dayjs';
import { useSelector } from 'react-redux';
import { queryWorkDate } from '@/api/Opportunity/WorkHourManage';
import DepartmentStaff from './components/DepartmentStaff';
import Department from './components/Department';
import DepartmentProject from './components/DepartmentProject';
import { getDeptData, getInnerUserList } from '@/utils/dictionary';
import { getByPermissionPerms } from '@/api/common';
import { pageProject } from '@/api/Opportunity/Project';
import { useRouterLink } from '@/hook/useRouter';

const DepartmentWorkHour = () => {
	const { searchParams } = useRouterLink();
	const [activeTab, setActiveTab] = useState(QUERY_TYPE.department);
	const [dateValue, setDateValue] = useState(dayjs());
	const [holidayList, setHolidayList] = useState([]);
	/* 部门列表 */
	const [departmentList, setDepartmentList] = useState([]);
	/* 获取员工列表 */
	const [userList, setUserList] = useState([]);
	/* 项目列表 */
	const [projectList, setProjectList] = useState([]);

	/* 用户信息获取 */
	const userInfo = useSelector((state) => {
		return state.user.userInfo;
	});

	useEffect(() => {
		const queryType = searchParams.get('queryType');
		if (queryType) {
			setActiveTab(+queryType);
		}
		queryHoliday();
		getDepartmentList();
		getUserList();
		getProjectList();
	}, []);
	/* 查询假日信息 */
	const queryHoliday = async () => {
		const date = dayjs();
		const res = await queryWorkDate({ year: date.format('YYYY') });
		if (res.data) {
			console.log(res.data);
			setHolidayList(res.data);
		}
	};
	/* 获取项目列表 */
	const getProjectList = async () => {
		console.log('获取项目列表');
		const res = await pageProject({ pageNum: 1, pageSize: 10000 });
		if (res.data) {
			setProjectList(
				res.data.records.map((item) => ({
					...item,
					label: item.name,
					value: item.id,
				})),
			);
		}
	};
	// 获取部门列表
	const getDepartmentList = async () => {
		const res = await getDeptData();
		if (res) {
			console.log(res);
			setDepartmentList(res);
		}
	};
	/* 员工列表 */
	const getUserList = async () => {
		const res = await getInnerUserList({});
		if (res) {
			console.log(res);
			setUserList(
				res.data.map((item) => {
					return {
						...item,
						label: item.userName,
						value: item.id,
					};
				}),
			);
		}
	};
	/* 切换tabs */
	const changeTab = (value) => {
		setActiveTab(value);
	};
	// 导出
	const exportExcel = () => {
		console.log('导出');
		switch (activeTab) {
			case 1:
				// 员工导出
				break;
			case 2:
				// 部门导出
				break;
			case 3:
				// 项目导出
				break;
		}
	};

	return (
		<div className={'flex-sub flex flex-direction-column margin-20 padding-20 '}>
			<div
				className='padding-20 bg-color-ffffff border-radius-8 flex justify-between align-center line-height-24 margin-bottom-20'>
				<div className={'font-size-16 flex flex-sub gap-40'}>
					{DEPARTMENT_TABS.map((item) => {
						return (
							<div key={item.value} className={'flex align-center'} onClick={() => changeTab(item.value)}>
								<span
									className={`font-size-16 cursor-pointer font-weight-600 ${activeTab === item.value ? 'color-165dff' : 'color-1d2129'}`}
								>
									{item.label}
								</span>
							</div>
						);
					})}
				</div>
				<Space>{/*<Button type={'primary'} onClick={exportExcel}>导出</Button>*/}</Space>
			</div>
			{activeTab === 1 && (
				<DepartmentStaff
					dateValue={dateValue}
					userInfo={userInfo}
					holidayList={holidayList}
					departmentList={departmentList}
					userList={userList}
				/>
			)}
			{activeTab === 2 && (
				<Department dateValue={dateValue} userInfo={userInfo} holidayList={holidayList}
							departmentList={departmentList} userList={userList} />
			)}
			{activeTab === 3 && <DepartmentProject dateValue={dateValue} userInfo={userInfo} holidayList={holidayList}
												   projectList={projectList} />}
		</div>
	);
};
export default DepartmentWorkHour;
