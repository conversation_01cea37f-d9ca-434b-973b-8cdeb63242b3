/**
 * @description UserDept.jsx - 文件描述
 * <AUTHOR>
 *
 * Created on 2025-07-18 上午 11:41
 */
import React, {useEffect, useState} from 'react';
import {DEPARTMENT_TABS,} from "./const";
import {Button, Space, message,} from "antd";
import dayjs from "dayjs";
import {useSelector} from "react-redux";
import {queryWorkDate} from "@/api/Opportunity/WorkHourManage";
import DepartmentStaff from "./components/DepartmentStaff";
import Department from "./components/Department";
import DepartmentProject from "./components/DepartmentProject";

const DepartmentWorkHour = () => {
    const [activeTab, setActiveTab] = useState(DEPARTMENT_TABS[0].value);
    const [dateValue, setDateValue] = useState(dayjs('2025-04-01'));
    const [holidayList, setHolidayList] = useState([]);

    /* 用户信息获取 */
    const userInfo = useSelector((state) => {
        return state.user.userInfo;
    });

    useEffect(() => {
        queryHoliday();
    }, []);
    /* 查询假日信息 */
    const queryHoliday = async () => {
        const date = dayjs();
        const res = await queryWorkDate({year: date.format('YYYY')});
        if (res.data) {
            console.log( res.data);
            setHolidayList(res.data);
        }
    }
    /* 切换tabs */
    const changeTab = (value) => {
        setActiveTab(value);
    };
    /* 审批 */
    const submitAudit = () => {
        console.log('提交审批');
        message.success('提交成功');
    }

    return (<div className={'flex-sub flex flex-direction-column margin-20 padding-20 '}>
        <div
            className="padding-20 bg-color-ffffff border-radius-8 flex justify-between align-center line-height-24 margin-bottom-20">
            <div className={'font-size-16 flex flex-sub gap-40'}>
                {
                    DEPARTMENT_TABS.map(item => {
                        return <div key={item.value} className={'flex align-center'}
                                    onClick={() => changeTab(item.value)}>
                            <span
                                className={`font-size-16 cursor-pointer font-weight-600 ${activeTab === item.value ? 'color-165dff' : 'color-1d2129'}`}>
                                {item.label}
                            </span>
                        </div>;
                    })
                }
            </div>
            <Space>
                <Button type={'primary'} onClick={submitAudit}>提交</Button>
            </Space>
        </div>
        {
            activeTab === 1 && <DepartmentStaff dateValue={dateValue} userInfo={userInfo} holidayList={holidayList}/>
        }
        {
            activeTab === 2 && <Department dateValue={dateValue} userInfo={userInfo} holidayList={holidayList}/>
        }
        {
            activeTab === 3 && <DepartmentProject dateValue={dateValue} userInfo={userInfo} holidayList={holidayList}/>
        }
    </div>)
}
export default DepartmentWorkHour;




