/**
 * @description ApproveStatus.jsx - 文件描述
 * <AUTHOR>
 *
 * Created on 2025-07-31 下午 5:19
 */
import React from 'react';
import { Badge } from 'antd';
import { APPROVAL_STATUS, APPROVAL_STATUS_COLOR } from '../const';

const ApproveStatus = ({ status, record, onClick }) => {
	// 点击状态
	const getApprovalProgress = () => {
		onClick && onClick();
	};
	return (
		<>
			<Badge
				status={APPROVAL_STATUS_COLOR[status]}
				onClick={getApprovalProgress}
				text={APPROVAL_STATUS.find((item) => item.value === status)?.label || '暂存'}
			/>
		</>
	);
};
export default ApproveStatus;
