/**
 * @description LeaderStatus.jsx - 文件描述
 * <AUTHOR>
 *
 * Created on 2025-07-31 下午 5:19
 */
import React from 'react';
import { Badge } from 'antd';
import { LEADER_APPROVAL_STATUS, LEADER_APPROVAL_STATUS_COLOR } from '../const';

const LeaderStatus = ({ status, record, onClick }) => {
	// 点击状态
	const getApprovalProgress = () => {
		onClick && onClick(record);
	};
	return (
		<>
			<Badge
				status={LEADER_APPROVAL_STATUS_COLOR[status]}
				onClick={getApprovalProgress}
				text={LEADER_APPROVAL_STATUS.find((item) => item.value === status)?.label || '待审批'}
			/>
		</>
	);
};
export default LeaderStatus;
