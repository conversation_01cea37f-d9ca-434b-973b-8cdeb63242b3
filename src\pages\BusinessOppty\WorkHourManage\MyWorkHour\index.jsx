/**
 * @description index.jsx - 我的工时
 * <AUTHOR>
 *
 * Created on 2025-07-11 下午 2:21
 */
import React, { useEffect, useState } from 'react';
import { Button, Col, DatePicker, Divider, Form, Row, Select, Space, Table } from 'antd';
import { CloudDownloadOutlined, PlusOutlined, ReloadOutlined, SearchOutlined } from '@ant-design/icons';
import { APPROVAL_STATUS, WORK_HOUR_TAB } from './const';
import { getTaskTimeSummaryList, queryHoliday } from '@/api/Opportunity/WorkHourManage';
import { useRouterLink } from '@/hook/useRouter';
import { getDeptData } from '@/utils/dictionary';
import { useSelector } from 'react-redux';
import { QUERY_TYPE } from '@/pages/BusinessOppty/WorkHourManage/DepartmentWorkHour/const';
import dayjs from 'dayjs';
import ApproveStatus from '@/pages/BusinessOppty/WorkHourManage/MyWorkHour/components/ApproveStatus';

const MyWorkHour = () => {
	const { linkTo } = useRouterLink();
	const [activeTab, setActiveTab] = useState('personal');
	const [loading, setLoading] = useState(false);
	/* 查看所有员工权限 */
	const [permission, setPermission] = useState(false);
	/* 部门列表 */
	const [departmentList, setDepartmentList] = useState([]);
	/* 员工列表 */
	const [userList, setUserList] = useState([]);
	const [dataSource, setDataSource] = useState([]);
	const [pagination, setPagination] = useState({
		current: 1,
		pageSize: 10,
		total: 0,
	});
	const [form] = Form.useForm();
	/* 用户信息获取 */
	const userInfo = useSelector((state) => {
		return state.user.userInfo;
	});
	const columns = [
		{
			title: '日期',
			dataIndex: 'currentMonth',
			key: 'currentMonth',
			align: 'center',
			width: 120,
		},
		{
			title: '员工姓名',
			dataIndex: 'userName',
			key: 'userName',
			align: 'center',
		},
		{
			title: '部门名称',
			dataIndex: 'departmentName',
			key: 'departmentName',
			width: 220,
			align: 'center',
		},
		{
			title: '总计工时（天）',
			dataIndex: 'totalTaskTime',
			key: 'totalTaskTime',
			width: 220,
			align: 'center',
			render: (text, record, index) => {
				return <div>{record.totalTaskTime / 2}</div>;
			},
		},
		{
			title: '填报时间',
			dataIndex: 'submitTime',
			key: 'submitTime',
			// width: 120,
			align: 'center',
		},
		{
			title: '审批状态',
			dataIndex: 'approveStatus',
			key: 'approveStatus',
			width: 120,
			align: 'center',
			render: (text, record) => <ApproveStatus status={text} record={record} />,
		},
		{
			title: '操作',
			dataIndex: 'action',
			key: 'action',
			width: 180,
			align: 'center',
			render: (text, record, index) => {
				return (
					<Space>
						<Button type={'link'} size={'small'} onClick={() => editWorkHour(record)}>
							编辑
						</Button>
					</Space>
				);
			},
		},
	];

	useEffect(() => {
		getDepartmentList();
		getUserList();
		getTableData();
	}, []);
	/* 查询表格数据 */
	const getTableData = async (args) => {
		const values = form.getFieldsValue();
		const params = {
			...values,
			// pageIndex: pagination.current,
			// pageSize: pagination.pageSize,
			...args,
			userId: userInfo.id,
			queryType: QUERY_TYPE.personal,
			currentMonth: values.taskDate ? dayjs(values.taskDate).format('YYYY-MM') : undefined,
		};
		const res = await getTaskTimeSummaryList(params);
		if (res.data) {
			setDataSource(res.data);
		} else {
			setDataSource([]);
		}
		console.log('查询表格数据', params);
		// WorkHourApi.pageWorkHour(params).then(res => {
		// });
	};
	/* 导出数据 */
	const exportData = () => {
		message.success('导出数据');
	};
	/* 获取人员列表 */
	const getUserList = async () => {
		console.log('获取人员列表');
		// todo: 获取人员列表接口
		const list = [];
		setUserList(list);
	};
	// 获取部门列表
	const getDepartmentList = async () => {
		const res = await getDeptData();
		if (res) {
			console.log(res);
			setDepartmentList(res);
		}
	};
	/* 新增数据 */
	const addData = () => {
		const sourcePathName = window.location.pathname.replace('/gbac-bidmgt-admfrontend', '');
		linkTo(`/businessOppty/workHourManage/completeWorkHour`, {
			state: {
				// id: record.id,
				userType: 'staff',
				sourcePathName,
				userId: userInfo.id,
				taskDate: dayjs().format('YYYY-MM'),
				create: true,
			},
		});
	};
	/* 重置表单 */
	const onReset = () => {
		form.resetFields();
	};
	/* 修改Tabs */
	const changeTab = (value) => {
		setActiveTab(value);
		getTableData();
	};
	/* 编辑/审核    工时 */
	const editWorkHour = (record) => {
		const sourcePathName = window.location.pathname.replace('/gbac-bidmgt-admfrontend', '');
		linkTo(`/businessOppty/workHourManage/completeWorkHour`, {
			state: {
				id: record.id,
				userType: 'staff',
				sourcePathName,
				userId: record.userId,
				taskDate: record.currentMonth,
			},
		});
	};
	// 不可选日期
	const disabledDate = (current) => {
		return current && current > dayjs().endOf('month');
	};
	return (
		<div className={'flex-sub flex flex-direction-column margin-20 padding-20  border-radius-4'}>
			<div className="padding-20 bg-color-ffffff border-radius-8 flex justify-between align-center line-height-24 margin-bottom-20">
				<div className={'font-size-16 flex flex-sub gap-40'}>
					{WORK_HOUR_TAB.map((item) => {
						return (
							<div key={item.value} className={'flex align-center'} onClick={() => changeTab(item.value)}>
								<span
									className={`font-size-16 cursor-pointer font-weight-600 ${activeTab === item.value ? 'color-165dff' : 'color-1d2129'}`}
								>
									{item.label}
								</span>
							</div>
						);
					})}
				</div>
				<Space>
					<Button type={'primary'} icon={<PlusOutlined />} onClick={addData}>
						填报
					</Button>
				</Space>
			</div>
			<div className={'bg-color-ffffff padding-20 border-radius-8'}>
				<Form form={form} layout={'inline'} className={'width-100per flex flex-sub align-start'}>
					<Row gutter={[20, 20]} className={'flex-sub'}>
						{permission && (
							<>
								<Col span={8}>
									<Form.Item label="部门信息" name="departmentId" className={'flex-sub '}>
										<Select options={departmentList} className={'width-100per'} placeholder={'请选择部门信息'} allowClear />
									</Form.Item>
								</Col>
								<Col span={8}>
									<Form.Item label="员工信息" name="userId" className={'flex-sub '}>
										<Select options={userList} className={'width-100per'} placeholder={'请选择员工信息'} allowClear />
									</Form.Item>
								</Col>
							</>
						)}
						<Col span={8}>
							<Form.Item label="日期" name="taskDate" className={'flex-sub '}>
								<DatePicker
									picker={'month'}
									placeholder={'请选择查询日期'}
									className={'width-100per'}
									allowClear
									disabledDate={disabledDate}
								/>
							</Form.Item>
						</Col>
						<Col span={8}>
							<Form.Item label="审批状态" name="approvalStatus" className={'flex-sub '}>
								<Select className={'width-100per'} placeholder={'请选择审批状态'} allowClear options={APPROVAL_STATUS} />
							</Form.Item>
						</Col>
					</Row>
					{/*<Divider type="vertical" style={{margin: '0 16px'}}/>*/}
					{/*<div className="width-1 height-84 bg-color-e5e6eb flex-shirnk margin-lr-20"/>*/}
					<Form.Item noStyle>
						<Space direction={'horizontal'}>
							<Button type={'primary'} htmlType={'submit'} icon={<SearchOutlined />} onClick={() => getTableData()}>
								查询
							</Button>
							<Button htmlType={'reset'} icon={<ReloadOutlined />} onClick={onReset}>
								重置
							</Button>
						</Space>
					</Form.Item>
				</Form>
				<Divider />
				<Table
					rowKey="id"
					columns={columns}
					loading={loading}
					pagination={false}
					// pagination={{
					//     ...pagination,
					//     onChange: changePage,
					//     showSizeChanger: true,
					//     showTotal: (total) => `共 ${total} 条`
					// }}
					dataSource={dataSource}
				/>
			</div>
		</div>
	);
};
export default MyWorkHour;
