/**
 * @description index.jsx - 我的工时
 * <AUTHOR>
 *
 * Created on 2025-07-11 下午 2:21
 */
import React, {useEffect, useState} from 'react';
import {Button, Col, DatePicker, Divider, Form, Row, Select, Space, Table} from "antd";
import {CloudDownloadOutlined, PlusOutlined, ReloadOutlined, SearchOutlined} from "@ant-design/icons";
import {APPROVAL_STATUS, WORK_HOUR_TAB} from "./const";
import {queryHoliday} from "@/api/Opportunity/WorkHourManage";
import {useRouterLink} from "@/hook/useRouter";

const MyWorkHour = () => {
    const {linkTo} = useRouterLink();
    const [activeTab, setActiveTab] = useState('personal');
    const [loading, setLoading] = useState(false);
    /* 查看所有员工权限 */
    const [permission, setPermission] = useState(true);
    const [dataSource, setDataSource] = useState([{
        key: '1',
        id: '1',
        date: '2023-07-11',
        userName: '张三',
        deptName: '研发部',
        workHours: 8,
        reportTime: '2023-07-11 10:00:00'
    }]);
    const [pagination, setPagination] = useState({
        current: 1,
        pageSize: 10,
        total: 0,
    });
    const [form] = Form.useForm();

    const columns = [
        {
            title: '日期',
            dataIndex: 'date',
            key: 'date',
            align: 'center',
            width: 120,
        },
        {
            title: '员工姓名',
            dataIndex: 'userName',
            key: 'userName',
            align: 'center',
        },
        {
            title: '部门名称',
            dataIndex: 'deptName',
            key: 'deptName',
            width: 220,
            align: 'center',
        },
        {
            title: '总计工时（天）',
            dataIndex: 'workHours',
            key: 'workHours',
            width: 220,
            align: 'center',
        },
        {
            title: '填报时间',
            dataIndex: 'reportTime',
            key: 'reportTime',
            // width: 120,
            align: 'center',
        },
        {
            title: '审批状态',
            dataIndex: 'approvalStatus',
            key: 'approvalStatus',
            width: 120,
            align: 'center',
        },
        {
            title: '操作',
            dataIndex: 'action',
            key: 'action',
            width: 180,
            align: 'center',
            render: (text, record, index) => {
                return <Space>
                    <Button type={'link'} size={'small'} onClick={() => editWorkHour(record)}>编辑</Button>
                    <Button type={'link'} size={'small'} danger>删除</Button>
                </Space>;
            }
        }
    ];

    /* 查询表格数据 */
    const getTableData = (args) => {
        setLoading(true);
        const values = form.getFieldsValue();
        const params = {
            ...values,
            pageIndex: pagination.current,
            pageSize: pagination.pageSize,
            ...args,
        };
        console.log('查询表格数据', params);
        // WorkHourApi.pageWorkHour(params).then(res => {
        // });
        setLoading(false);
    };
    /* 导出数据 */
    const exportData = () => {

    };
    /* 新增数据 */
    const addData = () => {
        linkTo(`/businessOppty/workHourManage/completeWorkHour?userType=staff`);
    };
    /* 重置表单 */
    const onReset = () => {
        form.resetFields();
    };
    /* 修改页面 */
    const changePage = (page, pageSize) => {
        setPagination({...pagination, current: page, pageSize});
        getTableData({pageSize, pageIndex: page});
    };
    /* 修改Tabs */
    const changeTab = (value) => {
        setActiveTab(value);
        getTableData();
    }
    /* 编辑/审核    工时 */
    const editWorkHour = (record) => {
        linkTo(`/businessOppty/workHourManage/completeWorkHour?userType=staff&id=${record.id}`);
    }
    return (<div className={'flex-sub flex flex-direction-column margin-20 padding-20  border-radius-4'}>
        <div
            className="padding-20 bg-color-ffffff border-radius-8 flex justify-between align-center line-height-24 margin-bottom-20">
            <div className={'font-size-16 flex flex-sub gap-40'}>
                {
                    WORK_HOUR_TAB.map(item => {
                        return <div key={item.value} className={'flex align-center'}
                                    onClick={() => changeTab(item.value)}>
                            <span
                                className={`font-size-16 cursor-pointer font-weight-600 ${activeTab === item.value ? 'color-165dff' : 'color-1d2129'}`}>{item.label}</span>
                        </div>;
                    })
                }
            </div>
            <Space>
                <Button icon={<CloudDownloadOutlined/>} onClick={exportData}>导出</Button>
                <Button type={'primary'} icon={<PlusOutlined/>} onClick={addData}>填报</Button>
            </Space>
        </div>
        <div className={'bg-color-ffffff padding-20 border-radius-8'}>
            <Form form={form} layout={'inline'}
                  className={'width-100per flex flex-sub align-start'}
            >
                <Row gutter={[20, 20]} className={'flex-sub'}>
                    {
                        permission && (<>
                            <Col span={8}>
                                <Form.Item label="部门信息" name="deptId" className={'flex-sub '}>
                                    <Select className={'width-100per'} placeholder={'请选择部门信息'} allowClear/>
                                </Form.Item>
                            </Col>
                            <Col span={8}>
                                <Form.Item label="员工信息" name="userId" className={'flex-sub '}>
                                    <Select className={'width-100per'} placeholder={'请选择员工信息'} allowClear/>
                                </Form.Item>
                            </Col>
                        </>)
                    }
                    <Col span={8}>
                        <Form.Item label="日期" name="clueName" className={'flex-sub '}>
                            <DatePicker picker={'month'} placeholder={'请选择查询日期'} className={'width-100per'} allowClear/>
                        </Form.Item>
                    </Col>
                    <Col span={8}>
                        <Form.Item label="审批状态" name="createTime" className={'flex-sub '}>
                            <Select className={'width-100per'} placeholder={'请选择审批状态'} allowClear
                                    options={APPROVAL_STATUS}/>
                        </Form.Item>
                    </Col>
                </Row>
                {/*<Divider type="vertical" style={{margin: '0 16px'}}/>*/}
                {/*<div className="width-1 height-84 bg-color-e5e6eb flex-shirnk margin-lr-20"/>*/}
                <Form.Item noStyle>
                    <Space direction={'horizontal'}>
                        <Button type={'primary'} htmlType={'submit'} icon={<SearchOutlined/>}
                                onClick={() => getTableData()}>查询</Button>
                        <Button htmlType={'reset'} icon={<ReloadOutlined/>} onClick={onReset}>重置</Button>
                    </Space>
                </Form.Item>
            </Form>
            <Divider/>
            <Table
                rowKey="id"
                columns={columns}
                loading={loading}
                pagination={{
                    ...pagination,
                    onChange: changePage,
                    showSizeChanger: true,
                    showTotal: (total) => `共 ${total} 条`
                }}
                dataSource={dataSource}
            />
        </div>
    </div>)
}
export default MyWorkHour;
