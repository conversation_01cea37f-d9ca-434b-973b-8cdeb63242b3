/**
 * @description index.jsx - 工时报表
 * <AUTHOR>
 *
 * Created on 2025-07-11 下午 2:21
 */
import React, {useEffect, useRef, useState} from 'react';
import {Button, Col, Collapse, DatePicker, Form, Row, Select, Space, Table} from "antd";
import {CloudDownloadOutlined, ReloadOutlined, SearchOutlined} from "@ant-design/icons";
import {TABS_TYPE} from "./const";
import UserWork from "@/pages/BusinessOppty/WorkHourManage/CompleteWorkHour/components/UserWork";
import dayjs from "dayjs";
import {queryWorkDate} from "@/api/Opportunity/WorkHourManage";

const hiddenFormItem = {
    depart: ['projectLeader', 'projectId'],
    project: ['ownCompany', 'deptId'],
    summary: ['ownCompany', 'projectId', 'projectLeader', 'deptId'],
};
const WorkHourReport = () => {
    const [activeTab, setActiveTab] = useState('depart');
    const [dateValue, setDateValue] = useState(dayjs());
    const [holidayList, setHolidayList] = useState([]);
    /* 项目列表 */
    const [projectList, setProjectList] = useState([]);
    const [form] = Form.useForm();
    const tableRef = useRef(null);
    useEffect(() => {
        queryHoliday();
        getProjectList();
        form.setFieldsValue({date: dateValue});
    }, []);
    /* 查询假日信息 */
    const queryHoliday = async () => {
        const res = await queryWorkDate({year: dateValue.format('YYYY')});
        if (res.data) {
            console.log(res.data);
            setHolidayList(res.data);
        }
    }
    /* 获取项目列表 */
    const getProjectList = async () => {
        console.log('获取项目列表');
        const list = [
            {
                projectId: '1',
                projectName: '项目1',
                projectLeader: '1',
                projectLeaderName: '张三',
                ownCompany: '1',
                ownCompanyName: '公司1',
            },
            {
                projectId: '2',
                projectName: '项目2',
                projectLeader: '2',
                projectLeaderName: '李四',
                ownCompany: '2',
            }
        ];
        setProjectList(list);
    };
    const changeTab = (value) => {
        setActiveTab(value);
    };
    /* 导出 */
    const exportData = () => {
        console.log('导出');
    };
    /* 重置表单 */
    const onReset = () => {
        form.resetFields();
    };
    /* 查询表格数据 */
    const getTableData = () => {
        console.log('查询表格数据');
        const params = form.getFieldsValue();
        tableRef.current.getTableData(params);
    };
    /* 修改日期 */
    const onDateChange = (value) => {
        setDateValue(value);
    };
    /* 汇总表头 */
    const summaryColumns = [
        {
            title: '项目名称',
            dataIndex: 'projectName',
            key: 'projectName',
            fixed: 'left',
            ellipsis: true,
            width: 200,
        },
        {
            title: '项目负责人',
            dataIndex: 'projectLeaderName',
            key: 'projectLeaderName',
            fixed: 'left',
            width: 150,
        },
        {
            title: '所属公司',
            dataIndex: 'ownCompanyName',
            key: 'ownCompanyName',
            fixed: 'left',
            width: 150,
        },
    ];
    return (<div className={'flex-sub flex flex-direction-column margin-20 padding-20'}>
        <div
            className="padding-20 bg-color-ffffff border-radius-8 flex justify-between align-center line-height-24 margin-bottom-20">
            <div className={'font-size-16 flex flex-sub gap-40'}>
                {
                    TABS_TYPE.map(item => {
                        return <div key={item.id} className={'flex align-center'}
                                    onClick={() => changeTab(item.id)}>
                            <span
                                className={`font-size-16 cursor-pointer font-weight-600 ${activeTab === item.id ? 'color-165dff' : 'color-1d2129'}`}>{item.name}</span>
                        </div>;
                    })
                }
            </div>
            <Space>
                <Button type={'primary'} icon={<CloudDownloadOutlined/>} onClick={exportData}>导出</Button>
            </Space>
        </div>
        <div className={'bg-color-ffffff padding-20 border-radius-8'}>
            <Form form={form} layout={'inline'}
                  className={'width-100per flex flex-sub align-start'}
            >
                <Row gutter={[20, 20]} className={'flex-sub'}>
                    <Col span={hiddenFormItem[activeTab].includes('date') ? 0 : 8}>
                        <Form.Item label="日期" name="date" className={'flex-sub '}
                                   hidden={hiddenFormItem[activeTab].includes('date')}>
                            <DatePicker
                                picker={'month'}
                                placeholder={'请选择查询日期'}
                                className={'width-100per'}
                                allowClear
                                onChange={onDateChange}
                            />
                        </Form.Item>
                    </Col>
                    <Col span={hiddenFormItem[activeTab].includes('ownCompany') ? 0 : 8}>
                        <Form.Item label="所属公司" name="ownCompany" className={'flex-sub '}
                                   hidden={hiddenFormItem[activeTab].includes('ownCompany')}>
                            <Select className={'width-100per'} placeholder={'请选择所属公司'} allowClear/>
                        </Form.Item>
                    </Col>
                    <Col span={hiddenFormItem[activeTab].includes('deptId') ? 0 : 8}>
                        <Form.Item label="部门名称" name="deptId" className={'flex-sub '}
                                   hidden={hiddenFormItem[activeTab].includes('deptId')}>
                            <Select className={'width-100per'} placeholder={'请选择部门'} allowClear/>
                        </Form.Item>
                    </Col>
                    <Col span={hiddenFormItem[activeTab].includes('projectId') ? 0 : 8}>
                        <Form.Item label="项目名称" name="projectId" className={'flex-sub '}
                                   hidden={hiddenFormItem[activeTab].includes('projectId')}>
                            <Select className={'width-100per'} placeholder={'请选择项目名称'} allowClear/>
                        </Form.Item>
                    </Col>
                    <Col span={hiddenFormItem[activeTab].includes('projectLeader') ? 0 : 8}>
                        <Form.Item label="项目负责人" name="projectLeader" className={'flex-sub '}
                                   hidden={hiddenFormItem[activeTab].includes('projectLeader')}>
                            <Select className={'width-100per'} placeholder={'请选择项目负责人'} allowClear/>
                        </Form.Item>
                    </Col>
                </Row>
                <Form.Item noStyle>
                    <Space direction={'horizontal'}>
                        <Button type={'primary'} htmlType={'submit'} icon={<SearchOutlined/>}
                                onClick={() => getTableData()}>查询</Button>
                        <Button htmlType={'reset'} icon={<ReloadOutlined/>} onClick={onReset}>重置</Button>
                    </Space>
                </Form.Item>
            </Form>
            {
                ['depart', 'project'].includes(activeTab) &&
                <UserWork
                    holidayList={holidayList}
                    dateValue={dateValue}
                    allowSelect={false}
                    ref={tableRef}
                />
            }
            {
                activeTab === 'summary' &&
                <Collapse
                    columns={[]}
                />
            }
        </div>
    </div>)
}
export default WorkHourReport;
