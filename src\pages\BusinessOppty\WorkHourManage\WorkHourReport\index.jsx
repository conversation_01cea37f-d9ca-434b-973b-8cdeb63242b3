/**
 * @description index.jsx - 工时报表
 * <AUTHOR>
 *
 * Created on 2025-07-11 下午 2:21
 */
import React, { useEffect, useRef, useState } from 'react';
import { Button, Col, Collapse, DatePicker, Divider, Empty, Form, Row, Select, Space, Table } from 'antd';
import { CloudDownloadOutlined, ReloadOutlined, SearchOutlined } from '@ant-design/icons';
import { TABS_TYPE, TABS_TYPE_ENUM } from './const';
import UserWork from '@/pages/BusinessOppty/WorkHourManage/CompleteWorkHour/components/UserWork';
import dayjs from 'dayjs';
import {
	exportSummaryReportData,
	exportTaskTimeReportData,
	getTaskTimeReportList,
	getTaskTimeSummaryList,
	queryWorkDate,
} from '@/api/Opportunity/WorkHourManage';
import { getDeptData } from '@/utils/dictionary';
import { getProjectComboboxData, pageProject } from '@/api/Opportunity/Project';
import { download } from '@/utils/common';

const hiddenFormItem = {
	// depart: ['projectLeader', 'projectId'],
	// project: ['ownCompany', 'departmentId'],
	report: [],
	summary: ['ownCompany', 'projectId', 'projectLeader', 'departmentId'],
};
const WorkHourReport = () => {
	const [activeTab, setActiveTab] = useState(TABS_TYPE_ENUM.report);
	const [dateValue, setDateValue] = useState(dayjs());
	const [holidayList, setHolidayList] = useState([]);
	/* 项目列表 */
	const [projectList, setProjectList] = useState([]);
	/* 人员参与列表 */
	const [userList, setUserList] = useState([]);
	// 表格数据
	const [dataSource, setDataSource] = useState([]);
	// 项目汇总表
	const [projectSummaryData, setProjectSummaryData] = useState([]);
	/* 项目负责人列表 */
	const [projectLeaderList, setProjectLeaderList] = useState([]);
	/* 部门列表 */
	const [departmentList, setDepartmentList] = useState([]);
	/* 项目负责人列表 */
	const [projectManagerList, setProjectManagerList] = useState([]);
	const [form] = Form.useForm();
	const tableRef = useRef(null);
	useEffect(() => {
		queryHoliday();
		getProjectList();
		getUserList();
		getDepartmentList();
		getSelectOptions();
	}, []);
	useEffect(() => {
		getTableData();
	}, [activeTab]);
	/* 查询假日信息 */
	const queryHoliday = async () => {
		const res = await queryWorkDate({ year: dateValue.format('YYYY') });
		if (res.data) {
			console.log(res.data);
			setHolidayList(res.data);
		}
	};
	// 获取部门列表
	const getDepartmentList = async () => {
		const res = await getDeptData();
		if (res) {
			console.log(res);
			setDepartmentList(res);
		}
	};
	/* 获取项目列表 */
	const getProjectList = async () => {
		console.log('获取项目列表');
		const res = await pageProject({ pageNum: 1, pageSize: 10000 });
		if (res.data) {
			const leaderList = [],
				projectList = [];
			res.data.records.map((item) => {
				if (item.managerUserId) {
					leaderList.push({
						label: item.managerUserName,
						value: item.managerUserId,
					});
				}
				if (item.id) {
					projectList.push({
						label: item.name,
						value: item.id,
					});
				}
			});
			setProjectList(projectList);
			setProjectLeaderList(leaderList);
		}
	};
	// 获取项目负责人选项
	const getSelectOptions = async () => {
		const res = await getProjectComboboxData();
		if (res) {
			console.log(res);
			const {projectManagerUserNames} = res.data;
			setProjectManagerList(projectManagerUserNames
				.filter(name => name)
				.map(name => ({
					label: name,
					value: name,
				})),
			);
		}
	};
	/* 获取人员列表 */
	const getUserList = async () => {
		console.log('获取人员列表');
		const list = [];
		setUserList(list);
	};
	const changeTab = (value) => {
		setActiveTab(value);
	};
	/* 导出 */
	const exportData = async () => {
		console.log('导出');
		const params = form.getFieldsValue();
		const values = {
			...params,
			currentMonth: params.currentMonth?.format('YYYY-MM'),
		};
		// exportSummaryReportData
		let res;
		switch (activeTab) {
			case TABS_TYPE_ENUM.report:
				values.queryType = 1;
				res = await exportTaskTimeReportData(values);
				break;
			case TABS_TYPE_ENUM.summary:
				values.queryType = 3;
				res = await exportSummaryReportData(values);
				break;
		}
		if (res) {
			download.excel(res, `${values.currentMonth}_${activeTab === TABS_TYPE_ENUM.report ? '工时报表' : '汇总表'}.xlsx`);
		}
	};
	/* 重置表单 */
	const onReset = () => {
		form.resetFields();
		getTableData();
	};
	/* 查询表格数据 */
	const getTableData = async () => {
		const params = form.getFieldsValue();
		console.log('查询表格数据', params);
		const values = {
			...params,
			managerUserName: params?.projectLeader,
			currentMonth: params.currentMonth?.format('YYYY-MM'),
		};
		switch (activeTab) {
			case TABS_TYPE_ENUM.report:
				values.queryType = 1;
				break;
			case TABS_TYPE_ENUM.summary:
				values.queryType = 3;
				break;
		}
		const res = await getTaskTimeReportList(values);
		if (res.data) {
			console.log('查询表格数据', res.data);
			if (activeTab === TABS_TYPE_ENUM.summary) {
				res.data.forEach((item) => {
					const taskTimes = item.taskTimes;
					// 按用户ID分组统计工时
					const userMap = new Map();
					taskTimes.forEach((taskItem) => {
						if (userMap.has(taskItem.userId)) {
							// 如果用户已存在，累加工时
							const existingUser = userMap.get(taskItem.userId);
							// 半天为单位计算
							existingUser.totalTaskTime += 0.5;
						} else {
							// 如果用户不存在，添加新用户
							userMap.set(taskItem.userId, {
								...taskItem,
								totalTaskTime: 0.5,
							});
						}
					});
					// 转换为数组
					item.uniqueUsers = Array.from(userMap.values());
				});
				setProjectSummaryData(res.data);
			} else {
				setDataSource(res.data);
			}
		}
	};
	/* 修改日期 */
	const onDateChange = (value) => {
		setDateValue(value);
	};

	const disabledDate = (current) => {
		// Can not select days after today
		return current && current > dayjs().endOf('day');
	};

	const columns = [
		{
			title: '部门',
			dataIndex: 'departmentName',
			key: 'departmentName',
			width: 120,
		},
		{
			title: '姓名',
			dataIndex: 'userName',
			key: 'userName',
			width: 120,
		},
		{
			title: '项目工时（天）',
			dataIndex: 'totalTaskTime',
			key: 'totalTaskTime',
			width: 120,
			render: (totalTaskTime, record) => {
				return `${totalTaskTime}天`;
			},
		},
	];
	return (
		<div className={'flex-sub flex flex-direction-column margin-20 padding-20'}>
			<div className="padding-20 bg-color-ffffff border-radius-8 flex justify-between align-center line-height-24 margin-bottom-20">
				<div className={'font-size-16 flex flex-sub gap-40'}>
					{TABS_TYPE.map((item) => {
						return (
							<div key={item.id} className={'flex align-center'} onClick={() => changeTab(item.id)}>
								<span
									className={`font-size-16 cursor-pointer font-weight-600 ${activeTab === item.id ? 'color-165dff' : 'color-1d2129'}`}
								>
									{item.name}
								</span>
							</div>
						);
					})}
				</div>
				<Space>
					<Button type={'primary'} icon={<CloudDownloadOutlined />} onClick={exportData}>
						导出
					</Button>
				</Space>
			</div>
			<div className={'bg-color-ffffff padding-20 border-radius-8'}>
				<Form
					form={form}
					className={'width-100per flex flex-sub align-start'}
					initialValues={{ currentMonth: dayjs() }}
					// labelCol={{ span: 6 }}
				>
					<Row className={'flex-sub'} gutter={[20]}>
						<Col span={hiddenFormItem[activeTab].includes('currentMonth') ? 0 : 8}>
							<Form.Item
								label="日期"
								name="currentMonth"
								className={'flex-sub '}
								hidden={hiddenFormItem[activeTab].includes('currentMonth')}
							>
								<DatePicker
									picker={'month'}
									placeholder={'请选择查询日期'}
									className={'width-100per'}
									disabledDate={disabledDate}
									onChange={onDateChange}
								/>
							</Form.Item>
						</Col>
						<Col span={hiddenFormItem[activeTab].includes('ownCompany') ? 0 : 8}>
							<Form.Item
								label="所属公司"
								name="ownCompany"
								className={'flex-sub '}
								hidden={hiddenFormItem[activeTab].includes('ownCompany')}
							>
								<Select className={''} placeholder={'请选择所属公司'} allowClear />
							</Form.Item>
						</Col>
						<Col span={hiddenFormItem[activeTab].includes('departmentId') ? 0 : 8}>
							<Form.Item
								label="部门名称"
								name="departmentId"
								className={'flex-sub '}
								hidden={hiddenFormItem[activeTab].includes('departmentId')}
							>
								<Select options={departmentList} className={''} placeholder={'请选择部门'} allowClear />
							</Form.Item>
						</Col>
						<Col span={hiddenFormItem[activeTab].includes('projectId') ? 0 : 8}>
							<Form.Item label="项目名称" name="projectId" className={' '} hidden={hiddenFormItem[activeTab].includes('projectId')}>
								<Select options={projectList} placeholder={'请选择项目名称'} allowClear showSearch optionFilterProp={'label'} />
							</Form.Item>
						</Col>
						<Col span={hiddenFormItem[activeTab].includes('projectLeader') ? 0 : 8}>
							<Form.Item
								label="项目负责人"
								name="projectLeader"
								className={'flex-sub '}
								hidden={hiddenFormItem[activeTab].includes('projectLeader')}
							>
								<Select className={''} options={projectManagerList} placeholder={'请选择项目负责人'} allowClear />
							</Form.Item>
						</Col>
					</Row>
					<Divider type={'vertical'} className={'height-40'} />
					<Form.Item noStyle>
						<Space direction={'horizontal'}>
							<Button type={'primary'} htmlType={'submit'} icon={<SearchOutlined />} onClick={() => getTableData()}>
								查询
							</Button>
							<Button htmlType={'reset'} icon={<ReloadOutlined />} onClick={onReset}>
								重置
							</Button>
						</Space>
					</Form.Item>
				</Form>
				{['depart', 'project', 'report'].includes(activeTab) && (
					<UserWork holidayList={holidayList} dateValue={dateValue} allowSelect={false} ref={tableRef} dataSource={dataSource} />
				)}
				{
					activeTab === 'summary' &&
					<div>
						{
							projectSummaryData.map((item) => {
								return (
									<Collapse
										size={'small'}
										className={'margin-top-20'}
										defaultActiveKey={item.projectId}
										items={[
											{
												key: item.projectId,
												label: `${item.projectName}`,
												extra: <span>合计{item.totalTaskTime / 2 || 0}天</span>,
												children: (
													<Table
														size={'small'}
														rowKey="userId"
														columns={columns}
														dataSource={item.uniqueUsers}
														pagination={false}
														bordered
														scroll={{ x: 'max-content' }}
													/>
												),
											},
										]}
									/>
								);
							})
						}
						{
							projectSummaryData.length === 0 && (
								<Empty description={'暂无数据'} />
							)
						}
					</div>
				}
			</div>
		</div>
	);
};
export default WorkHourReport;
