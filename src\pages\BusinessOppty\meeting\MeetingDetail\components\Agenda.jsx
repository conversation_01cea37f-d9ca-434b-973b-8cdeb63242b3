/**
 * @description Agenda.jsx - 会议议程
 * <AUTHOR>
 *
 * Created on 2025/4/15 17:09
 */
import React, {forwardRef, useEffect, useImperativeHandle, useState} from 'react';
import {Button, Form, Input, Modal, Table, TimePicker} from "antd";
import {PlusOutlined} from "@ant-design/icons";
import dayjs from "dayjs";

const { RangePicker } = TimePicker;
const Agenda = ({meetingAgenda, ...props}, ref) => {
    const [form] = Form.useForm();
    const [open, setOpen] = useState(false);
    // 议程数据
    const [dataSource, setDataSource] = useState([]);
    // 修改详情
    const [updateValues, setUpdateValues] = useState({});
    useEffect(() => {
        setDataSource(meetingAgenda);
    }, [meetingAgenda]);
    const onSubmit = () => {
        console.log('submit');
        return dataSource;
    };
    useImperativeHandle(ref, () => ({
        onSubmit: onSubmit
    }));
    // 提交表单
    const submitModal = async () => {
        try {
            const values = await form.validateFields();
            const { agendaTime, id, key } = values;

            // 统一时间处理
            const formatTime = time => dayjs(time).format("HH:mm:ss");
            const [startTime, endTime] = agendaTime?.map(formatTime) || [];

            // 使用函数式更新保证数据一致性
            setDataSource(prev => {
                // 创建新数组避免直接修改原数据
                const newData = [...prev];

                // 优化查找逻辑
                const existingIndex = newData.findIndex(
                    item => (id && item.id === id) || (key && item.key === key)
                );

                const newItem = {
                    ...values,
                    startTime,
                    endTime,
                    update: !!id,
                    key: existingIndex === -1 ? updateValues?.key : newData[existingIndex].key
                };

                if (existingIndex > -1) {
                    // 更新现有项
                    newData[existingIndex] = newItem;
                } else {
                    // 新增项（保持不可变性）
                    newData.push(newItem);
                }

                return newData;
            });

            onClose();
        } catch (error) {
            console.error("表单提交失败:", error);
            message.error("议程保存失败，请检查数据");
        }
    };
    // 关闭模态框
    const onClose = () => {
        setOpen(false);
        form.resetFields();
        setUpdateValues({});
    };
    // 点击新增按钮
    const onAddAgenda = () => {
        setOpen(true);
        form.resetFields();
        setUpdateValues({
            key: Math.random().toString(36).slice(-8),
            create: 1,
        });
    }
    // 点击编辑按钮
    const onEditAgenda = (record) => {
        setOpen(true);
        console.log(record)
        const currentDay = dayjs().format('YYYY-MM-DD');
        form.setFieldsValue({
            id: record?.id,
            theme: record?.theme,
            sharer: record?.sharer,
            agendaTime: [dayjs(`${currentDay} ${record.startTime}`), dayjs(`${currentDay} ${record.endTime}`)],
        });
        setUpdateValues(record);
    }
    // 点击删除按钮
    const onDelAgenda = (record) => {
        Modal.confirm({
            title: '确定删除该议程吗？',
            onOk: () => {
                console.log('删除成功')
                setDataSource(dataSource.filter(item => item.id !== record.id));
            }
        })
    }
    const columns = [
        {
            title: '序号',
            dataIndex: 'index',
            key: 'index',
            width: 100,
            render: (text, record, index) => index + 1
        },
        {
            title: '议程时间',
            dataIndex: 'startTime',
            key: 'startTime',
            width: '40%',
            render: (startTime, {endTime}) => {
                return `${startTime.slice(0, 5)} - ${endTime.slice(0, 5)}`
            }
        },
        {
            title: '议程安排',
            dataIndex: 'theme',
            key: 'theme',
            width: '40%',
            render: (theme, {sharer}) => {
                return <div>
                    <div className={'font-weight-600'}>{theme}</div>
                    <div className={'color-gray font-size-12'}>分享人：{sharer}</div>
                </div>
            }
        },
        {
            title: '操作',
            dataIndex: 'options',
            key: 'options',
            width: 200,
            render: (text, record, index) => {
                return [
                    <Button type={'link'} onClick={() => onEditAgenda(record)}>编辑</Button>,
                    <Button type={'link'} danger onClick={() => onDelAgenda(record)}>删除</Button>,
                ]
            }
        }
    ]
    return (<div className={'flex flex-direction-column gap-20 width-100per'}>
        <div className={'flex justify-between width-100per'}>
            <div className={'font-weight-600 font-size-16'}>会议议程信息</div>
            <Button icon={<PlusOutlined/>} type={'primary'} onClick={onAddAgenda}>新增议程</Button>
        </div>
        <Table columns={columns} dataSource={dataSource} pagination={false}/>
        <Modal
            open={open}
            title={updateValues?.id ? '编辑议程' : '新增议程'}
            onOk={submitModal}
            onCancel={onClose}
        >
            <Form form={form} onFinish={onSubmit} layout={'vertical'}>
                <Form.Item label={'id'} name={'id'} hidden>
                    <Input />
                </Form.Item>
                <Form.Item label={'议程时间'} name={'agendaTime'} rules={[{required: true, message: '请选择议程时间'}]}>
                    <RangePicker format={'HH:mm'} minuteStep={15} className={'width-100per'}/>
                </Form.Item>
                <Form.Item label={'议程主题'} name={'theme'} rules={[{required: true, message: '请输入议程主题'}]}>
                    <Input/>
                </Form.Item>
                <Form.Item label={'分享人'} name={'sharer'} >
                    <Input/>
                </Form.Item>
            </Form>
        </Modal>
    </div>)
}
export default forwardRef(Agenda);
