/**
 * @description InvitedMeeting - 文件描述
 * <AUTHOR>
 *
 * Created on 2025/4/10 17:27
 */
import React, {forwardRef, useEffect, useImperativeHandle, useRef, useState} from 'react';
import {Button, Col, Form, Input, Modal, Row, Select, Space, message, Cascader} from "antd";

const rowSpan = 8;
const Invited = ({userList, detail, meetingRoomList, userInfo, departmentList, ...props}, ref) => {
    const [form] = Form.useForm();
    const [userOptions, setUserOptions] = useState([]);
    const addCompanyRef = useRef(null);
    // 公共处理函数
    const mapUserData = (fieldName, values) => {
        if (!Array.isArray(values[fieldName][0])) {
            // 单选处理
            values.organizer = [values.organizer];
        }
        values[fieldName] = values[fieldName].map(item => {
            const [depId, userId] = item;
            const user = userOptions.find(user => user.value === userId);
            const dept = user?.deptList?.find(dep => dep.id === depId);
            if (item.length === 1) {
                return detail.admins?.find(admin => admin.userId === item[0]);
            }
            return {
                userId: user?.value,
                username: user?.userName,
                departmentId: dept?.id,
                departmentName: dept?.name,
                ...(fieldName === 'admins' ? {inviteId: detail.id} : { meetingId: values.id }) // 仅非admins字段添加meetingId
            }
        }).filter(item => item);
    };
    // 提交数据
    const onSubmit = async () => {
        const values = await form.validateFields();
        if (values.admins?.length > 0) {
            mapUserData('admins', values);
        }
        return values;
    };
    useImperativeHandle(ref, () => ({
        onSubmit: onSubmit
    }));
    // 初始化
    useEffect(() => {
        if (detail?.id) {
            const {fieldTags} = detail;
            const selectedAdmins = detail?.admins?.map(item => {
                const userId = item.userId;
                const depId = userList.find(item => item.value === userId)?.deptList[0]?.id;
                return [depId, userId];
            });
            const data = {
                ...detail,
                admins: selectedAdmins,
                meetingVisitorList: detail?.meetingVisitorList?.map(item => item.name),
                companyName: detail?.companyName.split('；'),
                meetingRoomId: fieldTags?.find(item => item.classifyCode === 'visit_meeting_room')?.tagId,
            };
            form.setFieldsValue(data);
        } else if (userInfo.id) {
            const isEmployee = userInfo.insiderStatus === 1; // await this.queryTechnicalManager(userId);
            const userDept = userInfo.deptList[0];
            const data = {
                title: userInfo?.userName + '邀请您填写来访登记',
                inviter: userInfo?.userName,
                contactPhone: userInfo?.mobile,
                address: isEmployee ? '广州市天河区天河北路 886 号 C 栋' : '',
                admins: [userDept.id, userInfo.id],
            };
            form.setFieldsValue(data);
        }
    }, [detail, userList, userInfo]);

    useEffect(() => {
        if (userList?.length > 0) {
            // 判断如果管理员不在列表中，则添加到管理员列表中
            const addUser = (detail?.admins?.filter(item => userList.findIndex(ov => ov.value === item.userId) === -1) || [])
                .map(item => ({label: item.username, value: item.userId}));
            setUserOptions([...userList, ...addUser]);
        }
    }, [userList, detail]);

    // 添加单位
    const updateCompany = (value) => {
        const companyName = form.getFieldValue('companyName') || [];
        // const inputValue = useRef('');
        Modal.confirm({
            title: '添加单位',
            content: <Input
                ref={addCompanyRef}
                placeholder={'请输入单位'}
            />,
            okText: '确定',
            cancelText: '取消',
            onOk: () => {
                const value = addCompanyRef.current.input.value;
                if (companyName.includes(value)) {
                    message.error('单位已存在');
                    return Promise.reject();
                }
                if (value.trim()) {
                    form.setFieldsValue({
                        companyName: [...new Set([...companyName, value])]
                    });
                }
            },
        })
    }

    return (<div className={'flex flex-direction-column gap-20 width-100per'}>
        <div className={'font-weight-600 font-size-16'}>邀请函基本信息</div>
        <Form form={form} layout={'vertical'} disabled={detail.status === 3}>
            <Form.Item label={'ID'} name={'id'} hidden>
                <Input placeholder={'请输入ID'}/>
            </Form.Item>
            <Form.Item label={'分享标题'} name={'shareText'} hidden>
                {/* params.inviter + '邀请您填写来访登记' */}
                <Input placeholder={'请输入分享标题'}/>
            </Form.Item>
            <Row gutter={20}>
                <Col span={rowSpan}>
                    <Form.Item label={'邀请函标题'} name={'title'} required rules={[{required: true, message: '请输入邀请函标题'}]}>
                        <Input placeholder={'请输入邀请函标题'}/>
                    </Form.Item>
                </Col>
                <Col span={rowSpan}>
                    <Form.Item label={'邀请人'} name={'inviter'} required rules={[{required: true, message: '请输入邀请人'}]}>
                        <Input placeholder={'请输入邀请函标题'}/>
                    </Form.Item>
                </Col>
                <Col span={rowSpan}>
                    <Form.Item label={'联系电话'} name={'contactPhone'} required
                               rules={[{required: true, message: '请输入联系电话'}]}>
                        <Input placeholder={'请输入联系电话'}/>
                    </Form.Item>
                </Col>
                <Col span={rowSpan}>
                    <Form.Item label={<Space>
                        <span>邀请单位/企业</span>
                        <Button size={'small'} type={'link'} onClick={updateCompany}>添加</Button>
                    </Space>} name={'companyName'} required tooltip={'回车添加单位/企业，用“；”隔开'}
                               rules={[{required: true, message: '请输入邀请单位/企业'},]}>
                        <Select placeholder={'请输入邀请单位/企业'} mode="tags" tokenSeparators={['；']}/>
                    </Form.Item>
                </Col>
                <Col span={rowSpan}>
                    <Form.Item label={'会议地址'} name={'address'}>
                        <Input placeholder={'请输入会议地址'}/>
                    </Form.Item>
                </Col>
                <Col span={rowSpan}>
                    <Form.Item label={'会议室'} name={'meetingRoomId'}>
                        <Select placeholder={'请选择会议室'} options={meetingRoomList}/>
                    </Form.Item>
                </Col>
                <Col span={rowSpan}>
                    <Form.Item label={'管理人员'} name={'admins'}>
                        {/*<Select*/}
                        {/*    optionFilterProp="label"*/}
                        {/*    placeholder={'请选择管理人员'}*/}
                        {/*    mode={'multiple'}*/}
                        {/*    options={userOptions}*/}
                        {/*/>*/}
                        <Cascader
                            placeholder={'请选择管理人员'}
                            options={departmentList}
                            multiple
                            showCheckedStrategy={Cascader.SHOW_CHILD}
                            // maxTagCount="responsive"
                            changeOnSelect
                            displayRender={(labels, selectedOptions) => `${labels.join('/')}`}
                        />
                    </Form.Item>
                </Col>
            </Row>
        </Form>
    </div>)
}
export default forwardRef(Invited);
