/**
 * @description Meeting.jsx - 文件描述
 * <AUTHOR>
 *
 * Created on 2025/4/11 16:24
 */
import React, { forwardRef, useEffect, useImperativeHandle, useRef, useState } from 'react';
import { Button, Col, DatePicker, Form, Input, message, Modal, Row, Select, Space, Radio, InputNumber, Cascader } from 'antd';
import {
	IS_IMPORTANT_MEETING,
	IS_NEED_ELECTRONIC_WATER_SIGN,
	MEETING_MATERIALS_PERMISSION,
	MEETING_MINUTE_PERMISSION,
} from '@/pages/BusinessOppty/meeting/MeetingDetail/const';
import dayjs from 'dayjs';
import { MeetingType } from '@/pages/BusinessOppty/meeting/MeetingList/const';
import MeetingMaterial from './MeetingMaterial';
import { useRouterLink } from '@/hook/useRouter';

const rowSpan = 8;
// 指定三方会议绑定
const THIRD_PARTY_MEETING = '三方会议';
const Meeting = (
	{
		detail,
		invitedDetail,
		meetingDurationList,
		meetingMaterialList,
		meetingRoomList,
		userList,
		meetingType,
		meetingTypeList,
		departmentList,
		...props
	},
	ref
) => {
	const [form] = Form.useForm();
	const { searchParams } = useRouterLink();
	const watchIsElectronicWater = Form.useWatch('isRequiredEcard', form);
	const watchMeetingTypeId = Form.useWatch('meetingTypeId', form);
	const watchCompanyName = Form.useWatch('companyName', form);
	const addCompanyRef = useRef(null);
	const [userOptions, setUserOptions] = useState([]);
	// 显示三方会议信息
	const [showThirdMeeting, setShowThirdMeeting] = useState(false);
	const [options, setOptions] = useState({
		materialOptions: [],
		electronicWater: [],
		isKeyMeeting: [],
		meetingMinutes: [],
	});

	useEffect(() => {
		// 三方会议ID
		const thirdPartyMeetingId = meetingTypeList.find((item) => item.label === THIRD_PARTY_MEETING)?.value;
		if (watchMeetingTypeId && watchMeetingTypeId === thirdPartyMeetingId) {
			setShowThirdMeeting(true);
		} else {
			setShowThirdMeeting(false);
		}
	}, [watchMeetingTypeId]);
	// 初始化表单选项
	useEffect(() => {
		const initCompanyName = searchParams.get('companyName');
		if (initCompanyName) {
			form.setFieldsValue({
				companyName: [initCompanyName],
			});
		}
		const materialOptions = [
			{
				label: '查看与下载',
				value: MEETING_MATERIALS_PERMISSION.EDIT,
			},
			{
				label: '仅查看',
				value: MEETING_MATERIALS_PERMISSION.VIEW,
			},
		];
		const electronicWater = [
			{
				label: '是',
				value: IS_NEED_ELECTRONIC_WATER_SIGN.YES,
			},
			{
				label: '否',
				value: IS_NEED_ELECTRONIC_WATER_SIGN.NO,
			},
		];
		const isKeyMeeting = [
			{
				label: '是',
				value: IS_IMPORTANT_MEETING.YES,
			},
			{
				label: '否',
				value: IS_IMPORTANT_MEETING.NO,
			},
		];
		const meetingMinutes = [
			{
				label: '全部人可见',
				value: MEETING_MINUTE_PERMISSION.ALL,
			},
			{
				label: '仅管理员可见',
				value: MEETING_MINUTE_PERMISSION.ADMIN,
			},
		];
		setOptions({
			materialOptions,
			electronicWater,
			isKeyMeeting,
			meetingMinutes,
		});
	}, []);
	// 表单初始化
	useEffect(() => {
		if (detail?.id) {
			const { fieldTagList } = detail;
			const values = {
				// ...detail,
				id: detail.id,
				theme: detail.theme,
				meetingMinutesPermission: detail.meetingMinutesPermission,
				ecardQuantity: detail.ecardQuantity,
				isRequiredEcard: detail.isRequiredEcard,
				meetingTypeId: detail.meetingTypeId,
				address: detail.address,
				contactPerson: detail.contactPerson,
				contactPhone: detail.contactPhone,
				filePermission: detail.filePermission,
				isImportant: detail.isImportant,
				companyName: detail?.companyName.split('；'),
				meetingRoomId: fieldTagList?.find((item) => item.classifyCode === 'visit_meeting_room')?.tagId,
				meetingDurationId: fieldTagList?.find((item) => item.classifyCode === 'visit_meeting_duration')?.tagId,
				material: fieldTagList?.filter((item) => item.classifyCode === 'visit_meeting_material'),
				meetingStartTime: dayjs(detail.meetingStartTime),
			};
			form.setFieldsValue(values);
		} else if (invitedDetail?.id) {
			const { fieldTags, registrations = [] } = invitedDetail;
			const values = {
				// ...invitedDetail,
				address: invitedDetail.address,
				companyName: invitedDetail?.companyName.split('；'),
				meetingRoomId: fieldTags?.find((item) => item.classifyCode === 'visit_meeting_room')?.tagId,
				meetingDurationId: meetingDurationList[1]?.value,
				material: fieldTags?.filter((item) => item.classifyCode === 'visit_meeting_material'),
				contactPerson: invitedDetail.inviter,
				theme: registrations && registrations[0] ? registrations[0]?.theme : '',
				meetingStartTime: registrations && registrations[0] ? dayjs(registrations[0]?.visitTime) : null,
			};
			form.setFieldsValue(values);
		}
	}, [detail, invitedDetail, meetingDurationList]);
	useEffect(() => {
		if (userList?.length > 0) {
			// 判断如果管理员不在列表中，则添加到管理员列表中
			const addUser = (invitedDetail?.admins?.filter((item) => userList.findIndex((ov) => ov.value === item.userId) === -1) || []).map(
				(item) => ({ label: item.username, value: item.userId })
			);
			setUserOptions([...userList, ...addUser]);
			const selectedAdmins = invitedDetail?.admins?.map((item) => {
				const userId = item.userId;
				const depId = userList.find((item) => item.value === userId)?.deptList[0]?.id;
				return [depId, userId];
			});
			const assistants = detail?.assistants?.map((item) => {
				return [item.departmentId, item.userId];
			});
			const organizer = detail?.organizer?.map((item) => {
				return [item.departmentId, item.userId];
			});
			form.setFieldsValue({
				// admins: invitedDetail?.admins?.map(item => item.userId),
				admins: selectedAdmins,
				assistants: assistants,
				organizer,
			});
		}
	}, [userList, invitedDetail, detail]);
	// 添加单位
	const updateCompany = (value) => {
		const companyName = form.getFieldValue('companyName') || [];
		Modal.confirm({
			title: '添加单位',
			content: <Input ref={addCompanyRef} placeholder={'请输入单位'} />,
			okText: '确定',
			cancelText: '取消',
			onOk: () => {
				const value = addCompanyRef.current.input.value;
				if (companyName.includes(value)) {
					message.error('单位已存在');
					return Promise.reject();
				}
				if (value.trim()) {
					form.setFieldsValue({
						companyName: [...new Set([...companyName, value])],
					});
				}
			},
		});
	};

	// 新增公共处理函数
	const mapUserData = (fieldName, values) => {
		if (!Array.isArray(values[fieldName][0])) {
			// 单选处理
			values.organizer = [values.organizer];
		}
		values[fieldName] = values[fieldName]
			.map((item) => {
				const [depId, userId] = item;
				const user = userOptions.find((user) => user.value === userId);
				const dept = user?.deptList?.find((dep) => dep.id === depId);
				if (item.length === 1) {
					return invitedDetail.admins?.find((admin) => admin.userId === item[0]);
				}
				return {
					userId: user?.value,
					username: user?.userName,
					departmentId: dept?.id,
					departmentName: dept?.name,
					...(fieldName === 'admins' ? { inviteId: invitedDetail.id } : { meetingId: values.id }), // 仅非admins字段添加meetingId
				};
			})
			.filter((item) => item);
	};

	const onSubmit = async () => {
		const values = await form.validateFields();

		// 使用公共函数处理重复逻辑
		if (values.admins?.length > 0) {
			mapUserData('admins', values);
		}
		if (values.assistants?.length > 0) {
			mapUserData('assistants', values);
		}
		if (values.organizer?.length > 0) {
			mapUserData('organizer', values);
		}

		return values;
	};

	useImperativeHandle(ref, () => ({
		onSubmit: onSubmit,
		getCompanyName: () => form.getFieldValue('companyName'),
		meetingForm: form,
	}));
	return (
		<div className={'flex flex-direction-column gap-20 width-100per'}>
			<div className={'font-weight-600 font-size-16'}>会议基本信息</div>
			<Form
				form={form}
				layout={'vertical'}
				initialValues={{
					filePermission: MEETING_MATERIALS_PERMISSION.EDIT,
					isRequiredEcard: IS_NEED_ELECTRONIC_WATER_SIGN.NO,
					isImportant: IS_IMPORTANT_MEETING.NO,
					meetingMinutesPermission: MEETING_MINUTE_PERMISSION.ALL,
				}}
			>
				<Form.Item label={'ID'} name={'id'} hidden>
					<Input disabled={true} />
				</Form.Item>
				<Row gutter={20}>
					<Col span={rowSpan}>
						<Form.Item label={'会议主题'} name={'theme'} required rules={[{ required: true, message: '请输入会议主题' }]}>
							<Input placeholder={'请输入会议主题'} />
						</Form.Item>
					</Col>
					<Col span={rowSpan}>
						<Form.Item
							label={'会议开始时间'}
							name={'meetingStartTime'}
							required
							rules={[{ required: true, message: '请选择会议开始时间' }]}
						>
							<DatePicker
								placeholder={'请选择会议开始时间'}
								showTime
								format={'YYYY-MM-DD HH:mm'}
								className={'width-100per'}
								minuteStep={15}
							/>
						</Form.Item>
					</Col>
					<Col span={rowSpan}>
						<Form.Item label={'会议时长'} name={'meetingDurationId'} required rules={[{ required: true, message: '请选择会议时长' }]}>
							<Select placeholder={'请选择会议时长'} options={meetingDurationList} />
						</Form.Item>
					</Col>
					{meetingType === MeetingType.EXTERNAL && (
						<>
							<Col span={rowSpan}>
								<Form.Item
									label={
										<Space>
											<span>拜访对象</span>
											<Button size={'small'} type={'link'} onClick={updateCompany}>
												添加
											</Button>
										</Space>
									}
									name={'companyName'}
									required
									tooltip={'回车添加单位/企业，用“；”隔开'}
									rules={[{ required: true, message: '请输入拜访对象' }]}
								>
									<Select placeholder={'请输入拜访对象'} mode="tags" tokenSeparators={['；']} />
								</Form.Item>
							</Col>
							<Col span={rowSpan}>
								<Form.Item label={'会议类型'} name={'meetingTypeId'} required rules={[{ required: true, message: '请选择会议类型' }]}>
									<Select placeholder={'请选择会议类型'} options={meetingTypeList} />
								</Form.Item>
							</Col>
							{showThirdMeeting && (
								<>
									<Col span={rowSpan}>
										<Form.Item
											label={'三方会议组织人'}
											name={'organizer'}
											required
											rules={[{ required: true, message: '请选择三方会议组织人' }]}
										>
											<Cascader placeholder={'请选择三方会议组织人'} options={departmentList} />
										</Form.Item>
									</Col>
									<Col span={rowSpan}>
										<Form.Item
											label={'三方会议协办人'}
											name={'assistants'}
											required
											rules={[{ required: true, message: '请选择三方会议协办人' }]}
										>
											<Cascader
												placeholder={'请选择三方会议协办人'}
												options={departmentList}
												multiple
												showCheckedStrategy={Cascader.SHOW_CHILD}
												// maxTagCount="responsive"
												changeOnSelect
												displayRender={(labels, selectedOptions) => `${labels.join('/')}`}
											/>
										</Form.Item>
									</Col>
								</>
							)}
						</>
					)}
					<Col span={rowSpan}>
						<Form.Item label={'会议地址'} name={'address'} required rules={[{ required: true, message: '请输入会议地址' }]}>
							<Input placeholder={'请输入会议地址'} />
						</Form.Item>
					</Col>
					{meetingType === MeetingType.INTERNAL && (
						<>
							<Col span={rowSpan}>
								<Form.Item label={'会议室'} name={'meetingRoomId'} required rules={[{ required: true, message: '请选择会议室' }]}>
									<Select placeholder={'请选择会议室'} options={meetingRoomList} />
								</Form.Item>
							</Col>
							<Col span={rowSpan}>
								<Form.Item label={'联系人'} name={'contactPerson'} required rules={[{ required: true, message: '请输入联系人' }]}>
									<Input placeholder={'请输入联系人'} />
								</Form.Item>
							</Col>
							<Col span={rowSpan}>
								<Form.Item label={'联系电话'} name={'contactPhone'} required rules={[{ required: true, message: '请输入联系电话' }]}>
									<Input placeholder={'请输入联系电话'} />
								</Form.Item>
							</Col>
						</>
					)}
					<Col span={rowSpan}>
						<Form.Item label={'会议材料'} name={'material'}>
							<MeetingMaterial meetingType={meetingType} meetingId={detail?.id} inviteId={invitedDetail?.id} />
						</Form.Item>
					</Col>
					<Col span={rowSpan}>
						<Form.Item label={'会议材料权限'} name={'filePermission'}>
							<Radio.Group placeholder={'请选择会议材料与下载权限'} options={options.materialOptions} />
						</Form.Item>
					</Col>
					<Col span={rowSpan}>
						<Form.Item label={'会议纪要权限'} name={'meetingMinutesPermission'}>
							<Radio.Group placeholder={'请选择会议材料与下载权限'} options={options.meetingMinutes} />
						</Form.Item>
					</Col>
					<Col span={rowSpan}>
						<Form.Item
							label={'是否需要电子水牌'}
							name={'isRequiredEcard'}
							required
							rules={[{ required: true, message: '请选择是否需要电子水牌' }]}
						>
							<Radio.Group placeholder={'请选择是否需要电子水牌'} options={options.electronicWater} />
						</Form.Item>
					</Col>
					{watchIsElectronicWater === MEETING_MATERIALS_PERMISSION.EDIT && (
						<Col span={rowSpan}>
							<Form.Item
								label={'电子水牌数量'}
								name={'ecardQuantity'}
								required
								rules={[{ required: true, message: '请输入电子水牌数量' }]}
							>
								<InputNumber min={1} step={1} placeholder={'请输入电子水牌数量'} className={'width-100per'} />
							</Form.Item>
						</Col>
					)}
					<Col span={rowSpan}>
						<Form.Item label={'标记为重点会议'} name={'isImportant'}>
							<Radio.Group placeholder={'请选择是否标记为重点会议'} options={options.isKeyMeeting} />
						</Form.Item>
					</Col>
					{meetingType === MeetingType.EXTERNAL && (
						<Col span={rowSpan}>
							<Form.Item label={'管理人员'} name={'admins'}>
								{/*<Select*/}
								{/*    optionFilterProp="label"*/}
								{/*    placeholder={'请选择管理人员'}*/}
								{/*    mode={'multiple'}*/}
								{/*    options={userOptions}*/}
								{/*/>*/}
								<Cascader
									placeholder={'请选择管理人员'}
									options={departmentList}
									multiple
									showCheckedStrategy={Cascader.SHOW_CHILD}
									// maxTagCount="responsive"
									changeOnSelect
									displayRender={(labels, selectedOptions) => `${labels.join('/')}`}
								/>
							</Form.Item>
						</Col>
					)}
				</Row>
			</Form>
		</div>
	);
};
export default forwardRef(Meeting);
