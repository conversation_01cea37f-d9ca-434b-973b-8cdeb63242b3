/**
 * @description MeetingMaterial.jsx - 文件描述
 * <AUTHOR>
 *
 * Created on 2025/4/15 9:18
 */
import React, {useEffect, useState} from 'react';
import {batchAddFieldTag, deleteFieldTag, listFieldTagCache, listFileType, listTag} from "@/api/Opportunity/Meeting";
import {Button, Checkbox, Input, Modal, Space, Upload} from "antd";
import {CloudUploadOutlined, DeleteOutlined, EditOutlined, UploadOutlined} from "@ant-design/icons";
import DraggerUpload from "@/components/DraggerUpload";
import './index.scss'
const { Dragger } = Upload;
// 会议材料组件
const MeetingMaterial = ({value, onChange, meetingId, inviteId, ...props}, ref) => {
    const [open, setOpen] = useState(false);
    const [materialType, setMaterialType] = useState([]);
    const [fileTypeId, setFileTypeId] = useState([]);
    // 材料列表
    const [materialList, setMaterialList] = useState([]);
    // 选中的文件
    const [fileList, setFileList] = useState([]);
    // 自定义缓存文件
    const [customCacheFile, setCustomCacheFile] = useState([]);
    // 选中缓存的ID
    const [cacheFileId, setCacheFileId] = useState([]);
    // 选中材料文件ID
    const [materialFileId, setMaterialFileId] = useState([]);
    // 上传附件
    const uploadFile = () => {
        // 上传附件
        openModal();
    };
    // 关闭弹窗
    const closeModal = () => {
        setOpen(false);
    };
    // 打开弹窗
    const openModal = () => {
        setOpen(true);
    };
    // 提交弹框
    const submitModal = () => {
        const listId = [...cacheFileId, ...materialFileId];
        const fileList = [...customCacheFile, ...materialList];
        const selectFile = fileList.filter(ov => listId.includes(ov.tagId || ov.id));
        onChange(selectFile);
        // 提交弹窗
        closeModal();
    }
    // 获取会议材料类型
    const getMeetingMaterialType = async () => {
        const res = await listFileType({});
        if (res.data) {
            setMaterialType(res.data.map(ov => ({
                value: ov.id,
                label: ov.name
            })));
            getMeetingMaterial([]);
        }
    };
    // 获取会议材料
    const getMeetingMaterial = async (value = []) => {
        const res = await listTag({
            classifyCode: 'visit_meeting_material',
            fileTypeId: value || fileTypeId,
            pageIndex: 1,
            pageSize: 1000
        });
        if (res.data) {
            const list = res.data.map(ov => ({
                ...ov,
                key: ov.id,
                tagId: ov.id,
                value: ov.id,
                label: ov.name,
            }));
            const listIds = res.data.map(ov => ov.tagId || ov.id);
            setMaterialList(list);
            const selectIds = listIds.filter(ov => fileList.map(ov => ov.tagId || ov.id).includes(ov));
            setMaterialFileId(selectIds);
        }
    };
    // 获取会议缓存文件
    const getCustomCacheFile = async (id) => {
        const res = await listFieldTagCache({
            cacheFlag: 1,
            sourceIds: [id]
        });
        if (res.data) {
            console.log('res.data', res.data);
            const cacheFile = res.data.map(ov => ({
                ...ov,
                create: true,
                name: ov.tagName
            }));
            const cacheIds = cacheFile.map(ov => ov.tagId || ov.id);
            const cacheIn = fileList.filter(ov => !ov.tagId && !cacheIds.includes(ov.id));
            cacheFile.push(...cacheIn.map(ov => ({...ov, create: true })));
            setCustomCacheFile(cacheFile);
            setCacheFileId(cacheFile.map(ov => ov.id));
        }
    }
    // 删除指定文件
    const deleteFile = (id) => {
        setFileList(fileList.filter(ov => ov.id !== id));
    };

    useEffect(() => {
        if (value) {
            console.log('value 变更===', value)
            setFileList(value);
        }
    }, [value]);

    useEffect(() => {
        if (open) {
            getMeetingMaterialType();
            if (meetingId) {
                getCustomCacheFile(meetingId);
            }
        }
    }, [open, meetingId]);

    // 打开新地址文件
    const openFile = (url) => {
        window.open(url);
    };
    // 切换会议材料类型
    const changeMaterialType = (value) => {
        setFileTypeId(value);
        getMeetingMaterial(value);
    };

    // 上传缓存文件
    const uploadCacheFile = async (fileUrl, file) => {
        console.log('file', fileUrl, file);
        // 优化后的文件类型处理
        const lastDotIndex = file.name.lastIndexOf('.');
        // const fileType = lastDotIndex !== -1 ? file.name.substring(lastDotIndex + 1) : '';
        const fileName = file.name.substring(0, lastDotIndex);
        const params = {
            sourceId: inviteId,
            tagName: fileName,
            extendInfo: fileUrl,
            fieldName: 'field_name',
            classifyCode: 'visit_meeting_material',
            cacheFlag: 1,
            sourceCode: 'visit_meeting',
            extendFileSize: file.size,
        };
        console.log('上传文件', params)
        const res = await batchAddFieldTag([params]);
        if (res) {
            console.log('上传文件成功', res);
            const addCacheFile = {
                ...params,
                id: fileUrl,
                create: true
            };
            const fileList = [...customCacheFile, addCacheFile];
            setCustomCacheFile(fileList);
            const checkId = cacheFileId;
            checkId.push(addCacheFile.id);
            setCacheFileId(checkId);
        }
    };
    // 删除缓存文件
    const deleteCacheFile = async ({sourceId, id}) => {
        const success = () => {
            const fileList = customCacheFile.filter(ov => ov.id !== id)
            setCustomCacheFile(fileList);
            const checkId = cacheFileId.filter(ov => ov !== id);
            setCacheFileId(checkId);
        }
        if (!sourceId) {
            // 未上传文件
            success();
        }
        const res = await deleteFieldTag({
            sourceIds: [sourceId],
        });
        if (res) {
            success();
        }
    };
    // 编辑缓存文件
    const editCacheFile = async (file) => {
        const fileList = customCacheFile.map(ov => {
                if (ov.id === file.id) {
                    return {...file, update: !ov.update};
                } else {
                    return ov;
                }
            });
        setCustomCacheFile(fileList);
    };
    // 修改缓存文件名称
    const changeCacheFileName = (e, file) => {
        const {value} = e.target;
        const fileList = customCacheFile.map(ov => {
            if (ov.id === file.id) {
                return {...file, update: true, tagName: value, name: value};
            } else {
                return ov;
            }
        });
        setCustomCacheFile(fileList);
    };

    return <div className={''}>
        <Button type={'primary'} icon={<UploadOutlined/>} onClick={uploadFile}>上传</Button>
        {
            fileList.length > 0 && (
                <div className={'flex flex-direction-column gap-10 margin-top-20'}>
                    {
                        fileList.map(ov => (
                            <div className={'flex align-items-center gap-10'}>
                                <div
                                    onClick={() => openFile(ov.extendInfo)}
                                    className={'font-size-14 color-86909c cursor-pointer flex align-items-center gap-10'}
                                >
                                    {ov.name || ov.tagName}
                                </div>
                                <DeleteOutlined onClick={() => deleteFile(ov.id)}/>
                            </div>
                        ))
                    }
                </div>
            )
        }
        <Modal maskClosable={false} width={600} title={'上传附件'} onCancel={closeModal} onOk={submitModal} open={open}>
            <div className={'flex flex-direction-column gap-20'}>
                <div className={'font-size-12 color-86909c'}>将所需文件上传至会议材料列表中</div>
                <DraggerUpload accept={'.doc,.docx,.pdf,.ppt'} onChange={uploadCacheFile}>
                    <p className="ant-upload-drag-icon">
                        <CloudUploadOutlined/>
                    </p>
                    <p className="ant-upload-text">
                        <a>点击</a>或将文件<a>拖拽</a>到这里上传
                    </p>
                    <p className="ant-upload-hint">
                        支持.doc、.docx、.pdf、.ppt
                    </p>
                </DraggerUpload>
                <Checkbox.Group value={cacheFileId} onChange={(e) => setCacheFileId(e)}
                    className={'flex flex-direction-column flex-nowrap gap-6 max-height-200 overflowY-auto'}>
                    {
                        customCacheFile.map(ov => (
                            <div key={ov.id} className={'flex justify-between align-center bg-color-f7f8fa padding-8'}>
                                <Checkbox value={ov.id} className={'flex-sub checkbox-full-width'}>
                                    {
                                        ov.update ?
                                            <Input
                                                size={'small'}
                                                className={'width-100per'}
                                                value={ov.tagName}
                                                onChange={(e) => changeCacheFileName(e, ov)}
                                                onPressEnter={() => editCacheFile(ov)}
                                            />:
                                            <a href={ov.extendInfo} target={'_blank'}>{ov.tagName}</a>
                                    }
                                </Checkbox>
                                <Space size={20}>
                                    <EditOutlined onClick={() => editCacheFile(ov)}/>
                                    <DeleteOutlined onClick={() => deleteCacheFile(ov)}/>
                                </Space>
                            </div>
                        ))
                    }
                </Checkbox.Group>
                <div className={'font-size-14 font-weight-600'}>会议材料类型</div>
                <Checkbox.Group
                    options={materialType}
                    value={fileTypeId}
                    onChange={changeMaterialType}
                />
                <Checkbox.Group value={materialFileId} onChange={(e) => setMaterialFileId(e)}
                    className={'flex flex-direction-column flex-nowrap gap-6 max-height-200 overflowY-auto'}>
                    {
                        materialList.map(ov => (
                            <div key={ov.id} className={'flex justify-between align-center bg-color-f7f8fa padding-8'}>
                                <Checkbox value={ov.id}>
                                    <a href={ov.extendInfo} target={'_blank'}>{ov.name}</a>
                                </Checkbox>
                            </div>
                        ))
                    }
                </Checkbox.Group>
            </div>
        </Modal>
    </div>
}

export default MeetingMaterial;
