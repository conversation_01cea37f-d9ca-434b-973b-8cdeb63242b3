/**
 * @description Participants.jsx - 文件描述
 * <AUTHOR>
 *
 * Created on 2025/4/15 16:48
 */
import React, {forwardRef, useEffect, useImperativeHandle, useState} from 'react';
import {Button, Modal, Table, message, Form, Input, Select, InputNumber} from "antd";
import {PlusOutlined} from "@ant-design/icons";
import {PARTICIPANT_TYPE} from "../const";

const Participants = ({detail, userList, meetingRef, ...props}, ref) => {
    const [form] = Form.useForm();
    const meetingForm = meetingRef?.meetingForm;
    const watchCompanyList = Form.useWatch(['companyName'], meetingForm);
    const [watchRefCompanyName, setWatchRefCompanyName] = useState('');
    const watchType = Form.useWatch(['type'], form);
    const watchId = Form.useWatch(['id'], form);
    const watchKey = Form.useWatch(['key'], form);
    const watchCompanyName = Form.useWatch(['companyName'], form);
    // 用户列表
    const [userOptions, setUserOptions] = useState([]);
    // 客户参会人员名单
    const [customerParticipants, setCustomerParticipants] = useState([]);
    // 中心参会人员名单
    const [centerParticipants, setCenterParticipants] = useState([]);
    const [open, setOpen] = useState(false);
    // 表格配置
    const columns = [
        {
            title: '序号',
            dataIndex: 'index',
            key: 'index',
            width: 100,
            render: (text, record, index) => {
                return index + 1;
            }
        },
        {
            title: '姓名',
            dataIndex: 'name',
            key: 'name',
            width: '40%',
        },
        {
            title: '职位',
            dataIndex: 'position',
            key: 'position',
            width: '40%',
        },
        {
            title: '操作',
            width: 200,
            render: (text, record, index) => {
                return [
                    <Button type={'link'} onClick={() => onEditParticipants(record)}>编辑</Button>,
                    <Button type={'link'} danger onClick={() => onDelParticipants(record)}>删除</Button>,
                ]
            }
        }
    ];
    useEffect(() => {
        if (watchCompanyList) {
            // 将数组转换为 Set 提高查找效率
            const currentCompanies = new Set(watchCompanyList);
            // 保留现有数据中仍存在的公司，同时过滤掉已删除的公司
            const preservedData = customerParticipants.filter(item =>
                currentCompanies.has(item.companyName)
            );
            // 找出需要新增的公司（存在于 watchCompanyList 但不在当前数据中）
            const newCompanies = watchCompanyList.filter(name =>
                !preservedData.some(item => item.companyName === name)
            ).map(name => ({
                companyName: name,
                participants: []
            }));
            // 合并保留数据和新增数据
            const updatedParticipants = [...preservedData, ...newCompanies];
            setCustomerParticipants(updatedParticipants);
        }
    }, [watchCompanyList]);
    // 删除参会人员名单
    const onDelParticipants = (params) => {
        Modal.confirm({
            title: '确认删除吗？',
            content: '删除后无法恢复，确认删除吗？',
            okType: 'danger',
            onOk() {
                if (params?.type === PARTICIPANT_TYPE.INVITED) {
                    // 创建新数组避免直接修改原状态
                    const newCustomerParticipants = customerParticipants.map(item => {
                        if (item.companyName === params?.companyName) {
                            return {
                                ...item,
                                participants: item.participants.filter(ov => ov.id !== params.id || ov.name !== params.name)
                            };
                        }
                        return item;
                    });
                    setCustomerParticipants(newCustomerParticipants);
                } else {
                    const filtered = centerParticipants.filter(item => (item.id !== params.id || item.userId !== params.userId));
                    setCenterParticipants(filtered);
                }
            }
        })
    };
    // 修改人员信息
    const onEditParticipants = (params) => {
        console.log(params)
        setOpen(true);
        if (params?.id || params?.key) {
            form.setFieldsValue({
                userId: params?.userId,
                name: params?.name,
                position: params?.position,
                id: params?.id,
                key: params?.key,
                type: params?.type,
                companyName: params?.companyName,
            });
        } else {
            form.setFieldsValue({
                name: '',
                position: '',
                id: '',
                key: Math.random().toString(36).substring(2, 15),
                type: params?.type,
                companyName: params?.companyName,
            });
        }
    };
    // 关闭弹窗
    const cloneModal = () => {
        setOpen(false);
        form.resetFields();
    };
    // 提交修改
    const submitModal = async () => {
        const editData = await form.validateFields();
        const participants = editData?.type === PARTICIPANT_TYPE.INVITED ?
            customerParticipants.find(item => item.companyName === editData?.companyName)?.participants :
            centerParticipants;
        const newParticipants = [];
        // 判断是否已存在相同的id或名称（排除当前编辑项）
        const isDuplicate = participants.some(item => {
            // 当前编辑项自身不参与校验（通过 id/key 判断）
            if ((editData.id && item.userId === editData?.userId) || item.key === editData?.key) return false;

            if (editData.type === PARTICIPANT_TYPE.CENTER) {
                // 中心人员：ID相同即视为重复
                return item.userId === editData?.userId
            } else {
                // 其他类型：同一企业下名称相同视为重复
                return item.name === editData?.name && item.companyName === editData?.companyName
            }
        });

        if (isDuplicate) {
            const errorMessage = editData.type === PARTICIPANT_TYPE.CENTER ?
                '该人员已存在，请勿重复添加' :
                `[${editData.companyName}] 已存在同名参会人员`;
            message.error(errorMessage);
            throw new Error(errorMessage);
        }
        if (editData?.id || participants.find(item => ((item.userId && item.userId === editData?.userId) || (item.key && item.key === editData?.key)))) {
            participants.map(item => {
                if ((item.userId && item.userId === editData?.userId) || (item.key && item.key === editData?.key)) {
                    item = editData;
                }
                newParticipants.push(item);
            });
        } else {
            newParticipants.push(...participants, editData);
        }
        if (editData?.type === PARTICIPANT_TYPE.INVITED) {
            customerParticipants.forEach(item => {
                if (item.companyName === editData?.companyName) {
                    item.participants = newParticipants;
                }
            })
            setCustomerParticipants(customerParticipants);
        } else {
            setCenterParticipants(newParticipants);
        }
        cloneModal();
    }
    // 保存提交
    const onSubmit = async () => {
        const participants = [];
        const outerParticipants = [];
        customerParticipants.map(item => {
            if (item?.participants?.length) {
                outerParticipants.push(...item.participants);
            }
        });
        const emptyCompany = customerParticipants.filter(item => item?.participants?.length === 0);
        if (emptyCompany?.length > 0) {
            message.error(`请添加${emptyCompany[0].companyName}人员名单`);
            throw new Error('请添加客户参会人员名单');
        }
        if (centerParticipants?.length === 0) {
            message.error('请添加中心参会人员名单');
            throw new Error('请添加中心参会人员名单');
        }
        participants.push(...outerParticipants, ...centerParticipants);
        return participants;
    };
    // 选择修改参会人员名单
    const onChangeParticipants = (userId) => {
        console.log(userId)
        const {positionName, userName} = userList?.find(item => item.id === userId) || {};
        form.setFieldsValue({
            position: positionName,
            name: userName
        });
    }
    useImperativeHandle(ref, () => ({
        onSubmit: onSubmit
    }))
    useEffect(() => {
        if (detail?.id) {
            const companyName = detail?.companyName.split('；');
            const meetings = detail?.meetings?.[0] || {};
            const {participants = []} = meetings;
            const customerParticipants = [];
            const outerParticipants = participants.filter(item => item.type === PARTICIPANT_TYPE.INVITED);
            companyName.map(company => {
                customerParticipants.push({
                    companyName: company,
                    participants: outerParticipants.filter(item => item.companyName === company)
                });
            });
            setCustomerParticipants(customerParticipants);
            setCenterParticipants(participants.filter(item => item.type === PARTICIPANT_TYPE.CENTER));
        }
    }, [detail]);
    useEffect(() => {
        if (userList?.length) {
            const options = userList.map(item => ({
                label: `${item.userName} - ${item.positionName || ''}`,
                value: item.value
            }));
            setUserOptions(options);
        }
    }, [userList]);
    return (<div className={'flex flex-direction-column gap-20 width-100per'}>
        <div className={'font-weight-600 font-size-16'}>参会人员信息</div>
        {
            customerParticipants.map((item, index) => {
                return (<section className={'flex flex-direction-column gap-10'} key={index}>
                    <div className={'flex justify-between align-center'}>
                        <div className={'font-weight-600'}>{item.companyName}</div>
                        <Button
                            icon={<PlusOutlined/>}
                            type={'primary'}
                            onClick={() => onEditParticipants({
                                type: PARTICIPANT_TYPE.INVITED,
                                companyName: item.companyName
                            })}
                        >添加</Button>
                    </div>
                    <Table
                        columns={columns}
                        dataSource={item.participants}
                        pagination={false}
                    />
                </section>)
            })
        }
        <section className={'flex flex-direction-column gap-10'}>
            <div className={'flex justify-between align-center'}>
                <div className={'font-weight-600'}>中心参会人员名单</div>
                <Button
                    icon={<PlusOutlined/>}
                    type={'primary'}
                    onClick={() => onEditParticipants({type: PARTICIPANT_TYPE.CENTER})}
                >添加</Button>
            </div>
            <Table
                columns={columns}
                dataSource={centerParticipants}
                pagination={false}
            />
        </section>
        <Modal
            title={(watchId) ? `编辑${watchCompanyName || '中心'}参会人员` : '添加参会人员'}
            open={open}
            onOk={submitModal}
            onCancel={cloneModal}
        >
            <Form form={form} layout={'vertical'} defaultValue={{personType: 2}}>
                <Form.Item label={'id'} name={'id'} hidden>
                    <Input placeholder={'id'}/>
                </Form.Item>
                <Form.Item label={'key'} name={'key'} hidden>
                    <Input placeholder={'key'}/>
                </Form.Item>
                <Form.Item label={'type'} name={'type'} hidden>
                    <InputNumber placeholder={'type'}/>
                </Form.Item>
                <Form.Item label={'companyName'} name={'companyName'} hidden>
                    <Input placeholder={'companyName'}/>
                </Form.Item>
                <Form.Item label={'personType'} name={'personType'} hidden>
                    <Input placeholder={'personType'}/>
                </Form.Item>
                {
                    watchType === PARTICIPANT_TYPE.CENTER ?
                        <>
                            <Form.Item label={'name'} name={'name'} hidden>
                                <Input placeholder={'name'}/>
                            </Form.Item>
                            <Form.Item label={'姓名'} name={'userId'} rules={[{required: true, message: '请选择参会人员'}]}>
                                <Select
                                    placeholder={'请选择参会人员'}
                                    options={userOptions}
                                    showSearch
                                    optionFilterProp={'label'}
                                    onChange={onChangeParticipants}
                                />
                            </Form.Item>
                        </> :
                        <Form.Item label={'姓名'} name={'name'} rules={[{required: true}]}>
                            <Input placeholder={'请输入姓名'}/>
                        </Form.Item>
                }
                <Form.Item label={'职位'} name={'position'}>
                    <Input placeholder={'请输入职位'}/>
                </Form.Item>
            </Form>
        </Modal>
    </div>)
}
export default forwardRef(Participants);
