/**
 * @description Summary.jsx - 文件描述
 * <AUTHOR>
 *
 * Created on 2025/4/15 17:09
 */
import React, { forwardRef, useEffect, useImperativeHandle, useState } from 'react';
import { Button, Form, Input, InputNumber, message, Space, Tag } from 'antd';
import UploadImg from '@/components/UploadImg';
import UploadFile from '@/components/UploadFile';
import { PlusOutlined, SyncOutlined } from '@ant-design/icons';
import UEditor from '@/components/UEditor';
import './index.scss';

const { TextArea } = Input;
const Summary = ({ meetingSummary, onAiGenerate, ...props }, ref) => {
	/* 纪要详情 */
	const [details, setDetails] = useState({});
	// 生成loading
	const [loading, setLoading] = useState(false);
	const [form] = Form.useForm();
	const watchUrl = Form.useWatch('attachmentUrl', form);
	const watchName = Form.useWatch('attachmentName', form);
	// 提交纪要信息
	const onSubmit = async () => {
		try {
			const values = await form.validateFields();
			console.log('values', values);
			return values;
		} catch (e) {
			// message.warning('请填写会议纪要内容信息')
			// throw new Error(e);
		}
	};
	const updateAttachment = (fileUrl, file) => {
		console.log('fileUrl', fileUrl, file);
		form.setFieldsValue({
			attachmentName: file.name,
			attachmentSize: file.size,
			attachmentUrl: fileUrl,
		});
	};
	const removeFile = () => {
		form.setFieldsValue({
			attachmentName: '',
			attachmentSize: 0,
			attachmentUrl: '',
		});
	};
	useEffect(() => {
		setDetails(meetingSummary || {});
		const { pictureUrl, id, content, attachmentName, attachmentSize } = meetingSummary || {};
		const pictureList = pictureUrl ? JSON.parse(pictureUrl) : [];
		form.setFieldsValue({
			pictureUrl: pictureList.map((item) => item.path),
			id,
			content,
			attachmentName,
			attachmentSize,
		});
	}, [meetingSummary]);
	useImperativeHandle(ref, () => ({
		onSubmit: onSubmit,
		setFieldsValue: (values) => {
			form.setFieldsValue(values);
		},
		setLoading,
	}));
	return (
		<div className={'flex flex-direction-column gap-20 width-100per'}>
			<div className={'flex justify-between'}>
				<div className={'font-weight-600 font-size-16'}>会议纪要信息</div>
				{/*<Button type={'primary'} onClick={onSubmit}>提交</Button>*/}
			</div>
			<Form form={form} layout={'vertical'} className={'flex flex-direction-column gap-20 width-100per'} defaultValues={{ pictureUrl: [] }}>
				<Form.Item label={'id'} name={'id'} hidden>
					<Input />
				</Form.Item>
				<Form.Item label={'会议照片'} name={'pictureUrl'}>
					<UploadImg.MultipleUpload maxCount={9} size={5} width={160} height={160} tips="建议尺寸：200px*200px" />
				</Form.Item>
				<Form.Item label={'attachmentName'} name={'attachmentName'} hidden>
					<Input />
				</Form.Item>
				<Form.Item label={'attachmentSize'} name={'attachmentSize'} hidden>
					<InputNumber />
				</Form.Item>
				<Form.Item label={'会议纪要附件'} name={'attachmentUrl'}>
					{watchUrl ? (
						<div className={'flex flex-direction-row gap-10'}>
							<Tag color={'processing'} closable onClose={removeFile}>
								{watchName}
							</Tag>
						</div>
					) : (
						<UploadFile onChange={updateAttachment} maxCount={1} accept={'.doc,.docx,.pdf,.mp3,.m4a'}>
							<Button icon={<PlusOutlined />} type={'primary'}>
								上传附件
							</Button>
						</UploadFile>
					)}
				</Form.Item>
				<Form.Item
					label={
						<Space>
							<div className={'font-weight-600 font-size-16'}>会议纪要</div>
							<Button type={'primary'} size={'small'} icon={<SyncOutlined />} loading={loading} onClick={onAiGenerate}>
								AI生成
							</Button>
						</Space>
					}
					tooltip="AI根据附件内容自动生成会议纪要"
					name={'content'}
					required
					rules={[{ required: true, message: '请输入会议纪要' }]}
				>
					{/*<UEditor*/}
					{/*    placeholder={'请输入会议纪要'}*/}
					{/*    height={300}*/}
					{/*/>*/}
					<TextArea className={'textArea-over'} placeholder={'请输入会议纪要'} rows={4} />
				</Form.Item>
			</Form>
		</div>
	);
};
export default forwardRef(Summary);
