/**
 * @description index.jsx - 文件描述
 * <AUTHOR>
 *
 * Created on 2025/4/7 15:08
 */
import React, {useEffect, useRef, useState} from 'react';
import {Affix, Anchor, <PERSON><PERSON>crumb, Button, Space, message, Modal} from "antd";
import {Link} from "react-router-dom";
import Invited from "./components/Invited";
import {useRouterLink} from "@/hook/useRouter";
import {
    addInvite,
    detailInvite,
    detailMeeting,
    addParticipant,
    listTag,
    updateInvite,
    updateRegistration,
    listAgenda,
    addAgenda,
    updateAgenda,
    deleteAgenda,
    detailMeetingMinutes,
    updateMeetingMinutes,
    addMeetingMinutes,
    meetingMinutesRecognition,
    extractMeetingMinutes,
    getMeetingMinutesContent,
    updateMeeting,
    addMeeting
} from "@/api/Opportunity/Meeting";
import {getByPermissionPerms} from "@/api/common";
import {useSelector} from "react-redux";
import Meeting from "./components/Meeting";
import {MeetingType} from "@/pages/BusinessOppty/meeting/MeetingList/const";
import dayjs from "dayjs";
import Participants from "./components/Participants";
import Agenda from "./components/Agenda";
import Summary from "@/pages/BusinessOppty/meeting/MeetingDetail/components/Summary";
import {listProjectClue} from "@/api/Opportunity/Clue";
import {listMeetingType} from "@/api/Opportunity/MettingType";
import {MEETING_MINUTE_PERMISSION} from "@/pages/BusinessOppty/meeting/MeetingDetail/const";

const MeetingDetail = () => {
    const invitedRef = useRef(null);
    const meetingRef = useRef(null);
    const participantsRef = useRef(null);
    const agendaRef = useRef(null);
    const summaryRef = useRef(null);
    const idRef = useRef(null);
    const {linkTo, searchParams} = useRouterLink();
    const [invitedDetail, setInvitedDetail] = useState({});
    const [meetingDetail, setMeetingDetail] = useState({});
    // 会议议程
    const [meetingAgenda, setMeetingAgenda] = useState([]);
    // 会议纪要信息
    const [meetingSummary, setMeetingSummary] = useState({});
    const [userList, setUserList] = useState([]);
    const [departmentList, setDepartmentList] = useState([]);
    const [meetingType, setMeetingType] = useState('1');
    // 会议室列表
    const [meetingRoomList, setMeetingRoomList] = useState([]);
    // 会议时长列表
    const [meetingDurationList, setMeetingDurationList] = useState([]);
    // 会议材料列表
    const [meetingMaterialList, setMeetingMaterialList] = useState([]);
    // 会议类型列表
    const [meetingTypeList, setMeetingTypeList] = useState([]);
    // 是否有会议纪要管理权限
    const [meetingMinutesPermission, setMeetingMinutesPermission] = useState(false);
    const [offsetTop, setOffsetTop] = useState(0); // 新增状态
    // 用户信息
    const userInfo = useSelector((state) => {
        return state?.user.userInfo;
    });
    useEffect(() => {
        const inviteId = searchParams.get('id') || '';
        const meetingId = searchParams.get('meetingId') || '';
        const type = searchParams.get('type') || '';
        setMeetingType(type);
        getInvitedDetail(inviteId);
        getDepartmentList();
        getMeetingRoomList();
        getMeetingDurationList();
        getMeetingMaterialList();
        getMeetingTypeList();
    }, []);
    useEffect(() => {
        if (meetingDetail?.id && invitedDetail?.id) {
            const adminsIds = invitedDetail.admins.map(ov => ov.userId);
            if (meetingDetail.meetingMinutesPermission === MEETING_MINUTE_PERMISSION.ALL ||
                (meetingDetail.meetingMinutesPermission === MEETING_MINUTE_PERMISSION.ADMIN && adminsIds.includes(userInfo.id))) {
                setMeetingMinutesPermission(true);
            }
        }
    }, [meetingDetail, invitedDetail]);
    // 查询邀请函详情
    const getInvitedDetail = async (id) => {
        if (!id) {
            return;
        }
        const res = await detailInvite({id: id});
        if (res.data) {
            console.log(res.data)
            setInvitedDetail(res.data);
            const {meetings} = res.data;
            const {id, minutes} = meetings ? meetings[0] : {};
            getMeetingDetail(id);
            getMeetingAgenda(id);
            getMeetingSummary(minutes?.id);
        }
    }
    // 查询会议详情
    const getMeetingDetail = async (id) => {
        if (!id) {
            return;
        }
        const res = await detailMeeting({id: id});
        if (res.data) {
            console.log(res.data)
            setMeetingDetail(res.data);
        }
    };
    // 查询会议议程
    const getMeetingAgenda = async (id) => {
        if (!id) {
            return;
        }
        const res = await listAgenda({meetingId: id});
        if (res.data) {
            console.log(res.data)
            setMeetingAgenda(res.data);
        }
    };
    // 查询会议纪要
    const getMeetingSummary = async (id) => {
        if (!id) {
            return;
        }
        const res = await detailMeetingMinutes({id});
        if (res.data) {
            console.log(res.data)
            setMeetingSummary(res.data);
        }
    };

    // 保存提交
    const onSubmit = async () => {
        // 内部会议需要先提交邀请函信息，外部会议不需要
        if (meetingType === MeetingType.INTERNAL) {
            const invitedData = await invitedRef.current?.onSubmit();
            if (invitedData) {
                onInvitedSubmit(invitedData);
            }
            // 如果还没邀请ID，则不提交会议信息
            if (!invitedDetail?.id) {
                return linkTo(`/businessOppty/meeting/meetingList?type=${meetingType}`);
            }
        }
        // 提交会议信息
        const meetingData = await meetingRef.current?.onSubmit();
        let meetingId = '' || meetingDetail?.id;
        if (meetingData) {
            meetingId = await onMeetingSubmit(meetingData);
        }
        // 下面内容必须要有会议ID
        if (!meetingId) {
            return linkTo(`/businessOppty/meeting/meetingList?type=${meetingType}`);
        }
        // 提交参会人员信息
        const participantsData = await participantsRef.current?.onSubmit();
        if (participantsData) {
            onParticipantsSubmit(participantsData);
        }
        //     提交议程信息
        const agendaData = await agendaRef.current?.onSubmit();
        if (agendaData) {
            onAgendaSubmit(agendaData);
        }
        // 要会议纪要权限才可提交 设置所有人可见或自己为管理员
        const adminsIds = invitedDetail.admins?.map(ov => ov.userId);
        if (meetingDetail.meetingMinutesPermission === MEETING_MINUTE_PERMISSION.ALL ||
            (meetingDetail.meetingMinutesPermission === MEETING_MINUTE_PERMISSION.ADMIN && adminsIds.includes(userInfo.id)) ||
        !searchParams.get('meetingId')) {
            console.log('提交会议纪要信息')
            //    提交会议纪要信息
            const summaryData = await summaryRef.current?.onSubmit();
            if (summaryData) {
                onSummarySubmit(summaryData);
            }
        }
        return linkTo(`/businessOppty/meeting/meetingList?type=${meetingType}`);
    }
    // 邀请函信息提交
    const onInvitedSubmit = async (values) => {
        console.log(values);
        const {meetingRoomId, ...args} = values;
        const fieldTags = [];
        if (meetingRoomId) {
            const meetingRoomName = meetingRoomList.find(ov => ov.value === meetingRoomId)?.label;
            fieldTags.push({
                tagId: meetingRoomId,
                fieldDescription: meetingRoomName,
                fieldName: "meeting",
                classifyCode: "visit_meeting_room"
            });
        }
        const params = {
            ...args,
            companyName: values.companyName.join('；'),
            fieldTags: fieldTags,
            shareText: values.title || '',
        }
        let res;
        if (values.id) {
            res = await updateInvite(params);
        } else {
            res = await addInvite(params);
            idRef.current = {
                ...idRef.current,
                inviteId: values.id || res.data
            }
        }
        return res.data;
    };
    // 会议信息提交
    const onMeetingSubmit = async (values) => {
        const {material, meetingDurationId, meetingStartTime, meetingRoomId, ...args} = values;
        const fieldTags = [];
        if (material?.length > 0) {
            const fileList = material.map((ov, index) => {
                if (ov.create) {
                    /* 上传的文件 */
                    return {
                        tagName: ov.name || ov.tagName,
                        extendInfo: ov.extendInfo,
                        fieldName: 'field_name',
                        classifyCode: 'visit_meeting_material',
                        extendFileSize: ov.extendFileSize,
                        seqNumber: index + 1,
                        cacheFlag: 1,
                    }
                }
                return {
                    tagName: ov.name || ov.tagName,
                    tagId: ov.tagId || ov.id,
                    extendInfo: ov.extendInfo,
                    fieldName: 'field_name',
                    classifyCode: 'visit_meeting_material',
                    extendFileSize: ov.extendFileSize,
                    seqNumber: index + 1,
                    cacheFlag: ov.create ? 1 : 0,
                };
            });
            fieldTags.push(...fileList);
        }
        if (meetingDurationId) {
            fieldTags.push({
                tagId: meetingDurationId,
                fieldDescription: meetingDurationList.find(ov => ov.value === meetingDurationId)?.label || '',
                fieldName: "meeting",
                classifyCode: "visit_meeting_duration"
            });
        }
        if (meetingRoomId) {
            fieldTags.push({
                tagId: meetingRoomId,
                fieldDescription: meetingRoomList.find(ov => ov.value === meetingRoomId)?.label,
                fieldName: "meeting",
                classifyCode: "visit_meeting_room"
            });
        }
        const params = {
            ...args,
            inviteId: invitedDetail?.id || '',
            companyName: values.companyName?.join('；'),
            assistants: values.assistants,
            organizer: values.organizer,
            fieldTags: fieldTags,
            meetingStartTime: dayjs(meetingStartTime).format('YYYY-MM-DD HH:mm') + ':00',
        }
        console.log(params);
        // 如果是外部会议还需要更新邀请信息
        if (meetingType === MeetingType.EXTERNAL) {
            const {admins, inviteId, companyName, theme} = params;
            let inviteParams = {
                meetingId: meetingDetail?.id || '',
                inviteId: invitedDetail?.id,
                inviteStatus: '1',
                status: '3',
                // 类型1内部接待 2外部拜访
                type: MeetingType.EXTERNAL,
                title: theme,
                companyName: companyName,
                admins: admins,
            };
            let resInvite;
            if (inviteId) {
                inviteParams.id = inviteId;
                setTimeout(() => updateInvite(inviteParams), 0)
            } else {
                resInvite = await addInvite(inviteParams);
                params.inviteId = resInvite.data;
                idRef.current = {
                    ...idRef.current,
                    inviteId: params.inviteId
                }
            }
        }
        let res;
        if (values.id) {
            res = await updateMeeting(params);
        } else {
            res = await addMeeting(params);
            idRef.current = {
                ...idRef.current,
                meetingId: res.data
            }
        }
        // const inviteId = searchParams.get('id') || '';
        // if (!inviteId) {
        //     return linkTo(`/businessOppty/meeting/meetingList?type=${meetingType}`);
        // }
        return res.data;
    };
    // 参会人员信息提交
    const onParticipantsSubmit = async (participants) => {
        let {id, inviteId} = meetingDetail;
        const ids = idRef.current;
        if (!id || !inviteId) {
            id = ids.meetingId;
            inviteId = ids.inviteId;
        }
        participants.forEach(ov => {
            ov.sourceId = id || '';
            ov.visitId = inviteId || '';
        });
        if (meetingType === MeetingType.INTERNAL) {
            const registrations = invitedDetail.registrations[0];
            let params = {
                ...registrations,
                personType: 2,
                inviteId,
                participants: participants
            };
            params.id = registrations.id;
            return await updateRegistration(params);
        } else {
            /* 校验是否存在删除数据 */
            if (participants?.length > 0) {
                const deleteParticipants =
                    meetingDetail?.participants?.filter(ov => participants.findIndex(oov => oov.id === ov.id) === -1)
                        .map(ov => {
                            return {
                                ...ov,
                                personType: 2,
                                visitId: inviteId,
                                sourceId: id,
                                delFlag: 1,
                            };
                        });
                if (deleteParticipants?.length > 0) {
                    participants = participants.concat(deleteParticipants);
                }
            }
            return await addParticipant(participants);
        }
    };
    // 议程信息提交
    const onAgendaSubmit = async (agenda = []) => {
        try {
            let {id: meetingId, inviteId: visitId} = meetingDetail || {};
            const ids = idRef.current;
            if (!meetingId || !visitId) {
                meetingId = ids.meetingId;
                visitId = ids.inviteId;
            }
            const currentAgendaIds = new Set(agenda.map(a => a.id));
            // 使用解构和语义化变量名
            const [addList, updateList] = agenda.reduce(
                ([add, update], item) => {
                    if (item.id) {
                        item.update && update.push(item)
                    } else {
                        add.push(item)
                    }
                    return [add, update];
                },
                [[], []]
            );
            const removeList = meetingAgenda.filter(a => !currentAgendaIds.has(a.id));
            // 创建通用请求处理器
            const handleRequests = async (items, requestFn, getParams) => {
                if (!items.length) return [];
                return Promise.allSettled(
                    items.map(item => requestFn(getParams(item)))
                ).then(results => results);
            };
            // 并行处理所有请求
            await Promise.all([
                handleRequests(addList, addAgenda, item => ({
                    meetingId,
                    visitId,
                    sharer: item.sharer,
                    startTime: item.startTime,
                    endTime: item.endTime,
                    theme: item.theme
                })),
                handleRequests(removeList, deleteAgenda, item => ({id: item.id})),
                handleRequests(updateList, updateAgenda, item => ({
                    meetingId,
                    visitId,
                    id: item.id,
                    sharer: item.sharer,
                    startTime: item.startTime,
                    endTime: item.endTime,
                    theme: item.theme
                }))
            ]);
            // 返回操作统计
            return {
                added: addList.length,
                updated: updateList.length,
                removed: removeList.length
            };
        } catch (error) {
            console.error('议程提交失败:', error);
            throw new Error('议程更新操作失败，请检查数据后重试');
        }
    };
    // 会议纪要提交
    const onSummarySubmit = async (values) => {
        let {id, pictureUrl} = values;
        let meetingId = meetingDetail.id;
        if (!values.content) {
            // 如果没有内容，则不进行提交
            return;
        }
        let res;
        if (id) {
            res = await updateMeetingMinutes({
                ...values,
                id: id,
                meetingId: meetingId,
                pictureUrl: JSON.stringify(pictureUrl.map(oov => ({path: oov})))
            });
        } else {
            const ids = idRef.current;
            if (!meetingId) {
                meetingId = ids.meetingId;
            }
            res = await addMeetingMinutes({
                ...values,
                meetingId: meetingId,
                pictureUrl: JSON.stringify(pictureUrl.map(oov => ({path: oov})))
            });
        }
        if (res) {
            return res;
        }
    };

    // 获取部门人员信息
    const getDepartmentList = async () => {
        const res = await getByPermissionPerms({
            perms: 'businessOppty'
        }, {showLoading: false});
        if (res) {
            const selectObj = {};
            setUserList(res.data.map(ov => ({
                ...ov,
                value: ov.id,
                label: [ov.userName].concat(ov.deptList?.map(oov => oov.name)).join('/'),
                // check: false,
                disabled: false
            })));
            (res.data || []).forEach(ov => {
                const {
                    id,
                    userName,
                    deptList = []
                } = ov;
                const staffItem = {
                    value: id,
                    label: userName,
                    // check: false,
                    disabled: false
                }
                deptList.forEach(oov => {
                    const {id, name} = oov;
                    if (!selectObj[id]) {
                        selectObj[id] = {
                            value: id,
                            label: name,
                            children: [],
                        }
                    }
                    selectObj[id].children.push({...staffItem})
                });
            });
            const selectList = Object.values(selectObj).filter(ov => ov.children.length > 0)
            console.log('部门人员信息', selectList);
            setDepartmentList(selectList);
        }
    };
    // 获取会议室列表
    const getMeetingRoomList = async () => {
        const res = await listTag({classifyCode: "visit_meeting_room"});
        if (res.data) {
            const list = res.data.map(ov => ({
                value: ov.id,
                label: ov.name
            }));
            setMeetingRoomList(list);
            console.log('会议列表', list)
        }
    }
    // 获取会议时长列表
    const getMeetingDurationList = async () => {
        const res = await listTag({classifyCode: "visit_meeting_duration"});
        if (res.data) {
            console.log('会议时长列表', res.data);
            setMeetingDurationList(res.data.map(ov => ({
                value: ov.id,
                label: ov.name
            })));
        }
    }
    // 获取会议材料列表
    const getMeetingMaterialList = async () => {
        const res = await listTag({classifyCode: "visit_meeting_material"});
        if (res.data) {
            console.log('会议材料列表', res.data);
            setMeetingMaterialList(res.data.map(ov => ({
                ...ov,
                key: ov.id,
                value: ov.id,
                label: ov.name,
            })));
        }
    };
    // 获取会议类型列表
    const getMeetingTypeList = async () => {
        const res = await listMeetingType({});
        if (res.data) {
            console.log('会议类型列表', res.data);
            setMeetingTypeList(res.data.map(ov => ({
                value: ov.id,
                label: ov.name
            })));
        }
    };
    // AI生成纪要信息（带轮询）
    const onAiGenerate = async () => {
        try {
            const summaryData = await summaryRef.current?.onSubmit();
            if (!summaryData?.attachmentUrl) {
                message.error('请上传会议纪要文件');
                return;
            }
            const fileExt = summaryData.attachmentUrl.split('.').pop().toLowerCase();
            summaryRef.current?.setLoading(true);
            // 音频文件处理
            if (['mp3', 'm4a'].includes(fileExt)) {
                const recognitionRes = await meetingMinutesRecognition({
                    url: summaryData.attachmentUrl
                });
                if (!recognitionRes.data?.id) {
                    summaryRef.current?.setLoading(false);
                    throw new Error('语音识别任务创建失败');
                }

                const taskId = recognitionRes.data.id;
                const MAX_RETRIES = 100; // 最大重试次数（约10分钟）
                const POLL_INTERVAL = 6000; // 6秒轮询一次

                // 轮询任务状态
                const pollTaskStatus = async (attempt = 0) => {
                    if (attempt >= MAX_RETRIES) {
                        summaryRef.current?.setLoading(false);
                        message.error('语音识别超时，请稍后重试');
                        throw new Error('语音识别超时，请稍后重试');
                    }

                    const statusRes = await getMeetingMinutesContent({taskId});
                    // 处理不同状态
                    switch (statusRes.data.status) {
                        case '1':
                            const data = JSON.parse(statusRes.data.object || '{}');
                            summaryRef.current?.setLoading(false);
                            return data?.ParagraphTitle + '\n' + data?.ParagraphSummary;
                        case '0':
                            await new Promise(resolve => setTimeout(resolve, POLL_INTERVAL));
                            return pollTaskStatus(attempt + 1);
                        case '-1':
                            summaryRef.current?.setLoading(false);
                            throw new Error('语音识别失败: ' + (statusRes.data.error || '未知错误'));
                        default:
                            summaryRef.current?.setLoading(false);
                            throw new Error('未知任务状态: ' + statusRes.data.status);
                    }
                };

                // 开始轮询并更新内容
                const summary = await pollTaskStatus();
                summaryRef.current?.setFieldsValue({
                    content: summary,
                });

            } else {
                // 文件处理逻辑（保持原样）
                const params = {
                    meetingId: meetingDetail?.id,
                    url: summaryData.attachmentUrl,
                };
                const res = await extractMeetingMinutes(params);
                if (res.data?.choices?.[0]?.message?.content) {
                    summaryRef.current?.setFieldsValue({
                        content: res.data.choices[0].message.content,
                    });
                    summaryRef.current?.setLoading(false);
                }
            }
        } catch (error) {
            summaryRef.current?.setLoading(false);
            console.error('AI生成失败:', error);
            message.error(error.message || '生成失败，请稍后重试');
        }
    };
    // 线索派发
    const handleDispatch = async () => {
        if (invitedDetail.status === 3) {
            const meetingId = meetingDetail?.id;
            const res = await listProjectClue({meetingId});
            if (res.data.length) {
                console.log(res.data)
                const jumpUrl = (id) => {
                    linkTo(`/businessOppty/clueManage/clueDetail?id=${id}`)
                    Modal.destroyAll();
                };
                Modal.confirm({
                    title: '该会议已存在线索，是否前往派发线索？',
                    content: <div>
                        {
                            res.data.map((ov, i) => {
                                return <div key={ov.id}>{i + 1}：<a onClick={() => jumpUrl(ov.id)}>{ov.clueName}</a>
                                </div>
                            })
                        }
                    </div>,
                    onOk() {
                        linkTo(`/businessOppty/clueManage/clueDetail?meetingId=${meetingId}`);
                    }
                })
            } else {
                // 直接派发线索
                linkTo(`/businessOppty/clueManage/clueDetail?meetingId=${meetingId}`);
            }
        }
    }
    // 滚动处理
    const onScroll = (e) => {
        // const container = e.target;
        // const scrollTop = container.scrollTop;
        // const headerHeight = 60; // 根据实际顶部高度调整
        // console.log(scrollTop, headerHeight)
        // 动态计算偏移量
        // setOffsetTop(0);
    };

    return (
        <div id={'meeting-detail'} onScroll={onScroll} style={{height: 'calc(100% - 40px)'}}
             className={'flex-sub overflowY-auto flex flex-direction-column padding-20 gap-20 border-radius-4'}>
            <Breadcrumb
                items={[
                    {title: <Link to={`/businessOppty/meeting/meetingList?type=${meetingType}`}>会议列表</Link>},
                    {title: '会议详情'},
                ]}
            />
            <div id={'base'} />
            <Affix
                target={() => document.getElementById('meeting-detail')}
                offsetTop={offsetTop}  // 增加顶部间距
                onChange={(affixed) => {
                    console.log('Affix状态:', affixed);
                }}
            >
                <div className="padding-20 bg-color-ffffff border-radius-8 flex justify-between align-center line-height-24">
                    <Anchor
                        affix={false}
                        rootClassName="anchor-header-tabBar-box"
                        direction="horizontal"
                        replace
                        targetOffset={120}  // 调整滚动偏移量
                        onClick={(e) => e.preventDefault()}  // 阻止默认跳转行为
                        items={[
                            {
                                key: 'base',
                                href: '#base',
                                title: <div className="margin-right-40 font-size-16 font-weight-600">会议基本信息</div>,
                            },
                            {
                                key: 'staff',
                                href: '#staff',
                                title: <div className="margin-right-40 font-size-16 font-weight-600">参会人员信息</div>,
                            },
                            {
                                key: 'agenda',
                                href: '#agenda',
                                title: <div className="margin-right-40 font-size-16 font-weight-600">会议议程信息</div>,
                            },
                            {
                                key: 'summary',
                                href: '#summary',
                                title: <div className="margin-right-40 font-size-16 font-weight-600">会议纪要信息</div>,
                            },
                        ].filter(item => {
                            if (item.key === 'summary') {
                                return meetingMinutesPermission;
                            } else {
                                return item.key === 'base' || meetingDetail?.id || meetingType === MeetingType.EXTERNAL;
                            }
                        })}
                    />
                    <Space>
                        <Button onClick={handleDispatch}>派发线索</Button>
                        <Button type={'primary'} onClick={onSubmit}>保存</Button>
                    </Space>
                </div>
            </Affix>
            {
                /* 邀约信息 */
                meetingType === MeetingType.INTERNAL &&
                <div className={'padding-20 bg-color-ffffff border-radius-8 flex justify-between align-center line-height-24'}>
                    <Invited
                        ref={invitedRef}
                        detail={invitedDetail}
                        userList={userList}
                        meetingRoomList={meetingRoomList}
                        userInfo={userInfo}
                        departmentList={departmentList}
                    />
                </div>
            }
            {
                /* 会议信息 */
                meetingType === MeetingType.INTERNAL && (!invitedDetail?.id || invitedDetail.status === 1) ? null:
                    <div id={'meeting'}
                         className={'padding-20 bg-color-ffffff border-radius-8 flex justify-between align-center line-height-24'}>
                        <Meeting
                            ref={meetingRef}
                            detail={meetingDetail}
                            invitedDetail={invitedDetail}
                            userList={userList}
                            meetingRoomList={meetingRoomList}
                            meetingDurationList={meetingDurationList}
                            meetingMaterialList={meetingMaterialList}
                            meetingTypeList={meetingTypeList}
                            userInfo={userInfo}
                            meetingType={meetingType}
                            departmentList={departmentList}
                        />
                    </div>
            }
            {
                (meetingDetail?.id || meetingType === MeetingType.EXTERNAL) && <>
                    {/* 参会人员信息 */}
                    <div id={'staff'} />
                    <div className={'padding-20 bg-color-ffffff border-radius-8 flex justify-between align-center line-height-24'}>
                        <Participants
                            detail={invitedDetail}
                            userList={userList}
                            ref={participantsRef}
                            meetingRef={meetingRef.current}
                        />
                    </div>
                    {/* 会议议程信息 */}
                    <div id={'agenda'} />
                    <div className={'padding-20 bg-color-ffffff border-radius-8 flex justify-between align-center line-height-24'}>
                        <Agenda
                            ref={agendaRef}
                            meetingAgenda={meetingAgenda}
                        />
                    </div>
                    {/* 会议纪要信息 */}
                    {
                        (!meetingDetail?.id || meetingMinutesPermission) &&
                            <>
                                <div id={'summary'} />
                                <div className={'padding-20 bg-color-ffffff border-radius-8 flex justify-between align-center line-height-24'}>
                                    <Summary
                                        ref={summaryRef}
                                        meetingSummary={meetingSummary}
                                        onAiGenerate={onAiGenerate}
                                    />
                                </div>
                            </>
                    }
                </>
            }
        </div>);
}
export default MeetingDetail;
