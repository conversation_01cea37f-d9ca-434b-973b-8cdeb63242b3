/**
 * @description InviteMeeting.jsx - 文件描述
 * <AUTHOR>
 *
 * Created on 2025/4/7 15:54
 */
import React, {forwardRef, useEffect, useMemo, useState} from 'react';
import {Col, Form, Input, Row, DatePicker, Select, Space, Button, Divider, Table, Badge, message, Modal} from "antd";
import {ReloadOutlined, SearchOutlined} from "@ant-design/icons";
import {allVisitCall, deleteInvite, pageInvite, updateInviteStatus} from "@/api/Opportunity/Meeting";
import {ImportantOptions, InviteMeetingStatus} from "../const";
import {useSelector} from "react-redux";
import {useRouterLink} from "@/hook/useRouter";
import dayjs from "dayjs";
import {listProjectClue} from "@/api/Opportunity/Clue";
import {Link} from "react-router-dom";

const {RangePicker} = DatePicker;

const InviteMeeting = ({userInfo, meetingType}, ref) => {
    const [form] = Form.useForm();
    const [dataSource, setDataSource] = useState([]);
    // 邀约列表
    const [visitList, setVisitList] = useState([]);
    const [loading, setLoading] = useState(false);
    const [pagination, setPagination] = useState({
        current: 1,
        pageSize: 10,
        total: 0
    });
    const {linkTo} = useRouterLink();

    // 初始化
    useEffect(() => {
        setLoading(true);
        Promise.all([
            getVisitList(),
            getTableData()
        ]).then((res) => {
            setLoading(false);
        }).catch((err) => {
            setLoading(false);
        });
    }, [])
    const onReset = () => {
        form.resetFields();
        getTableData();
    };
    const getTableData = async (args = {}) => {
        const {status, meetingTime, ...values} = form.getFieldsValue();
        const params = {
            ...values,
            pageNum: args.pageNum || 1,
            pageSize: args.pageSize || 10,
            type: 1,
            status: status ? [status] : undefined,
        };
        if (meetingTime && meetingTime.length === 2) {
            params.meetingStartTime = dayjs(meetingTime?.[0]).format('YYYY-MM-DD HH:mm:ss');
            params.meetingEndTime = dayjs(meetingTime?.[1]).endOf('day').format('YYYY-MM-DD HH:mm:ss');
        }
        try {
            const res = await pageInvite(params);
            if (res.data) {
                res.data.records.forEach(ov => {
                    ov.visitTime = (ov.createTime || '').slice(0, 16)
                    if (ov.registrations && ov.registrations.length) {
                        const registrations = ov.registrations[0] || {}
                        ov.visitTime = (registrations.visitTime || '').slice(0, 16)
                    }
                    /* 查询是否在管理员列表中 */
                    ov.admins.forEach(admin => {
                        if (admin.userId === userInfo.userId) {
                            ov.isAdmin = true;
                        }
                    });
                    if (ov.meetings && ov.meetings.length) {
                        const meetings = ov.meetings[0] || {}
                        ov.visitTime = (meetings.meetingStartTime || '').slice(0, 16) + '-' + (meetings.meetingEndTime || '').slice(11, 16);
                        const visitors = (meetings.visitors || []).map(ov => {
                            return {
                                ...ov,
                                wxAvatarUrl: ov.wxAvatarUrl || '/assets/images/invitingVisits/icon-default-avatar.png',
                            }
                        });
                        /* 访客记录处理 */
                        ov.meetingVisitors = visitors;
                        ov.showVisitUserList = visitors.slice(0, 3);
                        ov.visitUserListTotal = (meetings.visitors || []).length;
                        /* 重点会议 */
                        ov.isImportant = meetings.isImportant;
                        /* 是否已读 */
                        ov.isRead = meetings.isRead;
                    }
                });
                setDataSource(res.data.records);
                setPagination({
                    current: params.pageNum,
                    pageSize: params.pageSize,
                    total: res.data.total,
                });
            }
        } catch (e) {
            console.log(e);
        }
    }
    // 全部拜访列表
    const getVisitList = async () => {
        try {
            const res = await allVisitCall({status: 1});
            if (res.data) {
                console.log(res.data)
                setVisitList(res.data);
            }
        } catch (e) {
            console.log(e);
        }

        setLoading(false);
    }
    // 前往确认
    const handleConfirm = (record) => {
        console.log(record);
        // message.warning('前往确认开发中...');
        handleMeetingDetail(record);
    }
    // 会议详情查看
    const handleMeetingDetail = (record) => {
        let href = `/businessOppty/meeting/meetingDetail?id=${record.id}&type=${meetingType}`;
        if (record.status === 3 && record?.meetings && record.meetings.length > 0) {
            href += `&meetingId=${record.meetings[0].id}`;
        }
        linkTo(href);
    }
    // 线索派发
    const handleDispatch = async (record) => {
        if (record.status === 3) {
            const meetingId = record.meetings[0].id;
            const res = await listProjectClue({meetingId});
            if (res.data.length) {
                console.log(res.data)
                const jumpUrl = (id) => {
                    linkTo(`/businessOppty/clueManage/clueDetail?id=${id}`)
                    Modal.destroyAll();
                };
                Modal.confirm({
                    title: '该会议已存在线索，是否前往派发线索？',
                    content: <div>
                        {
                            res.data.map((ov, i) => {
                                return <div key={ov.id}>{i + 1}：<a onClick={() => jumpUrl(ov.id)}>{ov.clueName}</a>
                                </div>
                            })
                        }
                    </div>,
                    onOk() {
                        linkTo(`/businessOppty/clueManage/clueDetail?meetingId=${meetingId}`);
                    }
                })
            } else {
                // 直接派发线索
                linkTo(`/businessOppty/clueManage/clueDetail?meetingId=${meetingId}`);
            }
        }
    }
    // 取消会议
    const handleCancel = (record) => {
        Modal.confirm({
            title: record.status === 1 ? '是否撤销邀请？' : '是否取消会议？',
            content: record.status === 1 ? '撤销邀请后，将无法恢复。' : '取消会议后，将无法恢复。',
            onOk() {
                updateInviteStatus({
                    id: record.id,
                    status: 0,
                }).then(() => {
                    message.success(record.status === 1 ? '撤销邀请成功！' : '取消会议成功！');
                    getTableData();
                });
            }
        })
    }
    // 删除会议
    const handleDelete = (record) => {
        Modal.confirm({
            title: '是否删除会议？',
            content: '删除后，将无法恢复。',
            onOk() {
                deleteInvite({id: record.id}).then(() => {
                    message.success('删除成功！');
                    getTableData();
                });
            }
        })
    }

    const columns = [
        {
            title: '序号',
            dataIndex: 'index',
            key: 'index',
            width: 80,
            align: 'center',
            render: (_, record, index) => {
                return index + 1 + (pagination.current - 1) * pagination.pageSize;
            }
        },
        {
            title: '会议主题',
            dataIndex: 'title',
            key: 'title',
            render: (title, {meetings, registrations}) => {
                return (meetings && meetings[0].theme) || (registrations && registrations[0].theme) || title || '--';
            }
        },
        {
            title: '客户名称',
            dataIndex: 'companyName',
            key: 'companyName',
            render: (companyName, record) => {
                return companyName || '--';
            }
        },
        {
            title: '会议时间',
            dataIndex: 'meetingStartTime',
            key: 'meetingStartTime',
            align: 'center',
            width: 200,
            render: (_, {meetings}) => {
                return (meetings && meetings[0].meetingStartTime) || '--';
            }
        },
        {
            title: '会议状态',
            dataIndex: 'status',
            key: 'status',
            width: 150,
            align: 'center',
            render: (status) => {
                const statusColor = ['default', 'warning', 'processing', 'success'];
                const statusText = ['已撤销', '待对方登记', '待确认', '已安排会议'];
                return <Badge status={statusColor[status]} text={statusText[status]}/>;
            }
        },
        {
            title: '操作',
            dataIndex: 'options',
            key: 'options',
            width: 150,
            align: 'left',
            render: (inviteName, record) => {
                const {status,} = record;
                return <Space>
                    {
                        (status === 1 || status === 3) &&
                        <Button type="link" size={'small'} onClick={() => handleMeetingDetail(record)}>查看/编辑</Button>
                    }
                    {
                        status === 2 &&
                        <Button type="link" size={'small'} onClick={() => handleConfirm(record)}>安排会议</Button>
                    }
                    {
                        status === 3 &&
                        <Button type="link" size={'small'} onClick={() => handleDispatch(record)}>线索派发</Button>
                    }
                    {
                        status === 0 &&
                        <Button type="link" size={'small'} danger onClick={() => handleDelete(record)}>删除</Button>
                    }
                    {
                        (status === 1 || status === 3) &&
                        <Button type="link" size={'small'} danger onClick={() => handleCancel(record)}>
                            {
                                status === 1 ? '撤销邀请' : '取消会议'
                            }
                        </Button>
                    }
                </Space>;
            }
        }
    ];
    // 切换页码
    const changePage = (page, pageSize) => {
        getTableData({
            pageNum: page,
            pageSize: pageSize
        });
    }
    return (<div className={'flex width-100per flex-direction-column'}>
        <Form form={form} layout={'inline'} onFinish={(values) => getTableData()}
              className={'width-100per flex flex-sub align-center gap-20'}>
            <Row wrap className={'flex-sub flex-wrap'} gutter={[20, 20]}>
                <Col span={8}>
                    <Form.Item label={'会议主题'} name={'theme'}>
                        <Input placeholder={'请输入会议主题'} allowClear/>
                    </Form.Item>
                </Col>
                <Col span={8}>
                    <Form.Item label={'会议时间'} name={'meetingTime'}>
                        <RangePicker className={'width-100per'} placeholder={'请选择会议时间'} allowClear/>
                    </Form.Item>
                </Col>
                <Col span={8}>
                    <Form.Item label={'客户名称'} name={'companyName'}>
                        <Input placeholder={'请输入客户名称'} allowClear/>
                    </Form.Item>
                </Col>
                <Col span={8}>
                    <Form.Item label={'会议状态'} name={'status'}>
                        <Select options={InviteMeetingStatus} placeholder={'请选择会议状态'} allowClear/>
                    </Form.Item>
                </Col>
                <Col span={8}>
                    <Form.Item label={'是否重点会议'} name={'isImportant'}>
                        <Select options={ImportantOptions} placeholder={'请选择是否重点会议'} allowClear/>
                    </Form.Item>
                </Col>
            </Row>
            <div className="width-1 height-84 bg-color-e5e6eb flex-shirnk margin-lr-20"/>
            <Form.Item noStyle>
                <Space direction={'vertical'}>
                    <Button type={'primary'} htmlType={'submit'} icon={<SearchOutlined/>}>查询</Button>
                    <Button htmlType={'reset'} icon={<ReloadOutlined/>} onClick={onReset}>重置</Button>
                </Space>
            </Form.Item>
        </Form>
        <Divider/>
        <Table
            rowKey="id"
            columns={columns}
            loading={false}
            pagination={{
                ...pagination,
                showTotal: (total) => `共 ${total} 条`,
                onChange: changePage
            }}
            dataSource={[...visitList, ...dataSource]}
        />
    </div>)
}
export default forwardRef(InviteMeeting);
