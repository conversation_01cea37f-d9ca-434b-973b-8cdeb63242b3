/**
 * @description index.jsx - 文件描述
 * <AUTHOR>
 *
 * Created on 2025/2/26 10:58
 */
import React, { useEffect, useRef, useState } from 'react';
import { Button, Form, message, Space } from 'antd';
import { CloudDownloadOutlined, PlusOutlined } from '@ant-design/icons';
import InviteMeeting from './components/InviteMeeting';
import OutMeeting from './components/OutMeeting';
import { useSelector } from 'react-redux';
import { exportMeeting } from '@/api/Opportunity/Meeting';
import dayjs from 'dayjs';
import { download } from '@/utils/common';
import { MeetingType } from './const';
import { useRouterLink } from '@/hook/useRouter';

const TabList = [
	{
		label: '来访邀约',
		value: '1',
	},
	{
		label: '会议纪要',
		value: '2',
	},
];
const MeetingList = (props, ref) => {
	const [tabValue, setTabValue] = useState(MeetingType.INTERNAL);
	const inviteRef = useRef(null);
	const outRef = useRef(null);
	const { linkTo, searchParams } = useRouterLink();
	// 用户信息
	const userInfo = useSelector((state) => {
		return state?.user.userInfo;
	});
	useEffect(() => {
		const type = searchParams.get('type') || '';
		if (type) {
			setTabValue(type);
		}
	}, []);
	// 外部导出
	const exportData = async () => {
		// exportMeeting
		const { meetingTime, ...values } = outRef.current.getFormData();
		const params = { ...values };
		if (meetingTime && meetingTime.length === 2) {
			params.meetingStartTime = dayjs(meetingTime?.[0]).format('YYYY-MM-DD HH:mm:ss');
			params.meetingEndTime = dayjs(meetingTime?.[1]).format('YYYY-MM-DD HH:mm:ss');
		}
		const res = await exportMeeting(params);
		if (res) {
			console.log(res);
			download.excel(res, '会议纪要');
		}
	};
	// 会议邀约
	const meetingInvite = () => {
		let href = `/businessOppty/meeting/meetingDetail?type=${tabValue}`;
		linkTo(href);
	};
	// 会议创建
	const meetingCreate = () => {
		let href = `/businessOppty/meeting/meetingDetail?type=${tabValue}`;
		linkTo(href);
	};
	return (
		<div className={'flex-sub flex flex-direction-column margin-20 padding-20  border-radius-4'}>
			<div className="padding-20 bg-color-ffffff border-radius-8 flex justify-between align-center line-height-24 margin-bottom-20">
				<div className={'font-size-16 font-weight-600 color-1d2129 flex flex-sub gap-40'}>
					{TabList.map((item) => {
						return (
							<div key={item.value} className={'flex align-center'} onClick={() => setTabValue(item.value)}>
								<span
									className={`font-size-16 cursor-pointer ${tabValue === item.value ? 'font-weight-600 color-1d2129' : 'font-weight-400 color-86909c'}}`}
								>
									{item.label}
								</span>
							</div>
						);
					})}
				</div>
				<Space>
					{tabValue === MeetingType.INTERNAL ? (
						<Button type={'primary'} icon={<PlusOutlined />} onClick={meetingInvite}>
							发起邀约
						</Button>
					) : (
						<Button type={'primary'} icon={<PlusOutlined />} onClick={meetingCreate}>
							会议创建
						</Button>
					)}
					{tabValue === MeetingType.EXTERNAL && (
						<Button icon={<CloudDownloadOutlined />} onClick={exportData}>
							会议导出
						</Button>
					)}
				</Space>
			</div>
			<div className={'bg-color-ffffff padding-20 border-radius-8 flex'}>
				{tabValue === MeetingType.INTERNAL ? (
					<InviteMeeting userInfo={userInfo} ref={inviteRef} meetingType={tabValue} />
				) : (
					<OutMeeting userInfo={userInfo} ref={outRef} meetingType={tabValue} />
				)}
			</div>
		</div>
	);
};
export default MeetingList;
