/**
 * @description index.jsx - 文件描述
 * <AUTHOR>
 *
 * Created on 2025/2/28 15:41
 */
import React, { forwardRef, useState, useImperativeHandle } from 'react';
import { Modal, message, Form, Input } from 'antd';
import { addFileType, updateFileType } from '@/api/Opportunity/Meeting';

const ModifyModal = (props, ref) => {
	const [isModalOpen, setIsModalOpen] = useState(false);
	const [form] = Form.useForm();
	const [data, setData] = useState(null);
	const [loading, setLoading] = useState(false);

	const handleOpen = (data) => {
		setIsModalOpen(true);
		setData(data);
		form.setFieldsValue(data);
	};

	const handleClose = () => {
		setIsModalOpen(false);
		form.resetFields();
		setData(null);
		setLoading(false);
	};

	useImperativeHandle(ref, () => ({
		showModal: handleOpen,
	}));

	const onFinish = async () => {
		const values = form.getFieldsValue();
		setLoading(true);
		const params = {
			...values,
		};
		let res;
		if (data?.id) {
			params.id = data.id;
			res = await updateFileType(params);
		} else {
			res = await addFileType(params);
		}
		if (res) {
			console.log('修改成功');
			setLoading(false);
			handleClose();
			props.reload?.();
		}
	};

	return (
		<Modal title={`材料类型${data ? '修改' : '新增'}`} open={isModalOpen} onCancel={handleClose} onOk={onFinish} confirmLoading={loading}>
			<Form form={form} onFinish={onFinish} labelCol={{ span: 6 }}>
				<Form.Item label="类型名称" name="name" rules={[{ required: true, message: '请输入类型名称!' }]}>
					<Input />
				</Form.Item>
				<Form.Item label="类型排序" name="sortNumber" rules={[{ required: false, message: '请输入类型排序!' }]}>
					<Input />
				</Form.Item>
			</Form>
		</Modal>
	);
};

export default forwardRef(ModifyModal);
