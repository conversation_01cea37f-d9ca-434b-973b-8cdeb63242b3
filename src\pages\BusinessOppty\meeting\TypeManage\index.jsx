/**
 * @description index.jsx - 文件描述
 * <AUTHOR>
 *
 * Created on 2025年2月26日17:36:55
 */
import React, {useEffect, useState} from 'react';
import {Button, Divider, Form, Input, Modal, Select, Space, Table, message} from "antd";
import { PlusOutlined, ReloadOutlined, SearchOutlined} from "@ant-design/icons";
import ModifyModal from "./components/ModifyModal";
import {listFileType, deleteFileType} from "@/api/Opportunity/Meeting";

const FileName = () => {
    const [form] = Form.useForm();
    const [loading, setLoading] = useState(false);
    const optionRef = React.useRef(null);
    const [tableData, setTableData] = useState([]);

    /* 材料类型定义  */
    const materialTypeOptions = [
        {label: '基地画册类', value: '1'},
        {label: '团体标准类', value: '2'},
        {label: '其他类', value: '3'},
    ];

    /* 表格表头 */
    const columns = [
        {
            title: '序号',
            dataIndex: 'index',
            key: 'index',
            width: 80,
            render: (text, record, index) => {
                return index + 1;
            }
        },
        {
            title: '材料类型',
            dataIndex: 'name',
            key: 'name',
        },
        {
            title: '排序',
            dataIndex: 'sortNumber',
            key: 'sortNumber',
            width: 80,
        },
        {
            title: '操作',
            dataIndex: 'action',
            key: 'action',
            width: 185,
            render: (text, record) => {
                return <Space.Compact >
                    <Button type={'link'} className={'padding-0'} onClick={() => handleEdit(record)}>编辑</Button>
                    <Button type={'link'} danger onClick={() => handleDel(record)}>删除</Button>
                </Space.Compact>
            }
        }
    ]

    /* 初始化获取表格数据 */
    useEffect(() => {
        // 获取表格数据
        getTableData();
    }, []);

    /* 重置表单 */
    const onReset = () => {
        form.resetFields();
        getTableData();
    };
    /* 查询列表请求 */
    const getTableData = async (args = {}) => {
        setLoading(true);
        const formValues = form.getFieldsValue();
        const params = {
            ...formValues,
            ...args,
        }
        console.log('查询列表请求', params)
        /* todo: 等待API */
        try {
            const {data} = await listFileType(params);
            if (data) {
                setTableData(data);
            }
            setLoading(false);
        } catch (e) {
            console.log('查询列表请求失败', e);
            setLoading(false);
        } finally {
            setLoading(false);
        }
    }

    /* 新增会议材料 */
    const handleAdd = () => {
        optionRef.current.showModal();
    };
    /* 修改会议材料 */
    const handleEdit = (record) => {
        optionRef.current.showModal(record);
    };
    /* 删除会议材料类型 */
    const handleDel = (record) => {
        console.log('删除会议材料类型', record);
        Modal.confirm({
            title: '确定删除?',
            content: '删除后将无法恢复，请谨慎操作',
            onOk() {
                const params = {
                    id: record.id
                };
                return new Promise(async (resolve) => {
                    const res = await deleteFileType(params);
                    if (res) {
                        message.success('删除成功');
                        resolve();
                        await getTableData();
                    }
                }).catch((e) => console.log('Oops errors!', e));
            },
            onCancel() {},
        });
    }

    return (<div className={'flex-sub flex flex-direction-column padding-20 '}>
        <div className="padding-20 bg-color-ffffff border-radius-8 flex justify-between align-center line-height-24 margin-bottom-20">
            <div className={'font-size-16 font-weight-600 color-1d2129'}>材料类型配置</div>
            <Button type={'primary'} icon={<PlusOutlined />} onClick={handleAdd}>新增</Button>
        </div>
        <div className={'padding-20 bg-color-ffffff border-radius-8'}>
            <Form form={form} layout={'inline'}
                  className={'width-100per flex align-center'}
                  wrapperCol={{style: {width: 'calc(100% - 100px)'}}}>
                <div className={'flex flex-sub'}>
                    <Form.Item label="类型名称" name="name" className={'flex-sub'}>
                        <Input style={{width: 320}} placeholder={'请输入类型名称'}/>
                    </Form.Item>
                </div>
                <Divider type="vertical" style={{margin: '0 16px'}}/>
                <Form.Item noStyle>
                    <Space>
                        <Button type={'primary'} htmlType={'submit'} icon={<SearchOutlined />} onClick={() => getTableData()}>查询</Button>
                        <Button htmlType={'reset'} icon={<ReloadOutlined />} onClick={onReset}>重置</Button>
                    </Space>
                </Form.Item>
            </Form>
            <Divider />
            <Table
                rowKey="id"
                columns={columns}
                loading={loading}
                pagination={false}
                dataSource={tableData}
            />
        </div>
        <ModifyModal ref={optionRef} reload={onReset} />
    </div>)
}
export default FileName;
