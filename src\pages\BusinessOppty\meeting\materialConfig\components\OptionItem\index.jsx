/**
 * @description index.jsx - 文件描述
 * <AUTHOR>
 *
 * Created on 2025/2/26 11:59
 */
import React, {forwardRef, useImperativeHandle, useState} from 'react';
import {Button, Form, Input, InputNumber, Modal, Select, Tag} from "antd";
import {PlusOutlined} from "@ant-design/icons";
import UploadFile from "@/components/UploadFile";
import {addTag, updateTag} from "@/api/Opportunity/Meeting";
import {classifyCode} from "../../const";

const OptionItem = (props, ref) => {
    const [visible, setVisible] = useState(false);
    const [record, setRecord] = useState(null);
    const [form] = Form.useForm();
    const [loading, setLoading] = useState(false);
    const watchFile = Form.useWatch('extendInfo', form);
    const [uploadLoading, setUploadLoading] = useState(false);
    const handleVisible = (data) => {
        setVisible(true);
        if (data) {
            console.log('data', data)
            setRecord(data);
            form.setFieldsValue(data);
        }
    };
    // 关闭弹窗
    const handleClose = () => {
        setVisible(false);
        form.resetFields();
        setRecord(null);
    };
    // 提交数据
    const handleSubmit = async () => {
        const values = await form.validateFields();
        let res;
        setLoading(true);
        try {
            if (record?.id) {
                res = await updateTag({...values, id: record.id, classifyCode});
            } else {
                res = await addTag({...values, classifyCode});
            }
            if (res) {
                setLoading(false);
                handleClose();
                props.reload?.();
            }
        } catch (e) {
            setLoading(false);
        }
    };
    /* 暴露出去的方法 */
    useImperativeHandle(ref, () => ({
        showModal: handleVisible
    }));
    /* 删除文件 */
    const removeFile = () => {
        form.setFieldsValue({extendInfo: ''});
    }
    /* 文件回调 */
    const handleFile = (fileUrl, file) => {
        const fileName = file.name.replace(/\.[^.]+$/, '');
        form.setFieldsValue({name: fileName});
    }
    return (<Modal
        open={visible}
        onCancel={handleClose}
        onOk={handleSubmit}
        confirmLoading={loading}
        maskClosable={false}
        title={`${record?.id ? '编辑' : '新增'}会议材料`}
    >
        <Form form={form} labelCol={{span: 6}}>
            <Form.Item label={'材料名称'} name={'name'} rules={[{required: true, message: '请输入材料名称'}]}>
                <Input/>
            </Form.Item>
            <Form.Item label={'材料类型'} name={'fileTypeId'} rules={[{required: true, message: '请选择材料类型'}]}>
                <Select className={'width-100per'} options={props.typeList}/>
            </Form.Item>
            <Form.Item label={'材料附件'} name={'extendInfo'} rules={[{required: true, message: '请上传材料附件'}]}>
                {
                    watchFile ?
                        <Tag closable
                             href={watchFile}
                             target='_blank'
                             onClose={removeFile}
                             className={'flex'}
                             title={watchFile}
                        >
                            <div className={'ellipsis width-200 flex-sub'}>
                                {watchFile}
                            </div>
                        </Tag>
                        : <UploadFile accept={`.pdf,.doc,docx,.ppt`} customName onChange={handleFile} setLoading={setUploadLoading}>
                            <Button type='primary' icon={<PlusOutlined/>} loading={uploadLoading} disabled={uploadLoading}>
                                选择文件
                            </Button>
                        </UploadFile>
                }
            </Form.Item>
            <Form.Item label={'排序'} name={'serialNumber'}>
                <InputNumber className={'width-100per'}/>
            </Form.Item>
        </Form>
    </Modal>)
}
export default forwardRef(OptionItem);
