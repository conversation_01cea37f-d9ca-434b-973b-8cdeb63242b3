/**
 * @description index.jsx - 文件描述
 * <AUTHOR>
 *
 * Created on 2025/2/26 11:59
 */
import React, { forwardRef, useImperativeHandle, useState } from 'react';
import { Button, Form, Input, InputNumber, Modal, Select, Tag } from 'antd';
import { PlusOutlined } from '@ant-design/icons';
import UploadFile from '@/components/UploadFile';
import { addTag, updateTag } from '@/api/Opportunity/Meeting';
import { classifyCode } from '../../const';
import { addMeetingType, updateMeetingType } from '@/api/Opportunity/MettingType';

const OptionItem = (props, ref) => {
	const [visible, setVisible] = useState(false);
	const [record, setRecord] = useState(null);
	const [form] = Form.useForm();
	const [loading, setLoading] = useState(false);
	const handleVisible = (data) => {
		setVisible(true);
		if (data) {
			console.log('data', data);
			setRecord(data);
			form.setFieldsValue(data);
		}
	};
	// 关闭弹窗
	const handleClose = () => {
		setVisible(false);
		form.resetFields();
		setRecord(null);
	};
	// 提交数据
	const handleSubmit = async () => {
		const values = await form.validateFields();
		let res;
		setLoading(true);
		try {
			if (record?.id) {
				res = await updateMeetingType({ ...values, id: record.id });
			} else {
				res = await addMeetingType({ ...values });
			}
			if (res) {
				setLoading(false);
				handleClose();
				props.reload?.();
			}
		} catch (e) {
			setLoading(false);
		}
	};
	/* 暴露出去的方法 */
	useImperativeHandle(ref, () => ({
		showModal: handleVisible,
	}));
	return (
		<Modal
			open={visible}
			onCancel={handleClose}
			onOk={handleSubmit}
			confirmLoading={loading}
			maskClosable={false}
			title={`${record?.id ? '编辑' : '新增'}会议类型`}
		>
			<Form form={form} labelCol={{ span: 6 }}>
				<Form.Item label={'会议类型'} name={'name'} rules={[{ required: true, message: '请输入会议类型' }]}>
					<Input />
				</Form.Item>
				<Form.Item label={'排序'} name={'sortNumber'}>
					<InputNumber className={'width-100per'} />
				</Form.Item>
			</Form>
		</Modal>
	);
};
export default forwardRef(OptionItem);
