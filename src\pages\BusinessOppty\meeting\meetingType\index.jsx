/**
 * @description index.jsx - 文件描述
 * <AUTHOR>
 *
 * Created on 2025年4月27日13:52:56
 */
import React, {useEffect, useState} from 'react';
import {Button, Divider, Form, Input, Modal, Select, Space, Table, message} from "antd";
import {PlusOutlined, ReloadOutlined, SearchOutlined} from "@ant-design/icons";
import OptionItem from "./components/OptionItem";
import {deleteMeetingType, listMeetingType} from "@/api/Opportunity/MettingType";

const MeetingType = () => {
    const [form] = Form.useForm();
    const [loading, setLoading] = useState(false);
    const optionRef = React.useRef(null);
    /* 分页数据 */
    const [pagination, setPagination] = useState({
        current: 1,
        pageSize: 10,
        total: 0
    });
    const [tableData, setTableData] = useState([]);
    /* 材料类型定义  */
    const [materialTypeOptions, setMaterialTypeOptions] = useState([]);

    /* 表格表头 */
    const columns = [
        {
            title: '序号',
            dataIndex: 'index',
            key: 'index',
            width: 80,
            render: (text, record, index) => {
                console.log('current', pagination.current)
                return pagination.pageSize * (pagination.current - 1) + index + 1;
            }
        },
        {
            title: '会议类型',
            dataIndex: 'name',
            key: 'name',
            render: (text, record) => {
                return <a href={record.extendInfo} target={'_blank'}>{text}</a>
            }
        },
        {
            title: '排序',
            dataIndex: 'sortNumber',
            key: 'sortNumber',
        },
        {
            title: '操作',
            dataIndex: 'action',
            key: 'action',
            width: 185,
            render: (text, record) => {
                return <Space.Compact>
                    <Button type={'link'} className={'padding-0'} onClick={() => handleEdit(record)}>编辑</Button>
                    <Button type={'link'} danger onClick={() => handleDel(record)}>删除</Button>
                </Space.Compact>
            }
        }
    ]

    /* 初始化获取表格数据 */
    useEffect(() => {
        // 获取表格数据
        getTableData();
    }, []);

    /* 重置表单 */
    const onReset = () => {
        form.resetFields();
        getTableData({
            current: 1,
            pageSize: 10,
        });
    };
    /* 查询列表请求 */
    const getTableData = async (args = {}) => {
        setLoading(true);
        const formValues = form.getFieldsValue();
        const params = {
            pageIndex: pagination.current,
            pageSize: pagination.pageSize,
            ...formValues,
            ...args,
        }
        try {
            const res = await listMeetingType(params);
            if (res) {
                console.log('查询列表请求', res);
                const dataSource = res.data;
                const total = dataSource.length;
                setPagination({
                    ...pagination,
                    total: total,
                    current: params.pageIndex,
                    pageSize: params.pageSize,
                });
                setTableData(dataSource);
                setLoading(false);
            }
        } catch (e) {
            console.log('查询列表请求失败', e);
            setLoading(false);
        } finally {
            setLoading(false);
        }

    }
    /* 修改页码 */
    const handlePageChange = (pageIndex, pageSize) => {
        getTableData({pageIndex, pageSize})
    };
    /* 新增会议材料 */
    const handleAdd = () => {
        optionRef.current.showModal();
    };
    /* 修改会议材料 */
    const handleEdit = (record) => {
        optionRef.current.showModal(record);
    };
    /* 删除会议材料 */
    const handleDel = (record) => {
        console.log('删除会议材料', record);
        Modal.confirm({
            title: '确定删除?',
            content: '删除后将无法恢复，请谨慎操作',
            onOk() {
                return new Promise(async (resolve) => {
                    const res = await deleteMeetingType({id: record.id});
                    if (res) {
                        message.success('删除成功');
                        resolve();
                        await getTableData();
                    }
                }).catch(() => console.log('Oops errors!'));
            },
            onCancel() {
            },
        });
    }

    return (<div className={'flex-sub flex flex-direction-column padding-20 '}>
        <div className="padding-20 bg-color-ffffff border-radius-8 flex justify-between align-center line-height-24 margin-bottom-20">
            <div className={'font-size-16 font-weight-600 color-1d2129'}>会议类型配置</div>
            <Button type={'primary'} icon={<PlusOutlined/>} onClick={handleAdd}>新增</Button>
        </div>
        <div className={'padding-20 bg-color-ffffff border-radius-8'}>
            <Form form={form} layout={'inline'} className={'flex align-center'}
                  wrapperCol={{style: {width: 'calc(100% - 100px)'}}}>
                <div className={'flex flex-sub'}>
                    <Form.Item label="会议类型" name="name">
                        <Input style={{width: 320}} placeholder={'请输入会议类型'}/>
                    </Form.Item>
                </div>
                <Divider type="vertical" style={{margin: '0 16px'}}/>
                <Form.Item noStyle>
                    <Space>
                        <Button type={'primary'} htmlType={'submit'} icon={<SearchOutlined/>}
                                onClick={() => getTableData()}>查询</Button>
                        <Button htmlType={'reset'} icon={<ReloadOutlined/>} onClick={onReset}>重置</Button>
                    </Space>
                </Form.Item>
            </Form>
            <Divider/>
            <Table
                rowKey="rowKey"
                columns={columns}
                loading={loading}
                pagination={{
                    ...pagination,
                    onChange: handlePageChange,
                    showSizeChanger: true,
                    showTotal: (total) => `共 ${total} 条`
                }}
                dataSource={tableData}
            />
        </div>
        <OptionItem ref={optionRef} reload={onReset}/>
    </div>)
}
export default MeetingType;
