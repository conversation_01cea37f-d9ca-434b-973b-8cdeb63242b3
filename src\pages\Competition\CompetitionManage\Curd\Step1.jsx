import Breadcrumb from '@/components/Breadcrumb';
import { useEffect, useRef, useState, forwardRef, useImperativeHandle } from 'react';
import {
	Tabs,
	Radio,
	Pagination,
	Space,
	Table,
	Form,
	Row,
	Col,
	Input,
	Select,
	Switch,
	Modal,
	Button,
	Steps,
	DatePicker,
	Cascader,
	Image,
	message,
	InputNumber,
	Checkbox,
} from 'antd';
import {
	ExportOutlined,
	SearchOutlined,
	ReloadOutlined,
	PlusOutlined,
	CloseCircleOutlined,
	CloseOutlined,
	PlusCircleOutlined,
} from '@ant-design/icons';
import { useRouterLink } from '@/hook/useRouter/index';
import UploadFile from '@/components/UploadFile';
import { saveActivity } from '@/api/Competition/CompetitionManage/index';
import { listDictItem } from '@/api/Competition/ConfigCenter/index';
import { getByPermissionPerms, getThreeLevelData } from '@/api/common';
import dayjs from 'dayjs';
import { useSelector } from 'react-redux';
import RictText from '@/components/RictText/index';
import { handleEditor } from '@/utils/common';
import { getDictData, getDictLabelByValue } from '@/utils/dictionary';
import { pinyin } from 'pinyin-pro';

const Index = (props = { detail: {} }) => {
	const { linkTo, searchParams } = useRouterLink();
	const id = searchParams.get('id');

	const [form] = Form.useForm();
	const actIndustry = Form.useWatch('actIndustry', form);
	const actGroup = Form.useWatch('actGroup', form);

	// 赛事时间
	const beginDateendDate = Form.useWatch('beginDateendDate', form);
	const { RangePicker } = DatePicker;
	const [ieType, setIeType] = useState(1);
	const ieTypeChange = (e) => {
		setIeType(e.target.value);
	};

	const [bgUrl, setBgUrl] = useState('');
	const [carouselChartUrl, setCarouselChartUrl] = useState('');
	const [largeScreenBgUrl, setLargeScreenBgUrl] = useState('');
	const [officialAccount01, setOfficialAccount01] = useState('');
	const [officialAccount02, setOfficialAccount02] = useState('');
	// 地区数据
	const [areaOptions, setAreaOptions] = useState([]);
	// 简称
	const [actSortNmOptions, setActSortNmOptions] = useState([]);
	// 对象
	const [orgTagOptions, setOrgTagOptions] = useState([]);
	const [actObjOptions, setActObjOptions] = useState([]);
	const [actIndustryOptions, setActIndustryOptions] = useState([]);
	const [actGroupOptions, setActGroupOptions] = useState([]);

	// 介绍
	const [introduction, setIntroduction] = useState('');
	// 赛事流程
	const [flowInfo, setFlowInfo] = useState('');
	// 参赛条件
	const [eligibility, setEligibility] = useState('');
	// 评审规则
	const [reviewRule, setReviewRule] = useState('');
	// 奖励支持
	const [awardSetting, setAwardSetting] = useState('');
	// 参赛须知
	const [competitionNotice, setCompetitionNotice] = useState('');

	useEffect(() => {
		getThreeLevelData({ level: 2 }).then((res) => {
			setAreaOptions(res.data || []);
		});

		listDictItem({
			code: 'event_short_name',
			status: 1,
		}).then((res) => {
			setActSortNmOptions(
				(res.data || []).map((ov) => {
					return {
						label: ov.itemName,
						value: ov.id,
					};
				})
			);
		});
		listDictItem({
			code: 'org_tags',
			status: 1,
		}).then((res) => {
			setOrgTagOptions(
				(res.data || []).map((ov) => {
					return {
						label: ov.itemName,
						value: ov.id,
					};
				})
			);
		});
		listDictItem({
			code: 'event_object',
			status: 1,
		}).then((res) => {
			setActObjOptions(
				(res.data || []).map((ov) => {
					return {
						label: ov.itemName,
						value: ov.id,
					};
				})
			);
		});
		listDictItem({
			code: 'event_industry',
			status: 1,
		}).then((res) => {
			setActIndustryOptions(
				(res.data || []).map((ov) => {
					return {
						label: ov.itemName,
						value: ov.id,
						parentDictItemList: ov.parentDictItemList,
					};
				})
			);
		});
		listDictItem({
			code: 'event_group_name',
			status: 1,
		}).then((res) => {
			setActGroupOptions(
				(res.data || []).map((ov) => {
					return {
						label: ov.itemName,
						value: ov.id,
					};
				})
			);
		});
	}, []);

	useEffect(() => {
		if (props.detail && props.detail.id) {
			if (props.detail.ieType == 1) {
				const officialAccount = JSON.parse(props.detail.contactVo.officialAccount) || [];
				const contactDetail = JSON.parse(props.detail.contactVo.contactDetail) || [];
				const actObjType = (props.detail.actObjType || '').split(',');
				const initParams = {
					...props.detail,
					...props.detail.organizationVo,
					...props.detail.contactVo,
					officialAccountNm01: officialAccount[0]?.nm || '',
					officialAccount01: officialAccount[0]?.code || '',
					officialAccountEnable01: officialAccount[0]?.enable || 0,
					officialAccountNm02: officialAccount[1]?.nm || '',
					officialAccount02: officialAccount[1]?.code || '',
					officialAccountEnable02: officialAccount[1]?.enable || 0,
					contactDetail,
					actObjType,
					beginDateendDate: [
						(props.detail.beginDate && dayjs(props.detail.beginDate, 'YYYY-MM-DD')) || '',
						(props.detail.endDate && dayjs(props.detail.endDate, 'YYYY-MM-DD')) || '',
					],
					entryBeginTimeentryEndTime: [
						(props.detail.entryBeginTime && dayjs(props.detail.entryBeginTime, 'YYYY-MM-DD HH:mm:ss')) || '',
						(props.detail.entryEndTime && dayjs(props.detail.entryEndTime, 'YYYY-MM-DD HH:mm:ss')) || '',
					],
				};
				form.setFieldsValue(initParams);
				setIeType(props.detail.ieType);
				setBgUrl(props.detail.bg);
				setCarouselChartUrl(props.detail.carouselChart);
				setLargeScreenBgUrl(props.detail.largeScreenBg);
				setOfficialAccount01(officialAccount[0]?.code);
				setOfficialAccount02(officialAccount[1]?.code);

				setIntroduction(props.detail.introduction || '');
				setFlowInfo(props.detail.flowInfo || '');
				setEligibility(props.detail.eligibility || '');
				setReviewRule(props.detail.reviewRule || '');
				setAwardSetting(props.detail.awardSetting || '');
				setCompetitionNotice(props.detail.competitionNotice || '');
			} else {
				const initParams = {
					...props.detail,
				};
				setCarouselChartUrl(props.detail.carouselChart);
				setIeType(props.detail.ieType);

				form.setFieldsValue(initParams);
			}
		} else {
			form.setFieldsValue({
				ieType,
			});
		}
	}, [props.detail]);

	const submit = (way = 'back') => {
		form.validateFields()
			.then((values) => {
				console.log(values);

				let params = {
					ieType: values.ieType || '', // 内外类型（1内部活动，2外部活动）
					name: values.name || '', // 名称
					carouselChart: values.carouselChart || '', // 轮播图
					carouselChartLink: values.carouselChartLink || '', // 轮播图点击链接
					sort: values.sort || 1, // 排序
					actObjType: (values.actObjType || []).join(','), // 报名类型
					actIndustryShow: values.actIndustryShow, // 行业是否展示在评分展示页
					actGroupShow: values.actGroupShow, // 组别是否展示在评分展示页
					activityContactDto: {},
					contactVo: {},
					activityOrganizationDto: {},
					adminStaffList: values.adminStaffList || [],
				};
				if (ieType == 1) {
					params = {
						ieType: values.ieType || '', // 内外类型（1内部活动，2外部活动）
						name: values.name || '', // 名称
						shortNameId: values.shortNameId || '', // 简介
						theme: values.theme || '', // 主题
						sort: values.sort || 1, // 排序
						actObjType: (values.actObjType || []).join(','), // 报名类型
						actIndustryShow: values.actIndustryShow || '', // 行业是否展示在评分展示页
						actGroupShow: values.actGroupShow || '', // 组别是否展示在评分展示页
						beginDate: dayjs(values.beginDateendDate[0]).format('YYYY-MM-DD 00:00:00') || '', // 开始时间
						endDate: dayjs(values.beginDateendDate[1]).format('YYYY-MM-DD 23:56:56') || '', // 结束时间
						entryBeginTime: dayjs(values.entryBeginTimeentryEndTime[0]).format('YYYY-MM-DD HH:mm:ss') || '', // 报名起始时间
						entryEndTime: dayjs(values.entryBeginTimeentryEndTime[1]).format('YYYY-MM-DD HH:mm:ss') || '', // 报名结束时间
						venue: values.venue || '', // 地点
						provinceCode: values.provinceCode || '',
						cityCode: values.cityCode || '',
						// introduction: values.introduction || '', // 介绍
						// flowInfo: values.flowInfo || '', // 流程
						// eligibility: values.eligibility || '', // 资格条件
						// reviewRule: values.reviewRule || '', // 评审规则
						// awardSetting: values.awardSetting || '', // 奖项设置
						bg: values.bg || '', // 背景图
						carouselChart: values.carouselChart || '', // 轮播图
						carouselChartLink: values.carouselChartLink || '', // 轮播图点击链接
						largeScreenBg: values.largeScreenBg || '', // 大屏背景图
						orgTags: values.orgTags || [],
						actObj: values.actObj || [],
						actIndustry: values.actIndustry || [],
						actGroup: values.actGroup || [],
						// 活动联系信息
						activityContactDto: {
							// activityId: id || '',
							organizingCommittee: values.organizingCommittee || '',
							contactDetail: values.contactDetail || [],
							email: values.email || '',
							officialAccount: [
								{
									nm: values.officialAccountNm01,
									code: officialAccount01,
									enable: values.officialAccountEnable01,
								},
								{
									nm: values.officialAccountNm02,
									code: officialAccount02,
									enable: values.officialAccountEnable02,
								},
							],
						},
						// 活动组织信息
						activityOrganizationDto: {
							// activityId: id || '',
							guidanceUnit: values.guidanceUnit || '',
							organizerUnit: values.organizerUnit || '',
							undertakingUnit: values.undertakingUnit || '',
							helpUnit: values.helpUnit || '',
							supportUnit: values.supportUnit || '',
							specialSupportUnit: values.specialSupportUnit || '',
							structureOverview: values.structureOverview || '',
						},
						adminStaffList: values.adminStaffList || [],
					};
				}
				if (id) {
					params.id = id || ''; // 主键
					params.entryFormTemplateId = props.detail.entryFormTemplateId || ''; // 主键
					params.activityContactDto.activityId = id || ''; // 主键
					params.activityContactDto.id = (props.detail.contactVo && props.detail.contactVo.id) || ''; // 主键
					params.activityOrganizationDto.activityId = id || ''; // 主键
					params.activityOrganizationDto.id = (props.detail.organizationVo && props.detail.organizationVo.id) || ''; // 主键
				}
				Promise.all([
					handleEditor(introduction),
					handleEditor(flowInfo),
					handleEditor(eligibility),
					handleEditor(reviewRule),
					handleEditor(awardSetting),
					handleEditor(competitionNotice),
				]).then((resList) => {
					console.log('🚀 ~ .then ~ params:', params);
					saveActivity({
						...params,
						introduction: resList[0] || '',
						flowInfo: resList[1] || '',
						eligibility: resList[2] || '',
						reviewRule: resList[3] || '',
						awardSetting: resList[4] || '',
						competitionNotice: resList[5] || '',
					}).then((res) => {
						if (way == 'back') {
							linkTo(-1);
						} else {
							props.setDetailId && props.setDetailId(res.data.id);
							// 活动状态 0-暂存 1-待开始 才能修改报名模版
							props.setCurrent(1);
						}
					});
				});
			})
			.catch((err) => {
				console.log('🚀 ~ submit ~ err:', err);
				message.warning(err.errorFields && err.errorFields[0].errors);
			});
	};

	// 显示弹窗
	const UserModalRef = useRef(null);

	const userInfo = useSelector((state) => {
		return state.user.userInfo;
	});

	const showUserModal = () => {
		// UserModalRef.current.showModal(form.getFieldValue('adminStaffList') || [], userInfo.id);
		UserModalRef.current.showModal(form.getFieldValue('adminStaffList') || []);
	};

	// 管理人员改变
	const onChangeUser = (list = []) => {
		form.setFieldValue('adminStaffList', list);
	};

	return (
		<Form
			form={form}
			autoComplete="off"
			labelCol={{ span: 5 }}
			wrapperCol={{ span: 17 }}
			disabled={props.disabled || false}
			initialValues={{
				officialAccountEnable01: 1,
				officialAccountEnable02: 1,
				actIndustry: [],
				actGroup: [],
				sort: 1,
				actObjType: [1], // 报名类型
				actIndustryShow: 1,
				actGroupShow: 1,
				adminStaffList: [],
			}}
		>
			<div className="flex align-center justify-start line-height-24 padding-tb-16">
				<div className="width-6 height-16 border-radius-4 bg-color-165dff"></div>
				<div className="font-size-16 font-weight-500 margin-left-8">赛事信息</div>
			</div>

			<Form.Item
				label="赛事类型"
				name="ieType"
				rules={[
					{
						required: true,
					},
				]}
			>
				<Radio.Group onChange={(e) => ieTypeChange(e)}>
					<Radio value={1}>内部赛事（需填写详细信息）</Radio>
					<Radio value={2}>外部赛事</Radio>
				</Radio.Group>
			</Form.Item>

			<Form.Item
				label="排序"
				name="sort"
				rules={[
					{
						required: !true,
					},
				]}
			>
				{/* <Input placeholder='请输入赛事排序' /> */}
				<InputNumber min={1} max={********} className="width-200" />
			</Form.Item>

			<div className="flex align-center justify-start line-height-24 padding-tb-16">
				<div className="width-6 height-16 border-radius-4 bg-color-165dff"></div>
				<div className="font-size-16 font-weight-500 margin-left-8">赛事基本信息</div>
			</div>

			<Form.Item
				label="赛事名称"
				name="name"
				rules={[
					{
						required: true,
					},
				]}
			>
				<Input placeholder="请输入赛事名称" />
			</Form.Item>

			{ieType == 1 && (
				<>
					<Form.Item
						label="赛事标签"
						name="shortNameId"
						rules={[
							{
								required: true,
							},
						]}
					>
						{/* <Input placeholder='请输入赛事标签' /> */}
						<Select placeholder="请选择赛事标签" options={actSortNmOptions} />
					</Form.Item>
					<Form.Item label="赛事主题" name="theme">
						<Input placeholder="请输入赛事主题" />
					</Form.Item>
					<Form.Item
						label="赛事时间"
						name="beginDateendDate"
						rules={[
							{
								required: true,
							},
						]}
					>
						<RangePicker format="YYYY/MM/DD" valueFormat="YYYY/MM/DD" />
					</Form.Item>
					<Form.Item
						label="报名时间"
						name="entryBeginTimeentryEndTime"
						rules={[
							{
								required: true,
							},
						]}
					>
						<RangePicker
							showTime
							format="YYYY/MM/DD HH:mm:ss"
							valueFormat="YYYY/MM/DD HH:mm:ss"
							disabled={!beginDateendDate}
							disabledDate={(current) => {
								return current && (current < beginDateendDate[0] || current > beginDateendDate[1]);
							}}
						/>
					</Form.Item>

					<Form.Item hidden name="provinceCode">
						<Input />
					</Form.Item>
					<Form.Item hidden name="cityCode">
						<Input />
					</Form.Item>
					<Form.Item
						label="所属区域"
						name="tempArea"
						rules={[
							{
								required: true,
							},
						]}
					>
						<Cascader
							className="cascader-box"
							options={areaOptions}
							placeholder="请选择所属区域"
							displayRender={(label) => label.filter((ov) => ov).join('-')}
							onChange={(e = [undefined, undefined]) => {
								form.setFieldValue('provinceCode', e[0]);
								form.setFieldValue('cityCode', e[1]);
							}}
						/>
					</Form.Item>

					<Form.Item
						label="赛事地点"
						name="venue"
						rules={[
							{
								required: true,
							},
						]}
					>
						<Input placeholder="请输入赛事地点" />
					</Form.Item>
					<Form.Item
						label="赛事介绍"
						name="introduction"
						rules={[
							{
								required: true,
							},
						]}
					>
						<RictText
							value={introduction}
							disabled={!!props.disabled}
							onChange={(e) => {
								setIntroduction(e);
								// form.setFieldValue('introduction', e)
							}}
						></RictText>
					</Form.Item>
					<Form.Item
						label="赛事流程"
						name="flowInfo"
						rules={[
							{
								required: true,
							},
						]}
					>
						<RictText
							value={flowInfo}
							disabled={!!props.disabled}
							onChange={(e) => {
								setFlowInfo(e);
								// form.setFieldValue('flowInfo', e)
							}}
						></RictText>
					</Form.Item>
					<Form.Item
						label="参赛条件"
						name="eligibility"
						rules={[
							{
								required: true,
							},
						]}
					>
						<RictText
							value={eligibility}
							disabled={!!props.disabled}
							onChange={(e) => {
								setEligibility(e);
								// form.setFieldValue('eligibility', e)
							}}
						></RictText>
					</Form.Item>

					<Form.Item
						label="评审规则"
						name="reviewRule"
						rules={[
							{
								required: true,
							},
						]}
					>
						<RictText
							value={reviewRule}
							disabled={!!props.disabled}
							onChange={(e) => {
								setReviewRule(e);
								// form.setFieldValue('reviewRule', e)
							}}
						></RictText>
					</Form.Item>
					<Form.Item
						label="奖励支持"
						name="awardSetting"
						rules={[
							{
								required: true,
							},
						]}
					>
						<RictText
							value={awardSetting}
							disabled={!!props.disabled}
							onChange={(e) => {
								setAwardSetting(e);
								// form.setFieldValue('awardSetting', e)
							}}
						></RictText>
					</Form.Item>
					<Form.Item label="参赛须知" name="competitionNotice">
						<RictText
							value={competitionNotice}
							disabled={!!props.disabled}
							onChange={(e) => {
								setCompetitionNotice(e);
							}}
						></RictText>
					</Form.Item>
					<Form.Item
						label="赛事封面"
						name="bg"
						rules={[
							{
								required: true,
							},
						]}
					>
						<div>
							{bgUrl ? (
								<div className="border-box width-130 height-130 position-relative border-solid-0505050f">
									<div className="height-0 width-0 position-absolute opacity-0 overflow-hidden">
										<Input className="height-0 width-0" />
									</div>
									<Image width={130} height={130} src={bgUrl} />
									{!props.disabled && (
										<div className="position-absolute right-0 top-0 z-index-10">
											<CloseCircleOutlined
												className="a font-size-20 color-ff9535"
												onClick={() => {
													setBgUrl('');
													form.setFieldValue('bg', '');
												}}
											/>
										</div>
									)}
								</div>
							) : (
								<UploadFile
									onChange={(fileUrl) => {
										setBgUrl(fileUrl);
										form.setFieldValue('bg', fileUrl);
									}}
								>
									<div className="width-130 height-130 text-align-center border-box padding-top-30 bg-color-f2f3f5 border-radius-8 a margin-right-20">
										<PlusOutlined className="font-size-40" />
										<div>点击上传图片</div>
									</div>
								</UploadFile>
							)}
							<div>图片：建议尺寸：668*380</div>
						</div>
					</Form.Item>
					<Form.Item label="大屏背景图" name="largeScreenBg">
						<div>
							{largeScreenBgUrl ? (
								<div className="border-box width-130 height-130 position-relative border-solid-0505050f">
									<div className="height-0 width-0 position-absolute opacity-0 overflow-hidden">
										<Input className="height-0 width-0" />
									</div>
									<Image width={130} height={130} src={largeScreenBgUrl} />
									{!props.disabled && (
										<div className="position-absolute right-0 top-0 z-index-10">
											<CloseCircleOutlined
												className="a font-size-20 color-ff9535"
												onClick={() => {
													setLargeScreenBgUrl('');
													form.setFieldValue('largeScreenBg', '');
												}}
											/>
										</div>
									)}
								</div>
							) : (
								<UploadFile
									onChange={(fileUrl) => {
										setLargeScreenBgUrl(fileUrl);
										form.setFieldValue('largeScreenBg', fileUrl);
									}}
								>
									<div className="width-130 height-130 text-align-center border-box padding-top-30 bg-color-f2f3f5 border-radius-8 a margin-right-20">
										<PlusOutlined className="font-size-40" />
										<div>点击上传图片</div>
									</div>
								</UploadFile>
							)}
							<div>图片：建议尺寸：1920*1080</div>
						</div>
					</Form.Item>
					<Form.Item
						label="组织单位"
						name="orgTags"
						rules={[
							{
								required: true,
							},
						]}
					>
						<Select mode="multiple" placeholder="请选择组织单位" options={orgTagOptions} />
					</Form.Item>
					<Form.Item
						label="参赛方标签"
						name="actObj"
						rules={[
							{
								required: true,
							},
						]}
					>
						<Select mode="multiple" placeholder="请选择参赛方标签" options={actObjOptions} />
					</Form.Item>
					<Form.Item
						label="行业"
						name="actIndustry"
						rules={[
							{
								required: true,
							},
						]}
					>
						<Select
							mode="multiple"
							placeholder="请选择行业"
							options={actIndustryOptions}
							onChange={(_, e) => {
								const options = [];
								const ids = [];
								e.forEach(({ parentDictItemList }) => {
									parentDictItemList.forEach(({ parentDictItemId, parentDictItemName }) => {
										if (!options.find(({ value }) => value === parentDictItemId)) {
											options.push({
												value: parentDictItemId,
												label: parentDictItemName,
											});
											ids.push(parentDictItemId);
										}
									});
								});
								setActGroupOptions(options);
								if (!actGroup.some((ov) => ids.includes(ov))) {
									form.setFieldValue('actGroup', []);
								}
							}}
						/>
					</Form.Item>

					<Form.Item
						label="参赛方类型"
						name="actObjType"
						rules={[
							{
								required: true,
							},
						]}
					>
						<Checkbox.Group options={getDictData('actObjType')} />
					</Form.Item>

					<Form.Item
						label="行业是否展示在评分展示页"
						name="actIndustryShow"
						rules={[
							{
								required: !true,
							},
						]}
					>
						<Radio.Group>
							<Radio value={1}>是</Radio>
							<Radio value={0}>否</Radio>
						</Radio.Group>
					</Form.Item>
					<Form.Item
						label="组别"
						name="actGroup"
						rules={[
							{
								required: true,
							},
						]}
					>
						<Select
							mode="multiple"
							placeholder="请选择组别"
							options={actGroupOptions}
							disabled={!actIndustry || actIndustry.length === 0}
							onClick={() => {
								if (!actIndustry || actIndustry.length === 0) {
									message.warning('请先选择行业');
								}
							}}
						/>
					</Form.Item>

					<Form.Item
						label="组别是否展示在评分展示页"
						name="actGroupShow"
						rules={[
							{
								required: !true,
							},
						]}
					>
						<Radio.Group>
							<Radio value={1}>是</Radio>
							<Radio value={0}>否</Radio>
						</Radio.Group>
					</Form.Item>

					<div className="flex align-center justify-start line-height-24 padding-tb-16">
						<div className="width-6 height-16 border-radius-4 bg-color-165dff"></div>
						<div className="font-size-16 font-weight-500 margin-left-8">组织架构</div>
					</div>
					<Form.Item
						label="指导单位"
						name="guidanceUnit"
						rules={
							[
								// {
								// 	required: true,
								// },
							]
						}
					>
						<Input.TextArea
							autoSize={{
								minRows: 1,
							}}
							placeholder="请输入指导单位"
						/>
					</Form.Item>
					<Form.Item
						label="主办单位"
						name="organizerUnit"
						rules={[
							{
								required: true,
							},
						]}
					>
						<Input.TextArea
							autoSize={{
								minRows: 1,
							}}
							placeholder="请输入主办单位"
						/>
					</Form.Item>
					<Form.Item
						label="承办单位"
						name="undertakingUnit"
						rules={[
							{
								required: true,
							},
						]}
					>
						<Input.TextArea
							autoSize={{
								minRows: 1,
							}}
							placeholder="请输入承办单位"
						/>
					</Form.Item>
					<Form.Item label="协办单位" name="helpUnit" rules={[]}>
						<Input.TextArea
							autoSize={{
								minRows: 1,
							}}
							placeholder="请输入协办单位"
						/>
					</Form.Item>
					<Form.Item label="支持单位" name="supportUnit" rules={[]}>
						<Input.TextArea
							autoSize={{
								minRows: 1,
							}}
							placeholder="请输入支持单位"
						/>
					</Form.Item>
					<Form.Item label="特别支持单位" name="specialSupportUnit" rules={[]}>
						<Input.TextArea
							autoSize={{
								minRows: 1,
							}}
							placeholder="请输入特别支持单位"
						/>
					</Form.Item>
					<Form.Item label="组织架构概述" name="structureOverview" rules={[]}>
						<Input.TextArea
							autoSize={{
								minRows: 4,
							}}
							placeholder="请输入组织架构概述"
						/>
					</Form.Item>

					<div className="flex align-center justify-start line-height-24 padding-tb-16">
						<div className="width-6 height-16 border-radius-4 bg-color-165dff"></div>
						<div className="font-size-16 font-weight-500 margin-left-8">赛事联系信息</div>
					</div>

					<Form.Item
						label="赛事组委会名称"
						name="organizingCommittee"
						rules={[
							{
								required: true,
							},
						]}
					>
						<Input.TextArea
							autoSize={{
								minRows: 1,
							}}
							placeholder="请输入赛事组委会名称"
						/>
					</Form.Item>

					<Form.Item label="联系人" required>
						<Form.List
							name="contactDetail"
							initialValue={[
								{
									nm: '',
									tel: '',
								},
							]}
						>
							{(fields, { add, remove }, { errors }) => (
								<div className="flex align-start justify-start">
									<div className="">
										{fields.map((field, index) => {
											return (
												<div key={`${index}`}>
													<div className="flex justify-start align-start">
														<Form.Item
															name={[field.name, 'nm']}
															rules={[
																{
																	required: true,
																	message: '请输入联系人名称',
																},
															]}
														>
															<Input placeholder="请输入联系人名称" />
														</Form.Item>
														<Form.Item
															name={[field.name, 'tel']}
															rules={[
																{
																	required: true,
																	message: '请输入联系人联系方式',
																},
															]}
															className="margin-left-20"
														>
															<Input placeholder="请输入联系人联系方式" />
														</Form.Item>
														{!props.disabled && (
															<Button
																type="primary"
																danger
																className="margin-left-20"
																disabled={index === 0}
																onClick={() => remove(index)}
															>
																删除
															</Button>
														)}
													</div>
												</div>
											);
										})}
									</div>
									{!props.disabled && (
										<Button type="primary" onClick={() => add()} className="margin-left-20">
											添加联系人
										</Button>
									)}
								</div>
							)}
						</Form.List>
					</Form.Item>

					<Form.Item
						label="邮箱"
						name="email"
						rules={[
							{
								required: true,
							},
						]}
					>
						<Input.TextArea
							autoSize={{
								minRows: 1,
							}}
							placeholder="请输入邮箱"
						/>
					</Form.Item>

					<Form.Item
						label="公众号"
						name="officialAccount01"
						rules={[
							{
								required: true,
							},
						]}
					>
						{officialAccount01 ? (
							<div className="border-box width-130 height-130 position-relative border-solid-0505050f">
								<Image width={130} height={130} src={officialAccount01} />
								{!props.disabled && (
									<div className="position-absolute right-0 top-0 z-index-10">
										<CloseCircleOutlined
											className="a font-size-20 color-ff9535"
											onClick={() => {
												setOfficialAccount01('');
												form.setFieldValue('officialAccount01', '');
											}}
										/>
									</div>
								)}
							</div>
						) : (
							<UploadFile
								onChange={(fileUrl) => {
									setOfficialAccount01(fileUrl);
									form.setFieldValue('officialAccount01', fileUrl);
								}}
							>
								<div className="width-130 height-130 text-align-center border-box padding-top-30 bg-color-f2f3f5 border-radius-8 a margin-right-20">
									<PlusOutlined className="font-size-40" />
									<div>点击上传图片</div>
								</div>
							</UploadFile>
						)}
					</Form.Item>

					<Form.Item
						label="公众号名称"
						name="officialAccountNm01"
						rules={[
							{
								required: true,
							},
						]}
					>
						<Input.TextArea
							autoSize={{
								minRows: 1,
							}}
							placeholder="请输入二维码名称"
						/>
					</Form.Item>

					<Form.Item
						label="公众号状态"
						name="officialAccountEnable01"
						rules={[
							{
								required: true,
							},
						]}
					>
						<Select
							placeholder=""
							options={[
								{
									label: '启用',
									value: 1,
								},
								{
									label: '关闭',
									value: 0,
								},
							]}
						/>
					</Form.Item>

					<Form.Item
						label="视频号"
						name="officialAccount02"
						rules={[
							{
								required: true,
							},
						]}
					>
						{officialAccount02 ? (
							<div className="border-box width-130 height-130 position-relative border-solid-0505050f">
								<Image width={130} height={130} src={officialAccount02} />
								{!props.disabled && (
									<div className="position-absolute right-0 top-0 z-index-10">
										<CloseCircleOutlined
											className="a font-size-20 color-ff9535"
											onClick={() => {
												setOfficialAccount02('');
												form.setFieldValue('officialAccount02', '');
											}}
										/>
									</div>
								)}
							</div>
						) : (
							<UploadFile
								onChange={(fileUrl) => {
									setOfficialAccount02(fileUrl);
									form.setFieldValue('officialAccount02', fileUrl);
								}}
							>
								<div className="width-130 height-130 text-align-center border-box padding-top-30 bg-color-f2f3f5 border-radius-8 a margin-right-20">
									<PlusOutlined className="font-size-40" />
									<div>点击上传图片</div>
								</div>
							</UploadFile>
						)}
					</Form.Item>

					<Form.Item
						label="视频号名称"
						name="officialAccountNm02"
						rules={[
							{
								required: true,
							},
						]}
					>
						<Input.TextArea
							autoSize={{
								minRows: 1,
							}}
							placeholder="请输入二维码名称"
						/>
					</Form.Item>

					<Form.Item
						label="视频号状态"
						name="officialAccountEnable02"
						rules={[
							{
								required: true,
							},
						]}
					>
						<Select
							placeholder=""
							options={[
								{
									label: '启用',
									value: 1,
								},
								{
									label: '关闭',
									value: 0,
								},
							]}
						/>
					</Form.Item>

					<div className="flex align-center justify-start line-height-24 padding-tb-16">
						<div className="width-6 height-16 border-radius-4 bg-color-165dff"></div>
						<div className="font-size-16 font-weight-500 margin-left-8">赛事管理人员</div>
					</div>

					<Form.Item
						name="adminStaffList"
						label="管理人员"
						rules={[
							{
								type: 'array',
								required: true,
								message: '请选择管理人员',
							},
						]}
					>
						<Form.Item noStyle shouldUpdate>
							{() => {
								const list = form.getFieldValue('adminStaffList');
								return (
									<div className="flex align-center justify-between padding-4 bg-color-f2f3f5 border-radius-2">
										<Space size={4} className="flex-sub flex flex-wrap align-center">
											{list.map((item, index) => (
												<Space key={index} size={4} className="flex align-center padding-lr-8 line-height-22 bg-color-ffffff">
													<div>{item.adminUserName}</div>
													{item.adminUserId !== userInfo.id ? (
														<CloseOutlined
															style={{
																fontSize: '12px',
															}}
															onClick={() => {
																list.splice(index, 1);
																form.setFieldValue('adminStaffList', list);
															}}
														/>
													) : null}
												</Space>
											))}
										</Space>
										<div className="padding-lr-8 cursor-pointer" onClick={showUserModal}>
											<PlusCircleOutlined />
										</div>
									</div>
								);
							}}
						</Form.Item>
					</Form.Item>

					<UserModal ref={UserModalRef} onChange={onChangeUser} />
				</>
			)}
			{ieType != 1 && (
				<>
					<Form.Item
						label="首页轮播图"
						name="carouselChart"
						rules={[
							{
								required: true,
							},
						]}
					>
						<div>
							{carouselChartUrl ? (
								<div className="border-box width-130 height-130 position-relative border-solid-0505050f">
									<div className="height-0 width-0 position-absolute opacity-0 overflow-hidden">
										<Input className="height-0 width-0" />
									</div>
									<Image width={130} height={130} src={carouselChartUrl} />
									{!props.disabled && (
										<div className="position-absolute right-0 top-0 z-index-10">
											<CloseCircleOutlined
												className="a font-size-20 color-ff9535"
												onClick={() => {
													setCarouselChartUrl('');
													form.setFieldValue('carouselChart', '');
												}}
											/>
										</div>
									)}
								</div>
							) : (
								<UploadFile
									onChange={(fileUrl) => {
										setCarouselChartUrl(fileUrl);
										form.setFieldValue('carouselChart', fileUrl);
									}}
								>
									<div className="width-130 height-130 text-align-center border-box padding-top-30 bg-color-f2f3f5 border-radius-8 a margin-right-20">
										<PlusOutlined className="font-size-40" />
										<div>点击上传图片</div>
									</div>
								</UploadFile>
							)}
							<div>建议尺寸：1920*630</div>
						</div>
					</Form.Item>

					<Form.Item
						label="轮播图点击链接"
						name="carouselChartLink"
						rules={[
							{
								// required: true,
							},
						]}
					>
						<Input.TextArea
							autoSize={{
								minRows: 1,
							}}
							placeholder="请输入轮播图点击链接"
						/>
					</Form.Item>
				</>
			)}

			{!props.disabled && (
				<Form.Item label=" " rules={[]} colon={false}>
					<Button type="primary" onClick={() => submit('back')} className="margin-right-20">
						提交保存
					</Button>
					<Button
						type="primary"
						onClick={() => {
							submit('next');
						}}
					>
						保存并下一步
					</Button>
				</Form.Item>
			)}
		</Form>
	);
};

// 选择人员弹窗
const UserModal = forwardRef((props, ref) => {
	const [open, setOpen] = useState(false);
	const [userList, setUserList] = useState([]);
	const [defaultId, setDefaultId] = useState('');
	const [checkedList, setCheckedList] = useState([]);

	// 显示弹窗
	const showModal = (list = [], userId = '') => {
		setDefaultId(userId);
		setCheckedList(list.map((item) => item.adminUserId));
		setOpen(true);
	};

	// 选中/取消
	const checked = (checkedId) => {
		const index = checkedList.findIndex((id) => id === checkedId);

		if (index > -1 && checkedId !== defaultId) {
			checkedList.splice(index, 1);
		} else {
			checkedList.push(checkedId);
		}
		setCheckedList([...checkedList]);
	};

	// 提交
	const submit = () => {
		props.onChange([
			...userList
				.map((item) => item.list)
				.flat(2)
				.filter((item) => checkedList.includes(item.adminUserId)),
		]);
		setOpen(false);
	};

	// 用户列表按拼音排序
	const sortFormatUserList = (list = []) => {
		const chatObj = {};
		list.forEach((item) => {
			const { id, userName } = item;
			const chat = pinyin(userName, {
				toneType: 'none',
			})[0].toUpperCase();
			if (!chatObj[chat]) {
				chatObj[chat] = [];
			}

			chatObj[chat].push({
				adminUserId: id,
				adminUserName: userName,
			});
		});

		return Object.keys(chatObj)
			.sort()
			.map((chat) => {
				return {
					chat,
					list: chatObj[chat],
				};
			});
	};

	useEffect(() => {
		getByPermissionPerms({ perms: 'competition' }, { isCache: true }).then((res) => {
			setUserList(sortFormatUserList(res.data));
		});
	}, []);

	useImperativeHandle(ref, () => {
		return {
			showModal,
		};
	});

	return (
		<Modal open={open} title="添加管理人员" centered okText="保存" onCancel={() => setOpen(false)} onOk={submit} width={400}>
			<div className="padding-lr-4 height-360 overflowY-auto scrollbar">
				{userList.map((item) => {
					return (
						<div className="padding-tb-6" key={item.chat}>
							<div className="line-height-22 color-4e5969">{item.chat}</div>
							<div className="flex flex-wrap">
								{item.list.map((user) => {
									return (
										<div
											key={user.adminUserId}
											className={`margin-top-12 margin-right-12 padding-lr-12 padding-tb-4 line-height-22 border-radius-2 cursor-pointer ${
												checkedList.includes(user.adminUserId) || defaultId === user.adminUserId
													? 'border-165dff color-165dff'
													: 'border-c9cdd4'
											}`}
											onClick={() => checked(user.adminUserId)}
										>
											{user.adminUserName}
										</div>
									);
								})}
							</div>
						</div>
					);
				})}
			</div>
		</Modal>
	);
});

export default Index;
