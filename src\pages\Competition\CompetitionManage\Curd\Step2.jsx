import { memo, useEffect, useState } from 'react';

import {
	Button,
	Space,
	Modal,
	message,
	Form,
	Input,
	InputNumber,
	Checkbox,
	Radio,
	Popconfirm,
	DatePicker,
	Upload,
	Col,
	Row,
	Cascader,
	Select,
} from 'antd';
import {
	CloseCircleOutlined,
	EditOutlined,
	DeleteOutlined,
	CloseOutlined,
	CloudUploadOutlined,
	ArrowUpOutlined,
	ArrowDownOutlined,
} from '@ant-design/icons';

import { getImageSrc } from '@/assets/images/index';
import { getFileTypeData, handleLimitData, getRandomNum, initFormTplData } from './utils';

import { fileUpload } from '@/api/common';
import { getFormTemplateDetail, updateFormTemplate } from '@/api/Competition/CompetitionManage';

// 固定字段 name
const fixedName = ['industryId', 'projectGroupId'];

const Index = (props = {}) => {
	// 模版信息
	const templateId = props.templateId || '';

	// 表单模版数据
	const [formTplData, setFormTplData] = useState([]);

	// 模块弹窗 显示隐藏
	const [moduleOpen, setModuleOpen] = useState(false);
	const [moduleData, setModuleData] = useState({});

	// 字段弹窗  显示隐藏
	const [fieldOpen, setFieldOpen] = useState(false);
	const [fieldData, setFieldData] = useState({});

	// 新增模块项
	const addModuleItem = ({ name = '', type = 1, optionInfo = [] } = {}) => {
		return new Promise((resolve) => {
			const moduleItem = {
				key: getRandomNum(),
				id: undefined,
				templateId,
				name,
				type,
				optionInfo,
				delFlag: 0,
				fields: [],
			};
			formTplData.push(moduleItem);
			setFormTplData([...formTplData]);
			resolve(moduleItem);
		});
	};

	// 删除模块项
	const delModuleItem = (moduleItemData) => {
		if (moduleItemData.id) {
			// 有id 将 '本身' 及 'fields元素' 的 delFlag 修改为 1
			moduleItemData.delFlag = 1;
			moduleItemData.fields.forEach((ov) => {
				ov.delFlag = 1;
			});
		} else {
			// 无id 直接 根据 key 删除
			const index = formTplData.findIndex(({ key }) => key === moduleItemData.key);
			if (index > -1) {
				formTplData.splice(index, 1);
			}
		}

		setFormTplData([...formTplData]);
	};

	// 表单字段 弹窗确认回调
	const modalModuleSubmitCallback = (values) => {
		if (values.key) {
			// 更新
			// 找到对应模块项 index
			const index = formTplData.findIndex(({ key }) => key === moduleData.key);
			if (index > -1) {
				const { type, fields } = formTplData[index];
				formTplData[index] = {
					...formTplData[index],
					...values,
				};
				if (type === 2) {
					// 新的 optionInfo key
					const keys = values.optionInfo.map((ov) => ov.key);

					formTplData[index].fields = fields.filter((ov) => {
						if (!keys.includes(ov.pieceOptionKey)) {
							if (ov.id) {
								ov.delFlag = 1;
							} else {
								return false;
							}
						}
						return true;
					});
				}
			}
			setFormTplData([...formTplData]);
		} else {
			// 新增
			addModuleItem(values);
		}
		setModuleOpen(false);
	};

	// 新增表单字段
	const addFieldItem = (
		{ cnName = '', fieldName = '', fieldPrompt = '', fieldType = 3, fieldOption = [], fieldTemplate = '', fieldCheck = [] } = {},
		moduleKey,
		optionIndex = ''
	) => {
		if (typeof moduleKey !== 'string' || moduleKey === '') {
			return;
		}

		// 找到对应模块项
		const findModuleItem = formTplData.find(({ key }) => key === moduleKey);

		if (findModuleItem) {
			const { id = undefined, type, optionInfo = [] } = findModuleItem;
			const fieldItemData = {
				key: getRandomNum(),
				id: undefined,
				pieceId: id,
				pieceOptionKey: '',
				cnName,
				fieldName,
				fieldPrompt,
				fieldType,
				fieldOption,
				fieldTemplate,
				fieldCheck,
				delFlag: 0,
			};

			// 选项类处理
			if (type === 2 && optionIndex !== '' && optionInfo.length && optionInfo[optionIndex]) {
				fieldItemData.pieceOptionKey = optionInfo[optionIndex].key;
			}

			findModuleItem.fields.push(fieldItemData);

			setFormTplData([...formTplData]);
		}
	};

	// 删除表单字段
	const delFieldItem = (fieldItemData, moduleKey) => {
		if (fieldItemData.id) {
			// 有id 将 '本身' 的 delFlag 修改为 1
			fieldItemData.delFlag = 1;
		} else {
			// 无id 直接 根据 key 删除

			// 找到对应模块项
			const { fields = [] } = formTplData.find(({ key }) => key === moduleKey) || {};

			if (fields.length) {
				// 找到对应表单字段 index
				const index = fields.findIndex(({ key }) => key === fieldItemData.key);
				if (index > -1) {
					fields.splice(index, 1);
				}
			}
		}
		console.log(formTplData);
		setFormTplData([...formTplData]);
	};
	const moveFieldItem = (fieldItemData, moduleKey, flag) => {
		// 找到对应模块项
		const { fields = [] } = formTplData.find(({ key }) => key === moduleKey) || {};
		if (fields.length) {
			// 找到对应表单字段 index
			const index = fields.findIndex(({ key }) => key === fieldItemData.key);
			let item = null;
			if (index > -1) {
				item = fields.splice(index, 1);
			}
			if (item) {
				if (flag == 'down') {
					fields.splice(index + 1, 0, item[0]);
				} else {
					fields.splice(index - 1, 0, item[0]);
				}
			}
		}
		setFormTplData([...formTplData]);
	};

	// 表单字段 弹窗确认回调
	const modalFieldSubmitCallback = (values) => {
		// fieldName 保持唯一
		if (
			formTplData
				.map(({ fields }) => fields.filter(({ key }) => values.key !== key).map(({ fieldName }) => fieldName))
				.flat(2)
				.includes(values.fieldName)
		) {
			message.error('字段名称（英文）重复、请重新编辑');
			return;
		}
		if (values.key) {
			// 更新
			// 找到对应模块项
			const { fields = [] } = formTplData.find(({ key }) => key === fieldData.moduleKey) || {};
			// 找到对应字段 index
			const index = fields.findIndex(({ key }) => key === values.key);
			if (index > -1) {
				fields[index] = {
					...fields[index],
					...values,
				};
			}
			setFormTplData([...formTplData]);
		} else {
			// 新增
			addFieldItem(values, fieldData.moduleKey, fieldData.optionIndex);
		}
		setFieldOpen(false);
	};

	// 提交
	const submit = (isNext) => {
		const params = {
			id: templateId,
			pieces: formTplData.map(({ id: pieceId, name, type, optionInfo, delFlag, fields }, index) => {
				return {
					id: pieceId,
					templateId,
					name,
					type,
					optionInfo: JSON.stringify(optionInfo),
					sort: index + 1,
					delFlag,
					fields: fields.map(
						({ id, pieceOptionKey, cnName, fieldName, fieldPrompt, fieldType, fieldOption, fieldTemplate, fieldCheck, delFlag }, key) => {
							return {
								id,
								pieceId,
								pieceOptionKey,
								cnName,
								fieldName,
								fieldPrompt,
								fieldType,
								fieldOption: JSON.stringify(fieldOption),
								fieldTemplate,
								fieldCheck: JSON.stringify(fieldCheck),
								sort: key + 1,
								delFlag,
							};
						}
					),
				};
			}),
		};

		updateFormTemplate(params).then(() => {
			message
				.success({
					content: '保存成功',
					duration: 1.5,
				})
				.then(() => {
					if (isNext) {
						props.setCurrent && props.setCurrent(2);
					} else {
						onInit();
					}
				});
		});
	};

	const onInit = () => {
		if (templateId) {
			getFormTemplateDetail({ id: templateId }).then((res) => {
				if (res.data && res.data.pieces && res.data.pieces.length > 0) {
					const formTplData = res.data.pieces.map((ov) => {
						const { id, optionInfo, fields } = ov;
						return {
							...ov,
							key: id,
							optionInfo: JSON.parse(optionInfo),
							fields: [...(fields || [])]
								.sort((a, b) => a.sort - b.sort)
								.map((field) => {
									const { id, fieldCheck, fieldOption } = field;
									return {
										...field,
										key: id,
										fieldCheck: JSON.parse(fieldCheck),
										fieldOption: JSON.parse(fieldOption),
										delFlag: 0,
									};
								}),
							delFlag: 0,
						};
					});
					setFormTplData(formTplData);
				} else {
					initFormTplData(addModuleItem, addFieldItem);
				}
			});
		} else {
			message.error({
				content: '模板不存在',
				duration: 1,
				onClose: () => {
					props.setCurrent && props.setCurrent(0);
				},
			});
		}
	};

	useEffect(() => {
		onInit();
	}, []);

	return (
		<div>
			<Form disabled={!!props.disabled}>
				{/* 动态报名表单 开始 */}
				{formTplData
					.filter(({ delFlag }) => delFlag === 0)
					.map((moduleData) => {
						return (
							<div key={moduleData.id || moduleData.key}>
								{/* 模块标题 开始 */}
								<ModuleTitle
									disabled={props.disabled}
									title={moduleData.name}
									onDel={() => delModuleItem(moduleData)}
									onEdit={() => {
										setModuleData(moduleData);
										setModuleOpen(true);
									}}
								/>
								{/* 模块标题 结束 */}

								{/* type 1 基础 2 选项   为 1 时默认 optionInfo 为 [''] 统一处理 */}
								{/* 选项块 开始 */}
								{(moduleData.type === 1 ? [{ key: 1, val: '' }] : moduleData.optionInfo).map((option, index) => {
									const moduleDataFields = (moduleData.fields || []).filter(
										({ pieceOptionKey, delFlag }) =>
											delFlag === 0 && (moduleData.type === 1 || (moduleData.type === 2 && pieceOptionKey === option.key))
									);
									return (
										<div
											key={option.key}
											className={`margin-bottom-20 ${
												moduleData.type === 2 && 'padding-12 border-radius-8 border-dashed-165dff'
											}`}
										>
											{option.val && <div className="margin-bottom-12 color-165dff">{option.val}</div>}

											<Space className="width-100per" direction="vertical" size={20}>
												{/* 表单字段Item 开始 */}
												{moduleDataFields.map((field, fi) => {
													return (
														<FieldItem
															{...field}
															disabled={props.disabled}
															fi={fi}
															fieldsLength={moduleDataFields.length}
															onEdit={() => {
																setFieldOpen(true);
																setFieldData({
																	...field,
																	moduleKey: moduleData.key,
																	optionIndex: index,
																});
															}}
															onDel={() => {
																delFieldItem(field, moduleData.key);
															}}
															onDown={() => {
																moveFieldItem(field, moduleData.key, 'down');
															}}
															onUp={() => {
																moveFieldItem(field, moduleData.key, 'up');
															}}
														/>
													);
												})}
												{/* 表单字段Item 结束 */}
												{/* 新增 表单字段Item 开始 */}
												{!props.disabled && (
													<div className="flex align-start">
														<div className="width-200"></div>
														<div className="flex-sub flex align-center min-height-36">
															<Button
																type="primary"
																size="small"
																ghost
																onClick={() => {
																	setFieldOpen(true);
																	setFieldData({
																		moduleKey: moduleData.key,
																		optionIndex: index,
																	});
																}}
															>
																新增字段
															</Button>
														</div>
													</div>
												)}
												{/* 新增 表单字段Item 结束 */}
											</Space>
										</div>
									);
								})}
								{/* 选项块 结束 */}
							</div>
						);
					})}
				{/* 动态报名表单 结束 */}
			</Form>

			{/* 底部固定按钮 开始 */}
			{!props.hideBtn && (
				<>
					<div className="width-100per height-36"></div>
					<div className="position-absolute bottom-0 left-0 flex align-center justify-between padding-tb-12 padding-lr-20 width-100per border-box border-top-e5e6e8">
						{!props.disabled ? (
							<Button
								type="primary"
								onClick={() => {
									setModuleData({});
									setModuleOpen(true);
								}}
							>
								添加模块
							</Button>
						) : (
							<div></div>
						)}

						<Space size={16}>
							<Button onClick={() => props.setCurrent(0)}>上一步</Button>
							{!props.disabled ? (
								<>
									<Button onClick={() => submit()}>保存</Button>
									<Button type="primary" onClick={() => submit(true)}>
										保存并下一步
									</Button>
								</>
							) : (
								<Button type="primary" onClick={() => props.setCurrent(2)}>
									下一步
								</Button>
							)}
						</Space>
					</div>
				</>
			)}
			{/* 底部固定按钮 结束 */}
			{/* 模块弹窗 开始 */}
			<ModalModuleForm
				open={moduleOpen}
				moduleData={moduleData}
				onCancel={() => {
					setModuleOpen(false);
				}}
				onOk={modalModuleSubmitCallback}
			/>
			{/* 模块弹窗 结束 */}
			{/* 字段弹窗 开始 */}
			<ModalFieldForm
				open={fieldOpen}
				fieldData={fieldData}
				onCancel={() => {
					setFieldOpen(false);
				}}
				onOk={modalFieldSubmitCallback}
			/>
			{/* 字段弹窗 结束 */}
		</div>
	);
};

// 模块标题
const ModuleTitle = (props = {}) => {
	return (
		<div className="flex align-center margin-bottom-20 padding-right-12 bg-color-f7f9fc">
			<div className="width-6 height-16 border-radius-4 bg-color-165dff"></div>
			<div className="flex-sub padding-lr-8 line-height-36 font-size-16 font-weight-500">{props.title}</div>
			{/* 基本信息 固定不能删除 */}
			{!props.disabled && (
				<Space>
					<div className="color-165dff cursor-pointer" onClick={props.onEdit}>
						编辑
					</div>
					{props.title !== '基本信息' && (
						<Popconfirm
							icon={false}
							description="是否确认删除此模块?"
							onConfirm={() => {
								props.onDel();
							}}
							okText="确认"
							cancelText="取消"
						>
							<div className="color-165dff cursor-pointer">删除</div>
						</Popconfirm>
					)}
				</Space>
			)}
		</div>
	);
};

// 模块弹窗
const ModalModuleForm = (props = {}) => {
	const { key = '' } = props.moduleData;
	const [form] = Form.useForm();

	// 监听 form 选项类型
	const type = Form.useWatch('type', form);
	const optionInfo = Form.useWatch('optionInfo', form);

	// 选项文本
	const [optionName, setOptionName] = useState('');

	// 新增选项
	const addOption = () => {
		const optionItem = {
			key: getRandomNum(),
			val: optionName,
		};
		if (optionInfo) {
			optionInfo.push(optionItem);
			form.setFieldValue('optionInfo', [...optionInfo]);
		} else {
			form.setFieldValue('optionInfo', [optionItem]);
		}
		form.validateFields(['optionInfo']);
	};

	// 删除选项
	const delOption = (key) => {
		const newOption = optionInfo.filter((ov) => ov.key !== key);
		form.setFieldValue('optionInfo', newOption);
		form.validateFields(['optionInfo']);
	};

	//  修改选项
	const changeOption = (value, index) => {
		const newOption = [...optionInfo];
		newOption[index].val = value;
		form.setFieldValue('optionInfo', newOption);
		form.validateFields(['optionInfo']);
	};

	// 校验选项
	const validOption = (_, value) => {
		if (value && value.some((ov) => !ov.val)) {
			return Promise.reject('请输入选项名称');
		}
		return Promise.resolve();
	};

	// 提交
	const submit = () => {
		form.validateFields().then((values) => {
			props.onOk &&
				props.onOk({
					key,
					...values,
				});
			form.resetFields();
		});
	};

	useEffect(() => {
		if (form && key && props.open) {
			form.setFieldsValue(props.moduleData);
		} else {
			form.resetFields();
		}
	}, [props.moduleData]);

	return (
		<Modal
			open={props.open || false}
			title={key ? '编辑模块' : '新增模块'}
			centered
			onCancel={() => props.onCancel && props.onCancel()}
			onOk={submit}
		>
			<Form
				form={form}
				labelCol={{
					style: {
						width: '100px',
					},
				}}
				initialValues={{
					type: 1,
				}}
			>
				<Form.Item label="模块名称" name="name" rules={[{ required: true, message: '请输入模块名称' }]}>
					<Input placeholder="请输入模块名称" />
				</Form.Item>
				<Form.Item label="模块类型" name="type" rules={[{ required: true, message: '请选择模块类型' }]}>
					<Radio.Group
						disabled={!!key}
						options={[
							{ value: 1, label: '基础类' },
							{ value: 2, label: '选项类' },
						]}
					/>
				</Form.Item>
				{type === 2 && (
					<Form.Item
						label="选项"
						name="optionInfo"
						rules={[
							{
								required: true,
								type: 'array',
								message: '请添加选项',
							},
							{
								validator: validOption,
							},
						]}
					>
						<Form.Item noStyle>
							<Space className="width-100per" direction="vertical">
								<Input
									value={optionName}
									placeholder="请输入选项名称, 回车或点击按钮新增选项"
									suffix={
										<Button type="primary" size="small" onClick={addOption}>
											新增
										</Button>
									}
									onPressEnter={addOption}
									onChange={(e) => {
										setOptionName(e.target.value);
									}}
								/>
								<Space wrap={true}>
									{(optionInfo || []).map((option, index) => {
										return (
											<Input
												key={option.key}
												value={option.val}
												suffix={
													<CloseOutlined
														className=" font-size-12 hover-color-165dff cursor-pointer"
														onClick={() => {
															delOption(option.key);
														}}
													/>
												}
												onChange={(e) => changeOption(e.target.value, index)}
											/>
										);
									})}
								</Space>
							</Space>
						</Form.Item>
					</Form.Item>
				)}
			</Form>
		</Modal>
	);
};

// 表单字段
const FieldItem = (props = {}) => {
	const required = props.fieldCheck.find(({ check }) => check === 'required');
	return (
		<div className="flex align-start">
			<div className="flex align-center justify-end margin-right-4 width-200 line-height-36 font-size-14 font-weight-500">
				{required && <div className="color-f53f3f">*</div>}
				<div className="color-4e5969">{props.cnName}：</div>
			</div>
			<div className="flex-sub flex align-center min-height-36">
				<FieldItemType {...props} />
			</div>
			{!props.disabled && (
				<Space className="padding-lr-16 width-76 line-height-36 color-165dff" size={16}>
					<EditOutlined
						className="cursor-pointer"
						title="编辑"
						onClick={() => {
							props.onEdit && props.onEdit();
						}}
					/>
					{!['name', 'creditCode'].includes(props.fieldName) ? (
						<Popconfirm
							title="提示"
							description="请确认是否删除该字段？"
							onConfirm={() => {
								props.onDel && props.onDel();
							}}
							okText="确定"
							cancelText="取消"
						>
							<CloseCircleOutlined className="cursor-pointer" title="删除" />
						</Popconfirm>
					) : (
						<div className="width-14"></div>
					)}

					{props.fi == 0 ? (
						<div className="width-14"></div>
					) : (
						<ArrowUpOutlined
							className="cursor-pointer"
							title="上移"
							onClick={() => {
								props.onEdit && props.onUp();
							}}
						/>
					)}
					{props.fieldsLength > 1 && props.fi < props.fieldsLength - 1 ? (
						<ArrowDownOutlined
							className="cursor-pointer"
							title="下移"
							onClick={() => {
								props.onEdit && props.onDown();
							}}
						/>
					) : (
						<div className="width-14"></div>
					)}
				</Space>
			)}
		</div>
	);
};

// 表单字段类型
const FieldItemType = (props = {}) => {
	//  选项格式
	const optionFormat = (optionData) => {
		return optionData.map(({ val, key, value }) => {
			if (value) {
				return {
					label: (
						<Space>
							<div>{val}</div>
							<Input className="width-90" size="small" placeholder="请填写" />
						</Space>
					),
					value: key,
				};
			} else {
				return { label: val, value: key };
			}
		});
	};

	return (
		<>
			{/* 单选 不固定 */}
			{props.fieldType === 1 && !fixedName.includes(props.fieldName) && (
				<Radio.Group>
					{optionFormat(props.fieldOption).map((ov) => {
						return (
							<Radio key={ov.value} value={ov.value}>
								<div className="line-height-36">{ov.label}</div>
							</Radio>
						);
					})}
				</Radio.Group>
			)}
			{/* 单选 固定 */}
			{props.fieldType === 1 && fixedName.includes(props.fieldName) && (
				<Select className="width-100per" placeholder={props.fieldPrompt || `请输入${props.cnName}`} />
			)}
			{/* 复选 */}
			{props.fieldType === 2 && (
				<Checkbox.Group>
					{optionFormat(props.fieldOption).map((ov) => {
						return (
							<Checkbox key={ov.value} value={ov.value}>
								<div className="line-height-36">{ov.label}</div>
							</Checkbox>
						);
					})}
				</Checkbox.Group>
			)}
			{/* 文本 */}
			{props.fieldType === 3 && (
				<Input
					className="padding-lr-12 padding-tb-6 line-height-22 font-size-14 color-1d2129"
					placeholder={props.fieldPrompt || `请输入${props.cnName}`}
				/>
			)}
			{/* 文本域 */}
			{props.fieldType === 4 && (
				<Input.TextArea
					rows={3}
					className="padding-lr-12 padding-tb-6 line-height-22 font-size-14 color-1d2129"
					placeholder={props.fieldPrompt || `请输入${props.cnName}`}
				/>
			)}
			{/* 上传 */}
			{props.fieldType === 5 && (
				<div className="img-fileup-box flex">
					<div
						className="fileup position-relative border-radius-4 overflow-hidden"
						style={{
							border: 'dashed 1px #165DFF',
							width: `188px`,
							height: `90px`,
						}}
					>
						<div className="position-absolute inset-0 flex flex-direction-column align-center justify-center cursor-pointer">
							<img className="width-60 height-60" src={getImageSrc('@/assets/images/Public/fileup-icon.png')} />
							<div>上传文件</div>
						</div>
					</div>
					<div className="flex-sub flex flex-direction-column justify-center align-start margin-left-20 line-height-22 color-86909c">
						<div>{props.fieldPrompt}</div>
					</div>
				</div>
			)}
			{/* 日期 */}
			{props.fieldType === 6 && <DatePicker className="width-220" placeholder={props.fieldPrompt || `请选择${props.cnName}`} />}
			{/* 区域 */}
			{props.fieldType === 7 && <Cascader className="width-220" options={[]} placeholder={props.fieldPrompt || `请选择${props.cnName}`} />}
		</>
	);
};

// 表单字段弹窗
const ModalFieldForm = (props = {}) => {
	const { key = '' } = props.fieldData;
	const [form] = Form.useForm();

	// 是否禁止编辑条件 固定2个名称禁止修改条件
	const isDisabled = ['name', 'creditCode'].includes(props.fieldData.fieldName);

	// 监听 form 选项类型
	const fieldType = Form.useWatch('fieldType', form);
	const fieldName = Form.useWatch('fieldName', form);
	const fieldOption = Form.useWatch('fieldOption', form);
	const fieldCheck = Form.useWatch('fieldCheck', form);
	const fieldTemplate = Form.useWatch('fieldTemplate', form);

	// 限制条件
	const [limitRegValue, setLimitRegValue] = useState('number');
	const [length, setLength] = useState(20);
	const [fileSize, setFileSize] = useState(10);
	const [fileType, setFileType] = useState('');
	const [domainText, setDomainText] = useState('');

	// 选项文本
	const [optionName, setOptionName] = useState('');

	// 新增选项
	const addOption = () => {
		const optionItem = {
			key: getRandomNum(),
			val: optionName,
		};
		if (fieldOption) {
			fieldOption.push(optionItem);
			updateOption();
		} else {
			updateOption([optionItem]);
		}

		setOptionName('');
	};

	// 删除选项
	const delOption = (key) => {
		const newOption = fieldOption.filter((ov) => ov.key !== key);
		updateOption(newOption);
	};

	// 更新选项
	const updateOption = (val = null) => {
		if (val) {
			form.setFieldValue('fieldOption', val);
		} else {
			form.setFieldValue('fieldOption', [...fieldOption]);
		}
		form.validateFields(['fieldOption']);
	};

	// 上传
	const upload = (file) => {
		const formData = new FormData();
		formData.append('files', file);

		fileUpload(formData).then((res) => {
			form.setFieldValue('fieldTemplate', res[0]);
		});
		return false;
	};

	// 黑白名单互斥
	const whiteOrBlack = ({ target: { checked, value } }) => {
		if (checked) {
			setTimeout(() => {
				const fieldCheck = (form.getFieldValue('fieldCheck') || []).filter((ov) => !['data-domain-white', 'data-domain-black'].includes(ov));

				fieldCheck.push(value);

				form.setFieldValue('fieldCheck', fieldCheck);
			}, 10);
		}
	};

	// 提交
	const submit = () => {
		form.validateFields().then((values) => {
			props.onOk &&
				props.onOk({
					...values,
					key,
					fieldCheck: handleLimitData(values.fieldCheck, {
						length,
						fileSize,
						fileType,
						domainText,
					}),
				});
			resetForm();
		});
	};

	// 重置表单
	const resetForm = () => {
		form.resetFields();
		setLength(20);
		setFileSize(10);
		setFileType('');
		setDomainText('');
	};

	useEffect(() => {
		if (form && key && props.open) {
			const fieldCheck = [];
			(props.fieldData.fieldCheck || []).forEach(({ check, limitValue }) => {
				if (check !== 'file') {
					fieldCheck.push(check);
				}

				if (['data-domain-white', 'data-domain-black'].includes(check)) {
					// 黑白名单
					setDomainText(limitValue);
				} else if (check === 'length') {
					// 限制长度
					setLength(limitValue);
				} else if (check === 'file') {
					// 文件特别处理
					const fileArr = limitValue.split(';');
					if (fileArr[0]) {
						fieldCheck.push('file-1');
						setFileType(fileArr[0]);
					}
					if (fileArr[1]) {
						fieldCheck.push('file-2');
						setFileSize(fileArr[1] - 0);
					}
				} else if (['number', 'email', 'cn-phone', 'cn-idcard'].includes(check)) {
					// 正则特别处理
					setLimitRegValue(check);
				}
			});
			form.setFieldsValue({
				...props.fieldData,
				fieldCheck,
			});
		} else {
			setLimitRegValue('number');
			resetForm();
		}
	}, [key, props.open]);

	return (
		<Modal
			open={props.open}
			title={key ? '编辑字段' : '新增字段'}
			centered
			onCancel={() => props.onCancel && props.onCancel()}
			onOk={submit}
			width={780}
			styles={{
				body: {
					maxHeight: '75vh',
					paddingRight: '20px',
					overflowY: 'auto',
					overflowX: 'hidden',
				},
			}}
		>
			<Form
				form={form}
				labelCol={{
					style: {
						width: '160px',
					},
				}}
				initialValues={{
					fieldType: 3,
				}}
			>
				<Form.Item label="字段名称（中文）" name="cnName" rules={[{ required: true, message: '请输入字段名称' }]}>
					<Input placeholder="请输入字段名称" />
				</Form.Item>
				<Form.Item label="字段名称（英文）" name="fieldName" rules={[{ required: true, message: '请输入字段名称' }]}>
					<Input
						placeholder="请输入字段的英文名称，名称唯一且不能带空格，如：fieldName"
						disabled={isDisabled || (fieldType === 1 && fixedName.includes(fieldName))}
					/>
				</Form.Item>
				<Form.Item
					label="填写提示"
					name="fieldPrompt"
					rules={[
						{
							required: fieldType === 5,
							message: '请输入填写提示',
						},
					]}
				>
					<Input placeholder="请输入填写提示" />
				</Form.Item>
				<Form.Item label="字段类型" name="fieldType" rules={[{ required: true, message: '请选择字段类型' }]}>
					<Radio.Group disabled={isDisabled}>
						{['单选', '多选', '单行文本', '多行文本', '附件上传', '日期选择', '区域选择'].map((ov, oi) => {
							return (
								<Radio key={oi + 1} value={oi + 1}>
									<div className="width-80 line-height-32">{ov}</div>
								</Radio>
							);
						})}
					</Radio.Group>
				</Form.Item>
				{((fieldType === 1 && !fixedName.includes(fieldName)) || fieldType === 2) && (
					<Form.Item
						label="字段选项"
						name="fieldOption"
						rules={[
							{
								required: true,
								type: 'array',
								message: '请添加选项',
							},
						]}
					>
						<Form.Item noStyle>
							<Space className="width-100per" direction="vertical">
								<Input
									value={optionName}
									placeholder="请输入选项名称, 回车或点击按钮新增选项"
									suffix={
										<Button type="primary" size="small" onClick={addOption} disabled={isDisabled}>
											新增
										</Button>
									}
									onPressEnter={addOption}
									onChange={(e) => {
										setOptionName(e.target.value);
									}}
									disabled={isDisabled}
								/>
								<Space direction="vertical">
									{(fieldOption || []).map((option) => {
										return (
											<Space key={option.key} className="flex align-center">
												<div
													className="padding-lr-8 width-80 text-cut line-height-24 border-radius-4 bg-color-e6e6e6"
													title={option.val}
												>
													{option.val}
												</div>
												<Checkbox
													checked={option.value}
													onChange={(e) => {
														option.value = e.target.checked;
														updateOption();
													}}
												>
													填空
												</Checkbox>
												{option.value && (
													<Checkbox
														checked={option.required}
														onChange={(e) => {
															option.required = e.target.checked;
															updateOption();
														}}
													>
														填空必填
													</Checkbox>
												)}
												<DeleteOutlined
													style={{
														color: '#165dff',
														cursor: 'pointer',
													}}
													onClick={() => {
														delOption(option.key);
													}}
												/>
											</Space>
										);
									})}
								</Space>
							</Space>
						</Form.Item>
					</Form.Item>
				)}
				<Form.Item label="填写设置" name="fieldCheck">
					<Checkbox.Group className="width-100per line-height-36">
						<Row>
							<Col span={24}>
								<Checkbox value="required" disabled={isDisabled}>
									必填
								</Checkbox>
							</Col>
							{[3, 4].includes(fieldType) && (
								<Col span={24}>
									<Checkbox value="length" disabled={isDisabled}>
										限制字数
									</Checkbox>
									<Form.Item noStyle>
										<InputNumber
											value={length}
											className="width-90"
											size="small"
											placeholder="请输入"
											disabled={!(fieldCheck || []).includes('length') || isDisabled}
											suffix="字"
											onBlur={(e) => {
												const value = e.target.value - 0;
												setLength(value >= 1 ? value : 1);
											}}
										/>
									</Form.Item>
								</Col>
							)}
							{fieldType === 3 && (
								<Col span={24}>
									<Checkbox value={limitRegValue} disabled={isDisabled}>
										使用正则校验
									</Checkbox>
									<Form.Item noStyle>
										<Radio.Group
											value={limitRegValue}
											className="margin-left-24 line-height-24 width-100per"
											disabled={!(fieldCheck || []).some((ov) => ['number', 'email', 'cn-phone', 'cn-idcard'].includes(ov))}
											onChange={(e) => {
												const value = e.target.value;
												form.setFieldValue(
													'fieldCheck',
													(fieldCheck || []).join(',').replace(limitRegValue, value).split(',')
												);
												setLimitRegValue(value);
											}}
										>
											<Radio value="number">数字</Radio>
											<Radio value="email">邮箱</Radio>
											<Radio value="cn-phone">中国大陆手机号</Radio>
											<Radio value="cn-idcard">中国大陆身份证</Radio>
										</Radio.Group>
									</Form.Item>
								</Col>
							)}
							{fieldType === 5 && (
								<>
									<Col span={24}>
										<Checkbox value="file-1">限制上传类型</Checkbox>
										<Form.Item noStyle>
											<FileType value={fileType} onChange={setFileType} disabled={!(fieldCheck || []).includes('file-1')} />
										</Form.Item>
									</Col>
									<Col span={24}>
										<Checkbox value="file-2">限制上传大小</Checkbox>
										<InputNumber
											disabled={!(fieldCheck || []).includes('file-2')}
											value={fileSize}
											className="width-90"
											size="small"
											placeholder="请输入"
											suffix="M"
											onBlur={(e) => {
												const value = e.target.value;
												setFileSize(value >= 1 ? value : 1);
											}}
										/>
									</Col>
								</>
							)}
							{fieldType === 3 && (
								<>
									<Col span={24}>
										<Checkbox value="data-domain-white" onChange={whiteOrBlack}>
											数据白名单
										</Checkbox>
									</Col>
									{(fieldCheck || []).includes('data-domain-white') && (
										<Col span={24}>
											<div className="margin-left-24">
												<Form.Item noStyle>
													<Input.TextArea
														value={domainText}
														rows={4}
														placeholder="请复制表格数据粘贴或按分号分隔"
														onChange={(e) => {
															setDomainText(e.target.value);
														}}
													/>
												</Form.Item>
											</div>
										</Col>
									)}
									<Col span={24}>
										<Checkbox value="data-domain-black" onChange={whiteOrBlack}>
											数据黑名单
										</Checkbox>
									</Col>
									{(fieldCheck || []).includes('data-domain-black') && (
										<Col span={24}>
											<div className="margin-left-24">
												<Form.Item noStyle>
													<Input.TextArea
														value={domainText}
														rows={4}
														placeholder="请复制表格数据粘贴或按分号分隔"
														onChange={(e) => {
															setDomainText(e.target.value);
														}}
													/>
												</Form.Item>
											</div>
										</Col>
									)}
								</>
							)}
						</Row>
					</Checkbox.Group>
				</Form.Item>
				{fieldType === 5 && (
					<Form.Item label="模版文件" name="fieldTemplate">
						<Space direction="vertical">
							<Space size={20}>
								<Upload
									maxCount={1}
									accept=".xls, .xlsx, application/vnd.openxmlformats-officedocument.spreadsheetml.sheet, application/vnd.ms-excel"
									beforeUpload={upload}
									fileList={[]}
								>
									<Button type="primary" icon={<CloudUploadOutlined />} ghost>
										上传文件
									</Button>
								</Upload>
								{fieldTemplate && <div className="font-size-14 color-165dff">已上传</div>}
								<div className="color-86909c">字段类型为“附件上传”并且需要限定规范模版时上传</div>
							</Space>
						</Space>
					</Form.Item>
				)}
				{fieldType === 1 && (
					<Form.Item label="固定数据" initialValue={''}>
						<div>
							<Select
								value={fixedName.includes(fieldName) ? fieldName : ''}
								options={[
									{
										label: '不固定',
										value: '',
									},
									{
										label: '行业',
										value: 'industryId',
									},
									{
										label: '组别',
										value: 'projectGroupId',
									},
								]}
								allowClear
								onChange={(e) => {
									form.setFieldValue('fieldName', e);
									form.setFieldValue('fieldOption', []);
								}}
								placeholder="请选择固定数据类型"
							/>
						</div>
					</Form.Item>
				)}
			</Form>
		</Modal>
	);
};

//  文件类型
const FileType = (props = {}) => {
	const [fileList, setFieldList] = useState(getFileTypeData());

	const checkedFile = (checkedList, index) => {
		fileList.forEach((ov, oi) => {
			ov.checkedList = oi === index ? checkedList : [];
		});
		setFieldList([...fileList]);
		props.onChange(checkedList.join(','));
	};

	useEffect(() => {
		if (props.value) {
			const checkList = props.value.split(',');
			const index = fileList.findIndex(({ list }) => list.some(({ name }) => checkList.includes(name)));
			if (index > -1) {
				checkedFile(checkList, index);
			}
		}
	}, [props.value]);

	return (
		<div className="width-100per">
			{fileList.map((fileType, index) => {
				return (
					<Checkbox.Group disabled={props.disabled} key={fileType.id} value={fileType.checkedList} onChange={(e) => checkedFile(e, index)}>
						<div className="flex align-start">
							<div className="margin-right-12 width-64 text-align-right color-86909c">{fileType.name}：</div>
							<Space className="flex-sub" wrap={true} size={0}>
								{fileType.list.map((ov) => (
									<div className="width-100" key={ov.id} span={6}>
										<Checkbox value={ov.name}>{ov.name}</Checkbox>
									</div>
								))}
							</Space>
						</div>
					</Checkbox.Group>
				);
			})}
		</div>
	);
};

export default memo(Index);
