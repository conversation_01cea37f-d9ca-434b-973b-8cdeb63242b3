import { Button, Table, Popconfirm, Input, Modal, Form, Switch, Tag, Radio, Select, Space, message, Checkbox } from 'antd';
import { useEffect, useRef, useState, forwardRef, useImperativeHandle } from 'react';
import { entryJudgesPage } from '@/api/Competition/UserManage/judges';
import { saveActivityRatingConfig, getActivityRatingConfig, getFormTemplateDetail } from '@/api/Competition/CompetitionManage/index';

// 评分项
const RatingItemsModal = forwardRef((props = {}, ref) => {
	const [isModalOpen, setIsModalOpen] = useState(false);
	const [curRow, setCurRow] = useState({});

	const [form] = Form.useForm();
	const formRef = useRef();

	const submit = () => {
		form.validateFields().then((values) => {
			const params = {
				id: curRow.id || '',
				activityId: curRow.activityId || '',
				name: values.name || '',
				fullScore: values.fullScore || '',
				prompt: values.prompt || '',
				// delFlag: 0,
			};
			const fullScoreTotal = props.tableList.reduce((pre, cur) => {
				return (pre += cur.fullScore - 0);
			}, 0);
			if (fullScoreTotal + (values.fullScore - 0) > 100) {
				message.warning('评分项分数总和须等于总分(100分)');
				return;
			}
			props.onChange && props.onChange(params);
			reset();
		});
	};

	const reset = () => {
		formRef.current && formRef.current.resetFields && formRef.current.resetFields();
		setIsModalOpen(false);
		setCurRow({});
	};
	useEffect(() => {
		if (isModalOpen) {
			formRef.current.setFieldValue('name', curRow.name || '');
			formRef.current.setFieldValue('fullScore', curRow.fullScore || '');
			formRef.current.setFieldValue('prompt', curRow.prompt || '');
		}
	}, [isModalOpen]);

	useImperativeHandle(ref, () => {
		return {
			open: (data = {}) => {
				setCurRow(data);
				setIsModalOpen(true);
			},
			close: () => {
				setIsModalOpen(false);
			},
		};
	});

	return (
		<Modal
			title={`评分项`}
			open={isModalOpen}
			maskClosable={false}
			onOk={() => {
				submit();
			}}
			onCancel={() => {
				reset();
			}}
			width={700}
		>
			<Form
				labelCol={{
					span: 6,
				}}
				wrapperCol={{
					span: 18,
				}}
				autoComplete="off"
				ref={formRef}
				form={form}
			>
				<Form.Item
					name="name"
					label="评分项"
					rules={[
						{
							required: true,
						},
					]}
				>
					<Input placeholder="请输入评分项" allowClear />
				</Form.Item>
				<Form.Item
					name="fullScore"
					label="满分"
					rules={[
						{
							required: true,
						},
					]}
				>
					<Input placeholder="请输入满分" allowClear />
				</Form.Item>
				<Form.Item name="prompt" label="评分提示" rules={[]}>
					<Input.TextArea
						autoSize={{
							minRows: 1,
						}}
						placeholder="请输入评分提示"
						allowClear
					/>
				</Form.Item>
			</Form>
		</Modal>
	);
});

// 评审组
const RatingGroupsModal = forwardRef((props = { ratingPersonsOptions: [] }, ref) => {
	const [isModalOpen, setIsModalOpen] = useState(false);
	const [curRow, setCurRow] = useState({});

	const [form] = Form.useForm();
	const formRef = useRef();

	const submit = () => {
		form.validateFields().then((values) => {
			const params = {
				id: curRow.id || '',
				activityId: curRow.activityId || '',
				alias: curRow.alias || '',
				name: values.name || '',
				ratingPerson: (values.ratingPerson || []).map((key) => {
					const find = props.ratingPersonsOptions.find((ov) => key == ov.userId);
					return {
						name: find.name || '',
						userId: find.userId || '',
					};
				}),
			};

			props.onChange &&
				props.onChange({
					...params,
					rowKey: curRow.rowKey || JSON.stringify(params),
				});
			reset();
		});
	};

	const reset = () => {
		formRef.current && formRef.current.resetFields && formRef.current.resetFields();
		setIsModalOpen(false);
		setCurRow({});
	};
	useEffect(() => {
		if (isModalOpen) {
			formRef.current.setFieldValue('name', curRow.name || '');
			formRef.current.setFieldValue('ratingPerson', (curRow.ratingPerson || []).map((ov) => ov.userId) || []);
		}
	}, [isModalOpen]);

	useImperativeHandle(ref, () => {
		return {
			open: (data = {}) => {
				setCurRow(data);
				setIsModalOpen(true);
			},
			close: () => {
				setIsModalOpen(false);
			},
		};
	});

	return (
		<Modal
			title={`评审组`}
			open={isModalOpen}
			maskClosable={false}
			onOk={() => {
				submit();
			}}
			onCancel={() => {
				reset();
			}}
			width={700}
		>
			<Form
				labelCol={{
					span: 4,
				}}
				wrapperCol={{
					span: 20,
				}}
				autoComplete="off"
				ref={formRef}
				form={form}
			>
				<Form.Item
					name="name"
					label="评审组名称"
					rules={[
						{
							required: true,
						},
					]}
				>
					<Input placeholder="请输入评审组名称" allowClear />
				</Form.Item>
				<Form.Item
					name="ratingPerson"
					label="评委"
					rules={[
						{
							required: true,
						},
					]}
				>
					<Select
						mode="multiple"
						allowClear
						style={{
							width: '100%',
						}}
						placeholder="请输入评委名称"
						options={props.ratingPersonsOptions || []}
						fieldNames={{ label: 'name', value: 'userId' }}
					/>
				</Form.Item>
			</Form>
		</Modal>
	);
});
const Index = (props = {}) => {
	const [dataOrigin, setDataOrigin] = useState({});

	const [calculationMethod, setCalculationMethod] = useState(1);

	const [judgesList, setJudgesList] = useState([]);
	useEffect(() => {
		entryJudgesPage({
			pageNum: 1,
			pageSize: 2000,
		}).then((res) => {
			setJudgesList(
				(res.data.records || []).map((ov) => {
					return {
						userId: ov.userId,
						name: ov.name,
					};
				})
			);
		});
	}, []);
	const fileType = ['mp4', 'pdf', 'ppt/pptx', 'mov', 'avi'];
	const [attachedShowConfCheckBoxList, setAttachedShowConfCheckBoxList] = useState([]);
	const [signUpFormShowConfCheckBoxList, setSignUpFormShowConfCheckBoxList] = useState([]);
	const [signUpFormShowConf, setSignUpFormShowConf] = useState([]);
	const [attachedShowConf, setAttachedShowConf] = useState([]);
	const [attachedListShowConf, setAttachedListShowConf] = useState([]);
	const [ratingPersonLogoShow, setRatingPersonLogoShow] = useState(0);
	const [ratingPersonNameShow, setRatingPersonNameShow] = useState(0);
	useEffect(() => {
		if (props.detailId) {
			console.log('🚀 ~ useEffect ~ props:', props.detail);
			const attachedShowConfCheckList = [];
			const formCheckList = [];
			getFormTemplateDetail({ id: props.templateId }).then((res) => {
				if (res.data && res.data.pieces && res.data.pieces.length > 0) {
					res.data.pieces
						.map((ov) => {
							ov.fields = [...(ov.fields || [])].sort((a, b) => a.sort - b.sort);
							formCheckList.push({
								id: ov.id,
								name: ov.name,
							});
							return ov.fields;
						})
						.flat(2)
						.forEach((field) => {
							const { id, fieldCheck, fieldName, fieldType, cnName } = field;
							if (fieldType == 5 && fileType.some((type) => fieldCheck.includes(type))) {
								attachedShowConfCheckList.push({ id, fieldCheck, fieldName, fieldType, cnName });
							}
						});
				}
				setAttachedShowConfCheckBoxList(attachedShowConfCheckList);
				setSignUpFormShowConfCheckBoxList(formCheckList);
			});

			setRatingPersonLogoShow(props.detail.ratingPersonLogoShow || 0);
			setRatingPersonNameShow(props.detail.ratingPersonNameShow || 0);
			if (props.detail.attachedShowConf) {
				if (typeof props.detail.attachedShowConf == 'string') {
					setAttachedShowConf(JSON.parse(props.detail.attachedShowConf).map((ov) => ov.fieldName));
				} else {
					setAttachedShowConf(props.detail.attachedShowConf.map((ov) => ov.fieldName));
				}
			}
			if (props.detail.signUpFormShowConf) {
				if (typeof props.detail.signUpFormShowConf == 'string') {
					setSignUpFormShowConf(JSON.parse(props.detail.signUpFormShowConf).map((ov) => ov.id));
				} else {
					setSignUpFormShowConf(props.detail.signUpFormShowConf.map((ov) => ov.id));
				}
			}
			if (props.detail.attachedListShowConf) {
				if (typeof props.detail.attachedListShowConf == 'string') {
					setAttachedListShowConf(JSON.parse(props.detail.attachedListShowConf).map((ov) => ov.fieldName));
				} else {
					setAttachedListShowConf(props.detail.attachedListShowConf.map((ov) => ov.fieldName));
				}
			}

			getActivityRatingConfig({ id: props.detailId }).then((res) => {
				setDataOrigin(res.data);
				setCalculationMethod(res.data.calculationMethod || 1);
				setRatingItems(res.data.ratingItems);
				setRatingPersons(res.data.ratingPersons);
				setRatingGroups(
					(res.data.ratingGroups || []).map((ov) => {
						return {
							...ov,
							ratingPerson: ov.ratingPerson || [],
							rowKey: JSON.stringify(ov),
						};
					})
				);
			});
		}
	}, [props.detailId]);

	const [ratingItems, setRatingItems] = useState([]);

	const [ratingPersons, setRatingPersons] = useState([]);
	// {
	//     id: 0,
	//     activityId: 0,
	//     userId: 0,
	//     name: '',
	//     alias: '',
	//     groupId: 0,
	//     delFlag: 0,
	// },
	const RatingItemsModalRef = useRef();

	const ratingPersonsChange = (value, option) => {
		setRatingPersons(
			[...(option || [])].map((ov) => {
				return {
					userId: ov.userId,
					name: ov.name,
				};
			})
		);
		// setCalculationMethod(key.target.value)
	};
	const [ratingGroups, setRatingGroups] = useState([]);

	const RatingGroupsModalRef = useRef();

	const submitForm = () => {
		const params = {
			activityId: props.detailId,
			calculationMethod,
			ratingItems: ratingItems.map((ov) => {
				const obj = {
					activityId: props.detailId,
					name: ov.name || '',
					fullScore: ov.fullScore - 0,
					prompt: ov.prompt || '',
				};
				if (ov.id) {
					obj.id = ov.id;
				}
				return obj;
			}),
			ratingPersons: ratingPersons.map((ov) => {
				const obj = {
					activityId: props.detailId,
					userId: ov.userId || '',
					name: ov.name,
				};
				if (ov.id) {
					obj.id = ov.id;
				}
				return obj;
			}),
			ratingGroups: ratingGroups.map((ov) => {
				const obj = {
					activityId: props.detailId,
					name: ov.name,
					ratingPerson: ov.ratingPerson,
				};
				if (ov.id) {
					obj.id = ov.id;
				}
				return obj;
			}),
			ratingPersonLogoShow,
			ratingPersonNameShow,
			attachedShowConf: JSON.stringify(attachedShowConfCheckBoxList.filter((ov) => attachedShowConf.includes(ov.fieldName))),
			attachedListShowConf: JSON.stringify(attachedShowConfCheckBoxList.filter((ov) => attachedListShowConf.includes(ov.fieldName))),
			signUpFormShowConf: JSON.stringify(signUpFormShowConfCheckBoxList.filter((ov) => signUpFormShowConf.includes(ov.id))),
		};
		const ratingItemsIds = ratingItems.filter((ov) => ov.id).map((ov) => ov.id);
		const ratingItemsFilter = dataOrigin.ratingItems
			.filter((ov) => {
				return !ratingItemsIds.includes(ov.id);
			})
			.map((ov) => {
				return {
					...ov,
					delFlag: 1,
				};
			});
		params.ratingItems = [...params.ratingItems, ...ratingItemsFilter];

		const ratingPersonsIds = ratingPersons.filter((ov) => ov.id).map((ov) => ov.id);
		const ratingPersonsFilter = dataOrigin.ratingPersons
			.filter((ov) => {
				return !ratingPersonsIds.includes(ov.id);
			})
			.map((ov) => {
				return {
					...ov,
					delFlag: 1,
				};
			});
		params.ratingPersons = [...params.ratingPersons, ...ratingPersonsFilter];

		const ratingGroupsIds = ratingGroups.filter((ov) => ov.id).map((ov) => ov.id);
		const ratingGroupsFilter = dataOrigin.ratingGroups
			.filter((ov) => {
				return !ratingGroupsIds.includes(ov.id);
			})
			.map((ov) => {
				return {
					...ov,
					delFlag: 1,
				};
			});
		params.ratingGroups = [...params.ratingGroups, ...ratingGroupsFilter];

		saveActivityRatingConfig(params).then((res) => {
			props.setCurrent && props.setCurrent(3);
		});
	};

	return (
		<div>
			<div className="flex align-center justify-start line-height-24 padding-tb-16">
				<div className="width-6 height-16 border-radius-4 bg-color-165dff"></div>
				<div className="font-size-16 font-weight-500 margin-left-8">评分项配置</div>

				{!props.disabled && (
					<div className="flex-sub flex align-center justify-end">
						<Button
							type="primary"
							onClick={() => {
								RatingItemsModalRef.current.open();
							}}
						>
							+ 添加评分项
						</Button>
					</div>
				)}
			</div>
			<RatingItemsModal
				ref={RatingItemsModalRef}
				tableList={ratingItems}
				onChange={(data = {}) => {
					setRatingItems([...ratingItems, { ...data }]);
				}}
			/>
			<div className="padding-left-40">
				<Table
					rowKey="id"
					dataSource={ratingItems}
					pagination={false}
					footer={
						ratingItems.length
							? () => {
									return (
										<div className="flex align-center justify-start">
											<div className="width-240">总分</div>
											<div className="width-120">
												{ratingItems.reduce((pre, cur) => {
													return (pre += cur.fullScore - 0);
												}, 0)}
											</div>
										</div>
									);
								}
							: null
					}
				>
					<Table.Column title="评分项" key="name" dataIndex="name" width={240} />
					<Table.Column title="满分" key="fullScore" dataIndex="fullScore" width={120} />
					<Table.Column
						title="评分提示"
						key="prompt"
						dataIndex="prompt"
						render={(prompt) => {
							return prompt || '请输入评分提示';
						}}
					/>
					{!props.disabled && (
						<Table.Column
							title="操作"
							key="option"
							dataIndex="option"
							width={220}
							render={(_, record, index) => {
								return (
									<Popconfirm
										title="提示"
										description="确定删除吗？"
										onConfirm={() => {
											setRatingItems(ratingItems.filter((ov, oi) => oi != index));
										}}
										okText="确定"
										cancelText="取消"
									>
										<Button type="link" danger size="small">
											删除
										</Button>
									</Popconfirm>
								);
							}}
						/>
					)}
				</Table>
			</div>

			<div className="padding-10"></div>

			<div className="flex align-center justify-start line-height-24 padding-tb-16">
				<div className="width-6 height-16 border-radius-4 bg-color-165dff"></div>
				<div className="font-size-16 font-weight-500 margin-left-8">评分计算方式</div>
			</div>

			<div className="padding-left-40">
				<Radio.Group
					onChange={(e) => {
						setCalculationMethod(e.target.value);
					}}
					value={calculationMethod}
					disabled={!!props.disabled}
				>
					<Space direction="vertical">
						<Radio value={1}>按照评委人数计算平均分</Radio>
						<Radio value={2}>去掉最高分与最低分后计算平均分</Radio>
					</Space>
				</Radio.Group>
			</div>

			<div className="padding-10"></div>

			<div className="flex align-center justify-start line-height-24 padding-tb-16">
				<div className="width-6 height-16 border-radius-4 bg-color-165dff"></div>
				<div className="font-size-16 font-weight-500 margin-left-8">评审组配置</div>
			</div>

			<div className="padding-left-40">
				<div className="flex align-center justify-start padding-tb-16">
					<div className="font-size-16 font-weight-500 margin-right-12">选择评委：</div>
					<div className="flex-sub">
						<Select
							mode="multiple"
							allowClear
							style={{
								width: '100%',
							}}
							placeholder="请输入评委名称"
							options={judgesList}
							value={ratingPersons.map((ov) => ov.userId)}
							fieldNames={{ label: 'name', value: 'userId' }}
							onChange={ratingPersonsChange}
							disabled={!!props.disabled}
						/>
					</div>
				</div>
				<div className="flex align-center justify-start padding-tb-16">
					<div className="font-size-16 font-weight-500 margin-right-12">评委头像是否展示在评分展示页：</div>
					<div className="flex-sub">
						<Radio.Group
							onChange={(e) => {
								setRatingPersonLogoShow(e.target.value);
							}}
							value={ratingPersonLogoShow}
							disabled={!!props.disabled}
						>
							<Radio value={1}>是</Radio>
							<Radio value={0}>否</Radio>
						</Radio.Group>
					</div>
				</div>
				<div className="flex align-center justify-start padding-tb-16">
					<div className="font-size-16 font-weight-500 margin-right-12">评委名字是否展示在评分展示页：</div>
					<div className="flex-sub">
						<Radio.Group
							onChange={(e) => {
								setRatingPersonNameShow(e.target.value);
							}}
							value={ratingPersonNameShow}
							disabled={!!props.disabled}
						>
							<Radio value={1}>是</Radio>
							<Radio value={0}>否</Radio>
						</Radio.Group>
					</div>
				</div>
				<div className="flex align-center justify-start padding-tb-16">
					<div className="font-size-16 font-weight-500 margin-right-12">展示在评分展示页的附件：</div>
					<div className="flex-sub">
						<Checkbox.Group
							onChange={(vals) => {
								setAttachedShowConf(vals);
							}}
							value={attachedShowConf}
							disabled={!!props.disabled}
						>
							{attachedShowConfCheckBoxList.map((ov) => {
								return (
									<Checkbox key={ov.fieldName} value={ov.fieldName}>
										{ov.cnName || ''}
									</Checkbox>
								);
							})}
						</Checkbox.Group>
					</div>
				</div>
				<div className="flex align-center justify-start padding-tb-16">
					<div className="font-size-16 font-weight-500 margin-right-12">展示在评委端参赛者列表的报名详情：</div>
					<div className="flex-sub">
						<Checkbox.Group
							onChange={(vals) => {
								setSignUpFormShowConf(vals);
							}}
							value={signUpFormShowConf}
							disabled={!!props.disabled}
						>
							{signUpFormShowConfCheckBoxList.map((ov) => {
								return (
									<Checkbox key={ov.id} value={ov.id}>
										{ov.name || ''}
									</Checkbox>
								);
							})}
						</Checkbox.Group>
					</div>
				</div>
				{!props.disabled && (
					<div className="flex-sub flex align-center justify-end padding-tb-16">
						<Button
							type="primary"
							onClick={() => {
								RatingGroupsModalRef.current.open();
							}}
						>
							+ 添加评审组
						</Button>
					</div>
				)}
				<RatingGroupsModal
					ref={RatingGroupsModalRef}
					ratingPersonsOptions={ratingPersons}
					onChange={(data = {}) => {
						const findIndex = ratingGroups.findIndex((ov) => ov.rowKey == data.rowKey);
						if (findIndex >= 0) {
							setRatingGroups(
								ratingGroups.map((ov, oi) => {
									if (findIndex == oi) {
										return data;
									} else {
										return ov;
									}
								})
							);
						} else {
							setRatingGroups([...ratingGroups, { ...data }]);
						}
					}}
				/>
				<Table rowKey="rowKey" dataSource={ratingGroups} pagination={false}>
					<Table.Column
						title="序号"
						key="index"
						dataIndex="index"
						width={110}
						render={(text, record, index) => {
							return index + 1;
						}}
					/>

					<Table.Column title="评审组名称" key="name" dataIndex="name" width={260} />
					<Table.Column
						title="评委名称"
						key="ratingPerson"
						dataIndex="ratingPerson"
						render={(text, record, index) => {
							return (
								<Space wrap>
									{record.ratingPerson.map((ov, oi) => {
										return (
											<Tag
												color="#165dff"
												key={ov.userId}
												closable={!props.disabled && true}
												onClose={() => {
													setRatingGroups([
														...ratingGroups.map((ob, obi) => {
															if (index == obi) {
																return {
																	...ob,
																	ratingPerson: ob.ratingPerson.filter((co, ci) => ci != oi),
																};
															}
															return {
																...ob,
															};
														}),
													]);
												}}
											>
												评委{oi + 1}-{ov.name}
											</Tag>
										);
									})}
								</Space>
							);
						}}
					/>
					{!props.disabled && (
						<Table.Column
							title="操作"
							key="option"
							dataIndex="option"
							width={180}
							render={(_, record, index) => {
								return (
									<>
										<Button
											type="link"
											size="small"
											onClick={() => {
												RatingGroupsModalRef.current.open({
													...record,
												});
											}}
										>
											编辑
										</Button>

										<Popconfirm
											title="提示"
											description="确定删除吗？"
											onConfirm={() => {
												setRatingGroups([...ratingGroups.filter((ov, oi) => index != oi)]);
											}}
											okText="确定"
											cancelText="取消"
										>
											<Button type="link" danger size="small">
												删除
											</Button>
										</Popconfirm>
									</>
								);
							}}
						/>
					)}
				</Table>
				{!props.disabled && (
					<Space className="margin-tb-20">
						<Button type="primary" onClick={() => submitForm()}>
							提交保存
						</Button>
					</Space>
				)}
			</div>
		</div>
	);
};

export default Index;
