import { Button, Result } from 'antd';
import { useRouterLink } from '@/hook/useRouter/index';
const Index = () => {
	const { linkTo, searchParams, setSearchParams } = useRouterLink();
	return (
		<Result
			status="success"
			title="完成"
			subTitle=""
			extra={[
				<Button type="primary" onClick={() => linkTo('/competition/competitionManage', { replace: true })}>
					回到赛事列表
				</Button>,
			]}
		/>
	);
};

export default Index;
