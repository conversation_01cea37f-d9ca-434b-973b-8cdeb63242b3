import Breadcrumb from '@/components/Breadcrumb';
import { useEffect, useRef, useState } from 'react';
import { Steps } from 'antd';
import { useRouterLink } from '@/hook/useRouter/index';
import Step1 from './Step1';
import Step2 from './Step2';
import Step3 from './Step3';
import Step4 from './Step4';

import { getActivityDetail } from '@/api/Competition/CompetitionManage/index';

const Index = () => {
	const { linkTo, searchParams, setSearchParams } = useRouterLink();
	const id = searchParams.get('id') || '';
	const step = searchParams.get('step') || 0;
	const [detail, setDetail] = useState({});
	const [current, setCurrent] = useState(step);
	const [initFinish, setInitFinish] = useState(false);
	const [detailId, setDetailId] = useState(id);
	const getDetail = (id) => {
		return new Promise((resolve) => {
			getActivityDetail({
				id,
			}).then((res) => {
				const resData = res.data || {};
				// 处理区域
				resData.tempArea = [resData.provinceCode, resData.cityCode];
				setDetail(resData);
				resolve(resData);
			});
		});
	};
	useEffect(() => {
		if (detailId) {
			getDetail(detailId).then((resData) => {
				setInitFinish(true);
				setSearchParams(
					{
						step: current,
						id: detailId || '',
					},
					{ replace: true }
				);
			});
		} else {
			setInitFinish(true);
			setSearchParams(
				{
					step: current,
					id: detailId || '',
				},
				{ replace: true }
			);
		}
	}, [current]);

	return (
		<div className="flex-sub flex flex-direction-column margin-top-16">
			<Breadcrumb
				icon="icon-competitionManage"
				list={[
					{
						name: '赛事管理',
						link: '/competition/competitionManage',
					},
				]}
				name={`赛事${searchParams.get('id') ? '编辑' : '新增'}`}
			/>
			<div className="position-relative flex-sub margin-lr-20 margin-bottom-20 margin-top-4 padding-20 bg-color-ffffff border-radius-4">
				<div className="width-70per margin-0-auto padding-bottom-20">
					<Steps
						current={current - 0}
						items={[
							{
								title: '创建赛事信息',
								description: null,
							},
							{
								title: '创建报名信息',
								description: null,
							},
							{
								title: '评分配置',
								description: null,
							},
							{
								title: '创建完成',
								description: null,
							},
						]}
					/>
				</div>
				{current == 0 && initFinish && <Step1 detail={detail} setCurrent={setCurrent} setDetailId={setDetailId} />}
				{current == 1 && detail.id && initFinish && (
					<Step2 setCurrent={setCurrent} templateId={detail.entryFormTemplateId} disabled={!['', 1, 2].includes(detail.status || 0)} />
				)}
				{current == 2 && detail.id && initFinish && (
					<Step3 setCurrent={setCurrent} detail={detail} detailId={detailId} templateId={detail.entryFormTemplateId} />
				)}

				{current == 3 && initFinish && <Step4 setCurrent={setCurrent} />}
			</div>
		</div>
	);
};

export default Index;
