// 获取文件类型数据
export function getFileTypeData() {
	return [
		{
			name: '文档',
			list: ['pdf', 'ppt/pptx', 'doc/docx', 'xls/xlsx'],
		},
		{
			name: '视频',
			list: ['mp4', 'mov', 'avi'],
		},
		{
			name: '图片',
			list: ['jpg', 'png'],
		},
		{
			name: '音频',
			list: ['mp3', 'm4a', 'wma'],
		},
		{
			name: '压缩包',
			list: ['rar', 'zip'],
		},
	].map((ov, oi) => {
		return {
			id: oi + 1,
			name: ov.name,
			checkedList: [],
			list: ov.list.map((ovv, oii) => {
				return {
					id: oii + 1,
					name: ovv,
				};
			}),
		};
	});
}

// 获取限制列表
export function getLimitList() {
	return [
		{ check: 'required', mess: '必填项', limitValue: true },
		{ check: 'length', mess: '字符长度必须小于', limitValue: '' },
		{ check: 'number', mess: '请填写数字', limitValue: true },
		{ check: 'email', mess: '邮箱格式错误', limitValue: true },
		{ check: 'cn-phone', mess: '手机号格式错误', limitValue: true },
		{ check: 'cn-idcard', mess: '身份证格式错误', limitValue: true },
		{
			check: 'file',
			mess: '文件格式必须为%，文件大小不超过%M',
			limitValue: ';',
		},
		{ check: 'data-domain-white', mess: '白名单', limitValue: '' },
		{ check: 'data-domain-black', mess: '黑名单', limitValue: '' },
	];
}

// 处理限制数据
export function handleLimitData(checkName = [], { length, fileSize, fileType, domainText }) {
	const keyNameList = checkName.filter((item) => !['file-1', 'file-2'].includes(item));
	if (checkName.some((item) => ['file-1', 'file-2'].includes(item))) {
		keyNameList.push('file');
	}
	return getLimitList()
		.filter((ov) => keyNameList.includes(ov.check))
		.map(({ check, mess, limitValue }) => {
			const checkItem = { check, mess, limitValue };
			if (check === 'length') {
				checkItem.mess = `${mess}${length}`;
				checkItem.limitValue = length;
			} else if (check === 'file') {
				checkItem.mess = `文件格式必须为${fileType}，文件大小不超过${fileSize}M`;
				checkItem.limitValue = `${fileType};${fileSize}`;
			} else if (check === 'data-domain-white' || check === 'data-domain-black') {
				checkItem.limitValue = domainText;
			}
			return checkItem;
		});
}

// 初始化表单模板数据
export function getInitFormTplData() {
	return [
		{
			name: '基本信息',
			fields: [
				{
					cnName: '单位名称',
					fieldName: 'name',
					fieldPrompt: ' 单位名称',
				},
				{
					cnName: '单位名称',
					fieldName: 'name',
					fieldPrompt: '请输入单位名称',
				},
				{
					cnName: '单位名称',
					fieldName: 'name',
					fieldPrompt: '请输入单位名称',
				},
			],
		},
	];
}

// 获取随机数
export function getRandomNum() {
	return new Date().valueOf().toString() + Math.floor(Math.random() * 10000);
}

const initTemplateData = [
	{
		name: '基本信息',
		type: 1,
		optionInfo: [],
		fields: [
			{
				cnName: '单位名称',
				fieldName: 'name',
				fieldPrompt: '请输入单位名称',
				fieldType: 3,
				fieldOption: [],
				fieldTemplate: '',
				fieldCheck: [
					{
						check: 'required',
						mess: '必填项',
						limitValue: true,
					},
				],
			},
			{
				cnName: '统一社会信用代码',
				fieldName: 'creditCode',
				fieldPrompt: '请输入统一社会信用代码',
				fieldType: 3,
				fieldOption: [],
				fieldTemplate: '',
				fieldCheck: [
					{
						check: 'required',
						mess: '必填项',
						limitValue: true,
					},
				],
			},
			{
				cnName: '注册时间',
				fieldName: 'establishTime',
				fieldPrompt: '请输入注册时间',
				fieldType: 6,
				fieldOption: [],
				fieldTemplate: '',
				fieldCheck: [
					{
						check: 'required',
						mess: '必填项',
						limitValue: true,
					},
				],
			},
			{
				cnName: '注册地区',
				fieldName: 'provinceCodeCityCodeAreaCode',
				fieldPrompt: '请选择注册地区',
				fieldType: 7,
				fieldOption: [],
				fieldTemplate: '',
				fieldCheck: [
					{
						check: 'required',
						mess: '必填项',
						limitValue: true,
					},
				],
			},
			{
				cnName: '注册地址',
				fieldName: 'registerAddress',
				fieldPrompt: '请输入注册地址',
				fieldType: 3,
				fieldOption: [],
				fieldTemplate: '',
				fieldCheck: [
					{
						check: 'required',
						mess: '必填项',
						limitValue: true,
					},
					{
						check: 'length',
						mess: '字符长度必须小于50',
						limitValue: 50,
					},
				],
			},
			{
				cnName: '通讯地区',
				fieldName: 'contactProvinceCodeCityCodeAreaCode',
				fieldPrompt: '请选择通讯地区',
				fieldType: 7,
				fieldOption: [],
				fieldTemplate: '',
				fieldCheck: [
					{
						check: 'required',
						mess: '必填项',
						limitValue: true,
					},
				],
			},
			{
				cnName: '通讯地址',
				fieldName: 'contactAddress',
				fieldPrompt: '请输入通讯地址',
				fieldType: 3,
				fieldOption: [],
				fieldTemplate: '',
				fieldCheck: [
					{
						check: 'required',
						mess: '必填项',
						limitValue: true,
					},
					{
						check: 'length',
						mess: '字符长度必须小于50',
						limitValue: 50,
					},
				],
			},
			{
				cnName: '法定代表人',
				fieldName: 'legalPersonName',
				fieldPrompt: '请输入法定代表人',
				fieldType: 3,
				fieldOption: [],
				fieldTemplate: '',
				fieldCheck: [
					{
						check: 'required',
						mess: '必填项',
						limitValue: true,
					},
				],
			},
			{
				cnName: '法定代表人联系电话',
				fieldName: 'legalPersonPhone',
				fieldPrompt: '请输入法定代表人联系电话',
				fieldType: 3,
				fieldOption: [],
				fieldTemplate: '',
				fieldCheck: [
					{
						check: 'required',
						mess: '必填项',
						limitValue: true,
					},
					{
						check: 'cn-phone',
						mess: '手机号格式错误',
						limitValue: true,
					},
				],
			},
			{
				cnName: '法定代表人邮箱',
				fieldName: 'legalPersonEmail',
				fieldPrompt: '请输入法定代表人邮箱',
				fieldType: 3,
				fieldOption: [],
				fieldTemplate: '',
				fieldCheck: [
					{
						check: 'required',
						mess: '必填项',
						limitValue: true,
					},
					{
						check: 'email',
						mess: '邮箱格式错误',
						limitValue: true,
					},
				],
			},
			{
				cnName: '负责人',
				fieldName: 'headPersonName',
				fieldPrompt: '请输入负责人名称',
				fieldType: 3,
				fieldOption: [],
				fieldTemplate: '',
				fieldCheck: [
					{
						check: 'required',
						mess: '必填项',
						limitValue: true,
					},
				],
			},
			{
				cnName: '负责人联系电话',
				fieldName: 'headPersonPhone',
				fieldPrompt: '请输入负责人联系电话',
				fieldType: 3,
				fieldOption: [],
				fieldTemplate: '',
				fieldCheck: [
					{
						check: 'required',
						mess: '必填项',
						limitValue: true,
					},
					{
						check: 'cn-phone',
						mess: '手机号格式错误',
						limitValue: true,
					},
				],
			},
			{
				cnName: '负责人邮箱',
				fieldName: 'headPersonEmail',
				fieldPrompt: '请输入负责人邮箱',
				fieldType: 3,
				fieldOption: [],
				fieldTemplate: '',
				fieldCheck: [
					{
						check: 'required',
						mess: '必填项',
						limitValue: true,
					},
					{
						check: 'email',
						mess: '邮箱格式错误',
						limitValue: true,
					},
				],
			},
			{
				cnName: '单位职工人数',
				fieldName: 'workersNumber',
				fieldPrompt: '请输入单位职工人数',
				fieldType: 3,
				fieldOption: [],
				fieldTemplate: '',
				fieldCheck: [
					{
						check: 'required',
						mess: '必填项',
						limitValue: true,
					},
					{
						check: 'number',
						mess: '请填写数字',
						limitValue: true,
					},
				],
			},
			{
				cnName: '社保人数',
				fieldName: 'socialSecurityNumber',
				fieldPrompt: '请输入单位职工总数中的社保人数',
				fieldType: 3,
				fieldOption: [],
				fieldTemplate: '',
				fieldCheck: [
					{
						check: 'required',
						mess: '必填项',
						limitValue: true,
					},
					{
						check: 'number',
						mess: '请填写数字',
						limitValue: true,
					},
				],
			},
			{
				cnName: '单位简介',
				fieldName: 'introduction',
				fieldPrompt: '请输入单位简介，500字以内',
				fieldType: 4,
				fieldOption: [],
				fieldTemplate: '',
				fieldCheck: [
					{
						check: 'required',
						mess: '必填项',
						limitValue: true,
					},
					{
						check: 'length',
						mess: '字符长度必须小于500',
						limitValue: 500,
					},
				],
			},
			{
				cnName: '主营业务',
				fieldName: 'mainBusiness',
				fieldPrompt: '请输入主营业务，500字以内',
				fieldType: 4,
				fieldOption: [],
				fieldTemplate: '',
				fieldCheck: [
					{
						check: 'required',
						mess: '必填项',
						limitValue: true,
					},
					{
						check: 'length',
						mess: '字符长度必须小于500',
						limitValue: 500,
					},
				],
			},
		],
	},
	{
		name: '财务信息',
		type: 1,
		optionInfo: [],
		fields: [
			{
				cnName: '营业收入（万元人民币）',
				fieldName: 'operatingRevenue',
				fieldPrompt: '请输入营业收入（万元人民币）',
				fieldType: 3,
				fieldOption: [],
				fieldTemplate: '',
				fieldCheck: [
					{
						check: 'required',
						mess: '必填项',
						limitValue: true,
					},
					{
						check: 'number',
						mess: '请填写数字',
						limitValue: true,
					},
				],
			},
			{
				cnName: '总成本费用（万元人民币）',
				fieldName: 'totalCost',
				fieldPrompt: '请输入总成本费用（万元人民币）',
				fieldType: 3,
				fieldOption: [],
				fieldTemplate: '',
				fieldCheck: [
					{
						check: 'required',
						mess: '必填项',
						limitValue: true,
					},
					{
						check: 'number',
						mess: '请填写数字',
						limitValue: true,
					},
				],
			},
			{
				cnName: '研发费用总额（万元人民币）',
				fieldName: 'totalStudy',
				fieldPrompt: '请输入研发费用总额（万元人民币）',
				fieldType: 3,
				fieldOption: [],
				fieldTemplate: '',
				fieldCheck: [
					{
						check: 'required',
						mess: '必填项',
						limitValue: true,
					},
					{
						check: 'number',
						mess: '请填写数字',
						limitValue: true,
					},
				],
			},
			{
				cnName: '利润总额（万元人民币）',
				fieldName: 'totalProfit',
				fieldPrompt: '请输入利润总额（万元人民币）',
				fieldType: 3,
				fieldOption: [],
				fieldTemplate: '',
				fieldCheck: [
					{
						check: 'required',
						mess: '必填项',
						limitValue: true,
					},
					{
						check: 'number',
						mess: '请填写数字',
						limitValue: true,
					},
				],
			},
			{
				cnName: '净利润（万元人民币）',
				fieldName: 'netProfit',
				fieldPrompt: '请输入净利润（万元人民币）',
				fieldType: 3,
				fieldOption: [],
				fieldTemplate: '',
				fieldCheck: [
					{
						check: 'required',
						mess: '必填项',
						limitValue: true,
					},
					{
						check: 'number',
						mess: '请填写数字',
						limitValue: true,
					},
				],
			},
			{
				cnName: '资产总额（万元人民币）',
				fieldName: 'totalAssets',
				fieldPrompt: '请输入资产总额（万元人民币）',
				fieldType: 3,
				fieldOption: [],
				fieldTemplate: '',
				fieldCheck: [
					{
						check: 'required',
						mess: '必填项',
						limitValue: true,
					},
					{
						check: 'number',
						mess: '请填写数字',
						limitValue: true,
					},
				],
			},
			{
				cnName: '净资产（万元人民币）',
				fieldName: 'netAssets',
				fieldPrompt: '请输入净资产（万元人民币）',
				fieldType: 3,
				fieldOption: [],
				fieldTemplate: '',
				fieldCheck: [
					{
						check: 'required',
						mess: '必填项',
						limitValue: true,
					},
					{
						check: 'number',
						mess: '请填写数字',
						limitValue: true,
					},
				],
			},
			{
				cnName: '负债总额（万元人民币）',
				fieldName: 'totalLiabilities',
				fieldPrompt: '请输入负债总额（万元人民币）',
				fieldType: 3,
				fieldOption: [],
				fieldTemplate: '',
				fieldCheck: [
					{
						check: 'required',
						mess: '必填项',
						limitValue: true,
					},
					{
						check: 'number',
						mess: '请填写数字',
						limitValue: true,
					},
				],
			},
		],
	},
	{
		name: '团队信息',
		type: 1,
		optionInfo: [],
		fields: [
			{
				cnName: '核心人员一：姓名',
				fieldName: 'keyPerson1Name',
				fieldPrompt: '请输入姓名',
				fieldType: 3,
				fieldOption: [],
				fieldTemplate: '',
				fieldCheck: [
					{
						check: 'required',
						mess: '必填项',
						limitValue: true,
					},
				],
			},
			{
				cnName: '核心人员一：性别',
				fieldName: 'keyPersonnel1Gender',
				fieldPrompt: '请选择性别',
				fieldType: 1,
				fieldOption: [
					{
						key: '17133424023869746',
						val: '男',
						value: false,
						required: false,
					},
					{
						key: '1713342405658559',
						val: '女',
					},
				],
				fieldTemplate: '',
				fieldCheck: [
					{
						check: 'required',
						mess: '必填项',
						limitValue: true,
					},
				],
			},
			{
				cnName: '核心人员一：职位',
				fieldName: 'keyPersonnel1Position',
				fieldPrompt: '请输入职位',
				fieldType: 3,
				fieldOption: [],
				fieldTemplate: '',
				fieldCheck: [
					{
						check: 'required',
						mess: '必填项',
						limitValue: true,
					},
				],
			},
			{
				cnName: '核心人员一：最高学历',
				fieldName: 'keyPersonnel1HighestEducationLevel',
				fieldPrompt: '请输入最高学历',
				fieldType: 1,
				fieldOption: [
					{
						key: '17133426439291745',
						val: '博士',
					},
					{
						key: '17133426456333544',
						val: '硕士',
					},
					{
						key: '17133426470427412',
						val: '本科',
					},
					{
						key: '17133426556267711',
						val: '本科以下',
					},
				],
				fieldTemplate: '',
				fieldCheck: [
					{
						check: 'required',
						mess: '必填项',
						limitValue: true,
					},
				],
			},
			{
				cnName: '核心人员一：毕业院校',
				fieldName: 'keyPersonnel1GraduationInstitution',
				fieldPrompt: '请输入毕业院校',
				fieldType: 3,
				fieldOption: [],
				fieldTemplate: '',
				fieldCheck: [
					{
						check: 'required',
						mess: '必填项',
						limitValue: true,
					},
				],
			},
			{
				cnName: '核心人员一：联系电话',
				fieldName: 'keyPersonnel1Phone',
				fieldPrompt: '请输入联系电话',
				fieldType: 3,
				fieldOption: [],
				fieldTemplate: '',
				fieldCheck: [
					{
						check: 'required',
						mess: '必填项',
						limitValue: true,
					},
					{
						check: 'cn-phone',
						mess: '手机号格式错误',
						limitValue: true,
					},
				],
			},
			{
				cnName: '核心人员一：联系邮箱',
				fieldName: 'keyPersonnel1Email',
				fieldPrompt: '请输入联系邮箱',
				fieldType: 3,
				fieldOption: [],
				fieldTemplate: '',
				fieldCheck: [
					{
						check: 'required',
						mess: '必填项',
						limitValue: true,
					},
					{
						check: 'email',
						mess: '邮箱格式错误',
						limitValue: true,
					},
				],
			},
			{
				cnName: '核心人员二：姓名',
				fieldName: 'keyPerson2Name',
				fieldPrompt: '请输入姓名',
				fieldType: 3,
				fieldOption: [],
				fieldTemplate: '',
				fieldCheck: [
					{
						check: 'required',
						mess: '必填项',
						limitValue: true,
					},
				],
			},
			{
				cnName: ' 核心人员二：性别',
				fieldName: 'keyPersonnel2Gender',
				fieldPrompt: '请选择性别',
				fieldType: 1,
				fieldOption: [
					{
						key: '17138364285683388',
						val: '男',
					},
					{
						key: '17138364313677200',
						val: '女',
					},
				],
				fieldTemplate: '',
				fieldCheck: [
					{
						check: 'required',
						mess: '必填项',
						limitValue: true,
					},
				],
			},
			{
				cnName: '核心人员二：职位',
				fieldName: 'keyPersonnel2Position',
				fieldPrompt: '请输入职位',
				fieldType: 3,
				fieldOption: [],
				fieldTemplate: '',
				fieldCheck: [
					{
						check: 'required',
						mess: '必填项',
						limitValue: true,
					},
				],
			},
			{
				cnName: '核心人员二：最高学历',
				fieldName: 'keyPersonnel2HighestEducationLevel',
				fieldPrompt: '请输入最高学历',
				fieldType: 1,
				fieldOption: [
					{
						key: '17133443451767709',
						val: '博士',
					},
					{
						key: '17133443480322142',
						val: '硕士',
					},
					{
						key: '17133443510099772',
						val: '本科',
					},
					{
						key: '17133443578682064',
						val: '本科以下',
					},
				],
				fieldTemplate: '',
				fieldCheck: [
					{
						check: 'required',
						mess: '必填项',
						limitValue: true,
					},
				],
			},
			{
				cnName: '核心人员二：毕业院校',
				fieldName: 'keyPersonnel2GraduationInstitution',
				fieldPrompt: '请输入毕业院校',
				fieldType: 3,
				fieldOption: [],
				fieldTemplate: '',
				fieldCheck: [
					{
						check: 'required',
						mess: '必填项',
						limitValue: true,
					},
				],
			},
			{
				cnName: '核心人员二：联系电话',
				fieldName: 'keyPersonnel2Phone',
				fieldPrompt: '请输入联系电话',
				fieldType: 3,
				fieldOption: [],
				fieldTemplate: '',
				fieldCheck: [
					{
						check: 'required',
						mess: '必填项',
						limitValue: true,
					},
					{
						check: 'cn-phone',
						mess: '手机号格式错误',
						limitValue: true,
					},
				],
			},
			{
				cnName: '核心人员二：联系邮箱',
				fieldName: 'keyPersonnel2Email',
				fieldPrompt: '请输入联系邮箱',
				fieldType: 3,
				fieldOption: [],
				fieldTemplate: '',
				fieldCheck: [
					{
						check: 'required',
						mess: '必填项',
						limitValue: true,
					},
					{
						check: 'email',
						mess: '邮箱格式错误',
						limitValue: true,
					},
				],
			},
		],
	},
	{
		name: '知识产权',
		type: 1,
		optionInfo: [],
		fields: [
			{
				cnName: 'Ⅰ类知识产权',
				fieldName: 'firstKnowledgeTotal',
				fieldPrompt: '请输入Ⅰ类知识产权数量，包含发明专利等',
				fieldType: 3,
				fieldOption: [],
				fieldTemplate: '',
				fieldCheck: [
					{
						check: 'required',
						mess: '必填项',
						limitValue: true,
					},
					{
						check: 'number',
						mess: '请填写数字',
						limitValue: true,
					},
				],
			},
			{
				cnName: 'Ⅱ类知识产权',
				fieldName: 'secondKnowledgeTotal',
				fieldPrompt: '请输入Ⅱ类知识产权数量，包含实用新型专利、外观设计专利、软件著作权',
				fieldType: 3,
				fieldOption: [],
				fieldTemplate: '',
				fieldCheck: [
					{
						check: 'required',
						mess: '必填项',
						limitValue: true,
					},
					{
						check: 'number',
						mess: '请填写数字',
						limitValue: true,
					},
				],
			},
		],
	},
	{
		name: '项目信息',
		type: 1,
		optionInfo: [],
		fields: [
			{
				cnName: '项目名称',
				fieldName: 'projectName',
				fieldPrompt: '请输入项目名称',
				fieldType: 3,
				fieldOption: [],
				fieldTemplate: '',
				fieldCheck: [
					{
						check: 'required',
						mess: '必填项',
						limitValue: true,
					},
				],
			},
			{
				cnName: '行业',
				fieldName: 'industryId',
				fieldPrompt: '请选择行业',
				fieldType: 1,
				fieldOption: [],
				fieldTemplate: '',
				fieldCheck: [
					{
						check: 'required',
						mess: '必填项',
						limitValue: true,
					},
				],
			},
			{
				cnName: '组别',
				fieldName: 'projectGroupId',
				fieldPrompt: '请选择组别',
				fieldType: 1,
				fieldOption: [],
				fieldTemplate: '',
				fieldCheck: [
					{
						check: 'required',
						mess: '必填项',
						limitValue: true,
					},
				],
			},
			{
				cnName: '项目简介',
				fieldName: 'projectBrief',
				fieldPrompt: '请输入项目简介',
				fieldType: 4,
				fieldOption: [],
				fieldTemplate: '',
				fieldCheck: [
					{
						check: 'required',
						mess: '必填项',
						limitValue: true,
					},
					{
						check: 'length',
						mess: '字符长度必须小于500',
						limitValue: 500,
					},
				],
			},
			{
				cnName: '核心技术',
				fieldName: 'coreTechnology',
				fieldPrompt: '请输入核心技术',
				fieldType: 4,
				fieldOption: [],
				fieldTemplate: '',
				fieldCheck: [
					{
						check: 'required',
						mess: '必填项',
						limitValue: true,
					},
					{
						check: 'length',
						mess: '字符长度必须小于500',
						limitValue: 500,
					},
				],
			},
			{
				cnName: '项目负责人姓名',
				fieldName: 'projectLeaderName',
				fieldPrompt: '请输入项目负责人姓名',
				fieldType: 3,
				fieldOption: [],
				fieldTemplate: '',
				fieldCheck: [
					{
						check: 'required',
						mess: '必填项',
						limitValue: true,
					},
				],
			},
			{
				cnName: '项目负责人性别',
				fieldName: 'projectLeaderGender',
				fieldPrompt: '请输入项目负责人性别',
				fieldType: 1,
				fieldOption: [
					{
						key: '17133452868725106',
						val: '男',
					},
					{
						key: '17133452978109065',
						val: '女',
					},
				],
				fieldTemplate: '',
				fieldCheck: [
					{
						check: 'required',
						mess: '必填项',
						limitValue: true,
					},
				],
			},
			{
				cnName: '项目负责人职位',
				fieldName: 'projectLeaderPosition',
				fieldPrompt: '请输入项目负责人职位',
				fieldType: 3,
				fieldOption: [],
				fieldTemplate: '',
				fieldCheck: [
					{
						check: 'required',
						mess: '必填项',
						limitValue: true,
					},
				],
			},
			{
				cnName: '项目负责人最高学历',
				fieldName: 'projectLeaderHighestEducationLevel',
				fieldPrompt: '请输入项目负责人最高学历',
				fieldType: 3,
				fieldOption: [],
				fieldTemplate: '',
				fieldCheck: [
					{
						check: 'required',
						mess: '必填项',
						limitValue: true,
					},
				],
			},
			{
				cnName: '项目联系人姓名',
				fieldName: 'projectContactPersonName',
				fieldPrompt: '请输入项目联系人姓名',
				fieldType: 3,
				fieldOption: [],
				fieldTemplate: '',
				fieldCheck: [
					{
						check: 'required',
						mess: '必填项',
						limitValue: true,
					},
				],
			},
			{
				cnName: '项目联系人联系电话',
				fieldName: 'projectContactPersonPhone',
				fieldPrompt: '请输入项目联系人联系电话',
				fieldType: 3,
				fieldOption: [],
				fieldTemplate: '',
				fieldCheck: [
					{
						check: 'required',
						mess: '必填项',
						limitValue: true,
					},
					{
						check: 'cn-phone',
						mess: '手机号格式错误',
						limitValue: true,
					},
				],
			},
		],
	},
	{
		name: '附件上传',
		type: 1,
		optionInfo: [],
		fields: [
			{
				cnName: '路演视频',
				fieldName: 'roadshowVideo',
				fieldPrompt: '格式：mp4, mov, avi；大小：不超过500M；时长：不超过8分钟；录制工具：腾讯会议',
				fieldType: 5,
				fieldOption: [],
				fieldTemplate: '',
				fieldCheck: [
					{
						check: 'required',
						mess: '必填项',
						limitValue: true,
					},
					{
						check: 'file',
						mess: '文件格式必须为mp4,mov,avi，文件大小不超过500M',
						limitValue: 'mp4,mov,avi;500',
					},
				],
			},
			{
				cnName: '路演PPT',
				fieldName: 'roadshowPpt',
				fieldPrompt: '格式：PDF；大小：不超过100M',
				fieldType: 5,
				fieldOption: [],
				fieldTemplate: '',
				fieldCheck: [
					{
						check: 'required',
						mess: '必填项',
						limitValue: true,
					},
					{
						check: 'file',
						mess: '文件格式必须为ppt/pptx,pdf，文件大小不超过100M',
						limitValue: 'ppt/pptx,pdf;100',
					},
				],
			},
			{
				cnName: '承诺函',
				fieldName: 'commitmentLetter',
				fieldPrompt: '格式：PDF；大小：不超过100M',
				fieldType: 5,
				fieldOption: [],
				fieldTemplate: '',
				fieldCheck: [
					{
						check: 'required',
						mess: '必填项',
						limitValue: true,
					},
					{
						check: 'file',
						mess: '文件格式必须为pdf，文件大小不超过100M',
						limitValue: 'pdf;100',
					},
				],
			},
		],
	},
];

export const initFormTplData = (addModuleItem, addFieldItem) => {
	initTemplateData.forEach((ov) => {
		const { name, type, optionInfo, fields } = ov;
		addModuleItem({ name, type, optionInfo }).then(({ key }) => {
			fields.forEach((field) => {
				addFieldItem(field, key);
			});
		});
	});
};
