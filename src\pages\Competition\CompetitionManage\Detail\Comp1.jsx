import { useEffect, useState } from 'react';
import Step1 from '../Curd/Step1';
import Step2 from '../Curd/Step2';
import Step3 from '../Curd/Step3';
import { getActivityDetail } from '@/api/Competition/CompetitionManage/index';

const Index = (props = {}) => {
	const [detail, setDetail] = useState({});
	const getDetail = (id) => {
		return new Promise((resolve) => {
			getActivityDetail({
				id,
			}).then((res) => {
				setDetail(res.data || {});
				resolve(res.data || {});
			});
		});
	};
	useEffect(() => {
		if (props.id) {
			getDetail(props.id);
		}
	}, [props.id]);
	return (
		<div>
			<Step1 detail={detail} disabled={true} />

			{detail.id && (
				<>
					<Step2 disabled={true} hideBtn detailId={detail.id} templateId={detail.entryFormTemplateId} status={detail.status} />
				</>
			)}
			{detail.id && (
				<>
					<Step3 disabled={true} detailId={detail.id} detail={detail} templateId={detail.entryFormTemplateId} />
				</>
			)}
		</div>
	);
};
export default Index;
