import { useEffect, useRef, useState } from 'react';
import { Space, Table, Form, Row, Col, Input, Select, Button, message } from 'antd';
import Permission from '@/components/Permission';
import ModalForm from '@/components/ModalForm';
import ImportForm from '@/components/ImportForm';
import { ImportOutlined, ExportOutlined, SearchOutlined, ReloadOutlined } from '@ant-design/icons';
import { useRouterLink } from '@/hook/useRouter/index';
import {
	pageActivitySignUp,
	getActivityRatingSession,
	getActivityDetail,
	batchAuditStatus,
	signUpExportList,
	signUpImportList,
} from '@/api/Competition/CompetitionManage/index';
import { listDictItem } from '@/api/Competition/ConfigCenter/index';
import AuditStatus from '@/components/Competition/AuditStatus';
import RegistrationDetails from './RegistrationDetails';

import PreViewFile from '@/components/Competition/PreViewFile/index';

import { download } from '@/utils/common';
import dayjs from 'dayjs';

const statusOptions = [
	{
		value: -1,
		label: '预报名',
	},
	{
		value: 0,
		label: '暂存',
	},
	{
		value: 1,
		label: '审核中',
	},
	{
		value: 2,
		label: '未通过',
	},
	{
		value: 3,
		label: '已通过',
	},
	// {
	//     value: 4,
	//     label: '待评审',
	// },
	// {
	//     value: 5,
	//     label: '已评审',
	// },
	// {
	//     value: 6,
	//     label: '已结束',
	// }
];

const Index = () => {
	const { linkTo, searchParams } = useRouterLink();
	const activityId = searchParams.get('id');
	const ModalFormImportRef = useRef();
	const [form] = Form.useForm();

	// 赛事名称
	const [industryIdOptions, setIndustryIdOptions] = useState([]);
	// 赛事状态
	const [activityGroupIdOptions, setActivityGroupIdOptions] = useState([]);
	// 赛事时间
	const [activitySessionIdOptions, setActivitySessionIdOptions] = useState([]);

	const getOptionList = (filterObj = {}) => {
		listDictItem({
			code: 'event_group_name',
		}).then((res) => {
			setActivityGroupIdOptions(
				(res.data || [])
					.filter((ov) => {
						return (filterObj.actGroup || []).includes(ov.id);
					})
					.map((ov) => {
						return { label: ov.itemName, value: ov.id };
					})
			);
		});
		listDictItem({
			code: 'event_industry',
		}).then((res) => {
			setIndustryIdOptions(
				(res.data || [])
					.filter((ov) => {
						return (filterObj.actIndustry || []).includes(ov.id);
					})
					.map((ov) => {
						return { label: ov.itemName, value: ov.id };
					})
			);
		});
		getActivityRatingSession({
			activityId: activityId,
		}).then((res) => {
			setActivitySessionIdOptions(
				(res.data || []).map((ov) => {
					return { label: ov.name, value: ov.id };
				})
			);
		});
	};

	const getDetail = (id) => {
		return new Promise((resolve) => {
			getActivityDetail({
				id,
			}).then((res) => {
				console.log('🚀 ~ returnnewPromise ~ res:', res);
				resolve({
					actGroup: res.data.actGroup || [],
					actIndustry: res.data.actIndustry || [],
				});
			});
		});
	};

	useEffect(() => {
		getDetail(activityId).then((res) => {
			getOptionList(res);
		});
	}, []);
	const [dataSource, setDataSource] = useState([]);
	const [pagination, setPagination] = useState({
		pageNum: 1,
		pageSize: 10,
		total: 0,
		showTotal: (total) => `共 ${total} 条`,
	});
	// 搜索
	const paginationChange = (pageNum = 1, pageSize = pagination.pageSize) => {
		pagination.pageNum = pageNum;
		pagination.pageSize = pageSize;
		setPagination({ ...pagination });
	};

	const [params, setParams] = useState({});
	const searchBtn = () => {
		setParams({ ...form.getFieldValue() });
	};

	// 获取表格数据
	const getTableData = () => {
		pageActivitySignUp({
			pageNum: pagination.pageNum,
			pageSize: pagination.pageSize,
			...params,
			activityId,
		}).then((res) => {
			const { total, records } = res.data;
			pagination.total = total - 0;
			setDataSource(records);
			setPagination({ ...pagination });
		});
	};

	// 导入弹窗显示
	const importModalShow = () => {
		ModalFormImportRef.current.setOpen(true);
		ModalFormImportRef.current.setTitle('表格导入');
	};

	// 导入
	const handelImport = (formData) => {
		formData.append('activityId', activityId);
		signUpImportList(formData).then(() => {
			message.success('导入成功');
			pagination.current = 1;
			setPagination({ ...pagination });
			getTableData();
		});
	};

	// 导出
	const exportOutData = () => {
		signUpExportList({
			...params,
			activityId,
		}).then((res) => {
			if (res.size > 0) {
				download.excel(res, `报名列表-${dayjs().format('YYYYMMDD_HH:mm')}`);
				message.success('导出成功');
			} else {
				message.error('导出失败');
			}
		});
	};

	useEffect(() => {
		getTableData();
	}, [pagination.pageNum, pagination.pageSize, params]);

	const RegistrationDetailsRef = useRef();
	const AuditStatusRef = useRef();

	const PreViewFileRef = useRef();
	const previewBtn = (url = '') => {
		const fieldValueList = url.split('.');
		PreViewFileRef.current.open(url, fieldValueList.pop());
	};
	return (
		<div className="flex-sub">
			{/* 筛选条件 开始 */}
			<div className="flex margin-bottom-16 padding-bottom-16 border-bottom-e5e6eb">
				<div className="flex-sub padding-right-16 border-right-e5e6eb">
					<Form
						form={form}
						labelCol={{
							style: { width: '68px' },
						}}
						labelAlign="left"
						className="form-filter"
						initialValues={{}}
					>
						<Row gutter={[16, 16]}>
							<Col xs={24} sm={24} md={12} lg={8}>
								<Form.Item label="行业" name="industryId">
									<Select options={industryIdOptions} placeholder="请选择行业" allowClear />
								</Form.Item>
							</Col>
							<Col xs={24} sm={24} md={12} lg={8}>
								<Form.Item label="组别" name="activityGroupId">
									<Select options={activityGroupIdOptions} placeholder="请选择组别" allowClear />
								</Form.Item>
							</Col>
							<Col xs={24} sm={24} md={12} lg={8}>
								<Form.Item label="场次名称" name="activitySessionId">
									<Select options={activitySessionIdOptions} placeholder="请选择场次名称" allowClear />
								</Form.Item>
							</Col>
							<Col xs={24} sm={24} md={12} lg={8}>
								<Form.Item label="单位名称" name="name">
									<Input placeholder="请输入单位名称" allowClear />
								</Form.Item>
							</Col>
							<Col xs={24} sm={24} md={12} lg={8}>
								<Form.Item label="审核状态" name="status">
									<Select options={statusOptions} placeholder="请选择审核状态" allowClear />
								</Form.Item>
							</Col>
						</Row>
					</Form>
				</div>
				<Row className="padding-left-16 max-width-200" gutter={[16, 16]}>
					<Permission hasPermi={['competition:competitionManage:signUp:import']}>
						<Col span={12}>
							<Button type="primary" onClick={importModalShow}>
								<ImportOutlined />
								<span>导入</span>
							</Button>
						</Col>
					</Permission>
					<Permission hasPermi={['competition:competitionManage:signUp:export']}>
						<Col span={12}>
							<Button type="primary" onClick={exportOutData}>
								<ExportOutlined />
								<span>导出</span>
							</Button>
						</Col>
					</Permission>
					<Col span={12}>
						<Button
							type="primary"
							onClick={() => {
								searchBtn();
							}}
						>
							<SearchOutlined />
							<span>查询</span>
						</Button>
					</Col>
					<Col span={12}>
						<Button
							onClick={() => {
								form.resetFields();
								pagination.current = 1;
								setPagination(pagination);
								getTableData();
							}}
						>
							<ReloadOutlined />
							<span>重置</span>
						</Button>
					</Col>
				</Row>
			</div>
			{/* 筛选条件 结束 */}

			{/* 表格列表 开始 */}
			<Table
				rowKey="id"
				dataSource={dataSource}
				pagination={{
					...pagination,
					showQuickJumper: true,
					showSizeChanger: true,
					onChange: paginationChange,
				}}
				scroll={{ x: 'max-content' }}
			>
				<Table.Column
					title="序号"
					key="index"
					dataIndex="index"
					width={110}
					render={(text, record, index) => {
						return index + 1 + pagination.pageSize * (pagination.pageNum - 1);
					}}
				/>
				<Table.Column title="单位名称" dataIndex="name" key="name" />
				<Table.Column title="行业" dataIndex="industryName" key="industryName" />
				<Table.Column title="组别" dataIndex="activityGroupName" key="activityGroupName" />
				<Table.Column title="场次" dataIndex="activitySessionName" key="activitySessionName" />
				<Table.Column
					title="附件"
					dataIndex="attachedList"
					key="attachedList"
					render={(text) => {
						return (
							<div>
								{text &&
									text
										.filter((ov) => ov.fieldValue)
										.map((ov) => {
											return (
												<div
													key={ov.cnName}
													onClick={() => previewBtn(ov.fieldValue)}
													className="cursor-pointer color-165dff"
												>
													{/* <a
														href={ov.fieldValue}
														target='_blank'
													>
														{ov.cnName}
													</a> */}
													{ov.cnName}
												</div>
											);
										})}
							</div>
						);
					}}
				/>
				<Table.Column
					title="审核状态"
					dataIndex="status"
					key="status"
					render={(status) => {
						const find = statusOptions.find((ov) => ov.value == status) || {};
						return find.label || '';
					}}
				/>
				<Table.Column
					title="报名时间"
					dataIndex="createTime"
					key="createTime"
					render={(createTime) => {
						return (createTime || '').slice(0, 16);
					}}
				/>
				<Table.Column
					title="操作"
					dataIndex="options"
					key="options"
					align="center"
					fixed="right"
					width={280}
					render={(_, records) => (
						<Space>
							<Button
								onClick={() => {
									RegistrationDetailsRef.current.open(records);
								}}
								type="link"
								size="small"
							>
								查看报名详情
							</Button>
							{records.status == 1 && (
								<Button
									onClick={() => {
										AuditStatusRef.current.open(records, {
											auditStatus: 2,
											auditReason: '',
										});
									}}
									type="link"
									size="small"
								>
									审核
								</Button>
							)}
							{[2, 3].includes(records.status) && (
								<Button
									onClick={() => {
										AuditStatusRef.current.revoke(records);
									}}
									type="link"
									size="small"
									danger
								>
									撤销审核
								</Button>
							)}
						</Space>
					)}
				/>
			</Table>
			{/* 表格列表 结束 */}

			<RegistrationDetails
				ref={RegistrationDetailsRef}
				onChange={() => {
					getTableData();
				}}
				child={(props) => {
					return (
						<>
							{props.records && props.records.status == 1 && (
								<Button
									type="primary"
									onClick={() => {
										RegistrationDetailsRef.current.close();
										AuditStatusRef.current.open(props.records, {
											auditStatus: 2,
											auditReason: '',
										});
									}}
									size="small"
								>
									审核
								</Button>
							)}
							{props.records && [2, 3].includes(props.records.status) && (
								<Button
									onClick={() => {
										RegistrationDetailsRef.current.close();
										AuditStatusRef.current.revoke(props.records);
									}}
									size="small"
									danger
								>
									撤销审核
								</Button>
							)}
						</>
					);
				}}
			></RegistrationDetails>

			{/* 审核 开始 */}
			<AuditStatus
				ref={AuditStatusRef}
				onSubmit={(row, values) => {
					const params = {
						ids: [row.id],
						status: values.auditStatus == 1 ? 2 : 3,
					};
					batchAuditStatus({
						...params,
						auditReason: params.status == 2 ? values.auditReason : '',
					}).then(() => {
						getTableData();
					});
				}}
				onRevoke={(row) => {
					batchAuditStatus({
						ids: [row.id],
						status: 1,
						auditReason: '',
					}).then(() => {
						getTableData();
					});
				}}
			/>
			{/* 审核 结束 */}

			{/* 导入 开始 */}
			<ModalForm
				ref={ModalFormImportRef}
				modelConfig={{
					styles: {
						body: {
							minHeight: 'unset',
						},
					},
				}}
				onOk={handelImport}
				FormComp={(props) => (
					<ImportForm
						ref={props.FormCompRef}
						fileName="file"
						tplUrl="https://gbac-produce.oss-cn-shenzhen.aliyuncs.com/tip/competition/%E8%B5%9B%E4%BA%8B%E6%8A%A5%E5%90%8D%E5%AF%BC%E5%85%A5%E6%A8%A1%E7%89%88.xlsx"
					/>
				)}
			/>
			{/* 导入 结束 */}

			<PreViewFile ref={PreViewFileRef} />
		</div>
	);
};

export default Index;
