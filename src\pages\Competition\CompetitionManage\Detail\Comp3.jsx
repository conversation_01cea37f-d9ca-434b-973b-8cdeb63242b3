import { useEffect, useRef, useState, forwardRef, useImperativeHandle } from 'react';
import {
	Tabs,
	Radio,
	Pagination,
	Space,
	Table,
	Form,
	Row,
	Col,
	Input,
	Select,
	Switch,
	Modal,
	Button,
	DatePicker,
	Popconfirm,
	Tag,
	message,
} from 'antd';
import Permission from '@/components/Permission';
import ModalForm from '@/components/ModalForm';
import ImportForm from '@/components/ImportForm';
import { ExportOutlined, SearchOutlined, ReloadOutlined, PlusOutlined, ImportOutlined } from '@ant-design/icons';
import { useRouterLink } from '@/hook/useRouter/index';
import {
	getActivityRatingSession,
	saveActivityRatingSession,
	getActivityRatingGroup,
	deleteActivityRatingSession,
	getActivityDetail,
	importActivityRatingSessionEntrant,
	getActivityRatingSessionDetail,
	rankingActivityRatingSessionEntrant,
} from '@/api/Competition/CompetitionManage/index';
import { listDictItem } from '@/api/Competition/ConfigCenter/index';
import dayjs from 'dayjs';
import Comp3DrawerDetail from './Comp3DrawerDetail';
import Comp3FormModal from './Comp3FormModal';

const Index = () => {
	const { linkTo, searchParams } = useRouterLink();
	const activityId = searchParams.get('id');
	const [form] = Form.useForm();

	const [industryIdOptions, setIndustryIdOptions] = useState([]);
	const [activityGroupIdOptions, setActivityGroupIdOptions] = useState([]);

	const getOptionList = (filterObj = {}) => {
		listDictItem({
			code: 'event_group_name',
		}).then((res) => {
			setActivityGroupIdOptions(
				(res.data || [])
					.filter((ov) => {
						return (filterObj.actGroup || []).includes(ov.id);
					})
					.map((ov) => {
						return { label: ov.itemName, value: ov.id };
					})
			);
		});
		listDictItem({
			code: 'event_industry',
		}).then((res) => {
			setIndustryIdOptions(
				(res.data || [])
					.filter((ov) => {
						return (filterObj.actIndustry || []).includes(ov.id);
					})
					.map((ov) => {
						return { label: ov.itemName, value: ov.id };
					})
			);
		});
	};

	const getDetail = (id) => {
		return new Promise((resolve) => {
			getActivityDetail({
				id,
			}).then((res) => {
				resolve({
					actGroup: res.data.actGroup || [],
					actIndustry: res.data.actIndustry || [],
				});
			});
		});
	};

	useEffect(() => {
		getDetail(activityId).then((res) => {
			getOptionList(res);
		});
	}, []);

	const [dataSource, setDataSource] = useState([]);

	const [params, setParams] = useState({});
	const searchBtn = () => {
		setParams({ ...form.getFieldValue() });
	};

	// 获取表格数据
	const getTableData = () => {
		getActivityRatingSession({
			...params,
			activityId: activityId,
		}).then((res) => {
			setDataSource(res.data || []);
		});
	};

	useEffect(() => {
		getTableData();
	}, [params]);

	const Comp3FormModalRef = useRef();
	const Comp3DrawerDetailRef = useRef();

	const ModalFormImportRef = useRef();

	// 导入弹窗显示
	const importModalShow = () => {
		ModalFormImportRef.current.setOpen(true);
		ModalFormImportRef.current.setTitle('企业场次分配导入');
	};

	// 导入
	const handelImport = (formData) => {
		formData.append('id', activityId);
		importActivityRatingSessionEntrant(formData).then(() => {
			message.success('导入成功');
			getTableData();
		});
	};

	const toRanking = (data = {}) => {
		//  * @param {number} params.ratingSessionId 场次唯一标识ID
		// * @param {number} params.rankingType 排名类型，1并列跳跃排名 2并列连续排序 3连续排名
		rankingActivityRatingSessionEntrant({
			ratingSessionId: data.id || '',
			rankingType: '1',
		}).then(() => {
			linkTo(`/competition/competitionManage/ScoreRanking?activitySessionId=${data.id}&activityId=${activityId}`);
		});
	};
	return (
		<div className="flex-sub">
			{/* 筛选条件 开始 */}
			<div className="flex margin-bottom-16 padding-bottom-16 border-bottom-e5e6eb">
				<div className="flex-sub padding-right-16 border-right-e5e6eb">
					<Form
						form={form}
						labelCol={{
							style: { width: '68px' },
						}}
						labelAlign="left"
						className="form-filter"
						initialValues={{}}
					>
						<Row gutter={[16, 16]}>
							<Col xs={24} sm={24} md={12} lg={8}>
								<Form.Item label="行业" name="industryId">
									<Select options={industryIdOptions} placeholder="请选择行业" allowClear />
								</Form.Item>
							</Col>
							<Col xs={24} sm={24} md={12} lg={8}>
								<Form.Item label="组别" name="activityGroupId">
									<Select options={activityGroupIdOptions} placeholder="请选择组别" allowClear />
								</Form.Item>
							</Col>
							<Col xs={24} sm={24} md={12} lg={8}>
								<Form.Item label="场次名称" name="name">
									<Input placeholder="请输入场次名称" allowClear />
								</Form.Item>
							</Col>
							<Col xs={24} sm={24} md={12} lg={8}>
								<Space>
									<Button
										type="primary"
										onClick={() => {
											Comp3FormModalRef.current.open();
										}}
									>
										<PlusOutlined />
										<span>新增</span>
									</Button>
									<Button type="primary" onClick={() => {}}>
										<ExportOutlined />
										<span>导出</span>
									</Button>
									<Button type="primary" onClick={importModalShow}>
										<ImportOutlined />
										<span>导入</span>
									</Button>
								</Space>
							</Col>
						</Row>
					</Form>
				</div>
				{/* direction='vertical' */}
				<Space direction="vertical" className="padding-left-16" size={16}>
					<Button
						className="height-32 font-size-14 bg-color-165dff color-ffffff"
						onClick={() => {
							searchBtn();
						}}
					>
						<SearchOutlined />
						<span>查询</span>
					</Button>

					<Button
						className="height-32 font-size-14 bg-color-f2f3f5 color-4e5969"
						onClick={() => {
							form.resetFields();
							getTableData();
						}}
					>
						<ReloadOutlined />
						<span>重置</span>
					</Button>
				</Space>
			</div>
			{/* 筛选条件 结束 */}

			{/* 表格列表 开始 */}
			<Table rowKey="id" dataSource={dataSource} pagination={false} scroll={{ x: 'max-content' }}>
				<Table.Column
					title="序号"
					key="index"
					dataIndex="index"
					width={60}
					render={(text, record, index) => {
						return index + 1;
					}}
				/>
				<Table.Column title="场次名称" dataIndex="name" key="name" />
				<Table.Column title="行业" dataIndex="industryName" key="industryName" />
				<Table.Column title="组别" dataIndex="groupName" key="groupName" />
				<Table.Column
					title="时间"
					dataIndex="time"
					key="time"
					render={(_, record) => {
						return (
							<>
								<div>{record.beginTime || ''}</div>
								<div>{record.beginTime && record.endTime && '到'}</div>
								<div>{record.endTime || ''}</div>
							</>
						);
					}}
				/>
				<Table.Column title="地点" dataIndex="site" key="site" />
				<Table.Column
					title="评分状态"
					dataIndex="scoreStatus"
					key="scoreStatus"
					width={90}
					render={(scoreStatus) => {
						return <Tag color={scoreStatus == 1 ? 'success' : 'default'}>{scoreStatus == 1 ? '开启' : '关闭'}</Tag>;
					}}
				/>
				<Table.Column
					title="评审组名称"
					dataIndex="ratingGroupName"
					key="ratingGroupName"
					render={(_, record) => <div>{(record.ratingGroup && record.ratingGroup.name) || ''}</div>}
				/>
				<Table.Column
					title="评委名称"
					dataIndex="ratingGroup"
					key="ratingGroup"
					render={(_, record) => (
						<Space wrap className="width-200">
							{record.ratingGroup &&
								[...(record.ratingGroup.ratingPerson || [])].map((ov, oi) => {
									return (
										<Tag color="#165dff">
											评委{oi + 1}-{ov.name}
										</Tag>
									);
								})}
						</Space>
					)}
				/>
				<Table.Column
					title="操作"
					dataIndex="options"
					key="options"
					align="center"
					fixed="right"
					width={210}
					render={(_, records) => (
						<Space>
							<Button
								onClick={() => {
									getActivityRatingSessionDetail({ id: records.id }).then((res) => {
										Comp3FormModalRef.current.open(records);
									});
								}}
								type="link"
								size="small"
							>
								编辑
							</Button>
							<Popconfirm
								title="提示"
								description="确定删除吗？"
								onConfirm={() => {
									deleteActivityRatingSession({
										id: records.id,
									}).then(() => {
										getTableData();
									});
								}}
								okText="确定"
								cancelText="取消"
							>
								<Button type="link" danger size="small">
									删除
								</Button>
							</Popconfirm>
							<Button
								onClick={() => {
									Comp3DrawerDetailRef.current.open(records);
								}}
								type="link"
								size="small"
							>
								进入场次
							</Button>
							<Button
								onClick={() => {
									toRanking(records);
								}}
								type="link"
								size="small"
							>
								赛室成绩
							</Button>
						</Space>
					)}
				/>
			</Table>
			{/* 表格列表 结束 */}

			<Comp3FormModal
				ref={Comp3FormModalRef}
				activityId={activityId}
				onChange={() => {
					getTableData();
				}}
				industryIdOptions={industryIdOptions}
				activityGroupIdOptions={activityGroupIdOptions}
			/>
			<Comp3DrawerDetail
				ref={Comp3DrawerDetailRef}
				onChange={() => {
					getTableData();
				}}
			/>

			{/* 导入 开始 */}
			<ModalForm
				ref={ModalFormImportRef}
				modelConfig={{
					styles: {
						body: {
							minHeight: 'unset',
						},
					},
				}}
				onOk={handelImport}
				FormComp={(props) => (
					<ImportForm
						ref={props.FormCompRef}
						fileName="file"
						tplName={`场次分配模板${dayjs().format('YYYY_MM_DD_HH_mm_ss')}`}
						tplUrl={`https://gbac-produce.oss-cn-shenzhen.aliyuncs.com/tip/competition/%E5%9C%BA%E6%AC%A1%E5%88%86%E9%85%8D%E6%A8%A1%E6%9D%BF.xlsx?${new Date().valueOf()}`}
					/>
				)}
			/>
			{/* 导入 结束 */}
		</div>
	);
};

export default Index;
