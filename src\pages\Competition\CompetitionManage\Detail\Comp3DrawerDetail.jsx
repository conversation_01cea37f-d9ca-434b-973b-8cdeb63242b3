import { useEffect, useRef, useState, forwardRef, useImperativeHandle } from 'react';
import {
	Tabs,
	Radio,
	Pagination,
	Space,
	Table,
	Form,
	Row,
	Col,
	Input,
	Select,
	Switch,
	Modal,
	Button,
	DatePicker,
	Popconfirm,
	Tag,
	Drawer,
	InputNumber,
	message,
} from 'antd';
import Permission from '@/components/Permission';
import { ExportOutlined, SearchOutlined, ReloadOutlined } from '@ant-design/icons';
import { useRouterLink } from '@/hook/useRouter/index';
import {
	getActivityDetail,
	getActivityRatingSessionScoreInfo,
	enableActivityRatingSessionScoreStatus,
	getActivityRatingSessionDetail,
	saveRatingSessionEntrantSequence,
	exportActivityRatingSessionScoreInfo,
} from '@/api/Competition/CompetitionManage/index';
import RegistrationDetails from './RegistrationDetails';
import { download, formatToTwoDecimalPlaces } from '@/utils/common';
import dayjs from 'dayjs';

const Index = forwardRef((props, ref) => {
	const { linkTo, searchParams } = useRouterLink();

	const activityId = searchParams.get('id');
	const [open, setOpen] = useState(false);
	const [activityDetail, setActivityDetail] = useState(false);
	const showDrawer = () => {
		setOpen(true);
	};
	const onClose = () => {
		setOpen(false);
		setDataSource([]);
		setCurRow({});
		setTableHeader([]);
		setSorting(false);
		props.onChange && props.onChange();
	};
	const [form] = Form.useForm();

	const inputVal = Form.useWatch('inputVal', form);

	const [dataSource, setDataSource] = useState([]);

	const searchBtn = () => {
		getTableData();
	};
	const resetBtn = () => {
		form.resetFields();
		getTableData();
	};

	const getDetail = (id) => {
		return new Promise((resolve) => {
			getActivityDetail({
				id,
			}).then((res) => {
				setActivityDetail(res.data || {});
				resolve();
			});
		});
	};

	const [tableHeader, setTableHeader] = useState([]);
	// 获取表格数据
	const getTableData = () => {
		getActivityRatingSessionScoreInfo({
			id: curRow.id,
		}).then((res) => {
			const list = (res.data || [])
				.sort((a, b) => {
					return (a.sequence || 99999999) - (b.sequence || 99999999);
				})
				.map((item) => {
					const ratingPerson = ((curRow.ratingGroup && curRow.ratingGroup.ratingPerson) || []).map((ov) => ov.userId);
					const entrantScores = [];
					ratingPerson.forEach((userId) => {
						const find = item.entrantScores.find((oc) => oc.userId == userId);
						entrantScores.push(find);
					});
					let score = item.score;
					// 找到 有没有 没有评分的
					const find = (item.entrantScores || []).find((ov) => {
						if (ov.abandonStatus === null && ov.avoidanceStatus === null && ov.score === null) {
							return true;
						}
						return false;
					});
					// 找到 有没有 没有评分的
					if (find) {
						score = '待评分';
					}
					if (item.status == 1) {
						score = '弃赛';
					}
					return {
						...item,
						entrantScores,
						score: formatToTwoDecimalPlaces(score),
					};
				});
			setDataSource(list);
			if (res.data && res.data.length) {
				setTableHeader(
					(list[0].entrantScores || []).map((ov, oi) => {
						return {
							title: `评委${oi + 1}（${ov.userName}）`,
							key: `userName-${oi + 1}`,
							dataIndex: `userName-${oi + 1}`,
						};
					})
				);
			}
		});
	};
	const [reFlash, setReFlash] = useState(new Date().valueOf());
	useEffect(() => {
		if (open) {
			getDetail(activityId).then(() => {
				getTableData();
			});
		}
	}, [open, reFlash]);

	const openDrawer = (data = {}) => {
		getActivityRatingSessionDetail({ id: data.id }).then((res) => {
			setCurRow(res.data || {});
			setOpen(true);
		});
	};

	const [curRow, setCurRow] = useState({});
	useImperativeHandle(ref, () => {
		return {
			open: (data = {}) => {
				openDrawer(data);
			},
			close: () => {
				onClose();
			},
		};
	});

	const RegistrationDetailsRef = useRef();

	const changeStatus = (scoreStatus) => {
		// 1开启评分 2已完成评分
		enableActivityRatingSessionScoreStatus({ ratingSessionId: curRow.id, scoreStatus }).then(() => {
			openDrawer(curRow);
			setReFlash(new Date().valueOf());
		});
	};
	const [sorting, setSorting] = useState(false);

	// 设置路演 顺序
	const setSequence = () => {
		if (!sorting) {
			resetBtn();
			setSorting(!false);
		} else {
			const seen = [];
			const findIndex = dataSource.findIndex((item) => {
				const find = seen.find((ov) => item.sequence == ov.sequence);
				if (find) {
					return true;
				} else {
					seen.push(item);
					return false;
				}
			});
			if (findIndex > -1) {
				message.warning(`重复了 请检查第${findIndex + 1}行`);
			} else {
				saveRatingSessionEntrantSequence(
					dataSource.map((ov) => {
						return {
							ratingSessionEntrantId: ov.id,
							sequence: ov.sequence,
						};
					})
				).then(() => {
					message.success(`保存成功`);
					openDrawer(curRow);
					setReFlash(new Date().valueOf());
					setSorting(false);
				});
			}
		}
	};

	// 导出数据
	const exportData = () => {
		exportActivityRatingSessionScoreInfo({
			id: curRow.id,
		}).then((res) => {
			if (res.size > 0) {
				download.excel(res, `赛事成绩-${dayjs().format('YYYYMMDD_HH:mm')}`);
				message.success('导出成功');
			} else {
				message.error('导出失败');
			}
		});
	};

	return (
		<Drawer title="场次详情" width={'80%'} onClose={onClose} open={open}>
			<div className="flex margin-bottom-16 padding-bottom-16 border-bottom-e5e6eb justify-start align-center">
				<div className="flex-sub line-height-32">
					{activityDetail.name}-{curRow.industryName}-{curRow.groupName}-{curRow.name}
				</div>
				{/* 评分开启状态(0:未开启,1开启评分 2已完成评分) */}
				{!sorting && curRow.scoreStatus != 1 && (
					<Button className="height-32 font-size-14 bg-color-165dff color-ffffff margin-left-20" onClick={() => changeStatus(1)}>
						开始场次评分
					</Button>
				)}
				{!sorting && curRow.scoreStatus == 1 && (
					<Button className="height-32 font-size-14 bg-color-165dff color-ffffff margin-left-20" onClick={() => changeStatus(2)}>
						结束场次评分
					</Button>
				)}
			</div>
			{/* 筛选条件 开始 */}
			<div className="margin-bottom-16 padding-bottom-16 border-bottom-e5e6eb">
				<Form
					form={form}
					labelCol={{
						style: { width: '68px' },
					}}
					labelAlign="left"
					className="form-filter"
					initialValues={{}}
				>
					<Row gutter={[16, 16]}>
						<Col xs={24} sm={24} md={12} lg={8}>
							<Form.Item label="单位名称" name="inputVal">
								<Input placeholder="请输入单位名称" allowClear disabled={sorting} />
							</Form.Item>
						</Col>
						<Col xs={24} sm={24} md={12} lg={8}>
							<Space>
								{!sorting && (
									<>
										{/* <Button
											className='height-32 font-size-14 bg-color-165dff color-ffffff'
											onClick={() => {
												searchBtn();
											}}
										>
											<SearchOutlined />
											<span>查询</span>
										</Button> */}

										<Button
											className="height-32 font-size-14 bg-color-f2f3f5 color-4e5969"
											onClick={() => {
												resetBtn();
											}}
										>
											<ReloadOutlined />
											<span>重置</span>
										</Button>
									</>
								)}

								{dataSource.length > 0 && (
									<Button
										className="height-32 font-size-14 bg-color-165dff color-ffffff"
										onClick={() => {
											setSequence();
										}}
									>
										{/* <ReloadOutlined /> */}
										{!sorting && <span>设置路演顺序</span>}
										{sorting && <span>保存路演顺序</span>}
									</Button>
								)}

								{sorting && (
									<Button
										className="height-32 font-size-14 bg-color-f2f3f5 color-4e5969"
										onClick={() => {
											setSorting(false);
										}}
									>
										{sorting && <span>取消</span>}
									</Button>
								)}

								{!sorting && (
									<>
										<Button
											className="height-32 font-size-14 bg-color-165dff color-ffffff"
											onClick={() => {
												exportData();
											}}
										>
											<ExportOutlined />
											<span>导出数据</span>
										</Button>
									</>
								)}
							</Space>
						</Col>
					</Row>
				</Form>
			</div>
			{/* 筛选条件 结束 */}
			{/* 表格列表 开始 */}
			<Table
				rowKey="signUpId"
				dataSource={dataSource.filter((ov) => {
					if (inputVal) {
						return ov.entName.includes(inputVal);
					} else {
						return true;
					}
				})}
				pagination={false}
				scroll={{ x: 'max-content' }}
			>
				<Table.Column
					title="序号"
					key="index"
					dataIndex="index"
					width={60}
					render={(text, record, index) => {
						return index + 1;
					}}
				/>
				<Table.Column title="单位名称" dataIndex="entName" key="entName" />
				<Table.Column title="统一社会信用代码" dataIndex="entUscc" key="entUscc" />
				{tableHeader.map((ov, oi) => {
					return (
						<Table.Column
							title={ov.title}
							dataIndex={ov.dataIndex}
							key={ov.dataIndex}
							align="center"
							render={(_, records) => {
								const info = records.entrantScores && records.entrantScores[oi];
								return info ? (
									<div className={info.score ? 'color-165dff' : 'color-ff7d00'}>
										{formatToTwoDecimalPlaces(info.score) ||
											(info.avoidanceStatus == 1 ? '回避' : info.abandonStatus == 1 ? '弃赛' : '暂未评分')}
									</div>
								) : (
									'--'
								);
							}}
						/>
					);
				})}
				<Table.Column title="最终得分" dataIndex="score" key="score" />
				<Table.Column
					title="路演顺序"
					dataIndex="sequence"
					key="sequence"
					render={(_, records, index) => {
						return (
							<>
								{!sorting && records.sequence}
								{sorting && (
									<InputNumber
										min={0}
										max={99999999}
										value={records.sequence || 0}
										onChange={(val) => {
											setDataSource(
												dataSource.map((ov) => {
													return {
														...ov,
														sequence: ov.signUpId == records.signUpId ? val || 0 : ov.sequence || 0,
													};
												})
											);
										}}
										className="width-90"
									/>
								)}
							</>
						);
					}}
				/>
				{!sorting && (
					<Table.Column
						title="操作"
						dataIndex="options"
						key="options"
						align="center"
						fixed="right"
						width={curRow.scoreStatus == 1 ? 210 : 100}
						render={(_, records) => (
							<Space>
								<Button
									onClick={() => {
										RegistrationDetailsRef.current.open({ records, id: records.signUpId });
									}}
									type="link"
									size="small"
								>
									查看报名详情
								</Button>
								{curRow.scoreStatus == 1 && (
									<>
										<Button
											onClick={() => {
												linkTo(`/competition/competitionManage/ProjectRating?signUpId=${records.signUpId}`);
											}}
											type="link"
											size="small"
										>
											进入项目评分
										</Button>
										<Button
											onClick={() => {
												linkTo(`/competition/competitionManage/ProjectRating?signUpId=${records.signUpId}&isReset=1`);
											}}
											type="link"
											size="small"
										>
											修改评分
										</Button>
									</>
								)}
							</Space>
						)}
					/>
				)}
			</Table>
			{/* 表格列表 结束 */}

			<RegistrationDetails ref={RegistrationDetailsRef}></RegistrationDetails>
		</Drawer>
	);
});

export default Index;
