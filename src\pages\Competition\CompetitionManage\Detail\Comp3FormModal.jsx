import {
	useEffect,
	useRef,
	useState,
	forwardRef,
	useImperativeHandle,
} from 'react';
import {
	Tabs,
	Radio,
	Pagination,
	Space,
	Table,
	Form,
	Row,
	Col,
	Input,
	Select,
	Switch,
	Modal,
	Button,
	DatePicker,
	Popconfirm,
	Tag,
} from 'antd';
import {
	saveActivityRatingSession,
	getActivityRatingGroup,
} from '@/api/Competition/CompetitionManage/index';
import dayjs from 'dayjs';

// 查看详情
const Comp3FormModal = forwardRef((props = {}, ref) => {
	const [isModalOpen, setIsModalOpen] = useState(false);
	const [curRow, setCurRow] = useState({});

	const [form] = Form.useForm();
	const formRef = useRef();

	const { RangePicker } = DatePicker;

	const [ratingGroups, setRatingGroups] = useState([]);
	useEffect(() => {
		if (props.activityId) {
			getActivityRatingGroup({ id: props.activityId }).then((res) => {
				setRatingGroups(res.data || []);
			});
		}
	}, [props.activityId]);

	const submit = () => {
		form.validateFields().then((values) => {
			const params = {
				activityId: props.activityId || '', // 活动唯一标识ID
				name: values.name || '', // 场次名称
				industryId: values.industryId || '', // 行业
				groupId: values.groupId || '', // 组别
				beginTime:
					dayjs(values.beginTimeendTime[0]).format(
						'YYYY-MM-DD HH:mm:ss'
					) || '', // 开始时间
				endTime:
					dayjs(values.beginTimeendTime[1]).format(
						'YYYY-MM-DD HH:mm:ss'
					) || '', // 结束时间
				site: values.site || '', // 地点
				ratingGroupId: values.ratingGroupId || '', // 评审组唯一标识ID
				scoreStatus: values.scoreStatus ? 1 : 2, // 评分开启状态(0:未开启,1:开始 2 已停止)
				sortShow: values.sortShow || 0,
			};
			if (curRow.id) {
				params.id = curRow.id || ''; // 场次唯一标识ID
			}

			saveActivityRatingSession([params]).then(() => {
				reset();
				props.onChange && props.onChange();
			});
		});
	};

	const reset = () => {
		formRef.current &&
			formRef.current.resetFields &&
			formRef.current.resetFields();
		setIsModalOpen(false);
		setCurRow({});
	};

	useEffect(() => {
		if (isModalOpen) {
			const initParams = {
				name: curRow.name || '',
				industryId: curRow.industryId || '',
				groupId: curRow.groupId || '',
				beginTimeendTime: [
					(curRow.beginTime &&
						dayjs(curRow.beginTime, 'YYYY-MM-DD HH:mm:ss')) ||
					'',
					(curRow.endTime &&
						dayjs(curRow.endTime, 'YYYY-MM-DD HH:mm:ss')) ||
					'',
				],
				site: curRow.site || '',
				ratingGroupId: curRow.ratingGroupId || '',
				scoreStatus: curRow.scoreStatus == 1 ? true : false,
				sortShow: curRow.sortShow || 0,
			};

			form.setFieldsValue(initParams);
		}
	}, [isModalOpen]);

	useImperativeHandle(ref, () => {
		return {
			open: (data = {}) => {
				setCurRow(data);
				setIsModalOpen(true);
			},
			close: () => {
				setIsModalOpen(false);
			},
		};
	});

	return (
		<Modal
			title={`场次${curRow.id ? '修改' : '新增'}`}
			open={isModalOpen}
			maskClosable={false}
			onOk={() => {
				submit();
			}}
			onCancel={() => {
				reset();
			}}
			width={800}
		>
			<Form
				labelCol={{
					span: 6,
				}}
				wrapperCol={{
					span: 18,
				}}
				autoComplete='off'
				ref={formRef}
				form={form}
				initialValues={{
					scoreStatus: false,
				}}
			>
				<Form.Item
					label={`场次名称`}
					name='name'
					prop='name'
					rules={[
						{
							required: true,
						},
					]}
				>
					<Input placeholder='请输入场次名称' />
				</Form.Item>

				<Form.Item
					label='行业'
					name='industryId'
					rules={[
						{
							required: true,
						},
					]}
				>
					<Select
						options={props.industryIdOptions || []}
						placeholder='请选择行业'
						allowClear
					/>
				</Form.Item>
				<Form.Item
					label='组别'
					name='groupId'
					rules={[
						{
							required: true,
						},
					]}
				>
					<Select
						options={props.activityGroupIdOptions || []}
						placeholder='请选择组别'
						allowClear
					/>
				</Form.Item>
				<Form.Item
					label='地点'
					name='site'
					rules={[
						{
							required: true,
						},
					]}
				>
					<Input placeholder='请输入地点' />
				</Form.Item>
				<Form.Item
					label='时间'
					name='beginTimeendTime'
					rules={[
						{
							required: true,
						},
					]}
				>
					<RangePicker
						showTime
						format='YYYY-MM-DD HH:mm:ss'
						valueFormat='YYYY-MM-DD HH:mm:ss'
					/>
				</Form.Item>

				<Form.Item label='场次评分状态' name='scoreStatus'>
					<Switch checkedChildren='开启' unCheckedChildren='关闭' />
				</Form.Item>

				<Form.Item label='排名展示在评分展示页' name='sortShow'>
					<Radio.Group
						defaultValue={0}
					>
						<Radio value={1}>是</Radio>
						<Radio value={0}>否</Radio>
					</Radio.Group>
				</Form.Item>

				<Form.Item
					label='评分组'
					name='ratingGroupId'
					rules={[
						{
							required: true,
							message: '请选择评分组',
						},
					]}
				>
					<Radio.Group style={{ width: '100%' }}>
						<Table
							rowKey='id'
							dataSource={ratingGroups}
							pagination={false}
							style={{ width: '100%' }}
						>
							<Table.Column
								title='#'
								key='index'
								dataIndex='index'
								width={60}
								render={(text, record) => {
									return <Radio value={record.id}></Radio>;
								}}
							/>
							<Table.Column
								title='评审组名称'
								key='name'
								dataIndex='name'
								width={260}
							/>

							<Table.Column
								title='评委名称'
								key='ratingPerson'
								dataIndex='ratingPerson'
								render={(text, record, index) => {
									return (
										<Space wrap>
											{record.ratingPerson.map(
												(ov, oi) => {
													return (
														<Tag
															color='#165dff'
															key={oi}
														>
															评委{oi + 1}-
															{ov.name}
														</Tag>
													);
												}
											)}
										</Space>
									);
								}}
							/>
						</Table>
					</Radio.Group>
				</Form.Item>
			</Form>
		</Modal>
	);
});


export default Comp3FormModal;
