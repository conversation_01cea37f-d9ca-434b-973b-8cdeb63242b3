import { useEffect, useRef, useState, forwardRef, useImperativeHandle } from 'react';
import { Space, Table, Form, Row, Col, Input, Select, Modal, Button, message } from 'antd';
import Permission from '@/components/Permission';
import SignUpPreview from '@/components/Competition/SignUpPreview';
import { ExportOutlined, SearchOutlined, ReloadOutlined } from '@ant-design/icons';
import { useRouterLink } from '@/hook/useRouter/index';
import {
	pageActivitySignUp,
	getActivityRatingSession,
	getActivityDetail,
	getActivitySignUpById,
	batchAuditStatus,
	signUpExportList,
} from '@/api/Competition/CompetitionManage/index';
import { listDictItem } from '@/api/Competition/ConfigCenter/index';
import AuditStatus from '@/components/Competition/AuditStatus';

import { download } from '@/utils/common';
import dayjs from 'dayjs';

// 企业报名 查看详情
const DetailModal = forwardRef((props = {}, ref) => {
	const { searchParams } = useRouterLink();

	const [isModalOpen, setIsModalOpen] = useState(false);
	const [id, setId] = useState('');
	const [attachedList, setAttachedList] = useState([]);

	// 获取报名信息
	const getActivitySignUpData = () => {
		getActivitySignUpById({ id }).then((res = {}) => {
			const attachedList = res.data?.attachedList || [];

			attachedList.forEach((ov) => {
				// 公司名称 统一社会信用代码 读取外面的
				if (ov.enName === 'name') {
					ov.fieldValue = res.data?.name;
				}
				if (ov.enName === 'creditCode') {
					ov.fieldValue = res.data?.creditCode;
				}
			});

			setAttachedList(attachedList);
		});
	};

	// 关闭
	const close = () => {
		setId('');
		setAttachedList([]);
		setIsModalOpen(false);
	};

	useEffect(() => {
		if (isModalOpen && id) {
			getActivitySignUpData();
		}
	}, [isModalOpen]);

	const [row, setRow] = useState({});
	useImperativeHandle(ref, () => {
		return {
			open: (data = {}) => {
				setId(data.id);
				setRow(data);
				setIsModalOpen(true);
			},
			close: () => {
				setRow({});
				close();
			},
		};
	});

	return (
		<Modal
			title="报名详情"
			classNames={{ body: 'scrollbar' }}
			width={900}
			styles={{
				body: {
					maxHeight: '70vh',
					minHeight: '50vh',
					overflowY: 'auto',
					overflowX: 'hidden',
				},
			}}
			open={isModalOpen}
			maskClosable={false}
			destroyOnClose={!false}
			onOk={() => {
				close();
			}}
			onCancel={() => {
				close();
			}}
		>
			<>
				<SignUpPreview templateId={searchParams.get('entryFormTemplateId')} attachedList={attachedList} />
				{[1, 2, 3].includes(row.status) && props.child && (
					<div className="flex align-start justify-start line-height-24 padding-tb-16 padding-lr-24">
						<div className="flex-shrink text-align-right">审核：</div>
						<div className="flex-sub">
							<props.child records={row} />
						</div>
					</div>
				)}
			</>
		</Modal>
	);
});

export default DetailModal;
