import Breadcrumb from '@/components/Breadcrumb';
import { useEffect, useRef, useState } from 'react';
import {
	Tabs,
	Radio,
	Pagination,
	Space,
	Table,
	Form,
	Row,
	Col,
	Input,
	Select,
	Switch,
	Modal,
	Button,
} from 'antd';
import ConditionMapList from '@/components/Competition/ConditionMapList/index';
import Permission from '@/components/Permission';
import {
	ExportOutlined,
	SearchOutlined,
	ReloadOutlined,
} from '@ant-design/icons';
import { useRouterLink } from '@/hook/useRouter/index';
import { getActivityDetail } from '@/api/Competition/CompetitionManage/index';
import Comp1 from './Comp1';
import Comp2 from './Comp2';
import Comp3 from './Comp3';

const Index = () => {
	const { linkTo, searchParams, setSearchParams } = useRouterLink();
	const id = searchParams.get('id');
	const title = searchParams.get('title');
	const entryFormTemplateId = searchParams.get('entryFormTemplateId');
	const [curTab, setCurTab] = useState(searchParams.get('curTab'));
	const onChange = (key) => {
		setCurTab(key);
	};
	
	const [detail, setDetail] = useState({});
	const getDetail = (id) => {
		return new Promise((resolve) => {
			getActivityDetail({
				id,
			}).then((res) => {
				setDetail(res.data || {}); 
			});
		});
	};
	useEffect(() => {
		if (id) {
			getDetail(id);
		}
	}, []);

	useEffect(() => {
		setSearchParams(
			{
				curTab,
				id,
				entryFormTemplateId
			},
			{ replace: true }
		);
	}, [curTab]);

	return (
		<div className='flex-sub flex flex-direction-column margin-top-16'>
			<Breadcrumb
				icon='icon-competitionManage'
				list={[
					{
						name: '赛事管理',
						link: '/competition/competitionManage',
					},
				]}
				name={detail.name || ''}
			/>
			<div className='flex-sub margin-lr-20 margin-bottom-20 margin-top-4 padding-20 bg-color-ffffff border-radius-4'>
				<Tabs
					activeKey={curTab}
					items={[
						{
							key: '1',
							label: '赛事信息',
							children: '',
						},
						{
							key: '2',
							label: '查看报名',
							children: '',
						},
						{
							key: '3',
							label: '查看场次',
							children: '',
						},
						// {
						// 	key: '4',
						// 	label: '成绩管理',
						// 	children: '',
						// },
						// {
						// 	key: '5',
						// 	label: '证书配置',
						// 	children: '',
						// },
					]}
					onChange={onChange}
				/>
				<div className='position-relative'>
					{curTab == '1' && <Comp1 id={id} />}
					{curTab == '2' && <Comp2 />}
					{curTab == '3' && <Comp3 />}
				</div>
			</div>
		</div>
	);
};

export default Index;
