import { useEffect, useRef, useState, forwardRef, useImperativeHandle } from 'react';
import {
	Tabs,
	Radio,
	Pagination,
	Space,
	Table,
	Form,
	Row,
	Col,
	Input,
	Select,
	Switch,
	Modal,
	Button,
	DatePicker,
	Popconfirm,
	Tag,
	Popover,
	Dropdown,
	ConfigProvider,
	message,
} from 'antd';
import {
	getActivitySignUpById,
	getActivityDetail,
	getActivityRatingSessionDetail,
	getActivityRatingSessionScoreInfo,
	removeRatingSessionEntrantScore,
	rankingActivityRatingSessionEntrant,
} from '@/api/Competition/CompetitionManage/index';
import { ReloadOutlined, SyncOutlined, ExclamationCircleOutlined } from '@ant-design/icons';
import dayjs from 'dayjs';
import { getImageSrc } from '@/assets/images/index';
import { useRouterLink } from '@/hook/useRouter';
import PreViewFile from '@/components/Competition/PreViewFile/index';
import './index.scss';

const Index = (props = {}) => {
	const { linkTo, searchParams, setSearchParams } = useRouterLink();
	const isReset = searchParams.get('isReset') || 0;
	const [signUpDetail, setSignUpDetail] = useState({});
	const [activityDetail, setActivityDetail] = useState({});
	const [sessionDetail, setSessionDetail] = useState({});
	const [signUpOptions, setSignUpOptions] = useState([]);
	const [curSignUpSession, setCurSignUpSession] = useState({});
	const [attachedList, setAttachedList] = useState([]);
	// 获取报名信息
	const getActivitySignUpData = (signUpId = '') => {
		getActivitySignUpById({ id: signUpId }).then((resSignUp) => {
			setSignUpDetail(resSignUp.data || {});
			getActivityDetail({
				id: resSignUp.data.activityId,
			}).then((resActivity) => {
				setActivityDetail(resActivity.data || {});
				const attachedShowConf = (resActivity.data.attachedShowConf || []).map((ov) => ov.fieldName);
				if (attachedShowConf && attachedShowConf.length) {
					setAttachedList(
						resSignUp.data.attachedList.filter((ov) => {
							return attachedShowConf.includes(ov.enName);
						})
					);
				}
				getActivityRatingSessionDetail({
					id: resSignUp.data.activitySessionId,
				}).then((resp) => {
					setSessionDetail(resp.data || {});

					// 获取 当前 场次 所有的 参赛方
					getActivityRatingSessionScoreInfo({
						id: resSignUp.data.activitySessionId,
					}).then((res) => {
						const list = (res.data || [])
							.sort((a, b) => (a.sequence || 0) - (b.sequence || 0))
							.map((item) => {
								const ratingPerson = ((resp.data.ratingGroup && resp.data.ratingGroup.ratingPerson) || []).map((ov) => ov.userId);
								const entrantScores = [];
								ratingPerson.forEach((userId) => {
									const find = item.entrantScores.find((oc) => oc.userId == userId);
									entrantScores.push(find);
								});
								return {
									...item,
									entrantScores,
								};
							});
						setSignUpOptions(list);
						const curItem = list.find((ov) => ov.signUpId == signUpId);
						const entrantScores = [...(curItem.entrantScores || [])];
						console.log('🚀 ~ getActivitySignUpById ~ entrantScores:', entrantScores);
						if (resActivity.data.calculationMethod == 2) {
							// 找到 没有评分的
							const find = curItem.entrantScores.find((ov) => {
								if (ov.abandonStatus === null && ov.avoidanceStatus === null && ov.score === null) {
									return true;
								}
								return false;
							});
							if (!find && curItem.entrantScores.length) {
								const sortList = curItem.entrantScores
									.map((ov) => {
										if (ov.abandonStatus == 1 || ov.avoidanceStatus == 1) {
											ov.sort = 99999999;
										} else {
											ov.sort = ov.score || 0;
										}
										return ov;
									})
									.sort((a, b) => a.sort - b.sort);
								const lag = sortList.filter((ov) => ov.sort != 99999999);
								if (lag.length >= 2) {
									// 第一个是最低分
									const firstItem = entrantScores.find((ov) => ov.id == lag[0].id);
									firstItem.sortBgClass = 'expert-score-bg-low';
									// 最后一个是最高分
									const lastItem = entrantScores.find((ov) => ov.id == lag[lag.length - 1].id);
									lastItem.sortBgClass = 'expert-score-bg-top';
								}
							}
						}
						setCurSignUpSession({ ...curItem, entrantScores });
					});
				});
			});
		});
	};
	useEffect(() => {
		getActivitySignUpData(searchParams.get('signUpId'));
	}, []);
	const signUpIdChange = (ov = {}) => {
		setSearchParams(
			{
				isReset,
				signUpId: ov.signUpId,
			},
			{ replace: true }
		);
		getActivitySignUpData(ov.signUpId);
	};
	const [curTime, setCurTime] = useState('');
	useEffect(() => {
		const timerID = setInterval(
			() => {
				setCurTime(dayjs().format('YYYY年MM月DD日 HH:mm:ss'));
			},
			1000 // 每秒调用一次tick
		);
		return () => {
			clearInterval(timerID); // 清除定时器，防止内存泄漏
		};
	}, []);

	const confirmReScore = (item = {}, index) => {
		if (!item.id) {
			return;
		}
		Modal.confirm({
			title: '重新评分确认',
			icon: <ExclamationCircleOutlined />,
			content: `是否对“评委${index}”的评分进行清零，清零后请提示评委重新
			评分?`,
			onOk() {
				removeRatingSessionEntrantScore({
					scoreId: item.id,
				}).then((res) => {
					message.success('重置成功');
					getActivitySignUpData(searchParams.get('signUpId'));
				});
			},
			onCancel() {
				console.log('Cancel');
			},
		});
	};

	const PreViewFileRef = useRef();
	const previewBtn = (item = {}) => {
		const fieldValueList = item.fieldValue.split('.');
		PreViewFileRef.current.open(item.fieldValue, fieldValueList.pop());
	};
	const showScore = (item = {}, list = []) => {
		// 找到 有没有 没有评分的
		const find = list.find((ov) => {
			if (ov.abandonStatus === null && ov.avoidanceStatus === null && ov.score === null) {
				return true;
			}
			return false;
		});
		// 修改评分
		if (isReset == 1) {
			if (item.abandonStatus == 1) {
				return '弃赛';
			}
			if (item.avoidanceStatus == 1) {
				return '回避';
			}
			if (item.score === null) {
				return '待评分';
			}
			if (item.score - 0 < 10) {
				return '0' + (item.score - 0).toFixed(2);
			} else {
				return (item.score - 0).toFixed(2);
			}
			return;
		}
		// 找到 有没有 没有评分的
		if (find) {
			return '待评分';
		} else {
			if (item.abandonStatus == 1) {
				return '弃赛';
			}
			if (item.avoidanceStatus == 1) {
				return '回避';
			}
			if (item.score === null) {
				return '待评分';
			}
			if (item.score - 0 < 10) {
				return '0' + (item.score - 0).toFixed(2);
			} else {
				return (item.score - 0).toFixed(2);
			}
		}
	};

	const showTotalScore = () => {
		// 找到 有没有 没有评分的
		const find = (curSignUpSession.entrantScores || []).find((ov) => {
			if (ov.abandonStatus === null && ov.avoidanceStatus === null && ov.score === null) {
				return true;
			}
			return false;
		});
		// 找到 有没有 没有评分的
		if (find) {
			return '00.00';
		}
		if (!curSignUpSession.score) {
			return '00.00';
		} else {
			if (curSignUpSession.score - 0 < 10) {
				return '0' + (curSignUpSession.score - 0).toFixed(2);
			} else {
				return (curSignUpSession.score - 0).toFixed(2);
			}
		}
	};

	const toRanking = () => {
		//  * @param {number} params.ratingSessionId 场次唯一标识ID
		// * @param {number} params.rankingType 排名类型，1并列跳跃排名 2并列连续排序 3连续排名
		rankingActivityRatingSessionEntrant({
			ratingSessionId: signUpDetail.activitySessionId || '',
			rankingType: '1',
		}).then(() => {
			linkTo(`/competition/competitionManage/ScoreRanking?activitySessionId=${signUpDetail.activitySessionId}&activityId=${activityDetail.id}`);
		});
	};
	return (
		<div
			className="position-fixed top-0 left-0 right-0 bottom-0 z-index-12 ProjectRating-bg background-no-repeat background-position-center-top background-size-100-100 flex flex-direction-column align-stretch user-select-none"
			style={{
				backgroundImage: `url(${
					activityDetail.largeScreenBg ||
					'https://gbac-produce.oss-cn-shenzhen.aliyuncs.com/static-assets/gbac-bidmgt-admfrontend/competition/projectRating-20250529.jpg' // 'https://gbac-produce.oss-cn-shenzhen.aliyuncs.com/tip/projectRating-202407171649.png'
				})`,
			}}
		>
			<div className="ProjectRating-header-bg background-no-repeat background-position-center-top background-size-100-100 height-100 padding-left-50 padding-right-10 flex justify-between align-start padding-top-16 border-box flex-shrink">
				<div className="width-70 flex align-start justify-between a" onClick={() => linkTo(-1)}>
					<img src={getImageSrc('@/assets/images/competition/icon_back.png')} alt="返回" className="width-24 height-24" />
					<div className="font-size-18 line-height-26 color-ffffff">返回</div>
				</div>
				<div className="flex-sub text-align-center line-height-40 tltle font-size-30 font-bold letter-spacing-4 text-cut margin-left-90">
					{activityDetail.name || ''}
				</div>
				<div className="width-200 font-size-16 line-height-26 color-ffffff">{curTime}</div>
			</div>

			<div className="padding-lr-50 flex justify-between align-center">
				<Dropdown
					trigger={['click']}
					overlayClassName="dropdown-box max-height-400 overflow-hidden overflowY-auto"
					menu={{
						items: [...signUpOptions].map((ov, oi) => {
							return {
								key: `${ov.signUpId}`,
								label: (
									<div
										className="line-height-20 height-40 flex align-center justify-start hover-bg-color-ffffff33 padding-lr-8"
										onClick={() => signUpIdChange(ov)}
									>
										{oi + 1}.{ov.entName || ''}
									</div>
								),
							};
						}),
					}}
					placement="bottomLeft"
				>
					<div className="min-width-280 height-60 bg-square background-no-repeat background-position-center-top background-size-100-100 flex align-center justify-center a padding-lr-30 border-box">
						<div className="font-size-18 line-height-26 color-ffffff font-weight-500 margin-right-12">{sessionDetail.name || ''}</div>
						<img src={getImageSrc('@/assets/images/competition/arrow-down-ffffff.png')} alt="video" className="width-24 height-24" />
					</div>
				</Dropdown>
				<div
					className="flex justify-between align-center color-ffffff font-size-24 a margin-left-26"
					onClick={() => {
						getActivitySignUpData(signUpDetail.id);
					}}
				>
					<ReloadOutlined className="font-size-24 font-bold" />
					<div className="line-height-30 margin-left-10">刷新</div>
				</div>
				<div className="flex-sub"></div>
				{attachedList.map((ov) => {
					return (
						<div
							className="width-180 height-60 bg-square background-no-repeat background-position-center-top background-size-100-100 flex align-center justify-center  margin-left-20 a"
							key={ov.enName}
							onClick={() => {
								previewBtn(ov);
							}}
						>
							{(['ppt/pptx', 'pptx'].includes(ov.fieldValue.split('.').reverse()[0]) && (
								<img
									src={getImageSrc('@/assets/images/competition/icon-ppt.png')}
									alt="video"
									className="width-32 height-32 flex-shrink"
								/>
							)) ||
								''}
							{(['mp4', 'mov', 'avi'].includes(ov.fieldValue.split('.').reverse()[0]) && (
								<img
									src={getImageSrc('@/assets/images/competition/icon-video.png')}
									alt="video"
									className="width-32 height-32 flex-shrink"
								/>
							)) ||
								''}
							{(['pdf'].includes(ov.fieldValue.split('.').reverse()[0]) && (
								<img
									src={getImageSrc('@/assets/images/competition/icon-pdf.png')}
									alt="video"
									className="width-32 height-32 flex-shrink"
								/>
							)) ||
								''}
							<div className="font-size-18 line-height-26 color-ffffff font-weight-500 margin-left-12 text-cut-2">{ov.cnName}</div>
						</div>
					);
				})}
			</div>

			<div className="flex-sub"></div>
			{/* 最终分数 开始 */}
			<div className="padding-lr-50 text-align-center line-height-60 company font-size-56 font-bold letter-spacing-2 text-cut">
				{signUpDetail.name}
			</div>
			<div className="flex-sub"></div>
			<div className="flex align-center justify-between">
				<div className="flex-sub color-ffffff text-align-right font-size-36 font-weight-500">最终得分：</div>
				<div className="flex justify-between align-center">
					{`${showTotalScore()}`.split('').map((ov, oi) => {
						return (
							<div
								key={oi}
								className={`score-bg background-no-repeat background-position-center-top background-size-100-100 height-96 text-align-center overflow-hidden color-66ffff font-size-70 font-bold margin-lr-8 ${
									ov == '.' ? 'width-50' : 'width-80'
								}`}
							>
								{ov}
							</div>
						);
					})}
				</div>
				<div className="flex-sub"></div>
			</div>
			{/* 最终分数 结束 */}
			<div className="flex-sub"></div>

			{/* 评委列表 开始 */}
			<div className="flex justify-around align-center">
				{(curSignUpSession.entrantScores || []).map((ov, oi) => {
					return (
						<div className="text-align-center overflow-hidden margin-lr-4" key={oi}>
							{activityDetail.ratingPersonLogoShow == 1 && (
								<div className="width-100 height-100 expert-face-bg background-no-repeat background-position-center-top background-size-100-100 margin-0-auto flex align-center justify-center">
									<img
										src={ov.wxAvatarUrl || getImageSrc('@/assets/images/Public/defaultAvatar.png')}
										alt="face"
										className="width-92 height-92"
									/>
								</div>
							)}
							<div className="width-170 height-40 expert-name-bg background-no-repeat background-position-center-top background-size-100-100 line-height-40 text-cut font-size-18 color-ffffff font-weight-500 margin-tb-12">
								评委{oi + 1}
								{activityDetail.ratingPersonNameShow == 1 ? `-${ov.userName}` : ''}
							</div>
							<div
								className={`width-170 height-60 background-no-repeat background-position-center-top background-size-100-100 line-height-60 text-cut font-size-40 color-ffffff font-bold margin-top-12 ${
									ov.sortBgClass || 'expert-score-bg '
								}`}
							>
								{showScore(ov, curSignUpSession.entrantScores)}
							</div>
							{isReset == 1 ? (
								<>
									{(ov.id && (
										<div className="flex align-center justify-center">
											<div
												className="height-30 line-height-30 font-size-18 color-ffffff font-weight-500 margin-0-auto margin-top-8 a flex align-center justify-center"
												onClick={() => {
													confirmReScore(ov, oi + 1);
												}}
											>
												<SyncOutlined className="font-size-20" />
												<div className="margin-left-10">重新评分</div>
											</div>
										</div>
									)) ||
										''}
									{(!ov.id && <div className="height-30 min-height-30"></div>) || ''}
								</>
							) : null}
						</div>
					);
				})}
			</div>
			{/* 评委列表 开始 */}
			<div className="flex-sub"></div>

			<div className="flex justify-between align-center padding-lr-50">
				<div className="info-box ">
					{(activityDetail.actIndustryShow == 1 || activityDetail.actGroupShow == 1) && (
						<div className="info-bg padding-left-30 padding-right-50 padding-tb-10 color-ffffff font-size-18 line-height-28 font-bold">
							{activityDetail.actIndustryShow == 1 && <div className="">行业：{sessionDetail.industryName || ''}</div>}

							{activityDetail.actGroupShow == 1 && <div>组别：{sessionDetail.groupName || ''}</div>}
						</div>
					)}
				</div>
				{(sessionDetail.sortShow == 1 && (
					<div
						className="width-180 height-60 bg-square background-no-repeat background-position-center-top background-size-100-100 font-size-18 line-height-60 color-ffffff font-weight-500 flex align-center justify-center a"
						onClick={() => toRanking()}
					>
						<img src={getImageSrc('@/assets/images/competition/icon-score.png')} alt="score" className="width-32 height-32" />
						<div className="margin-left-12">赛室成绩</div>
					</div>
				)) ||
					''}
			</div>

			<div className="flex-sub"></div>

			<PreViewFile ref={PreViewFileRef} />
		</div>
	);
};

export default Index;
