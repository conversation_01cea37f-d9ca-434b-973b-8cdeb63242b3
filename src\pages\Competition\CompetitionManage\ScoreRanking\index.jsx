import { useEffect, useRef, useState, forwardRef, useImperativeHandle } from 'react';
import {
	Tabs,
	Radio,
	Pagination,
	Space,
	Table,
	Form,
	Row,
	Col,
	Input,
	Select,
	Switch,
	Modal,
	Button,
	DatePicker,
	Popconfirm,
	Tag,
	Popover,
} from 'antd';
import {
	getActivitySignUpById,
	getActivityDetail,
	getActivityRatingSessionDetail,
	getActivityRatingSessionScoreInfo,
	getSignUpCnName,
} from '@/api/Competition/CompetitionManage/index';
import { ReloadOutlined, SyncOutlined, ExclamationCircleOutlined } from '@ant-design/icons';
import dayjs from 'dayjs';
import { getImageSrc } from '@/assets/images/index';
import { useRouterLink } from '@/hook/useRouter';
import { formatToTwoDecimalPlaces } from '@/utils/common';
import './index.scss';

const Index = (props = {}) => {
	const { linkTo, searchParams, setSearchParams } = useRouterLink();
	const activitySessionId = searchParams.get('activitySessionId');
	const activityId = searchParams.get('activityId');

	const [activityDetail, setActivityDetail] = useState({});
	const [sessionDetail, setSessionDetail] = useState({});
	const [signUpOptions, setSignUpOptions] = useState([]);
	// 获取报名信息
	const getData = (signUpId = '') => {
		getActivityDetail({
			id: activityId,
		}).then((res) => {
			const resActivity = res.data || {};
			setActivityDetail(resActivity);
			getAllCompanyName(resActivity);
		});
		getActivityRatingSessionDetail({
			id: activitySessionId,
		}).then((res) => {
			setSessionDetail(res.data || {});
		});
	};

	const getAllCompanyName = (resActivity = {}) => {
		// 获取 当前 场次 所有的 参赛方
		getActivityRatingSessionScoreInfo({
			id: activitySessionId,
		}).then((res) => {
			const list = (res.data || [])
				.sort((a, b) => {
					if (a.ranking == b.ranking) {
						return (a.sequence || 9999999) - (b.sequence || 9999999);
					} else {
						return (a.ranking || 9999999) - (b.ranking || 9999999);
					}
				})
				.map((item) => {
					let score = item.score;
					if (item.status == 1) {
						score = '弃赛';
					}
					return {
						...item,
						score: formatToTwoDecimalPlaces(score),
					};
				});

			// 展示 企业名称 or 项目名称
			if (resActivity.nameShowType == 2) {
				getSignUpCnName({ ids: list.map((ov) => ov.signUpId) }).then((res) => {
					const nameMap = res.data || {};
					list.forEach((ov) => {
						const projectName = nameMap[ov.signUpId];
						if (projectName) {
							ov.entName = projectName;
						}
					});
					setSignUpOptions(list);
				});
			} else {
				setSignUpOptions(list);
			}
		});
	}
	useEffect(() => {
		if (activitySessionId && activityId) {
			getData();
		}
	}, []);

	return (
		<div className="position-fixed top-0 left-0 right-0 bottom-0 z-index-12 ScoreRanking-bg background-no-repeat background-position-center-top background-size-100-100 overflow-hidden overflowY-auto">
			<div className="ScoreRanking-header-bg background-no-repeat background-position-center-top background-size-100-100 height-100 padding-lr-50 flex justify-between align-start padding-top-16 border-box flex-shrink position-sticky top-0">
				<div className="width-70 flex align-start justify-between a" onClick={() => linkTo(-1)}>
					<img src={getImageSrc('@/assets/images/competition/icon_back.png')} alt="返回" className="width-24 height-24" />
					<div className="font-size-18 line-height-26 color-ffffff">返回</div>
				</div>
				<div className="flex-sub text-align-center line-height-40 tltle font-size-30 font-bold letter-spacing-4 text-cut">
					{activityDetail.name || ''}
				</div>
				<div className="width-70"></div>
			</div>
			{/* 公司名称 开始 */}
			<div className="padding-lr-50 text-align-center line-height-50 company font-size-40 font-bold letter-spacing-2 text-cut margin-tb-20">
				{sessionDetail.name || ''}
			</div>
			{/* 公司名称 结束 */}

			<div className="flex align-center justify-between table-header margin-lr-150 color-ffffff font-size-18 font-bold">
				<div className="flex justify-center align-center height-60 width-220">路演顺序</div>
				<div className="flex justify-center align-center height-60 flex-sub">{activityDetail.nameShowType == 2 ? '项目名称' : '单位名称'}</div>
				<div className="flex justify-center align-center height-60 width-220">最终得分</div>
				<div className="flex justify-center align-center height-60 width-220">赛室排名</div>
			</div>
			<div className="margin-lr-150 table-header-line padding-top-2"></div>
			{signUpOptions.map((ov, oi) => {
				return (
					<div
						key={oi}
						className={`flex align-center justify-between margin-lr-150 color-ffffff font-size-16  line-height-20  ${oi % 2 == 0 ? 'table-row-even' : 'table-row-odd'
							}`}
					>
						<div className="flex justify-center align-center height-60 width-220">{ov.sequence || ''}</div>
						<div className="flex justify-center align-center height-60 flex-sub">{ov.entName || ''}</div>
						<div className="flex justify-center align-center height-60 width-220 color-66ffff">{ov.score}</div>
						<div className="flex justify-center align-center height-60 width-220">{ov.ranking || '-'}</div>
					</div>
				);
			})}
			<div className="flex justify-end align-center margin-lr-150 margin-tb-20 color-ffffff font-size-16">最终参赛成绩以官方公布为准。</div>
		</div>
	);
};

export default Index;
