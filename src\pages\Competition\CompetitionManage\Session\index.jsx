import Breadcrumb from '@/components/Breadcrumb';
import { useEffect, useRef, useState } from 'react';
import { Tabs, Radio, Pagination, Space, Table, Form, Row, Col, Input, Select, Switch, Modal, Button } from 'antd';
import Permission from '@/components/Permission';
import { ExportOutlined, SearchOutlined, ReloadOutlined, PlusOutlined } from '@ant-design/icons';
import { useRouterLink } from '@/hook/useRouter/index';
import { pageActivity, deleteActivity } from '@/api/Competition/CompetitionManage/index';

const Index = () => {
	const { linkTo } = useRouterLink();

	const [form] = Form.useForm();

	const defaultItem = {
		label: '全部',
		value: '',
		active: true,
	};
	// 赛事名称
	const [nameList, setNameList] = useState([defaultItem]);
	// 赛事状态
	const [statusList, setStatusList] = useState([defaultItem]);
	// 赛事时间
	const [yearList, setYearList] = useState([defaultItem]);

	const [dataSource, setDataSource] = useState([]);
	const [pagination, setPagination] = useState({
		current: 1,
		pageSize: 10,
		total: 0,
		showTotal: (total) => `共 ${total} 条`,
	});
	// 搜索
	const paginationChange = (current = 1, pageSize = pagination.pageSize) => {
		pagination.current = current;
		pagination.pageSize = pageSize;
		setPagination({ ...pagination });
	};

	const [params, setParams] = useState({
		shortName: '',
		status: '',
		year: '',
	});
	const searchBtn = () => {
		const { publishUnit, content } = form.getFieldValue();
		setParams({ publishUnit, content });
	};

	// 获取表格数据
	const getTableData = () => {
		pageActivity({
			pageNum: pagination.current,
			pageSize: pagination.pageSize,
			...params,
		}).then((res) => {
			const { total, records } = res.data;
			pagination.total = total - 0;
			setDataSource(records);
			setPagination({ ...pagination });
		});
	};

	useEffect(() => {
		getTableData();
	}, [pagination.current, pagination.pageSize, params]);

	// 启用/禁用
	const changeStatus = ({ status = '', id = '' }) => {
		Modal.confirm({
			title: '提示',
			content: `是否确定${status == 1 ? '显示' : '不显示'}吗？`,
			okText: '确定',
			cancelText: '取消',
			onOk: () => {
				// const ids = id ? [id] : selectedRowKeys;
				// deleteActivity({ ids, useStatus }).then(() => {
				// 	message.success('操作成功');
				// 	getTableData();
				// });
			},
		});
	};

	return (
		<div className="flex-sub flex flex-direction-column margin-top-16">
			<Breadcrumb
				icon="icon-competitionManage"
				list={[
					{
						name: '赛事管理',
						link: '/competition/competitionManage',
					},
				]}
				name="场次列表"
			/>
			<div className="flex-sub margin-lr-20 margin-bottom-20 margin-top-4 padding-20 bg-color-ffffff border-radius-4">
				{/* 筛选条件 开始 */}
				<div className="flex margin-bottom-16 padding-bottom-16 border-bottom-e5e6eb">
					<div className="flex-sub padding-right-16 border-right-e5e6eb">
						<Form
							form={form}
							labelCol={{
								style: { width: '68px' },
							}}
							labelAlign="left"
							className="form-filter"
							initialValues={{}}
						>
							<Row gutter={[16, 16]}>
								<Col xs={24} sm={24} md={12} lg={8}>
									<Form.Item label="各场次的排名展示在大屏端（评分展示页）" name="shortName"></Form.Item>
								</Col>
								<Col xs={24} sm={24} md={12} lg={8}>
									<Form.Item label="赛事状态：" name="status">
										<Select options={[]} placeholder="请选择赛事状态" allowClear />
									</Form.Item>
								</Col>
								<Col xs={24} sm={24} md={12} lg={8}>
									<Form.Item label="赛事时间：" name="year">
										<Select options={[]} placeholder="请选择赛事时间" allowClear />
									</Form.Item>
								</Col>

								<Col xs={24} sm={24} md={12} lg={8}>
									<Button
										className="bg-color-165dff color-ffffff"
										onClick={() => {
											linkTo('/competition/competitionManage/curd');
										}}
									>
										<PlusOutlined />
										<span>新增</span>
									</Button>
								</Col>
							</Row>
						</Form>
					</div>
					{/* direction='vertical' */}
					<Space direction="vertical" className="padding-left-16" size={16}>
						<Button
							className="height-32 font-size-14 bg-color-165dff color-ffffff"
							onClick={() => {
								searchBtn();
							}}
						>
							<SearchOutlined />
							<span>查询</span>
						</Button>

						<Button
							className="height-32 font-size-14 bg-color-f2f3f5 color-4e5969"
							onClick={() => {
								form.resetFields();
								pagination.current = 1;
								setPagination(pagination);
								getTableData();
							}}
						>
							<ReloadOutlined />
							<span>重置</span>
						</Button>
					</Space>
				</div>
				{/* 筛选条件 结束 */}

				{/* 表格列表 开始 */}
				<Table
					rowKey="id"
					dataSource={dataSource}
					pagination={{
						...pagination,
						showQuickJumper: true,
						showSizeChanger: true,
						onChange: paginationChange,
					}}
					scroll={{ x: 'max-content' }}
				>
					<Table.Column title="赛事名称" dataIndex="name" key="name" fixed="left" />
					<Table.Column
						title="赛事时间"
						dataIndex="beginDate"
						key="beginDate"
						render={(_, records) => {
							return (
								<div>
									{' '}
									{records.beginDate} 至 {records.endDate}{' '}
								</div>
							);
						}}
					/>
					<Table.Column title="主办单位" dataIndex="organizerUnit" key="organizerUnit" />
					<Table.Column title="承办单位" dataIndex="undertakingUnit" key="undertakingUnit" />
					<Table.Column
						title="赛事状态"
						dataIndex="status"
						key="status"
						align="center"
						render={(status, records) => (
							<Switch
								className="vertical-align-top"
								checked={status === 1}
								checkedChildren="显示"
								unCheckedChildren="不显示"
								onChange={(checked) => {
									changeStatus({
										status: checked ? 1 : 0,
										id: records.id,
									});
								}}
							/>
							// <Permission
							// 	hasPermi={[]}
							// 	empty={(<div> {status === 1 ? '显示' : '不显示'} </div>)}
							// >

							// </Permission>
						)}
					/>
					<Table.Column
						title="操作"
						dataIndex="id"
						key="id"
						align="center"
						fixed="right"
						width={280}
						render={(_, records) => (
							<Space>
								<Button
									onClick={() => {
										linkTo(
											`/competition/competitionManage/curd?id=${records.id}&entryFormTemplateId=${records.entryFormTemplateId}`
										);
									}}
									type="link"
									size="small"
								>
									编辑
								</Button>
								<Button
									onClick={() => {
										linkTo(
											`/competition/competitionManage/session?id=${records.id}&curTab=3&entryFormTemplateId=${records.entryFormTemplateId}`
										);
									}}
									type="link"
									size="small"
								>
									查看场次
								</Button>
								<Button
									onClick={() => {
										linkTo(
											`/competition/competitionManage/detail?id=${records.id}&curTab=2&entryFormTemplateId=${records.entryFormTemplateId}`
										);
									}}
									type="link"
									size="small"
								>
									查看报名
								</Button>
								<Button
									onClick={() => {
										linkTo(
											`/competition/competitionManage/detail?id=${records.id}&curTab=4&entryFormTemplateId=${records.entryFormTemplateId}`
										);
									}}
									type="link"
									size="small"
								>
									成绩管理
								</Button>
							</Space>
						)}
					/>
				</Table>
				{/* 表格列表 结束 */}
			</div>
		</div>
	);
};

export default Index;
