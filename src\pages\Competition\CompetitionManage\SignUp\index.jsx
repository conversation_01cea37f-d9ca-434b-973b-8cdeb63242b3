import Breadcrumb from '@/components/Breadcrumb';
import { useEffect, useRef, useState } from 'react';
import {
	Tabs,
	Radio,
	Pagination,
	Space,
	Table,
	Form,
	Row,
	Col,
	Input,
	Select,
	Switch,
	Modal,
	Button,
} from 'antd';
import ConditionMapList from '@/components/Competition/ConditionMapList/index';
import Permission from '@/components/Permission';
import {
	ExportOutlined,
	SearchOutlined,
	ReloadOutlined,
} from '@ant-design/icons';

const Index = () => {
	const [form] = Form.useForm();

	const defaultItem = {
		label: '全部',
		value: '',
		active: true,
	};
	// 赛事名称
	const [nameList, setNameList] = useState([defaultItem]);
	// 赛事状态
	const [statusList, setStatusList] = useState([defaultItem]);
	// 赛事时间
	const [yearList, setYearList] = useState([defaultItem]);

	const [dataSource, setDataSource] = useState([{}, {}]);
	const [pagination, setPagination] = useState({
		current: 1,
		pageSize: 10,
		total: 0,
		showTotal: (total) => `共 ${total} 条`,
	});
	// 搜索
	const searchData = (current = 1, pageSize = pagination.pageSize) => {
		pagination.current = current;
		pagination.pageSize = pageSize;
		setPagination({ ...pagination });
		getTableData();
	};

	// 获取表格数据
	const getTableData = () => {
		const { name, useStatus, auditStatus, creditCode } =
			form.getFieldValue();
		// pageEntryEnterprise({
		// 	pageNum: pagination.current,
		// 	pageSize: pagination.pageSize,
		// 	name,
		// 	useStatus,
		// 	auditStatus,
		// 	creditCode,
		// }).then((res) => {
		// 	const { total, records } = res.data;
		// 	pagination.total = total-0;
		// 	setDataSource(records);
		// 	setPagination({ ...pagination });
		// });
	};

	// 启用/禁用
	const changeStatus = (useStatus, id = '') => {
		Modal.confirm({
			title: '提示',
			content: `是否确定${useStatus === 1 ? '显示' : '不显示'}吗？`,
			okText: '确定',
			cancelText: '取消',
			onOk: () => {
				// const ids = id ? [id] : selectedRowKeys;
				// batchUseStatus({ ids, useStatus }).then(() => {
				// 	message.success('操作成功');
				// 	getTableData();
				// });
			},
		});
	};

	return (
		<div className='flex-sub flex flex-direction-column margin-top-16'>
			<Breadcrumb
				icon='icon-competitionManage'
				list={[
					{
						name: '赛事管理',
						link: '/',
					},
				]}
				name='赛事列表'
			/>
			<div className='flex-sub margin-lr-20 margin-bottom-20 margin-top-4 padding-20 bg-color-ffffff border-radius-4'>
				{/* 筛选条件 开始 */}
				<div className='flex margin-bottom-16 padding-bottom-16 border-bottom-e5e6eb'>
					<div className='flex-sub padding-right-16 border-right-e5e6eb'>
						<Form
							form={form}
							labelCol={{
								style: { width: '68px' },
							}}
							labelAlign='left'
							className='form-filter'
							initialValues={{
								deptId: [],
							}}
						>
							<Row gutter={[16, 16]}>
								<Col xs={24} sm={24} md={12} lg={8}>
									<Form.Item
										label='赛事名称：'
										name='auditStatus'
									>
										<Select
											options={[]}
											placeholder='请选择用户角色'
											allowClear
										/>
									</Form.Item>
								</Col>
								<Col xs={24} sm={24} md={12} lg={8}>
									<Form.Item
										label='赛事状态：'
										name='auditStatus'
									>
										<Select
											options={[]}
											placeholder='请选择用户角色'
											allowClear
										/>
									</Form.Item>
								</Col>
								<Col xs={24} sm={24} md={12} lg={8}>
									<Form.Item
										label='赛事时间：'
										name='auditStatus'
									>
										<Select
											options={[]}
											placeholder='请选择用户角色'
											allowClear
										/>
									</Form.Item>
								</Col>
							</Row>
						</Form>
					</div>
					{/* direction='vertical' */}
					<Space className='padding-left-16'>
						<Button
							className='height-32 font-size-14 bg-color-165dff color-ffffff'
							onClick={() => {
								searchData();
							}}
						>
							<SearchOutlined />
							<span>查询</span>
						</Button>

						<Button
							className='height-32 font-size-14 bg-color-f2f3f5 color-4e5969'
							onClick={() => {
								form.resetFields();
								pagination.current = 1;
								setPagination(pagination);
								getTableData();
							}}
						>
							<ReloadOutlined />
							<span>重置</span>
						</Button>
					</Space>
				</div>
				{/* 筛选条件 结束 */}

				{/* 表格列表 开始 */}
				<Table
					rowKey='id'
					dataSource={dataSource}
					pagination={{
						...pagination,
						showQuickJumper: true,
						showSizeChanger: true,
						onChange: searchData,
					}}
					scroll={{ x: 'max-content' }}
				>
					<Table.Column
						title='赛事名称'
						dataIndex='name'
						key='name'
						fixed='left'
					/>
					<Table.Column
						title='赛事时间'
						dataIndex='creditCode'
						key='creditCode'
					/>
					<Table.Column
						title='主办单位'
						dataIndex='legalPersonPhone'
						key='legalPersonPhone'
					/>
					<Table.Column
						title='承办单位'
						dataIndex='auditStatus'
						key='auditStatus'
						render={(auditStatus) => <div></div>}
					/>
					<Table.Column
						title='赛事状态'
						dataIndex='useStatus'
						key='useStatus'
						align='center'
						render={(useStatus, records) => (
							<Permission
								hasPermi={['userManage:joiner:useStatus']}
								empty={
									<div>
										{' '}
										{useStatus === 1
											? '显示'
											: '不显示'}{' '}
									</div>
								}
							>
								<Switch
									className='vertical-align-top'
									disabled={records.auditStatus === 0}
									checked={useStatus === 1}
									checkedChildren='显示'
									unCheckedChildren='不显示'
									onChange={(checked) => {
										changeStatus(
											checked ? 1 : 0,
											records.id
										);
									}}
								/>
							</Permission>
						)}
					/>
					<Table.Column
						title='操作'
						dataIndex='id'
						key='id'
						align='center'
						fixed='right'
						width={280}
						render={(_, records) => (
							<Space>
								<Button
									onClick={() => {}}
									type='link'
									size='small'
								>
									编辑
								</Button>
								<Button
									onClick={() => {}}
									type='link'
									size='small'
								>
									查看场次
								</Button>
								<Button
									onClick={() => {}}
									type='link'
									size='small'
								>
									查看报名
								</Button>
								<Button
									onClick={() => {}}
									type='link'
									size='small'
								>
									成绩管理
								</Button>
							</Space>
						)}
					/>
				</Table>
				{/* 表格列表 结束 */}
			</div>
		</div>
	);
};

export default Index;
