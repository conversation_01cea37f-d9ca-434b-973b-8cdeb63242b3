import { useState, forwardRef, useImperativeHandle, useRef, useEffect } from 'react';

import {
	Tabs,
	Radio,
	Pagination,
	Space,
	Table,
	Form,
	Row,
	Col,
	Input,
	Select,
	Switch,
	Modal,
	Button,
	Steps,
	DatePicker,
	Upload,
	Image,
	message,
	InputNumber
} from 'antd';

import BannerList from '@/components/Competition/BannerList/index'
import {
	addConfig,
	updateConfig,
	detailConfig,
} from '@/api/Competition/ConfigCenter/index';

const Index = () => {
	const [form] = Form.useForm();
	const formRef = useRef();

	const [bannerList, setBannerList] = useState([]);
	// 主要业务
	const [businessList, setBusinessList] = useState([]);
	// 品牌赛事
	const [competitionList, setCompetitionList] = useState([]);
	// 品牌活动
	const [activitiesList, setActivitiesList] = useState([]);
	// 荣誉奖项
	const [awardsList, setAwardsList] = useState([]);

	const [config, setConfig] = useState({
		homePageConfig: {},
		aboutUsConfig: {},
	});

	const getDetail = () => {
		detailConfig().then((res) => {
			if (res.data) {
				res.data.homePageConfig = res.data.homePageConfig || JSON.stringify({});
				setConfig(res.data || {});
				let aboutUsConfig = res.data.aboutUsConfig || {};
				if (aboutUsConfig) {
					if (typeof aboutUsConfig == 'string') {
						aboutUsConfig = JSON.parse(aboutUsConfig)
					}
				}
				form.setFieldsValue(aboutUsConfig.form || {});
				setBannerList(aboutUsConfig.bannerList || []);
				setBusinessList(aboutUsConfig.businessList || []);
				setCompetitionList(aboutUsConfig.competitionList || []);
				setActivitiesList(aboutUsConfig.activitiesList || []);
				setAwardsList(aboutUsConfig.awardsList || []);
			}
		});
	};
	useEffect(() => {
		getDetail();
	}, []);

	const submit = (way = 'back') => {
		form.validateFields()
			.then((values) => {
				const params = {
					aboutUsConfig: JSON.stringify({
						bannerList: bannerList,
						businessList: businessList,
						competitionList: competitionList,
						activitiesList: activitiesList,
						awardsList: awardsList,
						form: {
							...values
						},
					}),
					homePageConfig: config.homePageConfig,
				};
				if (config.id) {
					params.id = config.id;
					updateConfig(params).then(() => {
						getDetail();
					});
				} else {
					addConfig(params).then(() => {
						getDetail();
					});
				}
			})
			.catch((err) => {
				message.warning(err.errorFields && err.errorFields[0].errors);
			});
	};
	return (
		<div className='flex-sub flex flex-direction-column margin-top-16'>
			<div className='flex-sub margin-lr-20 margin-bottom-20 margin-top-4 padding-lr-20 bg-color-ffffff border-radius-4'>
				<Form
					form={form}
					ref={formRef}
					labelCol={{
						style: { width: '200px' },
					}}
					autoComplete='off'
					labelAlign='right'
				>

					<div className='flex align-center justify-start line-height-24 padding-tb-16'>
						<div className='width-6 height-16 border-radius-4 bg-color-165dff'></div>
						<div className='font-size-16 font-weight-500 margin-left-8'>
							公司简介
						</div>
					</div>

					<Form.Item
						label='公司简介'
						name='profile'
						rules={[
							{
								required: true,
							},
						]}
					>
						<Input.TextArea placeholder='请输入公司简介' autoSize={{
							minRows: 4
						}} />
					</Form.Item>
					<Form.Item
						label='企业数据库'
						name='database'
						rules={[
							{
								required: true,
							},
						]}
					>
						<InputNumber min={0} max={999999999999} className='width-300' placeholder='请输入数量' />
					</Form.Item>
					<Form.Item
						label='挖掘技术需求'
						name='requirements'
						rules={[
							{
								required: true,
							},
						]}
					>
						<InputNumber min={0} max={999999999999} className='width-300' placeholder='请输入数量' />
					</Form.Item>
					<Form.Item
						label='服务企业'
						name='service'
						rules={[
							{
								required: true,
							},
						]}
					>
						<InputNumber min={0} max={999999999999} className='width-300' placeholder='请输入数量' />
					</Form.Item>
					<Form.Item
						label='承办品牌赛事'
						name='match'
						rules={[
							{
								required: true,
							},
						]}
					>
						<InputNumber min={0} max={999999999999} className='width-300' placeholder='请输入数量' />
					</Form.Item>
					<Form.Item
						label='举办科创活动'
						name='activity'
						rules={[
							{
								required: true,
							},
						]}
					>
						<InputNumber min={0} max={999999999999} className='width-300' placeholder='请输入数量' />
					</Form.Item>


					{/* =============== 轮播图 开始 ================== */}
					<div className='margin-tb-20'>
						<BannerList title='轮播图' dataSource={bannerList} setDataSource={setBannerList} />
					</div>
					{/* =============== 轮播图 结束 ================== */}


					{/* =============== 主要业务 开始 ================== */}
					<div className='margin-tb-20'>
						<BannerList title='主要业务' dataSource={businessList} setDataSource={setBusinessList} />
					</div>
					{/* =============== 主要业务 结束 ================== */}

					{/* =============== 品牌赛事 开始 ================== */}
					<div className='margin-tb-20'>
						<BannerList title='品牌赛事' dataSource={competitionList} setDataSource={setCompetitionList} />
					</div>
					{/* =============== 品牌赛事 结束 ================== */}

					{/* =============== 品牌活动 开始 ================== */}
					<div className='margin-tb-20'>
						<BannerList title='品牌活动' dataSource={activitiesList} setDataSource={setActivitiesList} />
					</div>
					{/* =============== 品牌活动 结束 ================== */}

					{/* =============== 荣誉奖项 开始 ================== */}
					<div className='margin-tb-20'>
						<BannerList title='荣誉奖项' dataSource={awardsList} setDataSource={setAwardsList} />
					</div>
					{/* =============== 荣誉奖项 结束 ================== */}

					<Form.Item label=' ' rules={[]} colon={false} className='flex justify-end'>
						<Button
							type='primary'
							onClick={() => submit('back')}
							className='margin-right-20'
						>
							提交保存
						</Button>
					</Form.Item>
				</Form>
			</div>
		</div>
	);
};

export default Index;
