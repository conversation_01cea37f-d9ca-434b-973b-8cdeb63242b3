import { Button, Table, Popconfirm, Input, Modal, Form, Switch, Tag } from 'antd';
import { SearchOutlined, PlusOutlined } from '@ant-design/icons';
import { useEffect, useRef, useState } from 'react';
import { addDictItem, delDictItem, updateDictItem, listDictItem } from '@/api/Competition/ConfigCenter/index';
const code = 'event_group_name';
const title = '组别';
const Index = (props = {}) => {
	const [name, setName] = useState('');
	const [isModalOpen, setIsModalOpen] = useState(false);
	const [curRow, setCurRow] = useState({});

	const [dataSource, setDataSource] = useState([]);

	const getList = () => {
		listDictItem({
			name: name || '',
			code,
		}).then((res) => {
			setDataSource(res.data || []);
		});
	};

	useEffect(() => {
		getList();
	}, []);

	useEffect(() => {
		if (isModalOpen) {
			formRef.current.setFieldValue('itemName', curRow.itemName || '');
			formRef.current.setFieldValue('status', curRow.status || '');
			formRef.current.setFieldValue('rankingNum', curRow.rankingNum || '');
		}
	}, [isModalOpen]);

	const [form] = Form.useForm();
	const formRef = useRef();

	const submit = () => {
		form.validateFields().then((values) => {
			const params = {
				id: curRow.id || '',
				itemName: values.itemName || '', // 字典名称
				code, // 字典编码
				status: values.status || 0, // 状态:0停用 1启用
				rankingNum: values.rankingNum || '', // 排名序号
			};
			if (curRow.id) {
				updateDictItem(params).then(() => {
					reset();
					getList();
				});
			} else {
				addDictItem(params).then(() => {
					reset();
					getList();
				});
			}
		});
	};

	const reset = () => {
		formRef.current && formRef.current.resetFields && formRef.current.resetFields();
		setIsModalOpen(false);
		setCurRow({ status: 1 });
	};
	return (
		<div className="">
			<div className="font-size-16 font-weight-500 color-1d2129 line-height-24 margin-bottom-20">{title}配置管理</div>
			<div className="flex justify-between align-center margin-bottom-20">
				<div className="flex-sub">
					<Button
						type="primary"
						icon={<PlusOutlined />}
						onClick={() => {
							setCurRow({ status: 1 });
							setIsModalOpen(true);
						}}
					>
						新建
					</Button>
				</div>
				{/* <Input
					placeholder='请输入关键词'
					className='width-280 margin-right-14'
					suffix={<SearchOutlined />}
					onInput={(e) => {
						setName(e.target.value || '');
					}}
				/> */}
				{/* <Button
					type='primary'
					icon={<SearchOutlined />}
					onClick={() => getList()}
				>
					查询
				</Button> */}
			</div>
			<Table size="small" rowKey="id" dataSource={dataSource} pagination={false}>
				<Table.Column
					title="序号"
					key="index"
					dataIndex="index"
					width={110}
					render={(text, record, index) => {
						return index + 1;
					}}
				/>
				<Table.Column title={`${title}名称`} key="itemName" dataIndex="itemName" />

				<Table.Column
					title="启用状态"
					key="status"
					dataIndex="status"
					align="center"
					render={(status) => {
						return <Tag color={status == 1 ? 'success' : 'default'}>{status == 1 ? '开启' : '关闭'}</Tag>;
					}}
				/>

				<Table.Column title="排序" key="rankingNum" dataIndex="rankingNum" />
				<Table.Column
					title="操作"
					key="option"
					dataIndex="option"
					width={220}
					render={(_, record) => {
						return (
							<>
								<Button
									type="link"
									size="small"
									onClick={() => {
										setCurRow(record);
										setIsModalOpen(true);
									}}
								>
									编辑
								</Button>

								<Popconfirm
									title="提示"
									description="确定删除吗？"
									onConfirm={() => {
										delDictItem({ id: record.id }).then(() => {
											getList();
										});
									}}
									okText="确定"
									cancelText="取消"
								>
									<Button type="link" danger size="small">
										删除
									</Button>
								</Popconfirm>
							</>
						);
					}}
				/>
			</Table>
			<Modal
				title={`${title}${form.id ? '修改' : '新增'}`}
				open={isModalOpen}
				maskClosable={false}
				onOk={() => {
					submit();
				}}
				onCancel={() => {
					reset();
				}}
			>
				<Form
					labelCol={{
						span: 6,
					}}
					wrapperCol={{
						span: 18,
					}}
					autoComplete="off"
					ref={formRef}
					form={form}
				>
					<Form.Item
						label={`${title}名称`}
						name="itemName"
						prop="itemName"
						rules={[
							{
								required: true,
								message: `请输入${title}名称`,
							},
						]}
					>
						<Input />
					</Form.Item>

					<Form.Item label="启用状态" prop="status" name="status">
						<Switch
							checked={curRow.status === 1}
							onChange={(e) => {
								curRow.status = e ? 1 : 0;
								formRef.current.setFieldValue('status', e ? 1 : 0);
								setCurRow({ ...curRow });
							}}
						/>
					</Form.Item>

					<Form.Item label="排序" prop="rankingNum" name="rankingNum">
						<Input />
					</Form.Item>
				</Form>
			</Modal>
		</div>
	);
};

export default Index;
