import CommonDict from "@/components/CommonDict";
import {
    addDictItem,
    delDictItem,
    updateDictItem,
    listDictItem,
} from "@/api/Competition/ConfigCenter/index";
const Index = () => {
    return (
        <>
            <div>
                <CommonDict
                    valueName="评委类型"
                    categoryCode="judges_expert_type"
                    {...{
                        add: addDictItem,
                        batchDel: delDictItem,
                        pageCategoryValue: listDictItem,
                        update: updateDictItem,
                    }}
                    tabItem={true}
                />
            </div>
            <div className="margin-top-24">
            <CommonDict
                valueName="擅长领域"
                categoryCode="judges_field"
                {...{
                    add: addDictItem,
                    batchDel: delDictItem,
                    pageCategoryValue: listDictItem,
                    update: updateDictItem,
                }}
                tabItem={true}
            />
            </div>
        </>
    );
};

export default Index;
