import { Button, Table, Popconfirm, Input, Modal, Form, Switch, Tag, Tabs } from 'antd';
import { useState } from 'react';
import OrgTags from './Components/OrgTags';
import EventObject from './Components/EventObject';
import EventShortName from './Components/EventShortName';
import EventGroup from './Components/EventGroupName';
import EventIndustry from './Components/EventIndustry';
import EventYear from './Components/EventYear';
import JudgesType from './Components/JudgesType';

const Index = (props = {}) => {
	const [activeKey, setActiveKey] = useState('org_tags');
	const tabItems = [
		{
			key: 'org_tags',
			label: '组织单位',
			children: null,
		},
		{
			key: 'event_object',
			label: '参赛方标签',
			children: null,
		},
		{
			key: 'event_short_name',
			label: '赛事标签',
			children: null,
		},
		{
			key: 'event_group_name',
			label: '组别',
			children: null,
		},
		{
			key: 'event_industry',
			label: '行业',
			children: null,
		},
		{
			key: 'event_year',
			label: '赛事年份',
			children: null,
		},
		{
			key: 'judges_type',
			label: '评委标签',
			children: null,
		},
	];
	const tabsChange = (val) => {
		setActiveKey(val);
	};
	return (
		<div className="flex-sub flex flex-direction-column padding-20">
			<div className="padding-20 flex-sub bg-color-ffffff border-radius-4">
				<Tabs activeKey={activeKey} items={tabItems} onChange={tabsChange} />
				{activeKey == 'org_tags' && <OrgTags />}
				{activeKey == 'event_object' && <EventObject />}
				{activeKey == 'event_short_name' && <EventShortName />}
				{activeKey == 'event_group_name' && <EventGroup />}
				{activeKey == 'event_industry' && <EventIndustry />}
				{activeKey == 'event_year' && <EventYear />}
				{activeKey == 'judges_type' && <JudgesType />}
			</div>
		</div>
	);
};

export default Index;
