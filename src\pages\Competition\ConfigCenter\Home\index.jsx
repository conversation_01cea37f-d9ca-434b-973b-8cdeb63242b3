import { useEffect, useRef, useState,forwardRef, useImperativeHandle, } from "react";
import {
    Button,
    Table,
    Popconfirm,
    Input,
    Modal,
    Form,
    Switch,
    Tag,
    Image,
    Row,
    Col,
    message,
	InputNumber,
} from "antd";
import {
    PlusOutlined,
    CloseCircleOutlined,
} from "@ant-design/icons";
import {
    addConfig,
    updateConfig,
    detailConfig,
} from "@/api/Competition/ConfigCenter/index";

import BannerList from '@/components/Competition/BannerList/index';
import UploadImg from "@/components/UploadImg";

const ContactForm = forwardRef((props = {}, ref) => {
    const [form] = Form.useForm();

	useEffect(() => {
		form.setFieldsValue(props.formData)
	}, [props.formData])

	useImperativeHandle(ref, () =>{
		return {
			submit: form.validateFields
		}
	})
	
    return (
        <div>
            <div className="flex align-center justify-between line-height-24 padding-tb-16">
                <div className="flex align-center justify-start">
                    <div className="width-6 height-16 border-radius-4 bg-color-165dff"></div>
                    <div className="font-size-16 font-weight-500 margin-left-8">
                        联系方式管理
                    </div>
                </div>
            </div>

            <Form
                labelCol={{
                    span: 6,
                }}
                wrapperCol={{
                    span: 14,
                }}
                autoComplete="off"
                form={form}
            >
                <Form.Item
                    label="赛事组委会名称"
                    name="groupName"
                    rules={[
                        {
                            required: true,
                        },
                    ]}
                >
                    <Input />
                </Form.Item>
                <Form.List
                    name="contact"
                    initialValue={[
                        {
                            nm: "",
                            tel: "",
                        },
                    ]}
                    rules={[
                        {
                            required: true,
                        },
                    ]}
                >
                    {(fields, { add, remove }, { errors }) => (
                        <Form.Item
                            label="联系人"
                            rules={[
                                {
                                    required: true,
                                },
                            ]}
                        >
                            <div className="flex align-start justify-start">
                                <div className="">
                                    {fields.map((field, index) => {
                                        return (
                                            <div key={`${index}`}>
                                                <div className="flex justify-start align-start">
                                                    <Form.Item
                                                        name={[
                                                            field.name,
                                                            "nm",
                                                        ]}
                                                    >
                                                        <Input placeholder="请输入联系人名称" />
                                                    </Form.Item>
                                                    <Form.Item
                                                        name={[
                                                            field.name,
                                                            "tel",
                                                        ]}
                                                        className="margin-left-20"
                                                    >
                                                        <Input placeholder="请输入联系人联系方式" />
                                                    </Form.Item>
                                                    <Button
                                                        type="primary"
                                                        danger
                                                        className="margin-left-20"
                                                        onClick={() =>
                                                            remove(index)
                                                        }
                                                    >
                                                        删除
                                                    </Button>
                                                </div>
                                            </div>
                                        );
                                    })}
                                </div>
                                <Button
                                    type="primary"
                                    onClick={() => add()}
                                    className="margin-left-20"
                                >
                                    添加联系人
                                </Button>
                            </div>
                        </Form.Item>
                    )}
                </Form.List>
                <Form.Item
                    label="邮箱"
                    name="email"
                    rules={[
                        {
                            required: true,
                        },
                    ]}
                >
                    <Input />
                </Form.Item>

                <Form.Item
                    label="公众号"
                    name="oaUrl01"
                    rules={[
                        {
                            required: true,
                            message: "请上传公众号二维码图片",
                        },
                    ]}
                >
					<UploadImg
						size={10}
						width={140}
						height={140}
						tips="建议尺寸：140px*140px"
					/>
                </Form.Item>

                <Form.Item
                    label="公众号名称"
                    name="oaNm01"
                    rules={[
                        {
                            required: true,
                        },
                    ]}
                >
                    <Input.TextArea
                        autoSize={{
                            minRows: 1,
                        }}
                        placeholder="请输入二维码名称"
                    />
                </Form.Item>

                <Form.Item
                    label="公众号状态"
                    name="oaEnable01"
                    rules={[
                        {
                            required: true,
                        },
                    ]}
                >
                    <Switch />
                </Form.Item>

                <Form.Item
                    label="视频号"
                    name="oaUrl02"
                    rules={[
                        {
                            required: true,
                            message: "请上传视频号二维码图片",
                        },
                    ]}
                >
					<UploadImg
						size={10}
						width={140}
						height={140}
						tips="建议尺寸：140px*140px"
					/>
                </Form.Item>

                <Form.Item
                    label="视频号名称"
                    name="oaNm02"
                    rules={[
                        {
                            required: true,
                        },
                    ]}
                >
                    <Input.TextArea
                        autoSize={{
                            minRows: 1,
                        }}
                        placeholder="请输入二维码名称"
                    />
                </Form.Item>

                <Form.Item
                    label="视频号状态"
                    name="oaEnable02"
                    rules={[
                        {
                            required: true,
                        },
                    ]}
                >
                    <Switch />
                </Form.Item>
            </Form>
        </div>
    );
});

const FlyBanner = (props = {}) => {
    const [form] = Form.useForm();
    const [isModalOpen, setIsModalOpen] = useState(false);
	const flyBanner = props.flyBanner

    const submit = () => {
        form.validateFields().then((values) => {
            console.log("🚀 ~ form.validateFields ~ values:", values)
            props.setFlyBanner({...values, id: `${new Date().valueOf()}`});
			setIsModalOpen(false)
        });
    };

    useEffect(() => {
        if (isModalOpen) {
            form.setFieldsValue(flyBanner);
        } else {
			form.resetFields()
		}
    }, [isModalOpen]);

    return (
        <div>
            <div className="flex align-center line-height-24 padding-tb-16">
                <div className="flex align-center justify-start">
                    <div className="width-6 height-16 border-radius-4 bg-color-165dff"></div>
                    <div className="font-size-16 font-weight-500 margin-left-8">
						飞标管理
                    </div>
                </div>
                {flyBanner.id === undefined && <Button
					className="margin-left-18"
                    type="primary"
					ghost
                    icon={<PlusOutlined />}
                    onClick={() => {
                        setIsModalOpen(true);
                    }}
                >
                    创建
                </Button>}
            </div>
            {flyBanner.id && <Table
                size="small"
                rowKey="id"
                dataSource={[flyBanner]}
                pagination={false}
				scroll={{ x: 'max-content' }}
            >
                <Table.Column title='飞标标题' key="name" dataIndex="name" />
                <Table.Column title='飞标宽度' key="width" dataIndex="width" />
                <Table.Column title='飞标高度' key="height" dataIndex="height" />
                <Table.Column title='跳转地址' key="jumpUrl" dataIndex="jumpUrl" />
                <Table.Column
                    title='轮播图'
                    key="bannerUrl"
                    dataIndex="bannerUrl"
                    align="center"
                    render={(bannerUrl) => {
                        return <Image src={bannerUrl} width={80} />;
                    }}
                />
                <Table.Column
                    title="启用状态"
                    key="status"
                    dataIndex="status"
                    align="center"
                    render={(status) => {
                        return (
                            <Tag color={status ? "success" : "default"}>
                                {status ? "开启" : "关闭"}
                            </Tag>
                        );
                    }}
                />
                <Table.Column
                    title="显示关闭按钮"
                    key="isClose" 
                    dataIndex="isClose"
                    align="center"
                    render={(status) => {
                        return (
                            <Tag color={status ? "success" : "default"}>
                                {status ? "开启" : "关闭"}
                            </Tag>
                        );
                    }}
                />
                <Table.Column
                    title="操作"
                    key="option"
                    dataIndex="option"
                    align="center"
                    width={220}
                    render={(_, record) => {
                        return (
                            <>
                                <Button
                                    type="link"
                                    size="small"
                                    onClick={() => {
                                        setIsModalOpen(true);
                                    }}
                                >
                                    编辑
                                </Button>
                            </>
                        );
                    }}
                />
            </Table>}
            <Modal
                title={`飞标管理`}
                open={isModalOpen}
                maskClosable={false}
                onOk={() => {
                    submit();
                }}
                onCancel={() => {
                    setIsModalOpen(false);
                }}
            >
                <Form
                    labelCol={{
                        span: 6,
                    }}
                    wrapperCol={{
                        span: 18,
                    }}
                    autoComplete="off"
                    form={form}
                >
				<Form.Item
					name="id"
					hidden
				>
					<Input />
				</Form.Item>
                    <Form.Item
                        label={`飞标标题`}
                        name="name"
                        rules={[
                            {
                                required: true,
                                message: `请输入飞标标题`,
                            },
                        ]}
                    >
                        <Input />
                    </Form.Item>
                    <Form.Item
                        label={`飞标宽度`}
                        name="width"
                        rules={[
                            {
                                required: true,
                                message: `请输入飞标宽度`,
                            },
                        ]}
                    >
                        <InputNumber className="width-100per" min={0}  placeholder="请输入飞标宽度(px)" />
                    </Form.Item>
                    <Form.Item
                        label={`飞标高度`}
                        name="height"
                        rules={[
                            {
                                required: true,
                                message: `请输入飞标高度`,
                            },
                        ]}
                    >
                        <InputNumber className="width-100per" min={0}  placeholder="请输入飞标高度(px)" />
                    </Form.Item>
                    <Form.Item
                        label={`跳转地址`}
                        name="jumpUrl"
                        rules={[
                            {
                                required: !true,
                                message: `请输入跳转地址`,
                            },
                        ]}
                    >
                        <Input.TextArea />
                    </Form.Item>
                    <Form.Item
                        label="飞标图片"
                        name="bannerUrl"
                    >
                        <UploadImg
                            size={10}
                            width={340}
                            height={120}
                            hideTip
                        />
                    </Form.Item>
                    <Form.Item label="启用状态" prop="status" name="status">
                        <Switch />
                    </Form.Item>
                    <Form.Item label="显示关闭按钮" prop="isClose" name="isClose">
                        <Switch />
                    </Form.Item>
                </Form> 
            </Modal>
        </div>
    );
};

const Index = () => {
    const ContactFormRef = useRef();
    const [bannerList, setBannerList] = useState([]);
    const [form, setForm] = useState({});
    const [flyBanner, setFlyBanner] = useState({});

    const [config, setConfig] = useState({});

    const getDetail = () => {
        detailConfig().then((res) => {
            if (res.data) {
                setConfig(res.data || {});
                const homePageConfig = JSON.parse(
                    res.data.homePageConfig || "{}"
                );
                console.log("🚀 ~ detailConfig ~ homePageConfig:", homePageConfig)
                setBannerList(homePageConfig.bannerList || []);
                setForm(homePageConfig.form || {});
                setFlyBanner(homePageConfig.flyBanner || {});
            }
        });
    };

    useEffect(() => {
        getDetail();
    }, []);

    const submit = () => {
        ContactFormRef.current.submit().then((formData) => {
            const params = {
                id: config.id || undefined,
                homePageConfig: JSON.stringify({
                    bannerList,
                    form: formData,
                    flyBanner,
                }),
                aboutUsConfig: config.aboutUsConfig || '{}',
            };
            (params.id ? updateConfig : addConfig)(params).then(() => {
                getDetail();
                message.success("保存成功");
            });
        });
    };

    return (
        <div className="flex-sub margin-20 padding-lr-20 padding-bottom-20 bg-color-ffffff border-radius-4">
            <BannerList dataSource={bannerList} setDataSource={setBannerList} />
            <ContactForm
                ref={ContactFormRef}
                formData={form}
            />
            <FlyBanner flyBanner={flyBanner} setFlyBanner={setFlyBanner} />

            <Row>
                <Col offset={6}>
                    <Button type="primary" onClick={submit}>
                        提交保存
                    </Button>
                </Col>
            </Row>
        </div>
    );
};

export default Index;
