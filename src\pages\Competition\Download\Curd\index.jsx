import { Select, Button, Form, Input, Image } from 'antd';
import Breadcrumb from '@/components/Breadcrumb';
import { useRouterLink } from '@/hook/useRouter';
import { useEffect, useState } from 'react';

import UploadFile from '@/components/UploadFile';

import { PlusOutlined, CloseCircleOutlined } from '@ant-design/icons';

import { saveActivityResource, updateActivityResource, getActivityResourceDetail } from '@/api/Competition/Download/index';
import { pageActivity } from '@/api/Competition/CompetitionManage/index';

const Index = () => {
	const { searchParams, linkTo } = useRouterLink();
	const id = searchParams.get('id');
	const [form] = Form.useForm();
	const [fileUrl, setFileUrl] = useState('');
	const [activityIdOptopns, setActivityIdOptopns] = useState([]);

	useEffect(() => {
		pageActivity({
			pageNum: 1,
			pageSize: 2000,
		}).then((res) => {
			setActivityIdOptopns(
				(res.data.records || []).map((ov) => {
					return {
						label: ov.name,
						value: ov.id,
					};
				})
			);
		});
		if (id) {
			getActivityResourceDetail({ id }).then((res) => {
				const resData = res.data || {};
				form.setFieldsValue(resData);
				setFileUrl(resData.fileUrl || []);
			});
		}
	}, []);

	const submit = () => {
		form.validateFields().then((values) => {
			const params = {
				id: id || '', // 主键
				activityId: values.activityId || '', // 活动id
				name: values.name || '', // 标题
				fileUrl: fileUrl || '', // 附件地址
			};
			if (id) {
				updateActivityResource(params).then(() => {
					linkTo(-1);
				});
			} else {
				saveActivityResource(params).then(() => {
					linkTo(-1);
				});
			}
		});
	};

	return (
		<div className="flex-sub flex flex-direction-column margin-top-16">
			<Breadcrumb
				icon="icon-download"
				list={[
					{
						name: '资料下载',
						link: '/competition/download',
					},
				]}
				name="资料编辑"
			/>
			<div className="flex-sub margin-lr-20 margin-bottom-20 margin-top-4 padding-20 bg-color-ffffff border-radius-4">
				<Form
					form={form}
					autoComplete="off"
					labelCol={{
						span: 4,
					}}
					wrapperCol={{
						span: 18,
					}}
					initialValues={{
						fileUrl: '',
					}}
				>
					<Form.Item
						name="name"
						label="资料名称"
						rules={[
							{
								required: true,
							},
						]}
					>
						<Input placeholder="请输入资料名称" allowClear />
					</Form.Item>
					<Form.Item
						name="activityId"
						label="赛事名称"
						rules={[
							{
								required: true,
							},
						]}
					>
						<Select
							showSearch
							placeholder="请选择赛事名称"
							options={activityIdOptopns}
							filterOption={(input, option) => (option?.label ?? '').includes(input)}
						/>
					</Form.Item>
					<Form.Item
						label="附件上传"
						rules={[
							{
								required: true,
							},
						]}
					>
						<Input className="height-0 width-0 position-absolute opacity-0" />
						{fileUrl ? (
							<div className="flex align-start justify-start padding-tb-4">
								<CloseCircleOutlined
									className="a font-size-20 color-ff9535"
									onClick={() => {
										setFileUrl('');
									}}
								/>
								<div className="margin-left-8 world-break-all">{fileUrl}</div>
							</div>
						) : (
							<UploadFile
								onChange={(url) => {
									setFileUrl(url);
									form.setFieldValue('fileUrl', '');
								}}
							>
								<Button type="primary">
									<PlusOutlined />
									点击上传
								</Button>
							</UploadFile>
						)}
					</Form.Item>
					<Form.Item label=" " rules={[]} colon={false}>
						<Button type="primary" onClick={() => submit()}>
							提交保存
						</Button>
					</Form.Item>
				</Form>
			</div>
		</div>
	);
};

export default Index;
