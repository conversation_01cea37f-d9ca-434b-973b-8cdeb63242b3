import { Select, Button, Form, Input, Image, DatePicker } from 'antd';
import Breadcrumb from '@/components/Breadcrumb';
import { useRouterLink } from '@/hook/useRouter';
import { useEffect, useState } from 'react';

import RictText from '@/components/RictText';
import UploadFile from '@/components/UploadFile';

import { PlusOutlined, CloseCircleOutlined } from '@ant-design/icons';

import {
	saveActivityNotice,
	updateActivityNotice,
	getActivityNoticeDetail,
} from '@/api/Competition/Notifie/index';
import { pageActivity } from '@/api/Competition/CompetitionManage/index';

import { handleEditor } from '@/utils/common';

import dayjs from 'dayjs'
const Index = () => {
	const { searchParams, linkTo } = useRouterLink();
	const id = searchParams.get('id');
	const [form] = Form.useForm();

	const [detailsContent, setDetailsContent] = useState('');

	const [pictureUrl, setPictureUrl] = useState('');
	const [activityIdOptopns, setActivityIdOptopns] = useState([]);

	useEffect(() => {
		pageActivity({
			pageNum: 1,
			pageSize: 2000,
		}).then((res) => {
			setActivityIdOptopns(
				(res.data.records || []).map((ov) => {
					return {
						label: ov.name,
						value: ov.id,
					};
				})
			);
		});
		if (id) {
			getActivityNoticeDetail({ id }).then((res) => {
				const resData = res.data || {};
				form.setFieldsValue(resData);
				setDetailsContent(resData.content || '');
				setPictureUrl(resData.pictureUrl || '');
			});
		}
	}, []);

	const submit = () => {
		form.validateFields().then((values) => {
			const params = {
				id: id || '', // 主键
				activityId: values.activityId || '', // 活动id
				title: values.title || '', // 标题
				publishUnit: values.publishUnit || '', // 发布单位
				publishDate: values.publishDate || '', // 发布时间
				content: detailsContent || '', // 内容
				seqNumber: values.seqNumber || '', // 序号
				pictureUrl: pictureUrl || '', // 封面
				type: '2', // 类型 1:通知,2:新闻
			};

			handleEditor(detailsContent).then((htmlData) => {
				params.content = htmlData;
				if (id) {
					updateActivityNotice(params).then(() => {
						linkTo(-1);
					});
				} else {
					saveActivityNotice(params).then(() => {
						linkTo(-1);
					});
				}
			});
		});
	};

	return (
		<div className='flex-sub flex flex-direction-column margin-top-16'>
			<Breadcrumb
				icon='icon-newsCenter'
				list={[
					{
						name: '新闻中心',
						link: '/competition/newsCenter/advisory',
					},
				]}
				name='资讯编辑'
			/>
			<div className='flex-sub margin-lr-20 margin-bottom-20 margin-top-4 padding-20 bg-color-ffffff border-radius-4'>
				<Form
					form={form}
					autoComplete='off'
					labelCol={{
						span: 4,
					}}
					wrapperCol={{
						span: 18,
					}}
					initialValues={{
						type: 2,
					}}
				>
					<Form.Item
						name='title'
						label='标题'
						rules={[
							{
								required: true,
							},
						]}
					>
						<Input placeholder='请输入标题' allowClear />
					</Form.Item>

					<Form.Item
						name='publishUnit'
						label='发布单位'
						rules={[
							{
								required: true,
							},
						]}
					>
						<Input placeholder='请输入发布单位' allowClear />
					</Form.Item>
					<Form.Item
						name='publishDate'
						label='发布时间'
						rules={[
							{
								required: true,
								message: '请选择发布时间',
							},
						]}
					>
						<PickerDate />
					</Form.Item>
					<Form.Item
						name='activityId'
						label='赛事名称'
						rules={[
							{
								required: true,
							},
						]}
					>
						<Select
							showSearch
							placeholder='请选择赛事名称'
							options={activityIdOptopns}
							filterOption={(input, option) =>
								(option?.label ?? '').includes(input)
							}
						/>
					</Form.Item>

					<Form.Item
						label='正文'
						rules={[{ required: true, message: '请输入正文' }]}
					>
						<RictText
							value={detailsContent}
							onChange={(e) => {
								setDetailsContent(e);
							}}
						></RictText>
						<div className='padding-16'></div>
					</Form.Item>
					<Form.Item name='seqNumber' label='排序' rules={[]}>
						<Input placeholder='请输入排序' allowClear />
					</Form.Item>
					<Form.Item label='封面' rules={[]}>
						{pictureUrl ? (
							<div className='border-box width-130 height-130 position-relative border-solid-0505050f'>
								<Image width={130} src={pictureUrl} />
								<div className='position-absolute right-0 top-0 z-index-10'>
									<CloseCircleOutlined
										className='a font-size-20 color-ff9535'
										onClick={() => {
											setPictureUrl('');
											form.setFieldValue(
												'pictureUrl',
												''
											);
										}}
									/>
								</div>
							</div>
						) : (
							<UploadFile
								onChange={(fileUrl) => {
									setPictureUrl(fileUrl);
								}}
							>
								<div className='width-130 height-130 text-align-center border-box padding-top-30 bg-color-f2f3f5 border-radius-8 a margin-right-20'>
									<PlusOutlined className='font-size-40' />
									<div>点击上传图片</div>
								</div>
							</UploadFile>
						)}
						<div>图片：建议尺寸：668*380</div>
					</Form.Item>
					<Form.Item label=' ' rules={[]} colon={false}>
						<Button type='primary' onClick={() => submit()}>
							提交保存
						</Button>
					</Form.Item>
				</Form>
			</div>
		</div>
	);
};


// 时间控件
const PickerDate = (props) => {
	return <DatePicker value={props.value ? dayjs(props.value) : null} placeholder='请选择日期' onChange={(e) => {
		props.onChange(e ? dayjs(e).format('YYYY-MM-DD') : e)
	}} />;
}
export default Index;
