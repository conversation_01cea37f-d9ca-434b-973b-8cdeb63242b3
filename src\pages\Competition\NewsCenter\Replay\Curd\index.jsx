import { Select, Table, Button, Form, Input, Modal, Image, Space } from 'antd';
import Breadcrumb from '@/components/Breadcrumb';
import { useRouterLink } from '@/hook/useRouter';
import { useEffect, useState } from 'react';

import UploadFile from '@/components/UploadFile';
import UploadImg, { CustomUpload } from '@/components/UploadImg';
import UploadVideo from '@/components/UploadVideo';

import { CloseCircleOutlined } from '@ant-design/icons';

import { saveActivityReview, updateActivityReview, getActivityReviewDetail } from '@/api/Competition/NewsCenter/index';
import { pageActivity } from '@/api/Competition/CompetitionManage/index';

const UploadMp4 = (props = { onChange: () => {} }) => {
	const [form] = Form.useForm();
	const [openUploadModal, setOpenUploadModal] = useState(false);
	const [coverUrl, setCoverUrl] = useState('');
	const [videoUrl, setVideoUrl] = useState('');
	const [size, setSize] = useState('');
	const [format, setFormat] = useState('');
	const close = (isCancel = false) => {
		if (!isCancel) {
			form.validateFields().then((values) => {
				props.onChange &&
					props.onChange({
						coverUrl,
						videoUrl,
						newFileName: values.newFileName || '',
						seqNumber: values.seqNumber || '',
						size,
						format,
					});
				setOpenUploadModal(false);
			});
		} else {
			setOpenUploadModal(false);
		}
	};
	const open = (data = {}) => {
		setOpenUploadModal(true);
		setCoverUrl(data.coverUrl || '');
		setVideoUrl(data.videoUrl || '');
		setSize(data.size || '');
		setFormat(data.format || '');
		form.setFieldsValue({
			coverUrl: data.coverUrl || '',
			videoUrl: data.videoUrl || '',
			newFileName: data.newFileName || '',
			seqNumber: data.seqNumber || '',
			size: data.size || '',
			format: data.format || '',
		});
	};

	return (
		<>
			<props.child open={open} />

			<Modal
				title="上传视频"
				maskClosable={false}
				open={openUploadModal}
				onOk={() => {
					close();
				}}
				onCancel={() => {
					close(true);
				}}
				okText="确认"
				cancelText="取消"
			>
				<Form
					form={form}
					autoComplete="off"
					labelCol={{
						span: 4,
					}}
					wrapperCol={{
						span: 18,
					}}
					initialValues={{
						originalName: '', // 原始文件名
						newFileName: '', // 新文件名
						videoUrl: '', // 视频地址
						format: '', // 文件格式
						size: '', // 文件大小
						coverUrl: '', // 封面url
						resourceId: '', // 资源id
						seqNumber: '', // 序号
					}}
				>
					<Form.Item
						label="视频封面"
						name="coverUrl"
						rules={[
							{
								required: true,
							},
						]}
					>
						<UploadImg size={10} width={96 * 2} height={54 * 2} tips="建议尺寸：960px*540px" />
					</Form.Item>
					<Form.Item
						label="视频文件"
						name="videoUrl"
						rules={[
							{
								required: true,
							},
						]}
					>
						<div>
							<Input className="height-0 width-0 position-absolute opacity-0" />
							<UploadVideo
								value={videoUrl}
								size={1}
								onChange={(fileUrl, file) => {
									form.setFieldValue('videoUrl', fileUrl);
									setVideoUrl(fileUrl);
									if (file) {
										setSize(file.size);
										const name = file.name.split('.');
										setFormat(name[name.length - 1]);
									} else {
										setSize('');
										setFormat('');
									}
								}}
							>
								<Button type="primary" className="margin-right-20">
									点击上传视频
								</Button>
							</UploadVideo>
						</div>
					</Form.Item>
					<Form.Item
						label="视频名称"
						name="newFileName"
						rules={[
							{
								required: true,
							},
						]}
					>
						<Input placeholder="请输入视频名称" />
					</Form.Item>
					<Form.Item label="排序" name="seqNumber">
						<Input placeholder="请输入排序" />
					</Form.Item>
				</Form>
			</Modal>
		</>
	);
};

const Index = () => {
	const { searchParams, linkTo } = useRouterLink();
	const id = searchParams.get('id');
	const [form] = Form.useForm();
	const [detail, setDetail] = useState({});

	const [pictureUrl, setPictureUrl] = useState([]);
	const [dataSource, setDataSource] = useState([]);

	const [activityIdOptopns, setActivityIdOptopns] = useState([]);
	useEffect(() => {
		pageActivity({
			pageNum: 1,
			pageSize: 2000,
		}).then((res) => {
			setActivityIdOptopns(
				(res.data.records || []).map((ov) => {
					const { beginDate = '', endDate = '' } = ov;
					return {
						label: ov.name,
						value: ov.id,
						address: ov.venue,
						startDateAndendDate: `${(beginDate || '').substring(0, 10)} 至 ${(endDate || '').substring(0, 10)}`,
					};
				})
			);
		});

		if (id) {
			getActivityReviewDetail({ id }).then((res) => {
				const resData = res.data || {};
				const { beginDate = '', endDate = '' } = resData;
				form.setFieldsValue({
					...resData,
					startDateAndendDate: `${beginDate.substring(0, 10)} 至 ${endDate.substring(0, 10)}`,
				});
				setPictureUrl(`${resData.pictureUrl || ''}`.split(','));
				setDataSource(resData.videos);
				setDetail(resData);
			});
		}
	}, []);

	const submit = () => {
		form.validateFields().then((values) => {
			const params = {
				id: id || '', // 主键
				activityId: values.activityId || '', // 活动id
				// beginDate: detail.beginDate || '', // 开始日期
				// endDate: detail.endDate || '', // 结束日期
				// venue: detail.venue || '', // 地址
				pictureUrl: pictureUrl.join(','), // 图片地址
				videos: dataSource, // 资源文件关联
				seqNumber: values.seqNumber || '',
			};
			if (id) {
				updateActivityReview(params).then(() => {
					linkTo(-1);
				});
			} else {
				saveActivityReview(params).then(() => {
					linkTo(-1);
				});
			}
		});
	};
	const activityIdChange = (val) => {
		const find = activityIdOptopns.find((ov) => ov.value == val);
		form.setFieldsValue({
			startDateAndendDate: find.startDateAndendDate,
			address: find.address,
		});
	};
	return (
		<div className="flex-sub flex flex-direction-column margin-top-16">
			<Breadcrumb
				icon="icon-newsCenter"
				list={[
					{
						name: '新闻中心',
						link: '/competition/newsCenter/replay',
					},
				]}
				name="回顾编辑"
			/>
			<div className="flex-sub margin-lr-20 margin-bottom-20 margin-top-4 padding-20 bg-color-ffffff border-radius-4">
				<Form
					form={form}
					autoComplete="off"
					labelCol={{
						span: 4,
					}}
					wrapperCol={{
						span: 18,
					}}
					initialValues={{
						type: 2,
					}}
				>
					<Form.Item
						name="activityId"
						label="赛事名称"
						rules={[
							{
								required: true,
							},
						]}
					>
						<Select
							showSearch
							placeholder="请选择赛事名称"
							options={activityIdOptopns}
							filterOption={(input, option) => (option?.label ?? '').includes(input)}
							onChange={activityIdChange}
						/>
					</Form.Item>
					<Form.Item label="时间" name="startDateAndendDate">
						<Input readOnly />
					</Form.Item>

					<Form.Item label="地点" name="venue">
						<Input readOnly />
					</Form.Item>

					<Form.Item name="seqNumber" label="排序" rules={[]}>
						<Input placeholder="请输入排序" allowClear />
					</Form.Item>

					<Form.Item label="图片列表">
						<div>
							<Space>
								<Form.Item name="pictureUrl" noStyle>
									<CustomUpload
										size={10}
										multiple
										onChange={(fileUrl) => {
											setPictureUrl([...pictureUrl, ...fileUrl]);
										}}
									>
										<Button type="primary" className="margin-right-20">
											点击上传图片
										</Button>
									</CustomUpload>
								</Form.Item>
								<div>上传格式为jpg/png文件，单个文件最大不超过10M</div>
							</Space>
						</div>
						<Space wrap className="padding-tb-10">
							{pictureUrl.map((ov) => {
								return (
									<div
										className="border-box width-130 height-130 position-relative border-solid-0505050f overflow-hidden"
										style={{
											background: `url(${ov}) no-repeat center center`,
											backgroundSize: 'contain',
										}}
										key={ov}
									>
										<Image className="opacity-0" width={'100%'} height={'100%'} src={ov} />
										<div className="position-absolute right-0 top-0 z-index-10">
											<CloseCircleOutlined
												className="a font-size-20 color-ff9535"
												onClick={() => {
													setPictureUrl(pictureUrl.filter((ob) => ob != ov));
												}}
											/>
										</div>
									</div>
								);
							})}
						</Space>
					</Form.Item>

					<Form.Item label="视频列表">
						<Space className="margin-bottom-10">
							<Form.Item name="videos" noStyle>
								<UploadMp4
									onChange={(row) => {
										setDataSource([...dataSource, row]);
									}}
									child={(props) => {
										return (
											<Button
												type="primary"
												className="margin-right-20"
												onClick={() => {
													props.open &&
														props.open({
															id: `${new Date().valueOf()}`,
														});
												}}
											>
												添加视频
											</Button>
										);
									}}
								></UploadMp4>
							</Form.Item>
							<div>支持上传mp4/mov/avi格式的文件，单个文件最大不超过5G</div>
						</Space>
						<Table rowKey="videoUrl" dataSource={dataSource} pagination={false}>
							<Table.Column
								title="视频封面"
								width={90}
								dataIndex="coverUrl"
								key="coverUrl"
								render={(coverUrl) => (
									<div>
										<Image width={60} src={coverUrl} />
									</div>
								)}
							/>

							<Table.Column title="视频名称" dataIndex="newFileName" key="newFileName" />
							<Table.Column
								title="视频大小"
								width={100}
								dataIndex="size"
								key="size"
								render={(text) => (
									<div>
										{text < 1024 && `${(text / 1024).toFixed(2)}Kb`}
										{text >= 1024 && text < 1024 * 1024 && `${((text / 1024) * 1024).toFixed(2)}M`}
										{text >= 1024 * 1024 && text < 1024 * 1024 * 1024 && `${((text / 1024) * 1024 * 1024).toFixed(2)}G`}
									</div>
								)}
							/>
							<Table.Column title="视频类型" width={100} dataIndex="format" key="format" />
							<Table.Column title="排序" width={70} dataIndex="seqNumber" key="seqNumber" />
							<Table.Column
								title="操作"
								dataIndex="id"
								key="id"
								align="center"
								width={100}
								render={(_, records) => (
									<Button
										onClick={() => {
											setDataSource(dataSource.filter((ov) => !ov.videoUrl));
										}}
										type="link"
										size="small"
									>
										删除
									</Button>
								)}
							/>
						</Table>
					</Form.Item>
					<Form.Item label=" " rules={[]} colon={false}>
						<Button type="primary" onClick={() => submit()}>
							提交保存
						</Button>
					</Form.Item>
				</Form>
			</div>
		</div>
	);
};

export default Index;
