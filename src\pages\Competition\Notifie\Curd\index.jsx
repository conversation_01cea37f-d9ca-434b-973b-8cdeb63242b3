import {
    Select,
    Button,
    Form,
    Input,
    Table,
    Modal,
    Space,
    DatePicker,
} from "antd";
import Breadcrumb from "@/components/Breadcrumb";
import { useRouterLink } from "@/hook/useRouter";
import { useEffect, useState } from "react";

import RictText from "@/components/RictText";
import UploadFile from "@/components/UploadFile";
import UploadVideo from "@/components/UploadVideo";

import { CloseCircleOutlined } from "@ant-design/icons";

import {
    saveActivityNotice,
    updateActivityNotice,
    getActivityNoticeDetail,
} from "@/api/Competition/Notifie/index";
import { pageActivity } from "@/api/Competition/CompetitionManage/index";

import { handleEditor } from "@/utils/common";

import dayjs from "dayjs";

const UploadFileBtn = (props = { onChange: () => {} }) => {
    const [form] = Form.useForm();
    const [openUploadModal, setOpenUploadModal] = useState(false);
    const [videoUrl, setVideoUrl] = useState("");
    const [size, setSize] = useState("");
    const [format, setFormat] = useState("");
    const close = (isCancel = false) => {
        if (!isCancel) {
            form.validateFields().then((values) => {
                props.onChange &&
                    props.onChange({
                        videoUrl,
                        newFileName: values.newFileName || "",
                        seqNumber: values.seqNumber || "",
                        size,
                        format,
                    });
                setOpenUploadModal(false);
            });
        } else {
            setOpenUploadModal(false);
        }
    };
    const open = (data = {}) => {
        setOpenUploadModal(true);
        setVideoUrl(data.videoUrl || "");
        setSize(data.size || "");
        setFormat(data.format || "");
        form.setFieldsValue({
            videoUrl: data.videoUrl || "",
            newFileName: data.newFileName || "",
            seqNumber: data.seqNumber || "",
            size: data.size || "",
            format: data.format || "",
        });
    };

    return (
        <>
            <props.child open={open} />

            <Modal
                title="上传视频"
                maskClosable={false}
                open={openUploadModal}
                onOk={() => {
                    close();
                }}
                onCancel={() => {
                    close(true);
                }}
                okText="确认"
                cancelText="取消"
            >
                <Form
                    form={form}
                    autoComplete="off"
                    labelCol={{
                        span: 4,
                    }}
                    wrapperCol={{
                        span: 18,
                    }}
                    initialValues={{
                        originalName: "", // 原始文件名
                        newFileName: "", // 新文件名
                        videoUrl: "", // 视频地址
                        format: "", // 文件格式
                        size: "", // 文件大小
                        coverUrl: "", // 封面url
                        resourceId: "", // 资源id
                        seqNumber: "", // 序号
                    }}
                >
                    <Form.Item
                        label="文件"
                        name="videoUrl"
                        rules={[
                            {
                                required: true,
                            },
                        ]}
                    >
                        <>
                            <Input className="height-0 width-0 position-absolute opacity-0" />
                            <UploadVideo
                                value={videoUrl}
                                size={1}
                                onChange={(fileUrl, file) => {
                                    form.setFieldValue("videoUrl", fileUrl);
                                    setVideoUrl(fileUrl);
                                    if (file) {
                                        setSize(file.size);
                                        const name = file.name.split(".");
                                        setFormat(name[name.length - 1]);
                                    } else {
                                        setSize("");
                                        setFormat("");
                                    }
                                }}
                            >
                                <Button
                                    type="primary"
                                    className="margin-right-20"
                                >
                                    点击上传视频
                                </Button>
                            </UploadVideo>
                        </>
                    </Form.Item>
                    <Form.Item
                        label="文件名称"
                        name="newFileName"
                        rules={[
                            {
                                required: true,
                            },
                        ]}
                    >
                        <Input placeholder="请输入文件名称" />
                    </Form.Item>
                    <Form.Item label="排序" name="seqNumber">
                        <Input placeholder="请输入排序" />
                    </Form.Item>
                </Form>
            </Modal>
        </>
    );
};

const Index = () => {
    const { searchParams, linkTo } = useRouterLink();
    const id = searchParams.get("id");
    const [form] = Form.useForm();

    const [detailsContent, setDetailsContent] = useState("");
    const [dataSource, setDataSource] = useState([]);

    const optionsType = [
        {
            label: "通知",
            value: 1,
        },
        {
            label: "公告",
            value: 3,
        },
    ];
    const [activityIdOptopns, setActivityIdOptopns] = useState([]);
    useEffect(() => {
        pageActivity({
            pageNum: 1,
            pageSize: 2000,
        }).then((res) => {
            setActivityIdOptopns(
                (res.data.records || []).map((ov) => {
                    return {
                        label: ov.name,
                        value: ov.id,
                    };
                })
            );
        });
        if (id) {
            getActivityNoticeDetail({ id }).then((res) => {
                const resData = res.data || {};
                form.setFieldsValue(resData);
                setDetailsContent(resData.content || "");
                setDataSource(resData.videos || []);
            });
        }
    }, []);

    const submit = () => {
        form.validateFields().then((values) => {
            console.log("🚀 ~ form.validateFields ~ values:", values);
            const params = {
                id: id || "", // 主键
                activityId: values.activityId || "", // 活动id
                title: values.title || "", // 标题
                publishUnit: values.publishUnit || "", // 发布单位
                publishDate: values.publishDate || "", // 发布时间
                content: detailsContent || "", // 内容
                seqNumber: values.seqNumber || "", // 序号
                videos: dataSource || [], // 资源文件关联
                type: values.type || "", // 类型 1:通知,2:新闻
            };
            handleEditor(detailsContent).then((htmlData) => {
                params.content = htmlData;
                if (id) {
                    updateActivityNotice(params).then(() => {
                        linkTo(-1);
                    });
                } else {
                    saveActivityNotice(params).then(() => {
                        linkTo(-1);
                    });
                }
            });
        });
    };

    return (
        <div className="flex-sub flex flex-direction-column margin-top-16">
            <Breadcrumb
                icon="icon-notifie"
                list={[
                    {
                        name: "通知公告",
                        link: "/competition/notifie",
                    },
                ]}
                name={`${id ? "编辑" : "新增"}`}
            />
            <div className="flex-sub margin-lr-20 margin-bottom-20 margin-top-4 padding-20 bg-color-ffffff border-radius-4">
                <Form
                    form={form}
                    autoComplete="off"
                    labelCol={{
                        span: 4,
                    }}
                    wrapperCol={{
                        span: 18,
                    }}
                >
                    <Form.Item
                        name="title"
                        label="标题"
                        rules={[
                            {
                                required: true,
                            },
                        ]}
                    >
                        <Input placeholder="请输入标题" allowClear />
                    </Form.Item>

                    <Form.Item
                        name="type"
                        label="类型"
                        rules={[
                            {
                                required: true,
                            },
                        ]}
                    >
                        <Select placeholder="类型" options={optionsType} />
                    </Form.Item>
                    <Form.Item
                        name="publishUnit"
                        label="发布单位"
                        rules={[
                            {
                                required: true,
                            },
                        ]}
                    >
                        <Input placeholder="请输入发布单位" allowClear />
                    </Form.Item>
                    <Form.Item
                        name="publishDate"
                        label="发布时间"
                        rules={[
                            {
                                required: true,
                                message: "请选择发布时间",
                            },
                        ]}
                    >
                        <PickerDate />
                    </Form.Item>
                    <Form.Item
                        name="activityId"
                        label="赛事名称"
                        rules={[
                            {
                                required: true,
                            },
                        ]}
                    >
                        <Select
                            showSearch
                            placeholder="请选择赛事名称"
                            options={activityIdOptopns}
                            filterOption={(input, option) =>
                                (option?.label ?? "").includes(input)
                            }
                        />
                    </Form.Item>

                    <Form.Item
                        label="正文"
                        name="content"
                        rules={[{ required: true, message: "请输入正文" }]}
                    >
                        <div>
							<RictText
								value={detailsContent}
								onChange={(e) => {
									setDetailsContent(e);
									form.setFieldValue("content", e);
								}}
							></RictText>
							<div className="padding-16"></div>
						</div>
                    </Form.Item>
                    <Form.Item name="seqNumber" label="排序" rules={[]}>
                        <Input placeholder="请输入排序" allowClear />
                    </Form.Item>
                    <Form.Item label="附件">
                        <div>
                            <UploadFileBtn
                                onChange={(row) => {
                                    setDataSource([...dataSource, row]);
                                }}
                                child={(props = {}) => {
                                    return (
                                        <Button
                                            type="primary"
                                            onClick={() => {
                                                props.open && props.open();
                                            }}
                                        >
                                            点击上传文件
                                        </Button>
                                    );
                                }}
                            ></UploadFileBtn>
                            <Table
                                className="margin-top-10"
                                rowKey="videoUrl"
                                dataSource={dataSource}
                                pagination={false}
                            >
                                <Table.Column
                                    title="文件名称"
                                    dataIndex="newFileName"
                                    key="newFileName"
                                />
                                <Table.Column
                                    title="视频大小"
                                    width={150}
                                    dataIndex="size"
                                    key="size"
                                    render={(text) => (
                                        <div>
                                            {text < 1024 &&
                                                `${(text / 1024).toFixed(2)}Kb`}
                                            {text >= 1024 &&
                                                text < 1024 * 1024 &&
                                                `${(
                                                    (text / 1024) /
                                                    1024
                                                ).toFixed(2)}M`}
                                            {text >= 1024 * 1024 &&
                                                text < 1024 * 1024 * 1024 &&
                                                `${(
                                                    (text / 1024) /
                                                    1024 /
                                                    1024
                                                ).toFixed(2)}G`}
                                        </div>
                                    )}
                                />
                                <Table.Column
                                    title="文件类型"
                                    width={150}
                                    dataIndex="format"
                                    key="format"
                                />
                                <Table.Column
                                    title="排序"
                                    width={70}
                                    dataIndex="seqNumber"
                                    key="seqNumber"
                                />
                                <Table.Column
                                    title="操作"
                                    dataIndex="id"
                                    key="id"
                                    align="center"
                                    width={100}
                                    render={(_, records) => (
                                        <Button
                                            onClick={() => {
                                                setDataSource(
                                                    dataSource.filter(
                                                        (ov) => !ov.videoUrl
                                                    )
                                                );
                                            }}
                                            type="link"
                                            size="small"
                                        >
                                            删除
                                        </Button>
                                    )}
                                />
                            </Table>
                        </div>
                    </Form.Item>
                    <Form.Item label=" "  colon={false}>
                        <Button type="primary" onClick={() => submit()}>
                            提交保存
                        </Button>
                    </Form.Item>
                </Form>
            </div>
        </div>
    );
};

// 时间控件
const PickerDate = (props) => {
    return (
        <DatePicker
            value={props.value ? dayjs(props.value) : null}
            placeholder="请选择日期"
            onChange={(e) => {
                props.onChange(e ? dayjs(e).format("YYYY-MM-DD") : e);
            }}
        />
    );
};
export default Index;
