import { Button, Table, Popconfirm, Input, Modal, Form, Switch, Tag, Tabs, Radio, Pagination, Space, Row, Col, Select, Dropdown } from 'antd';
import { useEffect, useRef, useState, forwardRef, useImperativeHandle } from 'react';
import Breadcrumb from '@/components/Breadcrumb';
import { CloseCircleOutlined, SearchOutlined, ReloadOutlined, PlusOutlined } from '@ant-design/icons';

import UploadFile from '@/components/UploadFile';

import { pageActivity } from '@/api/Competition/CompetitionManage/index';
import { pageNotification, sendSite, delNotification } from '@/api/Competition/SendNotifie/index';

import { downloadFileByUrl } from '@/utils/common';

const FormModal = forwardRef((props = {}, ref) => {
	const [isModalOpen, setIsModalOpen] = useState(false);
	const [curRow, setCurRow] = useState({});
	const [file, setFile] = useState('');

	const [form] = Form.useForm();
	const formRef = useRef();

	const [activityIdOptopns, setActivityIdOptopns] = useState([]);

	useEffect(() => {
		pageActivity({
			pageNum: 1,
			pageSize: 2000,
		}).then((res) => {
			setActivityIdOptopns(
				(res.data.records || []).map((ov) => {
					return {
						label: ov.name,
						value: ov.id,
					};
				})
			);
		});
	}, []);

	const submit = () => {
		form.validateFields().then((values) => {
			const params = {
				file,
				activityId: values.activityId || '', // 活动赛事id
				activityName: activityIdOptopns.find((ov) => ov.value == values.activityId).label || '', // 活动赛事id
				notificationTitle: values.notificationTitle || '', // 通知标题
				notificationContent: values.notificationContent || '', // 通知内容
				creditCodeList: values.creditCodeList || [], // 统一信用代码
			};

			sendSite(params).then(() => {
				reset();
				props.onChange && props.onChange();
			});
		});
	};

	const reset = () => {
		formRef.current && formRef.current.resetFields && formRef.current.resetFields();
		setIsModalOpen(false);
		setCurRow({});
		setFile('');
	};

	useEffect(() => {
		if (isModalOpen) {
			formRef.current.setFieldValue('activityId', curRow.activityId || '');
			formRef.current.setFieldValue('notificationTitle', curRow.noticeTitle || '');
			formRef.current.setFieldValue('notificationContent', curRow.noticeContent || '');
			setFile('');
		}
	}, [isModalOpen]);

	useImperativeHandle(ref, () => {
		return {
			open: (data = {}) => {
				setFile('');
				setCurRow(data);
				setIsModalOpen(true);
			},
			close: () => {
				setIsModalOpen(false);
			},
		};
	});

	return (
		<Modal
			title={`发送通知${form.id ? '修改' : '新增'}`}
			open={isModalOpen}
			maskClosable={false}
			onOk={() => {
				submit();
			}}
			onCancel={() => {
				reset();
			}}
			width={700}
		>
			<Form
				labelCol={{
					span: 6,
				}}
				wrapperCol={{
					span: 18,
				}}
				autoComplete="off"
				ref={formRef}
				form={form}
			>
				<Form.Item
					name="activityId"
					label="赛事名称"
					rules={[
						{
							required: true,
						},
					]}
				>
					<Select
						showSearch
						placeholder="请选择赛事名称"
						options={activityIdOptopns}
						filterOption={(input, option) => (option?.label ?? '').includes(input)}
					/>
				</Form.Item>
				<Form.Item
					name="notificationTitle"
					label="通知标题"
					rules={[
						{
							required: true,
						},
					]}
				>
					<Input placeholder="请输入通知标题" allowClear />
				</Form.Item>
				<Form.Item
					name="notificationContent"
					label="通知内容"
					rules={[
						{
							required: true,
						},
					]}
				>
					<Input.TextArea
						autoSize={{
							minRows: 4,
						}}
						placeholder="请输入通知内容"
						allowClear
					/>
				</Form.Item>
				<Form.Item
					label="统一社会信用代码"
					rules={[
						{
							required: true,
						},
					]}
				>
					{file ? (
						<div className="flex align-center justify-start">
							<div className="margin-lr-20">{file.name}</div>
							<Button
								type="primary"
								danger
								onClick={() => {
									setFile('');
								}}
							>
								删除
							</Button>
						</div>
					) : (
						<Dropdown
							menu={{
								items: [
									{
										key: '1',
										label: (
											<UploadFile
												justGetFile={true}
												onChange={(file) => {
													console.log('🚀 ~ FormModal ~ file:', file);
													setFile(file);
												}}
											>
												<Button type="primary">
													<PlusOutlined />
													选择文件
												</Button>
											</UploadFile>
										),
									},
									{
										key: '2',
										label: (
											<Button
												type="default"
												onClick={() => {
													downloadFileByUrl(
														`${window.location.origin}${
															import.meta.env.VITE_BASE_PATH ? import.meta.env.VITE_BASE_PATH : '/'
														}统一社会信用代码导入模板.xlsx`,
														'统一社会信用代码导入模板.xlsx'
													);
												}}
											>
												模板下载
											</Button>
										),
									},
								],
							}}
						>
							<Button type="default" className="margin-left-12">
								导入模板
							</Button>
						</Dropdown>
					)}
				</Form.Item>
			</Form>
		</Modal>
	);
});
const Index = (props = {}) => {
	const [form] = Form.useForm();

	const [dataSource, setDataSource] = useState([]);
	const [pagination, setPagination] = useState({
		current: 1,
		pageSize: 10,
		total: 0,
		showTotal: (total) => `共 ${total} 条`,
	});
	// 搜索
	const paginationChange = (current = 1, pageSize = pagination.pageSize) => {
		pagination.current = current;
		pagination.pageSize = pageSize;
		setPagination({ ...pagination });
	};

	const [params, setParams] = useState({
		activityId: '', // 大赛活动id
		title: '', // 标题
		content: '', // 内容
	});
	const searchBtn = () => {
		const { activityId, title, content } = form.getFieldValue();
		setParams({ activityId, title, content });
	};

	// 获取表格数据
	const getTableData = () => {
		pageNotification({
			pageNum: pagination.current,
			pageSize: pagination.pageSize,
			...params,
		}).then((res) => {
			const { total, records } = res.data;
			pagination.total = total - 0;
			setDataSource(records);
			setPagination({ ...pagination });
		});
	};

	useEffect(() => {
		getTableData();
	}, [pagination.current, pagination.pageSize, params]);

	const FormModalRef = useRef();

	return (
		<div className="flex-sub flex flex-direction-column margin-top-16">
			<Breadcrumb
				icon="icon-sendNotifie"
				list={[
					{
						name: '发送通知',
						link: '/competition/sendNotifie',
					},
				]}
				name="通知列表"
			/>
			<div className="flex-sub margin-lr-20 margin-bottom-20 margin-top-4 padding-20 bg-color-ffffff border-radius-4">
				{/* 筛选条件 开始 */}
				<div className="flex margin-bottom-16 padding-bottom-16 border-bottom-e5e6eb">
					<div className="flex-sub padding-right-16 border-right-e5e6eb">
						<Form
							form={form}
							labelCol={{
								style: { width: '68px' },
							}}
							labelAlign="left"
							className="form-filter"
							initialValues={{}}
						>
							<Row gutter={[16, 16]}>
								<Col xs={24} sm={24} md={12} lg={8}>
									<Form.Item label="赛事名称：" name="activityId">
										<Select options={[]} placeholder="请选择" allowClear />
									</Form.Item>
								</Col>
								<Col xs={24} sm={24} md={12} lg={8}>
									<Form.Item label="通知标题" name="title">
										<Input placeholder="请输入通知标题" allowClear />
									</Form.Item>
								</Col>
								<Col xs={24} sm={24} md={12} lg={8}>
									<Form.Item label="通知内容" name="content">
										<Input placeholder="请输入通知内容" allowClear />
									</Form.Item>
								</Col>

								<Col xs={24} sm={24} md={12} lg={8}>
									<Button
										className="bg-color-165dff color-ffffff"
										onClick={() => {
											FormModalRef.current.open();
										}}
									>
										<PlusOutlined />
										<span>新增</span>
									</Button>
								</Col>
							</Row>
						</Form>
					</div>
					{/* direction='vertical' */}
					<Space direction="vertical" className="padding-left-16" size={16}>
						<Button
							className="height-32 font-size-14 bg-color-165dff color-ffffff"
							onClick={() => {
								searchBtn();
							}}
						>
							<SearchOutlined />
							<span>查询</span>
						</Button>

						<Button
							className="height-32 font-size-14 bg-color-f2f3f5 color-4e5969"
							onClick={() => {
								form.resetFields();
								pagination.current = 1;
								setPagination(pagination);
								getTableData();
							}}
						>
							<ReloadOutlined />
							<span>重置</span>
						</Button>
					</Space>
				</div>
				{/* 筛选条件 结束 */}

				{/* 表格列表 开始 */}
				<Table
					rowKey="id"
					dataSource={dataSource}
					pagination={{
						...pagination,
						showQuickJumper: true,
						showSizeChanger: true,
						onChange: paginationChange,
					}}
					scroll={{ x: 'max-content' }}
				>
					<Table.Column title="通知标题" dataIndex="noticeTitle" key="noticeTitle" width={220} />
					<Table.Column title="赛事名称" dataIndex="activityName" key="activityName" width={220} />
					<Table.Column title="通知内容" dataIndex="noticeContent" key="noticeContent" />
					<Table.Column title="发布时间" dataIndex="noticeTime" key="noticeTime" width={120} />
					<Table.Column
						title="操作"
						dataIndex="id"
						key="id"
						align="center"
						fixed="right"
						width={140}
						render={(_, record) => (
							<Space>
								<Button
									onClick={() => {
										FormModalRef.current.open({
											...record,
										});
									}}
									type="link"
									size="small"
								>
									编辑
								</Button>

								<Popconfirm
									title="提示"
									description="确定删除吗？"
									onConfirm={() => {
										delNotification({ id: record.id }).then(() => {
											if (dataSource.length > 1) {
												getTableData();
											} else {
												if (pagination.current > 1) {
													paginationChange(pagination.current - 1, pagination.pageSize);
												} else {
													getTableData();
												}
											}
										});
									}}
									okText="确定"
									cancelText="取消"
								>
									<Button type="link" danger size="small">
										删除
									</Button>
								</Popconfirm>
							</Space>
						)}
					/>
				</Table>
				{/* 表格列表 结束 */}
			</div>
			<FormModal
				ref={FormModalRef}
				onChange={() => {
					getTableData();
				}}
			/>
		</div>
	);
};

export default Index;
