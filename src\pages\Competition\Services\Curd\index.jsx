import { Select, Button, Form, Input, Image, Switch } from 'antd';
import Breadcrumb from '@/components/Breadcrumb';
import { useRouterLink } from '@/hook/useRouter';
import { useEffect, useState } from 'react';

import UploadFile from '@/components/UploadFile';

import { PlusOutlined, CloseCircleOutlined } from '@ant-design/icons';

import {
	saveActivityService,
	updateActivityService,
	getActivityServiceDetail,
} from '@/api/Competition/Services/index';

const Index = () => {
	const { searchParams, linkTo } = useRouterLink();
	const id = searchParams.get('id');
	const [form] = Form.useForm();
	const [iconUrl, setIconUrl] = useState('');
	const [status, setStatus] = useState(1);

	useEffect(() => {
		if (id) {
			getActivityServiceDetail({ id }).then((res) => {
				const resData = res.data || {};
				form.setFieldsValue(resData);
				setIconUrl(resData.iconUrl || []);
				setStatus(resData.status);
			});
		}
	}, []);

	const submit = () => {
		form.validateFields().then((values) => {
			const params = {
				id: id || '', // 主键
				name: values.name || '', // 名称
				jumpUrl: values.jumpUrl || '', // 跳转地址
				status: status, // 状态(0:停用,1:启用)
				seqNumber: values.seqNumber || '', // 序号
				description: values.description || '', // 序号
				iconUrl: iconUrl || '', // 附件地址
			};
			if (id) {
				updateActivityService(params).then(() => {
					linkTo(-1);
				});
			} else {
				saveActivityService(params).then(() => {
					linkTo(-1);
				});
			}
		});
	};

	return (
		<div className='flex-sub flex flex-direction-column margin-top-16'>
			<Breadcrumb
				icon='icon-services'
				list={[
					{
						name: '赛事服务',
						link: '/competition/services',
					},
				]}
				name='服务编辑'
			/>
			<div className='flex-sub margin-lr-20 margin-bottom-20 margin-top-4 padding-20 bg-color-ffffff border-radius-4'>
				<Form
					form={form}
					autoComplete='off'
					labelCol={{
						span: 4,
					}}
					wrapperCol={{
						span: 18,
					}}
					initialValues={{
						iconUrl: '',
					}}
				>
					<Form.Item
						name='name'
						label='赛事服务名称'
						rules={[
							{
								required: true,
							},
						]}
					>
						<Input placeholder='请输入赛事服务名称' allowClear />
					</Form.Item>
					<Form.Item
						name='description'
						label='描述'
						rules={[
							{
								required: true,
							},
						]}
					>
						<Input placeholder='请输入描述' allowClear />
					</Form.Item>
					<Form.Item
						name='jumpUrl'
						label='跳转链接'
						rules={[
							{
								required: true,
							},
						]}
					>
						<Input placeholder='请输入跳转链接' allowClear />
					</Form.Item>
					<Form.Item
						name='status'
						label='启用状态'
						rules={
							[
								// {
								// 	required: true
								// }
							]
						}
					>
						<Switch
							checked={status === 1}
							checkedChildren='启用'
							unCheckedChildren='不启用'
							onChange={(checked) => {
								setStatus(checked ? 1 : 0);
							}}
						/>
					</Form.Item>
					<Form.Item
						label='图片上传'
						rules={[
							{
								required: true,
							},
						]}
					>
						<Input className='height-0 width-0 position-absolute opacity-0' />

						{iconUrl ? (
							<div className='border-box width-130 height-130 position-relative border-solid-0505050f'>
								<Image width={130} src={iconUrl} />
								<div className='position-absolute right-0 top-0 z-index-10'>
									<CloseCircleOutlined
										className='a font-size-20 color-ff9535'
										onClick={() => {
											setIconUrl('');
											form.setFieldValue('iconUrl', '');
										}}
									/>
								</div>
							</div>
						) : (
							<UploadFile
								onChange={(fileUrl) => {
									setIconUrl(fileUrl);
									form.setFieldValue('iconUrl', fileUrl);
								}}
							>
								<div className='width-130 height-130 text-align-center border-box padding-top-30 bg-color-f2f3f5 border-radius-8 a margin-right-20'>
									<PlusOutlined className='font-size-40' />
									<div>点击上传图片</div>
								</div>
							</UploadFile>
						)}
					</Form.Item>
					<Form.Item name='seqNumber' label='排序' rules={[]}>
						<Input placeholder='请输入排序' allowClear />
					</Form.Item>
					<Form.Item label=' ' rules={[]} colon={false}>
						<Button type='primary' onClick={() => submit()}>
							提交保存
						</Button>
					</Form.Item>
				</Form>
			</div>
		</div>
	);
};

export default Index;
