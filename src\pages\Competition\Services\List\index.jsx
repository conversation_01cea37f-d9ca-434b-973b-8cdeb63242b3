import Breadcrumb from '@/components/Breadcrumb';
import { useEffect, useRef, useState } from 'react';
import { Tabs, Radio, Pagination, Space, Table, Form, Row, Col, Input, Select, Switch, Modal, Button, Image, Popconfirm } from 'antd';
import Permission from '@/components/Permission';
import { ExportOutlined, SearchOutlined, ReloadOutlined, PlusOutlined } from '@ant-design/icons';
import { useRouterLink } from '@/hook/useRouter/index';

import { pageActivityService, deleteActivityService } from '@/api/Competition/Services/index';

const Index = () => {
	const { linkTo } = useRouterLink();

	const [form] = Form.useForm();

	const [dataSource, setDataSource] = useState([]);
	const [pagination, setPagination] = useState({
		current: 1,
		pageSize: 10,
		total: 0,
		showTotal: (total) => `共 ${total} 条`,
	});
	// 搜索
	const paginationChange = (current = 1, pageSize = pagination.pageSize) => {
		pagination.current = current;
		pagination.pageSize = pageSize;
		setPagination({ ...pagination });
	};
	const [params, setParams] = useState({
		publishUnit: '',
		content: '',
	});
	const searchBtn = () => {
		const { publishUnit, content } = form.getFieldValue();
		setParams({ publishUnit, content });
	};

	// 获取表格数据
	const getTableData = () => {
		pageActivityService({
			pageNum: pagination.current,
			pageSize: pagination.pageSize,
			...params,
		}).then((res) => {
			const { total, records } = res.data;
			pagination.total = total - 0;
			setDataSource(records);
			setPagination({ ...pagination });
		});
	};

	useEffect(() => {
		getTableData();
	}, [pagination.current, pagination.pageSize, params]);

	// 启用/禁用
	const changeStatus = ({ status = '', id = '' }) => {
		Modal.confirm({
			title: '提示',
			content: `是否确定${status === 1 ? '显示' : '不显示'}吗？`,
			okText: '确定',
			cancelText: '取消',
			onOk: () => {
				// deleteActivityService({ id }).then(() => {
				// 	message.success('操作成功');
				// 	getTableData();
				// });
			},
		});
	};

	return (
		<div className="flex-sub flex flex-direction-column margin-top-16">
			<Breadcrumb
				icon="icon-services"
				list={[
					{
						name: '赛事服务',
						link: '/competition/services',
					},
				]}
				name="服务列表"
			/>
			<div className="flex-sub margin-lr-20 margin-bottom-20 margin-top-4 padding-20 bg-color-ffffff border-radius-4">
				{/* 筛选条件 隐藏 开始 */}
				<div className="flex margin-bottom-16 padding-bottom-16 border-bottom-e5e6eb">
					<div className="flex-sub padding-right-16 border-right-e5e6eb">
						<Form
							form={form}
							labelCol={{
								style: { width: '68px' },
							}}
							labelAlign="left"
							className="form-filter"
						>
							<Row gutter={[16, 16]}>
								{/* <Col xs={24} sm={24} md={12} lg={8}>
									<Form.Item
										label='关键词'
										name='content'
									>
										<Input placeholder='请输入关键词' />
									</Form.Item>
								</Col>
								<Col xs={24} sm={24} md={12} lg={8}>
									<Form.Item
										label='发布单位'
										name='publishUnit'
									>
										<Input placeholder='请输入发布单位' />
									</Form.Item>
								</Col>
								<Col xs={24} sm={24} md={12} lg={8}></Col> */}

								<Col xs={24} sm={24} md={12} lg={8}>
									<Button
										className="bg-color-165dff color-ffffff"
										onClick={() => {
											linkTo('/competition/services/curd');
										}}
									>
										<PlusOutlined />
										<span>新增</span>
									</Button>
								</Col>
							</Row>
						</Form>
					</div>
					{/* direction='vertical' */}
					<Space direction="vertical" className="padding-left-16" size={16}>
						<Button
							className="height-32 font-size-14 bg-color-165dff color-ffffff"
							onClick={() => {
								searchBtn();
							}}
						>
							<SearchOutlined />
							<span>查询</span>
						</Button>

						<Button
							className="height-32 font-size-14 bg-color-f2f3f5 color-4e5969"
							onClick={() => {
								form.resetFields();
								pagination.current = 1;
								setPagination(pagination);
								getTableData();
							}}
						>
							<ReloadOutlined />
							<span>重置</span>
						</Button>
					</Space>
				</div>
				{/* 筛选条件 隐藏 结束 */}

				{/* 表格列表 开始 */}
				<Table
					rowKey="id"
					dataSource={dataSource}
					pagination={{
						...pagination,
						showQuickJumper: true,
						showSizeChanger: true,
						onChange: paginationChange,
					}}
					scroll={{ x: 'max-content' }}
				>
					<Table.Column title="排序" dataIndex="seqNumber" key="seqNumber" width={70} />
					<Table.Column title="赛事服务名称" dataIndex="name" key="name" width={220} />
					<Table.Column title="描述" dataIndex="description" key="description" width={220} />
					<Table.Column
						title="图标"
						dataIndex="iconUrl"
						key="iconUrl"
						align="center"
						width={90}
						render={(iconUrl) => {
							return <>{iconUrl && <Image className="width-80 height-80" src={iconUrl} />}</>;
						}}
					/>
					<Table.Column title="跳转链接" dataIndex="jumpUrl" key="jumpUrl" />
					<Table.Column
						title="启用状态"
						dataIndex="status"
						key="status"
						align="center"
						width={120}
						render={(status, records) => (
							<>
								<div> {status === 1 ? '启用' : '不启用'} </div>
								{/* <Switch
								className='vertical-align-top'
								checked={status === 1}
								checkedChildren='启用'
								unCheckedChildren='不启用'
								onChange={(checked) => {
									changeStatus({
										status: checked ? 1 : 0,
										id: records.id,
									});
								}}
							/> */}
								{/* <Permission
								hasPermi={[]}
								empty={(<div> {status === 1 ? '显示' : '不显示'} </div>)}
							></Permission> */}
							</>
						)}
					/>
					<Table.Column
						title="操作"
						dataIndex="id"
						key="id"
						align="center"
						fixed="right"
						width={130}
						render={(_, record) => (
							<Space>
								<Button
									onClick={() => {
										linkTo(`/competition/services/curd?id=${record.id}`);
									}}
									type="link"
									size="small"
								>
									编辑
								</Button>
								<Popconfirm
									title="提示"
									description="确定删除吗？"
									onConfirm={() => {
										deleteActivityService({
											id: record.id,
										}).then(() => {
											if (dataSource.length > 1) {
												getTableData();
											} else {
												if (pagination.current > 1) {
													paginationChange(pagination.current - 1, pagination.pageSize);
												} else {
													getTableData();
												}
											}
										});
									}}
									okText="确定"
									cancelText="取消"
								>
									<Button type="link" danger size="small">
										删除
									</Button>
								</Popconfirm>
							</Space>
						)}
					/>
				</Table>
				{/* 表格列表 结束 */}
			</div>
		</div>
	);
};

export default Index;
