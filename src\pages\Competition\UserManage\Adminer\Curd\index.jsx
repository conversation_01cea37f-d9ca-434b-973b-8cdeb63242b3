import {
	useEffect,
	useState,
	useRef,
	forwardRef,
	useImperativeHandle,
	memo,
} from 'react';

import { Form, Input, message, Button, Space, Select, Cascader } from 'antd';
import Breadcrumb from '@/components/Breadcrumb';
import { useRouterLink } from '@/hook/useRouter';
import { getDeptData } from '@/utils/dictionary';
import {
	getRoleList,
	userDetail,
	userAdd,
	userUpdate,
} from '@/api/System/index';

import { getDeptParentId, getDeptFlatData } from '@/utils/common';

// 验证手机格式
const validPhone = (_, value) => {
	if (value === undefined || value.trim() === '') {
		return Promise.reject('请输入手机号码');
	} else if (!/^(1[3-9]\d{9})$/.test(value)) {
		return Promise.reject('请输入正确格式手机号码');
	} else {
		return Promise.resolve();
	}
};

const Index = forwardRef((props, ref) => {
	const { linkTo, searchParams } = useRouterLink();
	const isFull = useRef();
	const [id, setId] = useState('');
	const [form] = Form.useForm();
	const [deptList, setDeptList] = useState([]);
	const [roleList, setRoleList] = useState([]);
	const [detail, setDetail] = useState({});

	// 获取详情
	const getDetail = (userId) => {
		userDetail({ userId })
			.then((res) => {
				const {
					userName,
					mobile,
					pwd = '不支持查看',
					roleList = [],
				} = res.data;
				form.setFieldsValue({
					userName,
					mobile,
					pwd,
					roleIds: (roleList || []).map(({ id }) => id),
				});
				setDetail(res.data);
			})
			.catch((err) => {
				console.log('🚀 ~ userDetail ~ err:', err);
				if (isFull.current) {
					linkTo(-1);
				}
			});
	};

	// 提交
	const handleSubmit = () => {
		return new Promise((resolve) => {
			form.validateFields().then((res) => {
				const params = {
					id,
					...res,
					loginName: res.mobile,
					deptIds: res.deptIds.map((item) => item[item.length - 1]),
					userType: 2,
				};
				params.pwd = res.pwd === '不支持查看' ? undefined : res.pwd;
				(id ? userUpdate : userAdd)(params).then(() => {
					message.success(id ? '编辑成功' : '新增成功');
					form.resetFields();
					setId('');
					if (isFull.current) {
						setTimeout(() => {
							linkTo(-1);
						}, 1500);
					}

					resolve(params);
				});
			});
		});
	};

	// 取消
	const handleCancel = () => {
		return new Promise((resolve) => {
			form.resetFields();
			setId('');
			if (isFull.current) {
				linkTo(-1);
			}
			resolve();
		});
	};

	// 弹窗形式
	useEffect(() => {
		if (
			props.formQuery &&
			props.formQuery.id &&
			id !== props.formQuery.id
		) {
			setId(props.formQuery.id);
			getDetail(props.formQuery.id);
		}
	}, [JSON.stringify(props.formQuery)]);

	// 新窗口形式
	useEffect(() => {
		isFull.current = !props.formQuery;
		if (searchParams.get('id')) {
			setId(searchParams.get('id'));
			getDetail(searchParams.get('id'));
		}
	}, []);

	useEffect(() => {
		// 请求部门
		getDeptData().then((data) => {
			setDeptList(data);
		});
		// 请求角色
		getRoleList({}, { isCache: true }).then(({ data }) => {
			setRoleList(
				data.map((item) => {
					return {
						value: item.id,
						label: item.roleName,
					};
				})
			);
		});
	}, []);

	// 处理部门
	useEffect(() => {
		if (deptList.length === 0 || (detail.deptList || []).length === 0) {
			return;
		}
		const deptFlatList = getDeptFlatData(deptList);

		form.setFieldValue(
			'deptIds',
			(detail.deptList || [])
				.map(({ id }) => {
					return getDeptParentId(id, deptFlatList);
				})
				.filter((item) => item.length)
		);
	}, [deptList, detail]);

	useImperativeHandle(ref, () => {
		return {
			onCancel: handleCancel,
			onOk: handleSubmit,
		};
	});
	return (
		<div
			className={`${
				isFull.current
					? 'flex-sub flex flex-direction-column margin-top-16'
					: ''
			}`}
		>
			{isFull.current && (
				<Breadcrumb
					icon='icon-userManage'
					list={[
						{
							name: '赛事账号',
							link: '/competition/userManage',
						},
						{
							name: '评委方',
							link: '/competition/userManage/judges',
						},
					]}
					name={id ? '编辑评委账号' : '新增评委账号'}
				/>
			)}
			<div
				className={`${
					isFull.current
						? 'flex-sub margin-lr-20 margin-bottom-20 margin-top-4 padding-20 bg-color-ffffff border-radius-4'
						: ''
				}`}
			>
				<Form
					form={form}
					labelCol={{
						style: { width: '80px' },
					}}
					labelAlign='right'
				>
					<Form.Item
						label='人员名称'
						name='userName'
						rules={[
							{
								required: true,
								message: '请输入人员名称',
							},
						]}
					>
						<Input placeholder='请输入人员名称' />
					</Form.Item>
					<Form.Item
						label='手机号码'
						name='mobile'
						required={true}
						rules={[{ validator: validPhone }]}
					>
						<Input placeholder='请输入手机号码' />
					</Form.Item>
					<Form.Item
						label='登录密码'
						name='pwd'
						rules={[
							{
								required: true,
								message: '请输入登录密码',
							},
						]}
					>
						<Input.Password
							placeholder='请输入登录密码'
							onFocus={(e) => {
								if (id && e.target.value === '不支持查看') {
									form.setFieldValue('pwd', '');
								}
							}}
							onBlur={(e) => {
								if (id && e.target.value === '') {
									form.setFieldValue('pwd', '不支持查看');
								}
							}}
						/>
					</Form.Item>
					<Form.Item
						label='归属部门'
						name='deptIds'
						rules={[
							{
								required: true,
								type: 'array',
								message: '请选择归属部门',
							},
						]}
					>
						<Cascader
							options={deptList}
							placeholder='请选择归属部门'
							multiple
							maxTagCount='responsive'
						/>
					</Form.Item>
					<Form.Item
						label='用户角色'
						name='roleIds'
						rules={[
							{
								required: true,
								type: 'array',
								message: '请选择用户角色',
							},
						]}
					>
						<Select
							options={roleList}
							placeholder='请选择用户角色'
							mode='multiple'
						/>
					</Form.Item>
					{isFull.current && (
						<Form.Item label=' ' colon={false}>
							<Space size={16}>
								<Button type='primary' onClick={handleSubmit}>
									提交
								</Button>
								<Button onClick={handleCancel}>取消</Button>
							</Space>
						</Form.Item>
					)}
				</Form>
			</div>
		</div>
	);
});

export default memo(Index);
