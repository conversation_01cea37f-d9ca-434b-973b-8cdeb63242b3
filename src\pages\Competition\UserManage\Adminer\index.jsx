import { useEffect, useState, useRef } from 'react';
import { Row, Col, Space, Form, Input, Select, Table, message, Button, Switch, Modal, Popconfirm, Cascader } from 'antd';
import { ImportOutlined, ExportOutlined, SearchOutlined, ReloadOutlined } from '@ant-design/icons';
import Breadcrumb from '@/components/Breadcrumb';
import Permission from '@/components/Permission';
import ModalForm from '@/components/ModalForm';
import ImportForm from '@/components/ImportForm';
import Curd from '@/pages/Competition/UserManage/Adminer/Curd';

import dayjs from 'dayjs';
import { getDictData, getDictLabelByValue, getDeptData } from '@/utils/dictionary';
import { download } from '@/utils/common';

import { getRoleList, getUserPageData, userDel, batchUpdateStatus, managementUserExport, managementUserImport } from '@/api/System/index';

const Index = () => {
	const ModalFormCurdRef = useRef();
	const ModalFormImportRef = useRef();
	const [form] = Form.useForm();
	const [deptList, setDeptList] = useState([]);
	const [roleList, setRoleList] = useState([]);
	const [dataSource, setDataSource] = useState([]);
	const [pagination, setPagination] = useState({
		current: 1,
		pageSize: 10,
		total: 0,
		showTotal: (total) => `共 ${total} 条`,
	});

	// 选择
	const [selectedRowKeys, setSelectedRowKeys] = useState([]);
	const [selectedRowObj, setSelectedRowObj] = useState({});

	// 搜索
	const searchData = (current = 1, pageSize = pagination.pageSize) => {
		pagination.current = current;
		pagination.pageSize = pageSize;
		setPagination({ ...pagination });
		getTableData();
		setSelectedRowKeys([]);
		setSelectedRowObj({});
	};

	// 导入弹窗显示
	const importModalShow = () => {
		ModalFormImportRef.current.setOpen(true);
		ModalFormImportRef.current.setTitle('表格导入');
	};

	// 导入
	const handelImport = (formData) => {
		managementUserImport(formData).then(() => {
			message.success('导入成功');
			pagination.current = 1;
			setPagination({ ...pagination });
			getTableData();
		});
	};

	// 导出
	const exportOutData = () => {
		const { keywords, deptId, status } = form.getFieldValue();
		const len = deptId.length;
		managementUserExport({
			keywords,
			deptId: len > 0 ? deptId[len - 1] : undefined,
			status,
			perms: 'competition',
			userType: 2,
		}).then((res) => {
			download.excel(res, `管理方列表-${dayjs().format('YYYYMMDDhhmmss')}`);
			message.success('导出成功');
		});
	};

	// 获取表格数据
	const getTableData = () => {
		const { keywords, deptId = [], status } = form.getFieldValue();
		const len = deptId.length;
		getUserPageData({
			pageNum: pagination.current,
			pageSize: pagination.pageSize,
			keywords,
			deptId: len > 0 ? deptId[len - 1] : undefined,
			status,
			perms: 'competition',
			userType: 2,
		}).then((res) => {
			const { total, records } = res.data;
			pagination.total = total - 0;
			setDataSource(records);
			setPagination({ ...pagination });
		});
	};

	// 启用/禁用
	const changeUserStatus = (status, id = '') => {
		Modal.confirm({
			title: '提示',
			content: `是否确定${status === 1 ? '启用' : '禁用'}吗？`,
			okText: '确定',
			cancelText: '取消',
			onOk: () => {
				const ids = id ? [id] : selectedRowKeys;
				batchUpdateStatus({ ids, status }).then(() => {
					message.success('操作成功');
					getTableData();
					setSelectedRowKeys([]);
					setSelectedRowObj({});
				});
			},
		});
	};

	// 删除
	const handleDel = (id) => {
		const userId = id ? [id] : selectedRowKeys;
		userDel({ userId }).then(() => {
			getTableData();
			message.success('操作成功');
		});
	};

	// 新建/编辑 打开表单
	const [formQuery, setFormQuery] = useState({ id: '' });
	const handleOpenForm = (id = '') => {
		ModalFormCurdRef.current.setOpen(true);
		ModalFormCurdRef.current.setTitle(id ? '编辑评委账号' : '新建评委账号');
		setFormQuery({ id });
	};

	useEffect(() => {
		// 请求部门
		getDeptData().then((data) => {
			setDeptList(data);
		});
		// 请求角色
		getRoleList({}, { isCache: true }).then(({ data }) => {
			setRoleList(
				data.map((item) => {
					return {
						value: item.id,
						label: item.roleName,
					};
				})
			);
		});
		getTableData();
	}, []);

	return (
		<div className="flex-sub flex flex-direction-column margin-top-16">
			{/* 面包屑 开始 */}
			<Breadcrumb
				icon="icon-userManage"
				list={[
					{
						name: '赛事账号',
						link: '/competition/userManage',
					},
				]}
				name="管理方"
			/>
			{/* 面包屑 结束 */}
			<div className="flex-sub margin-lr-20 margin-bottom-20 margin-top-4 padding-20 bg-color-ffffff border-radius-4">
				{/* 筛选条件 开始 */}
				<div className="flex margin-bottom-16 padding-bottom-16 border-bottom-e5e6eb">
					<div className="flex-sub padding-right-16 border-right-e5e6eb">
						<Form
							form={form}
							labelCol={{
								style: { width: '68px' },
							}}
							labelAlign="left"
							className="form-filter"
							initialValues={{
								deptId: [],
							}}
						>
							<Row gutter={[16, 16]}>
								<Col xs={24} sm={24} md={12} lg={8}>
									<Form.Item label="人员名称" name="keywords">
										<Input placeholder="请输入人员名称" />
									</Form.Item>
								</Col>
								<Col xs={24} sm={24} md={12} lg={8}>
									<Form.Item label="所属部门" name="deptId">
										<Cascader options={deptList} placeholder="请选择归属部门" maxTagCount="responsive" changeOnSelect />
									</Form.Item>
								</Col>
								<Col xs={24} sm={24} md={12} lg={8}>
									<Form.Item label="启用状态" name="status">
										<Select options={getDictData('userStatus')} placeholder="请选择启用状态" allowClear />
									</Form.Item>
								</Col>
							</Row>
						</Form>
					</div>
					<Row className="padding-left-16 max-width-200" gutter={[16, 16]}>
						<Permission hasPermi={['competition:userManage:adminer:import']}>
							<Col span={12}>
								<Button type="primary" onClick={importModalShow}>
									<ImportOutlined />
									<span>导入</span>
								</Button>
							</Col>
						</Permission>
						<Permission hasPermi={['competition:userManage:adminer:export']}>
							<Col span={12}>
								{' '}
								<Button type="primary" onClick={exportOutData}>
									<ExportOutlined />
									<span>导出</span>
								</Button>
							</Col>
						</Permission>
						<Col span={12}>
							<Button
								type="primary"
								onClick={() => {
									searchData();
								}}
							>
								<SearchOutlined />
								<span>查询</span>
							</Button>
						</Col>
						<Col span={12}>
							<Button
								onClick={() => {
									form.resetFields();
									pagination.current = 1;
									setPagination(pagination);
									getTableData();
								}}
							>
								<ReloadOutlined />
								<span>重置</span>
							</Button>
						</Col>
					</Row>
				</div>
				{/* 筛选条件 结束 */}
				{/* 按钮操作 开始 */}
				<Space size={16} className="margin-bottom-16">
					<Permission hasPermi={['competition:userManage:adminer:add']}>
						<Button
							type="primary"
							onClick={() => {
								handleOpenForm();
							}}
						>
							新建
						</Button>
					</Permission>
					<Permission hasPermi={['competition:userManage:adminer:useStatus']}>
						<Button
							type="primary"
							disabled={selectedRowKeys.length === 0}
							onClick={() => {
								changeUserStatus(1);
							}}
						>
							启用
						</Button>
					</Permission>
					<Permission hasPermi={['competition:userManage:adminer:useStatus']}>
						<Button
							type="primary"
							disabled={selectedRowKeys.length === 0}
							onClick={() => {
								changeUserStatus(0);
							}}
						>
							禁用
						</Button>
					</Permission>
					<Permission hasPermi={['competition:userManage:adminer:del']}>
						<Button
							type="primary"
							disabled={selectedRowKeys.length === 0}
							onClick={() => {
								handleDel();
							}}
						>
							删除
						</Button>
					</Permission>
					{selectedRowKeys.length > 0 && (
						<Space size={4}>
							<div>已选择：</div>
							<div className="color-165dff">{selectedRowKeys.length}</div>
							<div>项</div>
						</Space>
					)}
				</Space>
				{/* 按钮操作 结束 */}
				{/* 表格列表 开始 */}
				<Table
					rowKey="id"
					dataSource={dataSource}
					pagination={{
						...pagination,
						showQuickJumper: true,
						showSizeChanger: true,
						onChange: searchData,
					}}
					rowSelection={{
						selectedRowKeys,
						onChange: (checkedRowKeys) => {
							selectedRowObj[pagination.current] = checkedRowKeys;
							setSelectedRowObj({ ...selectedRowObj });
							setSelectedRowKeys(Object.values(selectedRowObj).flat(2));
						},
					}}
					scroll={{ x: 'max-content' }}
				>
					<Table.Column title="人员名称" dataIndex="userName" key="userName" fixed="left" />
					<Table.Column title="手机号" dataIndex="mobile" key="mobile" />
					<Table.Column
						title="所属部门"
						dataIndex="deptList"
						key="deptList"
						render={(deptList) => (
							<div className="world-break-all max-width-320">{(deptList || []).map(({ name }) => name).join(',') || '--'}</div>
						)}
					/>
					<Table.Column
						title="用户角色"
						dataIndex="roleList"
						key="roleList"
						render={(deptList) => (
							<div className="world-break-all max-width-320">{(deptList || []).map(({ roleName }) => roleName).join(',') || '--'}</div>
						)}
					/>
					<Table.Column
						title="启用状态"
						dataIndex="status"
						key="status"
						align="center"
						render={(status, records) => (
							<Permission hasPermi={['competition:userManage:adminer:useStatus']} empty={getDictLabelByValue('userStatus', status)}>
								<Switch
									className="vertical-align-top"
									checked={status === 1}
									checkedChildren="启用"
									unCheckedChildren="禁用"
									onChange={(checked) => {
										changeUserStatus(checked ? 1 : 0, records.id);
									}}
								/>
							</Permission>
						)}
					/>
					<Table.Column
						title="操作"
						dataIndex="id"
						key="id"
						align="center"
						fixed="right"
						render={(id) => (
							<Space size={16} className="padding-lr-16">
								<Permission hasPermi={['competition:userManage:adminer:edit']}>
									<div
										className="color-165dff cursor-pointer"
										onClick={() => {
											handleOpenForm(id);
										}}
									>
										编辑
									</div>
								</Permission>
								<Permission hasPermi={['competition:userManage:adminer:del']}>
									<Popconfirm
										icon={false}
										description="是否确认删除此数据?"
										onConfirm={() => {
											handleDel(id);
										}}
										okText="确认"
										cancelText="取消"
									>
										<div className="color-165dff cursor-pointer">删除</div>
									</Popconfirm>
								</Permission>
							</Space>
						)}
					/>
				</Table>
				{/* 表格列表 结束 */}
				{/* 编辑/新建 弹窗 开始 */}
				<ModalForm
					ref={ModalFormCurdRef}
					onOk={() => {
						searchData(pagination.current);
					}}
					FormComp={(props) => <Curd ref={props.FormCompRef} formQuery={formQuery} />}
				/>
				{/* 编辑/新建 弹窗 结束 */}
				{/* 导入 开始 */}
				<ModalForm
					ref={ModalFormImportRef}
					modelConfig={{
						styles: {
							body: {
								minHeight: 'unset',
							},
						},
					}}
					onOk={handelImport}
					FormComp={(props) => (
						<ImportForm
							ref={props.FormCompRef}
							fileName="file"
							tplUrl="https://gbac-produce.oss-cn-shenzhen.aliyuncs.com/tip/competition/%E7%AE%A1%E7%90%86%E6%96%B9%E8%B4%A6%E5%8F%B7%E5%AF%BC%E5%85%A5%E6%A8%A1%E7%89%88.xlsx"
						/>
					)}
				/>
				{/* 导入 结束 */}
			</div>
		</div>
	);
};

export default Index;
