import { useEffect, useState, forwardRef, useImperativeHandle, useRef } from 'react';

import { Button, Image, Space, Modal, message, Form, Input } from 'antd';
import Breadcrumb from '@/components/Breadcrumb';
import Permission from '@/components/Permission';
import VerifyCode from '@/components/VerifyCode';
import JoinerAuditStatus from '@/components/Competition/JoinerAuditStatus';

import { useRouterLink } from '@/hook/useRouter';

import { detailEntryEnterprise, updatePwdAndPhone } from '@/api/Competition/UserManage/joiner';
import { getDictData, getDictLabelByValue } from "@/utils/dictionary";

// 验证手机格式
const validPhone = (_, value) => {
	if (value === undefined || value.trim() === '') {
		return Promise.reject('请输入手机号码');
	} else if (!/^(1[3-9]\d{9})$/.test(value)) {
		return Promise.reject('请输入正确格式手机号码');
	} else {
		return Promise.resolve();
	}
};

const UpdatePswModal = forwardRef((props = {}, ref) => {
	const [form] = Form.useForm();
	const [open, setOpen] = useState(false);

	// 验证密码是否一致
	const validPassword = (_, value) => {
		if (value === form.getFieldValue('password')) {
			return Promise.resolve();
		}
		return Promise.reject('两次密码不一致');
	};

	// 提交保存
	const submit = () => {
		form.validateFields().then((values) => {
			updatePwdAndPhone({
				id: values.id,
				pwd: values.password,
				contactPersonPhone: values.phone,
			}).then(() => {
				message.success('修改成功');
				setOpen(false);
			});
		});
	};

	useImperativeHandle(ref, () => {
		return {
			showModal: (id, value) => {
				setOpen(true);
				form.setFieldValue('id', id);
				form.setFieldValue('phone', value);
			},
		};
	});

	return (
		<Modal
			title="修改账号信息"
			open={open}
			styles={{
				content: {
					paddingBottom: '1px',
				},
			}}
			footer={false}
			width={500}
			centered
			onCancel={() => {
				setOpen(false);
			}}
		>
			<Form
				form={form}
				className="margin-top-24"
				labelCol={{
					style: {
						width: '140px',
					},
				}}
			>
				<Form.Item label="id" name="id" required={true} style={{ display: 'none' }}>
					<Input placeholder="id" />
				</Form.Item>
				<Form.Item label="登录手机账号" name="phone" required={true} rules={[{ validator: validPhone }]}>
					<Input
						placeholder="请输入手机号码"
					/* suffix={
						<VerifyCode
							getMobile={() => form.getFieldValue('phone')}
						/>
					} */
					/>
				</Form.Item>
				{/* <Form.Item
					label='短信验证码'
					name='vcode'
					rules={[
						{
							required: true,
							message: '请输入短信验证码',
						},
					]}
				>
					<Input placeholder='请输入验证码' />
				</Form.Item> */}
				<Form.Item
					label="登录密码"
					name="password"
					rules={[
						{
							required: true,
							message: '请输入登录密码',
						},
					]}
				>
					<Input.Password placeholder="请输入登录密码" />
				</Form.Item>
				<Form.Item
					label="确认密码"
					name="password2"
					rules={[
						{
							required: true,
							message: '请输入确认密码',
						},
						{ validator: validPassword, trigger: 'blur' },
					]}
				>
					<Input.Password placeholder="请输入登录密码" />
				</Form.Item>
				<Form.Item label=" " colon={false}>
					<Space size={18}>
						<Button type="primary" onClick={submit}>
							保存
						</Button>
						<Button type="primary" ghost onClick={() => setOpen(false)}>
							取消
						</Button>
					</Space>
				</Form.Item>
			</Form>
		</Modal>
	);
});

const Index = () => {
	const { searchParams, linkTo } = useRouterLink();

	const UpdatePswModalRef = useRef();
	const [detailData, setDetailData] = useState({});
	const [visible, setVisible] = useState(false);

	const id = searchParams.get('id');

	// 获取详情
	const getDetailData = () => {
		if (id) {
			detailEntryEnterprise({ id }).then((res) => {
				if (res.data) {
					setDetailData(res.data);
				} else {
					message.error('查询不到数据');
					linkTo(-1);
				}
			});
		} else {
			message.error('非法参数');
			linkTo(-1);
		}
	};

	useEffect(() => {
		getDetailData();
	}, []);
	return (
		<div className="flex-sub flex flex-direction-column margin-top-16">
			<Breadcrumb
				icon="icon-userManage"
				list={[
					{
						name: '赛事账号',
						link: '/competition/userManage',
					},
					{
						name: '参赛方',
						link: '/competition/userManage/joiner',
					},
				]}
				name={detailData.name || '详情'}
			/>
			<div className="flex-sub margin-lr-20 margin-bottom-20 margin-top-4 padding-20 bg-color-ffffff border-radius-4">
				{/* 注册信息 开始 */}
				<div className="flex align-center margin-bottom-20">
					<div className="margin-right-8 width-6 height-16 border-radius-4 bg-color-165dff"></div>
					<div className="line-height-24 font-size-16 font-weight-500">注册信息</div>
				</div>
				{/* 企业 开始 */}
				{
					detailData.accountType == 1 ? (
						<>
							<div className="flex align-center margin-bottom-20">
								<div className="flex align-center justify-end margin-right-4 width-200 line-height-22 font-size-14 font-weight-500">
									<div className="color-f53f3f">*</div>
									<div className="color-4e5969">统一社会信用代码：</div>
								</div>
								<div className="width-366 padding-lr-12 padding-tb-6 line-height-22 font-size-14 bg-color-f2f3f5 color-1d2129">
									{detailData.creditCode || ''}
								</div>
							</div>
							<div className="flex align-center margin-bottom-20">
								<div className="flex align-center justify-end margin-right-4 width-200 line-height-22 font-size-14 font-weight-500">
									<div className="color-f53f3f">*</div>
									<div className="color-4e5969">单位名称：</div>
								</div>
								<div className="width-366 padding-lr-12 padding-tb-6 line-height-22 font-size-14 bg-color-f2f3f5 color-1d2129">
									{detailData.name || ''}
								</div>
							</div>

							<div className="flex align-center margin-bottom-20">
								<div className="flex align-center justify-end margin-right-4 width-200 line-height-22 font-size-14 font-weight-500">
									<div className="color-f53f3f">*</div>
									<div className="color-4e5969">注册地区：</div>
								</div>
								<div className="width-366 padding-lr-12 padding-tb-6 line-height-22 font-size-14 bg-color-f2f3f5 color-1d2129">
									{[detailData.provinceName, detailData.cityName, detailData.areaName].filter((ov) => ov).join('-')}
								</div>
							</div>
							<div className="flex align-center margin-bottom-20">
								<div className="flex align-center justify-end margin-right-4 width-200 line-height-22 font-size-14 font-weight-500">
									<div className="color-f53f3f">*</div>
									<div className="color-4e5969">注册地址：</div>
								</div>
								<div className="width-366 padding-lr-12 padding-tb-6 line-height-22 font-size-14 bg-color-f2f3f5 color-1d2129">
									{detailData.registerAddress || ''}
								</div>
							</div>
							<div className="flex align-center margin-bottom-20">
								<div className="flex align-center justify-end margin-right-4 width-200 line-height-22 font-size-14 font-weight-500">
									<div className="color-f53f3f">*</div>
									<div className="color-4e5969">通讯地区：</div>
								</div>
								<div className="width-366 padding-lr-12 padding-tb-6 line-height-22 font-size-14 bg-color-f2f3f5 color-1d2129">
									{[detailData.contactProvinceName, detailData.contactCityName, detailData.contactAreaName].filter((ov) => ov).join('-')}
								</div>
							</div>
							<div className="flex align-center margin-bottom-20">
								<div className="flex align-center justify-end margin-right-4 width-200 line-height-22 font-size-14 font-weight-500">
									<div className="color-f53f3f">*</div>
									<div className="color-4e5969">通讯地址：</div>
								</div>
								<div className="width-366 padding-lr-12 padding-tb-6 line-height-22 font-size-14 bg-color-f2f3f5 color-1d2129">
									{detailData.contactAddress || ''}
								</div>
							</div>
							<div className="flex align-center margin-bottom-20">
								<div className="flex align-center justify-end margin-right-4 width-200 line-height-22 font-size-14 font-weight-500">
									<div className="color-f53f3f">*</div>
									<div className="color-4e5969">成立日期：</div>
								</div>
								<div className="width-366 padding-lr-12 padding-tb-6 line-height-22 font-size-14 bg-color-f2f3f5 color-1d2129">
									{(detailData.establishTime || '').substr(0, 10)}
								</div>
							</div>
							<div className="flex align-center margin-bottom-20">
								<div className="flex align-center justify-end margin-right-4 width-200 line-height-22 font-size-14 font-weight-500">
									<div className="color-f53f3f">*</div>
									<div className="color-4e5969">法定代表人：</div>
								</div>
								<div className="width-366 padding-lr-12 padding-tb-6 line-height-22 font-size-14 bg-color-f2f3f5 color-1d2129">
									{detailData.legalPersonName || ''}
								</div>
							</div>
							<div className="flex align-center margin-bottom-20">
								<div className="flex align-center justify-end margin-right-4 width-200 line-height-22 font-size-14 font-weight-500">
									<div className="color-f53f3f">*</div>
									<div className="color-4e5969">法定代表人联系电话：</div>
								</div>
								<div className="width-366 padding-lr-12 padding-tb-6 line-height-22 font-size-14 bg-color-f2f3f5 color-1d2129">
									{detailData.legalPersonPhone || ''}
								</div>
							</div>
							<div className="flex align-center margin-bottom-20">
								<div className="flex align-center justify-end margin-right-4 width-200 line-height-22 font-size-14 font-weight-500">
									<div className="color-f53f3f">*</div>
									<div className="color-4e5969">法定代表人邮箱：</div>
								</div>
								<div className="width-366 padding-lr-12 padding-tb-6 line-height-22 font-size-14 bg-color-f2f3f5 color-1d2129">
									{detailData.legalPersonEmail || ''}
								</div>
							</div>
							<div className="flex align-center margin-bottom-20">
								<div className="flex align-center justify-end margin-right-4 width-200 line-height-22 font-size-14 font-weight-500">
									<div className="color-f53f3f">*</div>
									<div className="color-4e5969">负责人：</div>
								</div>
								<div className="width-366 padding-lr-12 padding-tb-6 line-height-22 font-size-14 bg-color-f2f3f5 color-1d2129">
									{detailData.headPersonName || ''}
								</div>
							</div>
							<div className="flex align-center margin-bottom-20">
								<div className="flex align-center justify-end margin-right-4 width-200 line-height-22 font-size-14 font-weight-500">
									<div className="color-f53f3f">*</div>
									<div className="color-4e5969">负责人联系电话：</div>
								</div>
								<div className="width-366 padding-lr-12 padding-tb-6 line-height-22 font-size-14 bg-color-f2f3f5 color-1d2129">
									{detailData.headPersonPhone || ''}
								</div>
							</div>
							<div className="flex align-center margin-bottom-20">
								<div className="flex align-center justify-end margin-right-4 width-200 line-height-22 font-size-14 font-weight-500">
									<div className="color-f53f3f">*</div>
									<div className="color-4e5969">负责人邮箱：</div>
								</div>
								<div className="width-366 padding-lr-12 padding-tb-6 line-height-22 font-size-14 bg-color-f2f3f5 color-1d2129">
									{detailData.headPersonEmail || ''}
								</div>
							</div>
							<div className="flex align-center margin-bottom-20">
								<div className="flex align-center justify-end margin-right-4 width-200 line-height-22 font-size-14 font-weight-500">
									<div className="color-f53f3f">*</div>
									<div className="color-4e5969">联系人：</div>
								</div>
								<div className="width-366 padding-lr-12 padding-tb-6 line-height-22 font-size-14 bg-color-f2f3f5 color-1d2129">
									{detailData.contactPersonName || ''}
								</div>
							</div>
							<div className="flex align-center margin-bottom-20">
								<div className="flex align-center justify-end margin-right-4 width-200 line-height-22 font-size-14 font-weight-500">
									<div className="color-f53f3f">*</div>
									<div className="color-4e5969">联系人电话：</div>
								</div>
								<div className="width-366 padding-lr-12 padding-tb-6 line-height-22 font-size-14 bg-color-f2f3f5 color-1d2129">
									{detailData.contactPersonPhone || ''}
								</div>
							</div>
							<div className="flex align-center margin-bottom-20">
								<div className="flex align-center justify-end margin-right-4 width-200 line-height-22 font-size-14 font-weight-500">
									<div className="color-f53f3f">*</div>
									<div className="color-4e5969">联系人邮箱：</div>
								</div>
								<div className="width-366 padding-lr-12 padding-tb-6 line-height-22 font-size-14 bg-color-f2f3f5 color-1d2129">
									{detailData.contactPersonEmail || ''}
								</div>
							</div>
							<div className="flex align-center margin-bottom-20">
								<div className="flex align-center justify-end margin-right-4 width-200 line-height-22 font-size-14 font-weight-500">
									<div className="color-f53f3f">*</div>
									<div className="color-4e5969">营业执照：</div>
								</div>
								<Button type="primary" ghost onClick={() => setVisible(true)}>
									查看
								</Button>
								{detailData.businessLicenseUrl && (
									<Image
										style={{
											display: 'none',
										}}
										preview={{
											visible,
											src: detailData.businessLicenseUrl,
											onVisibleChange: (value) => {
												setVisible(value);
											},
										}}
									/>
								)}
							</div>
						</>
					) : null
				}
				{/* 企业 结束 */}

				{/* 团队 开始 */}
				{
					detailData.accountType == 2 ? (
						<>
							<div className="flex align-center margin-bottom-20">
								<div className="flex align-center justify-end margin-right-4 width-200 line-height-22 font-size-14 font-weight-500">
									<div className="color-f53f3f">*</div>
									<div className="color-4e5969">团队id：</div>
								</div>
								<div className="width-366 padding-lr-12 padding-tb-6 line-height-22 font-size-14 bg-color-f2f3f5 color-1d2129">
									{detailData.creditCode || ''}
								</div>
							</div>
							<div className="flex align-center margin-bottom-20">
								<div className="flex align-center justify-end margin-right-4 width-200 line-height-22 font-size-14 font-weight-500">
									<div className="color-f53f3f">*</div>
									<div className="color-4e5969">团队名称：</div>
								</div>
								<div className="width-366 padding-lr-12 padding-tb-6 line-height-22 font-size-14 bg-color-f2f3f5 color-1d2129">
									{detailData.name || ''}
								</div>
							</div>

							<div className="flex align-center margin-bottom-20">
								<div className="flex align-center justify-end margin-right-4 width-200 line-height-22 font-size-14 font-weight-500">
									<div className="color-f53f3f">*</div>
									<div className="color-4e5969">联系人：</div>
								</div>
								<div className="width-366 padding-lr-12 padding-tb-6 line-height-22 font-size-14 bg-color-f2f3f5 color-1d2129">
									{detailData.contactPersonName || ''}
								</div>
							</div>
							<div className="flex align-center margin-bottom-20">
								<div className="flex align-center justify-end margin-right-4 width-200 line-height-22 font-size-14 font-weight-500">
									<div className="color-f53f3f">*</div>
									<div className="color-4e5969">联系人电话：</div>
								</div>
								<div className="width-366 padding-lr-12 padding-tb-6 line-height-22 font-size-14 bg-color-f2f3f5 color-1d2129">
									{detailData.contactPersonPhone || ''}
								</div>
							</div>
							<div className="flex align-center margin-bottom-20">
								<div className="flex align-center justify-end margin-right-4 width-200 line-height-22 font-size-14 font-weight-500">
									<div className="color-f53f3f">*</div>
									<div className="color-4e5969">联系人邮箱：</div>
								</div>
								<div className="width-366 padding-lr-12 padding-tb-6 line-height-22 font-size-14 bg-color-f2f3f5 color-1d2129">
									{detailData.contactPersonEmail || ''}
								</div>
							</div>
						</>
					) : null
				}
				{/* 团队 结束 */}

				{/* 个人 开始 */}
				{
					detailData.accountType == 3 ? (
						<>
							<div className="flex align-center margin-bottom-20">
								<div className="flex align-center justify-end margin-right-4 width-200 line-height-22 font-size-14 font-weight-500">
									<div className="color-f53f3f">*</div>
									<div className="color-4e5969">证件号码：</div>
								</div>
								<div className="width-366 padding-lr-12 padding-tb-6 line-height-22 font-size-14 bg-color-f2f3f5 color-1d2129">
									{detailData.creditCode || ''}
								</div>
							</div>
							<div className="flex align-center margin-bottom-20">
								<div className="flex align-center justify-end margin-right-4 width-200 line-height-22 font-size-14 font-weight-500">
									<div className="color-f53f3f">*</div>
									<div className="color-4e5969">姓名：</div>
								</div>
								<div className="width-366 padding-lr-12 padding-tb-6 line-height-22 font-size-14 bg-color-f2f3f5 color-1d2129">
									{detailData.name || ''}
								</div>
							</div>
							<div className="flex align-center margin-bottom-20">
								<div className="flex align-center justify-end margin-right-4 width-200 line-height-22 font-size-14 font-weight-500">
									<div className="color-f53f3f">*</div>
									<div className="color-4e5969">性别：</div>
								</div>
								<div className="width-366 padding-lr-12 padding-tb-6 line-height-22 font-size-14 bg-color-f2f3f5 color-1d2129">
									{getDictLabelByValue('gender',detailData.gender)}
								</div>
							</div>
							<div className="flex align-center margin-bottom-20">
								<div className="flex align-center justify-end margin-right-4 width-200 line-height-22 font-size-14 font-weight-500">
									<div className="color-f53f3f">*</div>
									<div className="color-4e5969">联系人电话：</div>
								</div>
								<div className="width-366 padding-lr-12 padding-tb-6 line-height-22 font-size-14 bg-color-f2f3f5 color-1d2129">
									{detailData.contactPersonPhone || ''}
								</div>
							</div>
							<div className="flex align-center margin-bottom-20">
								<div className="flex align-center justify-end margin-right-4 width-200 line-height-22 font-size-14 font-weight-500">
									<div className="color-f53f3f">*</div>
									<div className="color-4e5969">联系人邮箱：</div>
								</div>
								<div className="width-366 padding-lr-12 padding-tb-6 line-height-22 font-size-14 bg-color-f2f3f5 color-1d2129">
									{detailData.contactPersonEmail || ''}
								</div>
							</div>
							<div className="flex align-center margin-bottom-20">
								<div className="flex align-center justify-end margin-right-4 width-200 line-height-22 font-size-14 font-weight-500">
									<div className="color-f53f3f">*</div>
									<div className="color-4e5969">查看证件：</div>
								</div>
								<Button type="primary" ghost onClick={() => setVisible(true)}>
									查看
								</Button>
								{detailData.businessLicenseUrl && (
									<Image
										style={{
											display: 'none',
										}}
										preview={{
											visible,
											src: detailData.businessLicenseUrl,
											onVisibleChange: (value) => {
												setVisible(value);
											},
										}}
									/>
								)}
							</div>
						</>
					) : null
				}
				{/* 个人 结束 */}

				{/* 注册信息 结束 */}

				{/* 账号密码 开始 */}
				<div className="flex align-center margin-bottom-20">
					<div className="margin-right-8 width-6 height-16 border-radius-4 bg-color-165dff"></div>
					<div className="line-height-24 font-size-16 font-weight-500">账号密码</div>
				</div>
				<>
					<div className="flex align-center margin-bottom-20">
						<div className="flex align-center justify-end margin-right-4 width-200 line-height-22 font-size-14 font-weight-500">
							<div className="color-f53f3f">*</div>
							<div className="color-4e5969">登录帐号：</div>
						</div>
						<div className="width-366 padding-lr-12 padding-tb-6 line-height-22 font-size-14 bg-color-f2f3f5 color-1d2129">
							{detailData.creditCode || ''}
						</div>
					</div>
					<div className="flex align-center margin-bottom-20">
						<div className="flex align-center justify-end margin-right-4 width-200 line-height-22 font-size-14 font-weight-500">
							<div className="color-f53f3f">*</div>
							<div className="color-4e5969">登录手机账号：</div>
						</div>
						<div className="width-366 padding-lr-12 padding-tb-6 line-height-22 font-size-14 bg-color-f2f3f5 color-1d2129">
							{detailData.contactPersonPhone || ''}
						</div>
					</div>
					<div className="flex align-center margin-bottom-20">
						<div className="flex align-center justify-end margin-right-4 width-200 line-height-22 font-size-14 font-weight-500">
							<div className="color-f53f3f">*</div>
							<div className="color-4e5969">登录密码：</div>
						</div>
						<div className="width-366 padding-lr-12 padding-tb-6 line-height-22 font-size-14 bg-color-f2f3f5 color-1d2129">
							••••••••••••••••••
						</div>
					</div>
				</>
				{/* 账号密码 结束 */}

				{/* 审核进度 开始 */}
				{detailData.auditStatus !== 2 && (
					<>
						<div className="flex align-center margin-bottom-20">
							<div className="margin-right-8 width-6 height-16 border-radius-4 bg-color-165dff"></div>
							<div className="line-height-24 font-size-16 font-weight-500">审核进度</div>
						</div>
						<div className="flex align-center margin-bottom-20">
							<div className="flex align-center justify-end margin-right-4 width-200 line-height-22 font-size-14 font-weight-500">
								<div className="color-4e5969">审核状态：</div>
							</div>
							<div className="width-366 padding-lr-12 padding-tb-6 line-height-22 font-size-14 bg-color-f2f3f5 color-1d2129">
								{['审核中', '不通过', '已通过'][detailData.auditStatus || 0]}
							</div>
						</div>
						{detailData.auditStatus === 1 && (
							<div className="flex align-center margin-bottom-20">
								<div className="flex align-center justify-end margin-right-4 width-200 line-height-22 font-size-14 font-weight-500">
									<div className="color-4e5969">不通过原因：</div>
								</div>
								<div className="width-366 padding-lr-12 padding-tb-6 line-height-22 font-size-14 bg-color-f2f3f5 color-1d2129">
									{detailData.auditReason || '--'}
								</div>
							</div>
						)}
					</>
				)}
				{/* 审核进度 结束 */}

				{/* 操作按钮 开始 */}
				<div className="flex align-center margin-bottom-20">
					<div className="margin-right-4 width-200"></div>
					<Space size={18}>
						<Button type="primary" ghost onClick={() => linkTo(-1)}>
							返回
						</Button>
						<Permission hasPermi={['userManage:joiner:auditStatus']}>
							<JoinerAuditStatus
								ids={[detailData.id]}
								isRevoke={detailData.auditStatus !== 0}
								onCallback={() => {
									getDetailData();
								}}
							>
								<Button type="primary">{detailData.auditStatus === 0 ? '审核' : '撤销审核'}</Button>
							</JoinerAuditStatus>
						</Permission>
						{detailData.auditStatus === 2 && (
							<Permission hasPermi={['userManage:joiner:updatePsw']}>
								<Button
									type="primary"
									onClick={() => UpdatePswModalRef.current.showModal(detailData.id, detailData.contactPersonPhone)}
								>
									修改账号信息
								</Button>
							</Permission>
						)}
					</Space>
				</div>
				{/* 操作按钮 结束 */}

				{/* 修改弹窗 开始 */}
				<UpdatePswModal ref={UpdatePswModalRef} />
				{/* 修改弹窗 结束 */}
			</div>
		</div>
	);
};

export default Index;
