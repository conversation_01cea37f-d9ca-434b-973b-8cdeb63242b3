import { useEffect, useState } from 'react';
import { Row, Col, Space, Form, Input, Select, Table, message, Button, Switch, Modal } from 'antd';
import { ExportOutlined, SearchOutlined, ReloadOutlined } from '@ant-design/icons';
import Breadcrumb from '@/components/Breadcrumb';
import Permission from '@/components/Permission';
import JoinerAuditStatus from '@/components/Competition/JoinerAuditStatus';

import dayjs from 'dayjs';
import { useRouterLink } from '@/hook/useRouter';
import { getDictData, getDictLabelByValue } from '@/utils/dictionary';
import { download } from '@/utils/common';

import { pageEntryEnterprise, batchUseStatus, entryEnterpriseExport } from '@/api/Competition/UserManage/joiner';

const Index = () => {
	const { linkTo } = useRouterLink();
	const [form] = Form.useForm();
	const [dataSource, setDataSource] = useState([]);
	const [pagination, setPagination] = useState({
		current: 1,
		pageSize: 10,
		total: 0,
		showTotal: (total) => `共 ${total} 条`,
	});

	// 选择
	const [selectedRowKeys, setSelectedRowKeys] = useState([]);
	const [selectedRowObj, setSelectedRowObj] = useState({});

	// 搜索
	const searchData = (current = 1, pageSize = pagination.pageSize) => {
		pagination.current = current;
		pagination.pageSize = pageSize;
		setPagination({ ...pagination });
		getTableData();
		setSelectedRowKeys([]);
		setSelectedRowObj({});
	};

	// 导出
	const exportOutData = () => {
		const { name, useStatus, auditStatus, creditCode } = form.getFieldValue();
		entryEnterpriseExport({
			name,
			useStatus,
			auditStatus,
			creditCode,
		}).then((res) => {
			download.excel(res, `参赛企业列表-${dayjs().format('YYYYMMDD_HH:mm')}`);
			message.success('导出成功');
		});
	};

	// 获取表格数据
	const getTableData = () => {
		const { name, useStatus, auditStatus, creditCode, accountType } = form.getFieldValue();
		pageEntryEnterprise({
			pageNum: pagination.current,
			pageSize: pagination.pageSize,
			name,
			useStatus,
			auditStatus,
			creditCode,
			accountType,
		}).then((res) => {
			const { total, records } = res.data;
			pagination.total = total - 0;
			setDataSource(records);
			setPagination({ ...pagination });
		});
	};

	// 启用/禁用
	const changeUserStatus = (useStatus, id = '') => {
		Modal.confirm({
			title: '提示',
			content: `是否确定${useStatus === 1 ? '启用' : '禁用'}吗？`,
			okText: '确定',
			cancelText: '取消',
			onOk: () => {
				const ids = id ? [id] : selectedRowKeys;
				batchUseStatus({ ids, useStatus }).then(() => {
					message.success('操作成功');
					getTableData();
					setSelectedRowKeys([]);
					setSelectedRowObj({});
				});
			},
		});
	};

	// 跳转详情
	const handleDetail = (id) => {
		linkTo(`/competition/userManage/joiner/detail?id=${id}`);
	};

	useEffect(() => {
		getTableData();
	}, []);

	return (
		<div className="flex-sub flex flex-direction-column margin-top-16">
			{/* 面包屑 开始 */}
			<Breadcrumb
				icon="icon-userManage"
				list={[
					{
						name: '赛事账号',
						link: '/competition/userManage',
					},
				]}
				name="参赛方"
			/>
			{/* 面包屑 结束 */}
			<div className="flex-sub margin-lr-20 margin-bottom-20 margin-top-4 padding-20 bg-color-ffffff border-radius-4">
				{/* 筛选条件 开始 */}
				<div className="flex margin-bottom-16 padding-bottom-16 border-bottom-e5e6eb">
					<div className="flex-sub padding-right-16 border-right-e5e6eb">
						<Form
							form={form}
							labelCol={{
								style: { width: '68px' },
							}}
							labelAlign="left"
							className="form-filter"
							initialValues={{
								deptId: [],
							}}
						>
							<Row gutter={[16, 16]}>
								<Col xs={24} sm={24} md={12} lg={8}>
									<Form.Item label="单位名称" name="name">
										<Input placeholder="请输入单位名称" />
									</Form.Item>
								</Col>
								<Col xs={24} sm={24} md={12} lg={8}>
									<Form.Item label="审核状态" name="auditStatus">
										<Select options={getDictData('auditStatus')} placeholder="请选择审核状态" allowClear />
									</Form.Item>
								</Col>
								<Col xs={24} sm={24} md={12} lg={8}>
									<Form.Item label="启用状态" name="useStatus">
										<Select options={getDictData('userStatus')} placeholder="请选择启用状态" allowClear />
									</Form.Item>
								</Col>
								<Col xs={24} sm={24} md={12} lg={8}>
									<Form.Item label="统一社会信用代码" name="creditCode" labelCol={{ style: { width: '130px' } }}>
										<Input placeholder="请输入统一社会信用代码" />
									</Form.Item>
								</Col>
								<Col xs={24} sm={24} md={12} lg={8}>
									<Form.Item label="账号类型" name="accountType">
										<Select options={getDictData('accountType')} placeholder="请选择账号类型" allowClear />
									</Form.Item>
								</Col>
							</Row>
						</Form>
					</div>
					<Space direction="vertical" size={8} className="padding-left-16">
						<Permission hasPermi={['userManage:joiner:export']}>
							<Button type="primary" onClick={exportOutData}>
								<ExportOutlined />
								<span>导出</span>
							</Button>
						</Permission>
						<Button
							type="primary"
							onClick={() => {
								searchData();
							}}
						>
							<SearchOutlined />
							<span>查询</span>
						</Button>

						<Button
							onClick={() => {
								form.resetFields();
								pagination.current = 1;
								setPagination(pagination);
								getTableData();
							}}
						>
							<ReloadOutlined />
							<span>重置</span>
						</Button>
					</Space>
				</div>
				{/* 筛选条件 结束 */}
				{/* 按钮操作 开始 */}
				<Space size={16} className="margin-bottom-16">
					<Permission hasPermi={['userManage:joiner:auditStatus']}>
						<JoinerAuditStatus
							ids={selectedRowKeys}
							onCallback={() => {
								getTableData();
								setSelectedRowKeys([]);
								setSelectedRowObj({});
							}}
						>
							<Button type="primary" disabled={selectedRowKeys.length === 0}>
								审核
							</Button>
						</JoinerAuditStatus>
					</Permission>
					<Permission hasPermi={['userManage:joiner:useStatus']}>
						<Button
							type="primary"
							disabled={selectedRowKeys.length === 0}
							onClick={() => {
								changeUserStatus(1);
							}}
						>
							启用
						</Button>
					</Permission>
					<Permission hasPermi={['userManage:joiner:useStatus']}>
						<Button
							type="primary"
							disabled={selectedRowKeys.length === 0}
							onClick={() => {
								changeUserStatus(0);
							}}
						>
							禁用
						</Button>
					</Permission>
					{selectedRowKeys.length > 0 && (
						<Space size={4}>
							<div>已选择：</div>
							<div className="color-165dff">{selectedRowKeys.length}</div>
							<div>项</div>
						</Space>
					)}
				</Space>
				{/* 按钮操作 结束 */}
				{/* 表格列表 开始 */}
				<Table
					rowKey="id"
					dataSource={dataSource}
					pagination={{
						...pagination,
						showQuickJumper: true,
						showSizeChanger: true,
						onChange: searchData,
					}}
					rowSelection={{
						selectedRowKeys,
						onChange: (checkedRowKeys) => {
							selectedRowObj[pagination.current] = checkedRowKeys;
							setSelectedRowObj({ ...selectedRowObj });
							setSelectedRowKeys(Object.values(selectedRowObj).flat(2));
						},
					}}
					scroll={{ x: 'max-content' }}
				>
					<Table.Column title="单位名称" dataIndex="name" key="name" fixed="left" />
					<Table.Column title="统一社会信用代码" dataIndex="creditCode" key="creditCode" />
					<Table.Column title="法定代表人" dataIndex="legalPersonName" key="legalPersonName" />
					<Table.Column title="法定代表人联系方式" dataIndex="legalPersonPhone" key="legalPersonPhone" />
					<Table.Column
						title="审核状态"
						dataIndex="auditStatus"
						key="auditStatus"
						render={(auditStatus) => <div>{getDictLabelByValue('auditStatus', auditStatus)}</div>}
					/>
					<Table.Column
						title="启用状态"
						dataIndex="useStatus"
						key="useStatus"
						align="center"
						render={(useStatus, records) => (
							<Permission hasPermi={['userManage:joiner:useStatus']} empty={getDictLabelByValue('userStatus', useStatus)}>
								<Switch
									className="vertical-align-top"
									disabled={records.auditStatus === 0}
									checked={useStatus === 1}
									checkedChildren="启用"
									unCheckedChildren="禁用"
									onChange={(checked) => {
										changeUserStatus(checked ? 1 : 0, records.id);
									}}
								/>
							</Permission>
						)}
					/>
					<Table.Column title="注册时间" dataIndex="createTime" key="createTime" render={(text) => (text || '').slice(0, 16)} />
					<Table.Column
						title="操作"
						dataIndex="id"
						key="id"
						align="center"
						fixed="right"
						render={(id, records) => (
							<Space size={16} className="padding-lr-16">
								<div
									className="color-165dff cursor-pointer"
									onClick={() => {
										handleDetail(id);
									}}
								>
									详情
								</div>

								<Permission hasPermi={['userManage:joiner:auditStatus']}>
									<JoinerAuditStatus
										ids={[id]}
										isRevoke={records.auditStatus !== 0}
										onCallback={() => {
											getTableData();
											setSelectedRowKeys([]);
											setSelectedRowObj({});
										}}
									>
										<div className={`${records.auditStatus === 0 ? 'color-165dff' : 'color-f53f3f'} cursor-pointer`}>
											{records.auditStatus === 0 ? '审核' : '撤销审核'}
										</div>
									</JoinerAuditStatus>
								</Permission>
							</Space>
						)}
					/>
				</Table>
				{/* 表格列表 结束 */}
			</div>
		</div>
	);
};

export default Index;
