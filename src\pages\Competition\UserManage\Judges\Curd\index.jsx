import {
    useEffect,
    useState,
    useRef,
    forwardRef,
    useImperativeHandle,
    memo,
} from "react";

import {
    Form,
    Input,
    Cascader,
    message,
    Button,
    Space,
    Col,
    Row,
    Select,
} from "antd";
import Breadcrumb from "@/components/Breadcrumb";
import UploadImg from '@/components/UploadImg';
import { useRouterLink } from "@/hook/useRouter";

import {
    entryJudgesDetail,
    entryJudgesRegister,
    entryJudgesUpdate,
} from "@/api/Competition/UserManage/judges";
import { listDictItem } from "@/api/Competition/ConfigCenter/index";
import { getThreeLevelData } from "@/api/common";

import { getDictData } from "@/utils/dictionary";

const Index = forwardRef((props, ref) => {
    const { linkTo, searchParams } = useRouterLink();
    const isFull = useRef();
    const [id, setId] = useState("");
    const [form] = Form.useForm();
    const account = Form.useWatch('account', form);

    const [judgesTypeOptions, setJudgesTypeOptions] = useState([]);
    const [judgesFieldOptions, setJudgesFieldOptions] = useState([]);
    const [areaOptions, setAreaOptions] = useState([]);
    
    // 获取选项数据
    const getOptionsData = () => {
        listDictItem({
            code: "judges_expert_type",
        }).then((res) => {
            setJudgesTypeOptions(
                (res.data || []).map((ov) => {
                    return {
                        label: ov.itemName,
                        value: ov.id,
                    };
                })
            );
        });

        listDictItem({
            code: "judges_field",
        }).then((res) => {
            setJudgesFieldOptions(
                (res.data || []).map((ov) => {
                    return {
                        label: ov.itemName,
                        value: ov.id,
                    };
                })
            );
        });

        getThreeLevelData({level: 2}).then((res) => {
            setAreaOptions(res.data || []);
        });
    };

    // 获取详情
    const getDetail = (id) => {
        entryJudgesDetail({ id })
            .then((res) => {
                const resData = res.data || {};
                const {
                    provinceCode,
                    cityCode,
                    judgeTypeIds,
                    judgesFieldIds,
                } = resData;
                resData.pwd = "不支持查看";
                resData.area = [provinceCode, cityCode].filter(
                    (ov) => ov
                );
                resData.judgeTypeIds = judgeTypeIds || [];
                resData.judgesFieldIds = judgesFieldIds || [];
                form.setFieldsValue(resData);
            })
            .catch((err) => {
                console.log("🚀 ~ entryJudgesDetail ~ err:", err);
                if (isFull.current) {
                    linkTo(-1);
                }
            });
    };

    // 提交
    const handleSubmit = () => {
        return new Promise((resolve) => {
            form.validateFields().then((values) => {
                const { pwd, area } = values;
                const [provinceCode, cityCode] = area;
				const params = {
                    ...values,
                    provinceCode,
                    cityCode,
                    pwd: pwd === "不支持查看" ? undefined : pwd,
                };
				delete params.area;
                (id ? entryJudgesUpdate : entryJudgesRegister)(params).then(() => {
                    message.success(id ? "编辑成功" : "新增成功");
                    form.resetFields();
                    setId("");
                    if (isFull.current) {
                        setTimeout(() => {
                            linkTo(-1);
                        }, 1500);
                    }

                    resolve(params);
                });
            });
        });
    };

    // 取消
    const handleCancel = () => {
        return new Promise((resolve) => {
            form.resetFields();
            setId("");
            if (isFull.current) {
                linkTo(-1);
            }
            resolve();
        });
    };

    // 弹窗形式
    useEffect(() => {
        if (
            props.formQuery &&
            props.formQuery.id &&
            id !== props.formQuery.id
        ) {
            setId(props.formQuery.id);
            getDetail(props.formQuery.id);
        }
    }, [JSON.stringify(props.formQuery)]);

    // 新窗口形式
    useEffect(() => {
        isFull.current = !props.formQuery;
        if (searchParams.get("id")) {
            setId(searchParams.get("id"));
            getDetail(searchParams.get("id"));
        }

        getOptionsData();
    }, []);

    useImperativeHandle(ref, () => {
        return {
            onCancel: handleCancel,
            onOk: handleSubmit,
        };
    });

    return (
        <div
            className={`${
                isFull.current
                    ? "flex-sub flex flex-direction-column margin-top-16"
                    : ""
            }`}
        >
            {isFull.current && (
                <Breadcrumb
                    icon="icon-userManage"
                    list={[
                        {
                            name: "赛事账号",
                            link: "/competition/userManage",
                        },
                        {
                            name: "评委方",
                            link: "/competition/userManage/judges",
                        },
                    ]}
                    name={id ? "编辑评委账号" : "新增评委账号"}
                />
            )}
            <div
                className={`${
                    isFull.current
                        ? "flex-sub margin-lr-20 margin-bottom-20 margin-top-4 padding-20 bg-color-ffffff border-radius-4"
                        : ""
                }`}
            >
                <Form
                    form={form}
                    labelCol={{
                        style: { width: "80px" },
                    }}
                    labelAlign="right"
                    initialValues={{
                        id: "",
                        area: [],
                        judgeTypeIds: [],
                        judgesFieldIds: [],
                        gender: "",
                        pwd: "123456",
                    }}
                >
                    <Form.Item name="id" hidden>
                        <Input />
                    </Form.Item>
                    <div className="flex align-center justify-start margin-bottom-16 line-height-24">
                        <div className="width-6 height-16 border-radius-4 bg-color-165dff"></div>
                        <div className="font-size-16 font-weight-500 margin-left-8">
                            基本信息
                        </div>
                    </div>
                    <Row gutter={[16, 0]}>
                        <Col xxl={8} xl={8} lg={8} md={12} sm={24}>
                            <Form.Item
                                label="评委编号"
                                name="account"
                                rules={[
                                    {
                                        required: true,
                                        message: "请输入评委编号",
                                    },
                                ]}
                            >
                                <Input
                                    disabled={id}
                                    placeholder="请输入评委编号"
                                />
                            </Form.Item>
                        </Col>
                        <Col xxl={8} xl={8} lg={8} md={12} sm={24}>
                            <Form.Item
                                label="评委名称"
                                name="name"
                                rules={[
                                    {
                                        required: true,
                                        message: "请输入评委名称",
                                    },
                                ]}
                            >
                                <Input placeholder="请输入评委名称" />
                            </Form.Item>
                        </Col>
                        <Col xxl={8} xl={8} lg={8} md={12} sm={24}>
                            <Form.Item label="性别" name="gender" required>
                                <Select options={getDictData("gender")} />
                            </Form.Item>
                        </Col>
                        <Col xxl={8} xl={8} lg={8} md={12} sm={24}>
                            <Form.Item label="证件类型" name="idType">
                                <Select
                                    options={getDictData("idType")}
                                    placeholder="请选择证件类型"
                                    allowClear
                                />
                            </Form.Item>
                        </Col>
                        <Col xxl={8} xl={8} lg={8} md={12} sm={24}>
                            <Form.Item label="证件号码" name="idNumber">
                                <Input placeholder="请输入证件号码" />
                            </Form.Item>
                        </Col>
                        <Col xxl={8} xl={8} lg={8} md={12} sm={24}>
                            <Form.Item
                                label="联系方式"
                                name="mobile"
                                rules={[
                                    {
                                        required: true,
                                        message: "请输入联系方式",
                                    },
                                ]}
                            >
                                <Input placeholder="请输入联系方式" />
                            </Form.Item>
                        </Col>
                        <Col xxl={8} xl={8} lg={8} md={12} sm={24}>
                            <Form.Item
                                label="单位名称"
                                name="companyName"
                                rules={[
                                    {
                                        required: true,
                                        message: "请输入单位名称",
                                    },
                                ]}
                            >
                                <Input placeholder="请输入单位名称" />
                            </Form.Item>
                        </Col>
                        <Col xxl={8} xl={8} lg={8} md={12} sm={24}>
                            <Form.Item label="职务" name="positionName">
                                <Input placeholder="请输入职务" />
                            </Form.Item>
                        </Col>
                        <Col xxl={8} xl={8} lg={8} md={12} sm={24}>
                            <Form.Item label="职称" name="judgesTitle">
                                <Input placeholder="请输入职称" />
                            </Form.Item>
                        </Col>
                        <Col xxl={8} xl={8} lg={8} md={12} sm={24}>
                            <Form.Item label="所在地区" name="area">
                                <Cascader
                                    options={areaOptions}
                                    placeholder="请选择所在地区"
									displayRender={label => label.join('-')}
                                />
                            </Form.Item>
                        </Col>
                        <Col xxl={8} xl={8} lg={8} md={12} sm={24}>
                            <Form.Item label="联系地址" name="address">
                                <Input placeholder="请输入联系地址" />
                            </Form.Item>
                        </Col>
                        <Col xxl={8} xl={8} lg={8} md={12} sm={24}>
                            <Form.Item label="邮箱" name="email">
                                <Input placeholder="请输入邮箱" />
                            </Form.Item>
                        </Col>
                        <Col xxl={8} xl={8} lg={8} md={12} sm={24}>
                            <Form.Item label="评委类型" name="judgeTypeIds">
                                <Select
                                    mode="multiple"
                                    options={judgesTypeOptions}
                                    placeholder="请输入评委类型"
                                    allowClear
                                />
                            </Form.Item>
                        </Col>
                        <Col xxl={16} xl={16} lg={16} md={12} sm={24}>
                            <Form.Item label="擅长领域" name="judgesFieldIds">
                                <Select
                                    mode="multiple"
                                    options={judgesFieldOptions}
                                    placeholder="请输入擅长领域"
                                    allowClear
                                />
                            </Form.Item>
                        </Col>
                        <Col xxl={24} xl={24} lg={24} md={24} sm={24}>
                            <Form.Item label="评委头像" name="wxAvatarUrl">
                            <UploadImg
                                size={10}
                                width={104}
                                height={104}
                                tips='建议尺寸：200px*200px'
                            />
                            </Form.Item>
                        </Col>
                    </Row>
                    <div className="flex align-center justify-start margin-bottom-16 line-height-24">
                        <div className="width-6 height-16 border-radius-4 bg-color-165dff"></div>
                        <div className="font-size-16 font-weight-500 margin-left-8">
                            账号密码
                        </div>
                    </div>
                    <Row gutter={[16, 0]}>
                        <Col xxl={8} xl={8} lg={8} md={12} sm={24}>
                            <Form.Item
                                label="评委账号"
                                required
                            >
                                <Input  
                                    value={account}
                                    disabled={true}
                                    placeholder="请输入评委账号"
                                />
                            </Form.Item>
                        </Col>
                        <Col xxl={8} xl={8} lg={8} md={12} sm={24}>
                            <Form.Item
                                label="登录密码"
                                name="pwd"
                                rules={[
                                    {
                                        required: true,
                                        message: "请输入登录密码",
                                    },
                                ]}
                            >
                                <Input.Password
                                    autoComplete="off"
                                    placeholder="请输入登录密码"
                                    onFocus={(e) => {
                                        if (
                                            id &&
                                            e.target.value === "不支持查看"
                                        ) {
                                            form.setFieldValue("pwd", "");
                                        }
                                    }}
                                    onBlur={(e) => {
                                        if (id && e.target.value === "") {
                                            form.setFieldValue(
                                                "pwd",
                                                "不支持查看"
                                            );
                                        }
                                    }}
                                />
                            </Form.Item>
                        </Col>
                    </Row>
                    {isFull.current && (
                        <Form.Item label=" " colon={false}>
                            <Space size={16}>
                                <Button type="primary" onClick={handleSubmit}>
                                    提交
                                </Button>
                                <Button onClick={handleCancel}>取消</Button>
                            </Space>
                        </Form.Item>
                    )}
                </Form>
            </div>
        </div>
    );
});

export default memo(Index);
