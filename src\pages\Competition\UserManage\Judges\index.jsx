import { useEffect, useState, useRef } from 'react';
import { Row, Col, Space, Form, Input, Select, Table, message, Button, Switch, Modal, Popconfirm, Cascader } from 'antd';
import { ImportOutlined, ExportOutlined, SearchOutlined, ReloadOutlined } from '@ant-design/icons';
import Breadcrumb from '@/components/Breadcrumb';
import Permission from '@/components/Permission';
import ModalForm from '@/components/ModalForm';
import ImportForm from '@/components/ImportForm';
import Curd from '@/pages/Competition/UserManage/Judges/Curd';

import dayjs from 'dayjs';
import { getDictData, getDictLabelByValue } from '@/utils/dictionary';
import { download } from '@/utils/common';

import { getThreeLevelData } from '@/api/common';
import { listDictItem } from '@/api/Competition/ConfigCenter/index';
import { entryJudgesPage, batchUseStatus, entryJudgesExport, entryJudgesImport, entryJudgesBatchDel } from '@/api/Competition/UserManage/judges';

const Index = () => {
	const ModalFormCurdRef = useRef();
	const ModalFormImportRef = useRef();
	const [form] = Form.useForm();
	const [dataSource, setDataSource] = useState([]);
	const [pagination, setPagination] = useState({
		current: 1,
		pageSize: 10,
		total: 0,
		showTotal: (total) => `共 ${total} 条`,
	});

	// 选择
	const [selectedRowKeys, setSelectedRowKeys] = useState([]);
	const [selectedRowObj, setSelectedRowObj] = useState({});

	// 筛选数据
	const [judgesTypeOptions, setJudgesTypeOptions] = useState([]);
	const [judgesFieldOptions, setJudgesFieldOptions] = useState([]);
	const [areaOptions, setAreaOptions] = useState([]);

	// 获取选项数据
	const getOptionsData = () => {
		listDictItem({
			code: 'judges_expert_type',
		}).then((res) => {
			setJudgesTypeOptions(
				(res.data || []).map((ov) => {
					return {
						label: ov.itemName,
						value: ov.id,
					};
				})
			);
		});

		listDictItem({
			code: 'judges_field',
		}).then((res) => {
			setJudgesFieldOptions(
				(res.data || []).map((ov) => {
					return {
						label: ov.itemName,
						value: ov.id,
					};
				})
			);
		});

		getThreeLevelData({ level: 2 }).then((res) => {
			setAreaOptions(res.data || []);
		});
	};

	// 搜索
	const searchData = (current = 1, pageSize = pagination.pageSize) => {
		pagination.current = current;
		pagination.pageSize = pageSize;
		setPagination({ ...pagination });
		getTableData();
		setSelectedRowKeys([]);
		setSelectedRowObj({});
	};

	// 导入弹窗显示
	const importModalShow = () => {
		ModalFormImportRef.current.setOpen(true);
		ModalFormImportRef.current.setTitle('表格导入');
	};

	// 导入
	const handelImport = (formData) => {
		entryJudgesImport(formData).then(() => {
			message.success('导入成功');
			pagination.current = 1;
			setPagination({ ...pagination });
			getTableData();
		});
	};

	// 导出
	const exportOutData = () => {
		const { name, useStatus } = form.getFieldValue();
		entryJudgesExport({
			name,
			useStatus,
		}).then((res) => {
			download.excel(res, `评委方列表-${dayjs().format('YYYYMMDDhhmmss')}`);
			message.success('导出成功');
		});
	};

	// 获取表格数据
	const getTableData = () => {
		const params = { ...form.getFieldValue() };
		const [provinceCode = undefined, cityCode = undefined, areaCode = undefined] = params.area || [];
		delete params.area;
		entryJudgesPage({
			...params,
			pageNum: pagination.current,
			pageSize: pagination.pageSize,
			provinceCode,
			cityCode,
			areaCode,
		}).then((res) => {
			const { total, records } = res.data;
			pagination.total = total - 0;
			setDataSource(records);
			setPagination({ ...pagination });
		});
	};

	// 启用/禁用
	const changeUserStatus = (useStatus, id = '') => {
		Modal.confirm({
			title: '提示',
			content: `是否确定${useStatus === 1 ? '启用' : '禁用'}吗？`,
			okText: '确定',
			cancelText: '取消',
			onOk: () => {
				const ids = id ? [id] : selectedRowKeys;
				batchUseStatus({ ids, useStatus }).then(() => {
					message.success('操作成功');
					getTableData();
					setSelectedRowKeys([]);
					setSelectedRowObj({});
				});
			},
		});
	};

	// 删除
	const handleDel = (id) => {
		const ids = id ? [id] : selectedRowKeys;
		entryJudgesBatchDel({ ids }).then(() => {
			getTableData();
			message.success('操作成功');
		});
	};

	// 新建/编辑 打开表单
	const [formQuery, setFormQuery] = useState({ id: '' });
	const handleOpenForm = (id = '') => {
		ModalFormCurdRef.current.setOpen(true);
		ModalFormCurdRef.current.setTitle(id ? '编辑评委账号' : '新建评委账号');
		setFormQuery({ id });
	};

	useEffect(() => {
		getTableData();
		getOptionsData();
	}, []);

	return (
		<div className="flex-sub flex flex-direction-column margin-top-16">
			{/* 面包屑 开始 */}
			<Breadcrumb
				icon="icon-userManage"
				list={[
					{
						name: '赛事账号',
						link: '/competition/userManage',
					},
				]}
				name="评委方"
			/>
			{/* 面包屑 结束 */}
			<div className="flex-sub margin-lr-20 margin-bottom-20 margin-top-4 padding-20 bg-color-ffffff border-radius-4">
				{/* 筛选条件 开始 */}
				<div className="flex margin-bottom-16 padding-bottom-16 border-bottom-e5e6eb">
					<div className="flex-sub padding-right-16 border-right-e5e6eb">
						<Form
							form={form}
							name="filter"
							labelCol={{
								style: { width: '68px' },
							}}
							labelAlign="left"
							className="form-filter"
						>
							<Row gutter={[16, 16]}>
								<Col xs={24} sm={24} md={12} lg={12}>
									<Form.Item label="评委名称" name="name">
										<Input placeholder="请输入评委名称" />
									</Form.Item>
								</Col>
								<Col xs={24} sm={24} md={12} lg={12}>
									<Form.Item label="启用状态" name="useStatus">
										<Select options={getDictData('userStatus')} placeholder="请选择启用状态" allowClear />
									</Form.Item>
								</Col>
								<Col xs={24} sm={24} md={12} lg={12}>
									<Form.Item label="评委类型" name="judgeTypeIds">
										<Select mode="multiple" options={judgesTypeOptions} placeholder="请输入评委类型" allowClear />
									</Form.Item>
								</Col>
								<Col xs={24} sm={24} md={12} lg={12}>
									<Form.Item label="擅长领域" name="judgesFieldIds">
										<Select mode="multiple" options={judgesFieldOptions} placeholder="请输入擅长领域" allowClear />
									</Form.Item>
								</Col>
								<Col xs={24} sm={24} md={12} lg={12}>
									<Form.Item label="评委编号" name="account">
										<Input placeholder="请输入评委编号" />
									</Form.Item>
								</Col>
								<Col xs={24} sm={24} md={12} lg={12}>
									<Form.Item label="所在地区" name="area">
										<Cascader options={areaOptions} placeholder="请选择所在地区" displayRender={(label) => label.join('-')} />
									</Form.Item>
								</Col>
							</Row>
						</Form>
					</div>
					<div className="flex align-start padding-left-16 max-width-180">
						<Row gutter={[16, 16]}>
							<Permission hasPermi={['competition:userManage:judges:import']}>
								<Col span={12}>
									<Button type="primary" onClick={importModalShow}>
										<ImportOutlined />
										<span>导入</span>
									</Button>
								</Col>
							</Permission>
							<Permission hasPermi={['competition:userManage:judges:export']}>
								<Col span={12}>
									{' '}
									<Button type="primary" onClick={exportOutData}>
										<ExportOutlined />
										<span>导出</span>
									</Button>
								</Col>
							</Permission>
							<Col span={12}>
								<Button
									type="primary"
									onClick={() => {
										searchData();
									}}
								>
									<SearchOutlined />
									<span>查询</span>
								</Button>
							</Col>
							<Col span={12}>
								<Button
									onClick={() => {
										form.resetFields();
										pagination.current = 1;
										setPagination(pagination);
										getTableData();
									}}
								>
									<ReloadOutlined />
									<span>重置</span>
								</Button>
							</Col>
						</Row>
					</div>
				</div>
				{/* 筛选条件 结束 */}
				{/* 按钮操作 开始 */}
				<Space size={16} className="margin-bottom-16">
					<Permission hasPermi={['competition:userManage:judges:add']}>
						<Button
							type="primary"
							onClick={() => {
								handleOpenForm();
							}}
						>
							新建
						</Button>
					</Permission>
					<Permission hasPermi={['competition:userManage:judges:useStatus']}>
						<Button
							type="primary"
							disabled={selectedRowKeys.length === 0}
							onClick={() => {
								changeUserStatus(1);
							}}
						>
							启用
						</Button>
					</Permission>
					<Permission hasPermi={['competition:userManage:judges:useStatus']}>
						<Button
							type="primary"
							disabled={selectedRowKeys.length === 0}
							onClick={() => {
								changeUserStatus(0);
							}}
						>
							禁用
						</Button>
					</Permission>
					<Permission hasPermi={['competition:userManage:judges:del']}>
						<Button
							type="primary"
							disabled={selectedRowKeys.length === 0}
							onClick={() => {
								handleDel();
							}}
						>
							删除
						</Button>
					</Permission>
					{selectedRowKeys.length > 0 && (
						<Space size={4}>
							<div>已选择：</div>
							<div className="color-165dff">{selectedRowKeys.length}</div>
							<div>项</div>
						</Space>
					)}
				</Space>
				{/* 按钮操作 结束 */}
				{/* 表格列表 开始 */}
				<Table
					rowKey="id"
					dataSource={dataSource}
					pagination={{
						...pagination,
						showQuickJumper: true,
						showSizeChanger: true,
						onChange: searchData,
					}}
					rowSelection={{
						selectedRowKeys,
						onChange: (checkedRowKeys) => {
							selectedRowObj[pagination.current] = checkedRowKeys;
							setSelectedRowObj({ ...selectedRowObj });
							setSelectedRowKeys(Object.values(selectedRowObj).flat(2));
						},
					}}
					scroll={{ x: 'max-content' }}
				>
					<Table.Column title="评委编号" dataIndex="account" key="account" fixed="left" />
					<Table.Column title="评委名称" dataIndex="name" key="name" fixed="left" />
					<Table.Column
						title="评委类型"
						dataIndex="judgeTypeNames"
						key="judgeTypeNames"
						render={(judgeTypeNames) => {
							return (judgeTypeNames || []).join('、') || '--';
						}}
					/>
					<Table.Column
						title="擅长领域"
						dataIndex="judgesFieldNames"
						key="judgesFieldNames"
						render={(judgesFieldNames) => {
							return (judgesFieldNames || []).join('、') || '--';
						}}
					/>
					<Table.Column title="单位名称" dataIndex="companyName" key="companyName" />
					<Table.Column title="联系方式" dataIndex="mobile" key="mobile" />
					<Table.Column
						title="所在区域 "
						dataIndex="area"
						key="area"
						render={(_, record) => {
							const { provinceName, cityName, areaName } = record;
							return [provinceName, cityName, areaName].filter((ov) => ov).join('-') || '--';
						}}
					/>
					<Table.Column
						title="启用状态"
						dataIndex="useStatus"
						key="useStatus"
						align="center"
						render={(useStatus, record) => (
							<Permission hasPermi={['userManage:joiner:useStatus']} empty={getDictLabelByValue('userStatus', useStatus)}>
								<Switch
									className="vertical-align-top"
									checked={useStatus === 1}
									checkedChildren="启用"
									unCheckedChildren="禁用"
									onChange={(checked) => {
										changeUserStatus(checked ? 1 : 0, record.id);
									}}
								/>
							</Permission>
						)}
					/>
					<Table.Column
						title="操作"
						dataIndex="id"
						key="id"
						align="center"
						fixed="right"
						render={(id) => (
							<Space size={16} className="padding-lr-16">
								<Permission hasPermi={['competition:userManage:judges:edit']}>
									<div
										className="color-165dff cursor-pointer"
										onClick={() => {
											handleOpenForm(id);
										}}
									>
										编辑
									</div>
								</Permission>
								<Permission hasPermi={['competition:userManage:judges:del']}>
									<Popconfirm
										icon={false}
										description="是否确认删除此数据?"
										onConfirm={() => {
											handleDel(id);
										}}
										okText="确认"
										cancelText="取消"
									>
										<div className="color-165dff cursor-pointer">删除</div>
									</Popconfirm>
								</Permission>
							</Space>
						)}
					/>
				</Table>
				{/* 表格列表 结束 */}
				{/* 编辑/新建 弹窗 开始 */}
				<ModalForm
					ref={ModalFormCurdRef}
					onOk={() => {
						searchData(pagination.current);
					}}
					FormComp={(props) => <Curd ref={props.FormCompRef} formQuery={formQuery} />}
					modelConfig={{
						width: '80%',
						styles: {
							body: { padding: '0 24px' },
						},
					}}
				/>
				{/* 编辑/新建 弹窗 结束 */}
				{/* 导入 开始 */}
				<ModalForm
					ref={ModalFormImportRef}
					modelConfig={{
						styles: {
							body: {
								minHeight: 'unset',
							},
						},
					}}
					onOk={handelImport}
					FormComp={(props) => (
						<ImportForm
							ref={props.FormCompRef}
							fileName="file"
							tplUrl="https://gbac-produce.oss-cn-shenzhen.aliyuncs.com/tip/competition/%E8%AF%84%E5%A7%94%E6%96%B9%E8%B4%A6%E5%8F%B7%E5%AF%BC%E5%85%A5%E6%A8%A1%E7%89%88.xlsx"
						/>
					)}
				/>
				{/* 导入 结束 */}
			</div>
		</div>
	);
};

export default Index;
