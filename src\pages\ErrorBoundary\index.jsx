import { useRouteError } from 'react-router-dom';
import { useEffect, useState } from 'react';
import { useRouterLink } from '@/hook/useRouter';
import { ExclamationCircleOutlined } from '@ant-design/icons';
import { Button } from 'antd';

const ErrorBoundary = () => {
	const { linkTo } = useRouterLink();
	const reFlash = () => {
		window.location.reload();
	};
	const toHome = () => {
		linkTo('/', {});
	};
	//未发现的参考错误:路径未被定义
	return (
		<div className="ErrorBoundary bg-color-f4f7fb min-height-100vh flex align-center justify-center">
			<div className="height-20vw padding-tb-20 padding-lr-30 border-radius-20 bg-color-ffffff">
				<div className="flex align-start">
					<ExclamationCircleOutlined style={{ color: '#faad14', fontSize: '24px' }} />
					<div className="padding-left-10 font-size-24">404,访问可能更新啦，请刷新页面</div>
				</div>
				<div className="flex align-start padding-top-60 justify-end">
					<Button className="margin-right-20" onClick={reFlash}>
						{' '}
						刷新页面{' '}
					</Button>
					<Button type="primary" onClick={toHome}>
						{' '}
						回到首页{' '}
					</Button>
				</div>
			</div>
		</div>
	);
};
export default ErrorBoundary;
