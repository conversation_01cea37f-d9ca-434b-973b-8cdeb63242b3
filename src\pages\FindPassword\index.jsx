import { useState } from 'react';
import { Input, Button, Form, Steps, Result } from 'antd';
import VerifyCode from '@/components/VerifyCode';
import { useRouterLink } from '@/hook/useRouter';

import { findPasswordByBackground, isExistMobileByBackground } from '@/api/login';

import './index.scss';

const Index = () => {
	const { linkTo } = useRouterLink();
	const [form] = Form.useForm();
	const [current, setCurrent] = useState(0);

	const validPassword = (_, values) => {
		if (values === form.getFieldValue('pwd')) {
			return Promise.resolve();
		} else {
			return Promise.reject('两次密码不一致');
		}
	};

	const validPhone = (_, values) => {
		return isExistMobileByBackground({ phone: values })
			.then((res) => {
				if (res.data) {
					return Promise.resolve();
				} else {
					return Promise.reject('账号不存在！');
				}
			})
			.catch((err) => {
				return Promise.reject(err.message);
			});
	};

	const getMobile = () => {
		console.log(form.validateFields(['phone']));
		return new Promise((resolve, reject) => {
			form.validateFields(['phone'])
				.then((values) => {
					console.log(1);
					resolve(values.phone);
				})
				.catch((err) => {
					console.log(err);
					reject();
				});
		});
	};

	const onSubmit = () => {
		form.validateFields().then((values) => {
			if (current === 0) {
				setCurrent(1);
			} else if (current === 1) {
				findPasswordByBackground(values)
					.then(() => {
						setCurrent(2);
					})
					.catch(() => {
						setCurrent(0);
					});
			}
		});
	};

	return (
		<div className="Login flex align-center justify-center width1-100vw height-100vh position-relative">
			<div className="login-bg login-bg-1"></div>
			<div className="login-bg login-bg-2"></div>
			<div className="login-bg login-bg-3"></div>
			<div className="login-bg login-bg-4"></div>
			<div className={`padding-lr-80 padding-tb-60 border-radius-8 border-box bg-color-ffffff overflow-hidden`} style={{ width: '600px' }}>
				<div className="font-size-24 line-height-32 font-weight-500 color-1d2129">找回密码</div>
				<Steps
					current={current}
					className="margin-tb-24"
					items={[
						{
							title: '填写手机号',
						},
						{
							title: '重置密码',
						},
						{
							title: '完成',
						},
					]}
				/>
				<Form form={form}>
					<Form.Item
						name="phone"
						hidden={current !== 0}
						rules={[
							{ required: current === 0, message: '请输入手机号' },
							{ pattern: /^1[3-9]\d{9}$/, message: '请输入正确格式手机号码' },
							{ validator: validPhone },
						]}
						validateFirst
					>
						{/* 不要记住密码自动填入 */}
						<Input size="large" placeholder="请输入手机号" autoComplete="new-password" />
					</Form.Item>
					<Form.Item name="verificationCode" hidden={current !== 0} rules={[{ required: current === 0, message: '请输入手机验证码' }]}>
						<Input
							size="large"
							placeholder="请输入手机验证码"
							suffix={<VerifyCode getMobile={getMobile} />}
							autoComplete="new-password"
						/>
					</Form.Item>
					<Form.Item
						name="pwd"
						hidden={current !== 1}
						rules={[
							{ required: current === 1, message: '请输入新密码' },
							{ pattern: /^(?=.*[a-zA-Z])(?=.*\d).{6,}$/, message: '请输入6位以上包含数字、字母的密码' },
						]}
					>
						<Input size="large" type="password" placeholder="请输入新密码" autoComplete="new-password" />
					</Form.Item>
					<Form.Item
						name="pwd2"
						hidden={current !== 1}
						rules={[{ required: current === 1, message: '请输入确认密码' }, { validator: validPassword }]}
						validateFirst
					>
						<Input size="large" type="password" placeholder="请输入确认密码" autoComplete="new-password" />
					</Form.Item>
				</Form>
				{current === 2 ? (
					<>
						<Result
							icon={null}
							title="修改成功"
							subTitle="请重新登录"
							extra={[
								<Button
									type="primary"
									key="console"
									onClick={() => {
										linkTo('/login');
									}}
								>
									重新登录
								</Button>,
							]}
						/>
					</>
				) : (
					<>
						<div className="flex align-center justify-end">
							<div
								className="a color-165dff"
								onClick={() => {
									linkTo('/login');
								}}
							>
								去登录
							</div>
						</div>
						{/* 登录 按钮 开始 */}
						<Button size={'large'} type={'primary'} block htmlType={'submit'} className="login-btn margin-top-30" onClick={onSubmit}>
							{current === 0 ? '下一步' : '提交'}
						</Button>
						{/* 登录 按钮 开始 */}
					</>
				)}
			</div>
		</div>
	);
};

export default Index;
