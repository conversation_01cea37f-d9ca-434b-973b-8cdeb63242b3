import { useEffect, useState, useRef, forwardRef, useImperativeHandle, memo } from 'react';

import { Form, Input, InputNumber, Select, DatePicker, message, Button, Space, Cascader } from 'antd';

import { addUser, updateUser, isUserValid, getUserByUserId } from '@/api/IndustrialBrain/User/index';

import { addUserPermission, updateUserPermission, removeUserPermission } from '@/api/IndustrialBrain/Menu/index';

import dayjs from 'dayjs';

import './index.scss';

const pcPermissionList = [
	{
		value: 'dashboard',
		label: '首页',
		children: [],
		disabled: true,
	},
	{
		value: 'enterprise',
		label: '发现企业',
		children: [
			{
				value: 'emphasize',
				label: '企业总览',
				children: [
					{
						value: 'emphasizeDetail',
						label: '企业详情'
					},
				]
			},
			{
				value: 'search',
				label: '企业检索',
				children: [
					{
						value: 'iframe/intelligent-selection/selection-view/advanced-search',
						label: '广域企业筛查'
					},
					{
						value: 'index',
						label: '地区精准筛查',
					},
				]
			},
			{
				value: 'iframe/capital-investment/investment-institutions/key',
				label: '投资机构',
				children: [],
			},
			{
				value: 'radar',
				label: '湾创雷达',
				children: [
					{
						value: 'index',
						label: '本地精准触达'
					},
					{
						value: 'iframe/regional/regional-investment/map-search',
						label: '全域地图',
					},
				]
			},
			{
				value: 'report',
				label: '企业测评报告',
				children: [
					{
						value: 'innovationAbility',
						label: '创新能力测评'
					},
					{
						value: 'businessPlan',
						label: 'AI项目智能分析',
					},
				]
			},
		],
	},

	{
		value: 'industryChain',
		label: '产业分析',
		children: [],
	},
	{
		value: 'regionalEconomies',
		label: '区域经济',
		children: [
			{
				value: 'iframe/enterprise-migration/enterprise-overview',
				label: '企业总览',
			},
			{
				value: 'iframe/enterprise-migration/enterprise-add-standard',
				label: '企业新增',
			},
			{
				value: 'investmentAndFinancing',
				label: '地区投融资',
				children: [
					{
						value: 'iframe/regional-investment-financing/corporate-finance',
						label: '企业融资',
					},
					{
						value: 'iframe/regional-investment-financing/industrial-transfer',
						label: '产业承接转移',
					},
					{
						value: 'iframe/regional-investment-financing/investments-abroad',
						label: '对外投资',
					},
					{
						value: 'iframe/enterprise-migration/moveAndOut/crossing-city',
						label: '迁入迁出',
					},
				]
			},
		],
	},
	{
		value: 'QXBpage',
		label: '大数据招商',
		children: [
			{
				value: 'iframe/recommend/company-recommend/clue',
				label: '招商智推',
			},
			{
				value: 'iframe/supply-chain/dashboard/supply-dashboard',
				label: '供应链招商',
			},
			{
				value: 'iframe/capital-investment/capital/financing-dynamic',
				label: '资本招商',
			},
			{
				value: 'iframe/promotion-investment/promotion-group',
				label: '榜单招商',
			},
			{
				value: 'iframe/park-module/park-search',
				label: '园区招商',
			},
			{
				value: 'iframe/relation/touch-relation/direct-contact',
				label: '招商智达',
			},
		],
	},
	{
		value: 'incubationBreeding',
		label: '工作台',
		children: [
			{
				value: 'iframe/enterprise-monitor-new/layout/enterprise-monitor-new-list',
				label: '企业监控',
			},
			{
				value: 'myAttention',
				label: '我的关注',
			},
			{
				value: 'analysisModel',
				label: '分析模型',
			},
			{
				value: 'iframe/industrial-insight/portal-home',
				label: '产业洞察',
			},
			{
				value: 'myReport',
				label: '我的报告',
			},
		],
	},
	{
		value: 'dataScreen',
		label: '数据大屏',
	},
];

const Index = forwardRef((props, ref) => {
	const [id, setId] = useState('');
	const [form] = Form.useForm();
	const [detail, setDetail] = useState({});

	// 获取详情
	const getDetail = (params) => {
		if (!params) {
			setTimeout(() => {
				form.resetFields();
				setDetail({});
			}, 100);
			return;
		}
		form.setFieldsValue({
			id,
			permissionName: params.permissionName || '',
			pcPermissionModule: params.pcPermissionModule ? JSON.parse(params.pcPermissionModule) : [],
			weChatMiniProgramPermissionModule: params.weChatMiniProgramPermissionModule ? JSON.parse(params.weChatMiniProgramPermissionModule) : [],
		});
		setDetail(params);
	};

	// 提交
	const handleSubmit = () => {
		return new Promise((resolve) => {
			form.validateFields().then((res) => {
				const params = {
					id,
					...res,
					pcPermissionModule: JSON.stringify(res.pcPermissionModule),
					weChatMiniProgramPermissionModule: JSON.stringify(res.weChatMiniProgramPermissionModule),
				};
				(id ? updateUserPermission : addUserPermission)(params).then(() => {
					message.success(id ? '编辑成功' : '新增成功');
					form.resetFields();
					setId('');
					resolve(params);
				});
			});
		});
	};

	// 取消
	const handleCancel = () => {
		return new Promise((resolve) => {
			form.resetFields();
			setId('');
			resolve();
		});
	};

	// 弹窗形式
	useEffect(() => {
		if (props.formQuery && props.formQuery.id) {
			setId(props.formQuery.id);
			getDetail(props.formQuery);
		} else {
			setId('');
			getDetail();
		}
	}, [JSON.stringify(props.formQuery)]);

	useImperativeHandle(ref, () => {
		return {
			onCancel: handleCancel,
			onOk: handleSubmit,
		};
	});

	return (
		<Form
			form={form}
			labelCol={{
				style: { width: '120px' },
			}}
			labelAlign="right"
			initialValues={{
				permissionName: '',
				weChatMiniProgramPermissionModule: [],
				pcPermissionModule: [['dashboard'], ['enterprise'], ['industryChain'], ['regionalEconomies'], ['QXBpage'], ['incubationBreeding']],
			}}
		>
			<Form.Item
				label="菜单名称"
				name="permissionName"
				rules={[
					{
						required: true,
						message: '请输入菜单名称',
					},
				]}
			>
				<Input placeholder="请输入菜单名称" />
			</Form.Item>
			<Form.Item
				label="用户菜单"
				name="pcPermissionModule"
				rules={[
					{
						required: true,
						type: 'array',
						message: '请选择用户菜单',
					},
				]}
			>
				<Cascader.Panel multiple options={pcPermissionList} placeholder="请选择用户角色" />
			</Form.Item>
		</Form>
	);
});

export default memo(Index);
