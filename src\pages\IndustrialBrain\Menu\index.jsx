import { useEffect, useRef, useState } from 'react';
import Breadcrumb from '@/components/Breadcrumb';
import { Row, Col, Space, Form, Input, Select, Table, Popconfirm, Button, message, Modal, Upload } from 'antd';
import { SearchOutlined, ReloadOutlined, PlusOutlined } from '@ant-design/icons';
import Curd from '@/pages/IndustrialBrain/Menu/Curd/index';
import ModalForm from '@/components/ModalForm';

import { listUserPermission, removeUserPermission, importEnterpriseByExcel } from '@/api/IndustrialBrain/Menu/index';

const Index = () => {
	const ModalFormRef = useRef();
	const [dataSource, setDataSource] = useState([]);

	// 获取表格数据
	const getTableData = () => {
		listUserPermission({}).then((res) => {
			setDataSource(res.data || []);
		});
	};

	useEffect(() => {
		getTableData();
	}, []);

	// 删除
	const handleDel = (id) => {
		removeUserPermission({ id }).then(() => {
			getTableData();
			message.success('操作成功');
		});
	};

	// 新建/编辑 打开表单
	const [formQuery, setFormQuery] = useState({ id: '' });
	const handleOpenForm = (record = {}) => {
		ModalFormRef.current.setOpen(true);
		ModalFormRef.current.setTitle(record.id ? '编辑菜单' : '新建菜单');
		setFormQuery({ ...record });
	};

	return (
		<div className="flex-sub flex flex-direction-column margin-top-16">
			{/* 面包屑 开始 */}
			<Breadcrumb
				icon="icon-system"
				list={
					[
						// {
						// 	name: '系统管理',
						// 	link: '/system',
						// },
					]
				}
				name="菜单管理"
			/>
			{/* 面包屑 结束 */}
			<div className="flex-sub margin-lr-20 margin-bottom-20 margin-top-4 padding-20 bg-color-ffffff border-radius-4">
				{/* 按钮操作 开始 */}
				<Space size={16} className="margin-bottom-16">
					<Space
						size={10}
						className="padding-lr-16 height-32 line-height-32 font-size-14 border-radius-2 bg-color-165dff color-ffffff cursor-pointer"
						onClick={() => {
							handleOpenForm({});
						}}
					>
						<PlusOutlined />
						<span>新建</span>
					</Space>
					<Space
						size={10}
						className="padding-lr-14 height-32 line-height-32 font-size-14 border-radius-2 bg-color-165dff color-ffffff cursor-pointer"
						onClick={() => {
							getTableData();
						}}
					>
						<SearchOutlined />
						<span>查询</span>
					</Space>
					<Upload
						fileList={[]}
						showUploadList={false}
						beforeUpload={(file, fileList) => {
							const formData = new FormData();
							formData.append('file', file);
							importEnterpriseByExcel(formData).then((res) => {
								message.success('上传成功');
							});
							return false;
						}}
						multiple={false}
						maxCount={undefined}
					>
						<Button type="primary">导入企业</Button>
					</Upload>
				</Space>

				{/* 按钮操作 结束 */}

				{/* 表格列表 开始 */}
				<Table rowKey="id" dataSource={dataSource} pagination={false} scroll={{ x: 'max-content' }}>
					<Table.Column title="序号" dataIndex="index" key="index" render={(key, record, index) => <div>{index + 1}</div>} />
					<Table.Column title="菜单名称" dataIndex="permissionName" key="permissionName" />
					<Table.Column
						title="操作"
						fixed="right"
						dataIndex="id"
						key="id"
						align="center"
						render={(id, record) => (
							<Space size={16}>
								<div
									className="color-165dff cursor-pointer"
									onClick={() => {
										handleOpenForm(record);
									}}
								>
									编辑
								</div>
								<div
									className="color-165dff cursor-pointer"
									onClick={() => {
										handleDel(id);
									}}
								>
									删除
								</div>
							</Space>
						)}
					/>
				</Table>
				{/* 表格列表 结束 */}
				{/* 编辑/新建 弹窗 开始 */}
				<ModalForm
					ref={ModalFormRef}
					modelConfig={{
						title: '新建',
					}}
					onOk={(res) => {
						getTableData();
					}}
					FormComp={(props) => <Curd ref={props.FormCompRef} formQuery={formQuery} />}
				/>
				{/* 编辑/新建 弹窗 结束 */}
			</div>
		</div>
	);
};

export default Index;
