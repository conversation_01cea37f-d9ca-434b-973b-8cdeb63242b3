import { useEffect, useState, useRef, forwardRef, useImperativeHandle, memo } from 'react';

import { Form, Input, InputNumber, Select, DatePicker, message, Button, Space, Cascader } from 'antd';
import Breadcrumb from '@/components/Breadcrumb';
import UploadImg from '@/components/UploadImg';

import { getDictData, getDeptData } from '@/utils/dictionary';
import { useRouterLink } from '@/hook/useRouter';

import { getRoleList, userDetail, userAdd, userUpdate } from '@/api/System/index';

import { getDeptParentId, getDeptFlatData } from '@/utils/common';

import { addUser, updateUser, isUserValid, getUserByUserId } from '@/api/IndustrialBrain/User/index';

import { listUserPermission } from '@/api/IndustrialBrain/Menu/index';

import dayjs from 'dayjs';

import './index.scss';
 
const Index = forwardRef((props, ref) => {
	const { linkTo, searchParams } = useRouterLink();
	const isFull = useRef();
	const [id, setId] = useState('');
	const [form] = Form.useForm();
	const [deptList, setDeptList] = useState([]);
	const [roleList, setRoleList] = useState([]);
	const [detail, setDetail] = useState({});

	// 获取详情
	const getDetail = (userId) => {
		if (!userId) {
			setTimeout(() => {
				form.resetFields();
				setDetail({});
			}, 100);
			return;
		}
		getUserByUserId({ userId })
			.then((res) => {
				const {
					userId,
					userName,
					mobile,
					userPermissionId = '',
					aiReportNumber = 0,
					enterpriseQueryNumber = 0,
					validStartTime = '',
					validEndTime = '',
				} = res.data;
				form.setFieldsValue({
					userId,
					userName,
					mobile,
					userPermissionId,
					aiReportNumber,
					enterpriseQueryNumber,
					validStartTime: validStartTime ? dayjs(validStartTime) : '',
					validEndTime: validEndTime ? dayjs(validEndTime) : '',
				});
				setDetail(res.data);
			})
			.catch((err) => {
				console.log('🚀 ~ userDetail ~ err:', err);
				if (isFull.current) {
					linkTo(-1);
				}
			});
	};

	// 提交
	const handleSubmit = () => {
		return new Promise((resolve) => {
			form.validateFields().then((res) => {
				const params = {
					userId: id,
					...res,
					validStartTime: dayjs(res.validStartTime).format('YYYY-MM-DD HH:mm:ss'),
					validEndTime: dayjs(res.validEndTime).format('YYYY-MM-DD HH:mm:ss'),
				};
				(id ? updateUser : addUser)(params).then(() => {
					message.success(id ? '编辑成功' : '新增成功');
					form.resetFields();
					setId('');
					if (isFull.current) {
						setTimeout(() => {
							linkTo(-1);
						}, 1500);
					}

					resolve(params);
				});
			});
		});
	};

	// 取消
	const handleCancel = () => {
		return new Promise((resolve) => {
			form.resetFields();
			setId('');
			if (isFull.current) {
				linkTo(-1);
			}
			resolve();
		});
	};

	const isLoadDeptData = useRef(false);

	// 获取表格数据
	const getTableData = () => {
		listUserPermission({}).then((res) => {
			setRoleList(
				res.data.map((item) => ({
					label: item.permissionName,
					value: item.id,
				})) || []
			);
		});
	};

	useEffect(() => {
		getTableData();
	}, []);

	// 弹窗形式
	useEffect(() => {
		if (props.formQuery && props.formQuery.id) {
			setId(props.formQuery.id);
			getDetail(props.formQuery.id);
		} else {
			setId('');
			getDetail();
		}
	}, [JSON.stringify(props.formQuery)]);

	useImperativeHandle(ref, () => {
		return {
			onCancel: handleCancel,
			onOk: handleSubmit,
		};
	});

	return (
		<div className={`${isFull.current ? 'flex-sub flex flex-direction-column margin-top-16' : ''}`}>
			{isFull.current && (
				<Breadcrumb
					icon="icon-system"
					list={[
						{
							name: '账号管理',
							link: '/industrialBrain/user',
						},
					]}
					name={id ? '编辑账号' : '新增账号'}
				/>
			)}
			<div
				className={`${
					isFull.current ? 'flex-sub margin-lr-20 margin-bottom-20 margin-top-4 padding-20 bg-color-ffffff border-radius-4' : ''
				}`}
			>
				<Form
					form={form}
					labelCol={{
						style: { width: '120px' },
					}}
					labelAlign="right"
					initialValues={{
						userPermissionId: '',
					}}
				>
					<Form.Item
						label="账号名称"
						name="userName"
						rules={[
							{
								required: true,
								message: '请输入账号名称',
							},
						]}
					>
						<Input placeholder="请输入账号名称" />
					</Form.Item>
					<Form.Item
						label="联系电话"
						name="mobile"
						rules={[
							{
								required: true,
								message: '请输入联系电话',
							},
						]}
					>
						<Input placeholder="请输入联系电话" maxLength={11} />
					</Form.Item>

					<Form.Item
						label="开始时间"
						name="validStartTime"
						rules={[
							{
								required: true,
								message: '请输入开始时间',
							},
						]}
					>
						<DatePicker showTime format={'YYYY-MM-DD HH:mm:ss'} />
					</Form.Item>

					<Form.Item
						label="结束时间"
						name="validEndTime"
						rules={[
							{
								required: true,
								message: '请输入结束时间',
							},
						]}
					>
						<DatePicker showTime format={'YYYY-MM-DD HH:mm:ss'} />
					</Form.Item>

					<Form.Item
						label="用户菜单"
						name="userPermissionId"
						rules={[
							{
								required: true,
								message: '请选择用户菜单',
							},
						]}
					>
						<Select options={roleList} placeholder="请选择用户菜单" />
					</Form.Item>

					<Form.Item
						label="AI投资报告"
						name="aiReportNumber"
						rules={[
							{
								required: true,
								message: '请输入AI投资报告',
							},
						]}
					>
						<InputNumber placeholder="请输入AI投资报告" className="width-200" />
					</Form.Item>

					<Form.Item
						label="企业查询"
						name="enterpriseQueryNumber"
						rules={[
							{
								required: true,
								message: '请输入企业查询',
							},
						]}
					>
						<InputNumber placeholder="请输入企业查询" className="width-200" />
					</Form.Item>

					{isFull.current && (
						<Form.Item label=" " colon={false}>
							<Space size={16}>
								<Button type="primary" onClick={handleSubmit}>
									提交
								</Button>
								<Button onClick={handleCancel}>取消</Button>
							</Space>
						</Form.Item>
					)}
				</Form>
			</div>
		</div>
	);
});

export default memo(Index);
