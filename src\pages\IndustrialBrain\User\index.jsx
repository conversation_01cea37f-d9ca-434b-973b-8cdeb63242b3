import { useEffect, useRef, useState } from 'react';
import Breadcrumb from '@/components/Breadcrumb';
import { Row, Col, Space, Form, Input, Select, Table, Popconfirm, Cascader, message, Modal, Switch } from 'antd';
import { SearchOutlined, ReloadOutlined, PlusOutlined } from '@ant-design/icons';
import Curd from '@/pages/IndustrialBrain/User/Curd/index';
import ModalForm from '@/components/ModalForm';
import Permission from '@/components/Permission';

import { getDictData, getDictLabelByValue, getDeptData } from '@/utils/dictionary';

import { getRoleList, getUserPageData, userDel, batchUpdateIsInternalStatus } from '@/api/System';
import { pageUsers, updateUser, isUserValid, getUserByUserId } from '@/api/IndustrialBrain/User/index';
import { updatePassword } from '@/api/login';

const Index = () => {
	const ModalFormRef = useRef();
	const [form] = Form.useForm();
	const [deptList, setDeptList] = useState([]);
	const [roleList, setRoleList] = useState([]);
	const [dataSource, setDataSource] = useState([]);
	const [pagination, setPagination] = useState({
		current: 1,
		pageSize: 10,
		total: 0,
		showTotal: (total) => `共 ${total} 条`,
	});

	// 重置密码id resetId
	const [resetId, setResetId] = useState('');

	// 搜索
	const searchData = (current = 1, pageSize = pagination.pageSize) => {
		pagination.current = current;
		pagination.pageSize = pageSize;
		setPagination({ ...pagination });
		getTableData();
	};

	// 获取表格数据
	const getTableData = () => {
		const { keywords, mobile, deptId = [], roleId, status, userSource, userType, appid } = form.getFieldValue();
		const len = deptId.length;
		pageUsers({
			pageNum: pagination.current,
			pageSize: pagination.pageSize,
			userName: keywords,
			mobile,
			deptId: len > 0 ? deptId[len - 1] : undefined,
			roleId,
			status,
			userSource,
			userType,
			appids: appid ? [appid] : undefined,
		}).then((res) => {
			const { total, records } = res.data;
			pagination.total = total - 0;
			setDataSource(records);
			setPagination({ ...pagination });
		});
	};

	// 删除
	const handleDel = (userId) => {
		userDel({ userId }).then(() => {
			getTableData();
			message.success('操作成功');
		});
	};

	// 新建/编辑 打开表单
	const [formQuery, setFormQuery] = useState({ id: '' });
	const handleOpenForm = (id = '') => {
		console.log('🚀 ~ handleOpenForm ~ id:', id);

		ModalFormRef.current.setOpen(true);
		ModalFormRef.current.setTitle(id ? '编辑账号' : '新建账号');
		setFormQuery({ id });
	};

	useEffect(() => {
		getDeptData().then((data) => {
			setDeptList(data);
		});
		getRoleList({}, { isCache: true }).then(({ data }) => {
			setRoleList(
				data.map((item) => {
					return {
						value: item.id,
						label: item.roleName,
					};
				})
			);
		});
		getTableData();
	}, []);
	const insiderStatusChange = (data = {}) => {
		batchUpdateIsInternalStatus(data).then(() => {
			getTableData();
		});
	};

	return (
		<div className="flex-sub flex flex-direction-column margin-top-16">
			{/* 面包屑 开始 */}
			<Breadcrumb
				icon="icon-system"
				list={
					[
						// {
						// 	name: '系统管理',
						// 	link: '/system',
						// },
					]
				}
				name="用户管理"
			/>
			{/* 面包屑 结束 */}
			<div className="flex-sub margin-lr-20 margin-bottom-20 margin-top-4 padding-20 bg-color-ffffff border-radius-4">
				{/* 筛选条件 开始 */}
				<div className="flex margin-bottom-16 padding-bottom-16 border-bottom-e5e6eb">
					<div className="flex-sub padding-right-16 border-right-e5e6eb">
						<Form
							form={form}
							labelCol={{
								style: { width: '68px' },
							}}
							labelAlign="left"
							className="form-filter"
							initialValues={{
								deptId: [],
								userType: '2',
							}}
						>
							<Row gutter={[16, 16]}>
								<Col xs={24} sm={24} md={12} lg={8}>
									<Form.Item label="用户名" name="keywords">
										<Input placeholder="请输入用户名" />
									</Form.Item>
								</Col>
								<Col xs={24} sm={24} md={12} lg={8}>
									<Form.Item label="手机号码" name="mobile">
										<Input placeholder="请输入手机号码" />
									</Form.Item>
								</Col>
							</Row>
						</Form>
					</div>
					<Space direction="vertical" size={20} className="padding-left-16">
						<Space
							size={10}
							className="padding-lr-14 height-32 line-height-32 font-size-14 border-radius-2 bg-color-165dff color-ffffff cursor-pointer"
							onClick={() => {
								searchData();
							}}
						>
							<SearchOutlined />
							<span>查询</span>
						</Space>
						<Space
							size={10}
							className="padding-lr-14 height-32 line-height-32 font-size-14 border-radius-2 bg-color-f2f3f5 color-4e5969 cursor-pointer"
							onClick={() => {
								form.resetFields();
								pagination.current = 1;
								setPagination(pagination);
								getTableData();
							}}
						>
							<ReloadOutlined />
							<span>重置</span>
						</Space>
					</Space>
				</div>
				{/* 筛选条件 结束 */}
				{/* 按钮操作 开始 */}
				<Space size={16} className="margin-bottom-16">
					<Space
						size={10}
						className="padding-lr-16 height-32 line-height-32 font-size-14 border-radius-2 bg-color-165dff color-ffffff cursor-pointer"
						onClick={() => {
							handleOpenForm();
						}}
					>
						<PlusOutlined />
						<span>新建</span>
					</Space>
				</Space>
				{/* 按钮操作 结束 */}
				{/* 表格列表 开始 */}
				<Table
					rowKey="userId"
					dataSource={dataSource}
					pagination={{
						...pagination,
						showQuickJumper: true,
						showSizeChanger: true,
						onChange: searchData,
					}}
					scroll={{ x: 'max-content' }}
				>
					<Table.Column title="序号" dataIndex="index" key="index" render={(key, record, index) => <div>{index + 1}</div>} />
					<Table.Column title="用户名" dataIndex="userName" key="userName" />
					<Table.Column title="手机号码" dataIndex="mobile" key="mobile" render={(mobile) => <div>{mobile || '--'}</div>} />

					<Table.Column
						title="有效时间"
						dataIndex="validStartTime"
						key="validStartTime"
						render={(_, record) => (
							<div className="world-break-all">
								{record.validStartTime || ''}
								{record.validStartTime && record.validEndTime ? '-' : ''}
								{record.validEndTime || ''}
							</div>
						)}
					/>
					<Table.Column
						title="创建时间"
						dataIndex="createTime"
						key="createTime"
						width={160}
						render={(createTime, record) => <div className="world-break-all max-width-320">{(createTime || '').slice(0, 16)}</div>}
					/>
					<Table.Column
						title="操作"
						fixed="right"
						dataIndex="userId"
						key="userId"
						align="center"
						render={(id) => (
							<Space size={16}>
								<Permission hasPermi={['system:user:resetPwd']}>
									<div
										className="color-165dff cursor-pointer"
										onClick={() => {
											setResetId(id);
										}}
									>
										重置密码
									</div>
								</Permission>
								<div
									className="color-165dff cursor-pointer"
									onClick={() => {
										handleOpenForm(id);
									}}
								>
									编辑
								</div>
								<div
									className="color-165dff cursor-pointer"
									onClick={() => {
										handleOpenForm(id);
									}}
								>
									详情
								</div>
							</Space>
						)}
					/>
				</Table>
				{/* 表格列表 结束 */}
				{/* 编辑/新建 弹窗 开始 */}
				<ModalForm
					ref={ModalFormRef}
					modelConfig={{
						title: '新建',
					}}
					onOk={(res) => {
						console.log('🚀 ~ Index ~ res:', res);
						searchData(pagination.current);
					}}
					FormComp={(props) => <Curd ref={props.FormCompRef} formQuery={formQuery} />}
				/>
				{/* 编辑/新建 弹窗 结束 */}

				{/* 重置密码 弹窗 开始 */}
				<ModalResetPwd resetId={resetId} onCallback={() => setResetId('')} />
				{/* 重置密码 弹窗 结束 */}
			</div>
		</div>
	);
};

const ModalResetPwd = (props = {}) => {
	const [form] = Form.useForm();

	// 关闭
	const close = () => {
		form.resetFields();
		props.onCallback();
	};

	// 提交
	const submit = () => {
		form.validateFields().then((values) => {
			updatePassword({
				userId: props.resetId,
				password: values.password,
			}).then(() => {
				message.success('重置密码成功');
				props.onCallback();
			});
		});
	};

	return (
		<Modal centered open={props.resetId !== ''} title="重置密码" onCancel={close} onOk={submit}>
			<div className="margin-top-24">
				<Form form={form}>
					<Form.Item label="新密码" name="password" rules={[{ required: true, message: '请输入新密码' }]}>
						<Input placeholder="请输入新密码" />
					</Form.Item>
				</Form>
			</div>
		</Modal>
	);
};

export default Index;
