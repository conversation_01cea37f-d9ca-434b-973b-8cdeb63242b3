import {useNavigate} from 'react-router-dom';
import {useEffect, useRef, useState, useCallback} from 'react';
import {Tabs, Input, Modal, message, Button} from 'antd';
import {useDispatch} from 'react-redux';
import {setToken} from '@/store/userSlice';
import {SwapOutlined} from '@ant-design/icons';
import {setAccessToken} from '@/utils/common';

import {login, adminSelectTenant, getLoginState, oauthCallback, oauthBind} from '@/api/login';

import './index.scss';
import {getImageSrc} from '@/assets/images';
import * as ww from '@wecom/jssdk'
import {useRouterLink} from "@/hook/useRouter";

const Index = () => {
	const {linkTo, searchParams} = useRouterLink();
	const dispatch = useDispatch();
	const navigate = useNavigate();
	// 企微登录区域初始化
	const wxDomRef = useRef(null);
	const initializedRef = useRef(false);
	// 判断是否为ERP或test域名
	const isERP = /erp|test/.test(window.location.href);
	// http://localhost/
	const isLocal = window.location.hostname.includes('localhost');
	// 绑定账号登录
	const [bindAccount, setBindAccount] = useState(null);
	const [form, setForm] = useState({
		loginType: 'LOGINTYPE_U,LOGINTYPE_M',
		username: '',
		password: '',
		verifyCode: '',
	});
	const [items, setItems] = useState([{
		key: 'LOGINTYPE_U,LOGINTYPE_M',
		label: `账号登录`,
		children: null,
	}]);
	const [open, setOpen] = useState(false);
	const [tenantList, setTenantList] = useState([]);

	const initWxLogin = useCallback(async () => {
		if (initializedRef.current) return;
		initializedRef.current = true;
		const res = await getLoginState();
		// console.log('getLoginState', res)
		if (res.data) {
			const state = res.data;
			// 初始化登录组件
			ww.createWWLoginPanel({
				el: '#ww_login',
				params: {
					login_type: 'CorpApp',
					appid: import.meta.env.VITE_WX_APPID,
					agentid: import.meta.env.VITE_WX_AGENTID,
					redirect_uri: window.location.href,
					state: state,
					redirect_type: 'callback',
					panel_size: 'small'
				},
				onCheckWeComLogin({isWeComLogin}) {
					console.log(isWeComLogin, 'isWeComLogin')
				},
				onLoginSuccess({code}) {
					console.log(code, 'onLoginSuccess')
					oauthCallback({code, state}).then(oauth => {
						console.log('oauthCallback', oauth)
						wxLoginSuccess(oauth.data);
					})
				},
				onLoginFail(err) {
					console.log(err)
				},
			});
		}
	}, []);

	useEffect(() => {
		if (wxDomRef.current && (isERP || isLocal)) {
			initWxLogin();
		}
	}, [wxDomRef.current, isERP, initWxLogin]);

	useEffect(() => {
		if ((isERP || isLocal) && items.length < 2) {
			const tabs = [...items];
			tabs.push({
				key: 'WX_LOGIN',
				label: `企业微信登录`,
				children: null,
			});
			console.log(tabs)
			setItems(tabs);
		}
		const code = searchParams.get('code');
		const state = searchParams.get('state');
		if (code && state) {
			setForm({
				loginType: 'WX_LOGIN',
				username: '',
				password: '',
				verifyCode: '',
			})
			// 重定向获取code/state
			oauthCallback({code, state}).then(oauth => {
				console.log('oauthCallback', oauth);
				wxLoginSuccess(oauth.data);
			})
		}
	}, []);

	// 企业微信登录后处理
	const wxLoginSuccess = useCallback(async ({sessionToken, userid}) => {
		// 判断是否返回 sessionToken
		if (sessionToken) {
			setAccessToken(sessionToken);
			dispatch(setToken(sessionToken));
			navigate('/');
		} else {
			// 未绑定用户前往绑定
			setBindAccount(userid);
		}

	}, [wxDomRef.current]);

	// 输入
	const inputChange = (e, key) => {
		const obj = {
			...form,
		};
		obj[key] = e.target.value || '';
		setForm(obj);
	};
	const submit = () => {
		// * @param {string} params.phone 手机号
		// * @param {string} params.pwd 密码
		// * @param {string} params.verificationCode 短信验证码

		if (form.loginType == 'LOGINTYPE_U,LOGINTYPE_M') {
			if (!form.username.trim()) {
				message.warning({
					content: '请先输入正确手机号/账号',
				});
				return;
			}
			if (!form.password.trim()) {
				message.warning({
					content: '请输入正确密码',
				});
				return;
			}
			login(form).then((res) => {
				if (res.data.length === 1) {
					console.log('默认第一个租户');
					adminSelectTenantLogin(res.data[0].id);
				} else {
					setTenantList(res.data);
					setOpen(true);
					console.log('选择租户');
				}
			});
		}
	};
	// 绑定提交
	const bindSubmit = (create = false) => {
		if (!form.username.trim()) {
			return message.warning({
				content: '请先输入正确手机号/账号',
			});
		} else if (!form.password.trim()) {
			return message.warning({
				content: '请输入正确密码',
			});
		}
		oauthBind({
			userid: bindAccount,
			status: create ? 0 : 1, // 0: 创建 1: 绑定
			tenantId: '1',
			...form,
			loginType: 'LOGINTYPE_U,LOGINTYPE_M',
		}).then(res => {
			/* 登录成功 */
			setAccessToken(res.data.sessionToken);
			dispatch(setToken(res.data.sessionToken));
			navigate('/');
			message.success('绑定成功');
		});
	}

	// 登录tabs
	// 切换tab
	const changeTabs = (key) => {
		const obj = {
			loginType: key,
			username: '',
			password: '',
			verifyCode: '',
		};
		setForm(obj);
	};
	//  选择租户后登录
	const adminSelectTenantLogin = (tenantId) => {
		adminSelectTenant({
			...form,
			tenantId: tenantId,
		}).then((res) => {
			setAccessToken(res.data.sessionToken);
			dispatch(setToken(res.data.sessionToken));
			navigate('/');
		});
	};

	return (
		<div className='Login width1-100vw height-100vh position-relative'>
			<div className='login-bg login-bg-1'></div>
			<div className='login-bg login-bg-2'></div>
			<div className='login-bg login-bg-3'></div>
			<div className='login-bg login-bg-4'></div>
			<div
				className={`position-absolute inset-0 margin-auto width-580 ${form.loginType === 'WX_LOGIN' ? 'height-620' : 'height-532'}  padding-lr-100 padding-top-80 border-radius-8 border-box bg-color-ffffff`}>
				<div className='font-size-24 line-height-32 font-weight-500 color-1d2129 margin-bottom-40'>
					欢迎来到{import.meta.env.VITE_TITLE}
				</div>

				{/* 登录tabs 开始 */}
				<Tabs
					activeKey={form.loginType}
					items={items}
					onChange={changeTabs}
					destroyOnHidden={false}
				/>
				{/* 登录tabs 结束 */}

				{/* 账号密码登录 开始 */}
				{
					form.loginType === 'LOGINTYPE_U,LOGINTYPE_M' && (
						<>
							<div className='padding-top-20 padding-bottom-40'>
								<div className='margin-bottom-24'>
									<Input
										placeholder='请输入管理后台账号'
										className='height-40'
										value={form.phone}
										onChange={(e) => inputChange(e, 'username')}
									/>
								</div>
								<div>
									<Input
										placeholder='密码'
										className='height-40'
										type='password'
										value={form.password}
										onChange={(e) => inputChange(e, 'password')}
										onPressEnter={submit}
									/>
								</div>
								<div className="margin-top-12 flex align-center justify-end">
								    <div
								        className="a color-165dff"
								        onClick={() => {
								            linkTo('/findPassword');
								        }}
								    >
								        忘记密码？
								    </div>
								</div>
							</div>
							{/* 登录 按钮 开始 */}
							<Button size={"large"} type={"primary"} block
									htmlType={"submit"} className='login-btn' onClick={submit}>
								登录
							</Button>
							{/* 登录 按钮 开始 */}
						</>
					)
				}
				{/* 账号密码登录 结束 */}

				{/* 企微二维码登录 */}
				<div
					className={`flex align-start justify-center ${form.loginType === 'WX_LOGIN' ? '' : 'display-none'}`}>
					{
						bindAccount && (
							<div className={'flex-sub flex flex-direction-column align-center justify-center'}>
								<div
									className={'font-size-16 line-height-24 font-weight-500 color-333333 margin-20 flex align-center gap-12'}>
									<span>首次使用请先绑定账号</span>
									<SwapOutlined/>
									<span className={'color-1d2129'}>{bindAccount}</span>
								</div>
								<div className='padding-top-20 padding-bottom-40 width-100per'>
									<div className='margin-bottom-24'>
										<Input
											placeholder='请输入管理后台账号'
											className='height-40'
											value={form.phone}
											onChange={(e) => inputChange(e, 'username')}
										/>
									</div>
									<div>
										<Input
											placeholder='密码'
											className='height-40'
											type='password'
											value={form.password}
											onChange={(e) => inputChange(e, 'password')}
											onPressEnter={() => bindSubmit(false)}
										/>
									</div>
								</div>
								<div className={"flex justify-between gap-20 width-100per"}>
									{/* 登录 按钮 开始 */}
									<Button size={"large"} type={"primary"} block
											htmlType={"submit"} className='login-btn' onClick={() => bindSubmit(false)}>
										绑定用户
									</Button>
									{/* 创建新用户 */}
									{/*<Button size={"large"} type={"default"} block*/}
									{/*        htmlType={"submit"} className='login-btn' onClick={() => bindSubmit(true)}>*/}
									{/*    创建新用户*/}
									{/*</Button>*/}
								</div>
								{/* 没账号创建提示 */}
								<div className={"width-100per flex justify-end"}>
									<div className={'font-size-12 line-height-16 color-999999 margin-top-20'}>
										没有账号？
										<Button type={"link"} size={"small"}
												className={'font-size-12'} onClick={() => bindSubmit(true)}>
											创建新用户{bindAccount}
										</Button>
									</div>
								</div>
								{/* 登录 按钮 开始 */}
							</div>
						)
					}
					<div id={"ww_login"} className={`wx-login-iframe ${bindAccount ? 'display-none' : ''}`}
						 ref={wxDomRef}>
					</div>
				</div>
			</div>
			{/* 租户选择 弹窗 开始 */}
			<Modal
				title='请选择租户'
				open={open}
				footer={false}
				width={400}
				centered
				onCancel={() => {
					setOpen(false);
				}}
			>
				{tenantList.map((tenant) => {
					return (
						<div
							className='flex align-center margin-top-20 padding-12 border-solid-e5e6eb border-radius-4 hover hover-border-165dff cursor-pointer'
							key={tenant.id}
							type='primary'
							onClick={() => {
								adminSelectTenantLogin(tenant.id);
							}}
						>
							<img
								className='width-48 height-48 margin-right-10'
								src={getImageSrc(
									'@/assets/images/Login/tenant-icon.png'
								)}
							/>
							<div className='flex-sub line-height-24 font-size-16 color-333333  hover-color-165dff'>
								{tenant.name}
							</div>
						</div>
					);
				})}
			</Modal>
			{/* 租户选择 弹窗 结束 */}
		</div>
	);
};
export default Index;
