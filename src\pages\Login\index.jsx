import { useNavigate } from 'react-router-dom';
import { useState } from 'react';
import { Tabs, Input, Modal, message, Button } from 'antd';
import { useDispatch } from 'react-redux';
import { setToken } from '@/store/userSlice';

import { setAccessToken } from '@/utils/common';

import { login, adminSelectTenant } from '@/api/login';

import './index.scss';
import { getImageSrc } from '@/assets/images';

const Index = () => {
	const dispatch = useDispatch();
	const navigate = useNavigate();

	const [form, setForm] = useState({
		loginType: 'LOGINTYPE_U,LOGINTYPE_M',
		username: '',
		password: '',
		verifyCode: '',
	});

	const [open, setOpen] = useState(false);
	const [tenantList, setTenantList] = useState([]);

	// 输入
	const inputChange = (e, key) => {
		const obj = {
			...form,
		};
		obj[key] = e.target.value || '';
		setForm(obj);
	};

	// 登录tabs
	const items = [
		{
			key: 'LOGINTYPE_U,LOGINTYPE_M',
			label: `账号登录`,
			children: null,
		},
	];

	const onChangeTabs = (key) => {
		const obj = {
			loginType: key,
			username: '',
			password: '',
			verifyCode: '',
		};
		setForm(obj);
	};

	const submit = () => {
		// * @param {string} params.phone 手机号
		// * @param {string} params.pwd 密码
		// * @param {string} params.verificationCode 短信验证码

		if (form.loginType == 'LOGINTYPE_U,LOGINTYPE_M') {
			if (!form.username.trim()) {
				message.warning({
					content: '请先输入正确手机号/账号',
				});
				return;
			}
			if (!form.password.trim()) {
				message.warning({
					content: '请输入正确密码',
				});
				return;
			}
			login(form).then((res) => {
				if (res.data.length === 1) {
					console.log('默认第一个租户');
					adminSelectTenantLogin(res.data[0].id);
				} else {
					setTenantList(res.data);
					setOpen(true);
					console.log('选择租户');
				}
			});
		}
	};

	//  选择租户后登录
	const adminSelectTenantLogin = (tenantId) => {
		adminSelectTenant({
			...form,
			tenantId: tenantId,
		}).then((res) => {
			setAccessToken(res.data.sessionToken);
			dispatch(setToken(res.data.sessionToken));
			navigate('/');
		});
	};

	return (
		<div className='Login width1-100vw height-100vh position-relative'>
			<div className='login-bg login-bg-1'></div>
			<div className='login-bg login-bg-2'></div>
			<div className='login-bg login-bg-3'></div>
			<div className='login-bg login-bg-4'></div>
			<div className='position-absolute inset-0 margin-auto width-580 height-532 padding-lr-100 padding-top-80 border-radius-8 border-box bg-color-ffffff'>
				<div className='font-size-24 line-height-32 font-weight-500 color-1d2129 margin-bottom-40'>
					欢迎来到{import.meta.env.VITE_TITLE}
				</div>

				{/* 登录tabs 开始 */}
				<Tabs
					defaultActiveKey={form.loginType}
					items={items}
					onChange={onChangeTabs}
				/>
				{/* 登录tabs 结束 */}

				{/* 账号密码登录 开始 */}
				{form.loginType == 'LOGINTYPE_U,LOGINTYPE_M' ? (
					<div className='padding-top-20 padding-bottom-40'>
						<div className='margin-bottom-12'>
							<Input
								placeholder='手机号/账号'
								className='height-40'
								value={form.phone}
								onChange={(e) => inputChange(e, 'username')}
							/>
						</div>
						<div>
							<Input
								placeholder='密码'
								className='height-40'
								type='password'
								value={form.password}
								onChange={(e) => inputChange(e, 'password')}
								onPressEnter={submit}
							/>
						</div>
					</div>
				) : null}
				{/* 账号密码登录 结束 */}

				{/* 登录 按钮 开始 */}
				<div className='login-btn' onClick={submit}>
					登录
				</div>
				{/* 登录 按钮 开始 */}
			</div>
			{/* 租户选择 弹窗 开始 */}
			<Modal
				title='请选择租户'
				open={open}
				footer={false}
				width={400}
				centered
				onCancel={() => {
					setOpen(false);
				}}
			>
				{tenantList.map((tenant) => {
					return (
						<div
							className='flex align-center margin-top-20 padding-12 border-solid-e5e6eb border-radius-4 hover hover-border-165dff cursor-pointer'
							key={tenant.id}
							type='primary'
							onClick={() => {
								adminSelectTenantLogin(tenant.id);
							}}
						>
							<img
								className='width-48 height-48 margin-right-10'
								src={getImageSrc(
									'@/assets/images/Login/tenant-icon.png'
								)}
							/>
							<div className='flex-sub line-height-24 font-size-16 color-333333  hover-color-165dff'>
								{tenant.name}
							</div>
						</div>
					);
				})}
			</Modal>
			{/* 租户选择 弹窗 结束 */}
		</div>
	);
};
export default Index;
