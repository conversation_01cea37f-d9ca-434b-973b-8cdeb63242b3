import { useEffect, useState, useRef, forwardRef, useImperativeHandle, memo } from 'react';

import { Form, Input, Radio, Cascader, message, Button, Space, Select } from 'antd';
import Breadcrumb from '@/components/Breadcrumb';
import { getDictData, getDeptData } from '@/utils/dictionary';
import { useRouterLink } from '@/hook/useRouter';

import { deptDetail, deptAdd, deptUpdate, getUserPageData } from '@/api/System/index';

import './index.scss';

const Index = forwardRef((props, ref) => {
	const { linkTo, searchParams } = useRouterLink();
	const isFull = useRef();
	const [id, setId] = useState('');
	const [form] = Form.useForm();
	const [deptList, setDeptList] = useState([]);

	// 部门人员列表
	const [commanderOptions, setCommanderOptions] = useState([]);

	// 获取详情
	const getDetail = (deptId) => {
		deptDetail({ deptId })
			.then((res) => {
				const resData = res.data || {};
				form.setFieldsValue({
					...resData,
					parentId: getParentId(resData.parentId),
				});

				// 设置禁止选择当前及子集部门防止嵌套部门出现
				setDisabled(deptId);
			})
			.catch((err) => {
				console.log('🚀 ~ deptDetail ~ err:', err);
				if (isFull.current) {
					linkTo(-1);
				}
			});
	};

	// 获取部门父级id
	const getParentId = (id) => {
		function _getFlatData(data = []) {
			let flatList = [];

			data.forEach((item) => {
				flatList.push({
					value: item.value,
					label: item.label,
					parentId: item.parentId,
				});

				if (item.children && item.children.length) {
					flatList = flatList.concat(_getFlatData(item.children));
				}
			});

			return flatList;
		}

		function _findParentId(id) {
			const findData = deptFlatList.find(({ value }) => value === id);

			if (id === '' || findData === undefined) {
				return '';
			}

			let result = findData.value;
			if (findData.parentId) {
				result = _findParentId(findData.parentId) + '|' + result;
			}
			return result;
		}
		// 打平部门
		const deptFlatList = _getFlatData(deptList);
		const result = _findParentId(id);
		return result ? result.split('|') : [];
	};

	// 设置禁选部门
	const setDisabled = (id) => {
		const findData = getParentId(id).reduce(
			(list, item) => {
				return list.children.find(({ value }) => value === item);
			},
			{ children: deptList }
		);

		function _setDis(data) {
			data.disabled = true;

			if (data.children && data.children.length) {
				data.children.forEach((item) => _setDis(item));
			}
		}

		_setDis(findData);
	};

	// 提交
	const handleSubmit = () => {
		return new Promise((resolve) => {
			form.validateFields().then((res) => {
				const params = {
					id,
					...res,
					parentId: res.parentId && res.parentId.length ? res.parentId[res.parentId.length - 1] : '',
				};
				(id ? deptUpdate : deptAdd)(params).then(() => {
					message.success(id ? '编辑成功' : '新增成功');
					form.resetFields();
					setId('');
					if (isFull.current) {
						setTimeout(() => {
							linkTo(-1);
						}, 1500);
					}

					resolve(params);
				});
			});
		});
	};

	// 取消
	const handleCancel = () => {
		return new Promise((resolve) => {
			form.resetFields();
			setId('');
			if (isFull.current) {
				linkTo(-1);
			}
			resolve();
		});
	};

	const isLoadDeptData = useRef(false);
	const loadDeptData = () => {
		getDeptData().then((data) => {
			setDeptList(data);
			isLoadDeptData.current = true;
		});
	};

	useEffect(() => {
		if (!id) {
			return;
		}
		getUserPageData({
			deptId: id,
			pageSize: 1000,
			pageNum: 1,
		}).then((res) => {
			setCommanderOptions(
				(res?.data?.records || []).map((ov) => {
					return {
						value: ov.id,
						label: ov.userName,
					};
				})
			);
		});
	}, [id]);

	useEffect(() => {
		loadDeptData();
	}, []);

	// 弹窗形式
	useEffect(() => {
		if (!isLoadDeptData.current) {
			return;
		}
		if (props.formQuery && props.formQuery.id && id !== props.formQuery.id) {
			setId(props.formQuery.id);
			getDetail(props.formQuery.id);
		}
	}, [JSON.stringify(props.formQuery), isLoadDeptData.current]);

	// 新窗口形式
	useEffect(() => {
		if (!isLoadDeptData.current) {
			return;
		}
		isFull.current = !props.formQuery;
		if (searchParams.get('id')) {
			setId(searchParams.get('id'));
			getDetail(searchParams.get('id'));
		}
	}, [isLoadDeptData.current]);

	useImperativeHandle(ref, () => {
		return {
			onCancel: handleCancel,
			onOk: handleSubmit,
		};
	});
	return (
		<div className={`${isFull.current ? 'flex-sub flex flex-direction-column margin-top-16' : ''}`}>
			{isFull.current && (
				<Breadcrumb
					icon="icon-system"
					list={[
						{
							name: '系统管理',
							link: '/system',
						},
						{
							name: '部门管理',
							link: '/system/dept',
						},
					]}
					name={id ? '编辑部门' : '新增部门'}
				/>
			)}
			<div
				className={`${
					isFull.current ? 'flex-sub margin-lr-20 margin-bottom-20 margin-top-4 padding-20 bg-color-ffffff border-radius-4' : ''
				}`}
			>
				<Form
					form={form}
					labelCol={{
						style: { width: '80px' },
					}}
					labelAlign="right"
					initialValues={{
						parentId: [],
						commanders: [],
						status: 1,
					}}
				>
					<Form.Item
						label="部门名称"
						name="name"
						rules={[
							{
								required: true,
								message: '请输入部门名称',
							},
						]}
					>
						<Input placeholder="请输入部门名称" />
					</Form.Item>
					<Form.Item label="上级部门" name="parentId">
						<Cascader options={deptList} placeholder="请选择上级部门，默认为一级部门" changeOnSelect />
					</Form.Item>
					{id && (
						<Form.Item label="部门负责人" name="commanders">
							<CommanderSelect options={commanderOptions} />
						</Form.Item>
					)}
					<Form.Item
						label="状态"
						name="status"
						rules={[
							{
								required: true,
								type: 'number',
								message: '请选择状态',
							},
						]}
					>
						<Radio.Group options={getDictData('deptStatus')} />
					</Form.Item>
					<Form.Item label="备注" name="description">
						<Input.TextArea rows={3} placeholder="请输入备注" />
					</Form.Item>
					{isFull.current && (
						<Form.Item label=" " colon={false}>
							<Space size={16}>
								<Button type="primary" onClick={handleSubmit}>
									提交
								</Button>
								<Button onClick={handleCancel}>取消</Button>
							</Space>
						</Form.Item>
					)}
				</Form>
			</div>
		</div>
	);
});

// 部门负责人
const CommanderSelect = (props = {}) => {
	const [value, setValue] = useState([]);

	console.log(props.value);
	useEffect(() => {
		setValue((props.value || []).map((ov) => ov.id));
	}, [props.value]);

	return (
		<Select
			value={value}
			options={props.options}
			mode="multiple"
			allowClear
			placeholder="请选择部门负责人"
			onChange={(_, e) => {
				props.onChange(
					(e || []).map((ov) => {
						return {
							id: ov.value,
							userName: ov.label,
						};
					})
				);
			}}
		/>
	);
};
export default memo(Index);
