import { useEffect, useState, useRef, forwardRef, useImperativeHandle, memo } from 'react';

import { Form, Input, Radio, message, Button, Space, Tree, Checkbox } from 'antd';
import Breadcrumb from '@/components/Breadcrumb';
import { getDictData } from '@/utils/dictionary';
import { useRouterLink } from '@/hook/useRouter';
import { getPermissiontreeData, getPermissionListByKeyName } from '@/utils/permission';

import { roleDetail, roleAdd, roleUpdate } from '@/api/System/index';

import './index.scss';

const PermissionTree = (props = {}) => {
	const [treeData, setTreeData] = useState([]);
	const [ids, setIds] = useState([]);

	const [checkedKeys, setCheckedKeys] = useState({
		checked: [],
		halfChecked: [],
	});

	const [expandedKeys, setExpandedKeys] = useState([]);
	const [defaultCheckChild, setDefaultCheckChild] = useState(true);

	const checkedTree = (checked, node) => {
		const { id, children } = node;
		const keys = defaultCheckChild ? [id, getChildIds(children)].flat(2) : [id];
		if (checked) {
			setCheckedKeys({
				checked: checkedKeys.checked.concat(keys),
				halfChecked: [],
			});
		} else {
			setCheckedKeys({
				checked: checkedKeys.checked.filter((item) => !keys.includes(item)),
				halfChecked: [],
			});
		}
	};

	const getChildIds = (data) => {
		if (!(data && data.length)) {
			return [];
		}
		return data.map(({ id, children }) => {
			return [id, ...getChildIds(children)].flat(2);
		});
	};

	useEffect(() => {
		if (JSON.stringify(props.value) !== JSON.stringify(checkedKeys.checked)) {
			props.onChange && props.onChange(checkedKeys.checked);
		}
	}, [checkedKeys.checked]);

	useEffect(() => {
		setCheckedKeys({
			checked: props.value || [],
			halfChecked: [],
		});
	}, [props.value]);

	useEffect(() => {
		setTreeData(getPermissiontreeData());
		setIds(getPermissionListByKeyName());
	}, []);
	return (
		<Space direction="vertical" size={8}>
			<Space size={8} className="line-height-32">
				<Checkbox onChange={(e) => setCheckedKeys(e.target.checked ? ids : [])}>全选/全不选</Checkbox>
				<Checkbox onChange={(e) => setExpandedKeys(e.target.checked ? ids : [])}>展开/折叠</Checkbox>
				<Checkbox checked={defaultCheckChild} onChange={(e) => setDefaultCheckChild(e.target.checked)}>
					父子节点关联
				</Checkbox>
			</Space>
			<Tree
				checkable
				checkStrictly
				checkedKeys={checkedKeys}
				expandedKeys={expandedKeys}
				treeData={treeData}
				fieldNames={{
					title: 'menuName',
					key: 'id',
					children: 'children',
				}}
				onCheck={(_e, { checked, node }) => {
					checkedTree(checked, node);
				}}
				onExpand={(e) => {
					setExpandedKeys(e);
				}}
			/>
		</Space>
	);
};

const Index = forwardRef((props, ref) => {
	const { linkTo, searchParams } = useRouterLink();
	const isFull = useRef();
	const [id, setId] = useState('');
	const [form] = Form.useForm();

	// 获取详情
	const getDetail = (roleId) => {
		roleDetail({ roleId })
			.then((res) => {
				const { roleCode, roleName, status, description, permissions } = res.data;
				form.setFieldsValue({
					roleCode,
					roleName,
					status,
					description,
					permissionIds: (permissions || []).map(({ id }) => id),
				});
			})
			.catch((err) => {
				console.log('🚀 ~ roleDetail ~ err:', err);
				if (isFull.current) {
					linkTo(-1);
				}
			});
	};

	// 提交
	const handleSubmit = () => {
		return new Promise((resolve) => {
			form.validateFields().then((res) => {
				const params = {
					id,
					...res,
				};
				(id ? roleUpdate : roleAdd)(params).then(() => {
					message.success(id ? '编辑成功' : '新增成功');
					form.resetFields();
					setId('');
					if (isFull.current) {
						setTimeout(() => {
							linkTo(-1);
						}, 1500);
					}

					resolve(params);
				});
			});
		});
	};

	// 取消
	const handleCancel = () => {
		return new Promise((resolve) => {
			form.resetFields();
			setId('');
			if (isFull.current) {
				linkTo(-1);
			}
			resolve();
		});
	};

	useEffect(() => {}, []);

	// 弹窗形式
	useEffect(() => {
		if (props.formQuery && props.formQuery.id && id !== props.formQuery.id) {
			setId(props.formQuery.id);
			getDetail(props.formQuery.id);
		}
	}, [JSON.stringify(props.formQuery)]);

	// 新窗口形式
	useEffect(() => {
		isFull.current = !props.formQuery;
		if (searchParams.get('id')) {
			setId(searchParams.get('id'));
			getDetail(searchParams.get('id'));
		}
	}, []);

	useImperativeHandle(ref, () => {
		return {
			onCancel: handleCancel,
			onOk: handleSubmit,
		};
	});

	return (
		<div className={`${isFull.current ? 'flex-sub flex flex-direction-column margin-top-16' : ''}`}>
			{isFull.current && (
				<Breadcrumb
					icon="icon-system"
					list={[
						{
							name: '系统管理',
							link: '/system',
						},
						{
							name: '角色管理',
							link: '/system/role',
						},
					]}
					name={id ? '编辑角色' : '新增角色'}
				/>
			)}
			<div
				className={`${
					isFull.current ? 'flex-sub margin-lr-20 margin-bottom-20 margin-top-4 padding-20 bg-color-ffffff border-radius-4' : ''
				}`}
			>
				<Form
					form={form}
					labelCol={{
						style: { width: '80px' },
					}}
					labelAlign="right"
					initialValues={{
						permissions: [],
						status: 1,
					}}
				>
					<Form.Item
						label="角色编码"
						name="roleCode"
						rules={[
							{
								required: true,
								message: '请输入角色编码',
							},
						]}
					>
						<Input placeholder="请输入角色编码" disabled={id !== ''} />
					</Form.Item>
					<Form.Item
						label="角色名称"
						name="roleName"
						rules={[
							{
								required: true,
								message: '请输入角色名称',
							},
						]}
					>
						<Input placeholder="请输入角色名称" />
					</Form.Item>
					<Form.Item
						label="状态"
						name="status"
						rules={[
							{
								required: true,
								type: 'number',
								message: '请选择状态',
							},
						]}
					>
						<Radio.Group options={getDictData('roleStatus')} />
					</Form.Item>
					<Form.Item
						label="权限信息"
						name="permissionIds"
						rules={[
							{
								required: true,
								type: 'array',
								message: '请选择权限信息',
							},
						]}
					>
						<PermissionTree />
					</Form.Item>
					<Form.Item label="备注" name="description">
						<Input.TextArea rows={3} placeholder="请输入备注" />
					</Form.Item>
					{isFull.current && (
						<Form.Item label=" " colon={false}>
							<Space size={16}>
								<Button type="primary" onClick={handleSubmit}>
									提交
								</Button>
								<Button onClick={handleCancel}>取消</Button>
							</Space>
						</Form.Item>
					)}
				</Form>
			</div>
		</div>
	);
});

export default memo(Index);
