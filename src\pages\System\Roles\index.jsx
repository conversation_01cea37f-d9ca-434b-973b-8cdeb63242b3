import { useEffect, useRef, useState } from 'react';
import { Row, Col, Space, Form, Input, Table, Popconfirm, message } from 'antd';
import { SearchOutlined, ReloadOutlined, PlusOutlined } from '@ant-design/icons';

import Breadcrumb from '@/components/Breadcrumb';
import ModalForm from '@/components/ModalForm';

import Curd from '@/pages/System/Roles/Curd/index';

import { getDictLabelByValue } from '@/utils/dictionary';

import { getRolePageData, roleDel } from '@/api/System';

import '@/pages/System/index.scss';
const Index = () => {
	const ModalFormRef = useRef();
	const [form] = Form.useForm();
	const [dataSource, setDataSource] = useState([]);
	const [pagination, setPagination] = useState({
		current: 1,
		pageSize: 10,
		total: 0,
		showTotal: (total) => `共 ${total} 条`,
	});

	// 搜索
	const searchData = (current = 1, pageSize = pagination.pageSize) => {
		pagination.current = current;
		pagination.pageSize = pageSize;
		setPagination({ ...pagination });
		getTableData();
	};

	// 获取表格数据
	const getTableData = () => {
		const { roleName } = form.getFieldValue();
		getRolePageData({
			pageNum: pagination.current,
			pageSize: pagination.pageSize,
			roleName,
		}).then((res) => {
			const { total, records } = res.data;
			pagination.total = total - 0;
			setDataSource(records);
			setPagination({ ...pagination });
		});
	};

	// 删除
	const handleDel = (roleId) => {
		roleDel({ roleId }).then(() => {
			getTableData();
			message.success('操作成功');
		});
	};

	// 新建/编辑 打开表单
	const [formQuery, setFormQuery] = useState({ id: '' });
	const handleOpenForm = (id = '') => {
		ModalFormRef.current.setOpen(true);
		ModalFormRef.current.setTitle(id ? '编辑角色' : '新建角色');
		setFormQuery({ id });
	};

	useEffect(() => {
		getTableData();
	}, []);

	return (
		<div className="flex-sub flex flex-direction-column margin-top-16">
			{/* 面包屑 开始 */}
			<Breadcrumb
				icon="icon-system"
				list={[
					{
						name: '系统管理',
						link: '/system',
					},
				]}
				name="角色管理"
			/>
			{/* 面包屑 结束 */}
			<div className="flex-sub margin-lr-20 margin-bottom-20 margin-top-4 padding-20 bg-color-ffffff border-radius-4">
				{/* 筛选条件 开始 */}
				<div className="flex margin-bottom-16 padding-bottom-16 border-bottom-e5e6eb">
					<div className="flex-sub padding-right-16 border-right-e5e6eb">
						<Form
							className="form-filter"
							form={form}
							labelCol={{
								style: { width: '68px' },
							}}
							labelAlign="left"
						>
							<Row gutter={[16, 16]}>
								<Col xs={24} sm={24} md={12} lg={8}>
									<Form.Item label="角色名称" name="roleName">
										<Input placeholder="请输入角色名称" />
									</Form.Item>
								</Col>
							</Row>
						</Form>
					</div>
					<Space direction="vertical" size={20} className="padding-left-16">
						<Space
							size={10}
							className="padding-lr-14 height-32 line-height-32 font-size-14 border-radius-2 bg-color-165dff color-ffffff cursor-pointer"
							onClick={() => {
								searchData();
							}}
						>
							<SearchOutlined />
							<span>查询</span>
						</Space>
						<Space
							size={10}
							className="padding-lr-14 height-32 line-height-32 font-size-14 border-radius-2 bg-color-f2f3f5 color-4e5969 cursor-pointer"
							onClick={() => {
								form.resetFields();
								pagination.current = 1;
								setPagination(pagination);
								getTableData();
							}}
						>
							<ReloadOutlined />
							<span>重置</span>
						</Space>
					</Space>
				</div>
				{/* 筛选条件 结束 */}
				{/* 按钮操作 开始 */}
				<Space size={16} className="margin-bottom-16">
					<Space
						size={10}
						className="padding-lr-16 height-32 line-height-32 font-size-14 border-radius-2 bg-color-165dff color-ffffff cursor-pointer"
						onClick={() => {
							handleOpenForm();
						}}
					>
						<PlusOutlined />
						<span>新建</span>
					</Space>
				</Space>
				{/* 按钮操作 结束 */}
				{/* 表格列表 开始 */}
				<Table
					rowKey="id"
					dataSource={dataSource}
					pagination={{
						...pagination,
						showQuickJumper: true,
						showSizeChanger: true,
						onChange: searchData,
					}}
					scroll={{ x: 'max-content' }}
				>
					<Table.Column title="角色编码" dataIndex="roleCode" key="roleCode" />
					<Table.Column title="角色名称" dataIndex="roleName" key="roleName" />
					<Table.Column
						title="状态"
						dataIndex="status"
						key="status"
						align="center"
						render={(status) => (
							<Space size={8}>
								<div className={`width-6 height-6 border-radius-6 ${status === 1 ? 'bg-color-00b42a' : 'bg-color-c9cdd4'}`}></div>
								<div>{getDictLabelByValue('userStatus', status)}</div>
							</Space>
						)}
					/>
					<Table.Column title="创建时间" dataIndex="createTime" key="createTime" />
					<Table.Column
						title="操作"
						dataIndex="id"
						key="id"
						align="center"
						render={(id) => (
							<Space size={16} className="padding-lr-16">
								<div
									className="color-165dff cursor-pointer"
									onClick={() => {
										handleOpenForm(id);
									}}
								>
									编辑
								</div>
								<Popconfirm
									icon={false}
									description="是否确认删除此数据?"
									onConfirm={() => {
										handleDel(id);
									}}
									okText="确认"
									cancelText="取消"
								>
									<div className="color-f53f3f cursor-pointer">删除</div>
								</Popconfirm>
							</Space>
						)}
					/>
				</Table>
				{/* 表格列表 结束 */}
				{/* 编辑/新建 弹窗 开始 */}
				<ModalForm
					ref={ModalFormRef}
					modelConfig={{
						title: '新建活动',
					}}
					onOk={(res) => {
						console.log('🚀 ~ Index ~ res:', res);
						searchData(pagination.current);
					}}
					FormComp={(props) => <Curd ref={props.FormCompRef} formQuery={formQuery} />}
				/>
				{/* 编辑/新建 弹窗 结束 */}
			</div>
		</div>
	);
};

export default Index;
