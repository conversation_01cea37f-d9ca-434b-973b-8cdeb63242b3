import { useEffect, useState, useRef, forwardRef, useImperativeHandle, memo } from 'react';

import { Form, Input, Radio, Select, Cascader, message, Button, Space } from 'antd';
import Breadcrumb from '@/components/Breadcrumb';
import UploadImg from '@/components/UploadImg';

import { getDictData, getDeptData } from '@/utils/dictionary';
import { useRouterLink } from '@/hook/useRouter';

import { getRoleList, userDetail, userAdd, userUpdate } from '@/api/System/index';

import { getDeptParentId, getDeptFlatData } from '@/utils/common';

import './index.scss';

const Index = forwardRef((props, ref) => {
	const { linkTo, searchParams } = useRouterLink();
	const isFull = useRef();
	const [id, setId] = useState('');
	const [form] = Form.useForm();
	const [deptList, setDeptList] = useState([]);
	const [roleList, setRoleList] = useState([]);
	const [detail, setDetail] = useState({});

	const userType = Form.useWatch('userType', form);

	// 获取详情
	const getDetail = (userId) => {
		userDetail({ userId })
			.then((res) => {
				const {
					userType,
					loginName,
					userName,
					gender,
					mobile,
					deptList,
					roleList,
					wxAvatarUrl,
					wxQrCodeUrl,
					email,
					positionName,
					status,
					insiderStatus = 0,
				} = res.data;
				form.setFieldsValue({
					userType,
					loginName,
					userName,
					gender,
					mobile,
					roleIds: (roleList || []).map(({ id }) => id),
					wxAvatarUrl,
					wxQrCodeUrl,
					email,
					positionName,
					status,
					insiderStatus,
				});
				setDetail(res.data);
			})
			.catch((err) => {
				console.log('🚀 ~ userDetail ~ err:', err);
				if (isFull.current) {
					linkTo(-1);
				}
			});
	};

	// 提交
	const handleSubmit = () => {
		return new Promise((resolve) => {
			form.validateFields().then((res) => {
				const params = {
					id,
					...res,
					deptIds: (res.deptIds && res.deptIds.map((item) => item[item.length - 1])) || undefined,
				};
				(id ? userUpdate : userAdd)(params).then(() => {
					message.success(id ? '编辑成功' : '新增成功');
					form.resetFields();
					setId('');
					if (isFull.current) {
						setTimeout(() => {
							linkTo(-1);
						}, 1500);
					}

					resolve(params);
				});
			});
		});
	};

	// 取消
	const handleCancel = () => {
		return new Promise((resolve) => {
			form.resetFields();
			setId('');
			if (isFull.current) {
				linkTo(-1);
			}
			resolve();
		});
	};

	const isLoadDeptData = useRef(false);
	const loadDeptData = () => {
		getDeptData().then((data) => {
			setDeptList(data);
			isLoadDeptData.current = true;
		});
	};

	useEffect(() => {
		loadDeptData();
		getRoleList({}, { isCache: true }).then(({ data }) => {
			setRoleList(
				data.map((item) => {
					return {
						value: item.id,
						label: item.roleName,
					};
				})
			);
		});
	}, []);

	// 弹窗形式
	useEffect(() => {
		if (!isLoadDeptData.current) {
			return;
		}
		if (props.formQuery && props.formQuery.id && id !== props.formQuery.id) {
			setId(props.formQuery.id);
			getDetail(props.formQuery.id);
		}
	}, [JSON.stringify(props.formQuery), isLoadDeptData.current]);

	// 新窗口形式
	useEffect(() => {
		if (!isLoadDeptData.current) {
			return;
		}
		isFull.current = !props.formQuery;
		if (searchParams.get('id')) {
			setId(searchParams.get('id'));
			getDetail(searchParams.get('id'));
		}
	}, [isLoadDeptData.current]);

	// 处理部门
	useEffect(() => {
		if (deptList.length === 0 || (detail.deptList || []).length === 0) {
			return;
		}
		const deptFlatList = getDeptFlatData(deptList);

		form.setFieldValue(
			'deptIds',
			(detail.deptList || [])
				.map(({ id }) => {
					return getDeptParentId(id, deptFlatList);
				})
				.filter((item) => item.length)
		);
	}, [deptList, detail]);

	useImperativeHandle(ref, () => {
		return {
			onCancel: handleCancel,
			onOk: handleSubmit,
		};
	});
	return (
		<div className={`${isFull.current ? 'flex-sub flex flex-direction-column margin-top-16' : ''}`}>
			{isFull.current && (
				<Breadcrumb
					icon="icon-system"
					list={[
						{
							name: '系统管理',
							link: '/system',
						},
						{
							name: '用户管理',
							link: '/system/user',
						},
					]}
					name={id ? '编辑用户' : '新增用户'}
				/>
			)}
			<div
				className={`${
					isFull.current ? 'flex-sub margin-lr-20 margin-bottom-20 margin-top-4 padding-20 bg-color-ffffff border-radius-4' : ''
				}`}
			>
				<Form
					form={form}
					labelCol={{
						style: { width: '120px' },
					}}
					labelAlign="right"
					initialValues={{
						userType: '2',
						gender: '',
						deptIds: [],
						roleIds: [],
						status: 1,
						insiderStatus: 0,
					}}
				>
					<Form.Item label="用户类型" name="userType" required>
						<Select options={getDictData('userType')} placeholder="请选择用户类型" />
					</Form.Item>
					<Form.Item
						label="登录账号"
						name="loginName"
						rules={[
							{
								required: true,
								message: '请输入登录账号',
							},
						]}
					>
						<Input placeholder="请输入登录账号(不支持编辑修改且唯一)" disabled={id !== ''} />
					</Form.Item>
					<Form.Item
						label="用户名称"
						name="userName"
						rules={[
							{
								required: true,
								message: '请输入用户名称',
							},
						]}
					>
						<Input placeholder="请输入用户名称" />
					</Form.Item>
					<Form.Item label="性别" name="gender" required>
						<Radio.Group options={getDictData('gender')} />
					</Form.Item>
					<Form.Item
						label="手机号码"
						name="mobile"
						rules={[
							{
								required: true,
								message: '请输入手机号码',
							},
						]}
					>
						<Input placeholder="请输入手机号码" />
					</Form.Item>
					{userType === '2' && (
						<>
							<Form.Item
								label="归属部门"
								name="deptIds"
								rules={[
									{
										required: true,
										type: 'array',
										message: '请选择归属部门',
									},
								]}
							>
								<Cascader options={deptList} placeholder="请选择归属部门" multiple maxTagCount="responsive" />
							</Form.Item>
							<Form.Item
								label="用户角色"
								name="roleIds"
								rules={[
									{
										required: true,
										type: 'array',
										message: '请选择用户角色',
									},
								]}
							>
								<Select options={roleList} placeholder="请选择用户角色" mode="multiple" />
							</Form.Item>
						</>
					)}

					<Form.Item
						label="用户头像"
						name="wxAvatarUrl"
						rules={
							[
								// {
								// 	required: true,
								// 	message: '请上传用户头像',
								// },
							]
						}
					>
						<UploadImg size={3} width={104} height={104} tips="建议尺寸：200px*200px" />
					</Form.Item>
					<Form.Item label="企微二维码" name="wxQrCodeUrl">
						<UploadImg size={3} width={104} height={104} tips="建议尺寸：200px*200px" />
					</Form.Item>
					<Form.Item label="邮箱" name="email">
						<Input placeholder="请输入邮箱" />
					</Form.Item>
					<Form.Item label="职位名称" name="positionName">
						<Input placeholder="请输入职位名称" />
					</Form.Item>
					<Form.Item
						label="是否内部员工"
						name="insiderStatus"
						rules={[
							{
								required: true,
								type: 'number',
								message: '请选择',
							},
						]}
					>
						<Radio.Group options={getDictData('ynStatus')} />
					</Form.Item>
					<Form.Item
						label="状态"
						name="status"
						rules={[
							{
								required: true,
								type: 'number',
								message: '请选择状态',
							},
						]}
					>
						<Radio.Group options={getDictData('userStatus')} />
					</Form.Item>
					{isFull.current && (
						<Form.Item label=" " colon={false}>
							<Space size={16}>
								<Button type="primary" onClick={handleSubmit}>
									提交
								</Button>
								<Button onClick={handleCancel}>取消</Button>
							</Space>
						</Form.Item>
					)}
				</Form>
			</div>
		</div>
	);
});

export default memo(Index);
