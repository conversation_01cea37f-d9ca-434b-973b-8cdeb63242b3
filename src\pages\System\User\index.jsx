import { useEffect, useRef, useState } from 'react';
import Breadcrumb from '@/components/Breadcrumb';
import { Row, Col, Space, Form, Input, Select, Table, Popconfirm, Cascader, message, Modal, Switch } from 'antd';
import { SearchOutlined, ReloadOutlined, PlusOutlined } from '@ant-design/icons';
import Curd from '@/pages/System/User/Curd/index';
import ModalForm from '@/components/ModalForm';
import Permission from '@/components/Permission';

import { getDictData, getDictLabelByValue, getDeptData } from '@/utils/dictionary';

import { getRoleList, getUserPageData, userDel, batchUpdateIsInternalStatus } from '@/api/System';
import { updatePassword } from '@/api/login';

const Index = () => {
	const ModalFormRef = useRef();
	const [form] = Form.useForm();
	const [deptList, setDeptList] = useState([]);
	const [roleList, setRoleList] = useState([]);
	const [dataSource, setDataSource] = useState([]);
	const [pagination, setPagination] = useState({
		current: 1,
		pageSize: 10,
		total: 0,
		showTotal: (total) => `共 ${total} 条`,
	});

	// 重置密码id resetId
	const [resetId, setResetId] = useState('');

	// 搜索
	const searchData = (current = 1, pageSize = pagination.pageSize) => {
		pagination.current = current;
		pagination.pageSize = pageSize;
		setPagination({ ...pagination });
		getTableData();
	};

	// 获取表格数据
	const getTableData = () => {
		const { keywords, mobile, deptId = [], roleId, status, userSource, userType, appid } = form.getFieldValue();
		const len = deptId.length;
		getUserPageData({
			pageNum: pagination.current,
			pageSize: pagination.pageSize,
			keywords,
			mobile,
			deptId: len > 0 ? deptId[len - 1] : undefined,
			roleId,
			status,
			userSource,
			userType,
			appids: appid ? [appid] : undefined,
		}).then((res) => {
			const { total, records } = res.data;
			pagination.total = total - 0;
			setDataSource(records);
			setPagination({ ...pagination });
		});
	};

	// 删除
	const handleDel = (userId) => {
		userDel({ userId }).then(() => {
			getTableData();
			message.success('操作成功');
		});
	};

	// 新建/编辑 打开表单
	const [formQuery, setFormQuery] = useState({ id: '' });
	const handleOpenForm = (id = '') => {
		ModalFormRef.current.setOpen(true);
		ModalFormRef.current.setTitle(id ? '编辑用户' : '新建用户');
		setFormQuery({ id });
	};

	useEffect(() => {
		getDeptData().then((data) => {
			setDeptList(data);
		});
		getRoleList({}, { isCache: true }).then(({ data }) => {
			setRoleList(
				data.map((item) => {
					return {
						value: item.id,
						label: item.roleName,
					};
				})
			);
		});
		getTableData();
	}, []);
	const insiderStatusChange = (data = {}) => {
		batchUpdateIsInternalStatus(data).then(() => {
			getTableData();
		});
	};

	return (
		<div className="flex-sub flex flex-direction-column margin-top-16">
			{/* 面包屑 开始 */}
			<Breadcrumb
				icon="icon-system"
				list={[
					{
						name: '系统管理',
						link: '/system',
					},
				]}
				name="用户管理"
			/>
			{/* 面包屑 结束 */}
			<div className="flex-sub margin-lr-20 margin-bottom-20 margin-top-4 padding-20 bg-color-ffffff border-radius-4">
				{/* 筛选条件 开始 */}
				<div className="flex margin-bottom-16 padding-bottom-16 border-bottom-e5e6eb">
					<div className="flex-sub padding-right-16 border-right-e5e6eb">
						<Form
							form={form}
							labelCol={{
								style: { width: '68px' },
							}}
							labelAlign="left"
							className="form-filter"
							initialValues={{
								deptId: [],
								userType: '2',
							}}
						>
							<Row gutter={[16, 16]}>
								<Col xs={24} sm={24} md={12} lg={8}>
									<Form.Item label="用户名" name="keywords">
										<Input placeholder="请输入用户名" />
									</Form.Item>
								</Col>
								<Col xs={24} sm={24} md={12} lg={8}>
									<Form.Item label="手机号码" name="mobile">
										<Input placeholder="请输入手机号码" />
									</Form.Item>
								</Col>
								<Col xs={24} sm={24} md={12} lg={8}>
									<Form.Item label="所属部门" name="deptId">
										<Cascader options={deptList} placeholder="请选择归属部门" maxTagCount="responsive" changeOnSelect />
									</Form.Item>
								</Col>
								<Col xs={24} sm={24} md={12} lg={8}>
									<Form.Item label="用户角色" name="roleId">
										<Select options={roleList} placeholder="请选择用户角色" allowClear />
									</Form.Item>
								</Col>
								<Col xs={24} sm={24} md={12} lg={8}>
									<Form.Item label="用户状态" name="status">
										<Select options={getDictData('userStatus')} placeholder="请选择用户状态" allowClear />
									</Form.Item>
								</Col>
								<Col xs={24} sm={24} md={12} lg={8}>
									<Form.Item label="用户来源" name="userSource">
										<Select options={getDictData('userSource')} placeholder="请选择用户来源" allowClear />
									</Form.Item>
								</Col>
								<Col xs={24} sm={24} md={12} lg={8}>
									<Form.Item label="用户类型" name="userType">
										<Select options={getDictData('userType')} placeholder="请选择用户类型" allowClear />
									</Form.Item>
								</Col>
							</Row>
						</Form>
					</div>
					<Space direction="vertical" size={20} className="padding-left-16">
						<Space
							size={10}
							className="padding-lr-14 height-32 line-height-32 font-size-14 border-radius-2 bg-color-165dff color-ffffff cursor-pointer"
							onClick={() => {
								searchData();
							}}
						>
							<SearchOutlined />
							<span>查询</span>
						</Space>
						<Space
							size={10}
							className="padding-lr-14 height-32 line-height-32 font-size-14 border-radius-2 bg-color-f2f3f5 color-4e5969 cursor-pointer"
							onClick={() => {
								form.resetFields();
								pagination.current = 1;
								setPagination(pagination);
								getTableData();
							}}
						>
							<ReloadOutlined />
							<span>重置</span>
						</Space>
					</Space>
				</div>
				{/* 筛选条件 结束 */}
				{/* 按钮操作 开始 */}
				<Space size={16} className="margin-bottom-16">
					<Space
						size={10}
						className="padding-lr-16 height-32 line-height-32 font-size-14 border-radius-2 bg-color-165dff color-ffffff cursor-pointer"
						onClick={() => {
							handleOpenForm();
						}}
					>
						<PlusOutlined />
						<span>新建</span>
					</Space>
				</Space>
				{/* 按钮操作 结束 */}
				{/* 表格列表 开始 */}
				<Table
					rowKey="id"
					dataSource={dataSource}
					pagination={{
						...pagination,
						showQuickJumper: true,
						showSizeChanger: true,
						onChange: searchData,
					}}
					scroll={{ x: 'max-content' }}
				>
					<Table.Column title="用户名" dataIndex="userName" key="userName" />
					<Table.Column title="手机号码" dataIndex="mobile" key="mobile" render={(mobile) => <div>{mobile || '--'}</div>} />
					<Table.Column title="所属公司" dataIndex="companyName" key="companyName" render={(companyName) => '大湾区中心'} />
					<Table.Column
						title="所属部门"
						dataIndex="deptList"
						key="deptList"
						render={(deptList) => (
							<div className="world-break-all max-width-320">{(deptList || []).map(({ name }) => name).join(',') || '--'}</div>
						)}
					/>
					<Table.Column title="职位名称" dataIndex="positionName" key="positionName" render={(positionName) => positionName || '--'} />
					<Table.Column
						title="用户角色"
						dataIndex="roleList"
						key="roleList"
						render={(deptList) => (
							<div className="world-break-all max-width-320">{(deptList || []).map(({ roleName }) => roleName).join(',') || '--'}</div>
						)}
					/>
					<Table.Column
						title="用户来源"
						dataIndex="userSource"
						key="userSource"
						render={(userSource) => <div>{getDictLabelByValue('userSource', userSource)}</div>}
					/>
					<Table.Column
						title="用户类型"
						dataIndex="userType"
						key="userType"
						render={(userType) => <div>{getDictLabelByValue('userType', userType)}</div>}
					/>
					<Table.Column
						title="状态"
						dataIndex="status"
						key="status"
						align="center"
						render={(status) => (
							<Space size={8}>
								<div className={`width-6 height-6 border-radius-6 ${status === 1 ? 'bg-color-00b42a' : 'bg-color-c9cdd4'}`}></div>
								<div>{getDictLabelByValue('userStatus', status)}</div>
							</Space>
						)}
					/>
					<Table.Column
						title="是否内部员工"
						dataIndex="insiderStatus"
						key="insiderStatus"
						align="center"
						render={(insiderStatus, record) => (
							<Space size={8}>
								<Switch
									checked={insiderStatus == 1}
									onChange={() => {
										insiderStatusChange({
											insiderStatus: insiderStatus == 1 ? 0 : 1,
											ids: [record.id],
										});
									}}
								/>
							</Space>
						)}
					/>
					<Table.Column
						title="操作"
						fixed="right"
						dataIndex="id"
						key="id"
						align="center"
						render={(id) => (
							<Space size={16}>
								<Permission hasPermi={['system:user:resetPwd']}>
									<div
										className="color-165dff cursor-pointer"
										onClick={() => {
											setResetId(id);
										}}
									>
										重置密码
									</div>
								</Permission>
								<div
									className="color-165dff cursor-pointer"
									onClick={() => {
										handleOpenForm(id);
									}}
								>
									编辑
								</div>
								<Popconfirm
									icon={false}
									description="是否确认删除此数据?"
									onConfirm={() => {
										handleDel([id]);
									}}
									okText="确认"
									cancelText="取消"
								>
									<div className="color-f53f3f cursor-pointer">删除</div>
								</Popconfirm>
							</Space>
						)}
					/>
				</Table>
				{/* 表格列表 结束 */}
				{/* 编辑/新建 弹窗 开始 */}
				<ModalForm
					ref={ModalFormRef}
					modelConfig={{
						title: '新建活动',
					}}
					onOk={(res) => {
						console.log('🚀 ~ Index ~ res:', res);
						searchData(pagination.current);
					}}
					FormComp={(props) => <Curd ref={props.FormCompRef} formQuery={formQuery} />}
				/>
				{/* 编辑/新建 弹窗 结束 */}

				{/* 重置密码 弹窗 开始 */}
				<ModalResetPwd resetId={resetId} onCallback={() => setResetId('')} />
				{/* 重置密码 弹窗 结束 */}
			</div>
		</div>
	);
};

const ModalResetPwd = (props = {}) => {
	const [form] = Form.useForm();

	// 关闭
	const close = () => {
		form.resetFields();
		props.onCallback();
	};

	// 提交
	const submit = () => {
		form.validateFields().then((values) => {
			updatePassword({
				userId: props.resetId,
				password: values.password,
			}).then(() => {
				message.success('重置密码成功');
				props.onCallback();
			});
		});
	};

	return (
		<Modal centered open={props.resetId !== ''} title="重置密码" onCancel={close} onOk={submit}>
			<div className="margin-top-24">
				<Form form={form}>
					<Form.Item label="新密码" name="password" rules={[{ required: true, message: '请输入新密码' }]}>
						<Input placeholder="请输入新密码" />
					</Form.Item>
				</Form>
			</div>
		</Modal>
	);
};

export default Index;
