import { lazy, useEffect, useLayoutEffect, useState } from "react";
import { useSelector, useDispatch } from "react-redux";
import { setUserInfo, setSystemName,setSystemLogo } from "@/store/userSlice";
import { appOrigin } from "@/config/env";
import { setAccessToken } from "@/utils/common";
import { filterRouterName } from "@/utils/router";

import { useLocation, matchRoutes, useNavigate } from "react-router-dom";
import LayOutDefault from "@/LayOut";
import LayOutNew from "@/LayOut/index-new";

import { userInfo as getUserInfoData } from "@/api/login";
import { tenantAppInfo } from "@/api/common";

const ErrorBoundary = lazy(() => import("@/pages/ErrorBoundary"));

// 路由
import pagesRouters, { baseRouter } from "./pages";
import { Spin, message } from "antd";

const EmptyElement = (props = {}) => {
    const location = useLocation();
    const dispatch = useDispatch();
    const navigate = useNavigate();

    // 登录凭证
    let token = useSelector((state) => {
        return state.user.token;
    });
    // 用户信息
    const userInfo = useSelector((state) => {
        return state.user.userInfo;
    });

    // 路由信息
    const routerList = useSelector((state) => {
        return state.user.routerList || [];
    });

    // 选中系统
    const curSystemInfo = useSelector((state) => {
        return state.user.curSystemInfo || [];
    });
    // 处理 权限 处理 路由 在 这里可以处理
    const [meta, setMeta] = useState({});

    useEffect(() => {
        // 外部跳转token 写入并登录
        if (location.search.indexOf("?token=") > -1) {
            setAccessToken(location.search.split("=")[1]);
            window.location.replace(appOrigin + location.pathname);
            return;
        }

        // 登录标志
        if (token) {
            if (!userInfo.id) {
                tenantAppInfo().then(resp => {
                    const project = (resp.data || []).find(ov => ov.appType == 1 && ov.smpApp && ov.smpApp.remark
                        == 'gbac-tip-frontend');
                    if (project && project.configContent) {
                        try {
                            const configContent = JSON.parse(project.configContent);
                            if(configContent && configContent.systemName){
                                dispatch(setSystemName(configContent.systemName));
                            }
                            if(configContent && configContent.systemLogo){
                                dispatch(setSystemLogo(configContent.systemLogo));
                            }
                        } catch (error) {
                            
                        }
                    }

                    getUserInfoData({}).then((res) => {
                        const resData = res.data || {};
                        const permissionList = resData.permissionList || [];
                        // 权限判断 非活动
                        if (
                            !permissionList.every((item) =>
                                ["event", "system"].some(
                                    (ov) => item.perms.indexOf(ov) > -1
                                )
                            )
                        ) {
                            // 使用本地路由
                            // const routerData = pagesRouters

                            dispatch(setUserInfo(resData));
                        } else {
                            message.error(
                                "您没有访问系统的权限，请联系管理人员配置"
                            );
                            navigate("/login");
                        }
                    }).catch(() => {
                        message.error(
                            "您没有访问系统的权限，请联系管理人员配置"
                        );
                        navigate("/login");
                    });
                }).catch(() => {
                    message.error(
                        "您没有访问系统的权限，请联系管理人员配置"
                    );
                    navigate("/login");
                });
            }
        } else {
            navigate("/login");
        }

        // 匹配路由
        const matches = matchRoutes(routerList, {
            pathname: location.pathname,
        });
        if (matches && matches.length) {
            const curRouter = matches[matches.length - 1].route.meta || {};
            setMeta(curRouter);
            // document.title = curRouter.title ? metaTitle(curRouter.title) : '';
        }
    }, [location.pathname, routerList]);

    useLayoutEffect(() => {
        document.documentElement.scrollTo(0, 0);
    }, [location.pathname]);

    return ["bidmgt", "businessOppty", "competition"].includes(
        curSystemInfo.path
    ) ? (
        <props.LayOut meta={meta} />
    ) : (
        <LayOutNew meta={meta} />
    );
};

export const createRouterData = (routerList = []) => {
    const routerData = [...baseRouter];
    if (routerList.length === 0) {
        routerData.push({
            path: "*",
            element: (
                <EmptyElement
                    LayOut={() => <Spin spinning fullscreen></Spin>}
                />
            ),
        });
    } else {
        routerData.unshift({
            path: "/",
            element: <EmptyElement LayOut={LayOutDefault} />,
            children: routerList,
            errorElement: <ErrorBoundary />,
        });
        routerData.push({
            path: "*",
            element: <ErrorBoundary />,
        });
    }

    return routerData;
};
