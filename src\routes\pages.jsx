import { Suspense, lazy } from 'react';
import { Navigate, Outlet } from 'react-router-dom';
import SvgIcon from '@/assets/icons';

// 基础路由
export const baseRouterList = [
	{
		path: 'login',
		meta: { title: '登录', hidden: true },
		element: () => import('@/pages/Login'),
	},
	{
		path: 'findPassword',
		meta: { title: '找回密码', hidden: true },
		element: () => import('@/pages/FindPassword'),
	},
];

// 页面路由
const routerList = [
	{
		icon: 'icon-dwq-icon-fasongtongzhi',
		path: 'dashboardzs',
		meta: { title: '工作台' },
		element: () => import('@/pages/Bidmgt/Dashboard/index'),
	},
	{
		icon: 'icon-dwq-icon-fasongtongzhi',
		path: 'projectManage',
		meta: { title: '项目管理' },
		children: [
			{
				redirect: 'list',
			},
			{
				path: 'list',
				meta: { title: '项目列表', hidden: true },
				element: () => import('@/pages/Bidmgt/ProjectManage/List/index'),
			},
			{
				path: 'detail',
				meta: { title: '项目详情', hidden: true },
				element: () => import('@/pages/Bidmgt/ProjectManage/Detail/index'),
			},
		],
	},
	// {
	// 	icon: 'icon-dwq-icon-fasongtongzhi',
	// 	path: 'personalCenterZs',
	// 	meta: { title: '个人中心' },
	// 	element: () => import('@/pages/Bidmgt/PersonalCenter/info/index'),
	// },
	{
		icon: 'icon-dwq-icon-fasongtongzhi',
		path: 'configProject',
		meta: { title: '项目配置' },
		children: [
			{
				redirect: 'projectType',
			},
			{
				path: 'projectType',
				meta: { title: '项目类型' },
				element: () => import('@/pages/Bidmgt/ConfigCenter/ProjectType/index'),
			},
			{
				path: 'projectStage',
				meta: { title: '项目阶段' },
				element: () => import('@/pages/Bidmgt/ConfigCenter/ProjectStage/index'),
			},
			{
				path: 'industryType',
				meta: { title: '产业类型' },
				element: () => import('@/pages/Bidmgt/ConfigCenter/IndustryType/index'),
			},
		],
	},
	{
		icon: 'icon-dwq-icon-fasongtongzhi',
		path: 'dashboardsj',
		meta: { title: '工作台' },
		element: () => import('@/pages/BusinessOppty/Dashboard/index'),
	},
	{
		icon: 'icon-dwq-icon-fasongtongzhi',
		path: 'opportunity',
		meta: { title: '商机管理' },
		children: [
			{
				redirect: 'list',
			},
			{
				path: 'list',
				meta: { title: '商机列表', hidden: true },
				element: () => import('@/pages/BusinessOppty/OpptyManage/List/index'),
			},
			{
				path: 'detail',
				meta: { title: '商机详情', hidden: true },
				element: () => import('@/pages/BusinessOppty/OpptyManage/Detail/index'),
			},
			{
				path: 'exam',
				meta: { title: '靠谱都测评', hidden: true },
				element: () => import('@/pages/BusinessOppty/OpptyManage/Exam/index'),
			},
		],
	},
	{
		icon: 'icon-dwq-icon-fasongtongzhi',
		path: 'personalCenterSj',
		meta: { title: '个人中心' },
		element: () => import('@/pages/BusinessOppty/PersonalCenter/index'),
	},
	{
		icon: 'icon-dwq-icon-fasongtongzhi',
		path: 'configOpportunity',
		meta: { title: '商机配置' },
		children: [
			{
				redirect: 'opportunityType',
			},
			{
				path: 'opportunityType',
				meta: { title: '商机类型' },
				element: () => import('@/pages/BusinessOppty/ConfigCenter/OpportunityType/index'),
			},
			{
				path: 'opportunityStage',
				meta: { title: '商机阶段' },
				element: () => import('@/pages/BusinessOppty/ConfigCenter/OpportunityStage/index'),
			},
			{
				path: 'productType',
				meta: { title: '产品分类' },
				element: () => import('@/pages/BusinessOppty/ConfigCenter/ProductType/index'),
			},
			{
				path: 'customType',
				meta: { title: '客户类型' },
				element: () => import('@/pages/BusinessOppty/ConfigCenter/CustomType/index'),
			},
			{
				path: 'reliabilityType',
				meta: { title: '商机自评靠谱度' },
				element: () => import('@/pages/BusinessOppty/ConfigCenter/ReliabilityType/index'),
			},
		],
	},
	{
		icon: 'icon-dwq-icon-fasongtongzhi',
		path: 'system',
		meta: { title: '系统管理' },
		children: [
			{
				path: 'user',
				meta: { title: '用户管理' },
				children: [
					{
						redirect: 'list',
					},
					{
						path: 'list',
						meta: { title: '用户管理' },
						element: () => import('@/pages/System/User/index'),
					},
				],
			},
			{
				path: 'dept',
				meta: { title: '部门管理' },
				element: () => import('@/pages/System/Dept/index'),
			},
			{
				path: 'roles',
				meta: { title: '角色管理' },
				element: () => import('@/pages/System/Roles/index'),
			},
		],
	},
];

// 格式处理
const getFormatRouter = (routerList) => {
	return routerList.map((routerInfo) => {
		const { icon = null, path = '', meta = {}, redirect = '', element = '', children = [] } = routerInfo || {};

		// 重定向
		if (redirect) {
			return {
				path: '',
				element: <Navigate to={redirect} replace={true} />,
			};
		}

		const Element = element ? lazy(element) : null;

		const routerItem = {
			icon: icon ? <SvgIcon type={icon} /> : null,
			path,
			meta,
			element: Element ? <Suspense fallback={<></>}>{<Element meta={meta} />}</Suspense> : <Outlet />,
		};
		if (children.length) {
			routerItem.children = getFormatRouter(children);
		}
		return routerItem;
	});
};

export default getFormatRouter(routerList);

export const baseRouter = getFormatRouter(baseRouterList);
