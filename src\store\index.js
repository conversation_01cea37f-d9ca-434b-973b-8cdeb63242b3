import { configureStore } from '@reduxjs/toolkit';
import counterSlice from './counterSlice';
import userSlice from './userSlice';
import menuSlice from './menuSlice';
import messageSlice from './messageSlice';
import layoutSlice from './layoutSlice';
import { combineReducers } from 'redux';

const reducers = combineReducers({
	counter: counterSlice,
	user: userSlice,
	menu: menuSlice,
	message: messageSlice,
	layout: layoutSlice,
});

const store = configureStore({
	reducer: reducers,
	//不添加这句 redux-toolkit 会报无法序列化Warning
	middleware: (getDefaultMiddleware) =>
		getDefaultMiddleware({
			serializableCheck: false,
		}),
});
export default store;

// 栗子
// import { useSelector, useDispatch } from 'react-redux'
// import { decrement, increment } from '@/store/counterSlice'
// 获取对应数据
// const count = useSelector((state) => state.counter.value)
// 更新数据 调用方法
// const dispatch = useDispatch()
// onClick={() => dispatch(increment())}
// onClick={() => dispatch(decrement())}
