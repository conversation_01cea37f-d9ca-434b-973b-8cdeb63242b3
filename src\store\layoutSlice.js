import { createSlice } from '@reduxjs/toolkit';

export const layoutSlice = createSlice({
	name: 'layout',
	initialState: {
		hideHeader: false,
		hideMenu: false,
		hideTitle: false,
	},
	reducers: {
		setLayout: (state, action = {}) => {
			state.hideHeader = action.payload?.hideHeader || false;
			state.hideMenu = action.payload?.hideMenu || false;
			state.hideTitle = action.payload?.hideTitle || false;
		},
	},
});

// Action creators are generated for each case reducer function
export const { setLayout } = layoutSlice.actions;

export default layoutSlice.reducer;
