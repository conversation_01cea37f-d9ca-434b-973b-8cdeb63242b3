import { createSlice } from '@reduxjs/toolkit';

export const menuSlice = createSlice({
	name: 'menu',
	initialState: {
		menuClass: '',
	},
	reducers: {
		menuHide: (state) => {
			state.menuClass = 'width-80';
		},
		menuShow: (state) => {
			state.menuClass = '';
		},
	},
});

// Action creators are generated for each case reducer function
export const { menuHide, menuShow } = menuSlice.actions;

export default menuSlice.reducer;
