import { createSlice } from '@reduxjs/toolkit'
export const messageSlice = createSlice({
	name: 'message',
	initialState: {
		messageList: [],
		loopFlag: '',
	},
	reducers: {
		setMessageList: (state, action = {}) => {
			state.messageList = action.payload
		},
		setLoopFlag: (state, action = {}) => {
			state.loopFlag = action.payload
		},
	},
})
// Action creators are generated for each case reducer function
export const { setMessageList,setLoopFlag } = messageSlice.actions
export default messageSlice.reducer
