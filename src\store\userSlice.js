import { createSlice } from '@reduxjs/toolkit';
import { getAccessToken, setCurSystemName } from '@/utils/common';

export const userSlice = createSlice({
	name: 'user',
	initialState: {
		systemName:'',
		systemLogo:'',
		userInfo: {},
		token: getAccessToken(),
		routerList: [],
		curSystemInfo: {
			perms: '',
			path: '',
			title: '',
			logoUrl: '',
			menuList: [],
		},
		switchSystemOpen: false,
		taskCount: {
			mainTotal: 0,
			competitionTotal: 0,
		},
	},
	reducers: {
		setUserInfo: (state, action = {}) => {
			state.userInfo = action.payload;
		},
		setToken: (state, action = {}) => {
			state.token = action.payload;
		},
		setRouterList: (state, action = {}) => {
			state.routerList = action.payload;
		},
		setCurSystemInfo: (state, action = {}) => {
			setCurSystemName(action.payload.perms);
			state.curSystemInfo = action.payload;
		},
		setSwitchSystemOpen: (state, action = {}) => {
			state.switchSystemOpen = action.payload;
		},
		setTaskCount: (state, action = {}) => {
			state.taskCount = action.payload;
		},
		setSystemName: (state, action = {}) => {
			state.systemName = action.payload;
		},
		setSystemLogo: (state, action = {}) => {
			state.systemLogo = action.payload;
		},
	},
});

// Action creators are generated for each case reducer function
export const { setToken, setUserInfo, setRouterList, setCurSystemInfo, setSwitchSystemOpen, setTaskCount, setSystemName, setSystemLogo } = userSlice.actions;

export default userSlice.reducer;
