import store from '@/store';
import { setTaskCount } from '@/store/userSlice';

import { queryCategoryByCode } from '@/api/Achv/common';
import { getTaskCount } from '@/api/Achv/Task';
import { notificationPage } from '@/api/Bidmgt/PersonalCenter';

// 获取字典数据
export const getCategoryValueList = (categoryCode = '') => {
	return new Promise((resolve) => {
		if (typeof categoryCode !== 'string' || categoryCode === '') {
			resolve([]);
		}
		queryCategoryByCode({ categoryCode }).then((res) => {
			resolve(
				(res.data || []).map((ov) => ({
					label: ov.value,
					value: ov.id,
					code: ov.code,
				}))
			);
		});
	});
};

// 更新 待办事项 统计
export const updateTaskCount = () => {
	Promise.all([getTaskCount({ createSources: [1, 2] }), getTaskCount({ createSources: [3] })]).then((resList) => {
		store.dispatch(
			setTaskCount({
				mainTotal: resList[0]?.data?.total - 0 || 0,
				competitionTotal: resList[1]?.data?.total - 0 || 0,
			})
		);
	});
};

/* 更新获取个人消息数量 */
export const updatePersonalMsgCount = () => {
	notificationPage(
		{
			pageNum: 1,
			pageSize: 100,
			readFlag: 0,
		},
		{
			showLoading: false,
		}
	).then((res) => {
		const personalMsgTotal = +res.data?.total;
		store.dispatch(setTaskCount({ personalMsgTotal }));
	});
};
