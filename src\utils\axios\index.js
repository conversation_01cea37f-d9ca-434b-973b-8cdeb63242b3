import { service } from './service';
import { mode } from '@/config/env';
const catchRequestMap = new Map();
const refreshCacheMap = new Map();
export const request = (option) => {
	const {
		url,
		method,
		params,
		data,
		headersType,
		responseType,
		header = {},
		showLoading = true,
		isWhiteList = false,
		isCache = false,
		refreshCache = [],
	} = option;
	const key = url + method + JSON.stringify(params) + JSON.stringify(data);

	// 是否缓存 读取缓存
	if (isCache && catchRequestMap.get(key)) {
		return catchRequestMap.get(key);
	}

	const headers = {
		'Content-Type': headersType || 'application/json',
		...header,
	};
	if (mode == 'development') {
		headers.access_token = '9f1d952655041db9803072910a5c6121';
	}

	const req = service({
		url: url,
		method,
		params,
		data,
		responseType: responseType,
		headers,
		showLoading,
		isWhiteList,
	});

	req.catch(() => {
		if (isCache) {
			catchRequestMap.delete(key);
		}
	});

	// 写入/更新（之前是否缓存 ）缓存
	if (isCache || catchRequestMap.get(key)) {
		catchRequestMap.set(key, req);
	}

	// 写入刷新缓存接口
	if (isCache && refreshCache.length) {
		refreshCache.forEach((apiPath) => {
			if (!refreshCacheMap.has(apiPath)) {
				refreshCacheMap.set(apiPath, key);
			}
		});
	}

	// 删除缓存记录
	if (refreshCacheMap.has(url) && catchRequestMap.has(refreshCacheMap.get(url))) {
		console.log('更新缓存', refreshCacheMap.get(url));
		catchRequestMap.delete(refreshCacheMap.get(url));
	}
	return req;
};

export default {
	// get: async (option) => {
	//   const res = await request({ method: "GET", ...option });
	//   return res;
	// },
	// post: async (option) => {
	//   const res = await request({ method: "POST", ...option });
	//   return res;
	// },
	// postOriginal: async (option) => {
	//   const res = await request({ method: "POST", ...option });
	//   return res;
	// },
	// delete: async (option) => {
	//   const res = await request({ method: "DELETE", ...option });
	//   return res;
	// },
	// put: async (option) => {
	//   const res = await request({ method: "PUT", ...option });
	//   return res;
	// },
	download: async (option) => {
		const res = await request({
			method: 'GET',
			responseType: 'blob',
			...option,
		});
		return res;
	},
	upload: async (option) => {
		option.headersType = 'multipart/form-data';
		const res = await request({ method: 'POST', ...option });
		return res;
	},
};
