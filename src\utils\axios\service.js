import axios from "axios";
// import qs from 'qs'
// import { getAccessToken, getRefreshToken, getTenantId, removeToken, setToken } from '@/utils/auth'
import errorCode from "./errorCode";
import { message } from "antd";
import store from "@/store";
import { incrementByAmount } from "@/store/counterSlice";
import { appOrigin } from "@/config/env";
import { logout } from "@/utils/common";

// Axios 无感知刷新令牌，参考 https://www.dashingdog.cn/article/11 与 https://segmentfault.com/a/1190000020210980 实现

// 请求白名单，无须token的接口
const whiteList = ["/login"];

// 创建axios实例
const service = axios.create({
    baseURL:
        import.meta.env.MODE == "development"
            ? "/api"
            : import.meta.env.VITE_BASE_URL, // api 的 base_url
    timeout: 600000, // 请求超时时间
    withCredentials: false, // 禁用 Cookie 等信息
});
// request拦截器
service.interceptors.request.use(
    (config) => {
        // 发起请求
        if (
            config.headers &&
            config.headers["Content-Type"] == "application/json" &&
            config.showLoading
        ) {
            store.dispatch(incrementByAmount(1));
        }
        // 是否需要设置 token
        let isToken = (config.headers || {}).isToken === false;
        whiteList.some((v) => {
            if (config.url) {
                config.url.indexOf(v) > -1;
                return (isToken = false);
            }
        });
        const curToken = store.getState().user.token;
        if (curToken && !isToken) {
            config.headers["session_token"] = curToken; // 让每个请求携带自定义token
        }
        const params = config.params || {};
        const data = config.data || false;
        if (
            config.method?.toUpperCase() === "POST" &&
            config.headers["Content-Type"] ===
                "application/x-www-form-urlencoded"
        ) {
            config.data = data; //qs.stringify(data)
        }
        // get参数编码
        if (config.method?.toUpperCase() === "GET" && params) {
            let url = config.url + "?";
            for (const propName of Object.keys(params)) {
                const value = params[propName];
                if (
                    value !== void 0 &&
                    value !== null &&
                    typeof value !== "undefined"
                ) {
                    if (typeof value === "object") {
                        for (const val of Object.keys(value)) {
                            const params = propName + "[" + val + "]";
                            const subPart = encodeURIComponent(params) + "=";
                            url +=
                                subPart + encodeURIComponent(value[val]) + "&";
                        }
                    } else {
                        url += `${propName}=${encodeURIComponent(value)}&`;
                    }
                }
            }
            // 给 get 请求加上时间戳参数，避免从缓存中拿数据
            // const now = new Date().getTime()
            // params = params.substring(0, url.length - 1) + `?_t=${now}`
            url = url.slice(0, -1);
            config.params = {};
            config.url = url;
        }
        /* if (import.meta.env.MODE == "development") {
            config.headers["access_token"] = "9f1d952655041db9803072910a5c6121";
        } */
        config.headers["app-origin"] = appOrigin; // saas
        return config;
    },
    (error) => {
        // 发起请求 记录
        store.dispatch(incrementByAmount(-1));
        // Do something with request error
        console.log(error); // for debug
        Promise.reject(error);
    }
);

// response 拦截器
service.interceptors.response.use(
    async (response) => {
        // 发起请求 记录
        if (response.config.headers && response.config.showLoading) {
            setTimeout(() => {
                store.dispatch(incrementByAmount(-1));
            }, 30);
        }
        const { data } = response;
        // 如果是白名单 直接返回
        if (response.config.headers.isWhite || response.config.isWhiteList) {
            return response.data;
        }

        if (!data) {
            // 返回“[HTTP]请求没有返回值”;
            throw new Error();
        }

        // 上传文件特殊处理
        if (Array.isArray(data)) {
            return response.data;
        }

        // const { t } = useI18n()
        // 未设置状态码则默认成功状态
        const code = data.code || "00000";
        // 二进制数据则直接返回
        if (
            response.request.responseType === "blob" ||
            response.request.responseType === "arraybuffer"
        ) {
            return response.data;
        }
        // 获取错误信息
        const msg = data.subMsg || errorCode[code] || errorCode["default"];
        if (code === 401 || code == "40000") {
            logout().then(() => {
                message
                    .warning({
                        content: msg || "登录失效了",
                    })
                    .then(() => {
                        window.location.href =
                            import.meta.env.VITE_BASE_PATH +
                            (import.meta.env.VITE_BASE_PATH
                                ? "/login".slice(1)
                                : "/login");
                    });
                return Promise.reject(new Error(msg));
            });
        } else if (code === 500) {
            message.warning({
                content: msg || "服务出错了",
            });
            return Promise.reject(new Error(msg));
        } else if (code !== "00000") {
            message.warning({
                content: msg || "操作失败啦",
            });
            return Promise.reject("error");
        } else {
            return data;
        }
    },
    (error) => {
        // 发起请求 记录
        setTimeout(() => {
            store.dispatch(incrementByAmount(-1));
        }, 30);
        console.log("err" + error); // for debug
        let { message: msg } = error;
        if (msg === "Network Error") {
            message.warning({
                content: "网络失败啦",
            });
        } else if (msg.includes("timeout")) {
            message.warning({
                content: "超时啦",
            });
        } else if (msg.includes("Request failed with status code")) {
            message.warning({
                content: "操作失败啦!",
            });
        }
        return Promise.reject(error);
    }
);

export { service };
