import { listCategoryValue } from '@/api/Bidmgt/Dict/index';
import { listInvestmentResponsibleDept } from '@/api/Bidmgt/ConfigCenter/index';

// 获取字典数据
export const getCategoryValueList = (categoryCode = '') => {
	return new Promise((resolve) => {
		if (typeof categoryCode !== 'string' || categoryCode === '') {
			resolve([]);
		}
		listCategoryValue({ categoryCode }).then((res) => {
			resolve(
				(res.data || []).map((ov) => ({
					label: ov.value,
					value: ov.id,
					code: ov.code,
				}))
			);
		});
	});
};

// 获取责任单位

export const getInvestmentResponsibleDept = ({ isCache = true } = {}) => {
	return new Promise((resolve) => {
		listInvestmentResponsibleDept({}, { isCache }).then(({ data }) => {
			resolve(data.map((ov) => ({ value: ov.deptId, label: ov.deptName })));
		});
	});
};
