import OSS from 'ali-oss';
import store from '@/store';
import { setToken, setUserInfo, setCurSystemInfo } from '@/store/userSlice';
import { appOrigin } from '@/config/env';
import { fileUpload, getStsToken } from '@/api/common';

/**
 * @desc  函数防抖---“立即执行版本” 和 “非立即执行版本” 的组合版本
 * @param  func 需要执行的函数
 * @param  wait 延迟执行时间（毫秒）
 * @param  immediate---true 表立即执行，false 表非立即执行
 **/
export function debounce(func, wait = 200, immediate = false) {
	let timer;

	return function () {
		let context = this;
		let args = arguments;

		if (timer) clearTimeout(timer);
		if (immediate) {
			var callNow = !timer;
			timer = setTimeout(() => {
				timer = null;
			}, wait);
			if (callNow) func.apply(context, args);
		} else {
			timer = setTimeout(function () {
				func.apply(context, args);
			}, wait);
		}
	};
}
/**
 * @desc  函数 节流
 * @param  func 需要执行的函数
 * @param  wait 延迟执行时间（毫秒）
 **/
export function throttle(fn, delay = 200) {
	let timer = null;
	let startTime = Date.now();
	return function () {
		let curTime = Date.now(); // 当前时间，用来计算剩余时间（从上次执行完到现在还有多久执行下次函数）
		let remainning = delay - (curTime - startTime); // curTime - startTime 本次间隔已经过去多久
		let context = this;
		let args = arguments;
		clearTimeout(timer);
		if (remainning <= 0) {
			// 说明应该立即执行下一次事件
			fn.apply(context, args);
			startTime = Date.now();
		} else {
			// 距离下次还剩余时间，重新生成 timer,延迟时间改为 remainning
			timer = setTimeout(fn, remainning);
		}
	};
}
/**
 * @description: 获取url上面的 参数
 * @param {*} url: ?key=val
 * @return {*}
 */
export function getUrlParams(url) {
	if (!url) return null;
	let urlStr = url.split('?')[1];
	const urlSearchParams = new URLSearchParams(urlStr);
	const result = Object.fromEntries(urlSearchParams.entries());
	return result;
}
/**
 * @description: 时间格式化 YYYY-MM-DD 如果没有参数 就会获取今天的 YYYY-MM-DD
 * @param {*} date:  new Date()
 * @return {*}
 */
export function changeDateFormat(date) {
	if (!date) {
		date = new Date();
	}
	const year = date.getFullYear();
	const month = date.getMonth() + 1; // 月份是从0开始的
	const day = date.getDate();
	const hour = date.getHours();
	const min = date.getMinutes();
	const sec = date.getSeconds();
	const newTime =
		year +
		'-' +
		(month < 10 ? '0' + month : month) +
		'-' +
		(day < 10 ? '0' + day : day) +
		' ' +
		(hour < 10 ? '0' + hour : hour) +
		':' +
		(min < 10 ? '0' + min : min) +
		':' +
		(sec < 10 ? '0' + sec : sec);

	return newTime;
}

/**
 * @description: 文件下载方法 具体文件地址下载
 * @param {*} href: 地址
 * @param {*} filename: 文件名称
 * @return {*}
 */
export function downloadFileByUrl(href, filename) {
	if (href && filename) {
		let a = document.createElement('a');
		a.download = filename; //指定下载的文件名
		a.href = href; //  URL对象
		a.click(); // 模拟点击

		window.URL = window.URL || window.webkitURL;
		window.URL.revokeObjectURL(a.href); // 释放URL 对象
	}
}

/**
 * @description: 下载文件 数据转成文件下载
 * @param {*} data:Blob
 * @param {*} fileName:string
 * @param {*} mineType:string
 * @return {*}
 */
const downloadFileByBlob = (data = '', fileName = '', mineType = '') => {
	// 创建 blob
	const blob = new Blob([data], { type: mineType });
	// 创建 href 超链接，点击进行下载
	window.URL = window.URL || window.webkitURL;
	const href = URL.createObjectURL(blob);
	const downA = document.createElement('a');
	downA.href = href;
	downA.download = fileName;
	downA.click();
	// 销毁超连接
	window.URL.revokeObjectURL(href);
};

function base64ToBlob(base64data) {
	var arr = base64data.split(',');
	var mime = arr[0].match(/:(.*?);/)[1];
	var bstr = atob(arr[1]);
	var n = bstr.length;
	var u8arr = new Uint8Array(n);
	while (n--) {
		u8arr[n] = bstr.charCodeAt(n);
	}
	return { u8arr, mime };
}

export const download = {
	// 下载 Excel 方法
	excel: (data, fileName) => {
		downloadFileByBlob(data, fileName, 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
	},
	excel2: (data, fileName) => {
		downloadFileByBlob(data, fileName, 'application/vnd.ms-excel');
	},
	// 下载 Word 方法
	word: (data, fileName) => {
		downloadFileByBlob(data, fileName, 'application/msword');
	},
	// 下载 Zip 方法
	zip: (data, fileName) => {
		downloadFileByBlob(data, fileName, 'application/zip');
	},
	// 下载 Html 方法
	html: (data, fileName) => {
		downloadFileByBlob(data, fileName, 'text/html');
	},
	// 下载 Markdown 方法
	markdown: (data, fileName) => {
		downloadFileByBlob(data, fileName, 'text/markdown');
	},
	// 下载 base64 方法
	markBase64: (data, fileName) => {
		const { u8arr, mime } = base64ToBlob(data);
		downloadFileByBlob(u8arr, fileName, mime);
	},
	// 其他类型
	other: (data, fileName) => {
		downloadFileByBlob(data, fileName, 'application/octet-stream');
	},
};

/**
 * @description: rmb 分转元
 * @param {*} costNum:
 * @return {*}
 */
export const fractionalConversionYuan = (costNum = '') => {
	const pay_amount = `${costNum}`.padStart(3, '0');
	const money = `${pay_amount.slice(0, pay_amount.length - 2)}.${pay_amount.slice(pay_amount.length - 2)}`;
	return money;
};

/**
 * @description: 秒 转 分秒
 * @param {*} second: 数字
 * @return {*}
 */
export const timeCount = (second) => {
	var minute = Math.floor(second / 60);
	second %= 60;
	if (minute > 0) {
		return `${minute}分${second}秒`;
	} else {
		return `${second}秒`;
	}
};
/**
 * @description: input输入框只能输入数字和 小数点后两位
 * @param {*} obj:
 * @param {*} val:
 * @return {*}
 */
export function inputNum(value = '') {
	value.replace(/[^\d.]/g, ''); //清除"数字"和"."以外的字符
	value.replace(/^\./g, ''); //验证第一个字符是数字
	value.replace(/\.{2,}/g, ''); //只保留第一个, 清除多余的
	value.replace('.', '$#$').replace(/\./g, '').replace('$#$', '.');
	value.replace(/^(-)*(\d+)\.(\d\d).*$/, '$1$2.$3'); //只能输入两个小数
	return value;
}

export const sleep = (time = 1000) => {
	return new Promise((resolve) => setTimeout(resolve, time));
};

// 格式化千分位
export const formatter1000 = (v = '') => {
	return `${v}`.replace(/\d{1,3}(?=(\d{3})+$)/g, (s) => `${s},`);
};

// 随机颜色
export const getRandomColor = () => {
	return ['#7db3d9', '#ae9ee5', '#d294d2', '#e5a687', '#72bcce'][Math.ceil(Math.random() * 5) - 1];
};

// 过滤掉 为空为0 的 字段
export const objectFilterEmptyKey = (obj = {}) => {
	const params = {};
	for (const key in obj) {
		if (typeof obj[key] == 'object') {
			const item = obj[key];
			const newObj = {};
			for (const iKey in item) {
				if (item[iKey]) {
					newObj[iKey] = item[iKey];
				}
			}
			if (Object.keys(newObj).length) {
				params[key] = newObj;
			}
		} else {
			if (obj[key]) {
				params[key] = obj[key];
			}
		}
	}
	return params;
};

// 退出登录
export const logout = () => {
	return new Promise((resolve) => {
		removeAccessToken();
		removeCurSystemName();
		store.dispatch(setToken(''));
		store.dispatch(setUserInfo({}));
		store.dispatch(
			setCurSystemInfo({
				perms: '',
				path: '',
				title: '',
				logoUrl: '',
				menuList: [],
			})
		);
		resolve();
	});
};

const AccessTokenKey = 'ACCESS_TOKEN' + appOrigin;
export function getAccessToken() {
	return localStorage.getItem(AccessTokenKey);
}

export function setAccessToken(token) {
	localStorage.setItem(AccessTokenKey, token);
}

export function removeAccessToken() {
	localStorage.removeItem(AccessTokenKey);
}

// 选中系统
export function getCurSystemName() {
	return localStorage.getItem('curSystemName') || '';
}

export function setCurSystemName(curSystemName) {
	setTimeout(() => {
		if (getAccessToken()) {
			localStorage.setItem('curSystemName', curSystemName);
		}
	}, 50);
}

export function removeCurSystemName() {
	localStorage.removeItem('curSystemName');
}

// 标签处理
export function handleTag(tagList) {
	return tagList
		.filter((item, index) => {
			return item.tagYear || tagList.find((sItem, sIndex) => sIndex !== index && sItem.tagName === item.tagName) === undefined;
		})
		.map((item) => item.tagName + (item.tagYear ? `(${item.tagYear})` : ''));
}

// 富文本 base64 图片 上传 oss并替换
export function handleEditor(detailsContent) {
	return new Promise((resolve, reject) => {
		let imgReg = /<img.*?(?:>|V>)/gi;
		let srcReg = /(?<=(img[^>]*src="))[^"]*/g;
		let imgList = detailsContent.match(imgReg) || [];
		let srcList = [];

		imgList.forEach((img) => {
			let src = img.match(srcReg);
			if (src[0].indexOf('base64') > -1) {
				srcList.push(src[0]);
			}
		});

		if (srcList.length) {
			let formData = new FormData();
			srcList.forEach((item, index) => {
				formData.append('files', base64toFile(item, item.slice(20, 30) + index + '.png'));
			});

			fileUpload(formData)
				.then((res) => {
					srcList.forEach((item, index) => {
						detailsContent = detailsContent.replace(item, res[index]);
					});
					resolve(detailsContent);
				})
				.catch((err) => {
					reject(err);
				});
		} else {
			resolve(detailsContent);
		}
	});
}

// 将base64转换成文件流
export function base64toFile(base64Str, fileName) {
	// base64转Blob
	function _dataUrlToBlob(dataUrl) {
		const arr = dataUrl.split(',');
		const mime = arr[0].match(/:(.*?);/[1]);
		const blobStr = atob(arr[1]);
		let leng = blobStr.length;
		const uint8Array = new Uint8Array(leng);
		while (leng--) {
			uint8Array[leng] = blobStr.charCodeAt(leng);
		}
		return new Blob([uint8Array], { type: mime });
	}

	// blob 转 file
	function _blobToFile(blob, fileName) {
		return new File([blob], fileName);
	}

	const blob = _dataUrlToBlob(base64Str);
	return _blobToFile(blob, fileName);
}

// 全屏
export function toggleFullScreen() {
	if (!document.fullscreenElement) {
		document.documentElement.requestFullscreen();
	} else {
		if (document.exitFullscreen) {
			document.exitFullscreen();
		}
	}
}

// 根据id 获取部门父级Id
export function getDeptParentId(id, deptFlatList) {
	if (typeof id !== 'string' || id.trim() === '') {
		throw new Error('Invalid ID provided.');
	}

	function _findParentId(id, flatList) {
		const findData = flatList.find(({ value }) => value === id);

		if (id === '' || findData === undefined) {
			return '';
		}

		let result = findData.value;
		if (findData.parentId) {
			result = _findParentId(findData.parentId) + '|' + result;
		}
		return result;
	}

	const result = _findParentId(id, deptFlatList);

	return result ? result.split('|') : [];
}

// 打平部门列表
export function getDeptFlatData(data = []) {
	let flatList = [];

	data.forEach((item) => {
		flatList.push({
			value: item.value,
			label: item.label,
			parentId: item.parentId,
		});

		if (item.children && item.children.length) {
			flatList = flatList.concat(getDeptFlatData(item.children));
		}
	});

	return flatList;
}

/* 复制数据 */
export function copeText(text = '') {
	return new Promise((resolve) => {
		const input = document.createElement('input');
		//将input的值设置为需要复制的内容
		input.value = text;
		//添加input标签
		document.body.appendChild(input);
		//选中input标签
		input.select();
		//执行复制
		document.execCommand('copy');
		//移除input标签
		document.body.removeChild(input);
		resolve();
	});
}

// 扁平化树
export function flattenTree(deepTree, childKey = 'children', parentId = null, level = 0) {
	return deepTree.reduce((acc, node) => {
		// 将当前节点添加到累加器数组中，并设置其parentId和level
		acc.push({ ...node, parentId, level });
		// 如果当前节点有子节点，递归调用flattenDeepTree，并增加level
		if (node[childKey] && node[childKey].length > 0) {
			acc.push(...flattenTree(node[childKey], childKey, node.id, level + 1));
		}
		return acc;
	}, []);
}

/**
 * 获取 oss客户端
 */

let clientObj = null;
let clientPromise = null;
export function getOssClient() {
	if (clientPromise) {
		return clientPromise;
	}
	clientPromise = new Promise((resolve, reject) => {
		if (clientObj) {
			// 判断是否过期 过期重新请求
			if (clientObj.createDate + clientObj.expires * 1000 - clientObj.expires > 0) {
				resolve(clientObj);
				return;
			}
		}
		getStsToken().then((res) => {
			const { region, accessKeyId, accessKeySecret, bucket, securityToken: stsToken, expires } = res.data;
			const current = new OSS({
					region,
					accessKeyId,
					accessKeySecret,
					stsToken,
					bucket,
				}),
				clientObj = {
					current,
					region,
					bucket,
					expires: expires - 0,
					createDate: current.stsTokenFreshTime.valueOf(),
					ossUrl: `https://${bucket}.${region}.aliyuncs.com/`,
				};
			resolve(clientObj);
			clientPromise = null;
		});
	});
	return clientPromise;
}

// 生产 OSS 临时 附近url
export function createOSSTempFileUrl(url = '') {
	return new Promise((resolve, reject) => {
		getOssClient().then((client) => {
			console.log('🚀 ~ getOssClient ~ client:', client);
			// 需要临时加密URL
			if (url.includes(client.bucket)) {
				resolve(client.current.signatureUrl(url.replace(client.ossUrl, ''), { expires: 3600 }));
			} else {
				resolve(url);
			}
		});
	});
}


export function formatToTwoDecimalPlaces(value) {
	// 将输入转换为数字，如果转换失败（如非数字的字符串），则返回null
	const number = parseFloat(value);
	if (isNaN(number)) {
		return value;
	}
	// 使用 toFixed 方法格式化为两位小数
	return number.toFixed(2);
}
