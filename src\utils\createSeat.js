// 座位 绘制的 从 第几列 第几列开始
export const defaultStartColumnIndex = 2

// 座位 绘制的 从 第几行 第几列开始
export const defaultSeatStartRowIndex = 4

// 创建座位
export const createSeat = ({
	columnIndex = 0,
	rowIndex = 0,
	name = '',
	width = 90,
	height = 32,
	opacity = 0,
	x = 90,
	y = 32,
	seatType = 1,
}) => {
	return {
		columnIndex,
		rowIndex,
		seatType,
		autoArrange: 1,
		center: 0,
		description: '',
		seatNumber: 0,
		name: name || '',
		phone: '',
		id: `${new Date().valueOf()}${Math.random()}`,
		width: width || 90,
		height: height || 32,
		opacity: opacity,
		x: x,
		y: y,
	}
}

// 分发 绘制数据
export const dispatchDrawData = (dataList = []) => {
	// const dataList = !isDragging ? drawSeatData : tempDrawSeatData
	const obj = {
		// 绘制座位
		type1: [],
		// 绘制横向过道
		type2: [],
		// 绘制主席台
		type3: [],
		// 绘制纵向过道
		type4: [],

		// 绘制工作说明区域
		type5: [],
	}
	obj.type1 = [...dataList.filter((ov) => ov.seatType == 1)]

	const type2DataObj = dataList
		.filter((ov) => ov.seatType == 2)
		.reduce((pre, cur) => {
			;(pre[cur.rowIndex] || (pre[cur.rowIndex] = [])).push(cur)
			pre[cur.rowIndex].sort((a, b) => a.columnIndex - b.columnIndex)
			return pre
		}, {})
	const type2Data = Object.keys(type2DataObj).map((key) => {
		return {
			rowIndex: key,
			seatType: 2,
			autoArrange: 0,
			center: 0,
			description: '',
			seatNumber: 0,
			name: '横向过道',
			phone: '',
			id: `${new Date().valueOf()}${Math.random()}`,
			width: type2DataObj[key].length * 90,
			height: 32,
			opacity: 1,
			x:
				(type2DataObj[key][0].columnIndex + defaultStartColumnIndex) *
				90,
			y: (type2DataObj[key][0].rowIndex + defaultSeatStartRowIndex) * 32,
			children: (type2DataObj[key] || []).sort(
				(a, b) => a.columnIndex - b.columnIndex
			),
		}
	})

	obj.type2 = [...type2Data]

	const type3DataObj = dataList
		.filter((ov) => ov.seatType == 3)
		.reduce((pre, cur) => {
			;(pre[cur.rowIndex] || (pre[cur.rowIndex] = [])).push(cur)
			pre[cur.rowIndex].sort((a, b) => a.columnIndex - b.columnIndex)
			return pre
		}, {})
	const type3Data = Object.keys(type3DataObj).map((key) => {
		return {
			rowIndex: key,
			seatType: 3,
			center: 0,
			description: '',
			seatNumber: 0,
			name: '主席台',
			phone: '',
			id: `${new Date().valueOf()}${Math.random()}`,
			width: type3DataObj[key].length * 90,
			height: 32,
			opacity: 1,
			x: defaultStartColumnIndex * 90,
			y: 32,
			children: (type3DataObj[key] || []).sort(
				(a, b) => a.columnIndex - b.columnIndex
			),
		}
	})
	obj.type3 = [...type3Data]

	// 绘制 纵向过道数据
	const type4DataObj = dataList
		.filter((ov) => ov.seatType == 4)
		.reduce((pre, cur) => {
			;(pre[cur.columnIndex] || (pre[cur.columnIndex] = [])).push(cur)
			pre[cur.columnIndex].sort((a, b) => a.rowIndex - b.rowIndex)
			return pre
		}, {})
	const type4Data = Object.keys(type4DataObj).map((key) => {
		return {
			columnIndex: key,
			seatType: 4,
			center: 0,
			description: '',
			seatNumber: 0,
			name: '纵向过道',
			phone: '',
			id: `${new Date().valueOf()}${Math.random()}`,
			width: 90,
			height: type4DataObj[key].length * 32,
			opacity: 1,
			x:
				(type4DataObj[key][0].columnIndex + defaultStartColumnIndex) *
				90,
			y: (type4DataObj[key][0].rowIndex + defaultSeatStartRowIndex) * 32,
			children: (type4DataObj[key] || []).sort(
				(a, b) => a.rowIndex - b.rowIndex
			),
		}
	})
	obj.type4 = [...type4Data]

	const type5DataObj = dataList
		.filter((ov) => ov.seatType == 5)
		.reduce((pre, cur) => {
			;(pre[cur.rowIndex] || (pre[cur.rowIndex] = [])).push(cur)
			pre[cur.rowIndex].sort((a, b) => a.columnIndex - b.columnIndex)
			return pre
		}, {})
	const type5Data = Object.keys(type5DataObj).map((key) => {
		return {
			rowIndex: key,
			seatType: 5,
			autoArrange: 0,
			center: 0,
			description: '',
			seatNumber: 0,
			name: '横向过道',
			phone: '',
			id: `${new Date().valueOf()}${Math.random()}`,
			width: type5DataObj[key].length * 90,
			height: 32,
			opacity: 1,
			x:
				(type5DataObj[key][0].columnIndex + defaultStartColumnIndex) *
				90,
			y: (type5DataObj[key][0].rowIndex + defaultSeatStartRowIndex) * 32,
			children: (type5DataObj[key] || []).sort(
				(a, b) => a.columnIndex - b.columnIndex
			),
		}
	})
	obj.type5 = [...type5Data]
	return obj
}

// 分发行列
export const dispatchArrange = ({
	dataList = [],
	maxRow = 0,
	maxColumn = 0,
	seatMode = 2,
}) => {
	const seatModeObj = {
		1: '号',
		2: '列',
	}
	const rowTagList = []
	const columnTagList = []
	for (let i = 0; i <= maxRow; i++) {
		const find = dataList.find((ov) => {
			return ov.seatType == 1 && ov.rowIndex == i
		})
		if (find) {
			rowTagList.push(
				createSeat({
					columnIndex: -1,
					rowIndex: i,
					name: `${rowTagList.length + 1}排`,
					width: 90,
					height: 32,
					opacity: 1,
					x: (defaultStartColumnIndex - 1) * 90,
					y: (i + defaultSeatStartRowIndex) * 32,
					seatType: 'rowTitle',
				})
			)
		}
	}
	for (let i = 0; i <= maxColumn; i++) {
		const find = dataList.find((ov) => {
			return ov.seatType == 1 && ov.columnIndex == i
		})
		if (find) {
			columnTagList.push(
				createSeat({
					columnIndex: i,
					rowIndex: -1,
					name: `${columnTagList.length + 1}${
						seatModeObj[seatMode] || ''
					}`,
					width: 90,
					height: 32,
					opacity: 1,
					x: (i + defaultStartColumnIndex) * 90,
					y: (defaultSeatStartRowIndex - 1) * 32,
					seatType: 'columnTitle',
				})
			)
		}
	}
	return {
		row: rowTagList,
		column: columnTagList,
	}
}

// 创建table座位
export const createTableSeat = ({
	seatNumber = 0,
	rowIndex = 0,
	columnIndex = 0,
}) => {
	return {
		meetingId: '',
		tableId: '',
		rowIndex: rowIndex,
		columnIndex: columnIndex,
		seatType: '1',
		autoArrange: '',
		center: 0,
		description: '',
		seatNumber: seatNumber,
		id: `${new Date().valueOf()}${Math.random()}`,
	}
}
