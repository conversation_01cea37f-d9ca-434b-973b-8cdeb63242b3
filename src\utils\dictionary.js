import { getDeptList } from '@/api/System/index';

const ynData = [
	{
		label: '是',
		value: 1,
	},
	{
		label: '否',
		value: 0,
	},
];
const statusData = [
	{
		label: '启用',
		value: 1,
	},
	{
		label: '禁用',
		value: 0,
	},
];

const auditStatusData = [
	{
		label: '待审核',
		value: 0,
	},
	{
		label: '审核不通过',
		value: 1,
	},
	{
		label: '审核通过',
		value: 2,
	},
];

const dictionaryData = {
	// userStatus: statusData,
	ynStatus: ynData,
	userType: [
		{
			label: '前台C端用户',
			value: '1',
		},
		{
			label: '后台管理用户',
			value: '2',
		},
	],
	userStatus: statusData,
	deptStatus: statusData,
	roleStatus: statusData,
	gender: [
		{
			label: '男',
			value: 1,
		},
		{
			label: '女',
			value: 2,
		},
		{
			label: '未知',
			value: '',
		},
	],
	auditStatus: auditStatusData,
	idType: [
		{
			label: '居民身份证',
			value: '居民身份证',
		},
		{
			label: '护照',
			value: '护照',
		},
		{
			label: '港澳居民来往内地通行证',
			value: '港澳居民来往内地通行证',
		},
		{
			label: '台湾居民来往大陆通行证',
			value: '台湾居民来往大陆通行证',
		},
	],
	userSource: [
		{
			label: '后台管理',
			value: 1,
		},
		{
			label: '微信小程序',
			value: 2,
		},
		{
			label: 'PC端',
			value: 3,
		},
	],
	userType: [
		{
			label: '前台C端用户',
			value: '1',
		},
		{
			label: '后台管理用户',
			value: '2',
		},
	],
	accountType: [
		{
			label: '企业',
			value: 1,
		},
		{
			label: '团队',
			value: 2,
		},
		{
			label: '个人',
			value: 3,
		},
	],
	actObjType: [
		{
			label: '企业',
			value: '1',
		},
		{
			label: '团队',
			value: '2',
		},
		{
			label: '个人',
			value: '3',
		},
	],
};

/**
 * 获取对应字典数据
 * @param {string} dictKeyName 字典对应名称
 * @param {object} option 配置
 * @param {object} option.isDefault 是否需要默认值
 * @param {object} option.defaultValue 默认 Value
 * @param {object} option.defaultLabel 默认 Label
 * @returns
 */
export const getDictData = (dictKeyName, option = {}) => {
	const opt = Object.assign(
		{
			isDefault: false,
			defaultValue: '',
			defaultLabel: '全部',
			value: 'value',
			label: 'label',
		},
		option
	);
	if (dictKeyName && dictionaryData[dictKeyName] === undefined) {
		return [];
	}

	const dictData = dictionaryData[dictKeyName].map(({ label, value }) => {
		const resultItem = {};

		resultItem[opt.label] = label;
		resultItem[opt.value] = value;
		return resultItem;
	});

	if (opt.isDefault) {
		const defaultItem = {};

		defaultItem[opt.label] = opt.defaultLabel;
		defaultItem[opt.value] = opt.defaultValue;
		dictData.unshift(defaultItem);
	}
	return dictData;
};

/**
 * 根据 value 获取 对应字典数据的 lable 值
 * @param {*} dictKeyName
 * @param {*} value
 */
export const getDictLabelByValue = (dictKeyName, value) => {
	if (dictKeyName && dictionaryData[dictKeyName] === undefined) {
		return '';
	}

	const findData = dictionaryData[dictKeyName].find((item) => item.value === value) || {};

	return findData.label || '';
};

export const getDeptData = ({ isCache = true, hasChild = true } = {}) => {
	return new Promise((resolve) => {
		getDeptList({}, { isCache }).then(({ data }) => {
			resolve(_getChild(data, hasChild));
		});
	});
};

function _getChild(data = [], hasChild) {
	return data.map((item) => {
		const result = {
			value: item.id,
			label: item.name,
			parentId: item.parentId,
		};
		if (item.children && hasChild) {
			result.children = _getChild(item.children);
		}

		return result;
	});
}
