const pat =
	import.meta.env.MODE == 'development'
		? new RegExp('(?<=jsx\\?t=)\\d*', 'g')
		: new RegExp('(?<=index-)\\d*(?=.js)', 'g');

// 获取当前本地文档的 app 版本
export function getHtmlVersion() {
	// const pat = new RegExp("(?<=app.)\\d*(?=.js)", "g");
	let htmlVersion = '';
	const docScript = document.getElementsByTagName('script');
	for (let i = 0; i < docScript.length; i++) {
		const version = docScript[i].src.match(pat);
		if (version && version.length) {
			htmlVersion = version[0];
		}
	}
	return htmlVersion;
}

export const htmlVersion = getHtmlVersion();

// 上次获取的时间
let getLatestVersionTime = 0;
// 获取远程 文档的 app 版本 每隔 time 多少毫秒获取一次 默认半小时
export function getLatestVersion(time = 1800000) {
	return new Promise((resolve) => {
		const curTime = new Date().valueOf();
		if (curTime - getLatestVersionTime >= time) {
			fetch(`${window.location.origin}?v=${Date.now()}`)
				.then((res) => {
					const restext = res.text();
					return restext;
				})
				.then((res) => {
					// app.1702259183226.js
					getLatestVersionTime = curTime;
					const version = res.match(pat);
					if (version && version.length) {
						resolve(version);
					} else {
						resolve('');
					}
				})
				.catch((e) => {
					resolve('');
				});
		} else {
			resolve('');
		}
	});
}
