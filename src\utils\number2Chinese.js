/*
* 数字转换成汉字的方法
*  */
/* 中午大写 */
const digitMap = ['零', '壹', '贰', '叁', '肆', '伍', '陆', '柒', '捌', '玖'];
const unitMap = ['', '拾', '佰', '仟', '万', '亿'];
/* 中文小写 */
const digitSmallMap = ['零', '一', '二', '三', '四', '五', '六', '七', '八', '九'];
const unitSmallMap = ['', '十', '百', '千', '万', '亿'];
function numberToChineseCapital(num, isSmall = true) {
    let result = '';
    let unitsPos = 0;
    let hasZero = false;
    const digitMap = isSmall ? digitSmallMap : digitMap;
    const unitMap = isSmall ? unitSmallMap : unitMap;
    // 将数字字符串倒序处理，从个位开始
    for (let i = num.toString().length - 1; i >= 0; i--) {
        let digit = num.toString()[i];
        let digitStr = digitMap[digit];

        if (digit === '0') {
            if (!hasZero && result !== '') {
                result += digitStr;
                hasZero = true;
            }
        } else {
            hasZero = false;
            result = digitStr + unitMap[unitsPos] + result;
        }

        if ((unitsPos + 1) % 4 === 0 && unitsPos !== 0) {
            result = unitMap[unitsPos] + result;
        }

        unitsPos++;
    }

    return result.replace(/零+(?=[^零])/g, '零').replace(/零+万/g, '万').replace(/零+亿/g, '亿').replace(/(零+)(?!零)/g, '$1');
}

export default numberToChineseCapital;