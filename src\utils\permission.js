import store from '@/store';

// 获取权限数据
export const getPermissionData = () => {
	return JSON.parse(
		JSON.stringify(store.getState().user.userInfo.permissionList)
	);
};

// 获取权限树结构数据
export const getPermissiontreeData = () => {
	const permissionList = getPermissionData();

	permissionList.forEach((item) => {
		const { parentId } = item;
		if (parentId) {
			const findData = permissionList.find(({ id }) => id === parentId);
			if (findData) {
				if (findData.children === null) {
					findData.children = [];
				}
				findData.children.push(item);
			}
		}
	});
	return permissionList.filter((item) => !item.parentId);
};

// 获取所有权限id
export const getPermissionListByKeyName = (keyName = 'id') => {
	return getPermissionData()
		.map((item) => item[keyName])
		.filter((item) => item !== undefined);
};
