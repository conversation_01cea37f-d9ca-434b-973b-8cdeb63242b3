import { Suspense, lazy } from 'react';
import { Navigate, Outlet } from 'react-router-dom';
import OutLink from '@/components/OutLink';

import { getCurSystemName } from '@/utils/common';

export const filterRouterName = ['newAchv', 'bidmgt', 'businessOppty', 'competition'];

// 生成格式化路由数据
export const createFormatRouterData = (dataList) => {
	const routerList = _flatData(JSON.parse(JSON.stringify(dataList)))
		.sort((a, b) => {
			if (filterRouterName.includes(a.perms) && filterRouterName.includes(b.perms)) {
				return a.orderNum - b.orderNum;
			} else if (filterRouterName.includes(a.perms) || filterRouterName.includes(b.perms)) {
				return filterRouterName.includes(a.perms) ? -1 : 0;
			} else {
				return a.orderNum - b.orderNum;
			}
		})
		.filter((item) => !item.perms.includes('event'));

	const list = [
		{
			path: '',
			element: <Navigate to={getCurSystemName() || routerList[0]?.path} replace={true} />,
		},
		...routerList,
	];
	return list;
};

// 打平数据
const _flatData = (dataList) => {
	if (typeof dataList !== 'object' || dataList.length === undefined || dataList.length === 0) {
		return [];
	}
	dataList
		.filter((ov) => ov.status === 1 && [1, 2].includes(ov.menuType))
		.forEach((ov) => {
			const { parentId } = ov;
			if (parentId) {
				const find = dataList.find(({ id }) => id === parentId) || {};
				if (!find.children) {
					find.children = [];
				}
				find.children.push(ov);
			}
		});
	return dataList.filter(({ parentId }) => !parentId).map((ov) => _formatItemData(ov));
};

const _formatItemData = (routerInfo) => {
	const {
		icon = '',
		menuName = '',
		component = '',
		path = '',
		children = [],
		isFrame,
		visible = 1,
		isFull = 0,
		perms,
		description,
	} = routerInfo || {};

	const meta = {
		title: menuName,
		hidden: visible === 0,
		menuLayout: isFull === 0,
		logoUrl: description || '',
	};
	const routerItem = {
		icon,
		path: isFrame === 0 ? path : perms,
		meta,
		perms,
		element: _loadComp(isFrame === 0 ? component : '', meta),
	};

	if (children && children.length) {
		routerItem.children = [
			// 重定向
			{
				path: '',
				element: <Navigate to={children[0].path} replace={true} />,
			},
			...children.map((ov) => _formatItemData(ov)),
		];
	}

	return routerItem;
};

/**
 * 格式化路由数据
 * @param {*} component 路由路径
 * @param {*} meta 路由元信息
 * @param { boolean } meta.title 菜单名称
 * @param { boolean } meta.menu 是否侧边菜单
 * @param { boolean } meta.hidden 是否不显示在菜单
 * @returns Object
 */
const _loadComp = (component, meta = {}) => {
	if (component) {
		const Element = handleSystemPath(component);
		if (Element) {
			return <Suspense fallback={<></>}>{<Element meta={meta} />}</Suspense>;
		} else {
			// return <Navigate to={'//www.baidu.com'} replace={true} />;
			console.warn(`${component}菜单组件位置不存在`);
			return <OutLink meta={meta} />;
		}
	}
	return <Outlet />;
};

const modules = import.meta.glob('@/pages/**/*.jsx');
const handleSystemPath = (component) => {
	if (modules[`/src/pages/${component}.jsx`]) {
		return lazy(modules[`/src/pages/${component}.jsx`]);
	} else {
		// 处理系统管理路由  system/user/index
		const modulesName = component
			.split('/')
			.map((ov) => (ov === 'index' ? ov : ov.replace(ov[0], ov[0].toUpperCase())))
			.join('/');
		if (modules[`/src/pages/${modulesName}.jsx`]) {
			return lazy(modules[`/src/pages/${modulesName}.jsx`]);
		}
	}
	return false;
};
