import { defineConfig, loadEnv } from 'vite';
import react from '@vitejs/plugin-react-swc';
import { resolve } from 'path';
// 当前执行node命令时文件夹的地址(工作目录)
const root = process.cwd();

// 路径查找
function pathResolve(dir) {
	return resolve(root, '.', dir);
}

// https://vitejs.dev/config/
export default ({ mode }) => {
	const env = loadEnv(mode, root);
	return defineConfig({
		plugins: [react()],
		publicPath: env.VITE_BASE_PATH || '/',
		base: env.VITE_BASE_PATH || '/',
		css: {
			preprocessorOptions: {
				scss: {
					additionalData: `@import "@/assets/scss/variables.scss";`,
					JavascriptEnabled: true,
				},
			},
			postcss: {},
		},
		resolve: {
			alias: [
				{
					find: /\@\//,
					replacement: `${pathResolve('src')}/`,
				},
			],
		},
		server: {
			host: '0.0.0.0',
			port: 9531,
			hmr: true,
			proxy: {
				'/api/weixin': {
					target: 'https://api.weixin.qq.com/',
					changeOrigin: true,
					rewrite: (path) => {
						console.log('weixin' + path);
						return path.replace(/^\/api\/weixin/, '');
						// return path;
					},
				},
				'/api/jl': {
					target: 'http://**************:18088',
					changeOrigin: true,
					rewrite: (path) => {
						console.log('🚀 proxy:', path);
						return path.replace(/^\/api\/jl/, '');
						// return path;
					},
				},
				'/api': {
					target: env.VITE_BASE_URL || '',
					changeOrigin: true,
					rewrite: (path) => {
						console.log('🚀 proxy:', path);
						return path.replace(/^\/api/, '');
						// return path;
					},
				},
			},
		},
		// build: {
		//   commonjsOptions: {
		//     ignoreTryCatch: false,
		//   },
		// },
	});
};
